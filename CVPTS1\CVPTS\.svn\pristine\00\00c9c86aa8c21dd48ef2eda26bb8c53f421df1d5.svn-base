﻿@{
    @using BCM.Shared
    @using BCM.BusinessClasses
    @inject BCM.Shared.Utilities _Utilities
    ViewBag.Title = "User Profile";
    Layout = "~/Views/Shared/_Layout.cshtml";
    @using Newtonsoft.Json
    @inject IHttpContextAccessor HttpContextAccessor
    ManageUsersDetails _UserDetails = new ManageUsersDetails();
    _UserDetails = _Utilities.LoginUserDetails();
    ViewBag.OrgNizationLogoName = HttpContextAccessor.HttpContext.Session.GetString("OrgNizationLogoName");

    if (_UserDetails == null)
    {
        Context.Response.Redirect("/Login/Login");
    }
    if (ViewBag.OrgNizationLogoName == string.Empty)
    {
        ViewBag.OrgNizationLogoName = "Perpetuutit-Logo.svg";
    }
}

<div class="Page-Condant py-3 d-flex justify-content-center">
    <div class="card col-10">
        <div class="card-body p-4" style="height: calc(100vh - 89px);">
            <div class="row g-5">
                <div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <img src="~/img/profile-img/usericon.svg" width="80" />
                            <h6 class="my-2">Personal Info</h6>
                        </div>
                        <img src="~/img/isomatric/isometric.svg" />
                    </div>
                    <div>
                        <span class="text-secondary">
                            Manage your info, privacy and security to make Continuity Vault work better for you.
                            <span class="text-primary fw-semibold">Find out more</span>
                            <i class="cv-question-mark fw-semibold text-dark align-middle" title=""></i>
                        </span>
                    </div>
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td class="ps-0">Resource Name</td>
                                <td>:</td>
                                @*  <td><img src="~/img/logo/perpetuutit-logo.svg" /></td> *@
                                <td>@_UserDetails.UserName</td>
                            </tr>
                            <tr>
                                <td class="ps-0">Mobile Phone</td>
                                <td>:</td>
                                @*  <td><img src="~/img/logo/perpetuutit-logo.svg" /></td> *@
                                <td>@_UserDetails.MobilePhone</td>
                            </tr>
                            <tr>
                                <td class="ps-0">E-Mail</td>
                                <td>:</td>
                                @*  <td><img src="~/img/logo/perpetuutit-logo.svg" /></td> *@
                                <td>@_UserDetails.CompanyEmail</td>
                            </tr>
                            @* <tr>
                                <td class="ps-0">Organization</td>
                                <td>:</td>
                               @*  <td><img src="~/img/logo/perpetuutit-logo.svg" /></td> 
                                <td><img src="~/img/logo/@ViewBag.OrgNizationLogoName" width="130" height="40" /></td>
                            </tr> *@
                            <tr>
                                <td class="ps-0">Organization Name</td>
                                <td>:</td>
                                <td>@_UserDetails.OrgName</td>
                            </tr>
                            <tr>
                                <td class="ps-0">Unit</td>
                                <td>:</td>
                                <td>@_UserDetails.UnitName</td>
                            </tr>
                            <tr>
                                <td class="ps-0">Department</td>
                                <td>:</td>
                                <td>@_UserDetails.DepartmentName</td>
                            </tr>
                            <tr>
                                <td class="ps-0">Sub-Department</td>
                                <td>:</td>
                                @if (@_UserDetails.SubDepartmentName == string.Empty || @_UserDetails.SubDepartmentName == null)
                                {
                                    <td>NA</td>
                                }else{
                                    <td>@_UserDetails.SubDepartmentName</td>
                                } 
                                
                            </tr>
                            <tr>
                                <td class="ps-0">Resource Role</td>
                                <td>:</td>
                                <td>@_UserDetails.UserRole</td>
                            </tr>
                            
                        </tbody>
                    </table>
                </div>
                @* <div class="col-6">
                    <h6 class="mb-3">User Profile</h6>
                    <form class="row row-cols-2">
                        <div class="form-group col">
                            <label for="validationCustom01" class="form-label">Login Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-name"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Login Name">
                            </div>
                            <div class="invalid-feedback">Enter Login Name</div>
                        </div>
                        <div class="form-group col">
                            <label for="validationCustom01" class="form-label">First Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-login-code"></i></span>
                                <input type="text" class="form-control" placeholder="Enter First Name">
                            </div>
                            <div class="invalid-feedback">Enter First Name</div>
                        </div>
                        <div class="form-group col">
                            <label for="validationCustom01" class="form-label">Middle Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-login-code"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Middle Name">
                            </div>
                            <div class="invalid-feedback">Enter Middle Name</div>
                        </div>
                        <div class="form-group col">
                            <label for="validationCustom01" class="form-label">Last Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-login-code"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Middle Name">
                            </div>
                            <div class="invalid-feedback">Enter Middle Name</div>
                        </div>
                        <div class="form-group col">
                            <label for="validationCustom01" class="form-label">Title Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-login-code"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Middle Name">
                            </div>
                            <div class="invalid-feedback">Enter Middle Name</div>
                        </div>
                        <div class="form-group col">
                            <label for="validationCustom01" class="form-label">City</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-login-code"></i></span>
                                <input type="text" class="form-control" placeholder="Select State Name">
                            </div>
                            <div class="invalid-feedback">Select State Name</div>
                        </div>
                        <div class="form-group col">
                            <label for="validationCustom01" class="form-label">State</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-login-code"></i></span>
                                <input type="text" class="form-control" placeholder="Select State Name">
                            </div>
                            <div class="invalid-feedback">Select State Name</div>
                        </div>

                       
                    </form>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1">Cancel</button>
                        <button type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div> *@

               
            </div>
        </div>
    </div>
</div>