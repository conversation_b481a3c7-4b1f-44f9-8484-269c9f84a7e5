﻿@{
    ViewBag.Title = "RoleMaster";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    var selectedOrgGroupID = ViewBag.SelectedOrgGroupID ?? 0;
    var selectedOrgID = ViewBag.SelectedOrgID ?? 0;
}
<style>
    .table > :not(caption) > * > * {
        padding: 7px 5px;
    }

    i.cv-role.align-middle.me-1 {
        font-size: 15px
    }
</style>

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">
        User Role
    </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal" id="btnCreate"><i class="cv-Plus" title="Create New"></i>Create</button>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>

<div class="Page-Condant  border-0 pe-2" style="height: calc(100vh - 115px);overflow: auto;">
    <div class="card-body">
        <div class="row g-3">
            @foreach (var item in ViewBag.UserRole)
            {
                <div class="col-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex gap-2 align-items-center">
                                    <div>
                                        <ul class="ps-0 mb-0">
                                            <li class="list-group-item fw-bold text-primary">
                                                @item.UserRoleName
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <span class="me-1" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="cv-vertical-dots align-middle"></i></span>
                                    <ul class="dropdown-menu">
                                        <li><span href="#" class="dropdown-item btn-action btnEdit" id="btnEdit" type="button" data-id="@item.UserRoleID"><i class="cv-edit align-middle me-1"></i>Edit</span></li>
                                        <li><span class="dropdown-item btnDelete" href="#" data-id="@item.UserRoleID"><i class="cv-delete text-danger align-middle me-1"></i>Delete</span></li>
                                        <li><span class="dropdown-item btnAssignRoleRights" href="#" role="button" data-bs-toggle="modal" data-bs-target="#AssignRoleRightsModel" data-id="@<EMAIL><EMAIL>"><i class="cv-role align-middle me-1"></i>Assign Rights</span></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="mt-2">
                                <table class="table table-sm table-borderless mb-0">
                                    <tr>
                                        <td>
                                            <ul class="ps-0 mb-0">
                                                <li class="list-group-item text-muted">
                                                    <small>Role Display Text</small>
                                                </li>
                                                <li class="list-group-item fw-semibold">
                                                    @item.UserRoleDetails
                                                </li>
                                            </ul>
                                        </td>
                                        <td>
                                            <ul class="ps-0 mb-0">
                                                <li class="list-group-item text-muted">
                                                    <small>Creation Date</small>
                                                </li>
                                                <li class="list-group-item fw-semibold">
                                                    @item.CreateDate
                                                </li>
                                            </ul>
                                        </td>
                                        <td>
                                            <ul class="ps-0 mb-0">
                                                <li class="list-group-item text-muted">
                                                    <small>Org Group Name</small>
                                                </li>
                                                <li class="list-group-item fw-semibold">
                                                    @item.OrganizationGroupName
                                                </li>
                                            </ul>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <ul class="ps-0 mb-0">
                                                <li class="list-group-item text-muted">
                                                    <small>Organization Name</small>
                                                </li>
                                                <li class="list-group-item fw-semibold">
                                                    @item.OrganizationName
                                                </li>
                                            </ul>
                                        </td>
                                        <td>
                                            <ul class="ps-0 mb-0">
                                                <li class="list-group-item text-muted">
                                                    <small>Session TimeOut</small>
                                                </li>
                                                <li class="list-group-item fw-semibold">
                                                    @item.SessionTimeOut
                                                </li>
                                            </ul>
                                        </td>

                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">User Role Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!-- Role Modal -->
<div class="modal fade" id="AssignRoleRightsModel" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Organization Role Access</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-3">
                    <div class="form-group">
                        <label class="form-label">Org Group</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <select class="form-select" autocomplete="off" id="ddlOrganizationGroup" aria-label="Default select example" required>
                                <option selected disabled value="">-- Select Organizations --</option>
                                @{
                                    foreach (var orgGroup in ViewBag.OrgGroup)
                                    {
                                        <!option value="@orgGroup.Value" @(orgGroup.Value == selectedOrgGroupID.ToString() ? "selected=\"selected=\"" : "")>@orgGroup.Text</!option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Organization</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <select class="form-select" autocomplete="off" id="ddlOrganization" aria-label="Default select example" required>
                                <option selected disabled value="">-- Select Organizations --</option>
                                @{
                                    foreach (var org in ViewBag.OrgInfo)
                                    {
                                        <!option value="@org.Value" @(org.Value == selectedOrgID.ToString() ? "selected=\"selected=\"" : "")>@org.Text</!option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <div class="form-group w-100">
                            <label class="form-label">Role</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-role"></i></span>
                                <select class="form-select" autocomplete="off" aria-label="Default select example" id="ddlUserRoleConfig" asp-items="@(new SelectList(ViewBag.UserRole,"UserRoleID","UserRoleName"))">
                                    <option selected value="0">-- Select Role --</option>
                                </select>
                            </div>
                        </div>
                        <button type="button" class="btn icon-btn btn-primary btn-sm btnSearch">Search</button>
                        <input type="hidden" id="iRoleIdForConfig" value="0" />
                    </div>
                </div>
                <div class="tab-design pb-3">
                    <ul class="nav nav-tabs gap-2 " id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="pageAccess" data-bs-toggle="tab" type="button" role="tab" aria-selected="true">
                                Page Access
                            </button>
                        </li>
                        <!-- <li class="nav-item" role="presentation">
                            <button class="nav-link" id="masterScreeenAccess" data-bs-toggle="tab" type="button" role="tab" aria-selected="false" tabindex="-1">
                                Master Screen Access
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="configurationsSettingsAccess" data-bs-toggle="tab" type="button" role="tab" aria-selected="false" tabindex="-1">
                                Configurations Settings Access
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="dashboardTemplate" data-bs-toggle="tab" type="button" role="tab" aria-selected="false" tabindex="-1">
                                Dashboard Template
                            </button>
                        </li>-->
                    </ul>
                </div>
                <div class="tab-content" id="myTabContent">
                    <div id="one-tab-pane active show" role="tabpanel" aria-labelledby="one-tab">
                        <div style="height: calc(100vh - 350px);overflow:auto" id="showTabData">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Role Modal -->
<!-- Delete Modal -->
@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/delete.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div> *@

<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>

<!-- End Delete Modal -->
@section Scripts {
    <script>
        $(document).ready(function () {
            // Role Configuration Section Start //
            $('#btnCreate').click(function () {
                $.get('@Url.Action("AddRole", "RoleMaster")', function (data) {
                    $('.modal-body').html(data);
                    $('#CreateModal').modal('show');
                });
            });

            $(document).on('click', '.btnEdit', function () {
                var id = $(this).data('id');
                $.get('@Url.Action("EditRole", "RoleMaster")', { id: id }, function (data) {
                    $('.modal-body').html(data);
                    $('#CreateModal').modal('show');
                });
            });

            $(document).on('click', '.btnDelete', function () {
                var id = $(this).data('id');
                $.get('@Url.Action("DeleteRole", "RoleMaster")', { id: id }, function (data) {
                    $('.modal-body').html(data);
                    $('#DeleteModal').modal('show');
                });
            });
            // Role Configuration Section End //

            // Assign Role Rights Section Start //
            $(document).on('click', '.btnAssignRoleRights', function () {
                var parts = $(this).data('id').split('-');
                var iOrgGroupId = parts[0];
                var iOrgId = parts[1];
                var iRoleId = parts[2];

                $('#iRoleIdForConfig').val(iRoleId);
                $('#ddlUserRoleConfig').val(iRoleId);
                $('#ddlOrgGroupConfig').val(iOrgGroupId);
                $('#ddlOrgConfig').val(iOrgId);

                $.get('@Url.Action("ShowTab", "RoleMaster")', { iId: 1, iRoleId: iRoleId, OrgID : iOrgId }, function (data) {
                    $('#showTabData').html('');
                    $('#showTabData').html(data);
                });
            });

            $('#pageAccess').click(function () {
                var iRoleId = $('#iRoleIdForConfig').val();

                $.get('@Url.Action("ShowTab", "RoleMaster")', { iId: 1, iRoleId: iRoleId }, function (data) {
                    $('#showTabData').html('');
                    $('#showTabData').html(data);
                });
            });

            $('#masterScreeenAccess').click(function () {
                var iRoleId = $('#iRoleIdForConfig').val();
                var iOrgId = $('#ddlOrgConfig').val();
                var iOrgGroupId =$('#ddlOrgGroupConfig').val();
                debugger;
                $.get('@Url.Action("ShowTab", "RoleMaster")', { iId: 2, iRoleId: iRoleId, OrgGroupID : iOrgGroupId, OrgID:iOrgId }, function (data) {
                    $('#showTabData').html('');
                    $('#showTabData').html(data);
                });
            });

            $('#configurationsSettingsAccess').click(function () {
                var iRoleId = $('#iRoleIdForConfig').val();

                $.get('@Url.Action("ShowTab", "RoleMaster")', { iId: 3, iRoleId: iRoleId }, function (data) {
                    $('#showTabData').html('');
                    $('#showTabData').html(data);
                });
            });

            $('#dashboardTemplate').click(function () {
                var iRoleId = $('#iRoleIdForConfig').val();

                $.get('@Url.Action("ShowTab", "RoleMaster")', { iId: 4, iRoleId: iRoleId }, function (data) {
                    $('#showTabData').html('');
                    $('#showTabData').html(data);
                });
            });

            $(document).on('click', '.btnSearch', function () {
                var iRoleId = $('#ddlUserRoleConfig').val();

                $('#iRoleIdForConfig').val(iRoleId);

                $.get('@Url.Action("ShowTab", "RoleMaster")', { iId: 1, iRoleId: iRoleId }, function (data) {
                    $('#showTabData').html('');
                    $('#showTabData').html(data);
                });
            });
            // Assign Role Rights Section End //

            // Search functionality for role cards
            $('#search-inp').on('input', function () {
                var searchText = $(this).val().toLowerCase().trim();

                // Get all role cards
                $('.Page-Condant .row .col-4').each(function () {
                    var $card = $(this);
                    var searchableText = '';

                    // Extract specific searchable text from the card
                    var roleName = $card.find('.fw-bold.text-primary').text().toLowerCase(); // Role Name
                    var roleDisplayText = '';
                    var orgGroupName = '';
                    var orgName = '';
                    var sessionTimeout = '';

                    // Get Role Display Text (first fw-semibold in first row)
                    var firstRowCells = $card.find('table tr:first td');
                    if (firstRowCells.length > 0) {
                        roleDisplayText = $(firstRowCells[0]).find('.fw-semibold').text().toLowerCase();
                        if (firstRowCells.length > 2) {
                            orgGroupName = $(firstRowCells[2]).find('.fw-semibold').text().toLowerCase();
                        }
                    }

                    // Get Organization Name and Session Timeout (second row)
                    var secondRowCells = $card.find('table tr:last td');
                    if (secondRowCells.length > 0) {
                        orgName = $(secondRowCells[0]).find('.fw-semibold').text().toLowerCase();
                        if (secondRowCells.length > 1) {
                            sessionTimeout = $(secondRowCells[1]).find('.fw-semibold').text().toLowerCase();
                        }
                    }

                    // Combine all searchable text
                    searchableText = roleName + ' ' + roleDisplayText + ' ' + orgGroupName + ' ' + orgName + ' ' + sessionTimeout;

                    // Show/hide card based on search match
                    if (searchText === '' || searchableText.indexOf(searchText) !== -1) {
                        $card.show();
                    } else {
                        $card.hide();
                    }
                });

                // Show "No results found" message if no cards are visible
                var visibleCards = $('.Page-Condant .row .col-4:visible').length;

                // Remove existing no-results message
                $('.no-results-message').remove();

                if (visibleCards === 0 && searchText !== '') {
                    $('.Page-Condant .row').append(
                        '<div class="col-12 text-center no-results-message">' +
                        '<div class="alert alert-info">' +
                        '<i class="cv-search me-2"></i>No roles found matching "' + searchText + '"' +
                        '</div>' +
                        '</div>'
                    );
                }
            });

            // Clear search on page load
            $('#search-inp').val('');

            // Add search functionality on Enter key press
            $('#search-inp').on('keypress', function (e) {
                if (e.which === 13) { // Enter key
                    e.preventDefault();
                    // Trigger the input event to perform search
                    $(this).trigger('input');
                }
            });

            // Add clear search functionality (optional enhancement)
            $('#search-inp').on('keyup', function (e) {
                if (e.which === 27) { // Escape key
                    $(this).val('').trigger('input'); // Clear search and show all cards
                }
            });
        });
    </script>
}