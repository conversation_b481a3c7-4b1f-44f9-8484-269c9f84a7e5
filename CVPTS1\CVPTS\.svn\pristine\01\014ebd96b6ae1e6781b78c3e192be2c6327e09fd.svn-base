﻿@using BCM.BusinessClasses
@using BCM.Shared
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
    DashboardWidgets Widgets = @ViewBag.Widgets as DashboardWidgets ?? new DashboardWidgets();

}

<link href="~/css/Dashboard.css" rel="stylesheet" />
<link href="~/css/calendar.css" rel="stylesheet" />
@*<link href="~/css/ChartImprovements.css" rel="stylesheet" />*@
@*<link href="~/css/ResponsiveCharts.css" rel="stylesheet" />*@

<script>
    if (typeof window.jQuery === 'undefined') {
        document.write('<script src="~/lib/jquery/jquery.min.js"><\/script>');
    }
</script>

@using Newtonsoft.Json
@* <div class="Page-Header">
    <h6 class="Page-Title">Dashboard</h6>
</div> *@
<style>

    #RiskHeatMap_Chart1 {
        width: 100%;
        height: calc(100vh - 265px);
    }

    #tblheatmapSeverity {
        width: 100%;
        table-layout: fixed;
    }

    #PieChartDiv {
        width: 100%;
        height: 300px;
    }

    .sidebar.close ~ .home-section {
        height: calc(100vh - 20px);
        overflow-y: auto;
    }

</style>
<input type="hidden" id="message" value="@ViewData["MyData"]" />

<div class="Page-Condant border-0 mt-3">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-8 col-lg-8 col-sm-12 d-grid"
                 style="grid-template-rows: max-content; height:fit-content;">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="card h-100">
                            <div class="card-header header border-0">
                                <h6 class="page_title d-flex align-items-center">
                                    <span class="p-2 bg-white shadow-sm rounded-circle">
                                        <i class="cv-business-continuity-summary align-middle text-primary fs-5"></i>
                                    </span>
                                    <span>Business Continuity Summary</span>
                                </h6>
                                <form class="d-flex tab-design">
                                    <ul class="nav nav-tabs gap-3" id="myTab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="home-tab" data-bs-toggle="tab"
                                                    data-bs-target="#home-tab-pane" type="button" role="tab"
                                                    aria-controls="home-tab-pane" aria-selected="true">
@*                                                 BCM Summary *@
                                            </button>
                                        </li>
                                        @* <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="disabled-tab" data-bs-toggle="tab"
                                                    data-bs-target="#disabled-tab-pane" type="button" role="tab"
                                                    aria-controls="disabled-tab-pane" aria-selected="false">
                                                Risk Heat Map
                                            </button>
                                        </li> *@
                                    </ul>
                                </form>
                            </div>
                            <div class="card-body pt-0">
                                <div class="row h-100">
                                    <div class="tab-content" id="myTabContent">
                                        <div class="tab-pane fade  show active" id="home-tab-pane" role="tabpanel"
                                             aria-labelledby="home-tab" tabindex="0">
                                            <div class="row row-cols-3 g-3 dashboard-row">
                                                <div class="col-12 col-xl-5 col-xxl-4">
                                                    <div class="card shadow-sm h-100">
                                                        <div class="card-header border-0">
                                                            <h6>Business Processes BIA</h6>
                                                        </div>
                                                        <div class="card-body pt-0">
                                                            @* <div id="DepartmentBIA_Chart" class="pie-chart-container"></div> *@
                                                            <div id="DepartmentBIA-Chart" style="height:184px;"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-xl-3 col-xxl-2">
                                                    <div class="card shadow-sm h-100">
                                                        <div class="card-header border-0">
                                                            <h6>OverAll KPI Status</h6>
                                                        </div>
                                                        <div class="card-body pt-0 pb-2">
                                                            <div class="d-grid align-items-center justify-content-center mb-2">
                                                                <div class="donut-chart" style="--progress: 55;">
                                                                    <svg width="125" height="125" viewBox="0 0 120 120">
                                                                        <circle class="circle-bg" cx="60" cy="60"
                                                                                r="50" />
                                                                        <circle class="circle-progress " cx="60" cy="60"
                                                                                r="50" style="stroke:#d51ba1;" />
                                                                    </svg>
                                                                    <div class="percentage">
                                                                        @ViewBag.KPICompletedPercentage
                                                                    </div>
                                                                </div>
                                                                <div class="d-grid text-center">
                                                                    <span class="fw-normal">
                                                                        <span class="fs-4">
                                                                            @ViewBag.KPIInProgressPercentage
                                                                            %
                                                                        </span>
                                                                    </span>
                                                                    <span class="text-secondary">InProgress</span>
                                                                </div>
                                                            </div>
                                                            <div class="d-flex align-items-center justify-content-between d-none">
                                                                <div class="donut-chart" style="--progress: 60;">
                                                                    <svg width="92" height="92" viewBox="0 0 120 120">
                                                                        <circle class="circle-bg" cx="60" cy="60"
                                                                                r="50" />
                                                                        <circle class="circle-progress" cx="60" cy="60"
                                                                                r="50" style="stroke:#2315a3;" />
                                                                    </svg>
                                                                    <div class="percentage">60%</div>
                                                                </div>
                                                                <div class="d-grid">
                                                                    <span class="fw-normal">
                                                                        <span class="fs-4">06</span>
                                                                    </span>
                                                                    <span class="text-secondary">Marketing</span>
                                                                </div>
                                                            </div>

                                                            @* <div id="OverallKPI_Chart" class="pie-chart-container"></div> *@
                                                            @* <div id="OverAllKPIStatus-Chart"></div> *@
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-xl-4 col-xxl-6">
                                                    <div class="card shadow-sm h-100">
                                                        <div class="card-header header border-0">
                                                            <h6>Review Progress BIA</h6>
                                                            @* <ul class="nav nav-pills Review-Tab" id="pills-tab" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Progress</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Meetings</button>
                                                                </li>
                                                            </ul> *@
                                                        </div>
                                                        <div class="card-body pt-0 pb-2">
                                                            <ul class="list-group list-group-flush d-grid h-100">
                                                                <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                                                    <div class="d-flex gap-2">
                                                                        <i class="cv-dot fs-5" style="color:#2315a3;"></i>
                                                                        <div class="text-secondary">Business Progress</div>
                                                                    </div>
                                                                    <div class="d-flex flex-fill align-items-center gap-3">
                                                                        <div class="fw-semibold">@ViewBag.BusinessProcessApproved / @ViewBag.BusinessProcessTotal</div>
                                                                        <div class="flex-fill">
                                                                            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="@((int)ViewBag.ProcessPercentage)" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: @((int)ViewBag.ProcessPercentage)%; background-color:#2315a3;"></div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="fw-normal">@((int)ViewBag.ProcessPercentage)%</div>
                                                                    </div>
                                                                </li>
                                                                <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                                                    <div class="d-flex gap-2">
                                                                        <i class="cv-dot fs-5" style="color:#cd086a;"></i>
                                                                        <div class="text-secondary">Recovery Plan</div>
                                                                    </div>
                                                                    <div class="d-flex flex-fill align-items-center gap-3">
                                                                        <div class="fw-semibold">@ViewBag.ApprovedRecoveryPlanCount / @ViewBag.RecoveryPlanCount</div>
                                                                        <div class="flex-fill">
                                                                            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="@((int)ViewBag.RecoveryPlanPercentage)" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: @((int)ViewBag.RecoveryPlanPercentage)%; background-color:#cd086a;"></div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="fw-normal">@((int)ViewBag.RecoveryPlanPercentage)%</div>
                                                                    </div>
                                                                </li>
                                                                @* <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                                                    <div class="d-flex gap-2">
                                                                        <i class="cv-dot fs-5" style="color:#880ba7;"></i>
                                                                        <div class="text-secondary">Overall KPI Information</div>
                                                                    </div>
                                                                    <div class="d-flex flex-fill align-items-center gap-3">
                                                                        <div class="fw-semibold">@ViewBag.ApprovedCount / @ViewBag.KPICount</div>
                                                                        <div class="flex-fill">
                                                                            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="@((int)ViewBag.KPIPercentage)" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: @((int)ViewBag.KPIPercentage)%; background-color:#880ba7;"></div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="fw-normal">@((int)ViewBag.KPIPercentage)%</div>
                                                                    </div>
                                                                </li> *@
                                                                <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                                                    <div class="d-flex gap-2">
                                                                        <i class="cv-dot fs-5" style="color:#880ba7;"></i>
                                                                        <div class="text-secondary">Risk Assessment</div>
                                                                    </div>
                                                                    <div class="d-flex flex-fill align-items-center gap-3">
                                                                        <div class="fw-semibold">@ViewBag.ApprovedRiskCount / @ViewBag.TotalRiskCountForReview</div>
                                                                        <div class="flex-fill">
                                                                            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="@((int)ViewBag.RiskPercentage)" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: @((int)ViewBag.RiskPercentage)%; background-color:#880ba7;"></div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="fw-normal">@((int)ViewBag.RiskPercentage)%</div>
                                                                    </div>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row row-cols-3 g-3 dashboard-row mt-0">
                                                <div class="col-lg-8">
                                                    <div class="card shadow-sm h-100">
                                                        <div class="card-header border-0 header">
                                                            <h6>Business Entities Summary</h6>
                                                        </div>
                                                        <div class="card-body p-0 align-content-center">
                                                            <ul class="list-group list-group-horizontal">
                                                                <li class="list-group-item bg-white flex-fill border-0 border-end">
                                                                    <div class="d-grid">
                                                                        <div class="d-flex align-items-center justify-content-between">
                                                                            <span class="fs-6">Risk Assessment</span>
                                                                            <span class="shadow-sm rounded-2 icon-circle"
                                                                                  style="background-color:#2315a3;">
                                                                                <i class="cv-initiated-2 text-white fs-4"
                                                                                   style="line-height: 1.2;"></i>
                                                                            </span>
                                                                        </div>
                                                                        <span class="fw-normal my-1">
                                                                            <span class="fs-4">
                                                                                @* @ViewBag.TotalInitiatedRiskCount *@
                                                                                @ViewBag.RiskAssessmentCount
                                                                            </span>
                                                                        </span>
                                                                        @* <span>/@ViewBag.TotalRiskCount</span></span> *@
                                                                        <span class="text-secondary">

                                                                            <a href="@Url.Action("ManageRisk", "ManageRisk", new { area = "BCMRiskAssessment", upcoming = 1 })" style="text-decoration: underline; color: inherit;">
                                                                             
                                                                                <span class="fw-semibold text-primary">@ViewBag.UpComingRisksForReviews</span>
                                                                                BCM Risk(s) has been
                                                                                <span class="text-secondary">coming for review within next 7 days</span>
                                                                            </a>

                                                                        </span>

                                                                        <span class="text-secondary">

                                                                            <a href="@Url.Action("ManageRisk", "ManageRisk", new { area = "BCMRiskAssessment", past = 1 })" style="text-decoration: underline; color: inherit;">
                                                                      
                                                                                <span class="fw-semibold text-primary">@ViewBag.PastReviewDateRisks</span>
                                                                                BCM Risk
                                                                                <span class="text-secondary">item(s) past review date</span>
                                                                            </a>

                                                                        </span>
                                                                    </div>
                                                                </li>
                                                                <li class="list-group-item bg-white flex-fill border-0">
                                                                    <div class="d-grid">
                                                                        <div class="d-flex align-items-center justify-content-between">
                                                                            <span class="fs-6">Recovery Plan</span>
                                                                            <span class="shadow-sm rounded-2 icon-circle"
                                                                                  style="background-color:#880ba7;">
                                                                                <i class="cv-waiting-2 text-white fs-4"
                                                                                   style="line-height: 1.2;"></i>
                                                                            </span>
                                                                        </div>
                                                                        <span class="fw-normal my-1">
                                                                            <span class="fs-4">
                                                                                @* @ViewBag.TotalInitiatedRecoveryPlanCount  *@
                                                                                @ViewBag.TotalRecoveryPlanCount
                                                                            </span>
                                                                        </span>
                                                                        @* <span>/@ViewBag.TotalRecoveryPlanCount</span></span> *@
                                                                        <a href="@Url.Action("ManageRecoveryPlans", "ManageRecoveryPlans", new { area = "BCMFunctionRecoveryPlan", upcoming = 1 })" style="text-decoration: underline; color: inherit;">
                                                                            <span class="text-secondary"><span class="fw-semibold text-primary">@ViewBag.UpComingRecoveryPlansForReviews</span> Recovery Plan(s) has been</span>
                                                                            <span class="text-secondary">coming for review within next 7 days</span>
                                                                        </a>

                                                                        <a href="@Url.Action("ManageRecoveryPlans", "ManageRecoveryPlans", new { area = "BCMFunctionRecoveryPlan", past = 1 })" style="text-decoration: underline; color: inherit;">
                                                                            <span class="text-secondary"><span class="fw-semibold text-primary">@ViewBag.PastRecoveryPlansForReviews</span> Recovery Plan(s)</span>
                                                                            <span class="text-secondary">past review date</span>
                                                                        </a>
                                                                    </div>
                                                                </li>
                                                                <li class="list-group-item bg-white flex-fill border-0 border-start">
                                                                    <div class="d-grid">
                                                                        <div class="d-flex align-items-center justify-content-between">
                                                                            <span class="fs-6">Vendor</span>
                                                                            <span class="shadow-sm rounded-2 icon-circle"
                                                                                  style="background-color:#cd086a;">
                                                                                <i class="cv-approved text-white fs-4"
                                                                                   style="line-height: 1.2;"></i>
                                                                            </span>
                                                                        </div>
                                                                        <span class="fw-normal my-1">
                                                                            <span class="fs-4">
                                                                                @* @ViewBag.TotalInitiatedRecoveryPlanCount  *@
                                                                                @ViewBag.TotalVendorCount
                                                                            </span>
                                                                        </span>
                                                                        @* <span>/@ViewBag.TotalRecoveryPlanCount</span></span> *@
                                                                        <span class="text-secondary">
                                                                            <a href="@Url.Action("ManageVendor", "ManageVendor", new { area = "BCMThirdParty", upcoming = 1 })" style="text-decoration: underline; color: inherit;">
                                                                                <span class="fw-semibold text-primary">@ViewBag.UpComingVendorForReviews</span>
                                                                            Vendor(s) contract
                                                                       
                                                                        <span class="text-secondary">has been expiring in next 7 days</span>
                                                                        </a>
                                                                        </span>
                                                                        <span class="text-secondary">
                                                                            <a href="@Url.Action("ManageVendor", "ManageVendor", new { area = "BCMThirdParty", past = 1 })" style="text-decoration: underline; color: inherit;">
                                                                                <span class="fw-semibold text-primary">@ViewBag.PastVendorForReviews</span>
                                                                                Vendor(s) contract

                                                                                <span class="text-secondary">is expired in past days</span>
                                                                            </a>
                                                                        </span>
                                                                    </div>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4">
                                                    <div class="card shadow-sm h-100">
                                                        <div class="card-header border-0">
                                                            <h6>Critical & Non-Critical Business</h6>
                                                        </div>
                                                        <div class="card-body pt-0">
                                                            @* <div id="CriticalProcesses_Chart" class="pie-chart-container"></div> *@
                                                            @* <div id="Critical_NonCritical-Chart"></div> *@
                                                            <div class="card-group gap-3">
                                                                <div class="card w-50 text-center rounded-2 bg-light border-light-subtle"
                                                                     style="box-shadow:none !important;">
                                                                    <div class="card-header bg-transparent border-0 pt-3">
                                                                        <span class="shadow-sm rounded-2 icon-circle mx-auto"
                                                                              style="background-color:#2315a3;">
                                                                            <i class="cv-success1 text-white fs-4"
                                                                               style="line-height: 1.2;"></i>
                                                                        </span>
                                                                    </div>
                                                                    <div class="card-body pt-1">
                                                                        <div class="fs-4">@ViewBag.DashboardCount.NonCriticalbusinessprocessCount</div>
                                                                        <span class="Sub-Title">Non Critical</span>
                                                                    </div>
                                                                    @*  <div class="card-footer border-0 bg-transparent pb-3">
                                                                        <span class="text-secondary Sub-Title">Assets which are <br/> Safer in BCM</span>
                                                                    </div> *@
                                                                </div>
                                                                <div class="card w-50 text-center rounded-2 bg-light border-light-subtle"
                                                                     style="box-shadow:none !important;">
                                                                    <div class="card-header bg-transparent border-0 pt-3">
                                                                        <span class="shadow-sm rounded-2 icon-circle mx-auto"
                                                                              style="background-color:#cd086a;">
                                                                            <i class="cv-critical-entities text-white fs-4"
                                                                               style="line-height: 1.2;"></i>
                                                                        </span>
                                                                    </div>
                                                                    <div class="card-body pt-1">
                                                                        <div class="fs-4">@ViewBag.DashboardCount.CriticalbusinessprocessCount</div>
                                                                        <span class="Sub-Title">Critical</span>
                                                                    </div>
                                                                    @* <div class="card-footer border-0 bg-transparent pb-3">
                                                                        <span class="text-secondary Sub-Title">critical which are <br /> Safer in BCM</span>
                                                                    </div> *@
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="tab-pane fade h-100" id="disabled-tab-pane" role="tabpanel"
                                             aria-labelledby="disabled-tab" tabindex="0">
                                            <div class="card h-100">
@*                                                 @await Html.PartialAsync("~/Areas/BCMRiskAssessment/Views/RiskHeatmap_DynamicNew/_RiskHeatMapDashboard.cshtml") *@

                                                <div class="card-header header border-0 pb-0" id="RiskFilterList">
                                                    <form class="d-flex tab-design mb-0">
                                                        <div class="form-check form-check-inline mb-0">
                                                            <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="0" checked>
                                                            <label class="form-check-label" for="inlineRadio1">All Risks</label>
                                                        </div>
                                                        <div class="form-check form-check-inline mb-0">
                                                            <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="1">
                                                            <label class="form-check-label" for="inlineRadio2">Inherent Risks</label>
                                                        </div>
                                                        <div class="form-check form-check-inline mb-0">
                                                            <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="2">
                                                            <label class="form-check-label" for="inlineRadio3">Residual Risks</label>
                                                        </div>
                                                    </form>

                                                </div>
                                                <div class="card-body pt-0">
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <div class="p-2 bg-secondary-subtle text-center my-2">
                                                                <span>Impact( As a % of Annual Revenues )</span>
                                                            </div>

                                                            <!-- Heat Map Table -->
                                                            <div class="bg-white rounded p-3 mb-4">
                                                                <div class="table-responsive">
                                                                    <table class="table table-bordered text-center mb-3 risk-table" id="tblheatmapSeverity">
                                                                        <thead>
                                                                            <tr class="bg-light">
                                                                                <th class="text-center">HEAT MAP</th>
                                                                                <th style="background-color: #48A422;">Insignificant</th>
                                                                                <th style="background-color: #FFCC00;">Low</th>
                                                                                <th style="background-color: #FFA500;">Medium</th>
                                                                                <th style="background-color: #FF3300;">High</th>
                                                                                <th style="background-color: #FF3300;">Critical</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <th></th>
                                                                                <th class="text-center">1</th>
                                                                                <th class="text-center">2</th>
                                                                                <th class="text-center">3</th>
                                                                                <th class="text-center">4</th>
                                                                                <th class="text-center">5</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            <tr>
                                                                                <td class="text-center bg-light">Rare<br>1</td>
                                                                                <td style="background-color: #48A422;"></td>
                                                                                <td style="background-color: #FFCC00;"></td>
                                                                                <td style="background-color: #FFA500;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-center bg-light">Unlikely<br>2</td>
                                                                                <td style="background-color: #FFCC00;"></td>
                                                                                <td style="background-color: #FFCC00;"></td>
                                                                                <td style="background-color: #FFA500;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-center bg-light">Possible<br>3</td>
                                                                                <td style="background-color: #FFA500;"></td>
                                                                                <td style="background-color: #FFA500;"></td>
                                                                                <td style="background-color: #FFA500;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-center bg-light">Likely<br>4</td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-center bg-light">Almost Certain<br>5</td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                                <td style="background-color: #FF3300;"></td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </div>

                                                                <!-- Risk Severity Section -->
                                                                <div class="table-responsive mt-4">
                                                                    <table class="table table-bordered text-center risk-table risk-severity-table" id="tblheatmapSeverity1">
                                                                        <thead>
                                                                            <tr class="bg-light">
                                                                                <td class="text-center">RISK SEVERITY<br>( Probability x Impact )</td>
                                                                                <th class="text-center" style="background-color: #48A422;">Insignificant</th>
                                                                                <th class="text-center" style="background-color: #FFCC00;">Low</th>
                                                                                <th class="text-center" style="background-color: #FFA500;">Medium</th>
                                                                                <th class="text-center" style="background-color: #FF3300;">High</th>
                                                                                <th class="text-center" style="background-color: #FF3300;">Critical</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            <tr>
                                                                                <td></td>
                                                                                <td>Potential to cause<br>insignificant impact on<br>objectives</td>
                                                                                <td>Potential to cause minor<br>impact that, in most cases,<br>can be absorbed</td>
                                                                                <td>Potential to cause noticeable<br>impact on objectives</td>
                                                                                <td>Potential to cause major<br>impact on objectives</td>
                                                                                <td>Potential to cause severe<br>impact on objectives</td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>

                                                            <div id="RiskHeatMap_Chart"></div>
                                                        </div>

                                                    </div>

                                                </div>


                                            </div>
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-8 col-sm-12 ">
                        @if (Widgets.BCMCalender == 1)
                        {
                            <div class="card">
                                <div class="card-header header border-0">
                                    <h6 class="page_title d-flex align-items-center mb-0">
                                        <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                            <i class="cv-BCM-calendar align-middle text-primary fs-5"></i>
                                        </span><span>BCM Calendar</span>
                                    </h6>
                                    <form class="d-flex">
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-primary dropdown-toggle"
                                                        data-bs-toggle="dropdown" aria-expanded="false"
                                                        style="padding: 2px 4px; font-size: 13px;">
                                                    Meetings
                                                </button>
                                                <div class="dropdown-menu  dropdown-menu-end border-0 shadow">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex align-items-center border-0 bg-transparent gap-2">
                                                            <span class="shadow-sm rounded-2 icon-circle"
                                                                  style="background-color:#2315a3;">
                                                                <i class="cv-task-in-progress text-white fs-5"
                                                                   style="line-height: 1.2;"></i>
                                                            </span>
                                                            <div class="text-truncate">
                                                                <div class="fw-semibold">Initiated</div>
                                                                <div class="text-secondary text-truncate"
                                                                     title="Meeting which are started">
                                                                    Meeting which are
                                                                    started
                                                                </div>
                                                            </div>
                                                            <span class="fw-normal my-1 ms-auto">
                                                                <span class="fs-5">@ViewBag.ReviewMeetingsInitiated</span>
                                                            </span>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center border-0 bg-transparent gap-2">
                                                            <span class="shadow-sm rounded-2 icon-circle"
                                                                  style="background-color:#880ba7;">
                                                                <i class="cv-holiday-date text-white fs-5"
                                                                   style="line-height: 1.2;"></i>
                                                            </span>
                                                            <div class="text-truncate">
                                                                <div class="fw-semibold">Scheduled</div>
                                                                <div class="text-secondary text-truncate"
                                                                     title="Scheduled for future review">
                                                                    Scheduled for future
                                                                    review
                                                                </div>
                                                            </div>
                                                            <span class="fw-normal my-1 ms-auto">
                                                                <span class="fs-5">@ViewBag.ReviewMeetingsWaitingForApproval</span>
                                                            </span>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center border-0 bg-transparent gap-2">
                                                            <span class="shadow-sm rounded-2 icon-circle"
                                                                  style="background-color:#cd086a;">
                                                                <i class="cv-success text-white fs-5"
                                                                   style="line-height: 1.2;"></i>
                                                            </span>
                                                            <div class="text-truncate">
                                                                <div class="fw-semibold">Completed</div>
                                                                <div class="text-secondary text-truncate"
                                                                     title="session which are completed">
                                                                    session which are
                                                                    completed
                                                                </div>
                                                            </div>
                                                            <span class="fw-normal my-1 ms-auto">
                                                                <span class="fs-5">@ViewBag.ReviewMeetingsApproved</span>
                                                            </span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <span tooltip="Create Event" class="btn-action" type="button"
                                                  onclick="window.location.href = '../BCMCalendar/ManageBCMCalender/ManageBCMCalender'">
                                                <i class="cv-Plus me-1" title="Create"></i>
                                            </span>
                                        </div>
                                    </form>
                                </div>
                                <div class="card-body pt-0">
                                    <div id='calendar'></div>
                                </div>
                            </div>
                        }
                    </div>
                    <div class="col-lg-4 col-sm-12 d-grid" style="grid-template-rows: max-content;">
                        @if (Widgets.ToDo == 1)
                        {
                            <div class="card mb-3">
                                <div class="card-header header border-0">
                                    <h6 class="page_title d-flex align-items-center mb-0">
                                        <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                            <i class="cv-to-do-list align-middle text-primary fs-5"></i>
                                        </span>
                                        <span>To Do List</span>
                                    </h6>
                                    <div class="d-none gap-2">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                                    id="actionWithSelected" data-bs-toggle="dropdown" aria-expanded="false">
                                                Action with selected
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="actionWithSelected">
                                                <li>
                                                    <a class="dropdown-item todo-action" data-action="done"
                                                       href="#">Done</a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item todo-action" data-action="archive" href="#">
                                                        Add
                                                        to archive
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item todo-action" data-action="remove"
                                                       href="#">Remove</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary active"
                                                    data-filter="all">
                                                All
                                            </button>
                                            @* <button type="button" class="btn btn-sm btn-outline-primary"
                                                data-filter="new">New</button> *@
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    data-filter="done">
                                                Done
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    data-filter="archived">
                                                Archived
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0" style="max-height: 160px; overflow-y:auto;">
                                    <div class="todo-list-container" style="height: 160px !important; min-height: 160px !important;">
                                        @await Html.PartialAsync("_TodoItemsList")
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent p-2 d-none">
                                    <div class="d-flex justify-content-end">
                                        <form id="todoForm" class="d-flex gap-2 mb-3">
                                            <input type="text" class="form-control" id="todoInput"
                                                   placeholder="Add new task...">
                                            <button type="submit" class="btn btn-primary" id="addTodoBtn">Add</button>
                                            <button type="button" class="btn btn-secondary" id="cancelEditBtn"
                                                    style="display: none;">
                                                Cancel
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        }
                        @if (Widgets.PendingApprovals == 1)
                        {
                            <div class="card">
                                <div class="card-header border-0 pb-0">
                                    <h6 class="page_title d-flex align-items-center ">
                                        <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                            <i class="cv-approvals-pending align-middle text-primary fs-5"></i>
                                        </span>
                                        <span>Approvals Pending</span>
                                    </h6>
                                </div>
                                <div class="card-body py-0">
                                    <div class="row g-2">
                                        <div class="col">
                                            <ul class="list-group list-group-flush ">
                                                <li role="button" data-bs-target="#TakeActionModal"
                                                    data-id="@Convert.ToInt32(@BCPEnum.EntityType.BusinessProcess)" data-status="1"
                                                    class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                                    <div class="d-flex fw-semibold align-items-center">
                                                        <i class="cv-business-process fs-6 icon-circle bg-warning-subtle text-warning"></i><span class="ms-2 fw-normal">Business Process</span>
                                                    </div>
                                                    <span class="fs-5">@ViewBag.WFAProcessCount</span>
                                                </li>
                                                <li role="button" data-bs-target="#TakeActionModal"
                                                    data-id="@Convert.ToInt32(@BCPEnum.EntityType.RecoveryPlan)" data-status="1"
                                                    class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                                    <div class="d-flex fw-semibold align-items-center">
                                                        <i class="cv-recovery-plan fs-6 icon-circle bg-info-subtle text-info"></i><span class="ms-2 fw-normal">Recovery Plan</span>
                                                    </div>
                                                    <span class="fs-5">@ViewBag.WFARPCount</span>
                                                </li>
                                                <li role="button" data-bs-target="#TakeActionModal"
                                                    data-id="@Convert.ToInt32(@BCPEnum.EntityType.RiskAssessment)" data-status="1"
                                                    class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                                    <span class="d-flex fw-semibold  align-items-center">
                                                        <i class="cv-other-bcm-entities  icon-circle bg-danger-subtle text-danger-emphasis fs-6"></i>
                                                        <span class="ms-2 fw-normal">BCM Risk</span>
                                                    </span>
                                                    <span class="fs-5">@ViewBag.WFARACount</span>
                                                </li>
                                                @* <li role="button" data-bs-target="#TakeActionModal"
                                                    data-id="@Convert.ToInt32(@BCPEnum.EntityType.Audit)" data-status="1"
                                                    class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                                    <span class="d-flex fw-semibold  align-items-center">
                                                        <i class="cv-other-bcm-entities  icon-circle bg-success-subtle text-success-emphasis fs-6"></i><span class="ms-2 fw-normal">BCM Audit</span>
                                                    </span>
                                                    <span class="fs-5">@ViewBag.WFABCMAuditCount</span>
                                                </li> *@
                                                @* <li
                                                    class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1">
                                                    <div class="d-flex fw-semibold align-items-center">
                                                        <i
                                                            class="cv-other-bcm-entities  icon-circle bg-primary-subtle text-primary-emphasis fs-6"></i><span
                                                            class="ms-2 fw-normal">Other BCM Entities</span>
                                                    </div>
                                                    <span class="fs-5">@ViewBag.ApprovalPendingCount.OtherBCMEntities</span>
                                                </li> *@
                                                @* <li
                                                    class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1">
                                                    <span class="d-flex fw-semibold  align-items-center">
                                                        <i
                                                            class="cv-other-bcm-entities  icon-circle bg-danger-subtle text-danger-emphasis fs-6"></i><span
                                                            class="ms-2 fw-normal">BCM Strategy</span>
                                                    </span>
                                                    <span class="fs-5">@ViewBag.ApprovalPendingCount.BCMStategies</span>
                                                </li> *@                                                
                                                </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-sm-12 d-grid" style="grid-template-rows: max-content;">


                @if (Widgets.OrganisationSummary == 1)
                {
                    <div class="card mb-3">
                        <div class="card-header header border-0">
                            <h6 class="page_title d-flex align-items-center mb-0">
                                <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                    <i class="cv-organisation-summary align-middle text-primary fs-5"></i>
                                </span>
                                <span>Organization Summary</span>
                            </h6>
                        </div>
                        <div class="card-body pt-0">
                            <div class="row g-3">
                                <div class="col-auto col-xxl-3 border-end">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#f7e9fa">
                                        <i class="cv-unit align-middle fs-4" style="color:#ae1fcd;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Business&nbsp;Unit</div>
                                    <h4 class="fw-normal mb-0">@ViewBag.UnitCount</h4><span class="text-success">@* <sub>+1.0%</sub> *@</span>
                                </div>
                                <div class="col col-xxl-3 border-end">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#f5f4fd">
                                        <i class="cv-department align-middle fs-4" style="color:#6645e9;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Department</div>
                                    <h4 class="fw-normal mb-0">@ViewBag.DepartmentCount</h4><span class="text-success">@* <sub>+1.0%</sub> *@</span>
                                </div>
                                <div class="col col-xxl-3 border-end">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#efe7f6">
                                        <i class="cv-subdepartment align-middle fs-4" style="color:#640aa8;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Sub&nbsp;Department</div>
                                    <h4 class="fw-normal mb-0">@ViewBag.DivisionCount</h4><span class="text-success">@* <sub>+1.0%</sub> *@</span>
                                </div>
                                <div class="col col-xxl-3">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#f9e9f4">
                                        <i class="cv-business-process fs-4 align-middle" style="color:#c8238e;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Business&nbsp;Process</div>
                                    <h4 class="fw-normal mb-0">@ViewBag.ProcessCount</h4><span class="text-success">@* <sub>+1.0%</sub> *@</span>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                
                @if (Widgets.MyTaskList == 1)
                {
                    <div class="card mb-3">
                        <div class="card-header header border-0">
                            <h6 class="page_title d-flex align-items-center ">
                                <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                    <i class="cv-my-task-list align-middle text-primary fs-5"></i>
                                </span><span>My Task List</span>
                            </h6>
                        </div>
                        <div class="card-body pt-0" style="max-height: 188px; overflow-y:auto;">
                            <div class="mb-3">
                                <div class="d-flex align-items-start justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#2315a3;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold"
                                                      style="font-size:large;">@ViewBag.BusinessProcessWaiting </span>
                                                <span style="padding-left: 8px !important;">
                                                    Processes Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="TakeActionModal" data-id="1"
                                          data-status="@Convert.ToInt32(@BCPEnum.EntityType.BusinessProcess)"
                                          data-bs-toggle="modal" data-bs-target="#TakeActionModal" role="button">
                                        <i class="cv-action fs-4 text-primary"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#880ba7;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold"
                                                      style="font-size:large;">@ViewBag.RecoveryPlanWaiting</span>
                                                <span style="padding-left: 8px !important;">
                                                    Recovery Plan Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span role="button" class="TakeActionModal"
                                          data-id="@Convert.ToInt32(@BCPEnum.EntityType.RecoveryPlan)" data-status="1"
                                          data-bs-toggle="modal" data-bs-target="#TakeActionModal">
                                        <i class="cv-action fs-4 text-primary"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#cd086a;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold" style="font-size:large;">@ViewBag.RiskAssessmentWaiting</span> <span style="padding-left: 8px !important;">
                                                    Risk Assessments Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span role="button" class="TakeActionModal" data-bs-toggle="modal"
                                          data-id="@Convert.ToInt32(@BCPEnum.EntityType.RiskAssessment)" data-status="1"
                                          data-bs-target="#TakeActionModal"><i class="cv-action fs-4 text-primary"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                }


                <div class="d-grid" style="grid-template-rows: max-content;">
                    <div class="card-group gap-3 mb-3">
                        <div class="card rounded-2">
                            <div class="card-body p-2 d-flex align-items-center gap-2" role="button" id="NotifyIncident"
                                 onclick="NotifyIncidentClick()">
                                <span class="shadow-sm rounded-2 icon-circle" style="background-color:#fd8213;">
                                    <i class="cv-notify-incident text-white fs-4" style="line-height: 1.2;"></i>
                                </span>
                                <span>Notify Incident</span>
                            </div>
                        </div>
                        <div class="card rounded-2">
                            <div class="card-body p-2 d-flex align-items-center gap-2" role="button" id="NotifyIncident"
                                 onclick="NotifyTeamsClick()">
                                <span class="shadow-sm icon-circle rounded-2" style="background-color:#265ef9;">
                                    <i class="cv-notify-team align-middle text-white fs-5"
                                       style="line-height: 1.2;"></i>
                                </span>
                                <span>Notify Team</span>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header header border-0">
                            <form class="d-flex tab-design w-100">
                                <ul class="nav nav-tabs nav-justified w-100" id="myTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="incidentNotificationTab"
                                                data-bs-toggle="tab" type="button" role="tab" aria-selected="true">
                                            <h6 class="page_title d-flex align-items-center mb-0">
                                                <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                    <i class="cv-feed-notify align-middle text-primary fs-5"></i>
                                                </span>
                                                <span>Incident Summary</span>
                                            </h6>
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="teamNotificationTab" data-bs-toggle="tab"
                                                type="button" role="tab" aria-selected="false">
                                            <h6 class="page_title d-flex align-items-center mb-0">
                                                <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                    <i class="cv-notify-team align-middle text-primary fs-5"></i>
                                                </span><span>Team Notification</span>
                                            </h6>
                                        </button>
                                    </li>
                                </ul>
                            </form>
                        </div>
                        <div class="card-body pt-0 " id="notificationBlock"
                             style="height: calc(50vh - 45px); overflow-y: auto;">
                            @* <div class="card-body pt-0 " id="notificationBlock"> *@
                            @await Html.PartialAsync("_IncidentNotifications")
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @* Notify Incident Modal Start *@

    <div class="modal fade" id="NotifyIncidentModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">Notify Incident</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div id="NotifyIncidentBody">
                </div>
            </div>
        </div>
    </div>


    @* Notify Incident Modal End *@

    @* Business Process Approval Modal Start *@
    <div class="modal fade" id="TakeActionModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         data-bs-backdrop="static" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">Approval</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="tab-design">
                    <ul class="nav nav-tabs px-3 gap-3" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active  WatingforApproval" data-bs-toggle="tab" data-id="1"
                                    data-status="1" data-bs-target="#one-tab-pane" type="button" role="tab"
                                    aria-controls="one-tab-pane" aria-selected="false" tabindex="-1">
                                Wating for Approval
                            </button>
                        </li>
                        <li class="nav-item Approved" role="presentation">
                            <button class="nav-link" data-bs-toggle="tab" data-id="1" data-status="2"
                                    data-bs-target="#two-tab-pane" type="button" role="tab" aria-controls="one-tab-pane"
                                    aria-selected="true" tabindex="-1">
                                Approved
                            </button>
                        </li>
                        <li class="nav-item Disapproved" role="presentation">
                            <button class="nav-link" data-bs-toggle="tab" data-id="1" data-status="3"
                                    data-bs-target="#three-tab-pane" type="button" role="tab" aria-controls="one-tab-pane"
                                    aria-selected="false" tabindex="-1">
                                Disapproved
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="modal-body pt-1">
                </div>
            </div>
        </div>
    </div>
    @* Business Process Approval Modal End *@


    @* Risk Assessment Approval Modal Start *@


    @* Risk Assessment Approval Modal End *@

    <!--Notify Configuration Modal -->
    <div class="modal fade" id="NotifyModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
         data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">Notify Teams Configuration</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div id="NotifyBody">
                </div>
            </div>
        </div>
    </div>
    <!--End Notify Configuration Modal -->
</div>

@section Scripts {
    <script>
        $(document).ready(function () {

            var iId = "";
            var iStatusId = "";
            $(document).on('click', '.btnApproveProcess', function () {
                iId = $(this).data('id');
                iStatusId = $(this).data('status');
                iEntityTypeID = $(this).data('entitytypeid');
                $.get('@Url.Action("UpdateEntityStatus", "Dashboard")', { iId: iId, iStatusId: iStatusId, iEntityTypeID: iEntityTypeID }, function (data) {
                });
            });

            $(document).on('click', '.TakeActionModal', function () {
                iId = $(this).data('id');
                // $.get('@Url.Action("ApprovalPopup", "Dashboard")', { EntityTypeID: iId, iStatusId: iStatusId }, function (data) {
                //     var tableData = $('.modal-body');
                //     var tableData = $('.modal-body');
                //     tableData.empty();
                //     $('.modal-body').html(data);

                //     // $('.modal-body').html(data);
                //     // $('#TakeActionModal').modal('show');
                // });

                $('.modal-body').html('<div class="text-center"><i class="spinner-border text-primary"></i> Loading...</div>');
                $('#TakeActionModal').modal('show');
                $.get('@Url.Action("ApprovalPopup", "Dashboard")', { EntityTypeID: iId, iStatusId: iStatusId }, function (data) {
                    $('.modal-body').html(data);
                }).fail(function(error) {
                    console.error('Error loading approval popup:', error);
                    $('.modal-body').html('<div class="alert alert-danger">Failed to load content. Please try again.</div>');
                });
            });

            $(document).on('click', '.WatingforApproval', function () {
                iStatusId = 1;
                $.get('@Url.Action("ApprovalPopup", "Dashboard")', { EntityTypeID: iId, iStatusId: iStatusId }, function (data) {
                    var tableData = $('.modal-body');
                    var tableData = $('.modal-body');
                    tableData.empty();
                    $('.modal-body').html(data);

                    // $('.modal-body').html(data);
                    // $('#TakeActionModal').modal('show');
                });
            });

            $(document).on('click', '.Approved', function () {
                iStatusId = 2;
                $.get('@Url.Action("ApprovalPopup", "Dashboard")', { EntityTypeID: iId, iStatusId: iStatusId }, function (data) {

                    var tableData = $('.modal-body');
                    var tableData = $('.modal-body');
                    tableData.empty();
                    $('.modal-body').html(data);

                    // $('.modal-body').html(data);
                    // $('#TakeActionModal').modal('show');
                });
            });

            $(document).on('click', '.Disapproved', function () {
                iStatusId = 3;
                $.get('@Url.Action("ApprovalPopup", "Dashboard")', { EntityTypeID: iId, iStatusId: iStatusId }, function (data) {
                    var tableData = $('.modal-body');
                    var tableData = $('.modal-body');
                    tableData.empty();
                    $('.modal-body').html(data);

                    // $('.modal-body').html(data);
                    // $('#TakeActionModal').modal('show');
                });
            });

            $(document).on('click', '.btnDisApproveProcess', function () {
                var iId = $(this).data('id');
                iStatusId = $(this).data('status');
                iEntityTypeID = $(this).data('entitytypeid');
                $.get('@Url.Action("UpdateEntityStatus", "Dashboard")', { iId: iId, iStatusId: iStatusId, iEntityTypeID: iEntityTypeID }, function (data) {

                });
            });

            $('#NotifyModal').on('hidden.bs.modal', function () {
                clearStorage();
            });


            $(document).on('click', '#incidentNotificationTab', function () {
                $.ajax({
                    url: '@Url.Action("DisplayNotification", "Dashboard")',
                    type: 'GET',
                    data: { iNotificationTab: 0 },
                    success: function (data) {
                        $('#notificationBlock').html(data);
                    },
                    error: function (error) {
                        console.log('Error : ', error);
                    }
                })
            })

            $(document).on('click', '#teamNotificationTab', function () {
                $.ajax({
                    url: '@Url.Action("DisplayNotification", "Dashboard")',
                    type: 'GET',
                    data: { iNotificationTab: 1 },
                    success: function (data) {
                        $('#notificationBlock').html(data);
                    },
                    error: function (error) {
                        console.log('Error : ', error);
                    }
                })
            })

            // Load Risk Heatmap data when the Risk Heat Map tab is clicked
            $(document).on('click', '#disabled-tab', function () {
                //  debugger;
                //  var dataCondition;

                //  if ($("#inlineRadio1").prop("checked")) {

                //           dataCondition = $('#inlineRadio1').val();
                //     }
                //     else if ($("#inlineRadio2").prop("checked")) {

                //           dataCondition = $('#inlineRadio2').val();
                //     }
                //     else if ($("#inlineRadio3").prop("checked")) {

                //           dataCondition = $('#inlineRadio3').val();
                //     }

                // //                         $.ajax({
                // //     url: '@Url.Action("GetFilterRiskData", "Dashboard")',
                // //     type: 'GET',
                // //     data: { dataCondition: dataCondition },
                // //     success: function (data) {
                // //         console.log(data);
                // //         // $('#notificationBlock').html(data);
                // //     },
                // //     error: function (error) {
                // //         console.log('Error : ', error);
                // //     }
                // // })


                $.get('@Url.Action("GetFilterRiskData", "Dashboard")', {dataCondition : dataCondition}, function (data) {

                    console.log(data);
                    //debugger;
                     var tableData = $('#RiskFilterList');
                        var tableData = $('#RiskFilterList');
                        tableData.empty();
                        $('#RiskFilterList').html(data);

                });


                // if ($('#RiskHeatMap_Chart').children().length === 0) {
                //     $('#RiskHeatMap_Chart').html('<div class="d-flex justify-content-center align-items-center" style="height: 300px;"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><div class="ms-2">Loading Risk Heatmap...</div></div>');
                //     window.location.href = '@Url.Action("GetRiskHeatmapData", "Dashboard")';
                // }
            })

            function clearStorage() {
                localStorage.removeItem('fyaCheckboxesState');
                localStorage.removeItem('fyiCheckboxesState');
            }

       

            

            bindRiskDataToTables('all');

          

        });

        function NotifyIncidentClick() {
            $.ajax({
                url: '@Url.Action("NotifyIncidentForm", "NotifyIncidentForm", new { area = "BCMIncidentManagement" })',
                type: 'GET',
                contentType: 'application/json',
                success: function (data) {
                    $('#NotifyIncidentBody').html(data);
                    $('#NotifyIncidentModal').modal('show');
                },
                error: function (error) {
                    console.log('Error : ', error);
                }
            });
        }

        function NotifyTeamsClick() {
            $.ajax({
                url: '@Url.Action("BCMGroupsNotification", "BCMGroupsNotification", new { area = "BCMTeams" })',
                type: 'GET',
                contentType: 'application/json',
                data: { iGrpId: 0, strReturnUrl: '@Url.Action("Index", "Dashboard")' },
                success: function (data) {
                    $('#NotifyBody').html(data);
                    $('#NotifyModal').modal('show');
                },
                error: function (error) {
                    console.log('Error : ', error);
                }
            });
        }
    </script>

    <!-- #region To Do List Scripts Start-->

    <script>
        $(document).ready(function () {

            // To Do List filtering
            $('[data-filter]').on('click', function() {
                const filterType = $(this).data('filter');

                // Update active button
                $('[data-filter]').removeClass('active');
                $(this).addClass('active');

                // Implement filtering logic based on your data structure
                $.ajax({
                    url: '@Url.Action("FilterTodoItems", "Dashboard")',
                    type: 'GET',
                    data: { filter: filterType },
                    success: function(data) {
                        $('#todoListItems').html(data);

                        // Ensure all checkboxes are unchecked after filter change
                        uncheckAllTodoCheckboxes();
                    },
                    error: function(error) {
                        console.error('Error filtering todo items:', error);
                    }
                });
            });

            // Add new todo item
            $('#addTodoBtn').on('click', function() {
                const todoText = $('#newTodoText').val().trim();

                if (todoText) {
                    $.ajax({
                        url: '@Url.Action("AddTodoItem", "Dashboard")',
                        type: 'POST',
                        data: { description: todoText },
                        success: function(result) {
                            if (result.success) {
                                // Refresh the todo list
                                $('#newTodoText').val('');

                                // Reload the current filter
                                const currentFilter = $('[data-filter].active').data('filter');
                                $('[data-filter="' + currentFilter + '"]').trigger('click');
                            } else {
                                alert('Failed to add todo item: ' + result.message);
                            }
                        },
                        error: function(error) {
                            console.error('Error adding todo item:', error);
                            alert('An error occurred while adding the todo item.');
                        }
                    });
                } else {
                    alert('Please enter a description for the todo item.');
                }
            });

            // Handle actions on selected items
            $(document).on('click', '.todo-action', function(e) {
                e.preventDefault();

                const action = $(this).data('action');
                const selectedItems = [];

                // Use event delegation to find checkboxes within the partial view
                $('#todoListItems .todo-checkbox:checked').each(function() {
                    selectedItems.push($(this).val());
                });

                if (selectedItems.length === 0) {
                    alert('Please select at least one item');
                    return;
                }

                // Perform the selected action on the server
                $.ajax({
                    url: '@Url.Action("TodoBulkAction", "Dashboard")',
                    type: 'POST',
                    data: {
                        itemIds: selectedItems,
                        action: action
                    },
                    success: function(result) {
                        if (result.success) {
                            // Show success message
                            //showToast('Success', `Successfully ${action}d ${selectedItems.length} item(s)`);

                            // Uncheck all checkboxes
                            uncheckAllTodoCheckboxes();

                            // Reload the current filter after a short delay to show the visual feedback
                            setTimeout(function() {
                                const currentFilter = $('[data-filter].active').data('filter');
                                $('[data-filter="' + currentFilter + '"]').trigger('click');
                            }, 800);
                        } else {
                            // Show error message
                            //showToast('Error', result.message || 'Failed to perform action', 'error');

                            // Uncheck all checkboxes
                            uncheckAllTodoCheckboxes();

                            // Reload to reset visual state
                            const currentFilter = $('[data-filter].active').data('filter');
                            $('[data-filter="' + currentFilter + '"]').trigger('click');
                        }
                    },
                    error: function(error) {
                        console.error('Error performing bulk action:', error);
                        //showToast('Error', 'An error occurred while performing the action', 'error');

                        // Uncheck all checkboxes
                        uncheckAllTodoCheckboxes();

                        // Reload to reset visual state
                        const currentFilter = $('[data-filter].active').data('filter');
                        $('[data-filter="' + currentFilter + '"]').trigger('click');
                    }
                });
            });

            // Add event delegation for checkbox changes
            $(document).on('change', '.todo-checkbox', function() {
                // Optional: You can add code here to track checkbox state changes
                console.log('Checkbox changed:', $(this).val(), 'Checked:', $(this).is(':checked'));
            });

            // Global variable to track the ID of the item being edited
            let editingTodoId = null;

            // Handle edit button click
            $(document).on('click', '.edit-todo-btn', function() {
                // Get the item ID and description
                const itemId = $(this).data('id');
                const description = $(this).data('description');

                // Populate the input field with the item description
                $('#todoInput').val(description);

                // Store the item ID for later use
                editingTodoId = itemId;

                // Change the button text to "Update"
                $('#addTodoBtn').text('Update');

                // Show the cancel button
                $('#cancelEditBtn').show();

                // Focus the input field
                $('#todoInput').focus();
            });

            // Handle form submission (add/update todo)
            $('#todoForm').on('submit', function(e) {
                e.preventDefault();

                const description = $('#todoInput').val().trim();

                if (!description) {
                    alert('Please enter a task description');
                    return;
                }

                if (editingTodoId) {
                    // Update existing todo
                    $.ajax({
                        url: '@Url.Action("UpdateTodoItem", "Dashboard")',
                        type: 'POST',
                        data: {
                            id: editingTodoId,
                            description: description
                        },
                        success: function(result) {
                            if (result.success) {
                                // Reset the form
                                $('#todoInput').val('');
                                editingTodoId = null;
                                $('#addTodoBtn').text('Add');
                                $('#cancelEditBtn').hide();

                                // Reload the current filter
                                const currentFilter = $('[data-filter].active').data('filter');
                                $('[data-filter="' + currentFilter + '"]').trigger('click');
                            } else {
                                alert('Failed to update item: ' + result.message);
                            }
                        },
                        error: function(error) {
                            console.error('Error updating todo item:', error);
                            alert('An error occurred while updating the item.');
                        }
                    });
                } else {
                    // Add new todo
                    $.ajax({
                        url: '@Url.Action("AddTodoItem", "Dashboard")',
                        type: 'POST',
                        data: { description: description },
                        success: function(result) {
                            if (result.success) {
                                // Reset the form
                                $('#todoInput').val('');

                                // Reload the current filter
                                const currentFilter = $('[data-filter].active').data('filter');
                                $('[data-filter="' + currentFilter + '"]').trigger('click');
                            } else {
                                alert('Failed to add item: ' + result.message);
                            }
                        },
                        error: function(error) {
                            console.error('Error adding todo item:', error);
                            alert('An error occurred while adding the item.');
                        }
                    });
                }
            });

            // Add a cancel button event handler
            $('#cancelEditBtn').on('click', function() {
                // Reset the form
                $('#todoInput').val('');
                editingTodoId = null;
                $('#addTodoBtn').text('Add');
                $('#cancelEditBtn').hide();
            });

            function uncheckAllTodoCheckboxes() {
                $('#todoListItems .todo-checkbox').prop('checked', false);
            }

                    // Helper function to show toast notifications
        // function showToast(title, message, type = 'success') {
        //     // If you have a toast library, use it here
        //     // Example with Bootstrap toast:
        //     const toastHtml = `
        //         <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
        //             <div class="d-flex">
        //                 <div class="toast-body">
        //                     <strong>${title}</strong>: ${message}
        //                 </div>
        //                 <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        //             </div>
        //         </div>
        //     `;

        //     const toastContainer = document.getElementById('toast-container') || document.body;
        //     const toastElement = $(toastHtml);
        //     $(toastContainer).append(toastElement);

        //     // Initialize and show the toast
        //     const toast = new bootstrap.Toast(toastElement[0], { delay: 3000 });
        //     toast.show();
        // }
        });
    </script>

    <!-- #region To Do List Scripts End-->
    <!--Approval Click Start-->
    @* #processCount,#rpCount,#raCount,#bcmauditCount, *@
    @* <script>
        $(document).ready(function(){

            $(document).on('click','.TakeActionModal',function(){
                var iId = $(this).data('entity');
                var iStatusId = $(this).data('status');
                $('.modal-body').html('<div class="text-center"><i class="spinner-border text-primary"></i> Loading...</div>');
                $('#TakeActionModal').modal('show');
                $.get('@Url.Action("ApprovalPopup", "Dashboard")', { EntityTypeID: iId, iStatusId: iStatusId }, function (data) {
                    $('.modal-body').html(data);
                }).fail(function(error) {
                    console.error('Error loading approval popup:', error);
                    $('.modal-body').html('<div class="alert alert-danger">Failed to load content. Please try again.</div>');
                });
            });
        });
    </script> *@
    <!--Approval Click End-->
        #<!-- #Incident Timeline Scripts -->
    <script>
        function toggleStepDetails(button, IncidentStepID) {
            var detailsContent = document.getElementById('step-details-' + IncidentStepID);
            var icon = button.querySelector('i');

            // Toggle display
            if (detailsContent.style.display === 'none' || detailsContent.style.display === '') {
                detailsContent.style.display = 'block';
                if (icon) {
                    if (icon.classList.contains('bi-plus')) {
                        icon.classList.remove('bi-plus');
                        icon.classList.add('bi-dash');
                    }
                }
            } else {
                detailsContent.style.display = 'none';
                if (icon) {
                    if (icon.classList.contains('bi-dash')) {
                        icon.classList.remove('bi-dash');
                        icon.classList.add('bi-plus');
                    }
                }
            }

            // Prevent default action and stop event propagation
            return false;
        }
    </script>
    <!-- #endregion -->
}

<script src="~/lib/jquery/jquery.min.js"></script>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>

<script src="~/js/dashboard_chart/DepartmentBIA-Chart.js"></script>
<script src="~/js/dashboard_chart/BusinessProcessesBIA-Chart.js"></script>
<script src="~/js/dashboard_chart/OverAllKPIStatus-Chart.js"></script>
<script src="~/js/dashboard_chart/Critical_NonCritical-Chart.js"></script>
<script src="~/js/dashboard_chart/reviewinmeetings-chart.js"></script>

@* <script src="~/js/calendar.js"></script> *@

<script src="~/js/RiskHeatmap.js"></script>
@* <script src="~/js/Dashboard_Chart/DepartmentBIA_Chart.js?v=@DateTime.Now.Ticks"></script> *@
@* <script src="~/js/Dashboard_Chart/GRRS_Chart.js?v=@DateTime.Now.Ticks"></script> *@
@* <script src="~/js/Dashboard_Chart/OverallKPI_Chart.js?v=@DateTime.Now.Ticks"></script> *@
<script src="~/js/Dashboard_Chart/ReviewProgress_Chart.js?v=@DateTime.Now.Ticks"></script>
@* <script src="~/js/Dashboard_Chart/ReviewMeetings_Chart.js?v=@DateTime.Now.Ticks"></script> *@
@* <script src="~/js/Dashboard_Chart/CriticalProcesses_Chart.js?v=@DateTime.Now.Ticks"></script> *@





<script>
    // Pass dashboard data to JavaScript
    var dashboardData = {
        businessProcess: {
            initiated: @ViewBag.BusinessProcessInitiated, // Initiated (Status = 0)
            waiting: @ViewBag.BusinessProcessWaitingForApproval, // Waiting for Approval (Status = 1)
            approved: @ViewBag.BusinessProcessApproved, // Approved (Status = 2)
            critical: @ViewBag.DashboardCount.CriticalbusinessprocessCount, // Critical business processes
            nonCritical: @ViewBag.DashboardCount.NonCriticalbusinessprocessCount // Non-critical business processes
        },
        departmentBIA: {
            // Based on the reference code, these should be:
            // Approved (Status = 2) -> completed
            // Waiting for Approval (Status = 1) -> inProgress
            // Initiated (Status = 0) -> notStarted
            completed: @ViewBag.DepartmentBIAApproved, // Approved
            inProgress: @ViewBag.DepartmentBIAWaitingForApproval, // Waiting for Approval
            notStarted: @ViewBag.DepartmentBIAInitiated // Initiated
        },
        overallKPI: {
            completed: @ViewBag.KPICompletedPercentage, // Completed percentage
            inProgress: @ViewBag.KPIInProgressPercentage // In Progress percentage
        },
        reviewMeetings: {
            complete: @ViewBag.ReviewMeetingsApproved, // Approved (Status = 2)
            initiated: @ViewBag.ReviewMeetingsInitiated, // Initiated (Status = 0)
            scheduled: @ViewBag.ReviewMeetingsWaitingForApproval // Waiting for Approval (Status = 1)
        },
        reviewProgress: {
            bia: @ViewBag.ReviewProgressBiaPercentage,
            ra: @ViewBag.ReviewProgressRaPercentage,
            biaLabel: "@ViewBag.ReviewProgressBiaLabel",
            raLabel: "@ViewBag.ReviewProgressRaLabel",
            totalProcess:@ViewBag.BusinessProcessTotal,
            approvedProcess:@ViewBag.BusinessProcessApproved,
            totalKPI:@ViewBag.KPICount,
            approvedKPI:@ViewBag.ApprovedCount
        }
    };
</script>
<script>
    // Initialize risk data from the server
    //debugger;
    var riskDataJson = @Html.Raw(ViewBag.RiskDataJson ?? "[]")
</script>

<script src="~/js/Dashboard_Chart/DynamicChartData.js?v=@DateTime.Now.Ticks"></script>
<script>
    // Ensure all charts are properly initialized
    // This script will run after all chart scripts have loaded
    console.log('Setting up chart initialization...');

    // Function to initialize charts with dynamic data
    function initializeChartsWithDynamicData() {
        console.log('Initializing charts with dynamic data...');
        if (typeof loadDynamicChartData === 'function') {
            loadDynamicChartData();
        } else {
            console.error('loadDynamicChartData function not found');
        }
    }

    // Call the initialization function when the document is ready
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(function () {
            console.log('jQuery document ready, initializing charts...');
            initializeChartsWithDynamicData();
        });
    } else {
        // Fallback if jQuery is not available
        window.addEventListener('DOMContentLoaded', function () {
            console.log('DOMContentLoaded event, initializing charts...');
            initializeChartsWithDynamicData();
        });

        // Additional fallback with a delay
        setTimeout(function () {
            console.log('Delayed initialization of charts...');
            initializeChartsWithDynamicData();
        }, 1000);
    }
</script>

<script>
            document.addEventListener('DOMContentLoaded', function () {
        const calendarEl = document.getElementById('calendar');

        if (!calendarEl) {
            console.log('Calendar element not found');
            return;
        }

        console.log('Initializing FullCalendar...');
        let eventRenderCount = 0;

        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            initialDate: new Date(),
            themeSystem: "bootstrap",
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },

            allDaySlot: false,
            editable: true,
            droppable: true,
            navLinks: true,
            selectable: true,
            selectMirror: true,
                displayEventTime: false,
                    dayHeaderContent: function(arg) {
                const weekday = arg.date.toLocaleDateString('en-US', { weekday: 'short' });
                const day = arg.date.getDate().toString().padStart(2, '0');

                return {
                                html: `<span class="d-grid"><span class="text-secondary fw-normal">${weekday}</span><span class="text-dark fw-semibold Day-colum">${day}</span></span>`
                };
            },

            // select: function (arg) {
            //     const title = prompt('Event Title:');
            //     if (title) {
            //         calendar.addEvent({
            //             title: title,
            //             start: arg.start,
            //             end: arg.end,
            //             allDay: arg.allDay
            //         });
            //     }
            //     calendar.unselect();
            // },

            // eventClick: function (info) {
            //     alert('Event: ' + info.event.title);
            // },

            dayMaxEvents: true,

            // eventDidMount: function (info) {
            //     if (eventRenderCount < 6) {
            //         const colors = ['#ff9999', '#ffcc99', '#ffff99', '#ccff99', '#99ffcc', '#99ccff'];
            //                 const titleEl = info.el.querySelector('.fc-event-title');
            //         if (titleEl) {
            //             titleEl.style.backgroundColor = colors[eventRenderCount];
            //         }
            //         eventRenderCount++;
            //     }
            // },

            eventContent: function (arg) {
                let arrayOfDomNodes = [];

                // Title
                const titleEvent = document.createElement('div');
                if (arg.event.title) {
                    titleEvent.innerHTML = arg.event.title;
                    titleEvent.className = "fc-event-title";
                }

                // Image
                const imgEventWrap = document.createElement('div');
                if (arg.event.extendedProps.image_url) {
                        const imgEvent = `<img src="${arg.event.extendedProps.image_url}" class="img-fluid rounded-circle border border-white border-2" style="width:24px">`;
                    imgEventWrap.className = "fc-event-img";
                    imgEventWrap.innerHTML = imgEvent;
                }

                arrayOfDomNodes = [titleEvent, imgEventWrap];
                return { domNodes: arrayOfDomNodes };
            },

            events: function (fetchInfo, successCallback, failureCallback) {
                console.log('Loading calendar events...');
                $.ajax({
                    url: '@Url.Action("GetCalendarData", "Dashboard")',
                    method: 'GET',
                    dataType: 'json',
                    success: function (response) {
                        const events = [];
                        if (Array.isArray(response)) {
                            response.forEach(item => {
                                const eventObj = {
                                    id: item.eid,
                                    title: item.title,
                                    start: item.start,
                                    end: item.end !== item.start ? item.end : undefined,
                                    color: "#D6CCE6",
                                   textColor: "#333",
                                    allDay: true,
                                        extendedProps: {
                                            description: item.title,
                                                            image_url: '/img/Profile-img/Usericon.svg'
                                        }
                                };
                                events.push(eventObj);
                            });
                        }
                        successCallback(events);
                    },
                    error: function () {
                        console.warn('Using fallback test events');
                        successCallback([
                            {
                                id: 'test1',
                                title: 'Test Event 1',
                                start: new Date().toISOString().split('T')[0],
                               color: "#FF6B6B",
                               textColor: "#FFF",
                                allDay: true
                            },
                            {
                                id: 'test2',
                                title: 'Test Event 2',
                                start: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                               color: "#4ECDC4",
                                textColor: "#FFF",
                                allDay: true
                            }
                        ]);
                    }
                });
            }
        });

        calendar.render();
        console.log('FullCalendar rendered successfully');

         function mapImpactToLabel(impact) {
                if (!impact) return '';

                // Convert to string and normalize
                var imp = String(impact).toLowerCase();

                if (imp.includes('1') || imp.includes('insignificant')) return 'Insignificant';
                if (imp.includes('2') || imp.includes('low')) return 'Low';
                if (imp.includes('3') || imp.includes('medium')) return 'Medium';
                if (imp.includes('4') || imp.includes('high')) return 'High';
                if (imp.includes('5') || imp.includes('critical')) return 'Critical';

                return '';
            }

            function mapProbabilityToLabel(probability) {
                if (!probability) return '';

                // Convert to string and normalize
                var prob = String(probability).toLowerCase();

                if (prob.includes('1') || prob.includes('rare')) return 'Rare';
                if (prob.includes('2') || prob.includes('unlikely')) return 'Unlikely';
                if (prob.includes('3') || prob.includes('possible')) return 'Possible';
                if (prob.includes('4') || prob.includes('likely')) return 'Likely';
                if (prob.includes('5') || prob.includes('almost')) return 'Almost Certain';

                return '';
            }

            function clearTableCells(table) {
                if (!table) return;

                // Start from row 2 (skip headers) and column 1 (skip first column)
                for (var i = 2; i < table.rows.length; i++) {
                    var row = table.rows[i];
                    for (var j = 1; j < row.cells.length; j++) {
                        row.cells[j].textContent = '';
                    }
                }
            }

                   function bindRiskDataToTables(filterType) {
                console.log('Binding risk data to tables with filter:', filterType);

                // Get references to both tables
                var heatmapTable = document.getElementById('tblheatmapSeverity');
                var severityTable = document.getElementById('tblheatmapSeverity1');

                if (!heatmapTable || !severityTable) {
                    console.error('Tables not found in the DOM');
                    return;
                }

                // Clear any existing data in the cells
                clearTableCells(heatmapTable);

                // Create a data structure to track risk counts and codes in each cell
                var riskData = {};
                //debugger;
                // Process each risk and update the data structure
                riskDataJson.forEach(function(risk) {
                    var probability = '';
                    var impact = '';
                    var riskCode = risk.RiskCode || 'Unknown';

                    if (filterType === 'inherent' || filterType === 'all') {
                        // Use inherent risk values
                        probability = mapProbabilityToLabel(risk.LikliHood);
                        impact = mapImpactToLabel(risk.Impact);

                        // If we have valid probability and impact values
                        if (probability && impact) {
                            // Create a key for this cell
                            var key = probability + '|' + impact;

                            // Initialize the cell data if it doesn't exist
                            if (!riskData[key]) {
                                riskData[key] = {
                                    count: 0,
                                    riskCodes: []
                                };
                            }

                            // Increment the count
                            riskData[key].count++;

                            // Add the risk code if it's not already in the list
                            if (riskCode && !riskData[key].riskCodes.includes(riskCode)) {
                                riskData[key].riskCodes.push(riskCode);
                            }
                        }
                    }

                    if (filterType === 'residual' || filterType === 'all') {
                        // Use residual risk values if we're filtering for residual or showing all
                        var resProbability = mapProbabilityToLabel(risk.ResidualLikeliHood);
                        var resImpact = mapImpactToLabel(risk.ResidualImpact);

                        // If we have valid residual values and we're either showing residual or all risks
                        if (resProbability && resImpact && (filterType === 'residual' || filterType === 'all')) {
                            // Create a key for this cell
                            var resKey = resProbability + '|' + resImpact;

                            // Initialize the cell data if it doesn't exist
                            if (!riskData[resKey]) {
                                riskData[resKey] = {
                                    count: 0,
                                    riskCodes: []
                                };
                            }

                            // Increment the count
                            riskData[resKey].count++;

                            // Add the risk code if it's not already in the list
                            if (riskCode && !riskData[resKey].riskCodes.includes(riskCode)) {
                                riskData[resKey].riskCodes.push(riskCode);
                            }
                        }
                    }
                });

                // Update the heatmap table with risk counts and risk codes
                for (var i = 0; i < probabilityLabels.length; i++)
                {
                    var probability = probabilityLabels[i];
                    var row = heatmapTable.rows[i + 2]; // +2 to skip the header rows

                    if (!row) {
                        console.warn('Row not found for probability:', probability);
                        continue;
                    }

                    for (var j = 0; j < impactLabels.length; j++) {
                        var impact = impactLabels[j];
                        var cell = row.cells[j + 1]; // +1 to skip the first column (labels)

                        if (!cell) {
                            console.warn('Cell not found for impact:', impact);
                            continue;
                        }

                        // Create a key for this cell
                        var key = probability + '|' + impact;

                        // Get the data for this cell
                        var cellData = riskData[key];

                        if (cellData && cellData.count > 0) {
                            // Create a div to hold the count and risk codes
                            var countDiv = document.createElement('div');
                            countDiv.className = 'risk-count';
                            countDiv.textContent = cellData.count;

                            // Create a div to hold the risk codes
                            var codesDiv = document.createElement('div');
                            codesDiv.className = 'risk-codes';

                         if (cellData.riskCodes && cellData.riskCodes.length > 0)
                                  {
                                    // Check if riskCodes length exceeds 3
                                     if (cellData.riskCodes.length > 3)
                                     {
                                         codesDiv.style.maxHeight = '100px'; // Set max height for scroll
                                         codesDiv.style.overflowY = 'auto'; // Enable vertical scroll
                                     }

                                     cellData.riskCodes.forEach(function(code)
                                     {
                                       var codeSpan = document.createElement('span');
                                       codeSpan.className = 'risk-code';
                                       codeSpan.textContent = code;
                                       codeSpan.title = 'Risk Code: ' + code;
                                       codeSpan.onclick = function()
                                       {
                                           showRiskDetails(code);
                                       };
                                       codesDiv.appendChild(codeSpan);

                                       // Add a line break between codes
                                       codesDiv.appendChild(document.createElement('br'));
                                      });
                                    }

                            // Clear the cell and add the new content
                            cell.innerHTML = '';
                            // cell.appendChild(countDiv);
                            cell.appendChild(codesDiv);

                            // Add a tooltip with all risk codes
                            if (cellData.riskCodes && cellData.riskCodes.length > 0)
                            {
                                cell.title = 'Risk Codes: ' + cellData.riskCodes.join(', ');
                            }
                        }
                        else
                        {
                            cell.textContent = '';
                            cell.title = '';
                        }
                    }
                }

                console.log('Tables updated with risk counts and codes');
            }

              function showRiskDetails(riskCode)
            {
                console.log('Showing details for risk code:', riskCode);

                // Find the risk with the matching code
                var risk = riskDataJson.find(function(r) {
                    return r.RiskCode === riskCode;
                });

               if (risk)
                       {
                         // Create a formatted message with risk details
                          var message = '<b>Risk Details:</b> <hr></hr> ' ;

                          message += ' <b>Risk Code:</b>' + (risk.RiskCode || 'N/A') + '<br>';
                          message += ' <b>Risk Name:</b>' + (risk.RiskName || 'N/A') + '<br>';
                          message += ' <b>Risk Description:</b>' + (risk.RiskDescription || 'N/A')+'<br>';
                          message += ' <b>Risk Category:</b>' + (risk.RiskCategory || 'N/A') + '<br><br>';

                          message += ' <b>Risk Owner:</b>' + (risk.RiskOwnerName || 'N/A') + '<br>';
                          message += ' <b>Risk Entry Date:</b>' + (risk.RiskEntryDate || 'N/A') + '<br>';
                          message += ' <b>Last Review Date:</b>' + (risk.LastRevDate || 'N/A') + '<br>';
                          message += ' <b>Next Review Date:</b>' + (risk.NxtRevDate || 'N/A') + '<br><br>';

                          message += ' <b>Inherent Impact:</b>' + (risk.Impact || 'N/A') +'&nbsp;' ;
                          message += ' <b>Inherent Likelihood:</b>' + (risk.LikliHood || 'N/A') + '<br><br>';
                          message += ' <b>Residual Impact:</b>' + (risk.ResidualImpact || 'N/A') +'&nbsp;' ;
                          message += ' <b>Residual Likelihood:</b>' + (risk.ResidualLikeliHood || 'N/A') + '<br><br>';

                          // Add More Information link button
                          message += '<div style="text-align: center; margin-top: 10px;">';
                          message += '<a href="/BCMRiskAssessment/ManageRisk/ManageRisk?RiskCode=' + (risk.RiskCode || '') + '" ';
                          // message += '<a href="/BCMRiskAssessment/ManageRisk/ManageRisk?RiskID=' + (risk.RiskID || '') + '" ';
                          message += 'style="display: inline-block; padding: 6px 12px; background-color: #007bff; color: white; ';
                          message += 'text-decoration: none; border-radius: 4px; font-weight: bold; transition: background-color 0.3s;" ';
                          message += 'onmouseover="this.style.backgroundColor=\'#0056b3\'" onmouseout="this.style.backgroundColor=\'#007bff\'">';
                          message += 'More Information</a></div>';


                          // Show the details in a tooltip instead of an alert
                          var tooltipElement = document.createElement('div');
                          tooltipElement.className = 'tooltip-risk-details';
                          tooltipElement.innerHTML = message; // Use innerHTML instead of textContent

                          // Style the tooltip
                          tooltipElement.style.position = 'absolute';
                          tooltipElement.style.backgroundColor = '#fff';
                          tooltipElement.style.border = '1px solid #ccc';
                          tooltipElement.style.padding = '15px';
                          tooltipElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                          tooltipElement.style.zIndex = '1000';
                          tooltipElement.style.maxWidth = '550px';
                          tooltipElement.style.borderRadius = '5px';
                          tooltipElement.style.lineHeight = '1.5';
                          tooltipElement.style.fontSize = '14px';

                          // Append the tooltip to the body
                          document.body.appendChild(tooltipElement);

                          // Position the tooltip near the clicked element
                          tooltipElement.style.left = event.pageX + 'px';
                          tooltipElement.style.top = event.pageY + 'px';

                          // Remove the tooltip after a delay (10 seconds to give more time to read)
                          setTimeout(function()
                          {
                            document.body.removeChild(tooltipElement);
                          }, 10000);
                      }

            else
            {
              alert('Risk details not found for code: ' + riskCode);
            }
        }

    });
</script>

@* <script src="~/js/riskheatmap_dynamic.js"></script> *@
@* <script src="~/js/RiskHeatmap_DynamicNew.js"></script> *@
<script src="~/lib/full-calendar/index.global.min.js"></script>


