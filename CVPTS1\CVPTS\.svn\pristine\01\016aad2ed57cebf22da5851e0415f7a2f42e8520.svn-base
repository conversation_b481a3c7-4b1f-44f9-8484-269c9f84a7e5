﻿@{
    ViewBag.Title = "BCMWorkbench";
    Layout = "~/Views/Shared/_Layout.cshtml";

}
<style>
    #chartdiv {
  width:75%;
  height: 100px;
    }
</style>
<div class="Page-Header">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">BCM Workbench</h6>
        <div class="d-flex gap-3 justify-content-end align-items-end">
            <div class="input-group Search-Input">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control" placeholder="Search">
            </div>
            <div class="dropdown">
                <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                    <i class="cv-filter align-middle" title="View Filter"></i>
                </button>
                <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                    <div class="mb-3">
                        <label>Organizations</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                            <select class="form-select form-control" aria-label="Default select example">
                                <option selected>Select Organizations</option>
                                <option value="1">PTS</option>
                                <option value="2">TCS</option>
                                <option value="3">Continuity Vault</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Units</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                            <select class="form-select form-control" id="unitlist" aria-label="Default select example">
                                <option selected>Select Units</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-department"></i></span>
                            <select class="form-select form-control" id="departmentlist" aria-label="Default select example">
                                <option selected>Select Departments</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Entities</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-entity"></i></span>
                            <select class="form-select form-control" id="departmentlist" aria-label="Default select example">
                                <option selected>Select Entities</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Business Entities</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-entity"></i></span>
                            <select class="form-select form-control" id="departmentlist" aria-label="Default select example">
                                <option selected>Select Business Entities</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Critical</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-Critical"></i></span>
                            <select class="form-select form-control" id="departmentlist" aria-label="Default select example">
                                <option selected>Select Critical</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" class="btn btn-sm btn-primary">Search</button>
                    </div>
                </form>
            </div>
            <button title="Refresh PCI Score" type="button" class="btn btn-primary btn-sm"><i class="cv-refresh align-middle"></i></button>
            @*<button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#NormalModal"><i class="cv-Plus" title="Create New"></i>Create</button> *@
        </div>
    </div>
</div>

<div class="Page-Condant card border-0" style="height: calc(100vh - 108px);overflow: auto;">
    <table class="table table-hover" style="width:100%;vertical-align:middle">
        <thead class="Workbench-head position-sticky top-0 z-3">
            <tr>
                <th class="text-start">Business&nbsp;Entity</th>
                <th>Initiated</th>
                <th>BIA&nbsp;Completed</th>
                <th>Approved</th>
                <th>Reviewed</th>
                <th>RA&nbsp;Done</th>
                <th>RA&nbsp;Reviewed</th>
                <th>Risk&nbsp;Recovery&nbsp;Profile</th>
                <th>Drill&nbsp;Strategy&nbsp;Execution</th>
                <th>PCI&nbsp;Score</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="">
                    <span class="fw-semibold text-info">PRC-2023-185</span>
                    <span>CVCP Application 5.0</span>
                    <span>Status : <span class="text-info">Initiated</span></span>
                </td>
                <td>
                    <div class="position-relative">
                        <div class="progress bg-success" role="progressbar" aria-valuemax="100" style="height: 1px;"></div>
                        <span role="button" class="position-absolute top-0 start-0 translate-middle d-flex align-items-center justify-content-center rounded bg-success text-white" style="width: 2rem; height:2rem;"><i class="cv-success fs-5 fw-bold"></i></span>
                    </div>
                </td>
                <td>
                    <div class="position-relative">
                        <div class="progress bg-danger" role="progressbar" aria-valuemax="100" style="height: 1px;"></div>
                        <span role="button" class="position-absolute top-0 start-50 translate-middle d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                    </div>
                </td>
                <td>
                    <div class="position-relative">
                        <div class="progress bg-danger" role="progressbar" aria-valuemax="100" style="height: 1px;"></div>
                        <span role="button" class="position-absolute top-0 start-50 translate-middle d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                    </div>
                </td>
                <td>
                    <div class="position-relative">
                        <div class="progress bg-danger" role="progressbar" aria-valuemax="100" style="height: 1px;"></div>
                        <span role="button" class="position-absolute top-0 start-50 translate-middle d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                    </div>
                </td>
                <td>
                    <div class="position-relative">
                        <div class="progress bg-danger" role="progressbar" aria-valuemax="100" style="height: 1px;"></div>
                        <span role="button" class="position-absolute top-0 start-50 translate-middle d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                    </div>
                </td>
                <td>
                    <div class="position-relative">
                        <div class="progress bg-danger" role="progressbar" aria-valuemax="100" style="height: 1px;"></div>
                        <span role="button" class="position-absolute top-0 start-50 translate-middle  d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                    </div>
                </td>
                <td>
                    <div>
                        <div class="position-relative">
                            <div class="progress bg-danger" role="progressbar" aria-label="Progress" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="height: 1px;">
                                <div class="progress-bar"></div>
                            </div>
                            <span type="button" class="position-absolute top-0 start-0 translate-middle d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                            <span type="button" class="position-absolute top-0 start-50 translate-middle d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                            <span type="button" class="position-absolute top-0 start-100 translate-middle d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="position-relative">
                        <div class="progress bg-danger" role="progressbar" aria-valuemax="100" style="height: 1px;"></div>
                        <span role="button" class="position-absolute top-0 start-50 translate-middle d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                    </div>
                </td>
                <td class="text-center">
                    <div class="d-flex align-items-center gap-2">
                        <div id="chartdiv"></div> 
                        <span class="h6" role="button" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-view"></i></span>
                    </div>
                    
                   
                </td>
            </tr>
        </tbody>
    </table>
</div>


@* Configure Facility Modal Start *@
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <div class="d-flex w-100 align-items-center justify-content-between">
                    <div class="d-flex align-items-center gap-3">
                        <h6 class="Page-Title">PRC 2019 75</h6>
                        <h6 class="Page-Title">Panchshil Techpark</h6>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <h6 class="Page-Title text-primary">0%</h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
              
               
            </div>
            <div class="modal-body">
                <div class="list-group list-group-horizontal fw-semibold bg-white">
                    <div class="border-0 flex-fill p-2" style="width:3rem"></div>
                    <div class="border-0 w-25 flex-fill p-2">Step Status</div>
                    <div class="border-0 w-25 flex-fill p-2">Compliance Step</div>
                    <div class="border-0 w-25 flex-fill p-2">Step Weightage</div>
                    <div class="border-0 w-25 flex-fill p-2">Step Score</div>
                </div>
                <div>
                    <div>
                        <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal">
                            <div class="flex-fill p-2 border-0" style="width:3rem">
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                <span type="button" class="d-flex align-items-center justify-content-center rounded bg-success text-white" style="width: 2rem; height:2rem;"><i class="cv-success fs-5 fw-bold"></i></span>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                Process Initiated
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                10%
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                10%
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal">
                            <div class="flex-fill p-2 border-0" style="width:3rem">
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                <span type="button" class="d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                Process BIA Completed
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                15%
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                0%
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal">
                            <div class="flex-fill p-2 border-0" style="width:3rem">
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                <span type="button" class="d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                Process Approved
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                15%
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                0%
                            </div>
                        </div>
                    </div> 
                    <div >
                        <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal">
                            <div class="flex-fill p-2 border-0" style="width:3rem">
                                <span class="toggle-password d-block" role="button" data-bs-toggle="collapse" data-bs-target="#reviewtypeparent1" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                <span type="button" class="d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                Process Approved
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                15%
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                0%
                            </div>
                        </div>
                       @*  sub parent *@
                        <div class="collapse" id="reviewtypeparent1">
                            <div class="list-group list-group-horizontal fw-semibold" style="background-color:var(--bs-info-border-subtle)">
                                <div class="border-0 flex-fill p-2" style="width:3rem"></div>
                                <div class="border-0 w-25 flex-fill p-2"></div>
                                <div class="border-0 w-25 flex-fill p-2">Artifact</div>
                                <div class="border-0 w-25 flex-fill p-2">
                                    Prop.Artifact Weight
                                </div>
                                <div class="border-0 w-25 flex-fill p-2">
                                    Artifact Score
                                </div>
                            </div>
                            <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal">
                                <div class="flex-fill p-2 border-0" style="width:3rem">
                               
                                </div>
                                <div class="w-25 flex-fill p-2 border-0 ">
                                    <span type="button" class="d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                                </div>
                                <div class="w-25 flex-fill p-2 border-0 ">
                                    RAO-2022-91
                                </div>
                                <div class="w-25 flex-fill p-2 border-0 ">
                                   
                                </div>
                                <div class="w-25 flex-fill p-2 border-0 ">
                                    0
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal">
                            <div class="flex-fill p-2 border-0" style="width:3rem">
                                <span class="toggle-password d-block" role="button" data-bs-toggle="collapse" data-bs-target="#reviewtypeparent2" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                <span type="button" class="d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                Risk Recovery Profile
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                10%
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                0%
                            </div>
                        </div>
                        @*  sub parent *@
                        <div class="collapse" id="reviewtypeparent2">
                            <div class="list-group list-group-horizontal fw-semibold" style="background-color:var(--bs-info-border-subtle)">
                                <div class="border-0 flex-fill p-2" style="width:3rem"></div>
                                <div class="border-0 w-25 flex-fill p-2"></div>
                                <div class="border-0 w-25 flex-fill p-2">Artifact</div>
                                <div class="border-0 w-25 flex-fill p-2">
                                    Prop.Artifact Weight
                                </div>
                                <div class="border-0 w-25 flex-fill p-2">
                                    Artifact Score
                                </div>
                            </div>
                            <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal">
                                <div class="flex-fill p-2 border-0" style="width:3rem">
                                </div>
                                <div class="w-25 flex-fill p-2 border-0 ">
                                    <span type="button" class="d-flex align-items-center justify-content-center rounded bg-danger text-white" style="width: 2rem; height:2rem;"><i class="cv-reject fs-5 fw-bold"></i></span>
                                </div>
                                <div class="w-25 flex-fill p-2 border-0 ">
                                    RAO-2022-91
                                </div>
                                <div class="w-25 flex-fill p-2 border-0 ">
                                </div>
                                <div class="w-25 flex-fill p-2 border-0 ">
                                    0
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>

@* Configure Facility Modal End *@

<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/workbench.js"></script>