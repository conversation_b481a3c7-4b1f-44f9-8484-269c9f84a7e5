﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Security.Helper;
using BCM.Shared;
using DevExpress.Charts.Native;
using Humanizer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore.Metadata;
using MySqlX.XDevAPI;

namespace BCM.UI.Areas.BCMFunctionRecoveryPlan.Controllers;
[Area("BCMFunctionRecoveryPlan")]
//[Route("WorkflowConfiguration/[action]")]
public class WorkflowConfigurationController : Controller
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    private readonly ILoggerFactory? _LoggerFactory;
    ManageUsersDetails _UserDetails = new ManageUsersDetails();
    private int iPlanID = 0;

    public WorkflowConfigurationController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _UserDetails = _Utilities.LoginUserDetails();


        if (_UserDetails == null)
        {
            RedirectToAction("Login", "Login");
        }
        _CVLogger = CVLogger;
    }


    #region Diagram Code Start

    public IActionResult ManageWorkflowConfiguration(string strPlanID)
    {
        strPlanID = strPlanID=="0" ? HttpContext.Session.GetString("Planid") : CryptographyHelper.Decrypt(strPlanID.ToString());
        ViewData["PlanId"]=strPlanID;
        HttpContext.Session.SetString("Planid", strPlanID);
        try
        {
            PopulateDropDown(strPlanID);
            //GetWorkflowActionById(240);
            return View();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            //return NotFound("No Records Found.");
            return BadRequest(ex.Message);
        }

    }

    [HttpGet]
    public string GetWorkflowConfigurationDiagram(string strPlanID)
    {
        try
        {
            // Get workflow actions for the plan
            var workflowActions = _ProcessSrv.GetRecoveryPlanByID(Convert.ToInt32(strPlanID));
            return workflowActions.XML;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return ex.Message;
        }
    }

    [HttpPost]
    public int SaveWorkflowConfigurationDiagram([FromBody] Workflow objPlanInfo)
    {
        int iPlanID = 0;
        try
        {
            objPlanInfo.ChangedBy = _UserDetails.UserID.ToString();
            iPlanID = _ProcessSrv.WorkflowToRecoveryPlan_XMLSave(objPlanInfo, Convert.ToInt32(objPlanInfo.Id));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return iPlanID;
    }

    #endregion  Diagram Code End


    #region Step Configuration Code Start

    [HttpGet]
    public IActionResult GetWorkflowActionById(int iID)
    {
        WorkflowActionInfo objWorkflowActionInfo = new WorkflowActionInfo();
        var Planid = HttpContext.Session.GetString("Planid");
        try
        {
            PopulateDropDown(Planid);
            objWorkflowActionInfo = _ProcessSrv.WorkflowAction_getbyID(iID);
            List<RecoveryStepsDependentMapping> objStepsDetails = _ProcessSrv.RecoveryStepsDependentMapping_ByStepID(iID.ToString());
            objWorkflowActionInfo.objDependedOnCheckListNew = objStepsDetails.Select(step => step.DependentStepID).ToArray();
            if (objWorkflowActionInfo != null)
            {
                return Json(new
                {
                    Name = objWorkflowActionInfo.Name,
                    Description = objWorkflowActionInfo.Description,
                    StepOwnerId = objWorkflowActionInfo.Stepownerid,
                    AlterStepOwnerID = objWorkflowActionInfo.Aleternatespetownerid,
                    GoToStep = objWorkflowActionInfo.GoToStep,
                    EstimationTime = objWorkflowActionInfo.Estimationtime,
                    TimeUnit = objWorkflowActionInfo.TimeUnit,
                    InterDependancy = objWorkflowActionInfo.InterDependancy,
                    objDependedOnCheckListNew = objWorkflowActionInfo.objDependedOnCheckListNew,
                });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpPost]
    public string SaveWorkflowAction([FromBody] WorkflowActionInfo objWorkflowActionInfo)
    {
        int iSuccess = 0;
        string strPlanId = HttpContext.Session.GetString("Planid");
        objWorkflowActionInfo.PlanID = strPlanId;
        objWorkflowActionInfo.ChangedBy = _UserDetails.UserID.ToString();
        objWorkflowActionInfo.IsUsedinWorkflow = "1";
        objWorkflowActionInfo.Estimationtime = objWorkflowActionInfo.Estimationtime=="" ? "0" : objWorkflowActionInfo.Estimationtime;
        objWorkflowActionInfo.CPProfileID = "0";
        objWorkflowActionInfo.TimeUnit = objWorkflowActionInfo.TimeUnit=="1" ? "1" : objWorkflowActionInfo.TimeUnit;

        if (objWorkflowActionInfo.InterDependancy!= null ||objWorkflowActionInfo.InterDependancy!= "")
        {
            objWorkflowActionInfo.InterDependancy = objWorkflowActionInfo.InterDependancy == "true" ? "1" : "0";
        }
        else
        {
            objWorkflowActionInfo.InterDependancy = "0";
        }

        if (objWorkflowActionInfo.ID == null || objWorkflowActionInfo.ID.ToString() == "0" ||objWorkflowActionInfo.ID.ToString() == "")
        {
            iSuccess = _ProcessSrv.WorkflowAction_Save(objWorkflowActionInfo);
        }

        else
        {
            iSuccess = _ProcessSrv.WorkflowAction_Update(objWorkflowActionInfo);
        }

        if (iSuccess > 0 && Convert.ToInt32(objWorkflowActionInfo.InterDependancy) > 0)
        {
            if (objWorkflowActionInfo.objDependedOnCheckListNew != null && objWorkflowActionInfo.objDependedOnCheckListNew.Count() > 0)
            {
                int iRes = _ProcessSrv.RecoveryStepsDependentMapping_Save(Convert.ToInt32(iSuccess), objWorkflowActionInfo.objDependedOnCheckListNew);
            }
        }

        else if (Convert.ToInt32(objWorkflowActionInfo.InterDependancy) == 0)
        {
            bool iRes = _ProcessSrv.RecoveryStepsDependentMapping_DeleteByStepID(iSuccess.ToString());
        }

        return iSuccess.ToString();
    }

    public void PopulateDropDown(string strPlanID = "0")
    {
        try
        {

            ViewBag.ResourceList = _Utilities.GetAllResourceList();
            ViewBag.ddlLoadProperty = _ProcessSrv.WorkflowAction_LoadActions(strPlanID);
            ViewBag.DependentOnList = _ProcessSrv.WorkflowAction_LoadActions(strPlanID);

            //ViewBag.ddlWorkflow = _ProcessSrv.GetAllCPWorkflows();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    [HttpGet]
    public JsonResult GetAllStepsForInterdependencyBinding()
    {
        try
        {
            string Planid = HttpContext.Session.GetString("Planid");
            var objStepList = _ProcessSrv.WorkflowAction_LoadActions(Planid);
            return Json(objStepList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    #endregion  Step Configuration Code Start
}


