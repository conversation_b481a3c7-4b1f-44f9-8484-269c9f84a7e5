﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{

    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="Page-Header d-flex align-items-center justify-content-between mb-3">
    <h6 class="Page-Title">BIA Default</h6>
</div>

<div class="Page-Condant card border-0">
    <div class="card-body">
        <div class="row">
            <div class="col-12">
                <p class="fw-semibold">
                    Instructions and Guidelines
                </p>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <div class="accordion accordion-flush" id="accordionFlushExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                            Default BIA Section
                                        </button>
                                    </h2>
                                    <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                                        <div class="accordion-body">
                                            <p>Hi,Welcome to</p>
                                            @*  <ul>
                                            <li>
                                            Prioritized Activity List - What are the must do recoveries related activities to be performed once the process is resumed? What are the tasks that can be put on hold and postponed with minimal impact?
                                            </li>
                                            </ul> *@
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="col-12">
                <div>
                    <p class="fw-semibold">Configure Default Section for Human Resource ( PRC-2020-131 )</p>
                    <div class="">
                        <p class="text-primary d-flex align-items-center gap-1">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsequestion1" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                            Question: Who they are?
                        </p>
                        <div class="ps-2 collapse show" id="collapsequestion1">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Answer</th>
                                        <th>Question</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>A</td>
                                        <td>Who they are?</td>
                                        <td>
                                            <span class="btn-action btnEdit" type="button" data-id="6" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                            <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>aAA</td>
                                        <td>Who they are?</td>
                                        <td>
                                            <span class="btn-action btnEdit" type="button" data-id="6" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                            <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-lable">Version</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-version"></i></span>
                                <input class="form-control" type="text" readonly value="1" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-lable">Questions</label>
                            <div class="d-flex justify-content-between">
                                <div">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="option1">
                                        <label class="form-check-label" for="inlineRadio1">Who they are?</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="option2">
                                        <label class="form-check-label" for="inlineRadio2">How they do it?</label>
                                    </div>
                                    <div class="form-check ">
                                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="option3">
                                        <label class="form-check-label" for="inlineRadio3">When do they do it?</label>
                                    </div>
                                </div">
                                <div">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="option1">
                                        <label class="form-check-label" for="inlineRadio1">How long can they go without doing any work?</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="option2">
                                        <label class="form-check-label" for="inlineRadio2">Which systems and equipment do they use?</label>
                                    </div>
                                    <div class="form-check ">
                                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="option3">
                                        <label class="form-check-label" for="inlineRadio3">Who do they need to do it with?</label>
                                    </div>
                                </div">
                            </div>
                        </div>

                    </div>
                </div>



            </div>
            <div>
                <p class="fw-semibold">Configure Default Section</p>
                <div class=" row row-cols-2">
                    <div class="form-group">
                        <label class="form-label">Answer</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-answer"></i></span>
                            <textarea class="form-control" placeholder="Enter Answer" style="height:0px"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-description"></i></span>
                            <textarea class="form-control" placeholder="Enter Description" style="height:0px"></textarea>
                        </div>
                    </div>
                </div>
                <div>
                    <p class="text-primary d-flex align-items-center gap-1">
                        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAdditional" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                        Additional Fields
                    </p>
                    <div class="row row-cols-2 collapse show" id="collapseAdditional">
                        <div class="form-group">
                            <label class="form-label">Field1</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-version"></i></span>
                                <textarea class="form-control" placeholder="Enter Field1" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Field2</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-version"></i></span>
                                <textarea class="form-control" placeholder="Enter Field2" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Field3</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-version"></i></span>
                                <textarea class="form-control" placeholder="Enter Field3" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Field4</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-version"></i></span>
                                <textarea class="form-control" placeholder="Enter Field4" style="height:0px"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="text-end me-4 pb-3">
                        <button class="btn btn-sm btn-outline-primary">Back</button>
                        <button class="btn btn-sm btn-secondary">Cancel</button>
                        <button class="btn btn-sm btn-primary">View All</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



</div>
<div class="modal fade" id="ITModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">IT Services </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Service Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Service Name" />
                            </div>

                        </div>
                        <div class="form-group">
                            <label class="form-label">Owner</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                                <select class="form-select form-select-sm">
                                    <option></option>
                                    <option>Selvam</option>
                                </select>
                            </div>

                        </div>
                        <div class="form-group">
                            <label class="form-label">Service Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                                <textarea class="form-control" placeholder="Enter Service Description"></textarea>
                            </div>
                        </div>
                        <div>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Service Name</th>
                                        <th>Description</th>
                                        <th>Owner</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>E-Mail Services</td>
                                        <td>E-Mail Services</td>
                                        <td>Alia Al Hosani</td>
                                        <td>
                                            <span class="btn-action btnEdit" type="button" data-id="6" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                            <span class="btn-action btnDelete" type="button" data-id="6"><i class="cv-delete text-danger" title="Delete"></i></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>File & Print services</td>
                                        <td>File & Print services</td>
                                        <td>Alia Al Hosani</td>
                                        <td>
                                            <span class="btn-action btnEdit" type="button" data-id="6" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                            <span class="btn-action btnDelete" type="button" data-id="6"><i class="cv-delete text-danger" title="Delete"></i></span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/delete.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->