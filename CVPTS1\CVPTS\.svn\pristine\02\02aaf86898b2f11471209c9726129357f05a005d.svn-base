﻿

const sampleData = [
    { category: "Q1", value: 25 },
    { category: "Q2", value: 40 },
];
let selectizeInstance;
let datasetData = ""
let filterdataset = ""
let queryName;
let rowCount = 0;
let fieldCount = 0;
let globalqueryData=[]
const fieldContainer = document.getElementById('fieldContainer');

$(document).ready(function () {



    
    //const addRowBtn = document.getElementById('addRowBtn');



    //addRowBtn.addEventListener('click', addRow);

    //// Add initial two rows
    //addRow();
    //addRow();

    $(".ChartTypeForm").removeClass('d-none')

    
    $('#WidgetType').selectize({
        onChange: function (value) {
            $("#BodyContainer").empty();
            if (value == 'chart') {
                $(".tableForm").addClass('d-none')
                $(".ChartTypeForm").removeClass('d-none')
                $(".fieldForm").removeClass('d-none')
                $(".chartHeightWidth").removeClass('d-none')
            }
            else if (value == 'card') {
                $(".tableForm").addClass('d-none')
                $(".ChartTypeForm").addClass('d-none')
                $(".fieldForm").addClass('d-none')
                $(".chartHeightWidth").addClass('d-none')
            }
            else {
                $(".tableForm").removeClass('d-none')
                $(".fieldForm").addClass('d-none')
                $(".ChartTypeForm").addClass('d-none')
                $(".chartHeightWidth").addClass('d-none')
                //addColumn()
                renderChart(value, 'BodyContainer', sampleData, "", 'category', 'value');
               
            }
        }
    })

     selectizeInstance = $('#SPDataset').selectize({
        valueField: 'id',
        labelField: 'dataSetName',
        searchField: 'dataSetName',
        options: [],        // Start empty
        create: false,
        onChange: function (value) {
            debugger

            filterdataset = datasetData.filter((data) => { return data.id == value })

            queryName = filterdataset[0].storedQuery

            DatasetRunQuery(filterdataset[0].storedQuery)

        }
    })[0].selectize;


    $("#height").on("keyup", function (e) {

        $(".bcm-box").css('height', e.target.value)
    })

    $("#Width").on("keyup", function (e) {

        $(".bcm-box").css('width', e.target.value)
    })

    $("#Chartheight").on("keyup", function (e) {

        $("#BodyContainer").css('height', e.target.value)
    })

    $("#ChartWidth").on("keyup", function (e) {

        $("#BodyContainer").css('width', e.target.value)
    })



    $('#ChartType').selectize({
                onChange: function (value) {
                    $("#widgetCreationCard").empty()
                    console.log("Changed value (selectize):", value);
                    let name = $("#WidgetName").val();
            let ChartType = $("#ChartType option:selected").val()
            
            renderChart(ChartType, 'BodyContainer', sampleData, name, 'category','value');
                    //renderChart(ChartType, 'widgetCreationCard', sampleData, name);
                }
    });


    $("#WidgetName").on('keyup', function (e) {

        $("#headerName").text(e.target.value)

    })


    $("#search-widget").on('keyup', function () {

        var filter = $(this).val();
        let categoryFlagStatus = true
        $("#prebuildList .widgetName").each(function () {

            var $i = 0;

            var splitText = $(this).text().split(" ")
            if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }

            if ($i > 0) {
                $(this).closest("#prebuildList .widgetList").show();
                categoryFlagStatus = false
            } else {
                $(this).closest("#prebuildList .widgetList").hide();
            }
        });
    })

    $("#search-widget").on('keyup', function () {

        var filter = $(this).val();
        let categoryFlagStatus = true
        $("#customList .widgetName").each(function () {

            var $i = 0;

            var splitText = $(this).text().split(" ")
            if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }

            if ($i > 0) {
                $(this).closest("#customList .widgetList").show();
                categoryFlagStatus = false
            } else {
                $(this).closest("#customList .widgetList").hide();
            }
        });
    })


    WidgetBuilderList()


    $("#createCanvas").on('click', function () {
        $(".headerList").addClass('d-none')
        $(".widgetbuilderblock").removeClass('d-none')
        $(".btn_save").text("Save")
        GetAllDatasets()
        WidgetAllClear()
       

    })
    let tableArray = []

    $(".btn_Show").on('click', function () {


        let xaxis = $("#xaxis").val()
        let yaxis = $("#yaxis").val()
        debugger
        let ChartType = $("#ChartType option:selected").val()
        let WidgetType = $("#WidgetType option:selected").val()
        let name = $("#WidgetName").val();
        if (WidgetType == 'chart') {
            renderChart(ChartType, 'BodyContainer', globalqueryData[0], name, xaxis, yaxis);
        }
        else if (WidgetType == 'table') {
             tableArray=[]
            let tabledata = $(".tableForm").children()
            if (tabledata.length != 0) {
                tabledata.each((index,data) => {

                    let fieldName = $(data).find(".fieldName").val()
                    let fieldValue = $(data).find(".tablevalue").val()
                    tableArray.push({
                        name: fieldName,
                        value: fieldValue
                    })
                })
            }

            const newTableResult = globalqueryData[0].map(item => {
                const newObj = {};
                tableArray.forEach(map => {
                    newObj[map.name] = item[map.value];
                });
                return newObj;
            });

            renderChart(WidgetType, 'BodyContainer', newTableResult, name, null, null);
        }
        else {




        }
        
    })



    $(".btn_save").on("click", async function (e) {
        //arr = []
        //let scrolbarText = $("#scrolbarText").val()
        //let scrolbarTextVH = $("#scrolbarTextVH").val()

        //if (scrolbarText != "" && scrolbarText != undefined) {
        //    $(".tablePropertiesSet").attr("style", 'height:calc(' + scrolbarTextVH + 'vh - ' + scrolbarText + 'px);overflow:auto')
        //}

        let name = $("#WidgetName").val();
        let WidgetType = $("#WidgetType option:selected").val()
        let ChartType = $("#ChartType option:selected").val()
        let WidgetDescription = $("#WidgetDescription").val();
        let SPDataset = $("#SPDataset option:selected").val()
        let SPDatasetName = $("#SPDataset option:selected").text();
        let Height = $("#height").val();
        let width = $("#Width").val();
        let xaxis = $("#xaxis").val()
        let yaxis = $("#yaxis").val()
        let Chartheight = $("#Chartheight").val()
        let ChartWidth = $("#ChartWidth").val()
        if (name == "") {
            return false;
        }
        let widgetHtml = $('#widgetCreationCard').children()[0];
        let objInfo = {}
        var href = ''

        var container = document.getElementById("BodyContainer"); /* full page */
        const originalCanvas = await html2canvas(container, {
            scale: 0.1,
            useCORS: true,
            allowTaint: true
        });

        const targetWidth = originalCanvas.width / 2; // 50% smaller
        const targetHeight = originalCanvas.height / 2;

        const resizedCanvas = document.createElement("canvas");
        resizedCanvas.width = targetWidth;
        resizedCanvas.height = targetHeight;

        const ctx = resizedCanvas.getContext("2d");
        ctx.drawImage(originalCanvas, 0, 0, targetWidth, targetHeight);

        href = resizedCanvas.toDataURL("image/png",1); // small & optimized

        if (e.target.textContent == 'Update') {
            objInfo.id = e.target.getAttribute('widgetId')
            objInfo.ReferenceId = e.target.getAttribute('referenceId') 
        }
        else {
            const uuid = generateUUID();
            // data.__RequestVerificationToken = gettoken()

            objInfo.ReferenceId = uuid
        }


        const date = new Date()
        const comondate = formatDate(date)
        objInfo.Name = name
        objInfo.IsActive = true
        objInfo.CreatedBy = "3"
        objInfo.CreatedDate = new Date(comondate).toISOString()
        objInfo.LastModifiedBy = "3"
        objInfo.LastModifiedDate = new Date(comondate).toISOString()
        objInfo.Properties = JSON.stringify({
            WidgetType: WidgetType,
            ChartType: ChartType,
            WidgetDescription: WidgetDescription,
            Height: Height,
            width: width,
            Chartheight: Chartheight,
            ChartWidth: ChartWidth,
            xaxis: xaxis,
            yaxis: yaxis,
            datasetId: SPDataset,
            datasetName: SPDatasetName,
            datasetstoredQuery: queryName,
            hrefImage: href,
            libraryType: 'custom',
            tableArray: tableArray
        })

        $.ajax({
            type: "POST",
            url:'/BCMAdministration/WidgetList/SaveWidgetBuilder',
            data: JSON.stringify(objInfo),
            credentials: "include",
            dataType: "json",
            contentType: 'application/json',
            traditional: true,
            success: function (result) {

                if (result.success) {
                   
                    $(".headerList").removeClass('d-none')
                    $(".widgetbuilderblock").addClass('d-none')
                    const toastEl = document.getElementById('notificationToast');
                    const toast = new bootstrap.Toast(toastEl, {
                        delay: 2000,     // ⏱ 2 seconds
                        autohide: true   // ✅ Auto dismiss
                    });

                    // Update message and style
                    $("#notificationToast")
                        .removeClass('bg-danger')
                        .addClass('bg-success');

                    $("#notificationMessage").text(result.message);

                    // Show toast
                    toast.show();
                    WidgetBuilderList()
                }

            }
        })

    })


    $(".btn_preview").on("click", async function (e) {
        let length = $("#widgetCreationCard").children().length
        if (length == 0) {
            $("#previewZoom").modal("hide")
        } else {
            $("#previewZoom").modal("show")
        }

    })

})


async function editwidgetView(data) {

    debugger
   await GetAllDatasets()

    const WidgetName = data.getAttribute('name')
    const WidgetType = data.getAttribute('WidgetType')
    const ChartType = data.getAttribute('charttype')
    const SPDataset = data.getAttribute('datasetid')
    const WidgetDescription = data.getAttribute('widgetdescription')
    const datasetQuery = data.getAttribute('datasetQuery')
    let widgetId = data.getAttribute('id')
    let height = data.getAttribute('height')
    let width = data.getAttribute('width')
    let Chartheight = data.getAttribute('chartheight')
    let ChartWidth = data.getAttribute('chartwidth')
    let ReferenceId = data.getAttribute('referenceId')
    $("#WidgetName").val(WidgetName);
    $('#WidgetType')[0].selectize.setValue(WidgetType);
    $('#ChartType')[0].selectize.setValue(ChartType);
    $('#SPDataset')[0].selectize.setValue(SPDataset);
    $("#WidgetDescription").val(WidgetDescription);
    $("#height").val(height);
    $("#Width").val(width);
    $("#Chartheight").val(Chartheight);
    $("#ChartWidth").val(ChartWidth);
    $(".bcm-box").css('height', height)
    $(".bcm-box").css('width', width)
    $("#BodyContainer").css('height', Chartheight)
    $("#BodyContainer").css('width', ChartWidth)
    queryName = datasetQuery

    renderChart(ChartType, 'BodyContainer', sampleData, WidgetName, 'category','value');
    $(".btn_save").attr("widgetId", widgetId)
    $(".btn_save").attr("ReferenceId", ReferenceId)
    $(".btn_save").text("Update")
    $(".headerList").addClass('d-none')
    $(".widgetbuilderblock").removeClass('d-none')
}




function deletewidgetListView(data) {
    
    let widgetId = $(data).attr('id')

    $.ajax({
        type: "POST",
        url: '/BCMAdministration/WidgetList/DeleteWidgetBuilder',
        data: JSON.stringify({ id: widgetId }),
        contentType: 'application/json',
        success: function (result) {
            const toastEl = document.getElementById('notificationToast');
            const toast = new bootstrap.Toast(toastEl, {
                delay: 2000,     // ⏱ 2 seconds
                autohide: true   // ✅ Auto dismiss
            });
            if (result.success) {

                $("#notificationToast").removeClass('bg-danger').addClass('bg-success')
                $("#notificationMessage").text(result.message)
            }
            else {
                $("#notificationToast").removeClass('bg-success').addClass('bg-danger')
                $("#notificationMessage").text(result.message)
            }
            toast.show();

            $(".widgetbuilderblock").addClass('d-none')
           WidgetBuilderList()
        }
    })

}

function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}


function formatDate(date) {
    const pad = (n, digits = 2) => n.toString().padStart(digits, '0');

    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1); // months are 0-indexed
    const day = pad(date.getDate());
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());
    const milliseconds = pad(date.getMilliseconds(), 3);



    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
}


function WidgetAllClear() {

    $("#WidgetName").val("");
    $("#WidgetType ").val("")
    $("#ChartType").val("")
    $("#WidgetDescription").val("");
    $("#SPDataset").val("")
    $("#widgetCreationCard").empty()
}


function WidgetBuilderList() {



    $.ajax({
        url: '/BCMAdministration/WidgetList/WidgetBuilderList',
        type: 'GET',
        credentials: "include",
        contentType: "application/json",
        dataType: "json",
        success: async function (response) {
            
            //$("#prebuildList").empty()
            $("#customList").empty()
            if (response.success) {
                if (response.data.length != 0) {
                    response.data.forEach((data) => {

                        let widgetHtml = JSON.parse(data.properties)
                        let html = "";
                        html += '<div class="col-3 p-1 widgetList">'
                        html += '<div class="card border mb-0 h-100">'
                        html += '<div class="dropdown d-flex justify-content-md-end" >'
                        html += '<i class="cv-horizontal-dots p-1 show" role="button" data-bs-toggle="dropdown" title="More" aria-expanded="true"></i>'
                        html += '<ul class="dropdown-menu" style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(184px, 26px);" data-popper-placement="bottom-start">'
                        html += '<li class="editActionList" id="' + data.id + '" name="' + data.name + '" referenceId="' + data.referenceId + '"  ChartType="' + widgetHtml.ChartType + '" WidgetType="' + widgetHtml.WidgetType + '"   datasetId="' + widgetHtml.datasetId + '" datasetQuery="' + widgetHtml.datasetstoredQuery + '"  WidgetDescription="' + widgetHtml.WidgetDescription + '"  height ="' + widgetHtml.Height + '"  width ="' + widgetHtml.width + '" chartheight="' + widgetHtml.Chartheight + '" chartwidth="' + widgetHtml.ChartWidth +'" onclick = "editwidgetView(this,event)" > <a class="dropdown-item" href="#"><i class="cv-edit text-info me-2" title="Edit"></i>Edit</a></li >'
                        html += '<li class="deleteActionList " id="' + data.id + '"    name="' + data.name + '" onclick="deletewidgetListView(this)" data-bs-toggle="modal" data-bs-target="#DeleteModal"><a class="dropdown-item" href="#"><i class="cv-delete text-danger me-2" title="Delete"></i>Delete</a></li>'
                        html += '</ul >'
                        html += '</div >'
                        html += '<div class="card-body py-3">'
                        html += '<img src = "' + widgetHtml.hrefImage + '" class="w-100" style="height:170px; object-fit:scale-down; object-position: top; " />'
                        html += '</div>'
                        html += '<div class="card-footer widgetName list-title text-center" title="' + data.name + '">' + data.name + '</div>'
                        html += '</div>'
                        html += '</div>'
                        if (widgetHtml.libraryType == 'custom') {
                            $("#customList").append(html)
                        }
                        else {
                            $("#prebuildList").append(html)
                        }
            
                    })
                }
            }


        }

    })

}




function GetAllDatasets() {

    $.ajax({
        url: '/BCMAdministration/WidgetList/GetAllDatasets',
        type: 'GET',
        credentials: "include",
        contentType: "application/json",
        dataType: "json",
        success: async function (response) {

            if (response.success) {
                if (response.data.length != 0) {
                    datasetData = ""
                    datasetData = response.data

                    selectizeInstance.clearOptions(); // Clear any existing options

                    response.data.forEach(item => {
                        selectizeInstance.addOption(item); // Add new options
                    });

                    // Optional: Refresh dropdown
                    selectizeInstance.refreshOptions(false);


                }

            }

        }
    })


}

function addColumn() {
    fieldCount++;
    const container = document.getElementById("tableForm");

    const row = document.createElement("div");
    row.className = "row field-row align-items-end";
    row.id = `fieldRow${fieldCount}`;

    row.innerHTML = `
      <div class="col-5">
        <div class="form-group field-touched">
          <label>Field ${fieldCount}</label>
          <div class="input-group">
            <span class="input-group-text"><i class="cv-name"></i></span>
            <input type="text" class="form-control fieldName" placeholder="Field Name" id="field${fieldCount}">
          </div>
        </div>
      </div>
      <div class="col-5">
        <div class="form-group field-touched">
          <label>Value</label>
          <div class="input-group">
            <span class="input-group-text"><i class="cv-name"></i></span>
            <select class="form-control datasetValue${fieldCount} tablevalue" id="value${fieldCount}">
           
            </select>
          </div>
        </div>
      </div>
      <div class="col-2 text-end">
        <button class="btn btn-sm btn-danger" onclick="removeColumn(${fieldCount})"><i class="cv-close" title="Show"></i></button>
      </div>
    `;

    container.appendChild(row);


    if (globalqueryData[0].length != 0) {
        for (let tablekey in globalqueryData[0][0]) {

            let option = '<option value="' + tablekey + '">' + tablekey + '</option>'
            $(".datasetValue" + fieldCount).append(option)
        }
    }

}

function removeColumn(id) {
    const row = document.getElementById(`fieldRow${id}`);
    if (row) row.remove();
}

function DatasetRunQuery(query) {


    $.ajax({
        url: '/BCMAdministration/WidgetList/RunQuery',
        type: 'GET',

        data: { runQuery: query },
        success: async function (response) {

            if (response.success) {
               
                let tableQuery = JSON.parse(response.data.tableValue)
                globalqueryData = tableQuery
                    debugger
                if (tableQuery[0].length != 0) {
                    $(".datasetValue").empty()
                    for (let tablekey in tableQuery[0][0]) {

                        let option = '<option value="' + tablekey + '">' + tablekey +'</option>'
                        $(".datasetValue").append(option)
                    }

                

                }

            }

        }
    })

}
