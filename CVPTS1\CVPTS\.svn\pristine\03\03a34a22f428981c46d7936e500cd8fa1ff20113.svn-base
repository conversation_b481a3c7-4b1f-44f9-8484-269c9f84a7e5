﻿@model IEnumerable<BCM.BusinessClasses.BCMGroupResources>
@{
    ViewBag.Title = "BCM Group Members";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .dataTables_scrollBody {
        max-height: calc(100vh - 206px);
        height: calc(100vh - 248px);
    }
</style>
<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">@ViewBag.GroupName Members</h6>
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
    </div>
    <div class="d-flex align-items-center gap-2 mt-2">

        <div class="input-group w-25">
            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
            @* <input type="hidden" value="@ViewData["orgID"]" id="orgID" /> *@
            <input type="hidden" value="@ViewData["grpID"]" id="grpID" />
            <input type="text" placeholder="Enter Unit" class="form-control" value="@ViewBag.UnitName" readonly />
        </div>
        <div class="input-group w-25">
            <span class="input-group-text py-1"><i class="cv-department"></i></span>
            <input type="text" placeholder="Enter Department" class="form-control" value="@ViewBag.DepartmentName" readonly />
        </div>
        <div class="input-group w-25">
            <span class="input-group-text py-1"><i class="cv-site-incharge"></i></span>
            <input type="text" placeholder="Enter Site" class="form-control" value="@ViewBag.SideName" readonly />
        </div>

        <a class="btn icon-btn btn-primary btn-sm" id="addGrpMem" data-bs-toggle="modal" data-bs-target="#CreateModal">Add&nbsp;Member</a>
        <a class="btn icon-btn btn-primary btn-sm btnNotifyPopUP" data-bs-toggle="modal">Notify&nbsp;Team</a>@* data-bs-target="#NotifyModal" *@
        @* <a class="btn icon-btn btn-primary btn-sm" href="/BCMTeams/BCMGroups/BCMGroups">View All Groups</a> *@
        <a class="btn icon-btn btn-primary btn-sm" href="@Url.Action("BCMGroups","BCMGroups",new { area = "BCMTeams" })">View&nbsp;All&nbsp;Groups</a>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>@Html.DisplayName("User Details")</th>
                <th>@Html.DisplayName("Address")</th>
                <th>@Html.DisplayName("Login Details")</th>
                <th>@Html.DisplayName("Member Of Groups")</th>
                <th>@Html.DisplayName("Send Verification")</th>
                <th>@Html.DisplayName("Delete Members")</th>
            </tr>
        </thead>
        <tbody>
            @if (Model != null)
            {
                int iIndex = 0;                
                foreach(var item in Model)
                {
                    iIndex++;
                    item.Address = string.IsNullOrEmpty(item.Address) ? "NA" : item.Address;
                    <tr>
                        <td>@iIndex</td>
                        <td>
                            <div class="d-flex">
                                @* <div class="User-icon">
                                    <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                                </div> *@
                                <div>
                                    <ul class="ps-0 mb-0">
                                        <li hidden>@item.ResourceId</li>
                                        <li class="list-group-item">@item.ResourceName</li>@* id="<EMAIL>" *@
                                        <li class="list-group-item" style="display:none"><a class="text-primary" href="#"><i class="cv-mail"></i>:@item.CompanyEmail</a></li>
                                        <li class="list-group-item" style="display:none"><i class="cv-phone"></i>:@item.MobilePhone</li>

                                    </ul>
                                </div>
                            </div>
                        </td>
                        <td>@item.Address</td>
                        <td>
                            <ul class="ps-0 mb-0">
                                <li class="list-group-item">@item.LastLoginTime</li>
                                <li class="list-group-item" style="display:none">Last Email: @item.EmailVerifiedTooltip</li>
                                <li class="list-group-item" style="display:none">Last SMS: @item.MobileVerifiedTooltip</li>
                            </ul>
                        </td>
                        <td>@item.GroupName</td>
                        <td>
                            <span class="cv-mail me-1"></span>
                            <span class="cv-Mobile me-1"></span>
                        </td>
                        <td>
                            <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal" data-name="@item.ResourceName" data-id="@item.ResourceId"><i class="cv-delete text-danger" title="Delete"></i></span>
                        </td>
                    </tr>                    
                }
            }            
        </tbody>
    </table>


    <!-- Configuration Modal -->
    <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">BCM Add Group Member Configuration</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    
                </div>                
            </div>
        </div>
    </div>
    <!--End Configuration Modal -->
    <!--Notify Configuration Modal -->
    <div class="modal fade" id="NotifyModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">Notify Teams Configuration</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="NotifyBody">
                </div>
            </div>
        </div>
    </div>
    <!--End Notify Configuration Modal -->
    <!-- Delete Modal -->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center" id="DeleteBody">
                </div>
            </div>
        </div>
    </div>
    <!-- End Delete Modal -->
</div>

@section Scripts {
    <script>
       
        $('#addGrpMem').click(function () {
            $.get('@Url.Action("AddBCMGroupMembers", "AddBCMGroupMembers", new { area = "BCMTeams" })', function (data) {
                $('.modal-body').html(data);
                $('#CreateModal').modal('show');
            });
        });

        $(document).on('click', '.btnDelete', function () {            
            var GrpMemId = $(this).data('id');
            var ResourceName = $(this).data('name');
            $.ajax({
                url: '@Url.Action("DeleteBCMGrpMember", "BCMGroupmembers", new { area = "BCMTeams" })',
                type: 'GET',
                contentType: 'application/json',
                data: { iGrpMemId: GrpMemId, strResourceName: ResourceName },
                success: function (response) {                    
                    $('#DeleteBody').html(response);
                    $('#DeleteModal').modal('show');
                },
                error: function (error) {
                    console.log('Error : ', error);
                }
            });
        });

        $(document).on('click', '.btnNotifyPopUP', function () {
            var iID = $(this).data('id');
            $.ajax({
                url: '@Url.Action("BCMGroupsNotification", "BCMGroupsNotification", new { area = "BCMTeams" })',
                type: 'GET',
                contentType: 'application/json',
                data: { iGrpId: iID },
                success: function (data) {
                    $('#NotifyBody').html(data);
                    $('#NotifyModal').modal('show');
                },
                error: function (error) {
                    console.log('Error : ', error);
                }
            });
        });

    </script>
}