﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Data;
using System.Drawing.Drawing2D;
using System.Security.Cryptography;

namespace BCM.UI.Areas.BCMPerformanceEvaluation.Controllers;
[Area("BCMPerformanceEvaluation")]
public class ManagePerformanceEvaluationSCController : BaseController
{

    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    int iEntityTypeID = 0;


    public ManagePerformanceEvaluationSCController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    public IActionResult Index()
    {
        return View();
    }

    #region Get All PerformanceEvaluation
    [HttpGet]
    public IActionResult ManagePerformanceEvaluationSC()
    {
        //PopulateDropDown();
        List<PerformanceEvaluation> lstPerformaneEvaluationForm = new List<PerformanceEvaluation>();
        try
        {
            int OrgID = _UserDetails.OrgID;
            var performanceEvaluations = _ProcessSrv.GetPerformanceEvaluationSC_ByOrgID(OrgID.ToString());
            if (performanceEvaluations != null)
            {
                foreach (DataRow row in performanceEvaluations.Rows)
                {
                    PerformanceEvaluation evaluation = new PerformanceEvaluation
                    {
                        ID = Convert.ToInt32(row["ID"]),
                        OrgID = Convert.ToInt32(row["OrgID"]),
                        EvaluationName = row["EvaluationName"].ToString(),
                        StatusID = Convert.ToInt32(row["StatusID"]),
                        MetricID = Convert.ToInt32(row["MetricID"]),
                        MeasurementID = Convert.ToInt32(row["MeasurementID"]),
                        ModeID = Convert.ToInt32(row["ModeID"]),
                        AnalysisID = Convert.ToInt32(row["AnalysisID"]),
                        FrequencyID = Convert.ToInt32(row["FrequencyID"]),
                        FrequencyName = _Utilities.PopulateFrequency_ByID(Convert.ToInt32(row["FrequencyID"])),
                        EvaluationDate = Convert.ToDateTime(row["EvaluationDate"]),
                        EvFromDate = Convert.ToDateTime(row["EvFromDate"]),
                        EvToDate = Convert.ToDateTime(row["EvToDate"]),
                        CloseDate = Convert.ToDateTime(row["CloseDate"]),
                        MeasuredBy = Convert.ToInt32(row["MeasuredBy"]),
                        CurIndicator = row["CurIndicator"].ToString(),
                        PrevIndicator = row["PrevIndicator"].ToString(),
                        Trend = row["Trend"].ToString(),
                        TrendHtmlText = row["TrendHtmlText"].ToString(),
                        Reason = row["Reason"].ToString(),
                        ReasonHtmlText = row["ReasonHtmlText"].ToString(),
                        RequiredAction = row["RequiredAction"].ToString(),
                        ActionHtmlText = row["ActionHtmlText"].ToString(),
                        IsActive = Convert.ToInt32(row["IsActive"]),
                        CreatedBy = Convert.ToInt32(row["CreatedBy"]),
                        CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                        UpdatedBy = Convert.ToInt32(row["UpdatedBy"]),
                        UpdatedDate = Convert.ToDateTime(row["UpdatedDate"]),
                        IsEffective = Convert.ToInt32(row["IsEffective"]),
                        ResourcesReq = row["ResourcesReq"].ToString(),
                        RespParties = row["RespParties"].ToString(),
                        EffectiveCriteria = row["EffectiveCriteria"].ToString(),
                        RemarkEffLevel = row["RemarkEffLevel"].ToString(),
                        EffectiveRating = Convert.ToInt32(row["EffectiveRating"]),
                        Effectiveness = row["Effectiveness"].ToString(),
                        OverallKPI = Convert.ToInt32(row["OverallKPI"]),
                        Target = row["Target"].ToString(),
                        TargetedDate = Convert.ToDateTime(row["TargetedDate"]),
                        CorrectiveAction = row["CorrectiveAction"].ToString(),
                        CurrentRisk = row["CurrentRisk"].ToString(),
                        Remarks = row["Remarks"].ToString(),
                        Targeteddate = Convert.ToDateTime(row["Targeteddate"]),
                        ITDRName = row["ITDRName"].ToString(),
                        Objective = row["Objective"].ToString(),
                        Effectiveness1 = row["Effectiveness1"].ToString(),
                        MeasureKPI = row["MeasureKPI"].ToString(),
                        ResponsibleParties = row["ResponsibleParties"].ToString(),
                        Rating = row["Rating"].ToString()

                    };
                    lstPerformaneEvaluationForm.Add(evaluation);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View(lstPerformaneEvaluationForm);
    }

    #endregion

    #region Add PerformanceEvaluation

    [HttpGet]
    public IActionResult AddPerformanceEvaluation()
    {
        PopulateDropDown();
        return PartialView("_AddPerformanceEvaluation", new PerformanceEvaluation());
    }

    [HttpPost]
    public IActionResult AddPerformanceEvaluation(PerformanceEvaluation PerformanceEvaluation)
    {
        bool bSuccess = false;
        try
        {
            PerformanceEvaluation.OrgID = _UserDetails.OrgID;
            PerformanceEvaluation.IsActive = 1;
            PerformanceEvaluation.CreatedDate = DateTime.Now;
            PerformanceEvaluation.CreatedBy = _UserDetails.UserID;
            int ID = _ProcessSrv.PerformanceEvaluation_SaveUpdate(PerformanceEvaluation);
            bSuccess = ID > 0 ? true : false;
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? PerformanceEvaluation.ITDRName + " Added Successfully" : "Failed To Add." });            
        }
        return RedirectToAction("ManagePerformanceEvaluationSC");
    }

    #endregion

    #region Edit PerformanceEvaluation

    [HttpGet]
    public IActionResult EditPerformanceEvaluation(int iId)
    {
        PopulateDropDown();
        //HttpContext.Session.SetInt32("iId", Convert.ToInt32(iId));
        PerformanceEvaluation objPerformanceEvaluation = _ProcessSrv.GetPerformanceEvaluationSC_ByID(iId.ToString());
        objPerformanceEvaluation.ID = Convert.ToInt32(iId);
        objPerformanceEvaluation.IsActive = 1;
        objPerformanceEvaluation.OrgID = _UserDetails.OrgID;
        return PartialView("_EditPerformanceEvaluation", objPerformanceEvaluation);
    }

    [HttpPost]
    public IActionResult EditPerformanceEvaluation(PerformanceEvaluation PerformanceEvaluation)
    {
        bool bSuccess = false;
        try
        {
            PerformanceEvaluation.IsActive = 1;
            PerformanceEvaluation.OrgID = _UserDetails.OrgID;
            int ID = _ProcessSrv.PerformanceEvaluation_SaveUpdate(PerformanceEvaluation);
            bSuccess = ID > 0 ? true : false;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? PerformanceEvaluation.ITDRName + " Updated Successfully" : "Failed To Update." });
        }
        return RedirectToAction("ManagePerformanceEvaluationSC");

    }

    #endregion

    #region Delete PerformanceEvaluation
    [HttpGet]
    public IActionResult DeletePerformanceEvaluation(int iId)
    {
        //HttpContext.Session.SetInt32("iId", Convert.ToInt32(iId));
        PerformanceEvaluation objPerformanceEvaluation = _ProcessSrv.GetPerformanceEvaluationSC_ByID(iId.ToString());
        objPerformanceEvaluation.ID = Convert.ToInt32(iId);
        return PartialView("_DeletePerformanceEvaluation", objPerformanceEvaluation);
    }

    [HttpPost]
    public IActionResult DeletePerformanceEvaluation(PerformanceEvaluation PerformanceEvaluationMasters)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.DeletePerfomanceEvaluation(Convert.ToInt32(PerformanceEvaluationMasters.ID), Convert.ToInt32(_UserDetails.UserID));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? PerformanceEvaluationMasters.ITDRName + " Deleted Successfully" : "Failed To Delete." });
        }
        return RedirectToAction("ManagePerformanceEvaluationSC");

    }

    #endregion

    #region Utilities PerformanceEvaluation

    public void PopulateDropDown(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());
            ViewBag.Unit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);
            ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);
            ViewBag.ResourceList = _Utilities.GetAllResourceList();
            ViewBag.lstResource =  new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
            ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();
            //ViewBag.ReviewType = _ProcessSrv.GetReviewType(Convert.ToInt32(_UserDetails.OrgID));

            ViewBag.metric= new SelectList(_ProcessSrv.Metric_GetAll(), "ID", "MetricName");

            List<PerformanceEvaluation> ITDR_Obj = new List<PerformanceEvaluation>();
            foreach (DataRow row in _ProcessSrv.itdr_Objective_Getall().Rows)
            {
                PerformanceEvaluation itdr = new PerformanceEvaluation();

                itdr.ID = Convert.ToInt32(row["ID"]);
                itdr.ITDRName = row["ITDRName"].ToString();
                ITDR_Obj.Add(itdr);
            }
            ViewBag.ITDRObj= new SelectList(ITDR_Obj, "ID", "ITDRName");


            List<PerformanceEvaluation> ITDR_measure_KPI = new List<PerformanceEvaluation>();
            foreach (DataRow row in _ProcessSrv.itdr_measureKPI().Rows)
            {
                PerformanceEvaluation itdrmeasure_KPI = new PerformanceEvaluation();

                itdrmeasure_KPI.ID = Convert.ToInt32(row["ID"]);
                itdrmeasure_KPI.MeasureKPI = row["MeasureKPI"].ToString();
                ITDR_measure_KPI.Add(itdrmeasure_KPI);
            }
            ViewBag.ITDR_measureKPI= new SelectList(ITDR_measure_KPI, "ID", "MeasureKPI");


            List<PerformanceEvaluation> ITDR_Eff_Rating = new List<PerformanceEvaluation>();
            foreach (DataRow row in _ProcessSrv.itdr_EffectiveRating_Getall().Rows)
            {
                PerformanceEvaluation ITDR_EffRating = new PerformanceEvaluation();

                ITDR_EffRating.ID = Convert.ToInt32(row["ID"]);
                ITDR_EffRating.Rating = row["Rating"].ToString();
                ITDR_Eff_Rating.Add(ITDR_EffRating);
            }
            ViewBag.ITDR_EffRating= new SelectList(ITDR_Eff_Rating, "ID", "Rating");


            ViewBag.Populat_Frequency = new SelectList(_Utilities.PopulateFrequency(), "ID", "FrequencyName");


            List<PerformanceEvaluation> ITDR_ObjMeasuredBy = new List<PerformanceEvaluation>();
            foreach (DataRow row in _ProcessSrv.itdr_ObjMeasuredBy().Rows)
            {
                PerformanceEvaluation ITDR_EffRating = new PerformanceEvaluation();

                ITDR_EffRating.ID = Convert.ToInt32(row["ID"]);
                ITDR_EffRating.Objective = row["Objective"].ToString();
                ITDR_ObjMeasuredBy.Add(ITDR_EffRating);
            }
            ViewBag.ITDR_Objective= new SelectList(ITDR_ObjMeasuredBy, "ID", "Objective");


        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }



    #endregion

}

