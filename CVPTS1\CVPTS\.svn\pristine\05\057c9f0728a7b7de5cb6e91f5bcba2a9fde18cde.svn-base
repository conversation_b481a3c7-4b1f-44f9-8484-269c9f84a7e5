﻿using BCM.BusinessClasses;

namespace BCM.UI.Areas.BCMReports.ReportModels.BIAReport;

public class BIAReportModel : UserActivityStatus
{
    public int ID { get; set; }
    public int OrgID { get; set; }
    public string? OrgName { get; set; }
    public string? UnitName { get; set; }
    public int UnitHeadID { get; set; }
    public int UnitBCPCorID { get; set; }
    public int AltBCPCorID { get; set; }
    public string? DepartmentName { get; set; }
    public int DepartmentHeadID { get; set; }
    public int AltDepartmentHeadID { get; set; }
    public int SubfunctionID { get; set; }
    public string? SubFunctionName { get; set; }
    public int ProcessID { get; set; }
    public int ProcessID_Encrypted { get; set; }
    public string? ProcessName { get; set; }
    public int ProcessOwnerID { get; set; }
    public string? ProcessOwner { get; set; }
    public string? ProcessOwnerMobile { get; set; }
    public string? OwnerEmail { get; set; }
    public int AltProcessOwnerID { get; set; }
    public string? AltProcessOwner { get; set; }
    public string? AltProcessOwnerMobile { get; set; }
    public string? AltProcessOwnerEmail { get; set; }
    public DateTime? LastReviewDate { get; set; }
    public DateTime? ReviewDate { get; set; }
    public string? Reminder { get; set; }
    public string? RecurrenceRule { get; set; }
    public int RecurrenceParentID { get; set; }
    public int PrimaryResourceID { get; set; }
    public string? PrimaryResource { get; set; }
    //public int SubFunctionID { get; set; }
    public int DepartmentID { get; set; }
    public int UnitID { get; set; }
    public int Status { get; set; }
    public string? Priority { get; set; }
    public string? ProcessType { get; set; }
    public string? LossPerDay { get; set; }
    public string? AttachedFile { get; set; }
    public string? ApprovalCode { get; set; }
    public string? MyAssigned { get; set; }
    public string? Comments { get; set; }
    public string? MinimumResource { get; set; }
    public string? ProcessDescription { get; set; }
    public int IsCritical { get; set; }
    public int IsActive { get; set; }
    public DateTime? CreateDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public string? OwnerRTO { get; set; }
    public string? MTR { get; set; }
    public string? OwnerMTR { get; set; }
    public int ApproverID { get; set; }
    public string? OverAllStatus { get; set; }
    public int ChangedBy { get; set; }
    public string? ProcessCode { get; set; }
    public string? Version { get; set; }
    public int IsEffective { get; set; }
    public string? RTO { get; set; }
    public string? RPO { get; set; }
    public string? RPOUnit { get; set; }
    public string? Comment { get; set; }
    public int ProcessIsActive { get; set; }
    public string? ProcessVersion { get; set; }
    public string? ApproverName { get; set; }
    public string? ApproverEmail { get; set; }
    public string? ApproverMobile { get; set; }
    public int CreatedBy { get; set; }
    public int OrgGroupID { get; set; }
    public int DetailID { get; set; }
    public int SunFunOwnerId { get; set; }
    public int AltSubFunOwnerId { get; set; }
    public int OrgHeadId { get; set; }
    public int AltUnitHeadId { get; set; }
    public DateTime? ProcessReviewDate { get; set; }
    public int IncidentPlanID { get; set; }
    public string? IncidentPlanName { get; set; }
    public DateTime? IncidentPlanReviewDate { get; set; }
    public string? IncidentPlanStatus { get; set; }
    public string? DrillStatus { get; set; }
    public DateTime RiskReviewDate { get; set; }
    public int RiskID { get; set; }
    public string? RiskCode { get; set; }
    public string? ProcessPCIScore { get; set; }
    public int ProfileID { get; set; }
    public int EntityTypeID { get; set; }
    public int RecordID { get; set; }
    public string? ESCMatType { get; set; }
    public string? BCMEntityType { get; set; }
    public string? PageName { get; set; }
    public string? TimeOperationT { get; set; }
    public string? TimeOperationF { get; set; }
    public string? PeakTranVolume { get; set; }
    public string? PeakPeriod { get; set; }
    public string? SPOF { get; set; }
    public string? PossibleSol { get; set; }
    public string? ProfileName { get; set; }
    public string? Field1 { get; set; }
    public string? Field2 { get; set; }
    public string? Field3 { get; set; }
    public string? Field4 { get; set; }
    public int BPProfileID { get; set; }
    public int WorkType { get; set; }
    public int HandOffs { get; set; }
    public int TeamSize { get; set; }
    public string? RTOText { get; set; }
    public string? CategoryRange { get; set; }
    public int MinRange { get; set; }
    public int MaxRange { get; set; }
    public int IsAddedToBCM { get; set; }
    public string? ApprovalStatus { get; set; }
    public int SectionID { get; set; }
    public int SectionName { get; set; }
    public int BIAID { get; set; }
    public int SubFunOwnerId { get; set; }
    public string? BIAPageURL { get; set; }
    public int IsBCMEntity { get; set; }
    public string? ProcessOwnerName { get; set; }
    public string? LocationName { get; set; }
    public string? Latitude { get; set; }
    public string? Longitude { get; set; }
    public int IsButtonVisible { get; set; }
}