﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc;
using BCM.Shared;
using System.Text;
using BCM.Security.Helper;
using BCM.UI.Services;


namespace BCM.UI.Areas.BCMResourceManagement.Controllers;

[Area("BCMResourceManagement")]
public class EmailVerificationController : Controller
{
    private readonly ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    readonly CVLogger _CvLogger;
    private readonly ISimpleEncryptionService _encryptionService;

    // Link expiration time in seconds (5 minutes = 300 seconds)
    private const int LINK_EXPIRATION_SECONDS = 300;

    public EmailVerificationController(ProcessSrv processSrv, Utilities utilities , CVLogger cvLogger, ISimpleEncryptionService encryptionService)
    {
        _ProcessSrv = processSrv;
        _Utilities = utilities;
        _CvLogger = cvLogger;
        _encryptionService = encryptionService;
    }

    //public IActionResult Index()
    //{
    //    return View();
    //}

    /// <summary>
    /// Test method to verify encryption/decryption functionality
    /// Remove this method in production
    /// </summary>
    [HttpGet]
    public IActionResult TestEncryption(string testValue = "1234567890")
    {
        try
        {
            _CvLogger.LogInfo($"TestEncryption: Testing with value: {testValue}");

            // Test encryption
            string encrypted = _encryptionService.Encrypt(testValue);
            _CvLogger.LogInfo($"TestEncryption: Encrypted value: {encrypted}");

            // Convert to URL-safe format
            string urlSafe = ConvertToUrlSafeBase64(encrypted);
            _CvLogger.LogInfo($"TestEncryption: URL-safe format: {urlSafe}");

            // Convert back from URL-safe format
            string backToStandard = ConvertFromUrlSafeBase64(urlSafe);
            _CvLogger.LogInfo($"TestEncryption: Back to standard: {backToStandard}");

            // Test decryption
            string decrypted = _encryptionService.Decrypt(backToStandard);
            _CvLogger.LogInfo($"TestEncryption: Decrypted value: {decrypted}");

            bool success = testValue == decrypted;

            return Json(new
            {
                success = success,
                original = testValue,
                encrypted = encrypted,
                urlSafe = urlSafe,
                backToStandard = backToStandard,
                decrypted = decrypted,
                message = success ? "Encryption/Decryption test passed!" : "Encryption/Decryption test failed!"
            });
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return Json(new
            {
                success = false,
                error = ex.Message,
                message = "Encryption/Decryption test failed with exception!"
            });
        }
    }

    [HttpGet]
    public IActionResult VerifyEmail(string resourceId, string orgId, string timestamp)
    {
        try
        {
            var result = ProcessEmailVerification(resourceId, orgId, timestamp);

            // Set ViewBag data that the layout might need
            ViewBag.Title = "Email Verification";
            ViewBag.IsVerificationPage = true;

            // Generate dynamic login URL
            ViewBag.LoginUrl = Url.Action("Login", "Login", new { area = "" });
            _CvLogger.LogInfo($"VerifyEmail: Generated login URL: {ViewBag.LoginUrl}");

            return View(result);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            // If there's an error, return a simple view without the main layout
            var errorResult = new EmailVerificationResult
            {
                Success = false,
                Message = "An error occurred during verification.",
                MessageType = "error"
            };

            ViewBag.Title = "Email Verification";
            ViewBag.IsVerificationPage = true;

            // Generate dynamic login URL even for error cases
            ViewBag.LoginUrl = Url.Action("Login", "Login", new { area = "" });

            return View(errorResult);
        }
    }

    //[HttpGet]
    //public IActionResult EmailVerification()
    //{
    //    return View();
    //}

    private EmailVerificationResult ProcessEmailVerification(string encryptedResourceId, string encryptedOrgId, string encryptedTimestamp)
    {
        var result = new EmailVerificationResult();

        try
        {
            // Validate input parameters first
            if (string.IsNullOrEmpty(encryptedResourceId) || string.IsNullOrEmpty(encryptedOrgId) || string.IsNullOrEmpty(encryptedTimestamp))
            {
                _CvLogger.LogErrorApp(new Exception($"ProcessEmailVerification: One or more encrypted parameters are null/empty - ResourceId: '{encryptedResourceId}', OrgId: '{encryptedOrgId}', Timestamp: '{encryptedTimestamp}'"));
                result.Success = false;
                result.Message = "Invalid verification link format.";
                result.MessageType = "error";
                return result;
            }

            // Log the encrypted parameters for debugging
            _CvLogger.LogInfo($"ProcessEmailVerification: Attempting to decrypt - ResourceId: {encryptedResourceId}, OrgId: {encryptedOrgId}, Timestamp: {encryptedTimestamp}");

            // Decrypt the IDs and timestamp using the new encryption service
            string resourceId = DecryptUrlParameter(encryptedResourceId);
            string orgId = DecryptUrlParameter(encryptedOrgId);
            string timestamp = DecryptUrlParameter(encryptedTimestamp);

            // Check if any decryption failed (returned "0" indicates failure)
            if (resourceId == "0" && encryptedResourceId != ConvertToUrlSafeBase64(_encryptionService.Encrypt("0")))
            {
                _CvLogger.LogErrorApp(new Exception($"ProcessEmailVerification: ResourceId decryption failed for: {encryptedResourceId}"));
                result.Success = false;
                result.Message = "Invalid verification link - unable to decrypt resource information.";
                result.MessageType = "error";
                return result;
            }

            if (orgId == "0" && encryptedOrgId != ConvertToUrlSafeBase64(_encryptionService.Encrypt("0")))
            {
                _CvLogger.LogErrorApp(new Exception($"ProcessEmailVerification: OrgId decryption failed for: {encryptedOrgId}"));
                result.Success = false;
                result.Message = "Invalid verification link - unable to decrypt organization information.";
                result.MessageType = "error";
                return result;
            }

            if (timestamp == "0" && encryptedTimestamp != ConvertToUrlSafeBase64(_encryptionService.Encrypt("0")))
            {
                _CvLogger.LogErrorApp(new Exception($"ProcessEmailVerification: Timestamp decryption failed for: {encryptedTimestamp}"));
                result.Success = false;
                result.Message = "Invalid verification link - unable to decrypt timestamp information.";
                result.MessageType = "error";
                return result;
            }

            // Log the decrypted values for debugging
            _CvLogger.LogInfo($"ProcessEmailVerification: Decrypted values - ResourceId: {resourceId}, OrgId: {orgId}, Timestamp: {timestamp}");

            // Validate resourceId and orgId (must be positive integers)
            if (string.IsNullOrEmpty(resourceId) || resourceId == "0" ||
                !int.TryParse(resourceId, out int resourceIdInt) || resourceIdInt <= 0)
            {
                _CvLogger.LogErrorApp(new Exception($"ProcessEmailVerification: Invalid resourceId - Value: '{resourceId}', Original encrypted: '{encryptedResourceId}'"));
                result.Success = false;
                result.Message = "Invalid verification link.";
                return result;
            }

            if (string.IsNullOrEmpty(orgId) || orgId == "0" ||
                !int.TryParse(orgId, out int orgIdInt) || orgIdInt <= 0)
            {
                _CvLogger.LogErrorApp(new Exception($"ProcessEmailVerification: Invalid orgId - Value: '{orgId}', Original encrypted: '{encryptedOrgId}'"));
                result.Success = false;
                result.Message = "Invalid verification link.";
                return result;
            }

            // Validate timestamp (must be a valid number, but can be any value)
            if (string.IsNullOrEmpty(timestamp) || !long.TryParse(timestamp, out long timestampLong))
            {
                _CvLogger.LogErrorApp(new Exception($"ProcessEmailVerification: Invalid timestamp - Value: '{timestamp}', Original encrypted: '{encryptedTimestamp}'"));
                result.Success = false;
                result.Message = "Invalid verification link.";
                return result;
            }

            // Validate link expiration
            if (!IsLinkValid(timestamp, LINK_EXPIRATION_SECONDS))
            {
                result.Success = false;
                result.Message = "This verification link has expired. Please request a new reminder email.";
                result.MessageType = "warning";
                return result;
            }

            // Create response object
            var response = new ResourcesReminder
            {
                ResourceID = Convert.ToInt32(resourceId),
                MobilePhone = "0000", // Set dummy mobile number
                CommunicationMode = (int)BCPEnum.NotificationType.EMail,
                InOut_bound = (int)BCPEnum.CommunicationType.Outbound,
                SenderID = 0
            };

            // Get vault settings for the organization
            var vaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt32(orgId));

            if (vaultSettings != null)
            {
                // Add response with verification interval and days prior
                bool success = _ProcessSrv.AddResponseByResourceID(response,
                    vaultSettings.VerificationInterval,
                    vaultSettings.VerificationDaysPrior);

                if (success)
                {
                    result.Success = true;
                    result.Message = "Thank You For Verification!";
                    result.MessageType = "success";
                }
                else
                {
                    result.Success = false;
                    result.Message = "Error Occurred In Verification.";
                    result.MessageType = "error";
                }
            }
            else
            {
                result.Success = false;
                result.Message = "Organization settings not found.";
                result.MessageType = "error";
            }
        }
        catch (Exception ex)
        {
            // Log error using your logging mechanism
            // _Utilities.CreateCVCoreLogger("EmailVerificationError", ex.Message);
            _CvLogger.LogErrorApp(ex);
            result.Success = false;
            result.Message = "Error Occurred In Verification.";
            result.MessageType = "error";
        }

        return result;
    }

    private string DecryptUrlParameter(string encryptedValue)
    {
        try
        {
            if (string.IsNullOrEmpty(encryptedValue))
            {
                _CvLogger.LogErrorApp(new Exception("DecryptUrlParameter: Encrypted value is null or empty"));
                return "0";
            }

            // Validate that the encrypted value is not just whitespace
            if (string.IsNullOrWhiteSpace(encryptedValue))
            {
                _CvLogger.LogErrorApp(new Exception("DecryptUrlParameter: Encrypted value is whitespace"));
                return "0";
            }

            _CvLogger.LogInfo($"DecryptUrlParameter: Processing encrypted value: {encryptedValue}");

            // Convert from URL-safe Base64 back to standard Base64
            string standardBase64 = ConvertFromUrlSafeBase64(encryptedValue);
            _CvLogger.LogInfo($"DecryptUrlParameter: Converted to standard Base64: {standardBase64}");

            // Validate that we have a proper Base64 string
            if (!IsValidBase64String(standardBase64))
            {
                _CvLogger.LogErrorApp(new Exception($"DecryptUrlParameter: Invalid Base64 format after conversion: {standardBase64}"));
                return "0";
            }

            // Use the new encryption service for decryption
            string decryptedValue = _encryptionService.Decrypt(standardBase64);

            // Validate decrypted value
            if (string.IsNullOrEmpty(decryptedValue))
            {
                _CvLogger.LogErrorApp(new Exception($"DecryptUrlParameter: Decryption returned null/empty for input: {encryptedValue}"));
                return "0";
            }

            // Just validate that decrypted value is numeric (don't check if positive for timestamps)
            if (!long.TryParse(decryptedValue, out long numericValue))
            {
                _CvLogger.LogErrorApp(new Exception($"DecryptUrlParameter: Decrypted value is not a valid number: {decryptedValue}"));
                return "0";
            }

            _CvLogger.LogInfo($"DecryptUrlParameter: Successfully decrypted {encryptedValue} to {decryptedValue}");
            return decryptedValue;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(new Exception($"DecryptUrlParameter failed for input '{encryptedValue}': {ex.Message}", ex));
            return "0";
        }
    }

    /// <summary>
    /// Converts URL-safe Base64 back to standard Base64 format
    /// </summary>
    private string ConvertFromUrlSafeBase64(string urlSafeBase64)
    {
        try
        {
            if (string.IsNullOrEmpty(urlSafeBase64))
                return string.Empty;

            // Replace URL-safe characters back to standard Base64
            string base64 = urlSafeBase64
                .Replace('-', '+')
                .Replace('_', '/');

            // Add padding if necessary
            int padding = 4 - (base64.Length % 4);
            if (padding != 4)
            {
                base64 += new string('=', padding);
            }

            return base64;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(new Exception($"ConvertFromUrlSafeBase64 failed for input '{urlSafeBase64}': {ex.Message}", ex));
            return string.Empty;
        }
    }

    /// <summary>
    /// Converts standard Base64 to URL-safe Base64 format
    /// </summary>
    private string ConvertToUrlSafeBase64(string base64String)
    {
        try
        {
            if (string.IsNullOrEmpty(base64String))
                return string.Empty;

            // Replace URL-unsafe characters with URL-safe alternatives
            return base64String
                .Replace('+', '-')
                .Replace('/', '_')
                .TrimEnd('='); // Remove padding for URL safety
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(new Exception($"ConvertToUrlSafeBase64 failed for input '{base64String}': {ex.Message}", ex));
            return string.Empty;
        }
    }

    /// <summary>
    /// Validates if a string is a proper Base64 format
    /// </summary>
    private bool IsValidBase64String(string base64String)
    {
        try
        {
            if (string.IsNullOrEmpty(base64String))
                return false;

            // Check if string length is multiple of 4 (Base64 requirement)
            if (base64String.Length % 4 != 0)
                return false;

            // Try to convert from Base64 to validate format
            Convert.FromBase64String(base64String);
            return true;
        }
        catch
        {
            return false;
        }
    }





    private bool IsLinkValid(string timestamp, int expirationSeconds)
    {
        try
        {
            if (string.IsNullOrEmpty(timestamp))
                return false;

            // Parse the timestamp from the link
            if (!long.TryParse(timestamp, out long linkTimestamp))
                return false;

            // Get current timestamp
            long currentTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            // Calculate the difference in seconds
            long timeDifference = currentTimestamp - linkTimestamp;

            // Check if the link is still valid (within expiration time)
            return timeDifference <= expirationSeconds && timeDifference >= 0;
        }
        catch
        {
            return false;
        }
    }
}

public class EmailVerificationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = "";
    public string MessageType { get; set; } = "info"; // success, error, warning, info
}
