﻿@model BCM.BusinessClasses.PerformanceEvaluation

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@* <form asp-action="DeletePerformanceEvaluation" method="post">

    <div>
        <input type="hidden" asp-for="ID" />
    </div>
    <div class="modal-header d-grid text-center">

        <span class="fw-semibold">Do you really want to delete</span>
         <span>"<span class="text-primary fw-semibold">@Model.EvaluationName</span>" ?</span> 
    </div>
    <div class="modal-body text-center">
        <img src="~/img/isomatric/delete.svg" width="260" /> 
    </div>
    <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form> *@




<form asp-action="DeletePerformanceEvaluation" method="post">
    <div>
        <input type="hidden" asp-for="ID"  />
        <input type="hidden" asp-for="ITDRName" />
    </div>
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    <div class="modal-body d-grid px">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="OrgGroupSpan">@Model.ITDRName</span>" ?</span>
    </div>
    <div class="modal-footer justify-content-center p-0">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>