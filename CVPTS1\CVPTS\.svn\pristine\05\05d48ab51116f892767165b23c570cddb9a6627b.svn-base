﻿@model IEnumerable<BCM.BusinessClasses.ProcessBIALegalAndRegulatory>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.AspNetCore.Http
@using BCM.Shared;
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@{
    ViewBag.Title = "BIA Legal And Regulatory";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@{
    var ProcessName = HttpContextAccessor.HttpContext.Session.GetString("ProcessNameWithCode");
    var ProcessVersion = HttpContextAccessor.HttpContext.Session.GetString("ProcessVersion");
}
@* <div class="Page-Header d-flex align-items-center justify-content-between mb-3">
    <h6 class="Page-Title">Legal and Regulatory Dependencies for @ViewBag.ProcessName ( @ViewBag.ProcessCode )</h6>
</div> *@


<div class="Page-Condant card border-0">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        <p style="padding-left:1%" class="fw-bold mb-2">Legal and Regulatory Dependencies for @ViewBag.ProcessName ( @ViewBag.ProcessCode )</p>
        <div class="align-items-right" style="padding-right:2%">
            @* <p class="fw-semibold" id="iVersion">Version : @ViewBag.ProcessVersion</p> *@
            <p class="fw-semibold" id="iVersion">Version : @ProcessVersion</p>
        </div>
    </div>
    <div class="card-body">

        <div class="row">
            <div class="col-12">                
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <div class="accordion accordion-flush" id="accordionFlushExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                            Instructions and Guidelines
                                        </button>
                                    </h2>
                                    <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                                        <div class="accordion-body">
                                            @*  <textarea name="content" disabled contenteditable="false" id="editor2">@ViewBag.Description</textarea >*@
                                            <div id="editor2" class="content-editable">
                                                @Html.Raw(ViewBag.Description)
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div>                   
                    <div class="">
                        @foreach (var objQusetions in ViewBag.Questions)
                        {
                            <p class="text-primary d-flex align-items-center gap-1">
                                <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsequestion1" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                                Question: @objQusetions.QuestionDetails
                            </p>
                        }
                        <div class="ps-2 collapse show" id="collapsequestion1">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Dependency / Report etc. </th>
                                        <th>Legal / Regulatory Authority</th>
                                        <th>Frequency</th>
                                        @* <th>Completion Status</th> *@
                                        <th style="text-align:right !important;">Action</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model != null && Model.Count() != 0)
                                    {
                                        int iIndex = 0;
                                        foreach (var objQusetions in ViewBag.Questions)
                                        {
                                            <tr data-bs-toggle="collapse" data-bs-target="#collapseQuestion" aria-expanded="false" aria-controls="collapseQuestion" role="button">
                                                <td class="bg-secondary-subtle"><i class="cv-down-arrow ms-2"></i></td>
                                                <td class="bg-secondary-subtle" colspan="10">Question: @objQusetions.QuestionDetails</td>
                                            </tr>

                                            foreach (var item in Model)
                                            {
                                                string CompletionStatus = item.IsComplete == 0 ? "Incomplete" : "Complete";

                                                if (objQusetions.ID == item.QuestionID)
                                                {
                                                    iIndex++;
                                                    <tr>
                                                        <td>@iIndex</td>
                                                        <td>@item.DependencyReport</td>
                                                        <td>@item.LegalAndRegAuth</td>
                                                        <td>
                                                            @if (Convert.ToInt32(@item.Frequency) == 1)
                                                            {
                                                                <p>Weekly</p>
                                                            }
                                                            @if (Convert.ToInt32(@item.Frequency) == 2)
                                                            {
                                                                <p>Monthly</p>
                                                            }
                                                            @if (Convert.ToInt32(@item.Frequency) == 3)
                                                            {
                                                                <p>Quaterly</p>
                                                            }
                                                            @if (Convert.ToInt32(@item.Frequency) == 4)
                                                            {
                                                                <p>BI-Annualy</p>
                                                            }
                                                            @if (Convert.ToInt32(@item.Frequency) == 4)
                                                            {
                                                                <p>Annualy</p>
                                                            }

                                                        </td>
                                                        @* <td>@CompletionStatus</td> *@
                                                        <td style="text-align:right !important;">
                                                            <span role="button"><i class="cv-edit me-1 btnEdit" @ViewBag.ButtonAccess.btnUpdate data-id="@item.ID"></i></span>
                                                            <span role="button"><i class="cv-delete text-danger btnDelete" @ViewBag.ButtonAccess.btnImgDelete data-id="@item.ID"></i></span>
                                                        </td>
                                                    </tr>
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <img src="~/img/Isomatric/no_records_found.svg" alt="No Records Found" style="width: 120px; height: auto; margin-bottom: 1rem;">
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">
                <form asp-action="AddUpdateBIALegalAndRegularty" method="post" id="addUpdateBIALegalAndRegularty" class="needs-validation progressive-validation" novalidate>
                    <div class="row row-cols-2">
                        @* <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Version</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-version"></i></span>
                                    <input class="form-control" type="text" readonly value="1.0" />
                                    <input type="hidden" value="0" name="ID" />
                                </div>
                            </div>
                        </div> *@
                        <div class="col">
                            <div class="form-group">
                                <input type="hidden" value="" id="ID" name="ID" />
                                <label class="form-lable">Questions</label>
                                @foreach (var objQusetions in ViewBag.Questions)
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" checked="checked" name="QuestionID" id="@objQusetions.ID" value="@objQusetions.ID">
                                        <label class="form-check-label" for="inlineRadio1">@objQusetions.QuestionDetails</label>
                                    </div>
                                }
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">Dependency / Report etc.</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-reports"></i></span>
                                    <textarea class="form-control" placeholder="Enter Dependency / Report etc." style="height:0px" name="DependencyReport" required></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Dependency / Report etc.</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">Legal / Regulatory Authority</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-Legal-Entity"></i></span>
                                    <textarea class="form-control" placeholder="Enter Legal / Regulatory Authority" style="height:0px" name="LegalAndRegAuth" required></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Legal / Regulatory Authority</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">Frequency</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-critical-BCM-entities"></i></span>
                                    <select class="form-select-sm form-control selectized" name="Frequency" required>
                                        <option disabled value="0" selected>-- Select Frequency-- </option>
                                        <option value="1">Weekly</option>
                                        <option value="2">Monthly</option>
                                        <option value="3">Quaterly</option>
                                        <option value="4">BI-Annualy</option>
                                        <option value="5">Annualy</option>
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select Frequency</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Description" style="height:0px" name="BIAFindings"></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Description</div>
                            </div>
                        </div>
                        <div class="col-12 text-end">       
                            <a class="btn btn-sm btn-outline-primary" role="button" asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>
                                                                                    @* <a role="button" class="btn btn-sm btn-primary" asp-action="ManageBusinessProcess" asp-controller="ManageBusinessProcesses" asp-area="BCMProcessBIA" formnovalidate>View All</a> *@
                            <button type="submit" class="btn btn-sm btn-primary" @ViewBag.ButtonAccess.btnUpdate id="btnSubmit">Save</button>
                            <button class="btn btn-sm btn-secondary" id="btnCancel" formnovalidate>Cancel</button>


                        </div>
                    </div>
                </form>
            </div>
        </div>      
    </div> 
</div>


<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>


@section Scripts {
    <script>
        $(document).ready(function () {

             // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function() {
                        console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function() {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                console.log("Initializing validation for addUpdateBIALegalAndRegularty form");

                if (window.BCMValidation) {
                    console.log("BCMValidation found, initializing");

                    // Get the form element
                    const form = document.getElementById('addUpdateBIALegalAndRegularty');
                    if (!form) {
                        console.error("Form not found with ID: addUpdateBIALegalAndRegularty");
                        return;
                    }

                    // Store the original custom messages from invalid-feedback divs
                    const customMessages = {};
                    form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                        const originalMessage = element.textContent.trim();
                        console.log("Processing invalid-feedback element:", originalMessage);

                        // Find the associated input by looking in the form group
                        const formGroup = element.closest('.form-group');
                        let input = formGroup?.querySelector('input[required], select[required], textarea[required]');

                        // If not found, look for any input in the form group
                        if (!input) {
                            input = formGroup?.querySelector('input, select, textarea');
                        }

                        if (input) {
                            // Store the custom message using multiple keys for reliability
                            const keys = [
                                input.id,
                                input.name,
                                input.getAttribute('asp-for')
                            ].filter(key => key); // Remove null/undefined values

                            keys.forEach(key => {
                                customMessages[key] = originalMessage;
                                console.log("Stored custom message for key", key, ":", originalMessage);
                            });
                        } else {
                            console.log("No input found for invalid-feedback:", originalMessage);
                        }
                    });

                    // Function to restore custom message for an input
                    function restoreCustomMessage(input) {
                        const keys = [
                            input.id,
                            input.name,
                            input.getAttribute('asp-for')
                        ].filter(key => key);

                        for (let key of keys) {
                            if (customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                    return true;
                                }
                            }
                        }
                        return false;
                    }

                    // Function to restore all custom messages after validation
                    function restoreAllCustomMessages() {
                        form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                            // Small delay to ensure the global validation has finished
                            setTimeout(() => {
                                restoreCustomMessage(input);
                            }, 10);
                        });
                    }

                    // Initialize the validation framework
                    window.BCMValidation.init();

                    // Add required field indicators (asterisks)
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for pattern-based inputs
                    window.BCMValidation.addFormatIndicators(form);

                    // Add user interaction validation for inputs
                    const allInputs = form.querySelectorAll('input:not([type="hidden"]), select, textarea');
                    allInputs.forEach(function(input) {
                        // Add blur event listener to mark field as touched and validate
                        input.addEventListener('blur', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                // Mark field as touched and remove validation-pending
                                formGroup.classList.add(window.BCMValidation.classes.fieldTouchedClass);
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            // Validate the input using global validation
                            if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }

                            // Restore custom message after a short delay
                            setTimeout(() => {
                                restoreCustomMessage(this);
                            }, 20);
                        });

                        // Add input event listener for real-time validation (only after field is touched)
                        input.addEventListener('input', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup && formGroup.classList.contains(window.BCMValidation.classes.fieldTouchedClass)) {
                                // Validate the input
                                if (this.hasAttribute('pattern')) {
                                    window.BCMValidation.validatePatternInput(this);
                                } else {
                                    window.BCMValidation.validateInput(this);
                                }

                                // Restore custom message after a short delay
                                setTimeout(() => {
                                    restoreCustomMessage(this);
                                }, 20);
                            }
                        });
                    });

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");

                        // Show all validation messages
                        window.BCMValidation.showAllValidationMessages(form);

                        // Validate the form using global validation
                        const isValid = window.BCMValidation.validateForm(form);

                        // Restore all custom messages after validation
                        restoreAllCustomMessages();

                        console.log("Form validation result:", isValid);

                        if (!isValid) {
                            console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                        }
                    });
                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }

            UpdateButtonLabel();

            $('.btnEdit').click(function () {
                var iID = $(this).data('id');
                // Clear validation errors before populating new values
                const form = document.getElementById('addUpdateBIALegalAndRegularty');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Add validation-pending class to hide messages until user interaction
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }

                $.ajax({
                    url: '@Url.Action("EditBIALegalAndRegularty", "BIALegalAndRegulatory")',
                    type: 'GET',
                    data: { iID: iID },
                    success: function (data) {

                        console.log(data);
                        $('textarea[name="BIAFindings"]').val(data.biaFindings);
                        $('input[name="QuestionID"][value="' + data.questionID + '"]').prop('checked', true);
                        // $('select[name="EntityName"]').val(data.entityID);
                        $('textarea[name="DependencyReport"]').val(data.dependencyReport);
                        $('input[name="ID"]').val(data.id);
                        $('textarea[name="LegalAndRegAuth"]').val(data.legalAndRegAuth);
                        $('select[name="Frequency"]').val(data.frequency);
                        UpdateButtonLabel();
                    },
                    error: function () {
                        console.log('Error');
                    }
                });
            });

            $(document).on('click', '.btnDelete', function () {
                var iID = $(this).data('id');
                //$.get('/BCMProcessBIAForms/BIALegalAndRegulatory/DeleteBIALegalAndRegularty/', { iID: iID }, function (data) {
                $.get('@Url.Action("DeleteBIALegalAndRegularty", "BIALegalAndRegulatory")', { iID: iID }, function (data) {
                    $('#DeleteBody').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

            $(document).on('click', '#btnCancel', function (event) {
                event.preventDefault();

                // Clear validation errors when canceling
                const form = document.getElementById('addUpdateBIALegalAndRegularty');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Reset validation state
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }

                location.reload();
            });
        });

        $('body').on('submit', 'form', function (e) {
            e.preventDefault();
            var form = $(this);
            $.ajax({
                type: form.attr('method'),
                url: form.attr('action'),
                data: form.serialize(),
                success: function (data) {
                    $('#Modal').modal('hide');
                    $('#DeleteModal').modal('hide');

                    // Update toast message
                    $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                    // Determine toast color based on operation result and type
                    const toastElement = $('#liveToast');
                    const formAction = form.attr('action').toLowerCase();

                    // Remove existing background classes
                    toastElement.removeClass('bg-success bg-warning bg-danger');

                    if (data && data.success) {
                        // Success: Add appropriate background class based on action
                        if (formAction.includes('delete')) {
                            toastElement.addClass('bg-danger');
                        } else {
                            toastElement.addClass('bg-success');
                        }
                    } else {
                        // Failure: Add danger background class
                        toastElement.addClass('bg-danger');
                    }

                    const toastLiveExample = document.getElementById('liveToast');
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                    toastBootstrap.show();

                    // Delay reload to allow toast to be visible
                    setTimeout(function () {
                        location.reload();
                    }, 3000);
                },
                error: function (xhr, status, error) {
                    console.log(error);
                    console.error(xhr.status);
                    console.error(xhr.responseText);
                }
            });
        });


        function UpdateButtonLabel() {
            var id = $('input[name="ID"]').val();
            if (id > 0) {
                $('#btnSubmit').text('Update');
            } else {
                $('#btnSubmit').text('Save');
            }
        }
    </script>
}