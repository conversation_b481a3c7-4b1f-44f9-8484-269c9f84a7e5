﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewBag.Title = "BIA Qualitative Impact Matrix";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@using BCM.BusinessClasses
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor


<div class="Page-Header d-flex align-items-center justify-content-between mb-3">
    <h6 class="Page-Title">Quantitative Analysis Using BIA Matrix</h6>
</div>

<div class="Page-Condant card border-0">
    <div class="card-body">
        <div class="row">
            <div class="col-12">
                <p class="fw-semibold">
                    Instructions and Guidelines
                </p>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <div class="accordion accordion-flush" id="accordionFlushExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                            Worksheet #1: Process Identification and Criticality Assessment
                                        </button>
                                    </h2>
                                    <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                                        <div class="accordion-body">
                                            <p>Hi,Welcome to</p>

                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-12">
                <p class="fw-semibold">
                    Configure Process BIA Quantitative Impact for Human Resource ( PRC-2020-131 )
                </p>
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-lable">Version</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-version"></i></span>
                                <input class="form-control" type="text" readonly value="1" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-lable">Select Impact Severity</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-impact"></i></span>
                                <select class="form-select-sm" id="ddlImactSeverityType" required>
                                    <option disabled value="" selected>-- Select Impact Severity -- </option>
                                    @{
                                        foreach (var objImpactSeverity in ViewBag.ImpactSeverity)
                                        {
                                                        <option value="@objImpactSeverity.Value">@objImpactSeverity.Text</option>
                                        }
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-lable">Questions</label>
                            <div>
                                <div class="form-check form-check-inline mt-2">
                                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="option1" checked>
                                    <label class="form-check-label" for="inlineRadio1">What is the Quantitative Impact on the Process?</label>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-12">
                <div>
                    <div>
                        <div class="ps-2">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>Impact</th>
                                        @{
                                            var columnCount = 0;
                                            foreach (var objBIAProfileMaster in ViewBag.ColumnHeaders)
                                            {
                                                columnCount++;
                                                            <th>
                                                                <input type="hidden" id="<EMAIL>"
                                                                       name="@objBIAProfileMaster.TimeIntervalID" />
                                                                @objBIAProfileMaster.TimeIntervalText
                                                            </th>
                                            }
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    @{
                                        var rowCount = 0;
                                        var mainRowCount = 0;
                                        foreach (Impact objImpactTypeName in ViewBag.ImpactTypeName)
                                        {
                                            mainRowCount++;
                                            var textBoxValues = ViewBag.TextBoxValues as Dictionary<(int rowId, int colId), string>;
                                                        <tr>
                                                            <td colspan="9">
                                                                <input type="hidden" name="@objImpactTypeName.ImpactTypeID" id="<EMAIL>" />
                                                                <p class="text-primary d-flex align-items-center gap-1 mb-0">
                                                                    <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button"
                                                                          data-bs-toggle="collapse" data-bs-target="#collapsequestion_@mainRowCount" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                                                                    ImpactTypeName : @objImpactTypeName.ImpactTypeName
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        @foreach (Impact objImpact in ViewBag.ImpactName)
                                            {
                                                if (objImpactTypeName.ImpactTypeID == objImpact.ImpactTypeID)
                                                {
                                                    rowCount++;
                                                                <tr class="collapse show" id="collapsequestion_@mainRowCount">
                                                                    <td>
                                                                        <input type="hidden" name="@objImpact.ImpactID" />
                                                                        @objImpact.ImpactName
                                                                    </td>
                                                                    @for (int i = 1; i <= columnCount; i++)
                                                        {
                                                            var headerID = ViewBag.ColumnHeaders[i - 1].TimeIntervalID;
                                                            // var isSelected = selectedValues.ContainsKey((objImpact.ImpactID, headerID)) &&
                                                            // selectedValues[(objImpact.ImpactID, headerID)].ToString() == objImpactRatings.Value;
                                                                        <td>
                                                                            <div class="form-group">
                                                                                <div class="input-group">
                                                                                    <input class="form-control column-@i row-@rowCount txtbox" type="number" data-row="@rowCount" data-col="@i"
                                                                                           data-header-id="@headerID" data-impact-id="@objImpact.ImpactID" placeholder="0"                                                                               
                                                                                           data-impactType-id="@objImpactTypeName.ImpactTypeID"
                                                                                           value="@(textBoxValues?.ContainsKey((objImpact.ImpactID,headerID)) == true ?  textBoxValues[(objImpact.ImpactID,headerID)]: "")" />
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                        }
                                                                </tr>
                                                }
                                            }
                                        }
                                    }
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td class="fw-bold mb-0">
                                            Total
                                            <input type="hidden" value="@columnCount" id="columnCount" />
                                            <input type="hidden" value="@rowCount" id="rowCount" />
                                        </td>
                                        @{
                                            for (int i = 1; i <= columnCount; i++)
                                            {
                                                            <td>
                                                                <div class="form-group">
                                                                    <div class="input-group">
                                                                        <input class="form-control" type="text" value="" readonly id="totalColumnSum-@i" />
                                                                    </div>
                                                                </div>
                                                            </td>
                                            }
                                        }
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">
                @* <div class="text-end me-4 pb-3">
                    <button class="btn btn-sm btn-outline-primary">Back</button>
                    <button class="btn btn-sm btn-secondary">Cancel</button>
                    <button class="btn btn-sm btn-primary">View All</button>
                </div> *@
                <div class="text-end me-4 pb-3">
                    <a class="btn btn-sm btn-outline-primary" role="button" asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>
                    <a role="button" class="btn btn-sm btn-primary" asp-action="ManageBusinessProcess" asp-controller="ManageBusinessProcesses" asp-area="BCMProcessBIA">View All</a>
                    <button class="btn btn-sm btn-primary" @ViewBag.ButtonAccess.btnUpdate id="btnSubmit">Save</button>
                    <button class="btn btn-sm btn-secondary" id="btnCancel">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {

            var totalColumnCount = $('#columnCount').val();
            var totalRowCount = $('#rowCount').val();

            function getSumAtInitialStage() {
                for (var i = 1; i <= totalColumnCount; i++) {
                    columnSum('.column-' + i, '#totalColumnSum-' + i);
                }
            }

            getSumAtInitialStage();            

            $('.txtbox').on('change', function () {
                var currentColumnCount = $(this).data('col');
                var currentRowCount = $(this).data('row');
                if (!validateRows(currentRowCount, $(this))) {
                    return;
                }
                columnSum('.column-' + currentColumnCount, '#totalColumnSum-' + currentColumnCount);
            })

            function validateRows(currentRowCount, currentTxt) {
                var isValid = true;
                var prevValue = 0;
                $('.txtbox[data-row="' + currentRowCount + '"]').each(function () {
                    var currentValue = parseInt($(this).val());
                    var validateValue = parseInt($(this).data('validated-value')) || currentValue;
                    if (currentValue < prevValue && currentValue != 0) {
                        alert('The value in the next column must be greater than the previous column.');
                        $(this).data('validated-value', 0);
                        isValid = false;
                        $(this).val(0);
                        return false;
                    }
                    prevValue = currentValue;
                })
                return isValid;
            }

            function columnSum(columnClass, resultTextboxId) {
                let columnSum = 0;
                $(columnClass).each(function () {
                    let value = parseFloat($(this).val()) || 0;
                    columnSum += value;
                })
                $(resultTextboxId).val(columnSum);
            }

            function getTextBoxValues(columnClass) {
                var textboxValues = [];
                $(columnClass).each(function () {
                    var value = parseInt($(this).val());
                    if (isNaN(value)) {
                        value = 0;
                    }
                    textboxValues.push(value);
                })
                return textboxValues
            }

            $('#btnSubmit').on('click', function () {
                var headerIdArray = [];
                var rowWiseDdlValues = [];
                var ddlImpactSeverity = $('#ddlImactSeverityType').val();

                for (var i = 1; i <= totalColumnCount; i++) {
                    var currentColumnHeadID = $('.column-' + i).data('header-id');
                    headerIdArray.push(currentColumnHeadID);
                }

                for (var i = 1; i <= totalRowCount; i++) {
                    var rowsValue = getTextBoxValues('.row-' + i);
                    rowWiseDdlValues.push(rowsValue);
                }

                $.ajax({
                    type: 'POST',
                    url: '@Url.Action("QuantitativeBIAMatrix", "QuantitativeBIAMatrix")',
                    // data: {
                    //     iRowCount: totalRowCount,
                    //     iDDLImpactSeverity: ddlImpactSeverity,
                    //     strTimeIntevalIds: headerIdArray,
                    //     strRowWiseValues: rowWiseDdlValues
                    // },
                    data: JSON.stringify({
                        iRowCount: totalRowCount,
                        iDDLImpactSeverity: ddlImpactSeverity,
                        strTimeIntevalIds: headerIdArray,
                        strRowWiseValues: rowWiseDdlValues
                    }),
                    contentType: 'application/json',
                    success: function (response) {
                        if (response.success) {
                            alert('Data sent successfully!');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });

            })

            $(document).on('click', '#btnCancel', function (event) {
                event.preventDefault();
                location.reload();
            });

        })        
    </script>
}