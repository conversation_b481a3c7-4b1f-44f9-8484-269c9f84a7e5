﻿using AutoMapper;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Areas.BCMReports.Models.ServiceCriticalityReportModel;
using BCM.UI.Areas.BCMReports.ReportModels.BCMTrainingReport;
using BCM.UI.Areas.BCMReports.ReportModels.BIAReport;
using BCM.UI.Areas.BCMReports.ReportModels.HRReport;
using BCM.UI.Areas.BCMReports.ReportModels.KPIReport;
using BCM.UI.Areas.BCMReports.ReportModels.PCIReportModels;
using BCM.UI.Areas.BCMReports.ReportModels.ProcessRecovery_Report;
using BCM.UI.Areas.BCMReports.ReportModels.RiskAssessmentReport;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using System.Text.Json;

namespace BCM.UI.Controllers.PreBuildReport;
public class PreBuildReportController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    readonly IMapper _mapper;
    public static string ReportGeneratedBy = string.Empty;

    public PreBuildReportController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, IMapper mapper) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _mapper = mapper;
    }

    public IActionResult List()
    {
        ViewBag.OrgList = _Utilities.PupulateOrganisation(_UserDetails!.OrgGroupID.ToString(), _UserDetails!.UserRoleID.ToString());
        ViewBag.OrganizationList = new SelectList(ViewBag.OrgList, "Id", "OrganizationName");
        return View();
    }
    public IActionResult GetRTOSummaryReportDetails()
    {
        try
        {
            _CVLogger.LogInfo("Entered into the GetRTOSummaryReport method successfully.");

            var businessProcess = _ProcessSrv.GetAllBusinessProcessDetails();

            _CVLogger.LogInfo("RTO Summary Report Data retrieved successfully.");

            var reportDatas = businessProcess.Select(data => new PCIReportListVm
            {
                OrgID = data.OrgID,
                OrgName = data.OrgName,
                UnitID = data.UnitID,
                UnitName = data.UnitName,
                DepartmentID = data.DepartmentID,
                DepartmentName = data.DepartmentName,
                SubFunctionName = data.SubFunctionName,
                ProcessID = data.ProcessID,
                ProcessName = data.ProcessName,
                ProcessOwner = data.ProcessOwner,
                RTO = data.RTO,
                MTR = data.MTR,
                IsEffective = data.IsEffective,
                RTOValue = _Utilities.GetFormattedRTO_New(string.IsNullOrWhiteSpace(data.RTO) ? 0 : Convert.ToInt32(data.RTO))
            }).ToList();

            var pciReportData = new RtoSummaryReportVm
            {
                UserName = _UserDetails?.UserName,
                PCIReportListVms = reportDatas
            };

            return Json(new { success = true, data = pciReportData });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

            return Json(new { success = false, data = ex.Message.ToString() });
        }
    }

    [HttpGet]
    public IActionResult GetBCMTrainingReportDetails()
    {
        _CVLogger.LogInfo("Entered into the method GetBCMTrainingReport successfully.");

        try
        {
            if (_UserDetails == null)
            {
                return Json(new { success = false, data = "Session expired" });
            }
            var reportData = _ProcessSrv.BCMTrainingMaster_getAll();
            if (reportData != null && reportData.Count > 0)
            {
                var reportDataList = reportData.Select(data => new BCMTrainingReportVm
                {
                    TrainingName = data.TrainingName,
                    OwnerName = data.OwnerName,
                    RevieweDate = data.RevieweDate,
                    TrainingScore = data.TrainingScore.ToString(),
                    TotalQuestions = data.iTotalQuestions.ToString(),
                    Result = data.iResult.ToString()
                }).ToList();

                var bcmTrainingReportData = new BCMTrainingReportModel
                {
                    LoginName = _UserDetails?.UserName?.ToString() ?? "Unknown User",
                    BCMTrainingReportVms = reportDataList
                };

                _CVLogger.LogInfo("BCM Training Report Data retrieved successfully.");

                return Json(new { success = true, data = bcmTrainingReportData });

            }


            return Json(new { success = false, data = "ReportData is null" });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, data = ex.Message });
        }
    }
    [HttpGet]//HR-Report Getting Report Details
    public IActionResult GetHrReportDetails(int organizationId, int unitId, int departmentId, int subDepartmentId, int processId)
    {
        try
        {

            _CVLogger.LogInfo("Entered into the method GetHrReportDetails successfully.");
            ReportGeneratedBy = _UserDetails?.UserName?.ToString() ?? "Unknown User";
            var hrReportDto = _ProcessSrv.TimeIntervalData(organizationId, unitId, departmentId, subDepartmentId, processId);
            if (hrReportDto is not null && hrReportDto.Count > 0)
            {
                var hrReportList = new List<HRReportModel>();
                hrReportList = _mapper.Map<List<HRReportModel>>(hrReportDto);
                string serializedDetails = JsonConvert.SerializeObject(hrReportList);
                _CVLogger.LogInfo("Report data retrieved successfully.");
                return Json(new { success = true, data = serializedDetails });
            }
            else
            {
                return Json(new { success = false, data = "No data found" });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, data = ex.Message.ToString() });
        }
    }
    public IActionResult GetServiceCriticalityReport()
    {
        _CVLogger.LogInfo("Entered into the method GetServiceCriticalityReport successfully.");

        try
        {
            if (_UserDetails == null)
            {
                return Json(new { success = false, data = "Session expired" });
            }
            var ReportData = _ProcessSrv.GetDataForITServiceReport(_UserDetails.OrgID);
            var FilterData = new List<ServiceCriticalityReportModel>();
            if (ReportData != null && ReportData.Count > 0)
            {
                foreach (var item in ReportData)
                {
                    if (item.RiskTo != null && item.ServiceCriticallyValue != null)
                    {
                        FilterData.Add(new ServiceCriticalityReportModel
                        {
                            RiskTo = item.RiskTo,
                            ServiceCriticallyValue = item.ServiceCriticallyValue
                        });
                    }
                }
                var totalData = new List<ServiceCriticalityReportVm>
                    {
                      new ServiceCriticalityReportVm
                         {
                        LoginName = _UserDetails.UserName,
                        ServiceCriticalityReportData = FilterData
                         }

                     };
                return Json(new { success = true, data = totalData });

            }

            _CVLogger.LogInfo("Service Criticality Report Data retrieved successfully.");

            return Json(new { success = false, data = "ReportData is null" });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, data = ex.Message });
        }
    }

    public IActionResult GetProcessRecoveryPriorityReportData(int organizationId, int unitId, int departmentId, int subDepartmentId, int processNameId)
    {
        try
        {
            _CVLogger.LogInfo("Entered into the method GetProcessRecoveryPriorityReportData successfully.");
            var categorylist = _ProcessSrv.GetCategoryMasterDetails();
            var categoryData = categorylist.Select(data => new CategoryMasterlist
            {
                CategoryName = data.CategoryName,
                CategoryRange = data.CategoryRange,
                Description = data.Description,
                Id = data.Id,
            }).ToList();
            var prpreportData = _ProcessSrv.GetBIAProcess_GetAllForPriority(organizationId, unitId, departmentId, subDepartmentId, processNameId);
            var prprepot = prpreportData.Select(data => new ProcessRecoveryPriorityReportList
            {
                OwnerEmail = data.OwnerEmail,
                OrganizationName = data.OrgName,
                RTO = data.RTOText,
                ProcessName = data.ProcessName,
                StatusID = data.Status,
                UnitName = data.UnitName,
                DepartmentName = data.DepartmentName,
                SubDepartmentName = data.SubFunctionName
            }).ToList();

            if (prprepot.Any())
            {
                var prpreport = new ProcessRecoveryPriorityReportVm
                {
                    LoginName = _UserDetails?.UserName ?? "Unknown User",
                    CategoryMastersList = categoryData,
                    ProcessRecoveryPriorityReportList = prprepot
                };
                return Json(new { success = true, data = prpreport });
            }

            _CVLogger.LogInfo("ProcessRecoveryPriorityReport Data retrieved successfully.");
            return Json(new { success = false, data = "No data found" });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

            return Json(new { success = false, data = ex.Message.ToString() });
        }

    }
    [HttpPost]
    public ActionResult LoadReport([FromBody] JsonElement reportJsonData)
    {
        string reportName = reportJsonData.GetProperty("reportName").GetString();
        var reportData = reportJsonData.GetProperty("reportData");

        ViewData[reportName + "Data"] = reportData;
        return PartialView(reportName, reportData);
    }
    [HttpGet]//Unit dropdown for HR Report
    public JsonResult GetAllUnits(int organizationId)
    {
        try
        {
            var unitList = _Utilities.BindUnit(organizationId);
            return Json(new { success = false, data = unitList });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message.ToString() });
        }
    }

    [HttpGet]//Department dropdown for HR Report
    public JsonResult GetAllDepartments(int unitId)
    {
        try
        {
            var departmentList = _ProcessSrv.GetDepartmentByUnitId(unitId);
            return Json(new { success = false, data = departmentList });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message.ToString() });
        }
    }
    [HttpGet]//Sub-Department dropdown for HR Report
    public JsonResult GetAllSubDepartments(int departmentId)
    {
        try
        {
            var subDepartmentList = _ProcessSrv.GetSubFunctionListByFunctionID(departmentId.ToString());
            return Json(new { success = false, data = subDepartmentList });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message.ToString() });
        }
    }
    [HttpGet]//Process dropdown for HR Report
    public JsonResult GetAllProcess(int subDepartmentId, int organizationId)
    {
        try
        {
            var processList = _ProcessSrv.GetBusinessProcessMasterListByOrgIDAndSubDeptID(organizationId, subDepartmentId);
            return Json(new { success = false, data = processList });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message.ToString() });
        }
    }
    [HttpGet] //GetKPIReportDetails for KPI Report
    public IActionResult GetKPIReportDetails()
    {
        try
        {
            ReportGeneratedBy = string.Empty;
            _CVLogger.LogInfo("Entered into the method GetKPIReportDetails successfully.");
            ReportGeneratedBy = _UserDetails?.UserName?.ToString() ?? "Unknown User";
            var kpiReportData = _ProcessSrv.KPIReportData(_UserDetails!.OrgID);
            kpiReportData.ToList().ForEach(item => item.FrequencyName = _Utilities.PopulateFrequency_ByID(item.FrequencyID));
            if (kpiReportData is not null && kpiReportData.Count > 0)
            {
                var kpiReportModels = new List<KPIReportModel>();
                kpiReportModels = _mapper.Map<List<KPIReportModel>>(kpiReportData);
                string serializedDetails = JsonConvert.SerializeObject(kpiReportModels);
                return Json(new { success = true, data = serializedDetails });
            }
            else
            {
                return Json(new { success = false, message = "No data found" });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message.ToString() });
        }
    }
    [HttpGet]//GetRiskAssessmentReportDetails for Risk Assessment Report
    public IActionResult GetRiskAssessmentReportDetails()
    {
        try
        {
            ReportGeneratedBy = string.Empty;
            _CVLogger.LogInfo("Entered into the method GetRiskAssessmentReportDetails successfully.");
            ReportGeneratedBy = _UserDetails?.UserName?.ToString() ?? "Unknown User";
            var riskAssessmentDetails = _ProcessSrv.GetRiskAssessmentReportData(_UserDetails!.OrgID, _Utilities.DefaultRiskProfileID);
            if (riskAssessmentDetails is not null && riskAssessmentDetails.Count > 0)
            {
                var riskAssessmentReportModels = new List<RiskAssessmentReportModel>();
                riskAssessmentReportModels = _mapper.Map<List<RiskAssessmentReportModel>>(riskAssessmentDetails);
                string serializedDetails = JsonConvert.SerializeObject(riskAssessmentReportModels);
                _CVLogger.LogInfo("Risk Assessment Report Data retrieved successfully.");
                return Json(new { success = true, data = serializedDetails });
            }
            else
            {
                return Json(new { success = false, message = "No data found" });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, data = ex.Message.ToString() });
        }
    }
    [HttpGet]
    public IActionResult GetBiaReportDetails(int organizationId, int unitId, int departmentId, int subDepartmentId)
    {
        try
        {
            ReportGeneratedBy = string.Empty;
            _CVLogger.LogInfo("Entered into the method GetBiaReportDetails successfully.");
            ReportGeneratedBy = _UserDetails?.UserName?.ToString() ?? "Unknown User";
            var processIdentification = _ProcessSrv.GetBIAProfileImpactMatrixByProfID_Report(organizationId, unitId, departmentId, subDepartmentId) ?? null;
            var vitalRecords = _ProcessSrv.BIAVitalRecordGetAllByBIAID_Report(organizationId, unitId, departmentId, subDepartmentId) ?? null;
            var workspaceRequirements = _ProcessSrv.ProcessBIAFacilityGetByBIAID_Report(organizationId, unitId, departmentId, subDepartmentId) ?? null;
            var hrRequirements = _ProcessSrv.ProcessBIAPeopleGetByBIAID_Report(organizationId, unitId, departmentId, subDepartmentId) ?? null;
            var itRequirements = _ProcessSrv.ProcessBIAApplicationGetByBIAID_Report(organizationId, unitId, departmentId, subDepartmentId) ?? null;
            var workAreaRecovery = _ProcessSrv.ProcessBIAWorkAreaRecoverySuppliesBIAID_Report(organizationId, unitId, departmentId, subDepartmentId) ?? null;
            var thirdParties = _ProcessSrv.ProcessBIAThirdPartyGetByBIAID_Report(organizationId, unitId, departmentId, subDepartmentId) ?? null;

            if (processIdentification is not null && processIdentification.Count > 0 || vitalRecords is not null && vitalRecords.Count > 0
                || workspaceRequirements is not null && workspaceRequirements.Count > 0 || hrRequirements is not null && hrRequirements.Count > 0
                || itRequirements is not null && itRequirements.Count > 0 || workAreaRecovery is not null && workAreaRecovery.Count > 0
                || thirdParties is not null && thirdParties.Count > 0)
            {
                _CVLogger.LogInfo("BIA Report Data retrieved successfully.");
                var biaProcessIdentification = _mapper.Map<List<ProcessIdentificationAndCriticalityAssessment>>(processIdentification/*!.DistinctBy(x => x.ProcessName).ToList()*/);
                var biavitalRecords = _mapper.Map<List<VitalRecordsAndInformation>>(vitalRecords/*!.DistinctBy(x => x.ProcessName).ToList()*/);
                var biaWorkspaceRequirements = _mapper.Map<List<BuildingAndWorkspaceRequirements>>(workspaceRequirements/*!.DistinctBy(x => x.ProcessName).ToList()*/);
                var biaHrRequirements = _mapper.Map<List<HumanResourcesRequirements>>(hrRequirements/*!.DistinctBy(x => x.ProcessName).ToList()*/);
                var biaItRequirements = _mapper.Map<List<ITRequirements>>(itRequirements/*!.DistinctBy(x => x.ProcessName).ToList()*/);
                var biaWorkAreaRecovery = _mapper.Map<List<WorkAreaRecoverySupplies>>(workAreaRecovery/*!.DistinctBy(x => x.ProcessName).ToList()*/);
                var biaThirdParties = _mapper.Map<List<ThirdParties>>(thirdParties/*!.DistinctBy(x => x.ProcessName).ToList()*/);
                _CVLogger.LogInfo("BIA Report Data mapped successfully.");

                return Json(new
                {
                    success = true,
                    data = new
                    {
                        ProcessIdentification = biaProcessIdentification,
                        VitalRecords = biavitalRecords,
                        WorkspaceRequirements = biaWorkspaceRequirements,
                        HRRequirements = biaHrRequirements,
                        ITRequirements = biaItRequirements,
                        WorkAreaRecovery = biaWorkAreaRecovery,
                        ThirdParties = biaThirdParties
                    }
                });
            }
            else
            {
                _CVLogger.LogInfo("No data found for BIA Report.");
                return Json(new { success = false, data = "No data found" });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, data = ex.Message.ToString() });
        }
    }
}