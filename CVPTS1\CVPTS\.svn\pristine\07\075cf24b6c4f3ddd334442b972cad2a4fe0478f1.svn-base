﻿using BCM.UI.Areas.BCMReports.ReportModels.BIAReport;
using BCM.UI.Controllers.PreBuildReport;
using Newtonsoft.Json;

namespace BCM.UI.Areas.BCMReports.ReportTemplate;

public partial class BIAReport : DevExpress.XtraReports.UI.XtraReport
{
    public BIAReport(string biareportdata)
    {
        InitializeComponent();

        if (!string.IsNullOrWhiteSpace(biareportdata))
        {
            var reportDetails = JsonConvert.DeserializeObject<BIAReportModel>(biareportdata);

            if (reportDetails != null)
            {
                var processIdentifications = reportDetails._processIdentifications;
                var vitalRecords = reportDetails._vitalRecords;
                var workspaceRequirements = reportDetails._workspaceRequirements;
                var hrRequirements = reportDetails._hrRequirements;
                var itRequirements = reportDetails._itRequirements;
                var workAreaRecovery = reportDetails._workAreaRecovery;
                var thirdParties = reportDetails._thirdParties;
                var timeIntervalData = reportDetails._timeIntervalData;

                if (processIdentifications is not null && processIdentifications.Count > 0)
                {
                    var worksheet1 = processIdentifications.Where(x =>
                    x.ImpactName == "HSSE" || x.ImpactName == "Reputational" ||
                    x.ImpactTypeName == "HSSE Impact" || x.ImpactTypeName == "Reputational Impact").
                    Select(p => new ProcessIdentification
                    {
                        ProcessName = p.ProcessName,
                        ProcessOwner = p.ProcessOwner,
                        CriticalityAssessment = p.CriticalityAssessment = null,
                        HSSE = p.ImpactName == "HSSE" ? p.Cost : null,
                        Reputation = p.ImpactName == "Reputational" ? p.Cost : null,
                        ProgramDelivery = p.ImpactTypeName = null,
                        PriorityActivityList = p.PriorityActivityList = null
                    }).ToList();

                    var processIdentificationsDto = worksheet1.OrderBy(o => o.ProcessName).Distinct().ToList();

                    this.DetailReport.Visible = true;
                    this.DetailReport.DataSource = processIdentificationsDto;
                }
                else
                {
                    this.DetailReport.Visible = false;
                }

                if (vitalRecords is not null && vitalRecords.Count > 0)
                {
                    var vitalRecordsDto = vitalRecords
                        .GroupBy(x => new { x.ProcessName, x.VitalRecordsName })
                        .Select(g => g.First())
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport.Visible = true;
                    this.DetailReport1.DataSource = vitalRecordsDto;
                }
                else
                {
                    this.DetailReport1.Visible = false;
                }

                if (workspaceRequirements is not null && workspaceRequirements.Count > 0)
                {
                    var workspaceRequirementDto = workspaceRequirements
                        .GroupBy(x => new { x.ProcessName, x.PrimaryLocation })
                        .Select(g => g.First())
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport2.Visible = true;
                    this.DetailReport2.DataSource = workspaceRequirementDto;
                }
                else
                {
                    this.DetailReport2.Visible = false;
                }

                if (hrRequirements is not null && hrRequirements.Count > 0)
                {
                    var hrRequirementsDto = hrRequirements
                        .Where(x => !string.IsNullOrEmpty(x.ProcessName))
                        .GroupBy(x => x.ProcessName)
                        .Select(g => g.First())
                        .OrderBy(o => o.ProcessName)
                        .ToList();

                    var day1Global = hrRequirements.FirstOrDefault(x => x.Time == "Day1")?.Value ?? 0;
                    var day3Global = hrRequirements.FirstOrDefault(x => x.Time == "Day3")?.Value ?? 0;
                    var day7Global = hrRequirements.FirstOrDefault(x => x.Time == "Day7")?.Value ?? 0;
                    var day14Global = hrRequirements.FirstOrDefault(x => x.Time == "Day14")?.Value ?? 0;
                    var day30Global = hrRequirements.FirstOrDefault(x => x.Time == "Day30")?.Value ?? 0;
                    var beyondGlobal = hrRequirements.FirstOrDefault(x => x.Time == "Beyond")?.Value ?? 0;

                    foreach (var item in hrRequirementsDto)
                    {
                        var processTimeData = hrRequirements.Where(t => t.ProcessName == item.ProcessName).ToList();
                        if (processTimeData.Any())
                        {
                            var day1Value = processTimeData.FirstOrDefault(x => x.Time == "Day1")?.Value;
                            var day3Value = processTimeData.FirstOrDefault(x => x.Time == "Day3")?.Value;
                            var day7Value = processTimeData.FirstOrDefault(x => x.Time == "Day7")?.Value;
                            var day14Value = processTimeData.FirstOrDefault(x => x.Time == "Day14")?.Value;
                            var day30Value = processTimeData.FirstOrDefault(x => x.Time == "Day30")?.Value;
                            var beyondValue = processTimeData.FirstOrDefault(x => x.Time == "Beyond")?.Value;

                            if (day1Value.HasValue && day1Value.Value > 0)
                                item.Day1 = day1Value.Value.ToString();
                            if (day3Value.HasValue && day3Value.Value > 0)
                                item.Day3 = day3Value.Value.ToString();
                            if (day7Value.HasValue && day7Value.Value > 0)
                                item.Day7 = day7Value.Value.ToString();
                            if (day14Value.HasValue && day14Value.Value > 0)
                                item.Day14 = day14Value.Value.ToString();
                            if (day30Value.HasValue && day30Value.Value > 0)
                                item.Day30 = day30Value.Value.ToString();
                            if (beyondValue.HasValue && beyondValue.Value > 0)
                                item.Beyond = beyondValue.Value.ToString();
                        }
                        else
                        {
                            if (day1Global > 0)
                                item.Day1 = day1Global.ToString();
                            if (day3Global > 0)
                                item.Day3 = day3Global.ToString();
                            if (day7Global > 0)
                                item.Day7 = day7Global.ToString();
                            if (day14Global > 0)
                                item.Day14 = day14Global.ToString();
                            if (day30Global > 0)
                                item.Day30 = day30Global.ToString();
                            if (beyondGlobal > 0)
                                item.Beyond = beyondGlobal.ToString();
                        }
                    }
                    this.DetailReport3.Visible = true;
                    this.DetailReport3.DataSource = hrRequirementsDto;
                }
                else
                {
                    this.DetailReport3.Visible = false;
                }

                if (itRequirements is not null && itRequirements.Count > 0)
                {
                    var itRequirementsDto = itRequirements
                        .GroupBy(x => new { x.ProcessName, x.ITApplication })
                        .Select(g => g.First())
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport4.Visible = true;
                    this.DetailReport4.DataSource = itRequirementsDto;
                }
                else
                {
                    this.DetailReport4.Visible = false;
                }

                if (workAreaRecovery is not null && workAreaRecovery.Count > 0)
                {
                    var workAreaRecoveryDto = workAreaRecovery
                        .GroupBy(x => new { x.ProcessName, x.EquipmentSupply })
                        .Select(g => g.First())
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport5.Visible = true;
                    this.DetailReport5.DataSource = workAreaRecoveryDto;
                }
                else
                {
                    this.DetailReport5.Visible = false;
                }

                if (thirdParties is not null && thirdParties.Count > 0)
                {
                    var thirdPartiesDto = thirdParties
                        .GroupBy(x => new { x.ProcessName, x.ThirdPartName })
                        .Select(g => g.First())
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport6.Visible = true;
                    this.DetailReport6.DataSource = thirdPartiesDto;
                }
                else
                {
                    this.DetailReport6.Visible = false;
                }
            }
        }
        lblUserName.Text = PreBuildReportController.ReportGeneratedBy.ToString();
        this.DisplayName = "BIA Report_" + DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss tt");
    }
}