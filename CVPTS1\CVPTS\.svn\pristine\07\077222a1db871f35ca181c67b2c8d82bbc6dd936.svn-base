﻿@model BCM.BusinessClasses.BCMTrainingMaster

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<form id="editBCMTrainingMasterForm" asp-action="EditBCMTrainingMaster" method="post" class="needs-validation" novalidate>
    @*  <h6 class="Sub-Title">Target Audience</h6> *@
    <div class="row row-cols-2">
        <div class="col">
            <div class="form-group" hidden>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-login-code"></i></span>
                    <input type="hidden" class="form-control" asp-for="ID">
                </div>
                <div class="invalid-feedback">Enter Department Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Organization</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                    <select id="orglist" class="form-select form-control" autocomplete="off" aria-label="Default select example" asp-for="OrgID" required>
                        <option selected disabled value="">-- All Organizations --</option>
                        @foreach (var objOrg in ViewBag.OrgInfo)
                        {
                            <option value="@objOrg.Value">@objOrg.Text</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Department</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-department"></i></span>
                    <select id="departmentlist" class="form-select form-control" autocomplete="off" aria-label="Default select example" asp-for="DepartmentID" required>
                        <option selected value="0">-- All Departments --</option>
                        @foreach (var objDepartment in ViewBag.DepartmentInfo)
                        {
                            <option value="@objDepartment.Value">@objDepartment.Text</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Priority</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                    <select class="form-select form-select-sm" asp-for="Priority" required>
                        <option disabled selected value="">-- Select --</option>
                        <option value="1">Mandatory</option>
                        <option value="2">Not Mandatory</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Training Validity</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-entity"></i></span>
                    <input class="form-control" type="date" asp-format="yyyy-MM-dd" asp-for="ValidityDate" required />
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Trainng Frequency</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-unit"></i></span>
                    <select class="form-select form-select-sm" asp-for="Priority" required>
                        <option selected disabled value="">-- Select --</option>
                        <option value="1">Monthly</option>
                        <option value="3">Quarterly</option>
                        <option value="6">By Annually</option>
                        <option value="12">Annually</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Trainng Approver</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-unit"></i></span>
                    <select class="form-select form-control" id="headlist" autocomplete="off" aria-label="Default select example" asp-for="ApproverID" required>
                        <option selected disabled value="">-- All Resources --</option>
                        @foreach (var objResource in ViewBag.ResourcesInfo)
                        {
                            <option value="@objResource.Value">@objResource.Text</option>
                        }
                    </select>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Unit</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-unit"></i></span>
                    <select id="unitlist" class="form-select form-control" autocomplete="off" aria-label="Default select example" asp-for="UnitID" required>
                        <option selected value="0">-- All Units --</option>
                        @foreach (var objUnit in ViewBag.OrgUnit)
                        {
                            <option value="@objUnit.Value">@objUnit.Text</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Training Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-entity"></i></span>
                    <input class="form-control" type="text" placeholder="Training Name" asp-for="TrainingName" required />
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Training Owner</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-department"></i></span>
                    <select class="form-select form-control" id="headlist" autocomplete="off" aria-label="Default select example" asp-for="OwnerID" required>
                        <option selected disabled value="">-- All Resources --</option>
                        @foreach (var objResource in ViewBag.ResourcesInfo)
                        {
                            <option value="@objResource.Value">@objResource.Text</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Attaching Training Material</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-upload"></i></span>
                    <input type="file" id="upload" class="form-control" asp-for="AttachmentName" required>
                </div>
                <div class="invalid-feedback">Upload Logo</div>
            </div>
            <div class="form-group">
                <label class="form-label">Training Duration</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-entity"></i></span>
                    <input class="form-control" type="text" placeholder="Training Duration" asp-for="TrainingDuration" required />
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Review Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-entity"></i></span>
                    <input class="form-control" type="date" asp-format="yyyy-MM-dd" asp-for="RevieweDate" required />
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group w-100">
                <label class="form-label">Purpose</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-remarks"></i></span>
                    <textarea class="form-control" placeholder="Purpose" style="height:0px" asp-for="Purpose" required></textarea>
                </div>
            </div>
        </div>

    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button id="btnsave" type="submit" class="btn btn-primary btn-sm">Update</button>
            <button id="btnCreateQuestionarie" type="button" class="btn btn-primary btn-sm">Create Questionarie</button>
        </div>
    </div>
</form>

<script>
    // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    //console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            //console.log("Initializing validation for add BCMVendor form");

            if (window.BCMValidation) {
                //console.log("BCMValidation found, initializing");
                window.BCMValidation.init();

                // Manually validate the form on load to check for any initial errors
                const form = document.getElementById('editBCMTrainingMasterForm');
                if (form) {
                    //console.log("Found form with ID:", form.id);

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        //console.log("Form submission triggered");
                        const isValid = window.BCMValidation.validateForm(form);
                        //console.log("Form validation result:", isValid);

                        if (!isValid) {
                           // console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();
                        }
                    });

                    // Manually validate all inputs on page load
                    const inputs = form.querySelectorAll('[required]');
                   // console.log("Found", inputs.length, "required inputs");

                    inputs.forEach(function(input) {
                        if (input.type === 'email') {
                            //console.log("Setting up email validation for", input.id || input.name);
                            window.BCMValidation.validateEmail(input);

                            // Add specific event listeners for email fields
                            input.addEventListener('input', function() {
                                window.BCMValidation.validateEmail(this);
                            });

                            input.addEventListener('blur', function() {
                                window.BCMValidation.validateEmail(this);
                            });
                        } else {
                           // console.log("Setting up validation for", input.id || input.name);
                            window.BCMValidation.validateInput(input);

                            // Add specific event listeners for other fields
                            input.addEventListener('input', function() {
                                window.BCMValidation.validateInput(this);
                            });

                            input.addEventListener('blur', function() {
                                window.BCMValidation.validateInput(this);
                            });
                        }
                    });
                } else {
                    console.error("Form not found with ID: editBCMTrainingMasterForm");

                    // Try to find the form by class as a fallback
                    const formByClass = document.querySelector('form.needs-validation');
                    if (formByClass) {
                        console.log("Found form by class instead");
                        // Same validation setup as above
                        // (Code omitted for brevity)
                    } else {
                        console.error("Form not found by class either!");
                    }
                }
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }
</script>