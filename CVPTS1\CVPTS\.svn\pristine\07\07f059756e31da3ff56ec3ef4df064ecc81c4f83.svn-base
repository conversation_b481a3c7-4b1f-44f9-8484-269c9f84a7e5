﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

public class CVLogger
{
    public readonly ILoggerFactory _LoggerFactory;
    public readonly ILogger<ILogger> _Logger;
    public IConfiguration? _Configuration;
    public CVLogger(ILoggerFactory LoggerFactory, ILogger<ILogger> Logger, IConfiguration Configuration)
    {
        _LoggerFactory = LoggerFactory;
        _Logger = Logger;
        _Configuration = Configuration;

    }

    public void LogErrorApp(Exception ex)
    {
        LogError(ex,"CVApp");
    }

    public void LogErrorMgr(Exception ex)
    {
        LogError(ex, "CVMgr");
    }

    public void LogErrorSrv(Exception ex)
    {
        LogError(ex, "CVSrv");
    }

    public void LogErrorUtilities(Exception ex)
    {
        LogError(ex, "CVUtilities");
    }

    public void LogErrorApi(Exception ex)
    {
        LogError(ex, "CVApi");
    }

    private void LogError(Exception ex, string strFileName)
    {
        string? LogFilePath = _Configuration.GetSection("Path")["LogFilePath"].ToString();
        if (LogFilePath != null)
        {
            _LoggerFactory.AddFile($"{LogFilePath}" + strFileName + DateTime.Now.ToString("dd-MM-yyyy") + ".txt", fileSizeLimitBytes: 2000000);

            _Logger.LogError(ex.StackTrace);
        }
    }

    public void LogInfo(string message)
    {
        _Logger.LogInformation(message);
    }

    public void LogWarning(string message)
    {
        _Logger.LogWarning(message);
    }

    //public void LogErrorApp(string message)
    //{
    //    _Logger.LogWarning(message);
    //}
}