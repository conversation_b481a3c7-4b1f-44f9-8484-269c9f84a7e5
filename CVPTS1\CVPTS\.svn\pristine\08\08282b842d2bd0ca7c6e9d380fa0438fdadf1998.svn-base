﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class IncidentTypeMasterController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;

    public IncidentTypeMasterController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult IncidentTypeMaster()
    {
        List<IncidentType> objIncidentType = new List<IncidentType>();
        try
        {
            objIncidentType = _ProcessSrv.GetIncidentTypes_WithDetails();

            ViewBag.Disaster = new SelectList(_Utilities.PopulateDisasterTypeList_New(), "DisasterID", "DisasterName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
     

        return View(objIncidentType);
    }

    public IActionResult SaveIncidentTypeMasterDetails(string IncidentDetails, string IncidentTypeName, int DisasterID, int IncidentTypeID)
    {
        try
        {
            if (CheckIncidentNameExist(DisasterID, IncidentTypeName, IncidentDetails, IncidentTypeID) == true)
            {
                //lblMsgs.Visible = true;
                return RedirectToAction("IncidentTypeMaster"); ;
            }
            else
            {
               // lblMsgs.Visible = false;
                if (AddUpdateIncidentType(DisasterID, IncidentTypeName, IncidentDetails, IncidentTypeID))
                {
                  

                    //if (btnSave.Text == "Update")
                    //   // lblNotifyMsg.Text = "Record Update Successfully";
                    //else
                    // lblNotifyMsg.Text = "Record Saved Successfully";
                    // Response.Redirect("~/BCMAdministration/IncidentTypeMaster.aspx");
                    // lblNotifyMsg.Text = "Details saved successfully";

                    // clear();
                }
                else
                {
                   // lblInfo.Text = CVGlobal.ErrorOccured;
                };
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

        }
        return RedirectToAction("IncidentTypeMaster");
    }

    private bool CheckIncidentNameExist(int DisasterID, string IncidentTypeName, string IncidentDetails, int IncidentTypeID)
    {
        int id = 0;
        if (DisasterID > 0)
            id = Convert.ToInt32(DisasterID);
        return _ProcessSrv.ISExistIncidentType(IncidentTypeName, (Convert.ToInt32(id)) , IncidentTypeID);
    }

    private bool AddUpdateIncidentType(int DisasterID,  string IncidentTypeName, string IncidentDetails, int IncidentTypeID)
    {
        bool isDataSaved = false;

        IncidentType objIncident = new IncidentType();

        //BuildIncident(ref objIncident);


        objIncident.IncidentTypeID = IncidentTypeID;
        objIncident.IncidentTypeName = IncidentTypeName;
        objIncident.DisasterID = DisasterID;
        objIncident.IncidentDetails = IncidentDetails;
        objIncident.ChangedAt = Convert.ToDateTime(DateTime.Now);//Common.ParseDate(DateTime.Now.ToString());              
        objIncident.ChangedBy = Convert.ToString(_UserDetails.UserID);

        try
        {
            if (Convert.ToInt32(IncidentTypeID) < 0)
            {
                isDataSaved = _ProcessSrv.IncidentType_Save(objIncident);
            }
            else
            {
                objIncident.IncidentTypeID = IncidentTypeID;
                isDataSaved = _ProcessSrv.IncidentType_Update(objIncident);
               
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return isDataSaved;
    }

    [HttpGet]
    public IActionResult DeleteIncidentTypeDetails(int iId)
    {
        try
        {
            IncidentType objIncidentType = null;
            if (iId > 0)
            {
                var incidentTypes = _ProcessSrv.GetIncidentTypes_WithDetails();
                objIncidentType = incidentTypes.FirstOrDefault(x => x.IncidentTypeID == iId);
                if (objIncidentType != null)
                {
                    return PartialView("_DeleteIncidentTypeDetails", objIncidentType);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("IncidentTypeMaster");
    }

    [HttpPost]
    public IActionResult DeleteIncidentTypeDetails(IncidentType objIncidentType)
    {
        try
        {
            IncidentType objIncident = new IncidentType();
            if(objIncidentType.IncidentTypeID > 0)
            {
                objIncident.IncidentTypeID = objIncidentType.IncidentTypeID;
                objIncident.ChangedBy = Convert.ToString(_UserDetails.UserID);

                var Delete = _ProcessSrv.IncidentType_Delete(objIncidentType.IncidentTypeID, Convert.ToInt32(_UserDetails.UserID));
            }
            }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("IncidentTypeMaster");
    }

    //private void GetIncidentType()
    //{
    //    //DataTable dtIncidentType = new DataTable();

    //    //dtIncidentType.Columns.Add("IncidentTypeID");
    //    //dtIncidentType.Columns.Add("IncidentTypeName");
    //    //dtIncidentType.Columns.Add("DisasterID");
    //    //dtIncidentType.Columns.Add("DisasterName");

    //    List<IncidentType> lstIncident = new List<IncidentType>();
    //    lstIncident = _ProcessSrv.GetIncidentTypes_WithDetails();

    //    if (lstIncident != null)
    //    {
    //        foreach (BusinessClasses.IncidentType objIncident in lstIncident)
    //        {
    //            dtIncidentType.Rows.Add(objIncident.IncidentTypeID, objIncident.IncidentTypeName, objIncident.DisasterID, objIncident.DisasterName);

    //        }
    //    }

    //}

}

