﻿@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@model BCM.BusinessClasses.AttachmentsAndReviewHistory
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<form asp-action="AddAuditPolicy" id="addAuditPolicy" enctype="multipart/form-data" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="modal-body pt-0">

        <div class="row">
            <div class="col-md-6 col-lg-6 col-xl-6">
                <div class="form-group">
                    <label class="form-label">Organization</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select class="form-select form-control ddlOrganization" asp-for="@Model.Attachments.OrgID" autocomplete="off" id="ddlOrganization" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstOrg,"Id","OrganizationName"))" required>
                            <option selected disabled value="">-- All Organizations --</option>
                        </select>
                    </div>
                    <div class="invalid-feedback">Organization</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Department</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-department"></i></span>
                        <select class="form-select form-control ddlDepartment" autocomplete="off" asp-for="@Model.Attachments.DeptID" id="ddlDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstDepartment,"DepartmentID","DepartmentName"))">
                            <option selected value="0">-- All Departments --</option>
                        </select>
                    </div>
                    <div class="invalid-feedback">Department </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Owner </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-owner"></i></span>
                        <select class="form-select form-control" id="ddlOwner" asp-for="@Model.Attachments.UserId" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.lstResource,"ResourceId","ResourceName"))" required>
                            <option selected disabled value="">-- All Resources --</option>
                        </select>
                    </div>
                    <div class="invalid-feedback">Select Owner </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Attach file</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-attach"></i></span>
                        <input type="file" asp-for=@Model.Attachments.AttachementFile class="form-control custom-file-input" required />
                    </div>
                    <div class="invalid-feedback">Select Attach file</div>
                </div>


            </div>
            <div class="col-md-6 col-lg-6 col-xl-6">
                <div class="form-group">
                    <label class="form-label">Unit </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-unit"></i></span>
                        <select class="form-select form-control ddlUnit" autocomplete="off" id="ddlUnit" asp-for="@Model.Attachments.UnitID" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstUnit,"UnitID","UnitName"))">
                            <option selected value="0">-- All Units --</option>
                        </select>
                    </div>
                    <div class="invalid-feedback">Unit </div>
                </div>
                <div class="form-group">
                    <label class="form-label">SubDepartment </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                        <select class="form-select form-control ddlSubDepartment" autocomplete="off" id="ddlSubDepartment" asp-for="@Model.Attachments.SubFunction" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstSubDepartment,"SubFunctionID","SubFunctionName"))">
                            <option selected value="0">-- All SubDepartments --</option>
                        </select>
                    </div>
                    <div class="invalid-feedback">Select SubDepartment </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Approver </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-approver"></i></span>
                        <select class="form-select form-control" id="ddlApprover" asp-for="@Model.Attachments.ApproverID" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.lstResource,"ResourceId","ResourceName"))" required>
                            <option selected disabled value="">-- All Resources --</option>
                        </select>
                    </div>
                    <div class="invalid-feedback">Select Approver </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Effective Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                        <input type="date" class="form-control" asp-for="@Model.Attachments.EffectiveDate" required />
                    </div>
                    <div class="invalid-feedback">Select Effective Date</div>
                </div>

            </div>
            <div class="col-12">
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-description"></i></span>
                        <textarea class="form-control" asp-for="@Model.Attachments.Description"></textarea>
                    </div>
                    <div class="invalid-feedback">Description</div>
                </div>
            </div>
            <h6 class="Sub-Title">Review Section</h6>
            <div class="col-6">
                <div class="form-group">
                    <label class="form-label">Last Review Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-review-date"></i></span>
                        <input class="form-control" id="ContractStartDate" type="date" asp-for="@Model.Attachments.LastReviewDate" />
                    </div>
                    <div class="invalid-feedback" id="ContractStartDate-feedback">Last Review Date</div>
                </div>
            </div>
            <div class="col-6">
                <div class="form-group">
                    <label class="form-label">Next Review Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-review-date"></i></span>
                        <input class="form-control" id="ContractEndDate" type="date" asp-for="@Model.Attachments.ReviewDate" required />
                    </div>
                    <div class="invalid-feedback" id="ContractEndDate-feedback">Next Review Date</div>
                </div>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
            <div>
                <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</form>
<script>

            $(document).ready(function () {
                $('#ContractStartDate, #ContractEndDate').on('change', validateContractDates);
                $(".custom-file-input").on("change", function () {

                    var FileName = $(this).val().split("\\").pop();
                    $(this).next(".custom-file-input").html(FileName);

                })

                     // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function() {
                        console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function() {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                console.log("Initializing validation for addAuditPolicy form");

                if (window.BCMValidation) {
                    console.log("BCMValidation found, initializing");

                    // Get the form element
                    const form = document.getElementById('addAuditPolicy');
                    if (!form) {
                        console.error("Form not found with ID: addAuditPolicy");
                        return;
                    }

                    // Store the original content of all invalid-feedback divs
                    const customMessages = {};
                    form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                        // Find the associated input
                        const formGroup = element.closest('.form-group');
                        const input = formGroup?.querySelector('input, select, textarea');
                        if (input) {
                            // Store the custom message using the input's ID or name as the key
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key) {
                                customMessages[key] = element.textContent.trim();
                                console.log("Stored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    // Override the validateInput function to preserve custom messages
                    const originalValidateInput = window.BCMValidation.validateInput;
                    window.BCMValidation.validateInput = function(input, forceValidation = false) {
                        // Get the result from the original function
                        const result = originalValidateInput(input, forceValidation);

                        // If the input is invalid, restore the custom message
                        if (!result) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        }

                        return result;
                    };

                    // Override the validateEmail function similarly
                    const originalValidateEmail = window.BCMValidation.validateEmail;
                    window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                        // Get the result from the original function
                        const result = originalValidateEmail(input, forceValidation);

                        // If the input is invalid, restore the custom message
                        if (!result) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        }

                        return result;
                    };

                    // Override the validatePatternInput function similarly
                    const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                    window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                        // Get the result from the original function
                        const result = originalValidatePatternInput(input, forceValidation);

                        // If the input is invalid, restore the custom message
                        if (!result) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        }

                        return result;
                    };

                    // Override the validateForm function to restore all custom messages after validation
                    const originalValidateForm = window.BCMValidation.validateForm;
                    window.BCMValidation.validateForm = function(form) {
                        // Get the result from the original function
                        const result = originalValidateForm(form);

                        // Restore all custom messages for invalid inputs
                        form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        });

                        return result;
                    };

                    // Initialize the validation framework
                    window.BCMValidation.init();

                    // Add required field indicators (asterisks)
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for pattern-based inputs
                    window.BCMValidation.addFormatIndicators(form);

                    // Add user interaction validation for all required fields
                    form.querySelectorAll('input[required], select[required], textarea[required]').forEach(function(input) {
                        input.addEventListener('input', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            if (this.type === 'email') {
                                window.BCMValidation.validateEmail(this);
                            } else if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }
                        });

                        input.addEventListener('blur', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            if (this.type === 'email') {
                                window.BCMValidation.validateEmail(this);
                            } else if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }
                        });
                    });

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");

                        // Show all validation messages
                        window.BCMValidation.showAllValidationMessages(form);

                        // Validate the form
                        const isValid = window.BCMValidation.validateForm(form);
                        console.log("Form validation result:", isValid);

                        // Also validate dates
                        const datesValid = validateContractDates();
                        console.log("Date validation result:", datesValid);

                        if (!isValid || !datesValid) {
                            console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                        }
                    });
                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }

            function validateContractDates() {
                var startDateInput = $('#ContractStartDate');
                var endDateInput = $('#ContractEndDate');

                var startDateStr = startDateInput.val();
                var endDateStr = endDateInput.val();

                var today = new Date();
                today.setHours(0, 0, 0, 0); // remove time for comparison

                var isValid = true;

                // ---- Validate Last Review Date (optional field) ----
                if (startDateStr) {
                    var startDate = new Date(startDateStr);
                    if (isNaN(startDate.getTime())) {
                        startDateInput.addClass('is-invalid').removeClass('is-valid');
                        $('#ContractStartDate-feedback').text('Invalid Last Review Date').show();
                        isValid = false;
                    } else {
                        startDateInput.removeClass('is-invalid').addClass('is-valid');
                        $('#ContractStartDate-feedback').hide();
                    }
                } else {
                    // Last Review Date is optional, so clear any validation state
                    startDateInput.removeClass('is-invalid is-valid');
                    $('#ContractStartDate-feedback').hide();
                }

                // ---- Validate Next Review Date (required field) ----
                if (!endDateStr) {
                    endDateInput.addClass('is-invalid').removeClass('is-valid');
                    $('#ContractEndDate-feedback').text('Next Review Date is required').show();
                    isValid = false;
                } else {
                    var endDate = new Date(endDateStr);
                    if (isNaN(endDate.getTime())) {
                        endDateInput.addClass('is-invalid').removeClass('is-valid');
                        $('#ContractEndDate-feedback').text('Invalid Next Review Date').show();
                        isValid = false;
                    } else if (endDate < today) {
                        endDateInput.addClass('is-invalid').removeClass('is-valid');
                        $('#ContractEndDate-feedback').text('Next Review Date cannot be less than current date').show();
                        isValid = false;
                    } else if (startDateStr && !isNaN(new Date(startDateStr).getTime()) && endDate < new Date(startDateStr)) {
                        endDateInput.addClass('is-invalid').removeClass('is-valid');
                        $('#ContractEndDate-feedback').text('Next Review Date must be on or after the Last Review Date').show();
                        isValid = false;
                    } else {
                        endDateInput.removeClass('is-invalid').addClass('is-valid');
                        $('#ContractEndDate-feedback').hide();
                    }
                }
                return isValid;
            }

            });

    $('.ddlOrganization').change (function () {
        var ddlOrganizationVal = $('#ddlOrganization').val();
        var ddlUnitnVal = $('.ddlUnit').val();
        var ddlDepartmentVal = $('#ddlDepartment').val();
        var TextSearch = $('#search-inp').text();
        var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
            if (ddlOrganizationVal)
            {
                $.ajax({
                            url: '@Url.Action("GetAllUnits", "AuditPolicy")',
                            type: 'GET',
                            data: { iOrgID: ddlOrganizationVal },
                            success: function (data) {
                                debugger;
                                var ddlUnit = $('.ddlUnit');
                                ddlUnit.empty();
                                ddlUnit.append('<option value="0">-- All Units --</option>');


                                $.each(data, function (index, item) {
                                    ddlUnit.append('<option value="' + item.unitID + '">' + item.unitName + '</option>')

                                });
                            },
                            error:function(Error){
                                console.log(Error,"Error Block");
                            }

                })
            }
    });

    $('.ddlUnit').change (function () {
                            var ddlUnitnV = $('.ddlUnit').val();
                            var ddlUnitnVal = $('.ddlUnit').val();
                            var ddlOrganizationVal = $('#ddlOrganization').val();
                             var ddlDepartmentVal = $('.ddlDepartment').val();
                            var TextSearch = $('#search-inp').text();
                            var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                             debugger;
                        if (ddlUnitnV)
                        {
                            debugger;
                            $.ajax({
                                 url: '@Url.Action("GetDepartmentsByUnitID", "AuditPolicy")',
                                 type: 'GET',
                                 data: {iOrgID:ddlOrganizationVal, iUnitID: ddlUnitnVal },
                                 success: function (data) {
                                     var ddlDepartment = $('.ddlDepartment');
                                     ddlDepartment.empty();
                                     ddlDepartment.append('<option value="0">-- All Departments --</option>');
                                     $.each(data, function (index, item) {
                                         ddlDepartment.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                                     });
                                 }
                            })
                        }


            });

            $('.ddlDepartment').change(function ()
                {
                     var ddlUnitnV = $('#ddlUnit').val();
                     var ddlUnitnVal = $('#ddlUnit').val();
                     var ddlOrganizationVal = $('#ddlOrganization').val();
                     var ddlDepartmentVal = $('#ddlDepartment').val();
                     var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                     var TextSearch = $('#search-inp').text();

                    var iUnitID = $(this).val();
                    if (iUnitID)
                    {
                        $.ajax({
                                    url: '@Url.Action("GetAllSubDepartments", "AuditPolicy")',
                                    type: 'GET',
                                    data: {iOrgID : ddlOrganizationVal,iDepartmentID: iUnitID },
                                    success: function (data) {
                                        var ddlSubDepartment = $('.ddlSubDepartment');
                                        ddlSubDepartment.empty();
                                        ddlSubDepartment.append('<option value="0">-- All SubDepartments --</option>');
                                        $.each(data, function (index, item) {
                                            console.log(item);
                                            ddlSubDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>')
                                        });
                                    }
                        })
                    }
                   
            })

</script>

