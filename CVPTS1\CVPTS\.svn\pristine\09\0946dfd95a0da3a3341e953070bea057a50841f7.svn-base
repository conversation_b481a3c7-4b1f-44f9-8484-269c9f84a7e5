﻿@using BCM.Shared
@model IEnumerable<BCM.BusinessClasses.MenuRights>
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int Index = 1;
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Page Master</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input type="text" id="search-inp" class="form-control" placeholder="Search">
        </div>
        <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="NormalModal"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant card border-0 pe-2" style="height: calc(100vh - 170px);overflow: auto;" id="FacilityList">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Page</th>
                <th>Page Area Name</th>
                <th>Page Controller Name</th>
                <th>Page Action Name</th>
                <th>Privilages</th>
                <th>Sequence  </th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            @await Html.PartialAsync("_FilterPageMaster")
        </tbody>
    </table>
    
</div>
<!--Notify Configuration Modal -->
<div class="modal fade" id="NormalModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-body NormalModal">
            </div>

        </div>
    </div>
</div>
<!--End Notify Configuration Modal -->
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#btnCreate').click(function () {
                //$.get('/BCMMenuAccess/PageMaster/AddPageMaster', function (data) {
                $.get('@Url.Action("AddPageMaster", "PageMaster")', function (data) {
                    $('.modal-body').html(data);
                    $('#NormalModal').modal('show');
                    $('#modelTitle').text('Page Configuration');
                });
            });

            $('.btnEdit').click(function () {
                var id = $(this).data('id');
                //$.get('/BCMMenuAccess/PageMaster/EditPageMaster/' + id, function (data) {
                $.get('@Url.Action("EditPageMaster", "PageMaster")' ,{id:id}, function (data) {
                    $('.NormalModal').html(data);
                    $('#NormalModal').modal('show');
                    $('#modelTitle').text('Update Page Configuration');
                });
            });

            $('.btnDelete').click(function () {
                var id = $(this).data('id');
                //$.get('/BCMMenuAccess/PageMaster/DeletePageMaster/' + id, function (data) {
                $.get('@Url.Action("DeletePageMaster", "PageMaster")' ,{id:id}, function (data) {
                    $('.modal-body').html(data);
                    $('#DeleteModal').modal('show');
                    $('#modelTitle').text('Delete Page Configuration');
                });
            });


            $('#search-inp').keypress(function(){
                 debugger;
                 var TextSearch = $(this).val();
                 $.get('@Url.Action("GetSearchPages", "PageMaster")', {textSearch : TextSearch}, function (data) {
                         var tableData = $('#FacilityList');
                            var tableData = $('#FacilityList');
                            tableData.empty();
                            $('#FacilityList').html(data);
                    });
             });
        });
    </script>

    
}
<!-- End Delete Modal -->