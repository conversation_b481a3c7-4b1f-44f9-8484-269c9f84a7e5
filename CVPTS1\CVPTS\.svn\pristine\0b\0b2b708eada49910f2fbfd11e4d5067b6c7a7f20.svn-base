﻿
.testing {
    height: calc(100vh - 394px);
    overflow: auto;
}

.tree-views {
    padding: 5px;
}

    .tree-views:not(:empty):before,
    .tree-views:not(:empty):after,
    .tree-views ul:not(:empty):before,
    .tree-views ul:not(:empty):after,
    .tree-views li:not(:empty):before,
    .tree-views li:not(:empty):after {
        display: block;
        position: absolute;
        content: "";
    }

    .tree-views ul,
    .tree-views li {
        position: relative;
        margin: 0;
        padding: 0;
    }

    .tree-views li {
        list-style: none;
    }

        .tree-views li > div {
            background-color: #009d54;
            color: #fff;
            padding: 10px;
            border-radius: 10px;
            display: inline-block;
            text-align: center;
        }

            .tree-views li > div > p {
                margin-bottom: 5px;
            }

                .tree-views li > div > p:first-child {
                    font-weight: 600;
                }

    .tree-views.cascade li {
        margin-left: 24px;
    }

        .tree-views.cascade li div {
            margin-top: 12px;
        }

        .tree-views.cascade li:before {
            border-left: 1px solid #ddd;
            height: 100%;
            width: 0;
            top: 0;
            left: -12px;
        }

        .tree-views.cascade li:after {
            border-top: 1px solid #ddd;
            width: 12px;
            left: -12px;
            top: 24px;
        }

        .tree-views.cascade li:last-child:before {
            height: 24px;
            top: 0;
        }

    .tree-views.cascade > li:first-child:before {
        top: 24px;
    }

    .tree-views.cascade > li:only-child {
        margin-left: 0;
    }

        .tree-views.cascade > li:only-child:before,
        .tree-views.cascade > li:only-child:after {
            content: none;
        }

    .tree-views.horizontal li {
        display: flex;
        align-items: center;
        margin-left: 24px;
    }

        .tree-views.horizontal li div {
            margin: 6px 0;
        }

        .tree-views.horizontal li:before {
            border-left: 1px solid #ddd;
            height: 100%;
            width: 0;
            top: 0;
            left: -12px;
        }

        .tree-views.horizontal li:first-child:before {
            height: 50%;
            top: 50%;
        }

        .tree-views.horizontal li:last-child:before {
            height: 50%;
            bottom: 50%;
            top: auto;
        }

        .tree-views.horizontal li:after,
        .tree-views.horizontal li ul:after {
            border-top: 1px solid #ddd;
            height: 0;
            width: 12px;
            top: 50%;
            left: -12px;
        }

        .tree-views.horizontal li:only-child:before {
            content: none;
        }

        .tree-views.horizontal li ul:after {
            left: 0;
        }

    .tree-views.horizontal > li:only-child {
        margin-left: 0;
    }

        .tree-views.horizontal > li:only-child:before,
        .tree-views.horizontal > li:only-child:after {
            content: none;
        }

    .tree-views.vertical {
        display: flex;
    }

        .tree-views.vertical ul {
            display: flex;
            justify-content: center;
        }

        .tree-views.vertical li {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

            .tree-views.vertical li div {
                margin: 12px 6px;
            }

            .tree-views.vertical li:before {
                border-left: 1px solid #ddd;
                height: 12px;
                width: 0;
                top: 0;
            }

            .tree-views.vertical li:after {
                border-top: 1px solid #ddd;
                height: 0;
                width: 100%;
            }

            .tree-views.vertical li:first-child:after {
                border-top: 1px solid #ddd;
                height: 0;
                width: 50%;
                left: 50%;
            }

            .tree-views.vertical li:last-child:after {
                border-top: 1px solid #ddd;
                height: 0;
                width: 50%;
                right: 50%;
            }

            .tree-views.vertical li:only-child:after {
                content: none;
            }

            .tree-views.vertical li ul:before {
                border-left: 1px solid #ddd;
                height: 12px;
                width: 0;
                top: -12px;
            }

        .tree-views.vertical > li:only-child:before,
        .tree-views.vertical > li:only-child:after {
            content: none;
        }

        .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li,
        .tree-views.vertical.cascade-3 > li > ul > li > ul > li,
        .tree-views.vertical.cascade-2 > li > ul > li,
        .tree-views.vertical.cascade-1 > li,
        .tree-views.vertical .cascade {
            flex-direction: column;
            align-items: start;
            padding: 0 12px;
        }

            .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li:before,
            .tree-views.vertical.cascade-3 > li > ul > li > ul > li:before,
            .tree-views.vertical.cascade-2 > li > ul > li:before,
            .tree-views.vertical.cascade-1 > li:before,
            .tree-views.vertical .cascade:before {
                left: 24px;
            }

            .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li:after,
            .tree-views.vertical.cascade-3 > li > ul > li > ul > li:after,
            .tree-views.vertical.cascade-2 > li > ul > li:after,
            .tree-views.vertical.cascade-1 > li:after,
            .tree-views.vertical .cascade:after {
                left: 0;
            }

            .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li:first-child:after,
            .tree-views.vertical.cascade-3 > li > ul > li > ul > li:first-child:after,
            .tree-views.vertical.cascade-2 > li > ul > li:first-child:after,
            .tree-views.vertical.cascade-1 > li:first-child:after,
            .tree-views.vertical .cascade:first-child:after {
                left: 24px;
                width: 100%;
            }

            .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li:last-child:after,
            .tree-views.vertical.cascade-3 > li > ul > li > ul > li:last-child:after,
            .tree-views.vertical.cascade-2 > li > ul > li:last-child:after,
            .tree-views.vertical.cascade-1 > li:last-child:after,
            .tree-views.vertical .cascade:last-child:after {
                left: 0;
                width: 24px;
            }

            .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li ul,
            .tree-views.vertical.cascade-3 > li > ul > li > ul > li ul,
            .tree-views.vertical.cascade-2 > li > ul > li ul,
            .tree-views.vertical.cascade-1 > li ul,
            .tree-views.vertical .cascade ul,
            .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li li,
            .tree-views.vertical.cascade-3 > li > ul > li > ul > li li,
            .tree-views.vertical.cascade-2 > li > ul > li li,
            .tree-views.vertical.cascade-1 > li li,
            .tree-views.vertical .cascade li {
                display: block;
            }

                .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li ul:before,
                .tree-views.vertical.cascade-3 > li > ul > li > ul > li ul:before,
                .tree-views.vertical.cascade-2 > li > ul > li ul:before,
                .tree-views.vertical.cascade-1 > li ul:before,
                .tree-views.vertical .cascade ul:before,
                .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li ul:after,
                .tree-views.vertical.cascade-3 > li > ul > li > ul > li ul:after,
                .tree-views.vertical.cascade-2 > li > ul > li ul:after,
                .tree-views.vertical.cascade-1 > li ul:after,
                .tree-views.vertical .cascade ul:after,
                .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li li:before,
                .tree-views.vertical.cascade-3 > li > ul > li > ul > li li:before,
                .tree-views.vertical.cascade-2 > li > ul > li li:before,
                .tree-views.vertical.cascade-1 > li li:before,
                .tree-views.vertical .cascade li:before,
                .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li li:after,
                .tree-views.vertical.cascade-3 > li > ul > li > ul > li li:after,
                .tree-views.vertical.cascade-2 > li > ul > li li:after,
                .tree-views.vertical.cascade-1 > li li:after,
                .tree-views.vertical .cascade li:after {
                    border: none;
                }

            .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li div,
            .tree-views.vertical.cascade-3 > li > ul > li > ul > li div,
            .tree-views.vertical.cascade-2 > li > ul > li div,
            .tree-views.vertical.cascade-1 > li div,
            .tree-views.vertical .cascade div {
                margin: 0;
                margin-top: 12px;
            }

            .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li li,
            .tree-views.vertical.cascade-3 > li > ul > li > ul > li li,
            .tree-views.vertical.cascade-2 > li > ul > li li,
            .tree-views.vertical.cascade-1 > li li,
            .tree-views.vertical .cascade li {
                margin-left: 24px;
            }

                .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li li:before,
                .tree-views.vertical.cascade-3 > li > ul > li > ul > li li:before,
                .tree-views.vertical.cascade-2 > li > ul > li li:before,
                .tree-views.vertical.cascade-1 > li li:before,
                .tree-views.vertical .cascade li:before {
                    border-left: 1px solid #ddd;
                    height: 100%;
                    width: 0;
                    top: 0;
                    left: -12px;
                }

                .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li li:after,
                .tree-views.vertical.cascade-3 > li > ul > li > ul > li li:after,
                .tree-views.vertical.cascade-2 > li > ul > li li:after,
                .tree-views.vertical.cascade-1 > li li:after,
                .tree-views.vertical .cascade li:after {
                    border-top: 1px solid #ddd;
                    width: 12px;
                    height: 0;
                    left: -12px;
                    top: 24px;
                    content: "";
                }

                .tree-views.vertical.cascade-4 > li > ul > li > ul > li > ul > li
                li:last-child:before,
                .tree-views.vertical.cascade-3 > li > ul > li > ul > li li:last-child:before,
                .tree-views.vertical.cascade-2 > li > ul > li li:last-child:before,
                .tree-views.vertical.cascade-1 > li li:last-child:before,
                .tree-views.vertical .cascade li:last-child:before {
                    height: 24px;
                    top: 0;
                }

.testing {
    display: flex;
    flex-wrap: wrap;
    font-family: Arial;
    justify-content: center;
}

    .testing > div {
        flex: 1 0 0;
        margin: 1rem;
    }

.tree-views.vertical li {
    /* flex: 1 0 100%; */
}


