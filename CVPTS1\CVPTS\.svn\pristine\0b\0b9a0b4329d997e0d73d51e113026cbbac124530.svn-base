﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="~/css/orgchart.css" rel="stylesheet" />
<div class="Page-Header">
    <h6 class="Page-Title">Entity Impact Analysis</h6>
</div>
<div class="Page-Condant">
    <div class="card mb-3">
        <div class="card-body">
            <div class="row">
                <div class="col-12 col-lg-6">
                    <div class="d-flex gap-3">
                        <div class="w-50">
                            <div class="form-group">
                                <label class="form-label">Incident</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-incident"></i></span>
                                    <select class="form-select form-select-sm">
                                        <option value="value">Select Incident</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Entity</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-entity"></i></span>
                                    <select class="form-select form-select-sm">
                                        <option value="value">Select Entity</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="w-50">
                            <div class="form-group">
                                <label class="form-label">Entity Type</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-entity-type"></i></span>
                                    <select class="form-select form-select-sm">
                                        <option value="value">Select Entity Type</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Incident Time</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-incident-time"></i></span>
                                    <input class="form-control" type="date" placeholder="Select Incident Time" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-end"><button class="btn btn-sm btn-primary" type="submit">Simulate</button></div>
                </div>
                <div class="col-12 col-lg-6">
                    <div class="row g-0 align-items-center">
                        <div class="col-8">
                            <div id="FinancialImpactCost_Chart" style="height:180px"></div>
                        </div>
                        <div class="col-4">
                            <div class="card border ">
                                <div class="card-header border-bottom-0 fw-semibold px-2 d-flex align-items-center">
                                    <span class="bg-white shadow-sm icon-circle me-2">
                                        <i class="cv-dollar align-middle text-primary fs-5"></i>
                                    </span>
                                    <span>Financial Impact Cost</span>
                                </div>
                                <div class="card-body d-flex align-items-center pt-0">
                                    <div class="fs-4 fw-bold me-2">60</div><div>US $ in Thousands</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header header border-0">
            <form class="d-flex tab-design">
                <ul class="nav nav-tabs gap-2" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="Organization-tab" data-bs-toggle="tab" data-bs-target="#Organization-tab-pane" type="button" role="tab" aria-controls="Organization-tab-pane" aria-selected="true">
                            Organization Impact
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="Impacted-tab" data-bs-toggle="tab" data-bs-target="#Impacted-tab-pane" type="button" role="tab" aria-controls="Impacted-tab-pane" aria-selected="false">
                            Impacted Processes
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="Additional-tab" data-bs-toggle="tab" data-bs-target="#Additional-tab-pane" type="button" role="tab" aria-controls="Additional-tab-pane" aria-selected="false">
                            Additional Details
                        </button>
                    </li>
                </ul>
            </form>
        </div>
        <div class="card-body pt-0">
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade  show active" id="Organization-tab-pane" role="tabpanel" aria-labelledby="Organization-tab" tabindex="0">
                    <div class="testing">

                        <ul class="tree-views vertical">
                            <li>
                                <div>
                                    <p>Perpetuuiti</p>
                                    <p>Entity : Organization</p>
                                    <p>Weightage : 100%</p>
                                    <p>Impact : 100%</p>
                                </div>
                                <ul>
                             
                                      <li>
                                        <div>
                                            <p>Corporate Affairs</p>
                                            <p>Entity : Business Unit</p>
                                            <p>Weightage : 50%</p>
                                        </div>
                                        <ul>
                                            <li>
                                                <div>
                                                    <p>Department</p>
                                                    <p>Entity : Business Department</p>
                                                    <p>Weightage : 0%</p>
                                                </div>
                                            </li>
                                            <li>
                                                <div>
                                                    <p>Opration and Logistics Department</p>
                                                    <p>Entity : Business Department</p>
                                                    <p>Weightage : 0%</p>
                                                </div>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <div>
                                            <p>Corporate Affairs</p>
                                            <p>Entity : Business Unit</p>
                                            <p>Weightage : 50%</p>
                                        </div>
                                        <ul>
                                            <li>
                                                <div>
                                                    <p>Department</p>
                                                    <p>Entity : Business Department</p>
                                                    <p>Weightage : 0%</p>
                                                </div>
                                            </li>
                                            <li>
                                                <div>
                                                    <p>Opration and Logistics Department</p>
                                                    <p>Entity : Business Department</p>
                                                    <p>Weightage : 0%</p>
                                                </div>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <div>
                                            <p>Corporate Affairs</p>
                                            <p>Entity : Business Unit</p>
                                            <p>Weightage : 50%</p>
                                        </div>
                                        <ul>
                                            <li>
                                                <div>
                                                    <p>Department</p>
                                                    <p>Entity : Business Department</p>
                                                    <p>Weightage : 0%</p>
                                                </div>
                                            </li>
                                            <li>
                                                <div>
                                                    <p>Opration and Logistics Department</p>
                                                    <p>Entity : Business Department</p>
                                                    <p>Weightage : 0%</p>
                                                </div>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="tab-pane fade" id="Impacted-tab-pane" role="tabpanel" aria-labelledby="Impacted-tab" tabindex="0">
                    <h6>Security Operations</h6>
                    <div class="row g-3">
                        <div class="col">
                            <div id="ImpactedProcesses_Chart" style="width:100%;"></div>
                            <div class="text-center"><button class="btn btn-sm btn-primary" type="submit">Calculate</button></div>
                        </div>
                        <div class="col-4">
                            <div class="row row-cols-1 row-cols-md-2 g-3 h-100">
                                <div class="col">
                                    <div class="card border rounded-2 h-100">
                                        <div class="card-body d-flex align-items-center p-2">
                                            <span class="bg-white shadow-sm icon-circle me-2 rounded-2">
                                                <i class="cv-dollar align-middle text-primary fs-5"></i>
                                            </span>
                                            <div>
                                                <span>No. Of Transaction</span>
                                                <h6 class="mb-0">1500<small class="mx-2 text-success"><i class="cv-success fw-semibold"></i> 1497</small><small class="text-warning"><i class="cv-waiting fw-semibold"></i>3</small></h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card border rounded-2 h-100">
                                        <div class="card-body d-flex align-items-center p-2">
                                            <span class="bg-white shadow-sm icon-circle me-2 rounded-2">
                                                <i class="cv-dollar align-middle text-primary fs-5"></i>
                                            </span>
                                            <div>
                                                <span>Cost (In $)</span>
                                                <h6 class="mb-0">30</h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card border rounded-2 h-100">
                                        <div class="card-body d-flex align-items-center p-2">
                                            <span class="bg-white shadow-sm icon-circle me-2 rounded-2">
                                                <i class="cv-clock align-middle text-primary fs-5"></i>
                                            </span>
                                            <div>
                                                <span>Time (hrs)</span>
                                                <h6 class="mb-0">3:00</h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card border rounded-2 h-100">
                                        <div class="card-body d-flex align-items-center p-2">
                                            <span class="bg-white shadow-sm icon-circle me-2 rounded-2">
                                                <i class="cv-resources align-middle text-primary fs-5"></i>
                                            </span>
                                            <div>
                                                <span>Resources</span>
                                                <h6 class="mb-0">150</h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-5">
                            <div class="border rounded-2">
                                <table class="table mb-0 table-borderless">
                                    <thead class="border-bottom">
                                        <tr>
                                            <th>From</th>
                                            <th>To</th>
                                            <th>Resources</th>
                                            <th>Site</th>
                                            <th>Transcations</th>
                                            <th>IsActive</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="rounded-2">11:00:00</td>
                                            <td>21:00:00</td>
                                            <td>150</td>
                                            <td>Panchshil Techpark</td>
                                            <td>1500</td>
                                            <td class="rounded-2">Yes</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="border rounded-2 mt-3">
                        <table class="table mb-0 table-borderless">
                            <thead class="border-bottom">
                                <tr>
                                    <th>S.No</th>
                                    <th>Select</th>
                                    <th>Parameter Name</th>
                                    <th>Value</th>
                                    <th>Cost</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="rounded-2">01</td>
                                    <td>
                                        <input type="checkbox" class="form-check" />
                                    </td>
                                    <td>Move resource to other EGS location ( 0 )</td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td class="rounded-2">
                                        <div class="d-flex gap-2">
                                            <div class="input-group">
                                                <input class="form-control" placeholder="03" />
                                            </div>
                                            <div class="input-group">
                                                <select class="form-select">
                                                    <option>Mins</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="rounded-2">02</td>
                                    <td>
                                        <input type="checkbox" class="form-check" />
                                    </td>
                                    <td>Move resource to other location ( 0 )</td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td class="rounded-2">
                                        <div class="d-flex gap-2">
                                            <div class="input-group">
                                                <input class="form-control" placeholder="03" />
                                            </div>
                                            <div class="input-group">
                                                <select class="form-select">
                                                    <option>Mins</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="rounded-2">03</td>
                                    <td>
                                        <input type="checkbox" class="form-check" />
                                    </td>
                                    <td>People WFH ( 0 )</td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td class="rounded-2">
                                        <div class="d-flex gap-2">
                                            <div class="input-group">
                                                <input class="form-control" placeholder="03" />
                                            </div>
                                            <div class="input-group">
                                                <select class="form-select">
                                                    <option>Mins</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="rounded-2">04</td>
                                    <td>
                                        <input type="checkbox" class="form-check" />
                                    </td>
                                    <td>Increasing shift time at DR Site ( 0 )</td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td class="rounded-2">
                                        <div class="d-flex gap-2">
                                            <div class="input-group">
                                                <input class="form-control" placeholder="03" />
                                            </div>
                                            <div class="input-group">
                                                <select class="form-select">
                                                    <option>Mins</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="rounded-2">05</td>
                                    <td>
                                        <input type="checkbox" class="form-check" />
                                    </td>
                                    <td>Move resource to DR ( 0 )</td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input class="form-control" placeholder="03" />
                                        </div>
                                    </td>
                                    <td class="rounded-2">
                                        <div class="d-flex gap-2">
                                            <div class="input-group">
                                                <input class="form-control" placeholder="03" />
                                            </div>
                                            <div class="input-group">
                                                <select class="form-select">
                                                    <option>Mins</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="tab-pane fade" id="Additional-tab-pane" role="tabpanel" aria-labelledby="Additional-tab" tabindex="0">
                    <div>
                        <div class="row g-3">
                            <div class="col-md-8 col-lg-9">
                                <div class="row g-3">
                                    <div class="col-md-6 col-lg-6">
                                        <div class="card border-secondary-subtle border">
                                            <div class="card-header">

                                                <h6 class="page_title d-flex align-items-center mb-0">
                                                    <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                        <i class="cv-business-process align-middle text-primary fs-5"></i>
                                                    </span><span>
                                                        Impacted Process Details
                                                    </span>
                                                </h6>
                                            </div>
                                            <div class="card-body" style="height:calc(50vh - 3px);overflow:auto">
                                                <div >
                                                    <div class="tree-menu">
                                                        <ul class="tree">

                                                            <li>
                                                                <span role="button">Perpetuuiti( Weightage : 100 % )</span>
                                                                <ul>
                                                                    <li>
                                                                        <span role="button">Admin group( Weightage : 0 % )</span>
                                                                        <ul class="sub-parent">
                                                                            <li>
                                                                                <span role="button">Corporate affairs ( Weightage : 50 % )</span>
                                                                            </li>
                                                                            <li>
                                                                                <span>Department( Weightage : 0 % )</span>
                                                                                <ul class="sub-parent">
                                                                                    <li>
                                                                                        <span>Operation and Logistics Department ( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                    <li>
                                                                                        <span>Logistic SubDepartment( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                </ul>
                                                                            </li>

                                                                        </ul>
                                                                    </li>
                                                                    <li>
                                                                        <span role="button">Finance affairs( Weightage : 0 % )</span>
                                                                        <ul class="sub-parent">
                                                                            <li>
                                                                                <span role="button">Corporate affairs ( Weightage : 50 % )</span>
                                                                            </li>
                                                                            <li>
                                                                                <span>Department( Weightage : 0 % )</span>
                                                                                <ul class="sub-parent">
                                                                                    <li>
                                                                                        <span>Operation and Logistics Department ( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                    <li>
                                                                                        <span>Logistic SubDepartment( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                </ul>
                                                                            </li>

                                                                        </ul>
                                                                    </li>
                                                                    <li>
                                                                        <span role="button">Admin group( Weightage : 0 % )</span>
                                                                        <ul class="sub-parent">
                                                                            <li>
                                                                                <span role="button">Corporate affairs ( Weightage : 50 % )</span>
                                                                            </li>
                                                                            <li>
                                                                                <span>Department( Weightage : 0 % )</span>
                                                                                <ul class="sub-parent">
                                                                                    <li>
                                                                                        <span>Operation and Logistics Department ( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                    <li>
                                                                                        <span>Logistic SubDepartment( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                </ul>
                                                                            </li>

                                                                        </ul>
                                                                    </li>
                                                                </ul>

                                                            </li>

                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-6">
                                        <div class="card border-secondary-subtle border">
                                            <div class="card-header">

                                                <h6 class="page_title d-flex align-items-center mb-0">
                                                    <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                        <i class="cv-impact_detail align-middle text-primary fs-5"></i>
                                                    </span><span>
                                                        Impacted Entity Details
                                                    </span>
                                                </h6>
                                            </div>
                                            <div class="card-body" style="height:calc(50vh - 3px);overflow:auto">
                                                <div>
                                                    <div class="tree-menu">
                                                        <ul class="tree">

                                                            <li>
                                                                <span role="button">Perpetuuiti( Weightage : 100 % )</span>
                                                                <ul>
                                                                    <li>
                                                                        <span role="button">Admin group( Weightage : 0 % )</span>
                                                                        <ul class="sub-parent">
                                                                            <li>
                                                                                <span role="button">Corporate affairs ( Weightage : 50 % )</span>
                                                                            </li>
                                                                            <li>
                                                                                <span>Department( Weightage : 0 % )</span>
                                                                                <ul class="sub-parent">
                                                                                    <li>
                                                                                        <span>Operation and Logistics Department ( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                    <li>
                                                                                        <span>Logistic SubDepartment( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                </ul>
                                                                            </li>

                                                                        </ul>
                                                                    </li>
                                                                    <li>
                                                                        <span role="button">Finance affairs( Weightage : 0 % )</span>
                                                                        <ul class="sub-parent">
                                                                            <li>
                                                                                <span role="button">Corporate affairs ( Weightage : 50 % )</span>
                                                                            </li>
                                                                            <li>
                                                                                <span>Department( Weightage : 0 % )</span>
                                                                                <ul class="sub-parent">
                                                                                    <li>
                                                                                        <span>Operation and Logistics Department ( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                    <li>
                                                                                        <span>Logistic SubDepartment( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                </ul>
                                                                            </li>

                                                                        </ul>
                                                                    </li>
                                                                    <li>
                                                                        <span role="button">Admin group( Weightage : 0 % )</span>
                                                                        <ul class="sub-parent">
                                                                            <li>
                                                                                <span role="button">Corporate affairs ( Weightage : 50 % )</span>
                                                                            </li>
                                                                            <li>
                                                                                <span>Department( Weightage : 0 % )</span>
                                                                                <ul class="sub-parent">
                                                                                    <li>
                                                                                        <span>Operation and Logistics Department ( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                    <li>
                                                                                        <span>Logistic SubDepartment( Weightage : 0 % )</span>
                                                                                    </li>
                                                                                </ul>
                                                                            </li>

                                                                        </ul>
                                                                    </li>
                                                                </ul>

                                                            </li>

                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12 col-lg-12">
                                        <div class="card border-secondary-subtle border">
                                            <div class="card-header">
                                                <h6 class="page_title d-flex align-items-center mb-0">
                                                    <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                        <i class="cv-activity align-middle text-primary fs-5"></i>
                                                    </span><span>
                                                        Impacted Activities
                                                    </span>
                                                </h6>
                                            </div>
                                            <div class="card-body" style="height:calc(50vh - 215px);overflow:auto">

                                                <div >
                                                    <table class="table table-hover mb-0">
                                                        <thead>
                                                            <tr>
                                                                <th>S.No</th>
                                                                <th>Activity</th>
                                                                <th>RTO</th>
                                                                <th>RPO</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="4">
                                                                    <div class="text-center">
                                                                        <img src="/img/Isomatric/no_records_to_display.svg" class="img-fluid" width="150" />
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-3 col-lg-3">
                                <div class="card border-secondary-subtle border mb-3">
                                    <div class="card-header">
                                        <h6 class="page_title d-flex align-items-center mb-0">
                                            <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                <i class="cv-entity align-middle text-primary fs-5"></i>
                                            </span><span>
                                                Impacted Entities
                                            </span>
                                        </h6>
                                    </div>
                                    <div class="card-body px-0" style="height:calc(50vh - 3px);overflow:auto">
                                        <div >
                                            <ul class="list-group list-group-flush ">
                                                <li class="list-group-item bg-white d-flex justify-content-between align-items-center">
                                                    <span class="d-flex fw-semibold">
                                                        <i class="cv-audit me-1 fs-5"></i>Business Processes
                                                    </span>
                                                    <span class="fs-5">14</span>
                                                </li>
                                                <li class="list-group-item bg-white d-flex justify-content-between align-items-center">
                                                    <span class="d-flex fw-semibold">
                                                        <i class="cv-audit me-1 fs-5"></i>Activities
                                                    </span>
                                                    <span class="fs-5">14</span>
                                                </li>
                                                <li class="list-group-item bg-white d-flex justify-content-between align-items-center">
                                                    <span class="d-flex fw-semibold">
                                                        <i class="cv-audit me-1 fs-5"></i>People
                                                    </span>
                                                    <span class="fs-5">15</span>
                                                </li>
                                                <li class="list-group-item bg-white d-flex justify-content-between align-items-center">
                                                    <span class="d-flex fw-semibold">
                                                        <i class="cv-audit me-1 fs-5"></i>Applications
                                                    </span>
                                                    <span class="fs-5">2</span>
                                                </li>
                                                <li class="list-group-item bg-white d-flex justify-content-between align-items-center">
                                                    <span class="d-flex fw-semibold">
                                                        <i class="cv-audit me-1 fs-5"></i>Third Parties
                                                    </span>
                                                    <span class="fs-5">0</span>
                                                </li>
                                                <li class="list-group-item bg-white d-flex justify-content-between align-items-center">
                                                    <span class="d-flex fw-semibold">
                                                        <i class="cv-audit me-1 fs-5"></i>Facilities
                                                    </span>
                                                    <span class="fs-5">0</span>
                                                </li>
                                                <li class="list-group-item bg-white d-flex justify-content-between align-items-center">
                                                    <span class="d-flex fw-semibold">
                                                        <i class="cv-audit me-1 fs-5"></i>Vital Records
                                                    </span>
                                                    <span class="fs-5">0</span>
                                                </li>
                                                <li class="list-group-item bg-white d-flex justify-content-between align-items-center">
                                                    <span class="d-flex fw-semibold">
                                                        <i class="cv-audit me-1 fs-5"></i>Dependent Services
                                                    </span>
                                                    <span class="fs-5">0</span>
                                                </li>
                                                <li class="list-group-item bg-white d-flex justify-content-between align-items-center">
                                                    <span class="d-flex fw-semibold">
                                                        <i class="cv-audit me-1 fs-5"></i>Dependent Entities
                                                    </span>
                                                    <span class="fs-5">0</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="card border-secondary-subtle border">
                                    <div class="card-header">
                                        <h6 class="page_title d-flex align-items-center mb-0">
                                            <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                <i class="cv-team-size align-middle text-primary fs-5"></i>
                                            </span><span>
                                                Impacted People
                                            </span>
                                        </h6>
                                    </div>
                                    <div class="card-body px-0" style="height:calc(50vh - 212px);overflow:auto">
                                        <div >
                                            <table class="table table-hover mb-0">
                                                <thead>
                                                    <tr>
                                                        <th>S.No</th>
                                                        <th>Resource</th>
                                                        <th>Resource</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>01</td>
                                                        <td>
                                                            Neeraj Sahu
                                                        </td>
                                                        <td>9021698184</td>
                                                    </tr>
                                                    <tr>
                                                        <td>02</td>
                                                        <td>
                                                            Arun Patil

                                                        </td>
                                                        <td>9988337711</td>
                                                    </tr>
                                                    <tr>
                                                        <td>03</td>
                                                        <td>
                                                            Avinash Pathaak
                                                        </td>
                                                        <td>9988337711</td>
                                                    </tr>
                                                    <tr>
                                                        <td>04</td>
                                                        <td>
                                                            Anwar Chalamannil
                                                        </td>
                                                        <td>9988337711</td>
                                                    </tr>
                                                    <tr>
                                                        <td>05</td>
                                                        <td>
                                                            Kallirroi Skolarikou
                                                        </td>
                                                        <td>9988337711</td>
                                                    </tr>
                                                    <tr>
                                                        <td>06</td>
                                                        <td>
                                                            Anil Prakshale
                                                        </td>
                                                        <td>9021693184</td>
                                                    </tr>
                                                    <tr>
                                                        <td>07</td>
                                                        <td>
                                                            Kishan Kadam

                                                        </td>
                                                        <td>9021693184</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 ">
                                <div class="card border-secondary-subtle border">
                                    <div class="card-header">
                                        <h6 class="page_title d-flex align-items-center mb-0">
                                            <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                <i class="cv-third-party align-middle text-primary fs-5"></i>
                                            </span><span>
                                                Impacted Third Parties
                                            </span>
                                        </h6>
                                    </div>
                                    <div class="card-body px-0">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>S.No</th>
                                                    <th>
                                                        Thirty Party Name
                                                    </th>
                                                    <th>SPOC Name</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="3" class="text-center">No records to display</td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 ">
                                <div class="card border-secondary-subtle border">
                                    <div class="card-header">
                                        <h6 class="page_title d-flex align-items-center mb-0">
                                            <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                <i class="cv-records align-middle text-primary fs-5"></i>
                                            </span><span>
                                                Impacted Vital Records
                                            </span>
                                        </h6>
                                    </div>
                                    <div class="card-body px-0">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>S.No</th>
                                                    <th>
                                                        Record Name
                                                    </th>
                                                    <th>Owner Name</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>01</td>
                                                    <td>A</td>
                                                    <td>Arun Pati</td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 ">
                                <div class="card border-secondary-subtle border">
                                    <div class="card-header">
                                        <h6 class="page_title d-flex align-items-center mb-0">
                                            <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                <i class="cv-all-facilities align-middle text-primary fs-5"></i>
                                            </span><span>
                                                Impacted Facilities
                                            </span>
                                        </h6>
                                    </div>
                                    <div class="card-body px-0">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>S.No</th>
                                                    <th>
                                                        Facility Name
                                                    </th>
                                                    <th>Manager Name</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>01</td>
                                                    <td>Panchshil Techpark</td>
                                                    <td>Neeraj Sahu</td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 ">
                                <div class="card border-secondary-subtle border">
                                    <div class="card-header">
                                        <h6 class="page_title d-flex align-items-center mb-0">
                                            <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                <i class="cv-application align-middle text-primary fs-5"></i>
                                            </span><span>
                                                Impacted Applications
                                            </span>
                                        </h6>
                                    </div>
                                    <div class="card-body px-0">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>S.No</th>
                                                    <th>
                                                        Application Name
                                                    </th>
                                                    <th>Owner Name</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="3" class="text-center">No records to display</td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 ">
                                <div class="card border-secondary-subtle border">
                                    <div class="card-header">
                                        <h6 class="page_title d-flex align-items-center mb-0">
                                            <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                <i class="cv-entity align-middle text-primary fs-5"></i>
                                            </span><span>
                                                Impacted Dependent Entities
                                            </span>
                                        </h6>
                                    </div>
                                    <div class="card-body px-0">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>S.No</th>
                                                    <th>
                                                        Entity Name
                                                    </th>
                                                    <th>Owner Name</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="3" class="text-center">No records to display</td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 ">
                                <div class="card border-secondary-subtle border">
                                    <div class="card-header">
                                        <h6 class="page_title d-flex align-items-center mb-0">
                                            <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                <i class="cv-service align-middle text-primary fs-5"></i>
                                            </span><span>
                                                Impacted Dependent Services
                                            </span>
                                        </h6>
                                    </div>
                                    <div class="card-body px-0">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>S.No</th>
                                                    <th>
                                                        Process Name
                                                    </th>
                                                    <th>Owner Name</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="3" class="text-center">No records to display</td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/lib/amcharts4/core.js"></script>
    <script src="~/lib/amcharts4/charts.js"></script>
    <script src="~/lib/amcharts4/animated.js"></script>
    <script src="~/js/financialimpactcost_chart.js"></script>


