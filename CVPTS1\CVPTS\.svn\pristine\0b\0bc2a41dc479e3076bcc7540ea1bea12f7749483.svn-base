﻿@model BCM.BusinessClasses.ProcessBIAApplicationInfo
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    int Index = 0;
    var ProcessVersion = HttpContextAccessor.HttpContext.Session.GetString("ProcessVersion");
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers


<div class="Page-Condant card border-0">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        <p style="padding-left:1%" class="fw-bold mb-2">Configure IT Requirements for @ViewBag.ProcessName ( @ViewBag.ProcessCode )</p>
        <div class="align-items-right" style="padding-right:2%">
            @* <p class="fw-semibold">Version : @ViewBag.ProcessVersion</p> *@
            <p class="fw-semibold">Version : @ProcessVersion</p>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-12">
                <div class="accordion accordion-flush" id="accordionFlushExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                Instructions and Guidelines
                            </button>
                        </h2>
                        <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                            <div class="accordion-body">
                                <div class="content-editable">
                                    @Html.Raw(ViewBag.Description)
                                </div>
                                @* <textarea name="content" disabled  contenteditable="false" id="editor2">@ViewBag.Description</textarea> *@
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @await Html.PartialAsync("BIAApplicationGrid")
            @* @Html.Partial("BIAApplicationGrid"); *@

            <form asp-action="SaveUpdateApplicationBIA" method="post" id="saveUpdateApplicationBIA" class="needs-validation progressive-validation" novalidate>
                <div class="col-12">
                    <div class="row row-cols-2">
                        <div class="col">
                            @* <div class="form-group">
                                @* <label class="form-lable">Version</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-version"></i></span>
                                    <input class="form-control" type="text" readonly value="1" />
                                </div>

                            </div> *@
                            <div class="form-group">
                                <input type="hidden" id="lblID" name="ID" value="" asp-for="ID" />
                                <label class="form-lable">Questions</label>
                                <div class="form-check mt-2">
                                    @foreach (var item in ViewBag.lstQuestion)
                                    {
                                        <input class="form-check-input" asp-for="QuestionID" checked type="radio" name="inlineRadioOptions" id="inlineRadio1" value="@item.ID">
                                        <label class="form-check-label" for="inlineRadio1">@item.QuestionDetails</label>
                                    }
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-lable">Application Name</label>

                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-application"></i></span>
                                    <input type="hidden" asp-for="ApplicationName" id="ApplicationName" />
                                    <select class="form-select-sm form-control selectized" id="ddlApplication" asp-for="ApplicationID" required>
                                        <option selected disabled value="">-- Application --</option>
                                        @foreach (var item in ViewBag.lstApplication)
                                        {
                                            <option value="@item.ApplicationId">@item.ApplicationName</option>
                                        }
                                    </select>

                                </div>
                                <div class="invalid-feedback">Select Application Name</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">IT Application Recovery Time Objective</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-objectives1"></i></span>
                                    <select class="form-select-sm form-control selectized" id="ddlRTO" asp-for="RecoveryTime" required>
                                        <option selected disabled value="">-- Select RTO --</option>
                                        @foreach (var item in ViewBag.lstApplicationRTO)
                                        {
                                            <option value="@item.RTOID">@item.ApplicationRTOText</option>
                                        }
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select IT Application Recovery Time Objective</div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-lable">Key Usage</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-key"></i></span>
                                    @* <textarea class="form-control" id="txtKeyUsage" asp-for="KeyUsage" placeholder="Enter Key Usage" style="height:0px" required></textarea> *@
                                    <input type="text" id="KeyUsage" class="form-control" name="KeyUsage" asp-for="KeyUsage" placeholder="Enter Key Usage" required />
                                </div>
                                <div class="invalid-feedback">Enter Key Usage</div>
                                <div class="text-end text-secondary" id="remainingC">10000 characters left.</div>
                            </div>

                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">IT Application Data Loss</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-data-loss"></i></span>
                                    <select class="form-select-sm form-control selectized" id="ddlDataLoss" asp-for="DataLoss" required>
                                        <option selected disabled value="">-- Select DataLoss --</option>
                                        @foreach (var item in ViewBag.lstApplicationRPO)
                                        {
                                            <option value="@item.RPOID">@item.ApplicationRPOText</option>
                                        }
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select IT Application Data Loss</div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-lable">Manual Workarounds</label>
                                <input type="text" id="editor1" class="form-control" asp-for="ManualWorkarounds" placeholder="Enter Manual Workarounds" required />
                                <div class="invalid-feedback">Enter Manual Workarounds</div>
                                @*  <textarea id="editor" asp-for="ManualWorkarounds"></textarea> *@
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 mb-2 text-end">                 
                    @* <button class="btn btn-sm btn-outline-primary">Back</button> *@

                    <a class="btn btn-sm btn-outline-primary" role="button" asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>
                    @* <a role="button" class="btn btn-sm btn-primary" asp-action="ManageBusinessProcess" asp-controller="ManageBusinessProcesses" asp-area="BCMProcessBIA" formnovalidate>View All</a> *@
                    <button type="submit" id="Submit" @ViewBag.ButtonAccess.btnUpdate class="btn btn-sm btn-primary">Save</button>
                    <button class="btn btn-sm btn-secondary" id="btnCancel" formnovalidate>Cancel</button>
                    
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>

<script src="~/lib/ckeditor/js/ckeditor.js"></script>
<script>

    ClassicEditor.create(document.querySelector("#editor"));

    document.querySelector("form").addEventListener("submit", (e) => {

    });
</script>

@section Scripts {
    <script>

        $(document).ready(function () {

             // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function() {
                        console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function() {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                console.log("Initializing validation for saveUpdateApplicationBIA form");

                if (window.BCMValidation) {
                    console.log("BCMValidation found, initializing");

                    // Get the form element
                    const form = document.getElementById('saveUpdateApplicationBIA');
                    if (!form) {
                        console.error("Form not found with ID: saveUpdateApplicationBIA");
                        return;
                    }

                    // Store the original content of all invalid-feedback divs with multiple key strategies
                    const customMessages = {};
                    form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                        // Find the associated input
                        const formGroup = element.closest('.form-group');
                        const input = formGroup?.querySelector('input, select, textarea');
                        if (input) {
                            const message = element.textContent.trim();

                            // Store the custom message using multiple keys for better lookup
                            const aspFor = input.getAttribute('asp-for');
                            const id = input.id;
                            const name = input.name;

                            if (aspFor) {
                                customMessages[aspFor] = message;
                                console.log("Stored custom message for asp-for", aspFor, ":", message);
                            }
                            if (id) {
                                customMessages[id] = message;
                                console.log("Stored custom message for id", id, ":", message);
                            }
                            if (name) {
                                customMessages[name] = message;
                                console.log("Stored custom message for name", name, ":", message);
                            }
                        }
                    });

                    // Helper function to restore custom message
                    function restoreCustomMessage(input) {
                        const aspFor = input.getAttribute('asp-for');
                        const id = input.id;
                        const name = input.name;

                        // Try multiple lookup strategies
                        let message = customMessages[aspFor] || customMessages[id] || customMessages[name];

                        if (message) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                feedbackElement.textContent = message;
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message:", message, "for input:", aspFor || id || name);
                                return true;
                            }
                        }
                        return false;
                    }

                    // Function to restore all custom messages after validation
                    function restoreAllCustomMessages() {
                        form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                            // Small delay to ensure the global validation has finished
                            setTimeout(() => {
                                restoreCustomMessage(input);
                            }, 10);
                        });
                    }

                    // Initialize the validation framework
                    window.BCMValidation.init();

                    // Add required field indicators (asterisks)
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for pattern-based inputs
                    window.BCMValidation.addFormatIndicators(form);

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");

                        // Show all validation messages
                        window.BCMValidation.showAllValidationMessages(form);

                        // Validate the form using global validation
                        const isValid = window.BCMValidation.validateForm(form);

                        // Restore all custom messages after validation
                        restoreAllCustomMessages();

                        console.log("Form validation result:", isValid);

                        if (!isValid) {
                            console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                        }
                    });

                    // Add user interaction validation for required fields
                    const allInputs = form.querySelectorAll('input:not([type="hidden"]), select, textarea');
                    allInputs.forEach(function(input) {
                        // Add blur event listener to mark field as touched and validate
                        input.addEventListener('blur', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                // Mark field as touched and remove validation-pending
                                formGroup.classList.add(window.BCMValidation.classes.fieldTouchedClass);
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            // Validate the input using global validation
                            if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }

                            // Restore custom message after a short delay
                            setTimeout(() => {
                                restoreCustomMessage(this);
                            }, 20);
                        });

                        // Add input event listener for real-time validation (only after field is touched)
                        input.addEventListener('input', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup && formGroup.classList.contains(window.BCMValidation.classes.fieldTouchedClass)) {
                                // Validate the input
                                if (this.hasAttribute('pattern')) {
                                    window.BCMValidation.validatePatternInput(this);
                                } else {
                                    window.BCMValidation.validateInput(this);
                                }

                                // Restore custom message after a short delay
                                setTimeout(() => {
                                    restoreCustomMessage(this);
                                }, 20);
                            }
                        });
                    });

                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }

            $(document).on('change', '#ddlApplication', function () {
                var applicationName = $(this).find('option:selected').text();
                $('#ApplicationName').val(applicationName);
            });


            $(document).on('click', '.btnEdit', function () {
                UpdateButtonLabel();

                var id = $(this).data('id');
                // Clear validation errors before populating new values
                const form = document.getElementById('saveUpdateApplicationBIA');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Add validation-pending class to hide messages until user interaction
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }

                if (id) {
                    $.ajax({
                        url: '@Url.Action("ApplicationBIA_GetByID", "BIAApplication")',
                        type: 'GET',
                        data: { id: id },
                        success: function (data) {
                            console.log(data);
                            $('#ddlApplication').val(data.applicationID).change();
                            $('#KeyUsage').val(data.keyUsage);
                            $('#editor1').val(data.manualWorkarounds);
                            $('#ddlDataLoss').val(data.dataLoss);
                            $('#ddlRTO').val(data.recoveryTime);
                            $('input[name="ID"]').val(data.id);
                            UpdateButtonLabel();
                        }
                    })
                }
            });

            // $(document).on('click', '.btnDelete', function () {
            //     var id = $(this).data('id');
            //     if (id) {
            //         $.ajax({
            //             url: '@Url.Action("ApplicationBIA_DeleteByID", "BIAApplication")',
            //             type: 'POST',
            //             data: { id: id }
            //         })
            //     }
            // });

             $(document).on('click', '.btnDelete', function () {
                var id = $(this).data('id');
                //$.get('/BCMProcessBIAForms/BIALegalAndRegulatory/DeleteBIALegalAndRegularty/', { iID: iID }, function (data) {
                $.get('@Url.Action("ApplicationBIA_DeleteByID", "BIAApplication")', { id: id }, function (data) {
                    $('#DeleteBody').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

            $(document).on('click', '#btnCancel', function (event) {
                event.preventDefault();

                // Clear validation errors when canceling
                const form = document.getElementById('saveUpdateApplicationBIA');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Reset validation state
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }

                location.reload();
            });

            // Character counting for Key Usage field
            $('#KeyUsage').on('input keypress', function(){
                var currentLength = this.value.length;
                var maxLength = 9999;

                if(currentLength > maxLength){
                    this.value = this.value.substring(0, maxLength);
                    currentLength = maxLength;
                }
                $("#remainingC").html((maxLength - currentLength) + " characters left.");
            });


            function UpdateButtonLabel() {
                var id = $('input[name="ID"]').val();
                if (id && id > 0) {
                    $('#Submit').text('Update');
                } else {
                    $('#Submit').text('Save');
                }
            }

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        })
    </script>
}