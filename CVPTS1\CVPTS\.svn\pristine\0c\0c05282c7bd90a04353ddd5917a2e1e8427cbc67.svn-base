﻿@model BCM.BusinessClasses.KPIMeasurementMasters

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@* <form asp-action="DeleteKPIMeasurementMaster" method="post">
    <div>
        <input type="hidden" class="form-control" asp-for="ID" style="display:none;" />
    </div>
    <div class="modal-header d-grid text-center">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.OrganizationName</span>" ?</span> 
    </div>
    <img src="~/img/isomatric/delete.svg" width="260" />
    <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
        <button type="submit" class="btn btn-primary btn-sm">Yes delete the file</button>
    </div>
</form> *@



<form asp-action="DeleteKPIMeasurementMaster" asp-controller="KPIMeasurementMaster" method="post">
    <div>
        <input type="hidden" asp-for="ID" class="form-control" style="display:none;" />
    </div>
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    <div class="modal-body d-grid px">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="OrgGroupSpan">@Model.Objective</span>" ?</span> 
    </div>
    <div class="modal-footer justify-content-center p-0">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>
