﻿
@{
    ViewBag.Title = "ViewBusinessProcess";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">View Business Process </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
            <select class="form-select form-control" aria-label="Default select example">
                <option selected>All Organizations</option>
                <option value="1">PTS</option>
                <option value="2">TCS</option>
                <option value="3">Continuity Vault</option>
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
            <select class="form-select form-control" id="unitlist" aria-label="Default select example">
                <option selected>All Units</option>
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-department"></i></span>
            <select class="form-select form-control" id="departmentlist" aria-label="Default select example">
                <option selected>All Departments</option>
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
            <select class="form-select form-control" id="departmentlist" aria-label="Default select example">
                <option selected>All Sub Departments</option>
            </select>
        </div>
        <div class="input-group ">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>

        @*    <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>
<div class="Page-Condant card border-0">
    <table  class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">Sr.&nbsp;No.</th>
                <th>Department</th>
                <th>Total No Of Processes</th>
                <th>Non Critical Processes</th>
                <th>Critical Processes </th>
                <th>Approved </th>
                <th>Disapproved</th>
                <th>Waiting for Approval</th>                
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>1</td>
                <td>Accounting Department</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>               
            </tr>
        </tbody>
    </table>   
</div>
<div class="mt-4">
<h6 class="Page-Title mb-3">Process BIA Details</h6>
<table class="table table-hover" style="width:100%;vertical-align:middle">
    <thead>
        <tr>
                <th class="SrNo_th">Sr.&nbsp;No.</th>
                <th> Process Name </th>
                <th>Owner</th>
                <th>Alternate Owner </th>
                <th>Facilities </th>
                <th>Applications </th>
                <th>RTO</th>
                <th>RPO</th>
                <th>Final RTO</th>
                <th>MTR</th>
                <th>IsCritical</th>
                <th> Status</th>
                <th>Change RTO</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>1</td>
                <td>World Trade Center</td>
            <td>Selvam C P </td>
            <td>Neeraj</td>
            <td>0</td>
            <td>0</td>
            <td>NA</td>
             <td>1 Minute(s)</td>
            <td>0</td>
            <td>0</td>
            <td class="text-danger">NO</td>
                <td class="text-info">Initiated</td>
                <td class="text-primary">
                    Change RTO
                </td>
        </tr>
    </tbody>
</table>
</div>
