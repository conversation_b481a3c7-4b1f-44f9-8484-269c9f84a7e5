﻿@{
    ViewData["Title"] = "OrglevelDepDiagram";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .viewport {
        height: calc(100vh - 135px);
        overflow: hidden;
        position: relative;
        cursor: grab;
    }

        .viewport.grabbing {
            cursor: grabbing;
        }

    .containers {
        display: flex;
        align-items: flex-start;
        gap: 30px;
        margin: 40px;
        transform-origin: 0 0;
        transition: transform 0.1s ease-out;
        position: relative;
    }

    /* Zoom controls */
    .zoom-controls {
        position: fixed;
        bottom: 20px;
        left: 20px;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .zoom-btn {
        width: 40px;
        height: 40px;
        border: 1px solid #e1e1e1;
        border-radius: 50%;
        color: #ab9494;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        transition: all 0.2s ease;
    }

        .zoom-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

    .zoom-info {
        position: fixed;
        bottom: 20px;
        left: 75px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.9);
        padding: 10px 15px;
        border-radius: 10px;
        font-size: 12px;
        color: #666;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .main-section {
        /* background: white; */
        /* border-radius: 15px; */
        padding: 20px;
        /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); */
        min-width: 280px;
    }

    .section-header {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 18px;
        font-weight: 500;
        @* color: #333; *@
        margin-bottom: 20px;
        padding: 10px 15px;
        background: #ffc107;
        @* color: white; *@
        border-radius: 25px;
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        position: relative;
        justify-content: center;
    }

    /* Vertical line connecting section-header to workflow-tree-menu */
    .main-section.has-children .section-header::after {
        content: '';
        position: absolute;
        top: 93%;
        left: 4.4%;
        width: 1.8px;
        height: 42px;
        background: #9ca3af;
        transform: translateX(-50%);
        z-index: 5;
        border-radius: 2px;
    }

    /* Horizontal connecting lines between section headers */
    .main-section:not(:last-child) .section-header::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 100%;
        width: 30%;
        height: 1px;
        background: #9ca3af;
        transform: translateY(-50%);
        z-index: 10;
        border-radius: 2px;
    }


    .section-header .icon {
        font-size: 20px;
    }

    /*tree menu style start*/
    .viewport .workflow-tree-menu {
        position: relative;
    }

        .viewport .workflow-tree-menu ul {
            margin: 0;
            padding-left: 25px;
            line-height: 1.5em;
            list-style: none;
            /* background-color: #fff; */
        }

            .viewport .workflow-tree-menu ul li {
                position: relative;
                margin: 8px 0;
            }

                .viewport .workflow-tree-menu ul li .text-inside {
                    max-width: 100%;
                    width: 90% !important;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: inline-block;
                    vertical-align: middle;
                    padding: 8px 12px;
                    margin: 4px 0;
                    border-radius: 25px;
                    background: white;
                    border: 2px solid #e1e5e9;
                    font-size: 13px;
                    font-weight: 500;
                    transition: all 0.2s ease;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
                }

                    .viewport .workflow-tree-menu ul li .text-inside:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
                    }

                    .viewport .workflow-tree-menu ul li .text-inside.blue {
                        border-color: #007bff;
                        color: #6D6D6D;
                    }

                    .viewport .workflow-tree-menu ul li .text-inside.yellow {
                        border-color: #ffc107;
                        color: #6D6D6D;
                    }

                    .viewport .workflow-tree-menu ul li .text-inside.orange {
                        border-color: #fd7e14;
                        color: #6D6D6D;
                    }

                    .viewport .workflow-tree-menu ul li .text-inside.red {
                        border-color: #dc3545;
                        color: #6D6D6D;
                    }

                    .viewport .workflow-tree-menu ul li .text-inside.green {
                        border-color: #28a745;
                        color: #6D6D6D;
                    }

    .viewport .icon {
        width: 10px;
        height: 10px;
        display: inline-block;
        border-radius: 50%;
        vertical-align: middle;
    }

    .viewport .workflow-tree-menu ul li .text-inside.blue .icon {
        background-color: #007bff;
    }

    .viewport .workflow-tree-menu ul li .text-inside.yellow .icon {
        background-color: #ffc107;
    }

    .viewport .workflow-tree-menu ul li .text-inside.orange .icon {
        background-color: #fd7e14;
    }

    .viewport .workflow-tree-menu ul li .text-inside.red .icon {
        background-color: #dc3545;
    }

    .viewport .workflow-tree-menu ul li .text-inside.green .icon {
        background-color: #28a745;
    }



    .viewport .workflow-tree-menu ul li:before {
        position: absolute;
        top: -14px;
        left: -15px;
        display: block;
        width: 15px;
        height: 2.5em;
        content: "";
        border-bottom: 1px solid #9ca3af;
        border-left: 1px solid #9ca3af;
        border-radius: 0px 0px 0px 3px;
    }

    /* hide the vertical line on the first item */
    .viewport .workflow-tree-menu ul.trees > li:first-child:before {
        border-left: none;
    }

    .viewport .workflow-tree-menu ul li:after {
        position: absolute;
        top: 1.1em;
        bottom: -5px;
        left: -15px;
        display: block;
        content: "";
        border-left: 1px solid #9ca3af;
    }

    /* hide the lines on the last item */
    .viewport .workflow-tree-menu ul li:last-child:after {
        display: none;
    }

    /* inserted via JS  */
    /*   .workflow-tree-menu .js-toggle-icon {
            position: relative;
            z-index: 1;
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 2px;
            margin-left: -23px;
            line-height: 14px;
            text-align: center;
            font-style: normal;
            font-size: 14px;
            border-radius: 50%;
            vertical-align: middle;
            color: #fff;
            cursor: pointer;
            background: linear-gradient(242deg, rgba(230, 56, 117, 1) 0%, rgba(50, 2, 132, 1) 100%);
        } */

    .icon {
        margin-right: 5px;
        opacity: 0.8;
    }


    .toggle-btn {
        font-weight: bold;
        margin-right: 10px;
        cursor: pointer;
        display: inline-block;
        width: 10px;
    }

    .viewport .workflow-tree-menu ul.collapsed {
        display: none;
    }

</style>
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Organization Dependency Matrix</h6>
</div>
<div class="Page-Condant card border-0">


    <div class="w-100 h-100">
        <div class="d-flex align-items-center justify-content-end gap-2 p-2">
            <div>
                <span><i class="cv-radio me-1" style="color:#007bff"></i>Organization</span>
            </div>
            <div>
                <span><i class="cv-radio me-1" style="color:#ffc107"></i>Unit</span>
            </div>
            <div>
                <span><i class="cv-radio me-1" style="color:#fd7e14"></i>Department</span>
            </div>
            <div>
                <span><i class="cv-radio me-1" style="color:#dc3545"></i>Sub Department</span>
            </div>
            <div>
                <span><i class="cv-radio me-1" style="color:#28a745"></i>Business Process</span>
            </div>
        </div>


        <div class="viewport" id="viewport">
            <!-- Zoom Controls -->
            <div class="zoom-controls">
                <button class="zoom-btn" id="zoom-in">+</button>
                <button class="zoom-btn" id="zoom-out">-</button>
                <button class="zoom-btn" id="zoom-reset" style="font-size: 12px;">⌂</button>
            </div>

            <!-- Zoom Info -->
            <div class="zoom-info" id="zoom-info">
                Zoom: 100% | Drag to pan | Scroll to zoom
            </div>
            <div class="containers" id="containers"></div>
            <!-- Sections will be dynamically inserted here -->
        </div>



    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
      // Handle toggle functionality
      // const toggleIcons = document.querySelectorAll('.js-toggle-icon');

      // toggleIcons.forEach(function (icon) {
      //   icon.addEventListener('click', function () {
      //     const parentLi = this.parentElement;
      //     const childUl = parentLi.querySelector('ul');

      //     if (childUl) {
      //       if (childUl.style.display === 'none') {
      //         childUl.style.display = 'block';
      //         this.textContent = '-';
      //       } else {
      //         childUl.style.display = 'none';
      //         this.textContent = '+';
      //       }
      //     }
      //   });
      // });

      // Pan, Scale, and Grab functionality
      const viewport = document.getElementById('viewport');
      const container = document.getElementById('containers');
      const zoomInfo = document.getElementById('zoom-info');
      const zoomInBtn = document.getElementById('zoom-in');
      const zoomOutBtn = document.getElementById('zoom-out');
      const zoomResetBtn = document.getElementById('zoom-reset');

      let scale = 1;
      let translateX = 0;
      let translateY = 0;
      let isDragging = false;
      let startX = 0;
      let startY = 0;
      let lastX = 0;
      let lastY = 0;

      const minScale = 0.3;
      const maxScale = 3;
      const scaleStep = 0.1;

      function updateTransform() {
        container.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`;
        zoomInfo.textContent = `Zoom: ${Math.round(scale * 100)}% | Drag to pan | Scroll to zoom`;
      }

      function getMousePos(e) {
        const rect = viewport.getBoundingClientRect();
        return {
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        };
      }

      // Mouse wheel zoom
      viewport.addEventListener('wheel', function (e) {
        e.preventDefault();

        const mousePos = getMousePos(e);
        const delta = e.deltaY > 0 ? -scaleStep : scaleStep;
        const newScale = Math.max(minScale, Math.min(maxScale, scale + delta));

        if (newScale !== scale) {
          // Zoom towards mouse position
          const scaleRatio = newScale / scale;
          translateX = mousePos.x - (mousePos.x - translateX) * scaleRatio;
          translateY = mousePos.y - (mousePos.y - translateY) * scaleRatio;
          scale = newScale;
          updateTransform();
        }
      });

      // Mouse drag pan
      viewport.addEventListener('mousedown', function (e) {
        if (e.target === viewport || e.target === container) {
          isDragging = true;
          viewport.classList.add('grabbing');
          startX = e.clientX;
          startY = e.clientY;
          lastX = translateX;
          lastY = translateY;
          e.preventDefault();
        }
      });

      document.addEventListener('mousemove', function (e) {
        if (isDragging) {
          translateX = lastX + (e.clientX - startX);
          translateY = lastY + (e.clientY - startY);
          updateTransform();
        }
      });

      document.addEventListener('mouseup', function () {
        isDragging = false;
        viewport.classList.remove('grabbing');
      });

      // Zoom buttons
      zoomInBtn.addEventListener('click', function () {
        if (scale < maxScale) {
          scale = Math.min(maxScale, scale + scaleStep);
          updateTransform();
        }
      });

      zoomOutBtn.addEventListener('click', function () {
        if (scale > minScale) {
          scale = Math.max(minScale, scale - scaleStep);
          updateTransform();
        }
      });

      zoomResetBtn.addEventListener('click', function () {
        scale = 1;
        translateX = 0;
        translateY = 0;
        updateTransform();
      });

      // Touch support for mobile
      let touchStartDistance = 0;
      let touchStartScale = 1;

      viewport.addEventListener('touchstart', function (e) {
        if (e.touches.length === 2) {
          // Pinch zoom start
          const touch1 = e.touches[0];
          const touch2 = e.touches[1];
          touchStartDistance = Math.hypot(
            touch2.clientX - touch1.clientX,
            touch2.clientY - touch1.clientY
          );
          touchStartScale = scale;
        } else if (e.touches.length === 1) {
          // Pan start
          isDragging = true;
          const touch = e.touches[0];
          startX = touch.clientX;
          startY = touch.clientY;
          lastX = translateX;
          lastY = translateY;
        }
        e.preventDefault();
      });

      viewport.addEventListener('touchmove', function (e) {
        if (e.touches.length === 2) {
          // Pinch zoom
          const touch1 = e.touches[0];
          const touch2 = e.touches[1];
          const currentDistance = Math.hypot(
            touch2.clientX - touch1.clientX,
            touch2.clientY - touch1.clientY
          );
          const newScale = Math.max(minScale, Math.min(maxScale,
            touchStartScale * (currentDistance / touchStartDistance)
          ));
          scale = newScale;
          updateTransform();
        } else if (e.touches.length === 1 && isDragging) {
          // Pan
          const touch = e.touches[0];
          translateX = lastX + (touch.clientX - startX);
          translateY = lastY + (touch.clientY - startY);
          updateTransform();
        }
        e.preventDefault();
      });

      viewport.addEventListener('touchend', function () {
        isDragging = false;
      });

      // Initial update
      updateTransform();
    });
</script>
<script>
    // Get dynamic data from the controller via ViewBag
    var jsonData = null;

    @if (ViewBag.ChartData != null)
    {
        <text>
        // Use dynamic data from controller
        var chartDataFromServer = @Html.Raw(Json.Serialize(ViewBag.ChartData));
        console.log('Raw data from server:', chartDataFromServer);

        // Check if we have data and use the first organization
        if (Array.isArray(chartDataFromServer) && chartDataFromServer.length > 0) {
            jsonData = chartDataFromServer[0];
            console.log('Using first organization:', jsonData);
        } else if (chartDataFromServer && !Array.isArray(chartDataFromServer)) {
            // If it's a single object, use it directly
            jsonData = chartDataFromServer;
            console.log('Using single object:', jsonData);
        } else {
            console.warn('No chart data available from server, using fallback');
            jsonData = {
                name: "No Data Available",
                level: "red",
                fill: "#FF0000",
                error: true,
                children: []
            };
        }
        </text>
    }
    else
    {
        <text>
        // Fallback data if ViewBag.ChartData is null
        console.warn('ViewBag.ChartData is null, using fallback data');
        jsonData = {
            name: "Error Loading Data",
            level: "red",
            fill: "#FF0000",
            error: true,
            children: []
        };
        </text>
    }
</script>

<script>
    function renderTreeNode(node) {
      const li = document.createElement("li");
      const hasChildren = node.children && node.children.length > 0;

      const textSpan = document.createElement("span");
      textSpan.className = `text-inside ${node.level}`;

      let ul;
      if (hasChildren) {
        ul = document.createElement("ul");
        ul.classList.add("collapsed");

        node.children.forEach(child => {
          ul.appendChild(renderTreeNode(child));
        });

        const toggle = document.createElement("span");
        toggle.className = "toggle-btn";
        toggle.textContent = "+";

        toggle.addEventListener("click", function (e) {
          e.stopPropagation();
          ul.classList.toggle("collapsed");
          toggle.textContent = ul.classList.contains("collapsed") ? "+" : "−";
        });

        textSpan.appendChild(toggle);
      } else {
        const spacer = document.createElement("span");
        spacer.className = "toggle-btn";
        spacer.textContent = "";
        textSpan.appendChild(spacer);
      }

      const icon = document.createElement("span");
      icon.className = "icon";
      // icon.textContent = "";

      const label = document.createTextNode(node.name);
      textSpan.appendChild(icon);
      textSpan.appendChild(label);
      li.appendChild(textSpan);

      if (hasChildren) {
        li.appendChild(ul);
      }

      return li;
    }

    function renderSection(title, nodes) {
        debugger;
      const section = document.createElement("div");
      section.className = "main-section";

      if (nodes && nodes.length > 0) {
        section.classList.add("has-children");
      }

      section.innerHTML = `
      <div class="section-header">${title}</div>
      <div class="workflow-tree-menu"><ul class="trees"></ul></div>
    `;
      const treeRoot = section.querySelector("ul.trees");

      nodes.forEach(child => {
        treeRoot.appendChild(renderTreeNode(child));
      });

      return section;
    }

    document.addEventListener("DOMContentLoaded", () => {
      const container = document.getElementById("containers");

      // Debug: Log the data we received
      console.log('Organization Dependency Matrix Data:', jsonData);

      // Check if we have valid data
      if (!jsonData) {
        console.error('No jsonData available');
        container.innerHTML = '<div class="alert alert-danger">No data available to display</div>';
        return;
      }

      // Check if this is an error state
      if (jsonData.error) {
        console.warn('Data contains error flag:', jsonData);
        container.innerHTML = '<div class="alert alert-warning">Error loading organization data: ' + jsonData.name + '</div>';
        return;
      }

      // 🔹 First: Show Organization as standalone (root level)
      console.log('Rendering organization section:', jsonData.name);

      // Check if this is organization name and apply blue color
      const orgSection = renderSection(jsonData.name, []);
      const orgHeader = orgSection.querySelector('.section-header');
      if (orgHeader) {
        // Apply blue background color for organization
        orgHeader.style.background = '#007bff';
        orgHeader.style.color = 'white';
        console.log('Applied blue color to organization:', jsonData.name);
      }
      container.appendChild(orgSection);

      // 🔹 Then: Render each top-level child (Units) as a separate section
      if (jsonData.children && jsonData.children.length > 0) {
        console.log('Rendering', jsonData.children.length, 'units');
        jsonData.children.forEach((child, index) => {
          console.log('Rendering unit', index + 1, ':', child.name, 'with', (child.children || []).length, 'departments');

          // Apply yellow color for units
          const unitSection = renderSection(child.name, child.children || []);
          const unitHeader = unitSection.querySelector('.section-header');
          if (unitHeader) {
            unitHeader.style.background = '#ffc107';
            unitHeader.style.color = '#333';
            console.log('Applied yellow color to unit:', child.name);
          }
          container.appendChild(unitSection);
        });
      } else {
        console.warn('No children found for organization');
        // If no children, show a message
        const noDataSection = document.createElement("div");
        noDataSection.className = "main-section";
        noDataSection.innerHTML = '<div class="section-header">No Units Found</div>';
        container.appendChild(noDataSection);
      }
    });
</script>