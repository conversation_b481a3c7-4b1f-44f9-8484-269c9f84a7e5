﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Asn1.Ocsp;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class DisasterTypeMasterController : BaseController
{

    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
 

    public DisasterTypeMasterController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    [HttpGet]
    public IActionResult DisasterTypeMaster()
    {
        List<Disaster> objDisaster = new List<Disaster>();
        try
        {
            objDisaster = _ProcessSrv.GetDisasterTypeList(0);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View(objDisaster);
    }

    [HttpPost]
    public IActionResult SaveIncidentTypeDetails(string DisasterName,string DisasterDescription,int DisasterID)
    {
        try
        {
            int isDataSaved = -1;

            Disaster objDisaster = new Disaster();

            objDisaster.DisasterName = DisasterName;
            objDisaster.DisasterDescription = DisasterDescription;
            objDisaster.DisasterID = DisasterID;
            objDisaster.CreatedBy = _UserDetails.UserID;
            objDisaster.ChangedBy = _UserDetails.UserID;

            isDataSaved = _ProcessSrv.DisasterSaveAndUpdate(objDisaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("DisasterTypeMaster");
    }

    [HttpGet]
    public IActionResult DeleteDisasterTypeDetails(int iId)
    {
        Disaster objDisaster = null;
        try
        {
            if(iId > 0)
            {
                var disaster = _ProcessSrv.GetDisasterTypeList(iId);
                objDisaster = disaster.FirstOrDefault(x => x.DisasterID == iId);
                if(objDisaster != null)
                {
                    return PartialView("_DeleteDisasterTypeDetails", objDisaster);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("DisasterTypeMaster");
    }

    [HttpPost]
    public IActionResult DeleteDisasterTypeDetails(Disaster objDisasterID)
    {
        try
        {
            Disaster objDisaster = new Disaster();
            objDisaster.DisasterID = objDisasterID.DisasterID;
            objDisaster.ChangedBy = _UserDetails.UserID;
            var Delete = _ProcessSrv.DeleteDisasterType(objDisasterID.DisasterID, Convert.ToInt32(_UserDetails.UserID));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("DisasterTypeMaster");
    }


}

