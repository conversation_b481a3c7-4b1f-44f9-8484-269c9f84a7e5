﻿@using BCM.Shared
@model IEnumerable<BCM.BusinessClasses.BIAApprovalAndButtonAccess>
<div class="tab-pane fade active show" id="one-tab-pane" role="tabpanel" aria-labelledby="one-tab">
    <div>
        <table class="table table-info">
            <thead>
                <tr>
                    <td>@ViewBag.strEntityType Name</td>
                    <td>Owner Details</td>
                    <td>Approver Details</td>
                    @if (@ViewBag.strEntityType == "Process")
                    {
                        <td>RTO</td>
                    }
                    
                    <td>Status</td>
                    @if (ViewBag.iStatusID == 1)
                    {
                        <td>Action</td>
                    }
                </tr>
            </thead>
            <tbody>
                @if (Model != null)
                {
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>
                                <table>
                                    <tbody>
                                        <tr>
                                            <td>@item.BIAApproval.RecordName</td>
                                        </tr>
                                        <tr>
                                            <td>(@item.BIAApproval.EntityCode)</td>
                                        </tr>
                                        <tr>
                                            <td>Version : @item.BIAApproval.Version</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tbody>
                                        <tr>
                                            <td><i class="cv-user "></i></td>
                                            <td> : </td>
                                            <td>@item.BIAApproval.OwnerName</td>
                                        </tr>
                                        <tr>
                                            <td><i class="cv-mail "></i></td>
                                            <td>:</td>
                                            <td>@item.BIAApproval.OwnerEmail</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                                            <td>:</td>
                                            <td>@item.BIAApproval.OwnerPhone</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tbody>
                                        <tr>
                                            <td><i class="cv-user "></i></td>
                                            <td> : </td>
                                            <td>@item.BIAApproval.ApprovarName</td>
                                        </tr>
                                        <tr>
                                            <td><i class="cv-mail "></i></td>
                                            <td>:</td>
                                            <td>@item.BIAApproval.ApproverEmail</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                                            <td>:</td>
                                            <td>@item.BIAApproval.ApproverPhone</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            @if (@ViewBag.strEntityType == "Process"){
                                <td>@Utilities.GetFormattedRTO(Convert.ToInt32(@item.BIAApproval.RTO))</td>
                            }
                            
                            @if (ViewBag.iStatusID == 1){
                                <td class="text-warning">WaitingforApproval</td>
                            }
                            @if (ViewBag.iStatusID == 2)
                            {
                                <td class="text-success">Approved</td>
                            }
                            @if (ViewBag.iStatusID == 3)
                            {
                                <td class="text-danger">Disapproved</td>
                            }
                            @if (ViewBag.iStatusID == 1)
                            {
                            <td>
                                    <span class="btn-action btnApproveProcess" @item.ButtonAcces.btnApprove type="button" data-entitytypeid="@ViewBag.EntityTypeID" data-status="2" data-id="@item.BIAApproval.RecordID"><i class="cv-edit" title="Edit"></i></span>
                                    <span class="btn-action btnDisApproveProcess" @item.ButtonAcces.btnDisApprove type="button" data-entitytypeid="@ViewBag.EntityTypeID" data-status="3" data-id="@item.BIAApproval.RecordID" data-bs-toggle="modal" data-bs-target="Modal"><i class="cv-delete text-danger" title="Delete"></i></span>
                            </td>
                            }
                        </tr>
                    }
                }

            </tbody>
        </table>
    </div>
</div>
