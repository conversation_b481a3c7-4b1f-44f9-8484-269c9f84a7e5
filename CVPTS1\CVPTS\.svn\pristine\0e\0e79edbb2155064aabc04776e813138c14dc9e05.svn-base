﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Security.Policy;

namespace BCM.UI.Areas.BCMTraining.Controllers;
[Area("BCMTraining")]
public class TrainingandExaminationController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public TrainingandExaminationController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult Index()
    {
        return View();
    }

    public IActionResult TrainingandExamination()
    {
        try
        {
            GetTrainingStatistics();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
    }

    private void GetTrainingStatistics()
    {
        try
        {
            // Get all training data
            List<BCMTrainingMaster> allTrainings = _ProcessSrv.BCMTrainingMaster_getAll();

            if (allTrainings == null)
                allTrainings = new List<BCMTrainingMaster>();

            // Calculate counts
            int totalCourseCount = allTrainings.Count;
            int coursePendingCount = allTrainings.Where(t => t.Status == 0 || t.Status == 1).Count(); // Initiated or Waiting for Approval
            int courseAttendedCount = allTrainings.Where(t => t.Status == 2).Count(); // Approved/Completed

            // Set ViewBag values
            ViewBag.TotalCourseCount = totalCourseCount;
            ViewBag.CoursePendingCount = coursePendingCount;
            ViewBag.CourseAttendedCount = courseAttendedCount;

            // Get detailed lists for each section
            ViewBag.CoursePendingDetails = allTrainings.Where(t => t.Status == 0 || t.Status == 1).ToList();
            ViewBag.CourseAttendedDetails = allTrainings.Where(t => t.Status == 2).ToList();
            ViewBag.TotalCourseDetails = allTrainings;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

            // Set default values in case of error
            ViewBag.TotalCourseCount = 0;
            ViewBag.CoursePendingCount = 0;
            ViewBag.CourseAttendedCount = 0;
            ViewBag.CoursePendingDetails = new List<BCMTrainingMaster>();
            ViewBag.CourseAttendedDetails = new List<BCMTrainingMaster>();
            ViewBag.TotalCourseDetails = new List<BCMTrainingMaster>();
        }
    }

    public IActionResult TrainingQuestionPaper(int TrainingID, int watch = 0, int PublishID = 0, int questionIndex = 1)
    {
        try
        {
            _CVLogger.LogInfo($"TrainingQuestionPaper called with TrainingID: {TrainingID}, watch: {watch}, PublishID: {PublishID}, questionIndex: {questionIndex}");

            // Get current user ID early
            int currentUserID = _UserDetails?.UserID ?? 0;

            // Get training details
            var training = _ProcessSrv.BCMTrainingMaster_getByID(TrainingID);
            if (training == null)
            {
                _CVLogger.LogInfo($"Training not found for TrainingID: {TrainingID}");
                ViewBag.ErrorMessage = "Training not found.";
                ViewBag.TrainingId = TrainingID;
                ViewBag.PublishID = PublishID;
                ViewBag.ShowError = true;
                return View(); // Stay on TrainingQuestionPaper view with error message
            }

            // Get current user ID
            //int currentUserID = _UserDetails?.UserID ?? 0;
            _CVLogger.LogInfo($"Current user ID: {currentUserID}");

            // Get questions for this training
            var questions = _ProcessSrv.QuestionsDetailsGetByTrainingMasterID(TrainingID);
            if (questions == null || !questions.Any())
            {
                _CVLogger.LogInfo($"No questions found for TrainingID: {TrainingID}");
                ViewBag.ErrorMessage = "No questions found for this training.";
                ViewBag.TrainingId = TrainingID;
                ViewBag.TrainingName = training.TrainingName;
                ViewBag.ShowError = true;
                return View(); // Stay on TrainingQuestionPaper view with error message
            }

            // Debug: Log question details
            _CVLogger.LogInfo($"Found {questions.Count} questions for TrainingID: {TrainingID}");
            if (questions.Any())
            {
                var firstQ = questions.First();
                _CVLogger.LogInfo($"First question - ID: {firstQ.ID}, QuestionmasterID: {firstQ.QuestionmasterID}, QuestionText: {firstQ.QuestionText?.Substring(0, Math.Min(50, firstQ.QuestionText?.Length ?? 0))}...");
            }

            // Ensure questionIndex is within valid range
            if (questionIndex < 1) questionIndex = 1;
            if (questionIndex > questions.Count) questionIndex = questions.Count;

            // Set ViewBag properties for the view
            ViewBag.TrainingId = TrainingID;
            ViewBag.TrainingName = training.TrainingName;
            ViewBag.PublishID = PublishID;
            ViewBag.Watch = watch;
            ViewBag.TotalQuestions = questions.Count;
            ViewBag.CurrentQuestionIndex = questionIndex;
            // Get TimeRemaining from TrainingDuration
            int timeRemainingSeconds = ParseTrainingDurationToSeconds(training.TrainingDuration);
            ViewBag.TimeRemaining = timeRemainingSeconds;

            // Get current question details based on questionIndex
            if (questions.Any() && questionIndex <= questions.Count)
            {
                var currentQuestion = questions[questionIndex - 1]; // Convert to 0-based index
                ViewBag.QuestionText = currentQuestion.QuestionText;

                // Start with the QuestionmasterID from the current question
                int questionMasterID = currentQuestion.QuestionmasterID;

                // Debug: Log the initial questionMasterID
                _CVLogger.LogInfo($"Initial questionMasterID from currentQuestion: {questionMasterID}");
                _CVLogger.LogInfo($"currentQuestion.ID: {currentQuestion.ID}");

                // Final check: if questionMasterID is still 0, try to use the question ID from training
                if (questionMasterID <= 0)
                {
                    // Try different properties that might contain the question ID
                    if (!string.IsNullOrEmpty(currentQuestion.ID))
                    {
                        if (int.TryParse(currentQuestion.ID, out int parsedID) && parsedID > 0)
                        {
                            questionMasterID = parsedID;
                            _CVLogger.LogInfo($"Using currentQuestion.ID as questionMasterID: {questionMasterID}");
                        }
                    }
                }

                // If still 0, use the questionIndex as a fallback
                if (questionMasterID <= 0)
                {
                    questionMasterID = questionIndex; // Use questionIndex as fallback
                    _CVLogger.LogInfo($"Using questionIndex as questionMasterID: {questionMasterID}");
                }

                _CVLogger.LogInfo($"Final questionMasterID being used: {questionMasterID}");

                // Set the ViewBag with the final questionMasterID
                ViewBag.CurrentQuestionId = questionMasterID;

                // Get options for the current question
                var options = GetQuestionOptions(questionMasterID);
                ViewBag.QuestionOptions = options;

                // Get any previously selected answers
                var selectedAnswers = GetSelectedAnswers(TrainingID, currentUserID, questionMasterID);
                ViewBag.SelectedAnswers = selectedAnswers;

                // Debug: Log what we're setting in ViewBag
                _CVLogger.LogInfo($"Setting ViewBag.SelectedAnswers with {selectedAnswers?.Count ?? 0} answers: [{string.Join(", ", selectedAnswers ?? new List<string>())}]");
            }

            // Get UserTrainingId using the provided method
            List<BCMTrainingMaster> ob = _ProcessSrv.userExamMaster_getByTrainingMasterID(TrainingID, currentUserID, PublishID);
            string userTrainingId = "";

            if (ob != null && ob.Count > 0)
            {
                foreach (BCMTrainingMaster item in ob)
                {
                    userTrainingId = item.ID;
                    break; // Take the first item
                }
            }

            ViewBag.UserTrainingId = userTrainingId;

            _CVLogger.LogInfo($"Successfully loaded TrainingQuestionPaper for TrainingID: {TrainingID}");
            return View();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            _CVLogger.LogInfo($"Exception in TrainingQuestionPaper: {ex.Message}");

            // Stay on TrainingQuestionPaper view with error message instead of redirecting
            ViewBag.ErrorMessage = "An error occurred while loading the training question paper: " + ex.Message;
            ViewBag.TrainingId = TrainingID;
            ViewBag.ShowError = true;
            ViewBag.TotalQuestions = 0;
            ViewBag.CurrentQuestionIndex = 1;
            ViewBag.TimeRemaining = 3600; // Default 1 hour for error cases

            return View(); // Stay on TrainingQuestionPaper view
        }
    }

    private List<dynamic> GetQuestionOptions(int questionId)
    {
        try
        {
            _CVLogger.LogInfo($"GetQuestionOptions called with questionId: {questionId}");

            if (questionId <= 0)
            {
                _CVLogger.LogInfo("QuestionId is 0 or negative, returning empty options list");
                return new List<dynamic>();
            }

            // Get options for the specific question using the ProcessSrv method
            var optionsList = _ProcessSrv.OptionsDetailsGetByQuestionMasterID(questionId);

            _CVLogger.LogInfo($"OptionsDetailsGetByQuestionMasterID returned {optionsList?.Count ?? 0} options");

            if (optionsList == null || !optionsList.Any())
            {
                _CVLogger.LogInfo("No options found for questionId: " + questionId);
                return new List<dynamic>();
            }

            // Convert BCMTrainingMaster objects to dynamic objects for the view
            var options = optionsList.Select(option => new
            {
                OptionID = option.OptionID ?? "0", // Using the actual OptionID property
                OptionText = option.OptionText ?? string.Empty,
                IsAnswer = option.IsAnswer
            }).ToList<dynamic>();

            _CVLogger.LogInfo($"Converted {options.Count} options for display");
            return options;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            _CVLogger.LogInfo($"Error in GetQuestionOptions for questionId: {questionId}");
            return new List<dynamic>();
        }
    }

    private List<string> GetSelectedAnswers(int trainingId, int userId, int questionId)
    {
        try
        {
            _CVLogger.LogInfo($"GetSelectedAnswers - trainingId: {trainingId}, userId: {userId}, questionId: {questionId}");

            // First try to get from session (current session answers)
            string sessionKey = $"UserAnswer_{userId}_{trainingId}_{questionId}";
            var sessionAnswerJson = HttpContext.Session.GetString(sessionKey);

            if (!string.IsNullOrEmpty(sessionAnswerJson))
            {
                try
                {
                    var sessionAnswer = System.Text.Json.JsonSerializer.Deserialize<dynamic>(sessionAnswerJson);
                    var selectedAnswersProperty = ((System.Text.Json.JsonElement)sessionAnswer).GetProperty("SelectedAnswers");
                    var selectedAnswers = new List<string>();

                    foreach (var answer in selectedAnswersProperty.EnumerateArray())
                    {
                        selectedAnswers.Add(answer.GetString() ?? "");
                    }

                    _CVLogger.LogInfo($"Found {selectedAnswers.Count} selected answers from session for questionId: {questionId}");
                    return selectedAnswers;
                }
                catch (Exception sessionEx)
                {
                    _CVLogger.LogErrorApp(sessionEx);
                }
            }

            // Fallback: Try to get from database using existing method
            // IMPORTANT: For now, we'll be very conservative and NOT restore from database
            // until we understand the exact data structure returned by GetUserExamDetails_ByUserID
            var dbSelectedAnswers = new List<string>();

            // Enable database restoration now that we have proper saving mechanism
            bool enableDatabaseRestore = true; // Now enabled with proper filtering

            int publishID = 0;
            try
            {
                List<BCMTrainingMaster> allTrainings = _ProcessSrv.BCMTrainingMaster_getAll();
                if (allTrainings != null && allTrainings.Count > 0)
                {
                    var training = allTrainings.FirstOrDefault(t => Convert.ToString(t.TrainingID) == trainingId.ToString());
                    if (training != null && !string.IsNullOrEmpty(Convert.ToString(training.PublishID)))
                    {
                        publishID = Convert.ToInt32(training.PublishID);
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                publishID = 0; // Fallback to 0 if error occurs
            }

            if (enableDatabaseRestore)
            {
                _CVLogger.LogInfo("Attempting to get selected answers from database...");

                try
                {
                DataTable DS1 = _ProcessSrv.GetUserExamDetails_ByUserID(trainingId.ToString(), userId.ToString(), publishID.ToString());

                if (DS1 != null && DS1.Rows.Count > 0)
                {
                    _CVLogger.LogInfo($"DS1 has {DS1.Rows.Count} total rows");

                    // Log column names for debugging
                    if (DS1.Columns.Count > 0)
                    {
                        var columnNames = string.Join(", ", DS1.Columns.Cast<DataColumn>().Select(c => c.ColumnName));
                        _CVLogger.LogInfo($"Available columns: {columnNames}");
                    }

                    // Filter rows for the specific question
                    var questionRows = DS1.Select($"QuestionID = {questionId}");
                    _CVLogger.LogInfo($"Found {questionRows.Length} rows for questionId: {questionId}");

                    // Log first few rows to understand data structure
                    int logCount = Math.Min(3, questionRows.Length);
                    for (int i = 0; i < logCount; i++)
                    {
                        var row = questionRows[i];
                        var rowData = string.Join(", ", row.ItemArray.Select((field, index) => $"{DS1.Columns[index].ColumnName}={field}"));
                        _CVLogger.LogInfo($"Sample row {i + 1}: {rowData}");
                    }

                    // CONSERVATIVE APPROACH: Only restore if we have very clear indicators
                    foreach (DataRow row in questionRows)
                    {
                        // Method 1: Look for explicit SelectedOptionID column
                        if (row.Table.Columns.Contains("SelectedOptionID") &&
                            row["SelectedOptionID"] != DBNull.Value &&
                            !string.IsNullOrEmpty(row["SelectedOptionID"].ToString()) &&
                            row["SelectedOptionID"].ToString() != "0")
                        {
                            string selectedOptionId = row["SelectedOptionID"].ToString();
                            if (!dbSelectedAnswers.Contains(selectedOptionId))
                            {
                                dbSelectedAnswers.Add(selectedOptionId);
                                _CVLogger.LogInfo($"Added SelectedOptionID: {selectedOptionId}");
                            }
                        }
                        // Method 2: Look for IsSelected = "1" or "true"
                        else if (row.Table.Columns.Contains("OptionID") &&
                                 row.Table.Columns.Contains("IsSelected") &&
                                 row["OptionID"] != DBNull.Value &&
                                 row["IsSelected"] != DBNull.Value)
                        {
                            string isSelectedValue = row["IsSelected"].ToString();
                            if (isSelectedValue == "1" || isSelectedValue.ToLower() == "true")
                            {
                                string optionId = row["OptionID"].ToString();
                                if (!dbSelectedAnswers.Contains(optionId))
                                {
                                    dbSelectedAnswers.Add(optionId);
                                    _CVLogger.LogInfo($"Added OptionID (IsSelected={isSelectedValue}): {optionId}");
                                }
                            }
                        }
                    }

                    _CVLogger.LogInfo($"Final selected answers from database: [{string.Join(", ", dbSelectedAnswers)}]");
                }
                else
                {
                    _CVLogger.LogInfo("No user exam data found in database");
                }
            }
            catch (Exception dbEx)
            {
                _CVLogger.LogErrorApp(dbEx);
                _CVLogger.LogInfo("Error accessing database, returning empty list");
                }
            }
            else
            {
                _CVLogger.LogInfo("Database restoration is disabled. Only session-based answers will be restored.");
            }

            return dbSelectedAnswers;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return new List<string>();
        }
    }

    private int GetOrCreateUserTrainingId(int trainingId, int userId, int publishId)
    {
        try
        {
            // This method should get or create a user training record
            // You'll need to implement this based on your data structure
            // Return the UserTrainingID
            return 0; // Placeholder
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return 0;
        }
    }

    /// <summary>
    /// Get or create UserExamMasterID for a user and training
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="trainingId">Training ID</param>
    /// <returns>UserExamMasterID</returns>
    private int GetOrCreateUserExamMasterID(int userId, int trainingId)
    {
        try
        {
            _CVLogger.LogInfo($"GetOrCreateUserExamMasterID - userId: {userId}, trainingId: {trainingId}");

            // Try to get existing UserExamMasterID from session first
            string sessionKey = $"UserExamMasterID_{userId}_{trainingId}";
            var existingIdJson = HttpContext.Session.GetString(sessionKey);

            if (!string.IsNullOrEmpty(existingIdJson))
            {
                if (int.TryParse(existingIdJson, out int existingId) && existingId > 0)
                {
                    _CVLogger.LogInfo($"Found existing UserExamMasterID in session: {existingId}");
                    return existingId;
                }
            }

            // Create new UserExamMaster record
            BCMTrainingMaster masterRecord = new BCMTrainingMaster();
            masterRecord.ID = "0"; // New record
            masterRecord.UserID = userId;
            masterRecord.TrainingID = trainingId;
            masterRecord.ExamStartTime = DateTime.Now;
            masterRecord.Status = 1; // Active/In Progress
            masterRecord.CreatedBy = userId;
            masterRecord.UpdatedBy = userId;

            // Save the master record and get the ID
            // Note: You may need to create SaveUserExamMaster method in ProcessSrv if it doesn't exist
            try
            {
                int newUserExamMasterID = _ProcessSrv.SaveUserExamMaster(masterRecord);

                if (newUserExamMasterID > 0)
                {
                    // Store in session for future use
                    HttpContext.Session.SetString(sessionKey, newUserExamMasterID.ToString());
                    _CVLogger.LogInfo($"Created new UserExamMasterID: {newUserExamMasterID}");
                    return newUserExamMasterID;
                }
            }
            catch (Exception saveEx)
            {
                _CVLogger.LogErrorApp(saveEx);
                _CVLogger.LogInfo("SaveUserExamMaster method may not exist, using fallback approach");
            }

            // Fallback: Generate a unique ID based on user and training
            int fallbackId = int.Parse($"{userId}{trainingId}");
            HttpContext.Session.SetString(sessionKey, fallbackId.ToString());
            _CVLogger.LogInfo($"Using fallback UserExamMasterID: {fallbackId}");
            return fallbackId;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            // Final fallback: return a default value
            return 1;
        }
    }



    [HttpPost]
    public IActionResult SaveAnswer(int questionId, List<string> selectedAnswers, string direction, int trainingId, int currentQuestionIndex = 0)
    {
        try
        {
            int currentUserID = _UserDetails?.UserID ?? 0;

            _CVLogger.LogInfo($"SaveAnswer called - questionId: {questionId}, direction: {direction}, trainingId: {trainingId}, currentQuestionIndex: {currentQuestionIndex}");

            // Save the current answer
            //bool saveResult = SaveUserAnswer(questionId, selectedAnswers, currentUserID, trainingId);

            // If direction is "next", call UpdateExamdetails to save data
            if (direction == "next" )
            {
                _CVLogger.LogInfo($"Next button clicked - calling UpdateExamdetails for trainingId: {trainingId}, userId: {currentUserID}, questionId: {questionId}");
                bool updateExamResult = UpdateExamdetails(trainingId, currentUserID, questionId, selectedAnswers);

                if (!updateExamResult)
                {
                    _CVLogger.LogInfo($"UpdateExamdetails failed for trainingId: {trainingId}, userId: {currentUserID}");
                    return Json(new { success = false, message = "Failed to update exam details." });
                }

                _CVLogger.LogInfo($"UpdateExamdetails completed successfully for trainingId: {trainingId}, userId: {currentUserID}");
            }

            //if (!saveResult)
            //{
            //    return Json(new { success = false, message = "Failed to save answer." });
            //}

            // Get all questions for this training
            var questions = _ProcessSrv.QuestionsDetailsGetByTrainingMasterID(trainingId);
            if (questions == null || !questions.Any())
            {
                return Json(new { success = false, message = "No questions found." });
            }

            _CVLogger.LogInfo($"Found {questions.Count} questions for training");

            // Use currentQuestionIndex if provided, otherwise try to find by questionId
            int currentIndex = -1;

            if (currentQuestionIndex > 0)
            {
                // Convert from 1-based to 0-based index
                currentIndex = currentQuestionIndex - 1;
                _CVLogger.LogInfo($"Using provided currentQuestionIndex: {currentQuestionIndex} (0-based: {currentIndex})");
            }
            else
            {
                // Try to find by questionId
                currentIndex = questions.FindIndex(q => q.QuestionmasterID == questionId);
                _CVLogger.LogInfo($"Found question by ID at index: {currentIndex}");
            }

            // Validate current index
            if (currentIndex < 0 || currentIndex >= questions.Count)
            {
                _CVLogger.LogInfo($"Invalid currentIndex: {currentIndex}, defaulting to 0");
                currentIndex = 0;
            }

            int nextIndex = currentIndex;

            // Calculate next question index based on direction
            if (direction == "next" && currentIndex < questions.Count - 1)
            {
                nextIndex = currentIndex + 1;
                _CVLogger.LogInfo($"Moving to next question: {nextIndex + 1}");
            }
            else if (direction == "previous" && currentIndex > 0)
            {
                nextIndex = currentIndex - 1;
                _CVLogger.LogInfo($"Moving to previous question: {nextIndex + 1}");
            }
            else
            {
                _CVLogger.LogInfo($"No navigation - staying at question: {currentIndex + 1}");
            }

            // Get PublishID from BCMTrainingMaster_getAll method
            int publishID = 0;
            try
            {
                List<BCMTrainingMaster> allTrainings = _ProcessSrv.BCMTrainingMaster_getAll();
                if (allTrainings != null && allTrainings.Count > 0)
                {
                    var training = allTrainings.FirstOrDefault(t => Convert.ToString(t.TrainingID) == trainingId.ToString());
                    if (training != null && !string.IsNullOrEmpty(Convert.ToString(training.PublishID)))
                    {
                        publishID = Convert.ToInt32(training.PublishID);
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                publishID = 0; // Fallback to 0 if error occurs
            }

            // Build redirect URL for next question
            var redirectUrl = Url.Action("TrainingQuestionPaper", "TrainingandExamination", new
            {
                Area = "BCMTraining",
                TrainingID = trainingId,
                questionIndex = nextIndex + 1, // Convert back to 1-based
                watch = 1,
                PublishID = publishID // Use actual PublishID from database
            });

            _CVLogger.LogInfo($"Redirecting to: {redirectUrl}");

            return Json(new { success = true, redirectUrl = redirectUrl });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "An error occurred while saving the answer." });
        }
    }

    [HttpPost]
    public IActionResult AutoSaveAnswer(int questionId, List<string> selectedAnswers, int trainingId)
    {
        try
        {
            int currentUserID = _UserDetails?.UserID ?? 0;

            // Save the current answer
            bool saveResult = false;// SaveUserAnswer(questionId, selectedAnswers, currentUserID, trainingId);

            return Json(new { success = saveResult });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false });
        }
    }

    /// <summary>
    /// Update exam details using the provided method structure
    /// </summary>
    /// <param name="trainingId">Training ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="questionId">Question ID</param>
    /// <param name="selectedAnswers">Selected answer options</param>
    /// <param name="previous">Previous count (default 0)</param>
    /// <returns>True if successful, false otherwise</returns>
    private bool UpdateExamdetails(int trainingId, int userId, int questionId, List<string> selectedAnswers, int previous = 0)
    {
        try
        {
            _CVLogger.LogInfo($"UpdateExamdetails called - trainingId: {trainingId}, userId: {userId}, questionId: {questionId}, previous: {previous}");

            int IsAnswer = 0;
            int result = 0;
            int failCount = 0;
            int attemptedQuestionCount = 0;
            int correctAttemptedCount = 0;
            int SelectedOptionCount = 0;
            bool IsAttempted = false;
            bool Success = false;

            BCMTrainingMaster obj1 = new BCMTrainingMaster();
            BCMTrainingMaster obj = new BCMTrainingMaster();

            // Get UserExamMasterID from session or create new one
            //int userExamMasterID = GetOrCreateUserExamMasterID(userId, trainingId);
            //_CVLogger.LogInfo($"Using UserExamMasterID: {userExamMasterID}");

            List<BCMTrainingMaster> ob = _ProcessSrv.userExamMaster_getByTrainingMasterID(trainingId, userId, Convert.ToInt32(ViewBag.PublishID) ?? 0);
            string userExamMasterID = "0";

            if (ob != null && ob.Count > 0)
            {
                foreach (BCMTrainingMaster item in ob)
                {
                    userExamMasterID = item.ID; // Equivalent to lbluserTrainingID.Text = item.ID;
                    break; // Take the first item
                }
            }



            // Get user exam master details using the provided method
            obj1 = _ProcessSrv.userExamMaster_getByID(Convert.ToInt32(userExamMasterID));
            failCount = Convert.ToInt32(obj1.FailedQstnAttempted ?? "0");

            // Get option details for the current question
            List<BCMTrainingMaster> OptionDetails = _ProcessSrv.OptionsDetailsGetByQuestionMasterID(questionId);

            int answerCount = 0;
            foreach (BCMTrainingMaster item1 in OptionDetails)
            {
                if (item1.IsAnswer == 1)
                {
                    answerCount++;
                }
            }

            attemptedQuestionCount = Convert.ToInt32(obj1.AttemptedQuestionCount ?? "0");
            correctAttemptedCount = Convert.ToInt32(obj1.CorrectAttemptedQstnCount ?? "0");
            result = Convert.ToInt32(obj1.Result ?? "0");

            obj.QuestionID = questionId.ToString();
            obj.UserExamMasterID = userExamMasterID;
            obj.UserID = userId;
            obj.TrainingID = trainingId;
            obj.FailedQstnAttempted = obj1.FailedQstnAttempted;

            // Process each option for the question
            foreach (BCMTrainingMaster optionItem in OptionDetails)
            {
                obj.OptionID = optionItem.OptionID;
                bool isSelected = selectedAnswers != null && selectedAnswers.Contains(optionItem.OptionID);
                obj.IsSelected = isSelected ? "1" : "0";

                Success = _ProcessSrv.UpdateUserExamDetails(obj);

                if (isSelected)
                {
                    SelectedOptionCount++;
                    IsAttempted = true;

                    if (optionItem.IsAnswer == 1)
                    {
                        IsAnswer++;
                    }
                }
            }

            if (answerCount == IsAnswer)
            {
                correctAttemptedCount++;
            }

            if (answerCount == IsAnswer && (SelectedOptionCount > answerCount || SelectedOptionCount < answerCount))
            {
                failCount++;
                correctAttemptedCount--;
            }

            if (IsAttempted)
            {
                attemptedQuestionCount++;
            }

            if (previous != 0)
            {
                if (attemptedQuestionCount > 0) { attemptedQuestionCount = attemptedQuestionCount - previous; }
                if (correctAttemptedCount > 0) correctAttemptedCount = correctAttemptedCount - previous;
                if (failCount > 0) failCount = failCount - previous;
            }

            obj.ID = userExamMasterID;
            obj.AttemptedQuestionCount = attemptedQuestionCount.ToString();
            obj.FailedQstnAttempted = failCount.ToString();
            obj.CorrectAttemptedQstnCount = correctAttemptedCount.ToString();

            // Get total question count from training
            var totalQuestions = GetTotalQuestionCount(trainingId);
            obj.TotalQuestionCount = totalQuestions.ToString();

            _CVLogger.LogInfo($"UpdateExamdetails completed - Attempted: {attemptedQuestionCount}, Correct: {correctAttemptedCount}, Failed: {failCount}");
            return true;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return false;
        }
    }

    /// <summary>
    /// Get total question count for a training
    /// </summary>
    /// <param name="trainingId">Training ID</param>
    /// <returns>Total question count</returns>
    private int GetTotalQuestionCount(int trainingId)
    {
        try
        {
            var questions = _ProcessSrv.TrainingQuestions_GetByTrainingID(trainingId);
            if (questions != null)
            {
                // Get unique question count
                var uniqueQuestions = questions.Select(q => q.QuestionID).Distinct().Count();
                return uniqueQuestions;
            }
            return 0;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return 0;
        }
    }

    /// <summary>
    /// Shuffle DataTable rows randomly
    /// </summary>
    /// <param name="table">DataTable to shuffle</param>
    /// <returns>Shuffled DataTable</returns>
    private DataTable ShuffleDatatable(DataTable table)
    {
        try
        {
            DataTable shuffledTable = table.Clone();
            Random rng = new Random();

            var rows = table.AsEnumerable().OrderBy(r => rng.Next()).ToArray();

            foreach (var row in rows)
            {
                shuffledTable.ImportRow(row);
            }

            return shuffledTable;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return table; // Return original table if shuffle fails
        }
    }



    [HttpPost]
    public IActionResult FinishExam(int trainingId)
    {
        try
        {
            int currentUserID = _UserDetails?.UserID ?? 0;

            _CVLogger.LogInfo($"FinishExam called - trainingId: {trainingId}, userId: {currentUserID}");

            // Update exam details using the provided method structure
            // Note: For finish exam, we'll process all questions, so passing 0 for questionId and empty list for selectedAnswers
            bool updateResult = UpdateExamdetails(trainingId, currentUserID, 0, new List<string>());

            if (updateResult)
            {
                // Mark exam as completed
                bool finishResult = CompleteUserExam(trainingId, currentUserID);

                if (finishResult)
                {
                    _CVLogger.LogInfo($"Exam completed successfully for trainingId: {trainingId}, userId: {currentUserID}");
                    return Json(new { success = true, message = "Exam completed successfully!" });
                }
                else
                {
                    _CVLogger.LogInfo($"Failed to complete exam for trainingId: {trainingId}, userId: {currentUserID}");
                    return Json(new { success = false, message = "Failed to complete exam." });
                }
            }
            else
            {
                _CVLogger.LogInfo($"Failed to update exam details for trainingId: {trainingId}, userId: {currentUserID}");
                return Json(new { success = false, message = "Failed to update exam details." });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "An error occurred while finishing the exam." });
        }
    }

    public IActionResult ViewResults(int trainingId)
    {
        try
        {
            int currentUserID = _UserDetails?.UserID ?? 0;

            // Get training details
            var training = _ProcessSrv.BCMTrainingMaster_getByID(trainingId);
            if (training == null)
            {
                TempData["ErrorMessage"] = "Training not found.";
                return RedirectToAction("TrainingandExamination");
            }

            // Get exam results
            var results = GetExamResults(trainingId, currentUserID);

            // Set ViewBag properties for the view
            ViewBag.TrainingId = trainingId;
            ViewBag.TrainingName = training.TrainingName;
            ViewBag.PublishID = 0; // Default value

            // Set result data (these will come from GetExamResults method)
            ViewBag.TotalQuestions = results?.TotalQuestions ?? 0;
            ViewBag.CorrectAnswers = results?.CorrectAnswers ?? 0;
            ViewBag.IncorrectAnswers = (results?.TotalQuestions ?? 0) - (results?.CorrectAnswers ?? 0);
            ViewBag.ScorePercentage = results?.ScorePercentage ?? 0;
            ViewBag.IsPassed = results?.IsPassed ?? false;
            ViewBag.TimeTaken = results?.TimeTaken ?? "00:00:00";
            ViewBag.ExamDate = DateTime.Now.ToString("dd/MM/yyyy");
            ViewBag.PassingScore = 70; // Default passing score

            return View();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            TempData["ErrorMessage"] = "An error occurred while loading exam results.";
            return RedirectToAction("TrainingandExamination");
        }
    }



    private bool CompleteUserExam(int trainingId, int userId)
    {
        try
        {
            // Implement logic to mark exam as completed
            // This should:
            // 1. Calculate final score
            // 2. Mark exam as completed
            // 3. Update completion timestamp
            // 4. Determine pass/fail status

            return true; // Placeholder - return actual completion result
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return false;
        }
    }

    private dynamic GetExamResults(int trainingId, int userId)
    {
        try
        {
            // Get user exam details to calculate results
            DataTable DS1 = _ProcessSrv.GetUserExamDetails_ByUserID(trainingId.ToString(), userId.ToString(), "0");

            if (DS1 == null || DS1.Rows.Count == 0)
            {
                // No exam data found - return default values
                return new
                {
                    TotalQuestions = 0,
                    CorrectAnswers = 0,
                    ScorePercentage = 0,
                    IsPassed = false,
                    TimeTaken = "00:00:00"
                };
            }

            // Get unique questions
            DataTable dtUniqRecords = DS1.DefaultView.ToTable(true, "QuestionID");
            int totalQuestions = dtUniqRecords.Rows.Count;

            int correctAnswers = 0;

            // Calculate correct answers
            foreach (DataRow questionRow in dtUniqRecords.Rows)
            {
                if (questionRow["QuestionID"] != DBNull.Value)
                {
                    int questionId = Convert.ToInt32(questionRow["QuestionID"]);

                    // Check if this question was answered correctly
                    // This logic may need to be adjusted based on your data structure
                    var questionAnswers = DS1.Select($"QuestionID = {questionId}");

                    foreach (DataRow answerRow in questionAnswers)
                    {
                        // Check if the selected answer is correct
                        if (answerRow["IsCorrect"] != DBNull.Value && Convert.ToBoolean(answerRow["IsCorrect"]))
                        {
                            correctAnswers++;
                            break; // Count each question only once
                        }
                    }
                }
            }

            // Calculate score percentage
            int scorePercentage = totalQuestions > 0 ? (correctAnswers * 100) / totalQuestions : 0;

            // Determine pass/fail (assuming 70% is passing)
            bool isPassed = scorePercentage >= 70;

            // Calculate time taken (this may need adjustment based on your data structure)
            string timeTaken = "00:00:00";
            if (DS1.Rows.Count > 0 && DS1.Rows[0]["TimeTaken"] != DBNull.Value)
            {
                timeTaken = DS1.Rows[0]["TimeTaken"].ToString();
            }

            return new
            {
                TotalQuestions = totalQuestions,
                CorrectAnswers = correctAnswers,
                ScorePercentage = scorePercentage,
                IsPassed = isPassed,
                TimeTaken = timeTaken
            };
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

            // Return default values on error
            return new
            {
                TotalQuestions = 0,
                CorrectAnswers = 0,
                ScorePercentage = 0,
                IsPassed = false,
                TimeTaken = "00:00:00"
            };
        }
    }

    [HttpPost]
    public IActionResult SaveTestData(int trainingId, string action)
    {
        try
        {
            int currentUserID = _UserDetails?.UserID ?? 0;

            _CVLogger.LogInfo($"SaveTestData called - trainingId: {trainingId}, action: {action}, userId: {currentUserID}");

            if (action == "start")
            {
                return Json(new { success = true, message = "Test started successfully." });
            }

            return Json(new { success = false, message = "Invalid action." });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "An error occurred while saving test data." });
        }
    }

    /// <summary>
    /// Get all answered questions for a user and training
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="trainingId">Training ID</param>
    /// <returns>List of answered question IDs</returns>
    private List<int> GetAnsweredQuestions(int userId, int trainingId)
    {
        try
        {
            string answeredQuestionsKey = $"AnsweredQuestions_{userId}_{trainingId}";
            var answeredQuestionsJson = HttpContext.Session.GetString(answeredQuestionsKey);

            if (!string.IsNullOrEmpty(answeredQuestionsJson))
            {
                return System.Text.Json.JsonSerializer.Deserialize<List<int>>(answeredQuestionsJson) ?? new List<int>();
            }

            return new List<int>();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return new List<int>();
        }
    }

        /// <summary>
        /// Parses training duration string to seconds
        /// Supports formats like: "60 minutes", "1 hour", "90 mins", "2 hours", "120", etc.
        /// </summary>
        /// <param name="trainingDuration">Training duration string</param>
        /// <returns>Duration in seconds, defaults to 3600 (1 hour) if parsing fails</returns>
        private int ParseTrainingDurationToSeconds(string trainingDuration)
        {
            if (string.IsNullOrWhiteSpace(trainingDuration))
            {
                return 3600; // Default 1 hour
            }

            try
            {
                // Clean up the input string
                string duration = trainingDuration.Trim().ToLower();

                // Extract numeric value
                var match = System.Text.RegularExpressions.Regex.Match(duration, @"(\d+(?:\.\d+)?)");
                if (!match.Success)
                {
                    return 3600; // Default if no number found
                }

                if (!double.TryParse(match.Groups[1].Value, out double numericValue))
                {
                    return 3600; // Default if parsing fails
                }

                // Determine the unit and convert to seconds
                if (duration.Contains("hour") || duration.Contains("hr"))
                {
                    return (int)(numericValue * 3600); // hours to seconds
                }
                else if (duration.Contains("minute") || duration.Contains("min"))
                {
                    return (int)(numericValue * 60); // minutes to seconds
                }
                else if (duration.Contains("second") || duration.Contains("sec"))
                {
                    return (int)numericValue; // already in seconds
                }
                else
                {
                    // If no unit specified, assume minutes
                    return (int)(numericValue * 60);
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return 3600; // Default 1 hour on any error
            }
        }
    
}

