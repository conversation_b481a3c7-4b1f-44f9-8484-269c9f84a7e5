﻿
@{
    ViewBag.Title = "ViewDependentOrgStructure";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">View Dependent Org Structure</h6>
    <div class="row" style="width:80%">
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                <select class="form-select form-select-sm" aria-label="Default select example">
                    <option selected>All Organizations</option>
                    <option value="1">PTS</option>
                    <option value="2">TCS</option>
                    <option value="3">Continuity Vault</option>
                </select>
            </div>
        </div>
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                <select class="form-select form-select-sm" aria-label="Default select example">
                    <option selected>All Units</option>
                    <option value="1">PTS</option>
                    <option value="2">TCS</option>
                    <option value="3">Continuity Vault</option>
                </select>
            </div>
        </div>
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-department"></i></span>
                <select class="form-select form-select-sm" aria-label="Default select example">
                    <option selected>All Departments</option>
                    <option value="1">PTS</option>
                    <option value="2">TCS</option>
                    <option value="3">Continuity Vault</option>
                </select>
            </div>
        </div>
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
                <select class="form-select form-select-sm" aria-label="Default select example">
                    <option selected>All SubDepartments</option>
                    <option value="1">PTS</option>
                    <option value="2">TCS</option>
                    <option value="3">Continuity Vault</option>
                </select>
            </div>
        </div>
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control" placeholder="Search">
            </div>
        </div>
    </div>
</div>

<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover align-top" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th>#</th>
                <th>Process&nbsp;Name</th>
                <th>Process&nbsp;Owner</th>
                <th>RTO/MTPoD/RPO</th>
                <th>Org&nbsp;Level</th>
                <th>Dependent Org Structure</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>1</td>
                <td>
                    <div class="d-grid">
                        <span class="fw-medium text-warning">PRC-2022-152</span>
                        <span>Entity Type :<span class="fw-medium">Site</span></span>
                        <span>Entity Name : <span class="fw-medium">World Trade Center</span></span>
                        <span>IsCritical  : <span class="text-success fw-medium">No</span></span>
                        <span>Status   : <span class="text-info fw-medium">Initiated</span></span>
                    </div>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr>
                                <td class="fw-medium"><i class="cv-user"></i></td>
                                <td> : </td>
                                <td>Kishan Kadam</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr title="RTO">
                                <td class="fw-semibold"><i class="cv-RTO"></i></td>
                                <td> : </td>
                                <td>
                                    NA
                                </td>
                            </tr>
                            <tr title="MTPOD">
                                <td class="fw-semibold"><i class="cv-user"></i> </td>
                                <td>:</td>
                                <td>NA</td>
                            </tr>
                            <tr title="RPO">
                                <td class="fw-semibold"><i class="cv-rpo"></i></td>
                                <td>:</td>
                                <td>25 Minute(s)</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr title="Organization">
                                <td class="fw-semibold"><i class="cv-company"></i></td>
                                <td> : </td>
                                <td>
                                    Perpetuuiti
                                </td>
                            </tr>
                            <tr title="Unit">
                                <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                <td>:</td>
                                <td>Secretary General Group</td>
                            </tr>
                            <tr title="Department">
                                <td class="fw-semibold"><i class="cv-department"></i> </td>
                                <td>:</td>
                                <td>Secretary General</td>
                            </tr>
                            <tr title="Sub Department">
                                <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                <td>:</td>
                                <td>SG OfficeA</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table class="w-100 table table-bordered mb-0">
                        <thead>
                            <tr>
                                <th class="rounded-0 py-1">#</th>
                                <th class="py-1">Activity</th>
                                <th class="py-1">Description</th>
                                <th class="py-1">RTO</th>
                                <th class="py-1">MAO</th>
                                <th class="rounded-0 py-1">RPO</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="p-0">
                                    <div class="accordion accordion-flush" id="accordionFlushExample">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed p-2" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo1" aria-expanded="false" aria-controls="flush-collapseTwo">
                                                    <small class="pe-2">No records to display.</small>
                                                </button>
                                            </h2>
                                            <div id="flush-collapseTwo1" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                <div class="accordion-body p-0">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td>2</td>
                <td>
                    <div class="d-grid">
                        <span class="fw-medium text-warning">PRC-2022-153</span>
                        <span>Entity Type :<span class="fw-medium">Site</span></span>
                        <span>Entity Name : <span class="fw-medium">World Trade Center</span></span>
                        <span>IsCritical  : <span class="text-success fw-medium">No</span></span>
                        <span>Status   : <span class="text-info fw-medium">Initiated</span></span>
                    </div>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr>
                                <td class="fw-medium"><i class="cv-user"></i></td>
                                <td> : </td>
                                <td>Kishan Kadam</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr title="RTO">
                                <td class="fw-semibold"><i class="cv-RTO"></i></td>
                                <td> : </td>
                                <td>
                                    NA
                                </td>
                            </tr>
                            <tr title="MTPOD">
                                <td class="fw-semibold"><i class="cv-user"></i> </td>
                                <td>:</td>
                                <td>NA</td>
                            </tr>
                            <tr title="RPO">
                                <td class="fw-semibold"><i class="cv-rpo"></i></td>
                                <td>:</td>
                                <td>25 Minute(s)</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr title="Organization">
                                <td class="fw-semibold"><i class="cv-company"></i></td>
                                <td> : </td>
                                <td>
                                    Perpetuuiti
                                </td>
                            </tr>
                            <tr title="Unit">
                                <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                <td>:</td>
                                <td>Secretary General Group</td>
                            </tr>
                            <tr title="Department">
                                <td class="fw-semibold"><i class="cv-department"></i> </td>
                                <td>:</td>
                                <td>Secretary General</td>
                            </tr>
                            <tr title="Sub Department">
                                <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                <td>:</td>
                                <td>SG OfficeA</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table class="w-100 table table-bordered mb-0">
                        <thead>
                            <tr>
                                <th class="rounded-0 py-1">#</th>
                                <th class="py-1">Activity</th>
                                <th class="py-1">Description</th>
                                <th class="py-1">RTO</th>
                                <th class="py-1">MAO</th>
                                <th class="rounded-0 py-1">RPO</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="p-0">
                                    <div class="accordion accordion-flush" id="accordionFlushExample">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed p-2" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                                    <small class="pe-2">Question: WHAT ARE THE "MUST DO" RECOVERIES RELATED ACTIVITIES TO BE PERFORMED ONCE THE PROCESS IS RESUMED?</small>
                                                </button>
                                            </h2>
                                            <div id="flush-collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                <div class="accordion-body p-0">
                                                    <table class="w-100 table mb-0 table-borderless">
                                                        <tbody>
                                                            <tr class="small">
                                                                <td>1</td>
                                                                <td>Responding to incidents Enact and execute the first line of defense to respond to incidents starting with the Emergency Response Plan.</td>
                                                                <td>NA</td>
                                                                <td>NA</td>
                                                                <td>NA</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed p-2" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                                                    <small class="pe-2">Question: WHAT ARE THE "MUST DO" RECOVERIES RELATED ACTIVITIES TO BE PERFORMED ONCE THE PROCESS IS RESUMED?</small>
                                                </button>
                                            </h2>
                                            <div id="flush-collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                <div class="accordion-body p-0">
                                                    <table class="w-100 table mb-0 table-borderless">
                                                        <tbody>
                                                            <tr class="small">
                                                                <td>1</td>
                                                                <td>Responding to incidents Enact and execute the first line of defense to respond to incidents starting with the Emergency Response Plan. Mobilize resources and assemble required response teams.</td>
                                                                <td>NA</td>
                                                                <td>NA</td>
                                                                <td>NA</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</div>
