﻿
@{
    ViewData["Title"] = "Table Access";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>

    .tabel-access-card {
        border-radius: 10px;
        border: 0px;
        background: #FFF9FB;
        padding: 12px 10px;
        box-shadow: 1px 5px 10px #e3dddd;
    }
</style>


<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">Table Access</h6>
        <div class="d-flex gap-3 justify-content-end align-items-end">
            <div class="input-group w-30">
                <input class="form-control" type="text" placeholder="Search" />
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
            </div>
            <div class="form-check form-switch me-0 d-flex align-items-center gap-2 w-50" style="min-height:0px">
                <input class="form-check-input" type="checkbox" role="switch" id="switchCheckChecked" checked>
                <label class="form-check-label" for="switchCheckChecked">Select All</label>
            </div>
            <button class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</div>
<div class="Page-Condant card border-0" style="height: calc(100vh - 108px);">
    <div class="card-body">
        <div class="g-4 row row-cols-xl-4 row-cols-3" id="table-access">
        </div>
    </div>

</div>


<script>
    document.addEventListener("DOMContentLoaded", function () {
      const container = document.getElementById("table-access");

      for (let i = 1; i <= 15; i++) {
        const col = document.createElement("div");
        col.className = "col d-grid";

        col.innerHTML = `
          <div class="d-flex tabel-access-card">
            <div class="custom-control-label w-100 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
              <div class="col-8" style="display: inline-flex;">
                <span class="fs-6">
                  <i class="cv-calculated me-1 fs-5"></i>about_cv_${i}
                </span>
              </div>
              <div class="col-4" style="display:flex;justify-content:end;align-items:center">
                <div class="form-check form-switch" style="min-height:0px">
                  <input class="form-check-input" type="checkbox" checked="checked" role="switch" />
                </div>
              </div>
            </div>
          </div>
        `;

        container.appendChild(col);
      }
    });
</script>

