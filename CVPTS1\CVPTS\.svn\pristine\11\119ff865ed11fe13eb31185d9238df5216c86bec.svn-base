﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Areas.BCMReports.ReportTemplate;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text.Json;


namespace BCM.UI.Areas.BCMReports.Controllers;
[Area("BCMReports")]
public class ViewBIAReportController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    public static string ReportGeneratedBy = string.Empty;

    public ViewBIAReportController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    public IActionResult ViewBIAReport()
    {
        try
        {
            var orgList = _Utilities.PupulateOrganisation(_UserDetails?.OrgGroupID.ToString() ?? "1", _UserDetails?.UserRoleID.ToString() ?? "1");
            ViewBag.OrganizationList = new SelectList(orgList, "Id", "OrganizationName");

            _CVLogger.LogInfo($"ViewBIAReport loaded with {orgList?.Count ?? 0} organizations");

            return View();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            ViewBag.OrganizationList = new SelectList(new List<object>(), "Id", "OrganizationName");
            return View();
        }
    }


    [HttpGet]
    public IActionResult GetBIAReportData(int organizationId = 0, int unitId = 0, int departmentId = 0, int subDepartmentId = 0)
    {
        try
        {
            ReportGeneratedBy = _UserDetails?.UserName?.ToString() ?? "Unknown User";
            _CVLogger.LogInfo("Entered into the method GetBIAReportData successfully.");

            // Use default values if not provided - 0 means show all records
            if (organizationId <= 0)
            {
                organizationId = _UserDetails?.OrgID ?? 1; // Use 1 instead of 0 for better compatibility
            }

            _CVLogger.LogInfo($"BIA Report Data requested with filters - OrgId: {organizationId}, UnitId: {unitId}, DeptId: {departmentId}, SubDeptId: {subDepartmentId}");

            // Test if we can get any processes
            try
            {
                var testProcesses = _ProcessSrv.GetBIAProcess_OrgUnitLevel(organizationId);
                _CVLogger.LogInfo($"Found {testProcesses?.Count ?? 0} BIA processes for organization {organizationId}");
            }
            catch (Exception testEx)
            {
                _CVLogger.LogErrorApp(new Exception($"Error testing BIA processes: {testEx.Message}"));
            }

            // Create a simple data structure for BIA report
            var biaReportData = new
            {
                OrganizationId = organizationId,
                UnitId = unitId,
                DepartmentId = departmentId,
                SubDepartmentId = subDepartmentId,
                GeneratedBy = _UserDetails?.UserName?.ToString() ?? "Unknown User",
                GeneratedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                ShowAllRecords = (organizationId <= 0 && unitId <= 0 && departmentId <= 0 && subDepartmentId <= 0)
            };

            string reportDataJson = Newtonsoft.Json.JsonConvert.SerializeObject(biaReportData);
            _CVLogger.LogInfo($"BIA Report Data JSON: {reportDataJson}");

            return Json(new { success = true, data = reportDataJson });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, data = ex.Message.ToString() });
        }
    }

    [HttpPost]
    public ActionResult LoadReport([FromBody] JsonElement request)
    {
        try
        {
            string reportName = request.GetProperty("reportName").GetString() ?? "BIAReport";
            string responseData = request.GetProperty("responseData").GetString() ?? "{}";

            _CVLogger.LogInfo($"LoadReport called with reportName: {reportName}");
            _CVLogger.LogInfo($"LoadReport responseData: {responseData}");

            ViewData[reportName + "Data"] = responseData;

            // Use the correct partial view name
            return PartialView("_BIAReport", responseData);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpGet]
    public JsonResult GetAllUnits(int organizationId)
    {
        try
        {
            var objUnitList = _Utilities.BindUnit(organizationId);
            return Json(new { success = true, data = objUnitList });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message.ToString() });
        }
    }

    [HttpGet]
    public JsonResult GetAllDepartments(int unitId)
    {
        try
        {
            var objDepartmentList = _ProcessSrv.GetDepartmentByUnitId(unitId);
            return Json(new { success = true, data = objDepartmentList });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message.ToString() });
        }
    }

    [HttpGet]
    public JsonResult GetAllSubDepartments(int departmentId)
    {
        try
        {
            var objSubDepartmentList = _ProcessSrv.GetSubFunctionListByFunctionID(departmentId.ToString());
            return Json(new { success = true, data = objSubDepartmentList });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message.ToString() });
        }
    }

    public void PopulateDropdown()
    {
        try
        {
            ViewBag.DepartmentInfo = new SelectList(_Utilities.GetDepartmentAllListForDropdown(), "DepartmentID", "DepartmentName");
            ViewBag.SubFunction = new SelectList(_Utilities.GetAllSubDepartmentListDropdown(), "SubFunctionID", "SubFunctionName");
            ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails?.OrgGroupID.ToString() ?? "1"), "Id", "OrganizationName");
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
            ViewBag.OrgUnit = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails?.OrgID ?? 1), "UnitID", "UnitName");
            ViewBag.OrgGroup = new SelectList(_Utilities.GetOrgGroupList(), "OrgGroupID", "OrganizationGroupName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    // Test action to create BIA report directly
    [HttpGet]
    public IActionResult TestBIAReport(int orgId = 0, int deptId = 0)
    {
        try
        {
            orgId = orgId > 0 ? orgId : (_UserDetails?.OrgID ?? 1);

            // Test creating BIA report directly
            var report = new BIAReport(deptId, orgId, 0, 0);

            return View("_BIAReport", report);
        }
        catch (Exception ex)
        {
            return Json(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }

    // Test action to debug data retrieval
    [HttpGet]
    public IActionResult TestData(int orgId = 0, int deptId = 0)
    {
        try
        {
            orgId = orgId > 0 ? orgId : (_UserDetails?.OrgID ?? 1);

            var result = new
            {
                UserDetails = new
                {
                    OrgID = _UserDetails?.OrgID,
                    UserRole = _UserDetails?.UserRole,
                    UserID = _UserDetails?.UserID
                },
                Departments = _Utilities.GetDepartmentAllListForDropdown()?.Select(d => new { d.DepartmentID, d.DepartmentName }).ToList(),
                BIAProcesses = _ProcessSrv.GetBIAProcess_OrgUnitLevel(orgId)?.Take(5).Select(p => new { p.ProcessID, p.ProcessName, p.DepartmentID }).ToList(),
                Parameters = new { OrgId = orgId, DeptId = deptId }
            };

            return Json(result);
        }
        catch (Exception ex)
        {
            return Json(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }
}

