﻿@model IEnumerable<BCM.BusinessClasses.RiskTrendInfo>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewBag.Title = "Risk Trend Analysis";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var selectedOrgID = ViewBag.selectedOrgID;
}
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Risk Trend Analysis</h6>
    <div class="d-flex gap-2 justify-content-end align-items-end" style="width:85%;">
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
            <select class="form-select form-control" aria-label="Default select example" id="ddlTrend">
                <option value="-1" selected>-- All Trend --</option>
                <option value="0">No Change</option>
                <option value="1">Upward</option>
                <option value="2">Downward</option>
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
            <select class="form-select form-control" aria-label="Default select example" id="ddlImpReq">
                <option value="-1" selected>-- All Improvement Required --</option>
                <option value="0">No</option>
                <option value="1">Yes</option>
                <option value="2">NA</option>
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-department"></i></span>
            <select class="form-select form-control" aria-label="Default select example" id="ddlResRiskSev">
                <option value="0" selected>-- All Residual Risk Severity --</option>
                @{
                    foreach (var objRiskSeverity in ViewBag.RiskSeverity)
                    {
                                                                    <option value="@objRiskSeverity.Value">@objRiskSeverity.Text</option>
                    }
                }
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
            <select class="form-select form-control" aria-label="Default select example" id="ddlImpStatus">
                <option value="0" selected>-- All Improvement Status --</option>
                <option value="1">Draft</option>
                <option value="2">Approved</option>
                <option value="3">In Progress</option>
                <option value="4">Completed</option>
            </select>
        </div>
        <div class="input-group ">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
    </div>
</div>

<div id="partialContainer">
    @await Html.PartialAsync("_FilterTableRecords", Model)
</div>

<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">            
            <div class="modal-body attachImprovementBody">                
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->

@* <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#ImpromentModal">clclcccm</button> *@

<!--Improment Modal -->

<div class="modal fade" id="ImpromentModal" tabindex="-1" aria-labelledby="exampleModalLabel" data-bs-backdrop="static" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Imporvement Plan For - RAO-2024</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body createImprovementBody">                
            </div>            
        </div>
    </div>
</div>



@section Scripts {
    <script>
        $(document).ready(function () {

            var iRiskID = '';
            var iTrend = '';
            var iRiskEntityID = '';
            var strRiskName = '';
            var strRiskCode = '';
            var strVersion = '';

            $('#ddlTrend,#ddlImpReq,#ddlResRiskSev,#ddlImpStatus').change(function () {
                var ddlTrend = $('#ddlTrend').val();
                var ddlImpReq = $('#ddlImpReq').val();
                var ddlResRiskSev = $('#ddlResRiskSev').val();
                var ddlImpStatus = $('#ddlImpStatus').val();

                $.ajax({
                    url: '@Url.Action("FilterTableRecords", "RiskTrendAnalysis")',
                    type: 'GET',
                    data: { iTrend: ddlTrend, iImpReq: ddlImpReq, iResRiskSev: ddlResRiskSev, iImpStatus: ddlImpStatus },
                    success: function (data) {
                        $('#partialContainer').html(data);
                        var table = $('.data-table').DataTable({
                            language: {
                                paginate: {
                                    next: '<i class="cv-right-arrow"></i>',
                                    previous: '<i class="cv-left-arrow"></i>'
                                }
                            },
                            dom: '<"pull-left"B><"pull-right"f>rt<"row px-2 pb-2 align-items-center g-0"<"col"l><"col text-center"i><"col"p>>',
                            scrollY: true,
                            deferRender: true,
                            scroller: true,
                        });
                        $('#search-inp').on('keyup', function () {
                            table.search($(this).val()).draw();
                        });
                    },
                    error: function (error) {
                        console.log("Error : ", error);
                    }
                });
            });

            $('#partialContainer').on('click', '.btnAttach', function () {
                var riskId = $(this).data('id');
            })            

            $('.btnAttach').on('click',function(e){
                e.preventDefault();
                iRiskID = $(this).data('id');
                iTrend = $(this).data('trend');
                iRiskEntityID = $(this).data('risk-entity-id');
                strRiskName = $(this).data('risk-name');
                strRiskCode = $(this).data('risk-code');
                strVersion = $(this).data('version')
                //$.get('/BCMCorrectivePreventiveActions/AttachActionsToRisk/AttachActionsToRisk',
                $.get('@Url.Action("AttachActionsToRisk", "AttachActionsToRisk", new { area = "BCMCorrectivePreventiveActions" })',
                    {
                        iRiskID: iRiskID,
                        iTrend: iTrend,
                        iRiskEntityID: iRiskEntityID,
                        strRiskName: strRiskName,
                        strRiskCode: strRiskCode,
                        strVersion: strVersion
                    }, function (data) {
                        $('.attachImprovementBody').html(data);
                        $('#CreateModal').modal('show');
                        var msg = '@ViewBag.PageTitle';
                    })
            })

            $(document).on('click', '#btnAdd', function (e) {
                e.preventDefault();
                //$.get('/BCMCorrectivePreventiveActions/CreateRiskMitigation/CreateRiskMitigation', {
                $.get('@Url.Action("CreateRiskMitigation", "CreateRiskMitigation", new { area = "BCMCorrectivePreventiveActions" })', {
                    iTrend: iTrend,
                    iRiskID: iRiskID,
                    strRiskName: strRiskName,
                    strRiskCode: strRiskCode,
                    strVersion: strVersion
                },
                function (data) {
                    $('.createImprovementBody').html(data);
                    $('#ImpromentModal').modal('show');
                        $('input[name="IsCorrective"]').eq(3).prop('checked', true);
                        //$('input[name="IsCorrective"]').prop('disabled', true);
                })
            })

            $(document).on('click', '#riskID', function (e) {
                e.preventDefault();
                //$.get('/BCMCorrectivePreventiveActions/CreateRiskMitigation/EditRiskMitigation', {
                $.get('@Url.Action("EditRiskMitigation", "CreateRiskMitigation", new { area = "BCMCorrectivePreventiveActions" })', {
                    iID: $(this).data('risk-id')
                },
                    function (data) {
                        $('.createImprovementBody').html(data);
                        $('#ImpromentModal').modal('show');
                        $('input[name="IsCorrective"]').eq(3).prop('checked', true);
                    })
            })
        });


        // DataTable Script
        $(document).ready(function () {
            var table = $('.data-table').DataTable({
                language: {
                    paginate: {
                        next: '<i class="cv-right-arrow"></i>',
                        previous: '<i class="cv-left-arrow"></i>'
                    }
                },
                dom: '<"pull-left"B><"pull-right"f>rt<"row px-2 pb-2 align-items-center g-0"<"col"l><"col text-center"i><"col"p>>',
                scrollY: true,
                deferRender: true,
                scroller: true,
            });
            $('#search-inp').on('keyup', function () {
                table.search($(this).val()).draw();
            });
        });

    </script>
}