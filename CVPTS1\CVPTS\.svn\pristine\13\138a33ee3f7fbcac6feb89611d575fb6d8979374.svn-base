{
  "Logging": {
    "LogLevel": { // No provider, LogLevel applies to all the enabled providers.
      "Default": "Error",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Warning"
    },
    "Debug": { // Debug provider.
      "LogLevel": {
        "Default": "Information" // Overrides preceding LogLevel:Default setting.
      }
    },
    "Console": {
      "IncludeScopes": true,
      "LogLevel": {
        "Microsoft.AspNetCore.Mvc.Razor.Internal": "Warning",
        "Microsoft.AspNetCore.Mvc.Razor.Razor": "Debug",
        "Microsoft.AspNetCore.Mvc.Razor": "Error",
        "Default": "Information"
      }
    },
    "EventSource": {
      "LogLevel": {
        "Microsoft": "Information"
      }
    },
    "EventLog": {
      "LogLevel": {
        "Microsoft": "Information"
      }
    },
    "AzureAppServicesFile": {
      "IncludeScopes": true,
      "LogLevel": {
        "Default": "Warning"
      }
    },
    "AzureAppServicesBlob": {
      "IncludeScopes": true,
      "LogLevel": {
        "Microsoft": "Information"
      }
    },
    "ApplicationInsights": {
      "LogLevel": {
        "Default": "Information"
      }
    }
  },

  "AllowedHosts": "*",
  "Connectionstrings": {
    //"MsSql": "OajNMxcbtsZULcciF3R+Qifu9B7Woen+TCoRz+oHnY/LaOcQVO+OtHjjJlNA0fr3fpN63k9d+h+yZoiendnvz3wVvyIQyikOA1WoBX0cB79CQbr9W3xB1Cy+8YJ0UFuU7Qd7wJGYQAeArZsFLs+G54HpuzRVZ4l00beR+mnTneM=",
    "MsSql": "vMNq0TS2j79LeoyRMV9aX0eFe1ux4aK/5dtdAI0oLHEEiOom/jXNYZ5dq7YkxWgt4luM5ikXdQFXvq7amkq511J+fI/iiAAI+4WrpX+7xoItKQAuvbpvLFjkAVvLAloLalHa3Y+4ylE+p2Q/6BiVfKtXjiubmNVy9H8H/3Xij3k=",
    "MySql": "wxYcunG9J8QexXLiehwNLNMnI84MGlJpqSldb40v/h5e1xGauI5v6Il1nxYiUYUI1i7ZRnikI3KugvdzhEkPu+YrywJFybsRkBpR6hH9B15W4/iPw7q4m+DELpZegad7HtKKxlYv/NJk0g5djM8y6WkpcUhwlJ7AM1YnU/yiDqE=",
    //"MySql": "wxYcunG9J8QexXLiehwNLNMnI84MGlJpqSldb40v/h5e1xGauI5v6Il1nxYiUYUI1i7ZRnikI3KugvdzhEkPu+YrywJFybsRkBpR6hH9B15W4/iPw7q4m+DELpZegad7HtKKxlYv/NJk0g5djM8y6WkpcUhwlJ7AM1YnU/yiDqE=",
    //"MySql": "server=**************;User Id=root;password=root@123;Persist Security Info=True;database=cvroot_core;",
    //"MySql": "aHF3QNMvbe3mJKUMkVD+ctL+Z3TYftTpkbpw4OHM7eEeUvnZmOuVnw4Q/pAC5UY+4oHoYexuk5JIiHSI6XdZNfDBBv9wwSOK9gac+mv8tLGgYc+eL42m47Ffg7HlkTRorGOxhDd3lPmDLiu6kwsHQA==",

    "Oracle": "Data Source=*************;Initial Catalog=CVRoot;User ID=sa;Password=*********;TrustServerCertificate=True;",

    "localhost_CVRoot_Core_Connection": "XpoProvider=MSSqlServer;data source=*************;user id=sa;password=*********;initial catalog=CVRoot_Core;Persist Security Info=true"
  },
  "ConfigurationValue": {
    "DateFormat": "dd/MM/yyyy",
    "DateTimeFormat": "dd/MM/yyyy hh:mm:ss"
  },
  "Path": {
    "LogFilePath": "C:\\CVCore\\"
  },
  "UserRole": {
    "ProductAdminRole": "*",
    "SuperAdminRole" :  "**"
  },
  "ApiSettings": {
    "BaseUrl": "https://localhost:7161"
  },
  "Encryption": {
    "DefaultKey": "MySecretKey123456789012345678901234"
  }
}
