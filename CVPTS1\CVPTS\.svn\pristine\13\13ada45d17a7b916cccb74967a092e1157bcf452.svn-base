﻿@model IEnumerable<BCM.BusinessClasses.EntityReview>
@using BCM.Shared
@using BCM.BusinessClasses
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    int iIndex = 1;

    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Start Date</th>
                <th>End Date </th>
                <th>Next Review Date</th>
                <th>Status</th>
                <th>Reviewer</th>
            </tr>
        </thead>
        <tbody>
            @foreach (EntityReview item in Model)
            {
                <tr>
                    <td>
                        @iIndex
                    </td>
                    <td>@item.ReviewStartDate</td>
                    <td>@item.ReviewEndDate</td>
                    <td>@item.NextReviewDate</td>
                    @if (@item.Status == "1")
                    {
                        <td>

                            InProgress

                        </td>
                    }
                    else if (@item.Status == "2")
                    {
                        <td>
                            Completed
                        </td>
                    }
                    
                    <td>@item.ReviewerName</td>
                </tr>
                iIndex++;
            }
        </tbody>
    </table>
}