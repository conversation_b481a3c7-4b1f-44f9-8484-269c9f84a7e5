﻿@model BCM.BusinessClasses.DepartmentBIAEntities
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Configure Department BIA";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Configure Department BIA</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
        </div>
    </div>
</div>
<div class="Page-Condant card border-0">
    <div class="col-12">
        <div class="d-flex align-items-center gap-3 mb-2" style="border-bottom: 1px solid grey;padding: 15px;">
            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed"
                type="button" data-bs-toggle="collapse" data-bs-target="#Organisation" aria-expanded="false"
                aria-controls="collapseExample"><i class="align-middle"></i></span>@* class="cv-Plus align-middle" *@
            <h6 class="mb-0">Department Details</h6>
        </div>

        <form asp-action="SaveDepartmentSection" method="post">
            <div class="row p-3" id="Organisation">
                @* class="row collapse p-3" *@
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Department Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-department-name"></i></span>
                            <input type="hidden" asp-for="ID" />
                            <select id="ddlDepartment" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="DepartmentID" required>
                                <option selected disabled value="">-- All Departments --</option>
                                @foreach (var objDept in ViewBag.Department)
                                {
                                    <option value="@objDept.Value">@objDept.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Version</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-version"></i></span>
                            <input class="form-control" type="text" placeholder="Version" asp-for="Version" readonly />
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Approver</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-approver"></i></span>
                            <select id="ddlDepartment" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="ApproverID">
                                <option selected value="0">-- All Resources --</option>
                                @foreach (var objRes in ViewBag.ResourcesInfo)
                                {
                                    <option value="@objRes.Value">@objRes.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Author</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-user"></i></span>
                            <select id="ddlDepartment" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="Author">
                                <option selected value="0">-- All Resources --</option>
                                @foreach (var objRes in ViewBag.ResourcesInfo)
                                {
                                    <option value="@objRes.Value">@objRes.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">BIA Code</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                            <input class="form-control" type="text" placeholder="Enter BIA Code" asp-for="BIACode" />
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Co-Ordinator</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-BCP-coordinator"></i></span>
                            <select id="ddlDepartment" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="BCMCoOrdinatorID">
                                <option selected value="0">-- All Resources --</option>
                                @foreach (var objRes in ViewBag.ResourcesInfo)
                                {
                                    <option value="@objRes.Value">@objRes.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Alt Co-Ordinator</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-BCP-coordinator"></i></span>
                            <select id="ddlDepartment" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="BCMAltCoOrdinatorID">
                                <option selected value="0">-- All Resources --</option>
                                @foreach (var objRes in ViewBag.ResourcesInfo)
                                {
                                    <option value="@objRes.Value">@objRes.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Retension Period</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                            <select id="ddlDepartment" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="RetensionPeriodID">
                                <option selected value="1">Annual</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Document ID</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                            <input class="form-control" type="text" placeholder="Enter Process Name"
                                asp-for="DocumentID" />
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-12 col-xl-12">
                    <div class="form-group">
                        <label class="form-label">Geographical Applicability</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <textarea class="form-control" placeholder="NA" asp-for="GeographicalApp"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-12 col-xl-12">
                    <div class="form-group">
                        <label class="form-label">Impact to organization if this department is not available for one
                            day</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <textarea class="form-control" placeholder="NA" asp-for="ImpactToOrganisation"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-12 col-xl-12">
                    <div class="form-group">
                        <label class="form-label">The maximum time period the organization can function without this
                            department</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <textarea class="form-control" placeholder="NA" asp-for="MaximumTimePeriod"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <textarea class="form-control" placeholder="NA" readonly></textarea>
                        </div>
                    </div>
                </div>
                <div class="footer d-flex justify-content-between">
                    <span></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1"
                            data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </div>
        </form>

        <div class="d-flex align-items-center gap-3 my-2" style="border-bottom: 1px solid grey;padding: 15px;">
            <span class="toggle-password" role="button" data-bs-toggle="collapse"
                data-bs-target="#Authentication_settings" aria-expanded="false" aria-controls="collapseExample">
                <i class="align-middle"></i>@* class="cv-Plus align-middle" *@
            </span>
            <h6 class="mb-0">Location Details </h6>
        </div>

        <form asp-action="SaveLocationSection" method="post">
            <div class="row p-3" id="Authentication_settings">
                @* class="row collapse p-3" *@
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Primary Site / Building / Office</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-primary-facility"></i></span>
                            <select id="ddlPrimaryFacilities" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="PrimarySiteID">
                                <option selected value="0">-- All Site --</option>
                                @foreach (var objFacilities in ViewBag.Facilities)
                                {
                                    <option value="@objFacilities.Value">@objFacilities.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Secondary Site / Building / Office</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-secondary-facility"></i></span>
                            <select id="ddlSecondaryFacilities" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="AlternateSiteID">
                                <option selected value="0">-- All Sites --</option>
                                @foreach (var objFacilities in ViewBag.Facilities)
                                {
                                    <option value="@objFacilities.Value">@objFacilities.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Staff Location / City</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-platform-location"></i></span>
                            <textarea class="form-control" placeholder="NA" asp-for="StaffLocation" required></textarea>
                        </div>
                    </div>
                </div>
                <div class="footer d-flex justify-content-between">
                    <span></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1"
                            data-bs-dismiss="modal">Close</button>
                        <button id="btnsaveLocation" type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </div>
        </form>

        <div class="d-flex align-items-center gap-3 my-2" style="border-bottom: 1px solid grey;padding: 15px;">
            <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#Information"
                aria-expanded="false" aria-controls="collapseExample">
                <i class="align-middle"></i>@* class="cv-Plus align-middle" *@
            </span>
            <h6 class="mb-0">Custodian Information</h6>
        </div>
        <div class="row p-3" id="Information">@* class="row collapse p-3" *@
            <table class="table table-hover align-middle" name="">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Custodian Name</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            1
                        </td>
                        <td>
                            Na
                        </td>
                        <td>
                            <span class="btn-action btnEdit" type="button"><i class="cv-edit me-1"></i></span>
                            <span class="btn-action btnDelete" type="button"><i
                                    class="cv-delete text-danger"></i></span>
                        </td>
                    </tr>
                </tbody>
            </table>
            <form asp-action="SaveCustodianSection" method="post">
                <div class="col-md-6 col-lg-6 col-xl-6">
                    <div class="form-group">
                        <label class="form-label">Custodian</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                            <select id="example-multiple-selected" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="CustodianID">
                                <option value="0" selected>-- All Custodian --</option>
                                @foreach (var objRes in ViewBag.ResourcesInfo)
                                {
                                    <option value="@objRes.Value">@objRes.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="footer d-flex justify-content-between">
                    <span></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1"
                            data-bs-dismiss="modal">Close</button>
                        <button id="btnSaveCust" type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </form>

        </div>
        <div class="d-flex align-items-center gap-3 my-2" style="border-bottom: 1px solid grey;padding: 15px;">
            <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#Reviewer"
                aria-expanded="false" aria-controls="collapseExample">
                <i class="align-middle"></i>@* class="cv-Plus align-middle" *@
            </span>
            <h6 class="mb-0">Reviewer Details</h6>
        </div>
        <div class="row p-3" id="Reviewer">@* class="row collapse p-3" *@
            <table class="table table-hover align-middle" name="">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Rreviwer Name</th>
                        <th>Rreviwer Designation</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            1
                        </td>
                        <td>
                            Na
                        </td>
                        <td>
                            Na
                        </td>
                        <td>
                            Inititated
                        </td>
                        <td>
                            <span class="btn-action btnEdit" type="button"><i class="cv-edit me-1"></i></span>
                            <span class="btn-action btnDelete" type="button"><i
                                    class="cv-delete text-danger"></i></span>
                        </td>
                    </tr>

                </tbody>
            </table>
            <form asp-action="SaveReviewerSection" method="post">
                <div class="col-md-6 col-lg-6 col-xl-6">

                    <div class="form-group">
                        <label class="form-label">Reviewer</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                            <select id="ddlDepartment" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="ReviewerID">
                                <option selected value="0">-- All Resources --</option>
                                @foreach (var objRes in ViewBag.ResourcesInfo)
                                {
                                    <option value="@objRes.Value">@objRes.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="footer d-flex justify-content-between">
                    <span></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1"
                            data-bs-dismiss="modal">Close</button>
                        <button id="btnAddReviewer" type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </form>
        </div>

        <div class="d-flex align-items-center gap-3 my-2" style="border-bottom: 1px solid grey;padding: 15px;">
            <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#BriefDetail"
                aria-expanded="false" aria-controls="collapseExample">
                <i class="align-middle"></i>@* class="cv-Plus align-middle" *@
            </span>
            <h6 class="mb-0">Brief Detail of major incident(s) (if any) pertaining to this Department in the last 2
                years. Please specify the issue. How was it handled?</h6>
        </div>
        <div class="row p-3" id="BriegIssue">
            @* class="row collapse p-3" *@
            <table class="table table-hover align-middle" name="">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Location / City / Application</th>
                        <th>Issue</th>
                        <th>How it is handled</th>
                        <th>Revenue loss</th>
                        <th>Remarks</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            1
                        </td>
                        <td>
                            Na
                        </td>
                        <td>
                            Na
                        </td>
                        <td>
                            Na
                        </td>
                        <td>
                            Na
                        </td>
                        <td>Na</td>
                        <td>
                            <span class="btn-action btnEdit" type="button"><i class="cv-edit me-1"></i></span>
                            <span class="btn-action btnDelete" type="button"><i
                                    class="cv-delete text-danger"></i></span>
                        </td>
                    </tr>

                </tbody>
            </table>
            <form asp-action="SaveBriefIssueSection" method="post">
                <div class="col-md-12 col-lg-12 col-xl-12">
                    <div class="form-group">
                        <label class="form-label">Location / City / Application</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <textarea class="form-control" placeholder="NA" asp-for="LocationID"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-12 col-xl-12">
                    <div class="form-group">
                        <label class="form-label">Issue</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <textarea class="form-control" placeholder="NA" asp-for="Issue"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-12 col-xl-12">
                    <div class="form-group">
                        <label class="form-label">How it is handled</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <textarea class="form-control" placeholder="NA" asp-for="HowsItHandle"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-12 col-xl-12">
                    <div class="form-group">
                        <label class="form-label">Revenue loss for the organization due to incident(s) (if any)
                            pertaining to this Department</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <textarea class="form-control" placeholder="NA" asp-for="RevenueLoss"></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-12 col-xl-12">
                    <div class="form-group">
                        <label class="form-label">Remarks / Additional Information (if any)</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <textarea class="form-control" placeholder="NA" asp-for="Remarks"></textarea>
                        </div>
                    </div>
                </div>
                <div class="footer d-flex justify-content-between">
                    <span></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1"
                            data-bs-dismiss="modal">Close</button>
                        <button id="btnBriefIssue" type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </form>
        </div>
        <div class="footer d-flex justify-content-between">
            <span></span>
            <div>
                <button id="btnSendForApproval" type="submit" class="btn btn-primary btn-sm">SendForApproval</button>
                <button id="btnApprove" type="submit" class="btn btn-primary btn-sm">Approve</button>
                <button id="btnDisApprove" type="submit" class="btn btn-primary btn-sm">DisApprove</button>
                <button id="btnChangeVersion" type="submit" class="btn btn-primary btn-sm">Change Version</button>
            </div>
        </div>
    </div>
</div>
@section Scripts {
    <script type="text/javascript">
        $(document).ready(function () {
            $('#example-enableClickableOptGroups').multiselect({
                enableClickableOptGroups: true
            });
        });
    </script>
}