﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    /* Base nav-link style */
    .nav-underline .nav-link {
        position: relative;
        color: #000; /* Default text color */
        transition: all 0.3s ease;
        padding-bottom: 2px;
        font-weight: 500 !important;
    }

        /* Active tab with gradient text and bottom border */
        .nav-underline .nav-link.active {
            background: linear-gradient(242deg, rgba(230,56,117,1), rgba(50,2,132,1));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            border-bottom-color: transparent !important;
        }

            .nav-underline .nav-link.active::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 2px;
                background: linear-gradient(242deg, rgba(230,56,117,1), rgba(50,2,132,1));
            }
</style>
<div class="Page-Header d-flex align-items-center justify-content-between">
    <div class="d-flex align-items-center gap-5">
        <h6 class="Page-Title">
            Report List
        </h6>
        <ul class="nav nav-underline" id="pills-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Prebuild Report</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Custom Report</button>
            </li>
        </ul>

    </div>

    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal">
            <i class="cv-Plus" title="Create New"></i>Create
        </button>
    </div>
</div>

<div class="Page-Condant card border-0 ">
    <div class="tab-content" id="pills-tabContent">
        <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0">

            <table id="example" class="table table-hover" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">#</th>
                        <th>Report</th>
                        <th>User Name</th>
                        <th>Description</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="log-table-body">
                </tbody>
            </table>

        </div>
        <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">

            <table id="example" class="table table-hover" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">#</th>
                        <th>Report</th>
                        <th>User Name</th>
                        <th>Description</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="custom-table-body">
                </tbody>
            </table>






        </div>

    </div>
   

</div>



<div class="modal fade" id="AddModal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Report Configuration</h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-1">
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Choose Report Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-calculated"></i></span>
                                <select class="form-select form-control selectized">
                                    <option></option>
                                    <option>Report 1</option>
                                    <option>Report 2</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">User Mapping Type</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-user"></i></span>
                                <input type="text" class="form-control" placeholder="Enter User Mapping Type" name="database" value="">
                            </div>
                        </div>
                    </div>

                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Description" name="Description" value="">
                            </div>
                        </div>
                    </div>


                </div>

            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary">Cancel</button>
                <button class="btn btn-primary">Save</button>
            </div>
        </div>

    </div>
</div>


<script>
    document.addEventListener("DOMContentLoaded", function () {
      const tbody = document.getElementById("log-table-body");
      const customtbody = document.getElementById("custom-table-body");

      for (let i = 1; i <= 10; i++) {
        const tr = document.createElement("tr");

        // Generate a random profile image
        const imgUrl = `https://randomuser.me/api/portraits/men/${i + 30}.jpg`;

        tr.innerHTML = `
          <td>${i}</td>
          <td>Prebuild Report ${i}</td>
          <td>
            <div class="d-flex align-items-center gap-2">
              <img src="${imgUrl}" alt="User" width="30" height="30" class="rounded-circle" style="object-fit: cover;">
              <span>User ${i}</span>
            </div>
          </td>
          <td>Report 1 is Created by user ${i}</td>
          <td>
           <div class="d-flex align-items-center gap-2">
            
              <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#Modal" data-id="${i}">
                <i class="cv-delete text-danger" title="Delete"></i>
              </span>
            </div>
          </td>
        `;

        tbody.appendChild(tr);
      }


      for (let i = 1; i <= 10; i++) {
        const tr = document.createElement("tr");

        // Generate a random profile image
        const imgUrl = `https://randomuser.me/api/portraits/men/${i + 30}.jpg`;

        tr.innerHTML = `
          <td>${i}</td>
          <td>Custom Report ${i}</td>
          <td>
            <div class="d-flex align-items-center gap-2">
              <img src="${imgUrl}" alt="User" width="30" height="30" class="rounded-circle" style="object-fit: cover;">
              <span>User ${i}</span>
            </div>
          </td>
          <td>Report 1 is Created by user ${i}</td>
          <td>
           <div class="d-flex align-items-center gap-2">
              <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="#Modal" data-id="${i}">
                <i class="cv-edit" title="Edit"></i>
              </span>
              <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#Modal" data-id="${i}">
                <i class="cv-delete text-danger" title="Delete"></i>
              </span>
            </div>
          </td>
        `;

        customtbody.appendChild(tr);
      }







    });
</script>

