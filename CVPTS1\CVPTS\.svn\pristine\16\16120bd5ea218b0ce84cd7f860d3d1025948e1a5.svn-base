﻿@using BCM.BusinessClasses
@using BCM.Shared
@{
    ViewBag.Title = "Business Process Form";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@model BCM.BusinessClasses.BusinessProcessInfo
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor

<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">@ViewBag.Title</h6>
    </div>
</div>

<div class="card mt-2">
    <div class="card-body">
        <div>
            <table class="table table-hover">
                <tbody>
                    <a class="btn btn-primary btn-sm" onclick="history.go(-1); return false;" href="#">Back</a>

                    <a asp-action="BusinessProcessForm" class="btn btn-primary btn-sm" style="float:right" asp-controller="BusinessProcessForm" asp-route-strEntityTypeID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@Model.EntityTypeID.ToString())" asp-route-strRecordID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@Model.RecordID.ToString())" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@Model.ProcessID.ToString())">
                        Edit Process
                    </a>
                </tbody>
            </table>
        </div>
        <div class="d-flex justify-content-between gap-5 mb-2 p-3">
            <div class="w-75">
                <div>
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td>
                                    <i class="cv-process-code me-1"></i>Process Name
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.ProcessName ( @Model.ProcessCode )
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-department me-1"></i>Department Name
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.DepartmentName
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-user"></i>Owner
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.ProcessOwner
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-organization me-1"></i>RTO
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Utilities.GetFormattedRTO(Convert.ToInt32(@Model.OwnerRTO))
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-status me-1"></i>Status
                                </td>
                                <td>:</td>
                                <td class="text-info">
                                    @if (@Model.Status == Convert.ToInt32((int)BCM.Shared.BCPEnum.ApprovalType.Initiated))
                                    {
                                        <p class="text-info fw-semibold">
                                            Initiated
                                        </p>
                                    }
                                    @if (@Model.Status == Convert.ToInt32((int)BCM.Shared.BCPEnum.ApprovalType.WaitingforApproval))
                                    {
                                        <p class="text-warning fw-semibold">
                                            WaitingForApprove
                                        </p>
                                    }
                                    @if (@Model.Status == Convert.ToInt32((int)BCM.Shared.BCPEnum.ApprovalType.Approved))
                                    {
                                        <p class="text-success fw-semibold">
                                            Approved
                                        </p>
                                    }
                                    @if (@Model.Status == Convert.ToInt32((int)BCM.Shared.BCPEnum.ApprovalType.Disapproved))
                                    {
                                        <p class="text-danger fw-semibold">
                                            DisApproved
                                        </p>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-entity-type me-1"></i>Review Date
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.ReviewDate;
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="vr"></div>
            <div class="w-75">
                <div>
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td>
                                    <i class="cv-unit me-1"></i>Organization
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.OrgName
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-subdepartment me-1"></i>Sub Department
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.SubFunctionName
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-user"></i>Alt Owner
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.AltProcessOwner
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-unit me-1"></i>MAO
                                </td>
                                <td>:</td>
                                <td class="text-danger">
                                    @Utilities.GetFormattedRTO(Convert.ToInt32(@Model.OwnerMTR))
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-subdepartment me-1"></i>Profile Name
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.ProfileName
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-business-process me-1"></i>Is Critical
                                </td>
                                <td>:</td>
                                @if (@Model.IsCritical == 1)
                                {
                                    <td class="text-danger">
                                        Yes
                                    </td>
                                }
                                else
                                {
                                    <td class="text-success">
                                        No
                                    </td>
                                }

                            </tr>


                        </tbody>
                    </table>
                </div>
            </div>
            <div class="vr"></div>
            <div class="w-75">
                <div>
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td>
                                    <i class="cv-subdepartment me-1"></i>Unit Name
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.UnitName
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-user"></i>Approver
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.ApproverName
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-unit me-1"></i>RPO
                                </td>
                                <td>:</td>
                                <td class="text-danger">
                                    @Utilities.GetFormattedRPO(Convert.ToInt32(@Model.RPO))

                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="cv-subdepartment me-1"></i>Version
                                </td>
                                <td>:</td>
                                <td class="text-primary">
                                    @Model.Version
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>BIA/Dependencies</th>
                    <th>Completion Status</th>
                    <th>Created By Name</th>
                    <th>Changed By Name</th>
                    <th>Created At</th>
                    <th>Changed At</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                @if (ViewBag.lstBIASection != null)
                {
                    @foreach (BIASection item in ViewBag.lstBIASection)
                    {
                        <tr>

                            <td>
                                @item.SectionName
                            </td>
                            @if (@item.BIADONE == "1")
                            {
                                <td>
                                    Complete
                                </td>
                            }
                            else
                            {
                                <td>
                                    InComplete
                                </td>
                            }

                            <td>@item.CreatedByName</td>
                            <td>@item.ChangedByName</td>

                            <td>@item.CreatedAt</td>
                            <td>@item.ChangedAt.ToString("dd/MM/yyyy") </td>
                            <td>
                                <a href="@Url.Action(@item.ActionName,@item.ControllerName, new { area = @item.AreaName , strSectionID=@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.SectionID.ToString()) ,strProcessID=@BCM.Security.Helper.CryptographyHelper.Encrypt( HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString()), strIsBCMEntity=@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.IsBCMEntity.ToString()) ,strBIAID=@BCM.Security.Helper.CryptographyHelper.Encrypt(item.BIAID.ToString())})">
                                <i class="cv-edit " role="button"></i>
                                </a>
                            </td>
                        </tr>
                    }

                }

            </tbody>
        </table>

    </div>
</div>