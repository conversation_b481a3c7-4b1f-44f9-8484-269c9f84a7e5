﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.BusinessProcessComponents;
using BCM.Shared;
using BCM.UI.Areas.BCMIncidentManagement.Services;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using NuGet.Protocol.Plugins;
using Serilog;
using System.Collections.Generic;
using System.Data;
using System.Net.Http.Headers;
using System.Runtime.Serialization;
using System.Text;
using System.Text.Json;
using System.Web;
using System.Xml;

namespace BCM.UI.Areas.BCMIncidentManagement.Controllers;
[Area("BCMIncidentManagement")]
public class IncidentTimeLineLatestController : BaseController
{
    private ProcessSrv _ProcessSrv;
    private ProcessMgr _oMgrService;
    private readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    private readonly BCMMail _BCMMail;
    private readonly ApiService _ApiService;
    public IncidentTimeLineLatestController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, BCMMail bcmMail, ProcessMgr oMgrService, ApiService ApiService) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _BCMMail = bcmMail;
        _oMgrService = oMgrService;
        _ApiService = ApiService;
    }


    [HttpGet]
    public IActionResult IncidentTimelineDetails(int iIncidentID)
    {
        IncidentManagement objIncidentManagement = new IncidentManagement();
        try
        {
            if (iIncidentID <= 0)
            {
                return PartialView("_IncidentTimelineDetails", objIncidentManagement);
            }
            HttpContext.Session.SetString("sessionIncidentId", iIncidentID.ToString());
            objIncidentManagement = _ProcessSrv.GetIncidentManagementGetByIncidentID(iIncidentID);
            ResourcesInfo objResource = new ResourcesInfo();
            if (objIncidentManagement!=null)
            {
                objResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt16(objIncidentManagement.NotifiedBy));
            }
            objIncidentManagement.OwnerEmail = objResource.CompanyEmail;
            objIncidentManagement.OwnerMobile = Convert.ToInt64(objResource.MobilePhone);
            objIncidentManagement.NotifiedAs = _Utilities.GetNotificationType(objIncidentManagement.NotifiedAs);
            objIncidentManagement.Status = _Utilities.GetIncidentStatus(objIncidentManagement.Status);

            List<RecoveryTaskStepInfo> objIncidentColl = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(iIncidentID, 0);

            DataTable StepStatusCount = new DataTable();
            StepStatusCount.Columns.Add("PlanName");
            StepStatusCount.Columns.Add("TotalSteps");
            StepStatusCount.Columns.Add("NotInitiated");
            StepStatusCount.Columns.Add("Assigned");
            StepStatusCount.Columns.Add("Acknowledged");
            StepStatusCount.Columns.Add("InProgress");
            StepStatusCount.Columns.Add("Completed");
            StepStatusCount.Columns.Add("ReInitiated");

            StepStatusCount.Rows.Add(objIncidentManagement.EventName, objIncidentColl.Count(), objIncidentColl.Count(x => x.StepStatus=="0"), objIncidentColl.Count(x => x.StepStatus=="1"), objIncidentColl.Count(x => x.StepStatus=="2"), objIncidentColl.Count(x => x.StepStatus=="3"), objIncidentColl.Count(x => x.StepStatus=="4"), objIncidentColl.Count(x => x.StepStatus=="5"));

            ViewBag.StatusCounts = StepStatusCount;
            ViewBag.RecoverySteps = objIncidentColl.OrderBy(x => x.Sequence);
            //getStepDetailsForChatbot(iIncidentID);
            //apiExecution(29);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return StatusCode(500, "An error occurred while loading incident details.");
        }
        return PartialView("_IncidentTimelineDetails", objIncidentManagement);
    }

    [HttpGet]
    public List<RecoveryTaskStepInfo> getStepDetailsForChatbot(int iIncidentID)
    {
        IncidentManagement objIncidentManagement = new IncidentManagement();
        List<RecoveryTaskStepInfo> objIncidentColl = new List<RecoveryTaskStepInfo>();
        try
        {
            objIncidentManagement = _ProcessSrv.GetIncidentManagementGetByIncidentID(iIncidentID);
            ResourcesInfo objResource = new ResourcesInfo();
            if (objIncidentManagement!=null)
            {
                objResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt16(objIncidentManagement.NotifiedBy));
            }
            objIncidentManagement.OwnerEmail = objResource.CompanyEmail;
            objIncidentManagement.OwnerMobile = Convert.ToInt64(objResource.MobilePhone);
            objIncidentManagement.NotifiedAs = _Utilities.GetNotificationType(objIncidentManagement.NotifiedAs);
            objIncidentManagement.Status = _Utilities.GetIncidentStatus(objIncidentManagement.Status);

            objIncidentColl = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(iIncidentID, 0);

            ViewBag.RecoverySteps = objIncidentColl.OrderBy(x => x.Sequence);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return objIncidentColl;
    }

    [HttpPost]
    public IActionResult updateStepStatus(int iIncidentStepId, int stepStatus)
    {
        int iErrorCount = 0;
        int iStepNotSelected = 0;
        try
        {
            RecoveryTaskStepInfo objTaskSteps = GetRecoveryStepsData(iIncidentStepId);
            objTaskSteps.IncidentID=iIncidentStepId.ToString();
            objTaskSteps.StepStatus=stepStatus.ToString();

            RecoveryTaskStepInfo objRecoveryTaskStep = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(Convert.ToInt32(objTaskSteps.IncidentStepID));

            if (!objRecoveryTaskStep.StepStatus.Equals(((int)BCPEnum.StepStatus.Completed).ToString()))
            {
                if (objTaskSteps.ExecutionType != "0" && objTaskSteps.ExecutionType =="2")
                {
                    apiExecutionManual(objRecoveryTaskStep, stepStatus);
                }

                else
                {
                    objRecoveryTaskStep.StepStatus=objTaskSteps.StepStatus;
                    if (objRecoveryTaskStep.StepStatus.Equals(((int)BCPEnum.StepStatus.Completed).ToString()) ||
                                    objRecoveryTaskStep.StepStatus.Equals(((int)BCPEnum.StepStatus.Failed).ToString()))
                    {
                        objRecoveryTaskStep.NotificationSentTime = string.Empty;
                        objRecoveryTaskStep.CompletionTime = DateTime.Now.ToString();
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(objRecoveryTaskStep.CompletionTime))
                        {
                            objRecoveryTaskStep.CompletionTime = objRecoveryTaskStep.CompletionTime;
                        }
                        else
                        {
                            objRecoveryTaskStep.CompletionTime = string.Empty;
                        }
                    }

                    objRecoveryTaskStep.UpdatedBy = ((int)BCPEnum.NotificationType.EMail).ToString();
                    objRecoveryTaskStep.Remarks="";
                    objRecoveryTaskStep.ChangedBy = _UserDetails.UserID.ToString();
                    if (objRecoveryTaskStep.ExecutedBy == "0")
                    {
                        objRecoveryTaskStep.ExecutedBy = AcknowledgeUser(objRecoveryTaskStep);
                    }

                    //if (!_ApiService.UpdateStepStatus(objRecoveryTaskStep, Convert.ToInt32(objRecoveryTaskStep.OrgID)))
                    if (!_ApiService.UpdateStepStatusNew(objRecoveryTaskStep, Convert.ToInt32(objRecoveryTaskStep.OrgID)))
                    {
                        iErrorCount+=1;
                        return Json(new { success = false, message = "Error occoured while updating status..." });
                    }

                    else
                    {
                        string CurrentStepId = "0";
                        List<RecoveryTaskStepInfo> objIncidentColl = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(Convert.ToInt32(objRecoveryTaskStep.IncidentID), 0);
                        //RecoveryTaskStepInfo ObjNextStep = objIncidentColl.FirstOrDefault(X => Convert.ToInt32(X.IncidentStepID) > Convert.ToInt32(objRecoveryTaskStep.IncidentStepID));
                        RecoveryTaskStepInfo ObjNextStep = new RecoveryTaskStepInfo();
                        if (objRecoveryTaskStep.StepStatus=="4")
                        {
                            ObjNextStep = objIncidentColl.FirstOrDefault(X => Convert.ToInt32(X.IncidentStepID) > Convert.ToInt32(objRecoveryTaskStep.IncidentStepID));
                        }
                        else if (objRecoveryTaskStep.StepStatus=="5")
                        {
                            if (objRecoveryTaskStep.IsCondition == "1" && objRecoveryTaskStep.FailureStepID !="0")
                            {
                                ObjNextStep = objIncidentColl.FirstOrDefault(X => Convert.ToInt32(X.Sequence) == Convert.ToInt32(objRecoveryTaskStep.FailureStepID));
                            }
                        }

                        if (ObjNextStep!=null && ObjNextStep.ExecutionType =="2")
                        {
                            apiExecutionAutomatic(ObjNextStep);
                        }
                    }
                }

                return Json(new { success = true, message = "Step Updated Successfully..." });
            }
            else
                iStepNotSelected +=1;
            return Json(new { success = false, message = "Step status is match,Please select other status..." });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "ex" });
        }
    }

    private RecoveryTaskStepInfo GetRecoveryStepsData(int iIncidentStepID)
    {
        try
        {
            return _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(iIncidentStepID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return new RecoveryTaskStepInfo();
        }
    }

    private void BindStatus()
    {
        try
        {
            ViewBag.StepStaus = new SelectList(_Utilities.PopulateStepStatus(), "StatusId", "StatusName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private string AcknowledgeUser(RecoveryTaskStepInfo objRecoveryTaskStep)
    {
        string ValidMessageBody = string.Empty;
        string ExecutedBy = "0";

        try
        {
            if (objRecoveryTaskStep.StepOwnerID.Equals(HttpContext.Session.GetInt32("iUserID").ToString()))
            {
                if (_ProcessSrv.ResourceMaster_UpdateActiveUser(HttpContext.Session.GetInt32("iUserID").ToString(), HttpContext.Session.GetInt32("iIncidentStepID") ?? 0))
                {
                    ValidMessageBody =  objRecoveryTaskStep.StepOwnerName + " has taken the responsibilty of performing Task" + objRecoveryTaskStep.StepName;
                    ExecutedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                    //BCPSms.SMSSend(objRecoveryTaskStep.AltStepOwnerMobileNo, objRecoveryTaskStep.AltStepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);
                    ExecutedBy = objRecoveryTaskStep.StepOwnerID;
                }
            }
            else if (objRecoveryTaskStep.AltStepOwnerID.Equals(HttpContext.Session.GetInt32("iUserID").ToString()))
            {
                if (_ProcessSrv.ResourceMaster_UpdateActiveUser(HttpContext.Session.GetInt32("iUserID").ToString(), HttpContext.Session.GetInt32("iIncidentStepID") ?? 0))
                {
                    ValidMessageBody = objRecoveryTaskStep.AltStepOwnerName + " has taken the responsibilty of performing Task" + objRecoveryTaskStep.StepName; //"Dear " + objRecoveryTaskStep.StepOwnerName + ", This is to inform you that " +
                    ExecutedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                    //BCPSms.SMSSend(objRecoveryTaskStep.StepOwnerMobileNo, objRecoveryTaskStep.StepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);
                    ExecutedBy = objRecoveryTaskStep.AltStepOwnerID;
                }
            }
            else if (objRecoveryTaskStep.ReassignedTo.Equals(HttpContext.Session.GetInt32("iUserID").ToString()))
            {
                if (_ProcessSrv.ResourceMaster_UpdateActiveUser(objRecoveryTaskStep.ReassignedTo, HttpContext.Session.GetInt32("iIncidentStepID") ?? 0))
                {
                    ValidMessageBody = objRecoveryTaskStep.ReassignedOwnerName + " has taken the responsibilty of performing Task" + objRecoveryTaskStep.StepName;//"Dear " + objRecoveryTaskStep.StepOwnerName + ", This is to inform you that " +

                    //BCPSms.SMSSend(objRecoveryTaskStep.StepOwnerMobileNo, objRecoveryTaskStep.StepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);

                    ValidMessageBody = objRecoveryTaskStep.ReassignedOwnerName + " has taken the responsibilty of performing Task"
                        + objRecoveryTaskStep.StepName; //"Dear " + objRecoveryTaskStep.AltStepOwnerName + ", This is to inform you that " +
                                                        //BCPSms.SMSSend(objRecoveryTaskStep.AltStepOwnerMobileNo, objRecoveryTaskStep.AltStepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);

                    ExecutedBy = objRecoveryTaskStep.ReassignedTo;
                }
            }
            else
            {

                RecoveryTaskStepInfo obj = new RecoveryTaskStepInfo();
                obj.StepID = objRecoveryTaskStep.IncidentStepID;
                obj.PlanID = objRecoveryTaskStep.PlanID;
                obj.AltStepOwnerID = HttpContext.Session.GetInt32("iUserID").ToString();
                bool Success = _ProcessSrv.RecoveryTaskStepInfoUpdate_AltOwner(obj);
                if (Success)
                {
                    if (_ProcessSrv.ResourceMaster_UpdateActiveUser(HttpContext.Session.GetInt32("iUserID").ToString(), Convert.ToInt32(objRecoveryTaskStep.IncidentStepID)))
                    {
                        //bool notify = NotifyReassignedOwner(Convert.ToInt32(ResponseID), Convert.ToInt32(objTaskStepInfo.IncidentID));
                        //ValidMessageBody = "Dear " + objTaskStepInfo.StepOwnerName + ", " + Convert.ToString(_DataTable.Rows[0]["ResourceName"]) + " has taken the responsibilty of performing Task" + objTaskStepInfo.StepName;

                        //BCPSms.SMSSend(objTaskStepInfo.StepOwnerMobileNo, ValidMessageBody);

                        //ValidMessageBody = "Dear " + objTaskStepInfo.AltStepOwnerName + ", " + Convert.ToString(_DataTable.Rows[0]["ResourceName"]) + " has taken the responsibilty of performing Task" + objTaskStepInfo.StepName;
                        //BCPSms.SMSSend(objTaskStepInfo.ReassignedOwnerMobileNo, ValidMessageBody);
                        RecoveryTaskStepInfo objrecstep = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(HttpContext.Session.GetInt32("iIncidentStepID") ?? 0);
                        ValidMessageBody =  objrecstep.ReassignedOwnerName + " has taken the responsibilty of performing Task" + objrecstep.StepName;

                        //BCPSms.SMSSend(objrecstep.StepOwnerMobileNo, objrecstep.StepOwnerName, ValidMessageBody, objrecstep.OrgID);

                        ValidMessageBody =  objrecstep.ReassignedOwnerName + " has taken the responsibilty of performing Task" + objrecstep.StepName;
                        //BCPSms.SMSSend(objrecstep.AltStepOwnerMobileNo, objrecstep.AltStepOwnerName, ValidMessageBody, objrecstep.OrgID);

                        ExecutedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return ExecutedBy;
    }

    public void MarkAsCompleted()
    {
        try
        {
            int IncidentID = Convert.ToInt32(HttpContext.Session.GetString("sessionIncidentId"));
            bool update = _ProcessSrv.IncidentManagementUpdateStatus(Convert.ToInt32(IncidentID), Convert.ToInt32(_UserDetails.UserID), (int)BCPEnum.IncidentStatus.Completed, 0);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public void apiExecutionManual(RecoveryTaskStepInfo objRecoveryTaskStep, int StepStatus)
    {
        objRecoveryTaskStep.StepStatus = StepStatus.ToString();
        _ApiService.UpdateStepStatusNew(objRecoveryTaskStep, Convert.ToInt32(objRecoveryTaskStep.OrgID));

        //int B =  _ProcessSrv.RecoveryTaskStepStatusSave(objRecoveryTaskStep);
    }

    public void apiExecutionAutomatic(RecoveryTaskStepInfo objRecoveryTaskStep)
    {
        if (objRecoveryTaskStep.APIProfileID != "0")
        {
            bool isSuccess = false;
            //isSuccess = apiExecution(Convert.ToInt32(objRecoveryTaskStep.APIProfileID));
            apiExecution(Convert.ToInt32(objRecoveryTaskStep.APIProfileID));

            //objRecoveryTaskStep.StepStatus = isSuccess==true ? "4" : "5";
            objRecoveryTaskStep.StepStatus = "4";
            objRecoveryTaskStep.CompletionTime = DateTime.Now.ToString();
            objRecoveryTaskStep.NotificationSentTime = string.Empty;

            _ApiService.UpdateStepStatusNew(objRecoveryTaskStep, Convert.ToInt32(objRecoveryTaskStep.OrgID));
        }
    }


    #region Api Execution
    /// <returns>JSON result with execution status</returns>
    [HttpGet]
    public async Task<IActionResult> apiExecution(int apiId)
    {
        try
        {
            var result = await ExecuteApiAsync(apiId);

            return Json(new
            {
                success = result.IsSuccess,
                statusCode = result.StatusCode,
                statusText = result.StatusText,
                responseContent = result.ResponseContent,
                errorMessage = result.ErrorMessage,
                extractedData = result.ExtractedData,
                mappedFields = result.MappedFields,
                executionTime = result.ExecutionTime
            });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new
            {
                success = false,
                errorMessage = ex.Message
            });
        }
    }

    /// <summary>
    /// Example method showing how to execute multiple APIs in sequence
    /// </summary>
    /// <param name="apiIds">Array of API configuration IDs to execute</param>
    /// <returns>Results of all API executions</returns>
    [HttpPost]
    public async Task<IActionResult> ExecuteMultipleApis([FromBody] int[] apiIds)
    {
        var results = new List<object>();

        try
        {
            foreach (var apiId in apiIds)
            {
                var result = await ExecuteApiAsync(apiId);
                results.Add(new
                {
                    apiId = apiId,
                    success = result.IsSuccess,
                    statusCode = result.StatusCode,
                    errorMessage = result.ErrorMessage,
                    extractedData = result.ExtractedData,
                    executionTime = result.ExecutionTime
                });
            }

            return Json(new
            {
                success = true,
                results = results
            });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new
            {
                success = false,
                errorMessage = ex.Message,
                results = results
            });
        }
    }

    /// <summary>
    /// Executes an API call using configuration data from APIConfiguration model
    /// </summary>
    /// <param name="apiId">The ID of the API configuration to execute</param>
    /// <returns>API execution result</returns>
    public async Task<ApiExecutionResult> ExecuteApiAsync(int apiId)
    {
        var result = new ApiExecutionResult();

        try
        {
            // Get API configuration from database
            APIConfiguration apiConfig = _ProcessSrv.GetAPIDetails_ById(apiId);

            if (apiConfig == null)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"API configuration with ID {apiId} not found.";
                return result;
            }

            //if (!apiConfig.IsActive)
            //{
            //    result.IsSuccess = false;
            //    result.ErrorMessage = $"API configuration '{apiConfig.APIName}' is not active.";
            //    return result;
            //}

            // Execute the API call
            result = await ExecuteApiCallAsync(apiConfig);

            // Log the execution
            _CVLogger.LogInfo($"API '{apiConfig.APIName}' executed. Success: {result.IsSuccess}");

            return result;
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = $"Error executing API: {ex.Message}";
            _CVLogger.LogErrorApp(ex);
            return result;
        }
    }

    /// <summary>
    /// Executes the actual API call based on configuration
    /// </summary>
    /// <param name="apiConfig">API configuration object</param>
    /// <returns>API execution result</returns>
    private async Task<ApiExecutionResult> ExecuteApiCallAsync(APIConfiguration apiConfig)
    {
        var result = new ApiExecutionResult();

        try
        {
            using var httpClient = new HttpClient();

            // Set timeout
            if (apiConfig.ResponseTimeout > 0)
            {
                httpClient.Timeout = TimeSpan.FromSeconds(apiConfig.ResponseTimeout);
            }

            // Build the complete URL
            string requestUrl = BuildRequestUrl(apiConfig);

            // Create HTTP request message
            var request = new HttpRequestMessage(GetHttpMethod(apiConfig.APIType), requestUrl);

            // Add authentication
            AddAuthentication(httpClient, request, apiConfig);

            // Add headers
            AddHeaders(request, apiConfig);

            // Add request body for POST/PUT/PATCH
            if (ShouldIncludeBody(apiConfig.APIType) && !string.IsNullOrEmpty(apiConfig.RequestBody))
            {
                request.Content = new StringContent(
                    apiConfig.RequestBody,
                    Encoding.UTF8,
                    apiConfig.ContentType ?? "application/json"
                );
            }

            // Execute the request with retry logic
            HttpResponseMessage response = await ExecuteWithRetryAsync(httpClient, request, apiConfig.RetryAttempts);

            // Process the response
            result = await ProcessResponseAsync(response, apiConfig);

            return result;
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = $"HTTP request failed: {ex.Message}";
            result.Exception = ex;
            return result;
        }
    }

    /// <summary>
    /// Builds the complete request URL with path and query parameters
    /// </summary>
    private string BuildRequestUrl(APIConfiguration apiConfig)
    {
        string baseUrl = !string.IsNullOrEmpty(apiConfig.FullURL) ? apiConfig.FullURL :
                        $"{apiConfig.BaseURL?.TrimEnd('/')}/{apiConfig.EndpointPath?.TrimStart('/')}";

        // Replace path parameters
        if (apiConfig.PathParameters != null && apiConfig.PathParameters.Any())
        {
            foreach (var pathParam in apiConfig.PathParameters)
            {
                if (!string.IsNullOrEmpty(pathParam.Key) && !string.IsNullOrEmpty(pathParam.Value))
                {
                    baseUrl = baseUrl.Replace($"{{{pathParam.Key}}}", HttpUtility.UrlEncode(pathParam.Value));
                }
            }
        }

        // Add query parameters
        if (apiConfig.QueryParameters != null && apiConfig.QueryParameters.Any())
        {
            var queryString = string.Join("&",
                apiConfig.QueryParameters
                    .Where(q => !string.IsNullOrEmpty(q.Key) && !string.IsNullOrEmpty(q.Value))
                    .Select(q => $"{HttpUtility.UrlEncode(q.Key)}={HttpUtility.UrlEncode(q.Value)}"));

            if (!string.IsNullOrEmpty(queryString))
            {
                baseUrl += baseUrl.Contains("?") ? "&" + queryString : "?" + queryString;
            }
        }

        return baseUrl;
    }

    /// <summary>
    /// Gets the HTTP method from API type string
    /// </summary>
    private HttpMethod GetHttpMethod(string apiType)
    {
        return apiType?.ToUpper() switch
        {
            "GET" => HttpMethod.Get,
            "POST" => HttpMethod.Post,
            "PUT" => HttpMethod.Put,
            "DELETE" => HttpMethod.Delete,
            "PATCH" => HttpMethod.Patch,
            "HEAD" => HttpMethod.Head,
            "OPTIONS" => HttpMethod.Options,
            _ => HttpMethod.Get
        };
    }

    /// <summary>
    /// Adds authentication to the HTTP request
    /// </summary>
    private void AddAuthentication(HttpClient httpClient, HttpRequestMessage request, APIConfiguration apiConfig)
    {
        if (string.IsNullOrEmpty(apiConfig.AuthType))
            return;

        switch (apiConfig.AuthType.ToUpper())
        {
            case "BASIC":
                if (!string.IsNullOrEmpty(apiConfig.Username) && !string.IsNullOrEmpty(apiConfig.Password))
                {
                    var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{apiConfig.Username}:{apiConfig.Password}"));
                    request.Headers.Authorization = new AuthenticationHeaderValue("Basic", credentials);
                }
                break;

            case "BEARER":
            case "JWT":
                if (!string.IsNullOrEmpty(apiConfig.BearerToken))
                {
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", apiConfig.BearerToken);
                }
                break;

            case "APIKEY":
                if (!string.IsNullOrEmpty(apiConfig.APIKeyName) && !string.IsNullOrEmpty(apiConfig.APIKeyValue))
                {
                    request.Headers.Add(apiConfig.APIKeyName, apiConfig.APIKeyValue);
                }
                break;

            case "OAUTH2":
                // For OAuth2, you might need to implement token refresh logic
                if (!string.IsNullOrEmpty(apiConfig.BearerToken))
                {
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", apiConfig.BearerToken);
                }
                break;
        }
    }

    /// <summary>
    /// Adds custom headers to the HTTP request
    /// </summary>
    private void AddHeaders(HttpRequestMessage request, APIConfiguration apiConfig)
    {
        if (apiConfig.Headers != null && apiConfig.Headers.Any())
        {
            foreach (var header in apiConfig.Headers)
            {
                if (!string.IsNullOrEmpty(header.Key) && !string.IsNullOrEmpty(header.Value))
                {
                    try
                    {
                        request.Headers.Add(header.Key, header.Value);
                    }
                    catch (Exception ex)
                    {
                        _CVLogger.LogErrorApp(ex);
                    }
                }
            }
        }
    }

    /// <summary>
    /// Determines if the HTTP method should include a request body
    /// </summary>
    private bool ShouldIncludeBody(string apiType)
    {
        return apiType?.ToUpper() switch
        {
            "POST" or "PUT" or "PATCH" => true,
            _ => false
        };
    }

    /// <summary>
    /// Executes HTTP request with retry logic
    /// </summary>
    private async Task<HttpResponseMessage> ExecuteWithRetryAsync(HttpClient httpClient, HttpRequestMessage request, int retryAttempts)
    {
        int maxAttempts = Math.Max(1, retryAttempts);
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxAttempts; attempt++)
        {
            try
            {
                // Clone the request for retry attempts
                var clonedRequest = await CloneHttpRequestMessageAsync(request);
                var response = await httpClient.SendAsync(clonedRequest);

                // If successful or client error (4xx), don't retry
                if (response.IsSuccessStatusCode || ((int)response.StatusCode >= 400 && (int)response.StatusCode < 500))
                {
                    return response;
                }

                // For server errors (5xx), retry if attempts remaining
                if (attempt < maxAttempts)
                {
                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt))); // Exponential backoff
                    continue;
                }

                return response;
            }
            catch (Exception ex)
            {
                lastException = ex;
                if (attempt < maxAttempts)
                {
                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt))); // Exponential backoff
                    continue;
                }
            }
        }

        throw lastException ?? new Exception("Request failed after all retry attempts");
    }

    /// <summary>
    /// Clones an HTTP request message for retry attempts
    /// </summary>
    private async Task<HttpRequestMessage> CloneHttpRequestMessageAsync(HttpRequestMessage original)
    {
        var clone = new HttpRequestMessage(original.Method, original.RequestUri);

        // Copy headers
        foreach (var header in original.Headers)
        {
            clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }

        // Copy content if present
        if (original.Content != null)
        {
            var originalContent = await original.Content.ReadAsStringAsync();
            clone.Content = new StringContent(originalContent, Encoding.UTF8, original.Content.Headers.ContentType?.MediaType ?? "application/json");

            // Copy content headers
            foreach (var header in original.Content.Headers)
            {
                clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
        }

        return clone;
    }

    /// <summary>
    /// Processes the HTTP response and extracts relevant data
    /// </summary>
    private async Task<ApiExecutionResult> ProcessResponseAsync(HttpResponseMessage response, APIConfiguration apiConfig)
    {
        var result = new ApiExecutionResult
        {
            StatusCode = (int)response.StatusCode,
            StatusText = response.ReasonPhrase
        };

        try
        {
            // Read response content
            result.ResponseContent = await response.Content.ReadAsStringAsync();
            result.ResponseHeaders = response.Headers.ToDictionary(h => h.Key, h => string.Join(", ", h.Value));

            // Check if response is successful based on expected status codes
            result.IsSuccess = IsSuccessfulResponse(response, apiConfig.ExpectedStatusCodes);

            if (result.IsSuccess)
            {
                // Extract success data if success key path is specified
                if (!string.IsNullOrEmpty(apiConfig.SuccessKeyPath))
                {
                    result.ExtractedData = ExtractDataFromResponse(result.ResponseContent, apiConfig.SuccessKeyPath);
                }

                // Apply response field mappings
                if (apiConfig.ResponseFieldMappings != null && apiConfig.ResponseFieldMappings.Any())
                {
                    result.MappedFields = ApplyFieldMappings(result.ResponseContent, apiConfig.ResponseFieldMappings);
                }
            }
            else
            {
                // Extract error message if error key path is specified
                if (!string.IsNullOrEmpty(apiConfig.ErrorKeyPath))
                {
                    result.ErrorMessage = ExtractDataFromResponse(result.ResponseContent, apiConfig.ErrorKeyPath)?.ToString();
                }

                if (string.IsNullOrEmpty(result.ErrorMessage))
                {
                    result.ErrorMessage = $"API call failed with status {response.StatusCode}: {response.ReasonPhrase}";
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = $"Error processing response: {ex.Message}";
            result.Exception = ex;
            return result;
        }
    }

    /// <summary>
    /// Determines if the response is successful based on expected status codes
    /// </summary>
    private bool IsSuccessfulResponse(HttpResponseMessage response, string expectedStatusCodes)
    {
        // If no expected status codes specified, use default success range
        if (string.IsNullOrEmpty(expectedStatusCodes))
        {
            return response.IsSuccessStatusCode;
        }

        // Parse expected status codes (comma-separated)
        var expectedCodes = expectedStatusCodes.Split(',')
            .Select(code => code.Trim())
            .Where(code => int.TryParse(code, out _))
            .Select(int.Parse)
            .ToList();

        return expectedCodes.Contains((int)response.StatusCode);
    }

    /// <summary>
    /// Extracts data from JSON response using JSONPath-like syntax
    /// </summary>
    private object ExtractDataFromResponse(string responseContent, string keyPath)
    {
        try
        {
            if (string.IsNullOrEmpty(responseContent) || string.IsNullOrEmpty(keyPath))
                return null;

            // Simple implementation for basic JSON path extraction
            // For more complex scenarios, consider using a JSONPath library
            using var document = JsonDocument.Parse(responseContent);
            var root = document.RootElement;

            // Split the key path by dots
            var pathParts = keyPath.Split('.');
            JsonElement current = root;

            foreach (var part in pathParts)
            {
                if (current.ValueKind == JsonValueKind.Object && current.TryGetProperty(part, out var property))
                {
                    current = property;
                }
                else if (current.ValueKind == JsonValueKind.Array && int.TryParse(part, out int index))
                {
                    if (index >= 0 && index < current.GetArrayLength())
                    {
                        current = current[index];
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }

            return current.ValueKind switch
            {
                JsonValueKind.String => current.GetString(),
                JsonValueKind.Number => current.GetDecimal(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => null,
                _ => current.GetRawText()
            };
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return null;
        }
    }

    /// <summary>
    /// Applies field mappings to extract specific fields from response
    /// </summary>
    private Dictionary<string, object> ApplyFieldMappings(string responseContent, List<ResponseFieldMapping> mappings)
    {
        var result = new Dictionary<string, object>();

        try
        {
            foreach (var mapping in mappings)
            {
                if (!string.IsNullOrEmpty(mapping.SourceField) && !string.IsNullOrEmpty(mapping.TargetField))
                {
                    var extractedValue = ExtractDataFromResponse(responseContent, mapping.SourceField);
                    if (extractedValue != null)
                    {
                        result[mapping.TargetField] = extractedValue;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return result;
    }

    #endregion Api Execution
}

/// <summary>
/// Result model for API execution
/// </summary>
public class ApiExecutionResult
{
    public bool IsSuccess { get; set; }
    public int StatusCode { get; set; }
    public string StatusText { get; set; }
    public string ResponseContent { get; set; }
    public Dictionary<string, string> ResponseHeaders { get; set; } = new Dictionary<string, string>();
    public string ErrorMessage { get; set; }
    public Exception Exception { get; set; }
    public object ExtractedData { get; set; }
    public Dictionary<string, object> MappedFields { get; set; } = new Dictionary<string, object>();
    public DateTime ExecutionTime { get; set; } = DateTime.UtcNow;
}