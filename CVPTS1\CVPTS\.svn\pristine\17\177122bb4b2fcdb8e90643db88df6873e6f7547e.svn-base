﻿@model BCM.BusinessClasses.ResourceTrainingInfo

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<form asp-action="AddResourceTraining" method="post">
    <div class="row row-cols-2">
        <div class="col">
            <div class="form-group">
                <label class="form-label">Resource</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                    <select class="form-select form-control" id="headlist" autocomplete="off" aria-label="Default select example" asp-for="ResourceID" required>
                        <option selected disabled value="">-- All Resources --</option>
                        @foreach (var objResource in ViewBag.ResourcesInfo)
                        {
                            <option value="@objResource.Value">@objResource.Text</option>
                        }
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">Primary Skills</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-process-name"></i></span>
                    <input class="form-control" type="text" placeholder="Primary Skills" asp-for="PrimarySkills" required />
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Role</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-unit"></i></span>
                    <select class="form-select form-control" id="rolelist" autocomplete="off" aria-label="Default select example" asp-for="Role" required>
                        <option selected disabled value="">-- All Roles --</option>
                        @foreach (var objRole in ViewBag.Role)
                        {
                            <option value="@objRole.Value">@objRole.Text</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Training</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <input class="form-control" type="text" placeholder="Enter Training" asp-for="Training" required />
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button id="btnsave" type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>