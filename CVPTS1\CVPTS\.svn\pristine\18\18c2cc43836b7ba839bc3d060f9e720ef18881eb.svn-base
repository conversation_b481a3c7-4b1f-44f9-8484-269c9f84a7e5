﻿using System;
using System.Diagnostics;
//using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Xml;
using System.Xml.Serialization;


namespace BCP.BusinessProcessComponents
{
    [Serializable]
    public abstract class BaseEntity
    {
        public string? GetSerializeText()
        {
            Type currentEntityType = GetType();

            var serializedContent = new StringBuilder();
            var xmlWriterSettings = new XmlWriterSettings
            {
                Encoding = Encoding.UTF8,
                CloseOutput = true,
                OmitXmlDeclaration = true
            };
            using (XmlWriter xmlWriter = XmlWriter.Create(serializedContent, xmlWriterSettings))
            {
                var serializer = new XmlSerializer(currentEntityType);
                serializer.Serialize(xmlWriter, this);
                xmlWriter.Flush();
                xmlWriter.Close();
            }
            return serializedContent.ToString();
        }
    }

}