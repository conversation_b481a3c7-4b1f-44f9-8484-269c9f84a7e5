﻿@{
    ViewBag.Title = "Business Process Form";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@* @model BCM.BusinessClasses.BCMTrainingMaster *@
@using static BCM.Shared.Common;
@using static BCM.Shared.BCPEnum;
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">Training Questions Configuration</h6>
    </div>
</div>
<input type="hidden" class="form-control">
<!-- Questions List Table -->
<div class="Page-Condant card border-0" id="QuestionsListTable">
    <div class="card">
        <div class="card-body py-0">
            <table id="questionsTable" class="table table-hover" style="width:100%;vertical-align:middle">
                <thead>
                    <tr>
                        <th class="SrNo_th" style="width: 60px; text-align: center;">#</th>
                        <th style="text-align: left;">Question&nbsp;Text</th>
                        <th style="width: 200px; text-align: center;">Action</th>
                    </tr>
                </thead>
                <tbody id="tblBody">
                    @{
                        int iCount = 0;
                        if (ViewBag.Questions != null && ViewBag.Questions.Count > 0)
                        {
                            foreach (var que in ViewBag.Questions)
                            {
                                if (que != null)
                                {
                                    iCount++;
                                    <tr>
                                        <td style="text-align: center; vertical-align: middle;">@iCount</td>
                                        <td style="text-align: left; vertical-align: middle;" title="ID: @(que.ID ?? "N/A"), QuestionText: '@(que.QuestionText ?? "Empty")'">
                                            @if (!string.IsNullOrEmpty(que.QuestionText))
                                            {
                                                @que.QuestionText
                                            }
                                            else
                                            {
                                                <span class="text-muted fst-italic">No question text available (ID: @(que.ID ?? "N/A"))</span>
                                            }
                                        </td>
                                        <td style="text-align: center; vertical-align: middle;">
                                            <button type="button" class="btn btn-sm btn-success me-1 btnAddOptionForQuestion"
                                                data-question-id="@(que.ID ?? "")" data-bs-toggle="modal"
                                                data-bs-target="#AddOptionsModal" title="Add Options">
                                                <i class="cv-plus me-1"></i>Add Option
                                            </button>
                                            <span class="btn-action btnEditQuestion me-1" data-question-id="@(que.ID ?? "")"
                                                data-question-text="@(que.QuestionText ?? "")" type="button" title="Edit Question">
                                                <i class="cv-edit"></i>
                                            </span>
                                            <span class="btn-action btnDeleteQuestion" data-question-id="@(que.ID ?? "")" type="button"
                                                title="Delete Question">
                                                <i class="cv-delete text-danger"></i>
                                            </span>
                                        </td>
                                    </tr>
                                }
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="3" class="text-center text-muted py-4" style="vertical-align: middle;">
                                    <i class="cv-no-data fs-1 d-block mb-2"></i>
                                    <span>No questions found. Click "Save" to create your first question.</span>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>

        <form action="AddQuestion" method="post" id="addUpdateQuestionForm"
            class="needs-validation progressive-validation" novalidate>

            <!-- Hidden fields for question management -->
            <input type="hidden" id="txtQuestionID" name="ID" value="0">
            <input type="hidden" id="txtTrainingMasterID" name="TrainingMasterID" value="@ViewBag.TrainingMasterID">

            <div class="col-12">
                <div class="col">
                    <div class="form-group">
                        <label class="form-label"><i class="cv-question me-1"></i>Question Text</label>
                        <div class="accordion accordion-flush" id="accordionFlushExample">
                            <div class="accordion-item">
                                <div>
                                    <div class="accordion-body">
                                        <textarea class="form-control" id="txtQuestionText" name="QuestionText" required
                                            placeholder="Enter your question here..." rows="3"></textarea>
                                        <div class="invalid-feedback">Please enter question text</div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 text-end">
                @* <button type="button" id="btnAddOption" class="btn btn-sm btn-success me-2" data-bs-toggle="modal"
                    data-bs-target="#AddOptionsModal">
                    <i class="cv-plus me-1"></i>Add Option
                </button> *@
                <button type="submit" class="btn btn-sm btn-primary btnUpdate" id="btnSubmit">
                    Save
                </button>
            </div>
        </form>
    </div>
</div>







<!-- Add Options For Question Modal -->
<div class="modal fade" id="AddOptionsModal" tabindex="-1" aria-labelledby="addOptionsModalLabel" aria-hidden="true"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h6 class="modal-title" id="addOptionsModalLabel">
                    <i class="cv-question me-2"></i>Add Options For Question
                </h6>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <!-- Options Table with ManageBusinessProcess styling -->
                <div class="Page-Condant card border-0" id="OptionsListTable">
                    <div class="card">
                        <div class="card-body py-0">
                            <table id="optionsTable" class="table table-hover" style="width:100%;vertical-align:middle">
                                <thead>
                                    <tr>
                                        <th class="SrNo_th" style="width: 60px; text-align: center;">#</th>
                                        <th style="text-align: left;">Option&nbsp;Text</th>
                                        <th style="width: 120px; text-align: center;">Is&nbsp;Correct</th>
                                        <th style="width: 150px; text-align: center;">Action</th>
                                    </tr>
                                </thead>
                                <tbody id="optionsTableBody">
                                    <!-- Options will be loaded dynamically -->
                                    <tr>
                                        <td colspan="4" class="text-center text-muted py-4" style="vertical-align: middle;">
                                            <i class="cv-loading fs-3 d-block mb-2"></i>
                                            <span>Click "Add Option" button from a question row to load options</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Add Option Form -->
                <div class="p-4 bg-light border-top">
                    <form id="addOptionForm">
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label fw-semibold text-primary">
                                        <i class="cv-question me-1"></i>Question :
                                    </label>
                                    <div class="bg-light p-3 border rounded border-primary">
                                        <span id="questionText" class="fw-semibold text-dark">Click "Add Option" button from a question row to load question text</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label class="form-label fw-semibold">
                                        <i class="cv-option me-1"></i>Option :
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-text"></i></span>
                                        <input type="text" class="form-control" id="optionText"
                                            placeholder="Enter option text" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label fw-semibold">
                                        <i class="cv-check me-1"></i>Is Correct :
                                    </label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" type="checkbox" id="isCorrect">
                                        <label class="form-check-label" for="isCorrect">
                                            Mark as correct answer
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="cv-close me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="btnSaveOption">
                    <i class="cv-save me-1"></i>Add Option
                </button>
            </div>
        </div>
    </div>
</div>


@section Scripts {
    <style>
        /* Enhanced styling for tables - matching ManageBusinessProcess */
        #questionsTable thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #E63875;
            font-weight: 600;
            color: #495057;
            padding: 12px 8px;
        }

        #questionsTable tbody tr:hover {
            background-color: rgba(230, 56, 117, 0.05);
        }

        #questionsTable .SrNo_th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        /* Enhanced styling for the Add Options modal - matching ManageBusinessProcess */
        #optionsTable thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #E63875;
            font-weight: 600;
            color: #495057;
            padding: 12px 8px;
        }

        #optionsTable tbody tr:hover {
            background-color: rgba(230, 56, 117, 0.05);
        }

        #optionsTable .SrNo_th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        /* Modal specific styling */
        .modal-body .Page-Condant.card {
            border: none !important;
            box-shadow: none !important;
        }

        .modal-body .card-body {
            padding: 0 !important;
        }

        /* Ensure option text is visible and properly styled */
        #optionsTable tbody td {
            padding: 8px 12px;
            vertical-align: middle;
        }

        #optionsTable tbody td:nth-child(2) {
            font-weight: 500;
            color: #333;
            min-width: 200px;
        }

        /* Badge styling */
        #optionsTable .badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        /* Button styling in modal */
        #optionsTable .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .modal-header.bg-primary {
            background: linear-gradient(135deg, #E63875 0%, #320284 100%) !important;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        }

        .badge.bg-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #E63875 0%, #320284 100%);
            border-color: #E63875;
        }

        .btn-outline-danger:hover {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .form-check-input:checked {
            background-color: #E63875;
            border-color: #E63875;
        }

        .bg-light {
            background-color: #f8f9fa !important;
        }
    </style>
    <script>
        $(document).ready(function () {
            let optionCounter = 5; // Start from 5 since we have 4 sample options

            // Debug: Log ViewBag data
            console.log('ViewBag.TrainingMasterID:', '@ViewBag.TrainingMasterID');
            console.log('ViewBag.Questions count:', @(ViewBag.Questions?.Count ?? 0));

            @if (ViewBag.Questions != null && ViewBag.Questions.Count > 0)
            {
                <text>
                console.log('Questions data:');
                @foreach (var que in ViewBag.Questions)
                {
                    <text>
                    console.log('Question:', {
                        ID: '@(que.ID ?? "null")',
                        QuestionText: '@Html.Raw(Json.Serialize(que.QuestionText ?? "null"))',
                        TrainingMasterID: '@(que.TrainingMasterID)'
                    });
                    </text>
                }
                </text>
            }
            else
            {
                <text>console.log('No questions found in ViewBag');</text>
            }

            // Handle form submission for adding/updating questions
            $('#addUpdateQuestionForm').on('submit', function (e) {
                e.preventDefault();

                const form = $(this);

                // Get form values
                const questionText = $('#txtQuestionText').val().trim();
                const trainingMasterID = parseInt($('#txtTrainingMasterID').val());
                const questionID = $('#txtQuestionID').val();

                // Debug: Log the values being captured
                console.log('Form Values:', {
                    questionText: questionText,
                    trainingMasterID: trainingMasterID,
                    questionID: questionID,
                    hiddenFieldValue: $('#txtTrainingMasterID').val()
                });

                // Validate form data
                if (!questionText) {
                    showToast('Please enter question text', 'danger');
                    $('#txtQuestionText').focus();
                    return;
                }

                if (!trainingMasterID || trainingMasterID <= 0 || isNaN(trainingMasterID)) {
                    showToast('Training Master ID is required and must be a valid number', 'danger');
                    console.error('Invalid TrainingMasterID:', {
                        value: $('#txtTrainingMasterID').val(),
                        parsed: trainingMasterID,
                        isNaN: isNaN(trainingMasterID)
                    });
                    return;
                }

                // Show loading state
                const submitBtn = form.find('button[type="submit"]');
                const originalText = submitBtn.text();
                submitBtn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i>Saving...');

                // Prepare form data for submission
                const formData = {
                    ID: questionID || '0',
                    TrainingMasterID: trainingMasterID,
                    QuestionText: questionText
                };

                console.log('Submitting form data:', formData); // Debug log

                // Submit the form via AJAX
                $.ajax({
                    url: '@Url.Action("AddQuestion", "AddQuestionsAndOptions")',
                    type: 'POST',
                    data: formData,
                    success: function (response) {
                        console.log('Server response:', response); // Debug log

                        if (response.success) {
                            showToast(response.message, 'success');

                            // Reset form for new question
                            $('#txtQuestionText').val('');
                            $('#txtQuestionID').val('0');
                            $('#addUpdateQuestionForm button[type="submit"]').text('Save');

                            // Reload the page to show updated questions list
                            setTimeout(function () {
                                location.reload();
                            }, 1500);
                        } else {
                            showToast(response.message || 'Failed to save question', 'danger');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('AJAX Error:', {
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });
                        showToast('An error occurred while saving the question', 'danger');
                    },
                    complete: function () {
                        // Reset button state
                        submitBtn.prop('disabled', false).text(originalText);
                    }
                });
            });

            // Edit Question button click
            $(document).on('click', '.btnEditQuestion', function () {
                const questionId = $(this).data('question-id');
                const questionText = $(this).data('question-text');

                // Fill the form with existing question data
                $('#txtQuestionID').val(questionId);
                $('#txtQuestionText').val(questionText);

                // Change button text to indicate editing
                $('#addUpdateQuestionForm button[type="submit"]').text('Update Question');

                // Scroll to form
                $('html, body').animate({
                    scrollTop: $('#addUpdateQuestionForm').offset().top - 100
                }, 500);

                // Focus on the question text field
                $('#txtQuestionText').focus();

                showToast('Question loaded for editing', 'info');
            });

            // Delete Question button click
            $(document).on('click', '.btnDeleteQuestion', function () {
                const questionId = $(this).data('question-id');

                if (confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
                    // Here you would typically call a delete endpoint
                    // For now, just show a message
                    showToast('Delete functionality to be implemented', 'warning');
                }
            });

            // Reset form when starting new question
            function resetQuestionForm() {
                $('#txtQuestionID').val('0');
                $('#txtQuestionText').val('');
                $('#addUpdateQuestionForm button[type="submit"]').text('Save');
            }

            // Add a "New Question" button to reset the form
            $('#txtQuestionText').on('focus', function () {
                if ($('#txtQuestionID').val() !== '0') {
                    const resetBtn = '<button type="button" class="btn btn-sm btn-outline-secondary ms-2" id="btnNewQuestion">New Question</button>';
                    if (!$('#btnNewQuestion').length) {
                        $(this).closest('.form-group').find('label').append(resetBtn);
                    }
                }
            });

            $(document).on('click', '#btnNewQuestion', function () {
                resetQuestionForm();
                $(this).remove();
                showToast('Form reset for new question', 'info');
            });

            // Add Option button click (from table rows)
            $(document).on('click', '.btnAddOptionForQuestion', function () {
                const questionId = $(this).data('question-id');
                const questionText = $(this).closest('tr').find('td:eq(1)').text().trim();
                const trainingMasterID = parseInt($('#txtTrainingMasterID').val());

                console.log('Add Option clicked:', {
                    questionId: questionId,
                    questionText: questionText,
                    trainingMasterID: trainingMasterID
                });

                // Set the question text in the modal
                $('#questionText').text(questionText);

                // Update modal title to include question number
                const questionNumber = $(this).closest('tr').find('td:eq(0)').text().trim();
                $('#addOptionsModalLabel').html(`<i class="cv-question me-2"></i>Add Options For Question #${questionNumber}`);

                // Store question ID and training master ID for later use
                $('#AddOptionsModal').data('question-id', questionId);
                $('#AddOptionsModal').data('training-master-id', trainingMasterID);

                // Clear the form when opening modal
                $('#optionText').val('');
                $('#isCorrect').prop('checked', false);
                $('#btnSaveOption').text('Add Option').removeClass('btn-warning').addClass('btn-primary');
                $('#addOptionForm')[0].reset();

                // Load existing options for this question
                loadOptionsForQuestion(questionId, trainingMasterID);
            });

            // Generic Add Option button click (if any)
            $('#btnAddOption').click(function () {
                // Clear the form when opening modal
                $('#optionText').val('');
                $('#isCorrect').prop('checked', false);
                $('#btnSaveOption').text('Add Option').removeClass('btn-warning').addClass('btn-primary');
                $('#addOptionForm')[0].reset();

                // Clear options table
                $('#optionsTableBody').empty();
            });

            // Function to load options for a specific question
            function loadOptionsForQuestion(questionId, trainingMasterID) {
                console.log('Loading options for question:', questionId, 'TrainingMasterID:', trainingMasterID);

                // Show loading state
                $('#optionsTableBody').html(`
                    <tr>
                        <td colspan="4" class="text-center py-3">
                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                            Loading options...
                        </td>
                    </tr>
                `);

                // Make AJAX call to get options for this question
                $.ajax({
                    url: '@Url.Action("GetOptionsByQuestionId", "AddQuestionsAndOptions")',
                    type: 'GET',
                    data: {
                        questionId: questionId,
                        trainingMasterID: trainingMasterID
                    },
                    success: function (response) {
                        console.log('Options loaded:', response);

                        if (response.success) {
                            if (response.data && response.data.length > 0) {
                                // Clear the table
                                $('#optionsTableBody').empty();

                                // Populate options
                                response.data.forEach(function (option, index) {
                                    const optionRow = `
                                        <tr data-option-id="${option.id || ''}" data-question-id="${option.questionID || ''}" data-training-master-id="${option.trainingMasterID || ''}">
                                            <td style="text-align: center; vertical-align: middle;">${index + 1}</td>
                                            <td style="text-align: left; vertical-align: middle; padding: 8px;">${option.optionText || 'No option text'}</td>
                                            <td style="text-align: center; vertical-align: middle;">
                                                ${option.isCorrect ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}
                                            </td>
                                            <td style="text-align: center; vertical-align: middle;">
                                                <button type="button" class="btn btn-sm btn-outline-primary me-1 btnEditOption"
                                                        data-option-id="${option.id || ''}"
                                                        data-option-text="${option.optionText || ''}"
                                                        data-is-correct="${option.isCorrect || false}"
                                                        title="Edit Option">
                                                    <i class="cv-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger btnDeleteOption"
                                                        data-option-id="${option.id || ''}" title="Delete Option">
                                                    <i class="cv-delete"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `;
                                    $('#optionsTableBody').append(optionRow);
                                });

                                // Update option counter for new additions
                                optionCounter = response.data.length + 1;

                                // Show success message with count
                                showToast(`${response.data.length} option(s) loaded successfully`, 'success');
                            } else {
                                // No options found
                                $('#optionsTableBody').html(`
                                    <tr>
                                        <td colspan="4" class="text-center text-muted py-4">
                                            <i class="cv-no-data fs-3 d-block mb-2"></i>
                                            <span>No options found for this question. Add your first option below.</span>
                                        </td>
                                    </tr>
                                `);
                                optionCounter = 1;
                                showToast('No options found for this question', 'info');
                            }
                        } else {
                            // Error from server
                            $('#optionsTableBody').html(`
                                <tr>
                                    <td colspan="4" class="text-center text-danger py-4">
                                        <i class="cv-error fs-3 d-block mb-2"></i>
                                        <span>${response.message || 'Error loading options'}</span>
                                    </td>
                                </tr>
                            `);
                            showToast(response.message || 'Error loading options', 'danger');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Error loading options:', error);
                        $('#optionsTableBody').html(`
                            <tr>
                                <td colspan="4" class="text-center text-danger py-4">
                                    <i class="cv-error fs-3 d-block mb-2"></i>
                                    <span>Error loading options. Please try again.</span>
                                </td>
                            </tr>
                        `);
                        showToast('Error loading options', 'danger');
                    }
                });
            }

            // Save Option button click
            $('#btnSaveOption').click(function () {
                const optionText = $('#optionText').val().trim();
                const isCorrect = $('#isCorrect').is(':checked');
                const questionId = $('#AddOptionsModal').data('question-id');
                const trainingMasterID = $('#AddOptionsModal').data('training-master-id');

                if (!optionText) {
                    showToast('Please enter option text', 'danger');
                    $('#optionText').focus();
                    return;
                }

                if (!questionId) {
                    showToast('Question ID is required', 'danger');
                    return;
                }

                if (!trainingMasterID) {
                    showToast('Training Master ID is required', 'danger');
                    return;
                }

                // Show loading state
                const saveBtn = $(this);
                const originalText = saveBtn.text();
                saveBtn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i>Saving...');

                // Prepare data for saving
                const optionData = {
                    OptionText: optionText,
                    IsCorrect: isCorrect,
                    QuestionID: questionId,
                    TrainingMasterID: trainingMasterID
                };

                console.log('Saving option:', optionData);

                // Check if we're editing or adding
                const isEditing = $(this).hasClass('btn-warning');

                if (isEditing) {
                    // Update existing option
                    const editingRow = $('#optionsTableBody tr.editing');
                    const optionId = editingRow.data('option-id');
                    optionData.ID = optionId;

                    // Call update API
                    saveOptionToServer(optionData, true, editingRow, saveBtn, originalText);
                } else {
                    // Add new option
                    optionData.ID = "0"; // New option

                    // Call save API
                    saveOptionToServer(optionData, false, null, saveBtn, originalText);
                }
            });

            // Function to save option to server
            function saveOptionToServer(optionData, isUpdate, editingRow, saveBtn, originalText) {
                $.ajax({
                    url: '@Url.Action("AddOption", "AddQuestionsAndOptions")',
                    type: 'POST',
                    data: optionData,
                    success: function (response) {
                        console.log('Option save response:', response);

                        if (response.success) {
                            if (isUpdate && editingRow) {
                                // Update existing row
                                editingRow.find('td:eq(1)').text(optionData.OptionText);
                                editingRow.find('td:eq(2)').html(
                                    optionData.IsCorrect ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'
                                );
                                editingRow.removeClass('editing table-warning');
                                saveBtn.text('Add Option').removeClass('btn-warning').addClass('btn-primary');
                            } else {
                                // Add new row
                                const newRow = `
                                    <tr data-option-id="${response.optionId || ''}" data-question-id="${optionData.QuestionID || ''}" data-training-master-id="${optionData.TrainingMasterID || ''}">
                                        <td style="text-align: center; vertical-align: middle;">${optionCounter}</td>
                                        <td style="text-align: left; vertical-align: middle; padding: 8px;">${optionData.OptionText}</td>
                                        <td style="text-align: center; vertical-align: middle;">
                                            ${optionData.IsCorrect ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}
                                        </td>
                                        <td style="text-align: center; vertical-align: middle;">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1 btnEditOption"
                                                    data-option-id="${response.optionId || ''}"
                                                    data-option-text="${optionData.OptionText}"
                                                    data-is-correct="${optionData.IsCorrect}"
                                                    title="Edit Option">
                                                <i class="cv-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger btnDeleteOption"
                                                    data-option-id="${response.optionId || ''}" title="Delete Option">
                                                <i class="cv-delete"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `;

                                // Remove "no options" message if it exists
                                if ($('#optionsTableBody tr td[colspan="4"]').length > 0) {
                                    $('#optionsTableBody').empty();
                                }

                                $('#optionsTableBody').append(newRow);
                                optionCounter++;
                            }

                            // Clear form
                            $('#addOptionForm')[0].reset();

                            // Show success message
                            showToast(response.message || 'Option saved successfully!', 'success');
                        } else {
                            showToast(response.message || 'Failed to save option', 'danger');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Error saving option:', error);
                        showToast('An error occurred while saving the option', 'danger');
                    },
                    complete: function () {
                        // Reset button state
                        saveBtn.prop('disabled', false).text(originalText);
                    }
                });
            }

            // Edit Option button click
            $(document).on('click', '.btnEditOption', function () {
                const row = $(this).closest('tr');
                const optionId = $(this).data('option-id') || row.data('option-id');
                const optionText = $(this).data('option-text') || row.find('td:eq(1)').text();
                const isCorrect = $(this).data('is-correct') || row.find('td:eq(2) .badge').hasClass('bg-success');

                console.log('Edit Option clicked:', {
                    optionId: optionId,
                    optionText: optionText,
                    isCorrect: isCorrect
                });

                // Fill form with existing data
                $('#optionText').val(optionText);
                $('#isCorrect').prop('checked', isCorrect);

                // Mark row as being edited and store option ID
                $('#optionsTableBody tr').removeClass('editing table-warning');
                row.addClass('editing table-warning').data('option-id', optionId);

                // Change button to update mode
                $('#btnSaveOption').text('Update Option').removeClass('btn-primary').addClass('btn-warning');

                // Focus on option text field
                $('#optionText').focus();

                showToast('Option loaded for editing', 'info');
            });

            // Delete Option button click
            $(document).on('click', '.btnDeleteOption', function () {
                if (confirm('Are you sure you want to delete this option?')) {
                    $(this).closest('tr').remove();

                    // Renumber the options
                    $('#optionsTableBody tr').each(function (index) {
                        $(this).find('td:first').text(index + 1);
                    });

                    showToast('Option deleted successfully!', 'danger');
                }
            });

            // Reset form when modal is closed
            $('#AddOptionsModal').on('hidden.bs.modal', function () {
                $('#addOptionForm')[0].reset();
                $('#optionsTableBody tr').removeClass('editing table-warning');
                $('#btnSaveOption').text('Add Option').removeClass('btn-warning').addClass('btn-primary');
            });

            // Toast notification function
            function showToast(message, type) {
                const toastHtml = `
                                                <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                                                    <div class="d-flex">
                                                        <div class="toast-body">
                                                            ${message}
                                                        </div>
                                                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                                                    </div>
                                                </div>
                                            `;

                // Create toast container if it doesn't exist
                if (!$('#toastContainer').length) {
                    $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
                }

                const $toast = $(toastHtml);
                $('#toastContainer').append($toast);

                const toast = new bootstrap.Toast($toast[0]);
                toast.show();

                // Remove toast element after it's hidden
                $toast.on('hidden.bs.toast', function () {
                    $(this).remove();
                });
            }
        });
    </script>
}