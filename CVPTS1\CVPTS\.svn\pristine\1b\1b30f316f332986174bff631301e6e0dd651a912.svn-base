﻿@model BCM.BusinessClasses.ManageUsersDetails

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers


<form asp-action="EditManageUsers" method="post" id="editUserForm" class="needs-validation progressive-validation" novalidate>
    <div class="row row-cols-2">
        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group" hidden>
                <label for="validationCustom01" class="form-label">UserID</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-login-code"></i></span>
                    <input type="hidden" class="form-control" asp-for="UserID">
                    <input type="hidden" class="form-control" asp-for="UserRole">
                    <input type="hidden" class="form-control" asp-for="UserRoleID">
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Login Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-login-name"></i></span>
                    <input type="text" class="form-control" placeholder="Enter Login Name" asp-for="LoginName" required/>
                </div>
                <div class="invalid-feedback">Login Name is required</div>
            </div>
            <div class="form-group">
                <label class="form-label">Domain Name</label>
                <div class="d-flex align-items-center">
                    <div class="w-75">
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-domain-name"></i></span>
                            <select id="domainSelect" class="form-control" asp-for="DomainName" style="display: none;">
                                <option value="">-- Select Domain --</option>
                            </select>
                            <input type="text" id="domainInput" class="form-control" placeholder="Enter Domain Name" asp-for="DomainName" />
                        </div>
                        <div class="invalid-feedback">Domain Name</div>
                    </div>
                    <div class="ms-2">
                        <button type="button" id="btnFindDomain" class="btn btn-primary btn-sm" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                            <span id="findDomainText">Find Domain</span>
                            <span id="findDomainSpinner" class="spinner-border spinner-border-sm ms-1" style="display: none;" role="status" aria-hidden="true"></span>
                        </button>
                    </div>
                </div>
                <div id="domainMessage" class="mt-1" style="display: none;">
                    <small class="text-muted"></small>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group">
                <label class="form-label">Password</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-password"></i></span>
                    <input type="password" class="form-control" placeholder="Enter Password" asp-for="Password" id="passwordField"
                           required
                           minlength="8"
                           pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d]).{8,}$"
                           title="Password must be at least 8 characters and include uppercase, lowercase, number, and special character" />
                </div>
                <div class="invalid-feedback">Password must be at least 8 characters and include uppercase, lowercase, number, and special character</div>
            </div>
            <div class="form-group">
                <label class="form-label">AD Login Name</label>
                <div class="d-flex align-items-center">
                    <div class="w-75">
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-login-name"></i></span>
                            <select id="userSelect" class="form-control" asp-for="ADLoginName" style="display: none;">
                                <option value="">-- Select User --</option>
                            </select>
                            <input type="text" id="userInput" class="form-control" placeholder="Enter AD-Login Name" asp-for="ADLoginName" />
                        </div>
                        <div class="invalid-feedback">AD Login Name</div>
                    </div>
                    <div class="d-flex gap-1 ms-2">
                        <button type="button" id="btnFindUser" class="btn btn-primary btn-sm" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                            <span id="findUserText">Find</span>
                            <span id="findUserSpinner" class="spinner-border spinner-border-sm ms-1" style="display: none;" role="status" aria-hidden="true"></span>
                        </button>
                        <button type="button" id="btnClearUsers" class="btn btn-secondary btn-sm" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;" title="Clear search results and search again">
                            Clear
                        </button>
                    </div>
                </div>
                <div id="userMessage" class="mt-1" style="display: none;">
                    <small class="text-muted"></small>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Update</button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {

         if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    //console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
           // console.log("BCMValidation is already defined");
            initializeValidation();
        }
        function initializeValidation() {
           // console.log("Initializing validation for AddOrgGroup form");

            if (window.BCMValidation) {
              //  console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('editUserForm');
                if (!form) {
                    console.error("Form not found with ID: addOrgGroupForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            //console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                               // console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };
               
                // Override the validatePatternInput function similarly
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidatePatternInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                //console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                //console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                   // console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    //console.log("Form validation result:", isValid);

                    if (!isValid) {
                       // console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        // Initialize validation for the login name field
        setupLoginNameValidation();

        // Initialize validation for the password field
        setupPasswordValidation();

        // Initialize Find Domain functionality
        setupFindDomainFunctionality();

        // Initialize Find User functionality
        setupFindUserFunctionality();

        // Add button click validation
        $('button[type="submit"]').on('click', function(e) {
            e.preventDefault(); // Prevent default form submission

            const loginNameInput = document.querySelector('input[name="LoginName"]');
            const passwordInput = document.getElementById('passwordField');

            let hasErrors = false;

            // Check login name validation
            if (loginNameInput && loginNameInput.classList.contains('is-invalid')) {
                hasErrors = true;
            }

            // Explicitly trigger password validation on button click
            if (passwordInput) {
                const isPasswordValid = validatePasswordField(passwordInput);
                if (!isPasswordValid) {
                    hasErrors = true;
                }
            }

            if (hasErrors) {
                //alert('Please fix the errors before submitting the form.');
                return false;
            } else {
                // If no errors, submit the form
                $('#editUserForm').submit();
            }
        });

        // Also keep the form submission validation as a fallback
        $('#editUserForm').on('submit', function(e) {
            const loginNameInput = document.querySelector('input[name="LoginName"]');
            const passwordInput = document.getElementById('passwordField');

            let hasErrors = false;

            // Check login name validation
            if (loginNameInput && loginNameInput.classList.contains('is-invalid')) {
                hasErrors = true;
            }

            // Check password validation
            if (passwordInput && !validatePassword(passwordInput.value)) {
                passwordInput.classList.add('is-invalid');
                hasErrors = true;
            }

            if (hasErrors) {
                e.preventDefault(); // Prevent form submission if there are validation errors
                //alert('Please fix the errors before submitting the form.');
                return false;
            }
            return true;
        });
    });

    function setupLoginNameValidation() {
        const loginNameInput = document.querySelector('input[name="LoginName"]');
        if (!loginNameInput) return;

        // Get the current user ID (if in edit mode)
        const userIdInput = document.querySelector('input[name="UserID"]');
        const userId = userIdInput ? userIdInput.value : 0;

        // Add event listener for input changes
        loginNameInput.addEventListener('blur', function() {
            validateLoginName(this, userId);
        });

        // Add event listener for input changes (with debounce)
        let debounceTimer;
        loginNameInput.addEventListener('input', function() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                validateLoginName(this, userId);
            }, 500); // Wait 500ms after user stops typing
        });
    }

    function validateLoginName(input, userId) {
        const loginName = input.value.trim();
        if (!loginName) return; // Skip validation if empty

        const formGroup = input.closest('.form-group');
        const feedbackElement = formGroup.querySelector('.invalid-feedback');
        const inputGroup = input.closest('.input-group');

        // Make AJAX call to check if login name exists
        $.ajax({
            url: '@Url.Action("CheckLoginNameExists", "ManageUsers")',
            type: 'GET',
            data: {
                loginName: loginName,
                userId: userId
            },
            success: function(response) {
                if (response.exists) {
                    // Login name already exists
                    input.classList.add('is-invalid');
                    if (inputGroup) inputGroup.classList.add('is-invalid');
                    feedbackElement.textContent = "This Login Name already exists. Please choose another.";
                    feedbackElement.style.display = 'block';
                } else {
                    // Login name is available
                    input.classList.remove('is-invalid');
                    if (inputGroup) inputGroup.classList.remove('is-invalid');
                    feedbackElement.style.display = 'none';
                }
            },
            error: function(xhr, status, error) {
                // Error occurred during validation
                console.error("Error checking login name availability:", error);
                // Remove validation state to allow form submission
                input.classList.remove('is-invalid');
                if (inputGroup) inputGroup.classList.remove('is-invalid');
                feedbackElement.style.display = 'none';
            }
        });
    }


    function setupPasswordValidation() {
        const passwordInput = document.getElementById('passwordField');
        if (!passwordInput) return;

        // Add event listener for input changes
        passwordInput.addEventListener('input', function() {
            validatePasswordField(this);
        });

        // Add event listener for blur event
        passwordInput.addEventListener('blur', function() {
            validatePasswordField(this);
        });
    }

    function validatePasswordField(input) {
        const password = input.value;
        const formGroup = input.closest('.form-group');
        const feedbackElement = formGroup.querySelector('.invalid-feedback');
        const inputGroup = input.closest('.input-group');

        if (!password) {
            // Empty password
            input.classList.add('is-invalid');
            if (inputGroup) inputGroup.classList.add('is-invalid');
            feedbackElement.textContent = "Password is required";
            feedbackElement.style.display = 'block';
            return false;
        }

        const isValid = validatePassword(password);

        if (!isValid) {
            // Invalid password
            input.classList.add('is-invalid');
            if (inputGroup) inputGroup.classList.add('is-invalid');
            feedbackElement.style.display = 'block';
            return false;
        } else {
            // Valid password
            input.classList.remove('is-invalid');
            if (inputGroup) inputGroup.classList.remove('is-invalid');
            feedbackElement.style.display = 'none';
            return true;
        }
    }

    function validatePassword(password) {
        // Check if password meets all requirements
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumber = /\d/.test(password);
        const hasSpecialChar = /[^A-Za-z\d]/.test(password);

        return (
            password.length >= minLength &&
            hasUpperCase &&
            hasLowerCase &&
            hasNumber &&
            hasSpecialChar
        );
    }

    function setupFindDomainFunctionality() {
        const btnFindDomain = document.getElementById('btnFindDomain');
        const domainInput = document.getElementById('domainInput');
        const domainSelect = document.getElementById('domainSelect');

        if (!btnFindDomain) return;

        // Add click event listener to Find Domain button
        btnFindDomain.addEventListener('click', function(e) {
            e.preventDefault();
            findDomains();
        });

        // Add change event listener to domain select
        if (domainSelect) {
            domainSelect.addEventListener('change', function() {
                // Update the hidden input value when dropdown selection changes
                if (domainInput) {
                    domainInput.value = this.value;
                    // Trigger change event for form validation
                    domainInput.dispatchEvent(new Event('change'));
                }
            });
        }
    }

    function findDomains() {
        const btnFindDomain = document.getElementById('btnFindDomain');
        const findDomainText = document.getElementById('findDomainText');
        const findDomainSpinner = document.getElementById('findDomainSpinner');
        const domainMessage = document.getElementById('domainMessage');
        const domainInput = document.getElementById('domainInput');
        const domainSelect = document.getElementById('domainSelect');

        // Show loading state
        if (findDomainText) findDomainText.textContent = 'Finding...';
        if (findDomainSpinner) findDomainSpinner.style.display = 'inline-block';
        if (btnFindDomain) btnFindDomain.disabled = true;
        if (domainMessage) {
            domainMessage.style.display = 'block';
            domainMessage.querySelector('small').textContent = 'Searching for domains...';
            domainMessage.querySelector('small').className = 'text-info';
        }

        // Make AJAX call to find domains
        $.ajax({
            url: '@Url.Action("FindDomains", "ManageUsers")',
            type: 'POST',
            success: function(response) {
                resetFindDomainButton();

                if (response.success && response.domains && response.domains.length > 1) {
                    // Populate dropdown with domains
                    populateDomainDropdown(response.domains);

                    // Switch from input to dropdown
                    if (domainInput) domainInput.style.display = 'none';
                    if (domainSelect) domainSelect.style.display = 'block';

                    // Show success message
                    showDomainMessage(response.message || 'Domains found successfully', 'text-success');

                    // Auto-select first domain if available
                    if (response.domains.length > 1 && domainSelect) {
                        domainSelect.value = response.domains[1].value;
                        domainInput.value = response.domains[1].value;
                    }
                } else {
                    showDomainMessage('No domains found or domain discovery failed', 'text-warning');
                }
            },
            error: function(xhr, status, error) {
                resetFindDomainButton();
                showDomainMessage('Error occurred while finding domains. Please try again.', 'text-danger');
                console.error('Find domains error:', error);
            }
        });
    }

    function populateDomainDropdown(domains) {
        const domainSelect = document.getElementById('domainSelect');
        if (!domainSelect) return;

        // Clear existing options
        domainSelect.innerHTML = '';

        // Add domains to dropdown
        domains.forEach(function(domain) {
            const option = document.createElement('option');
            option.value = domain.value;
            option.textContent = domain.text;
            domainSelect.appendChild(option);
        });
    }

    function resetFindDomainButton() {
        const btnFindDomain = document.getElementById('btnFindDomain');
        const findDomainText = document.getElementById('findDomainText');
        const findDomainSpinner = document.getElementById('findDomainSpinner');

        if (findDomainText) findDomainText.textContent = 'Find Domain';
        if (findDomainSpinner) findDomainSpinner.style.display = 'none';
        if (btnFindDomain) btnFindDomain.disabled = false;
    }

    function showDomainMessage(message, className) {
        const domainMessage = document.getElementById('domainMessage');
        if (domainMessage) {
            const messageElement = domainMessage.querySelector('small');
            if (messageElement) {
                messageElement.textContent = message;
                messageElement.className = className;
            }
            domainMessage.style.display = 'block';

            // Hide message after 5 seconds
            setTimeout(function() {
                domainMessage.style.display = 'none';
            }, 5000);
        }
    }

    // Find User functionality
    function setupFindUserFunctionality() {
        const btnFindUser = document.getElementById('btnFindUser');
        const btnClearUsers = document.getElementById('btnClearUsers');
        const userInput = document.getElementById('userInput');
        const userSelect = document.getElementById('userSelect');

        if (!btnFindUser) return;

        // Add click event listener to Find User button
        btnFindUser.addEventListener('click', function(e) {
            e.preventDefault();
            findUsers();
        });

        // Add click event listener to Clear Users button
        if (btnClearUsers) {
            btnClearUsers.addEventListener('click', function(e) {
                e.preventDefault();
                clearUserSearch();
            });
        }

        // Add change event listener to user select
        if (userSelect) {
            userSelect.addEventListener('change', function() {
                // Update the hidden input value when dropdown selection changes
                if (userInput) {
                    userInput.value = this.value;
                    // Trigger change event for form validation
                    userInput.dispatchEvent(new Event('change'));
                }
            });
        }
    }

    function findUsers() {
        const btnFindUser = document.getElementById('btnFindUser');
        const findUserText = document.getElementById('findUserText');
        const findUserSpinner = document.getElementById('findUserSpinner');
        const userMessage = document.getElementById('userMessage');
        const userInput = document.getElementById('userInput');
        const userSelect = document.getElementById('userSelect');
        const domainInput = document.getElementById('domainInput');
        const domainSelect = document.getElementById('domainSelect');

        // Get the selected domain
        let selectedDomain = '';
        if (domainSelect && domainSelect.style.display !== 'none') {
            selectedDomain = domainSelect.value;
        } else if (domainInput) {
            selectedDomain = domainInput.value;
        }

        // Get the search term from AD Login Name input field
        let searchTerm = '';
        if (userInput && userInput.style.display !== 'none') {
            searchTerm = userInput.value.trim();
        }

        // Show loading state
        if (findUserText) findUserText.textContent = 'Finding...';
        if (findUserSpinner) findUserSpinner.style.display = 'inline-block';
        if (btnFindUser) btnFindUser.disabled = true;
        if (userMessage) {
            userMessage.style.display = 'block';
            if (searchTerm) {
                userMessage.querySelector('small').textContent = `Searching for users matching "${searchTerm}"...`;
            } else {
                userMessage.querySelector('small').textContent = 'Searching for users...';
            }
            userMessage.querySelector('small').className = 'text-info';
        }

        // Make AJAX call to find users
        $.ajax({
            url: '@Url.Action("FindUsers", "ManageUsers")',
            type: 'POST',
            data: {
                domainName: selectedDomain,
                searchTerm: searchTerm
            },
            success: function(response) {
                resetFindUserButton();

                if (response.success && response.users && response.users.length > 1) {
                    // Populate dropdown with users
                    populateUserDropdown(response.users);

                    // Switch from input to dropdown
                    if (userInput) userInput.style.display = 'none';
                    if (userSelect) userSelect.style.display = 'block';

                    // Show success message
                    let message = response.message || 'Users found successfully';
                    if (searchTerm) {
                        message = `Found ${response.users.length - 1} user(s) matching "${searchTerm}"`;
                    }
                    showUserMessage(message, 'text-success');

                    // Auto-select first user if available
                    if (response.users.length > 1 && userSelect) {
                        userSelect.value = response.users[1].value;
                        userInput.value = response.users[1].value;
                    }
                } else {
                    let message = 'No users found or user discovery failed';
                    if (searchTerm) {
                        message = `No users found matching "${searchTerm}". Try a different search term or leave blank to see all users.`;
                    }
                    showUserMessage(message, 'text-warning');
                }
            },
            error: function(xhr, status, error) {
                resetFindUserButton();
                showUserMessage('Error occurred while finding users. Please try again.', 'text-danger');
                console.error('Find users error:', error);
            }
        });
    }

    function clearUserSearch() {
        const userInput = document.getElementById('userInput');
        const userSelect = document.getElementById('userSelect');
        const userMessage = document.getElementById('userMessage');

        // Switch back to input mode
        if (userSelect) userSelect.style.display = 'none';
        if (userInput) {
            userInput.style.display = 'block';
            userInput.value = ''; // Clear the input field
            userInput.focus(); // Focus on the input for immediate typing
        }

        // Hide user message
        if (userMessage) userMessage.style.display = 'none';

        // Clear dropdown options
        if (userSelect) {
            userSelect.innerHTML = '<option value="">-- Select User --</option>';
        }
    }

    function populateUserDropdown(users) {
        const userSelect = document.getElementById('userSelect');
        if (!userSelect) return;

        // Clear existing options
        userSelect.innerHTML = '';

        // Add users to dropdown
        users.forEach(function(user) {
            const option = document.createElement('option');
            option.value = user.value;
            option.textContent = user.text;
            userSelect.appendChild(option);
        });
    }

    function resetFindUserButton() {
        const btnFindUser = document.getElementById('btnFindUser');
        const findUserText = document.getElementById('findUserText');
        const findUserSpinner = document.getElementById('findUserSpinner');

        if (findUserText) findUserText.textContent = 'Find User';
        if (findUserSpinner) findUserSpinner.style.display = 'none';
        if (btnFindUser) btnFindUser.disabled = false;
    }

    function showUserMessage(message, className) {
        const userMessage = document.getElementById('userMessage');
        if (userMessage) {
            const messageElement = userMessage.querySelector('small');
            if (messageElement) {
                messageElement.textContent = message;
                messageElement.className = className;
            }
            userMessage.style.display = 'block';

            // Hide message after 5 seconds
            setTimeout(function() {
                userMessage.style.display = 'none';
            }, 5000);
        }
    }
</script>