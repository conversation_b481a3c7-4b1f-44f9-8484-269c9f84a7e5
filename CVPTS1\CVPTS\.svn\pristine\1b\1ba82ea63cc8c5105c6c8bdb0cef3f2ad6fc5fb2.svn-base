<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="CryptographyHelper.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog.Extensions.Logging.File" Version="3.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BCM.BusinessClasses\BCM.BusinessClasses.csproj" />
    <ProjectReference Include="..\BCM.BusinessFacadeSrv\BCM.BusinessFacadeSrv.csproj" />
    <ProjectReference Include="..\BCM.BusinessProcessComponents\BCM.BusinessProcessComponents.csproj" />
    <ProjectReference Include="..\CVLogger\CVLogger.csproj" />
  </ItemGroup>

</Project>
