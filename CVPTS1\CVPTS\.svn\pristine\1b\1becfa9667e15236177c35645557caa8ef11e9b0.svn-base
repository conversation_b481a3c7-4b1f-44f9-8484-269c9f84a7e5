﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using BCM.Shared
@{


}

<div class="card">
    <div class="card-body" style="height: calc(100vh - 130px);overflow: auto;">
        <div class="tree-menu">
            <ul class="tree">
                @if (ViewBag.ThirdPartiesList != null)
                {
                    foreach (var thirdPartyList in ViewBag.ThirdPartiesList)
                    {
                        <li>
                            <div class="align-items-center w-50 d-inline-flex">
                                <span class="fw-medium w-auto me-2" role="button">@thirdPartyList.ThirdPartyName</span>
                            </div>
                            <ul class="sub-parent">
                                <li>
                                    <table class="table table-sm table-bordered mb-0">
                                        <thead>
                                            <tr>
                                                <th class="text-center">Min RTO</th>
                                                <th class="text-center">Min RPO</th>
                                                <th class="text-center">IsCritical</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="text-center">@Utilities.GetFormattedRTO(Convert.ToInt32(thirdPartyList.MinimumRTO))</td>
                                                <td class="text-center">@Utilities.GetFormattedRPO(Convert.ToInt32(thirdPartyList.MinimumRPO))</td>
                                                <td class="text-center">
                                                    <span class="@Utilities.isCriticalCss(thirdPartyList.IsCritical) fw-semibold">
                                                        @Utilities.GetYesNo(thirdPartyList.IsCritical)
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </li>
                                @{
                                    foreach (var processList in ViewBag.ProcessList)
                                    {
                                        processList.UnitName = string.IsNullOrEmpty(processList.UnitName) ? "NA" : processList.UnitName;
                                        processList.DepartmentName = string.IsNullOrEmpty(processList.DepartmentName) ? "NA" : processList.DepartmentName;
                                        processList.SubFunctionName = string.IsNullOrEmpty(processList.SubFunctionName) ? "NA" : processList.SubFunctionName;


                                        if (thirdPartyList.ThirdPartyID == processList.ThirdPartyID)
                                        {
                                            <li>
                                                <div class="align-items-center w-50 d-inline-flex">
                                                    <span class="fw-medium w-auto me-2" role="button">@processList.ProcessName</span>&nbsp;&nbsp;
                                                    @if (@processList.ProcessCode != string.Empty)
                                                    {
                                                        <span class="fw-semibold text-warning">  ( @processList.ProcessCode )</span>
                                                    }
                                                </div>
                                                <ul class="sub-parent">
                                                    <li>
                                                        <table class="table table-sm table-bordered mb-0 align-middle">
                                                            <thead>
                                                                <tr>
                                                                    
                                                                    <th class="text-center" style="width:15%">Process Name</th>
                                                                    <th class="text-center" style="width:15%">Owner Details</th>
                                                                    @*<th class="text-center" style="width:15%">Org Level</th>*@
                                                                    <th class="text-center" style="width:15%">Unit</th>
                                                                    <th class="text-center" style="width:15%">Department</th>
                                                                    <th class="text-center" style="width:15%">SubDepartment</th>
                                                                    @*<th class="text-center" style="width:15%">RTO / MAO / RPO</th>*@
                                                                    <th class="text-center" style="width:15%">RTO</th>
                                                                    <th class="text-center" style="width:10%">IsCritical</th>
                                                                    <th class="text-center" style="width:10%">Status</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                
                                                                <tr>
                                                              
                                            
                                                                    <td style="width:15%">
                                                                        <div class="d-grid">
                                                                            <span>
                                                                            @processList.ProcessName
                                                                            <span class="text-info">(@processList.Version)</span>
                                                                            </span>
                                                                            @* <span>Version: @processList.Version</span>
                                                                           <span>
                                                                                Is Under BCM Scope:
                                                                                @if (@processList.ProcessCode != "")
                                                                                {
                                                                                    <span class="text-success fw-semibold">
                                                                                        Yes
                                                                                    </span>
                                                                                }
                                                                                else
                                                                                {
                                                                                    <span class="text-danger fw-semibold">
                                                                                        No
                                                                                    </span>
                                                                                }
                                                                            </span>*@
                                                                        </div>
                                                                    </td>
                                                                    <td style="width:15%">
                                                                        <table>
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="fw-semibold"><i class="cv-user"></i></td>
                                                                                     <td> : </td> 
                                                                                    <td>@processList.ProcessOwnerName</td>
                                                                                </tr>
                                                                                @* <tr>
                                                                                    <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.OwnerMail</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.OwnerMobile</td>
                                                                                </tr>*@
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                  @*  <td style="width:15%">
                                                                        <table>
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="fw-semibold"><i class="cv-company"></i></td>
                                                                                    <td> : </td>
                                                                                    <td>
                                                                                        @processList.OrganizationName
                                                                                    </td>
                                                                                </tr>
                                                                                <tr title="Unit">
                                                                                    <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.UnitName</td>
                                                                                </tr>
                                                                                <tr title="Department">
                                                                                    <td class="fw-semibold"><i class="cv-department"></i> </td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.DepartmentName</td>
                                                                                </tr>
                                                                                <tr title="Sub Department">
                                                                                    <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.SubFunctionName</td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>*@
                                                                    <td style="width:15%">
                                                                        <table>
                                                                            <tbody>
                                                                               @* <tr>
                                                                                    <td class="fw-semibold"><i class="cv-company"></i></td>
                                                                                    <td> : </td>
                                                                                    <td>
                                                                                        @processList.OrganizationName
                                                                                    </td>
                                                                                </tr>*@
                                                                                <tr title="Unit">
                                                                                    <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                                                                   <td> : </td> 
                                                                                    <td>@processList.UnitName</td>
                                                                                </tr>
                                                                               @* <tr title="Department">
                                                                                    <td class="fw-semibold"><i class="cv-department"></i> </td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.DepartmentName</td>
                                                                                </tr>
                                                                                <tr title="Sub Department">
                                                                                    <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.SubFunctionName</td>
                                                                                </tr>*@
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                             <td style="width:15%">
                                                                        <table>
                                                                            <tbody>
                                                                                @* <tr>
                                                                                    <td class="fw-semibold"><i class="cv-company"></i></td>
                                                                                    <td> : </td>
                                                                                    <td>
                                                                                        @processList.OrganizationName
                                                                                    </td>
                                                                                </tr>
                                                                                <tr title="Unit">
                                                                                    <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.UnitName</td>
                                                                                </tr>*@
                                                                                <tr title="Department">
                                                                                    <td class="fw-semibold"><i class="cv-department"></i> </td>
                                                                                  <td> : </td> 
                                                                                    <td>@processList.DepartmentName</td>
                                                                                </tr>
                                                                                @*<tr title="Sub Department">
                                                                                    <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.SubFunctionName</td>
                                                                                </tr>*@
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                    <td style="width:15%">
                                                                        <table>
                                                                            <tbody>
                                                                                @* <tr>
                                                                                    <td class="fw-semibold"><i class="cv-company"></i></td>
                                                                                    <td> : </td>
                                                                                    <td>
                                                                                        @processList.OrganizationName
                                                                                    </td>
                                                                                </tr>
                                                                                <tr title="Unit">
                                                                                    <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.UnitName</td>
                                                                                </tr>
                                                                                <tr title="Department">
                                                                                    <td class="fw-semibold"><i class="cv-department"></i> </td>
                                                                                    <td>:</td>
                                                                                    <td>@processList.DepartmentName</td>
                                                                                </tr>*@
                                                                                <tr title="Sub Department">
                                                                                    <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                                                                   <td> : </td>
                                                                                    <td>@processList.SubFunctionName</td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>

                                                                    <td style="width:15%">
                                                                        <table>
                                                                            <tbody>
                                                                                <tr title="RTO">
                                                                                    <td class="fw-semibold"><i class="cv-RTO me-1"></i></td>
                                                                                    <td> : </td>
                                                                                    <td>
                                                                                        @if (!string.IsNullOrEmpty(processList.OwnerRTO))
                                                                                        {
                                                                                            @Utilities.GetFormattedRTO(Convert.ToInt32(processList.OwnerRTO))
                                                                                        }                                                                                        
                                                                                    </td>
                                                                                </tr>
                                                                                @*<tr title="RPO">
                                                                                    <td class="fw-semibold"><i class="cv-rpo"></i></td>
                                                                                    <td>:</td>
                                                                                    <td>
                                                                                        @if (!string.IsNullOrEmpty(processList.RPO))
                                                                                        {
                                                                                            @Utilities.GetFormattedRPO(Convert.ToInt32(processList.RPO))
                                                                                        }                                                                                        
                                                                                    </td>
                                                                                </tr>*@
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                    <td class="text-center" style="width:10%">
                                                                        <span class="@Utilities.isCriticalCss(processList.IsCritical) fw-semibold">
                                                                            @Utilities.GetYesNo(processList.IsCritical)
                                                                        </span>
                                                                    </td>
                                                                    <td class="text-info text-center" style="width:10%">
                                                                        <p class="@Utilities.ApprovalStatusWiseTextClass(processList.StatusID)">
                                                                            <i class="@Utilities.ApprovalStatusWiseClass(processList.StatusID) me-1"></i>
                                                                            @Utilities.ApprovalStatus(processList.StatusID)
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </li>
                                                </ul>
                                            </li>
                                        }
                                    }
                                }
                            </ul>
                        </li>
                    }
                }
            </ul>
        </div>
    </div>
</div>

<script>
    $(function () {
        // Hide all lists except the outermost.
        $('ul.tree ul').show();

        $('.tree li > ul').each(function (i) {
            var $subUl = $(this);
            var $parentLi = $subUl.parent('li');
            var $toggleIcon = '<i class="js-toggle-icon">-</i>';

            $parentLi.addClass('has-children');

            $parentLi.prepend($toggleIcon).find('.js-toggle-icon').on('click', function () {
                $(this).text($(this).text() == '-' ? '+' : '-');
                $subUl.slideToggle('fast');
            });
        });


    });
</script>