$(document).ready(function () {
    let isValid = true;
    let defaultimageSvg = document.getElementById("defaultimageSvg");
    let biareporthtml = document.getElementById("biareporthtml");
    defaultimageSvg.classList.remove('d-none');

    $(document).on('change', '#orglist', function () {
        DefaultImageLoader();
        clearSelectizeDropdown('#unitlist');
        clearSelectizeDropdown('#departmentlist');
        clearSelectizeDropdown('#subdepartmentlist');
        var selectedOrgID = $(this).val();

        // Only load units if an organization is selected
        if (selectedOrgID) {
            $.ajax({
                url: "/BCMReports/ViewBIAReport/GetAllUnits",
                dataType: "json",
                traditional: true,
                type: 'GET',
                data: {
                    organizationId: selectedOrgID
                },
                success: function (response) {
                    if (response?.data && response.data?.length != 0) {
                        let selectizeInstance = $('#unitlist')[0].selectize;
                        response?.data && response.data.forEach(({ unitID, unitName }) => {
                            if (unitID && unitName) {
                                selectizeInstance.addOption({ value: unitID, text: unitName });
                            }
                        });
                    }
                },
                error: function (error) {
                    console.log('Error loading units:', error);
                }
            });
        }
    });
    $(document).on('change', '#unitlist', function () {
        DefaultImageLoader();
        clearSelectizeDropdown('#departmentlist');
        clearSelectizeDropdown('#subdepartmentlist');
        var unitIds = $(this).val();

        // Only load departments if a unit is selected
        if (unitIds) {
            $.ajax({
                url: "/BCMReports/ViewBIAReport/GetAllDepartments",
                dataType: "json",
                traditional: true,
                type: 'GET',
                data: {
                    unitId: unitIds
                },
                success: function (response) {
                    if (response?.data && response.data?.length != 0) {
                        let selectizeInstance = $('#departmentlist')[0].selectize;
                        response?.data && response.data.forEach(({ departmentID, departmentName }) => {
                            if (departmentID && departmentName) {
                                selectizeInstance.addOption({ value: departmentID, text: departmentName });
                            }
                        });
                    }
                },
                error: function (error) {
                    console.log('Error loading departments:', error);
                }
            });
        }
    });
    $(document).on('change', '#departmentlist', function () {
        DefaultImageLoader();
        clearSelectizeDropdown('#subdepartmentlist');
        var departmentId = $(this).val();

        // Only load subdepartments if a department is selected
        if (departmentId) {
            $.ajax({
                url: "/BCMReports/ViewBIAReport/GetAllSubDepartments",
                dataType: "json",
                traditional: true,
                type: 'GET',
                data: {
                    departmentId: departmentId
                },
                success: function (response) {
                    if (response?.data && response.data?.length != 0) {
                        let selectizeInstance = $('#subdepartmentlist')[0].selectize;
                        response?.data && response.data.forEach(({ subFunctionID, subFunctionName }) => {
                            if (subFunctionID && subFunctionName) {
                                selectizeInstance.addOption({ value: subFunctionID, text: subFunctionName });
                            }
                        });
                    }
                },
                error: function (error) {
                    console.log('Error loading subdepartments:', error);
                }
            });
        }
    });
    $(document).on('click', '#btnbiareport', function () {
        let organizationId = $('#orglist').val() || 0;
        let unitId = $('#unitlist').val() || 0;
        let departmentId = $('#departmentlist').val() || 0;
        let subDepartmentId = $('#subdepartmentlist').val() || 0;

        console.log('BIA Report - Filters:', { organizationId, unitId, departmentId, subDepartmentId });

        if (!isValid) return;

        $('#loadingSvg').removeClass('d-none');
        $('#defaultimageSvg').addClass('d-none');
        $('#nodatafoundSvg').addClass('d-none');
        $('#biareporthtml').show();
        // No validation required - show all records if no filters selected
        $.ajax({
            url: "/BCMReports/ViewBIAReport/GetBIAReportData",
            dataType: "json",
            type: 'GET',
            traditional: true,
            data: {
                organizationId: organizationId,
                unitId: unitId,
                departmentId: departmentId,
                subDepartmentId: subDepartmentId
            },
            success: function (response) {               
                if (response?.data && response.success !== false) {
                    loadAjax('/BCMReports/ViewBIAReport/LoadReport', "BIAReport", response.data);
                    $('#defaultimageSvg').addClass('d-none');
                } else {
                    $('#loadingSvg').addClass('d-none');
                    biareporthtml.style.display = 'none';
                    $('#nodatafoundSvg').removeClass('d-none');
                }
            },
            error: function (error) {
                $('#loadingSvg').addClass('d-none');
                $('#nodatafoundSvg').removeClass('d-none');
            }
        });
        function loadAjax(loadReportUrl, reportName, response) {
            console.log('BIA Report - Loading report with data:', response);
            $.ajax({
                url: loadReportUrl,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    reportName: reportName,
                    responseData: response
                }),
                success: function (data) {
                    if (data != null) {
                        $('#loadingSvg').addClass('d-none');
                        $('#biareporthtml').html(data).show();
                        $('#defaultimageSvg').addClass('d-none');
                    } else {
                        $('#biareporthtml').style.display = 'none';
                        $('#nodatafoundSvg').removeClass('d-none');
                    }
                },
                error: function (error) {
                    $('#biareporthtml').style.display = 'none';
                    $('#loadingSvg').addClass('d-none');
                    $('#nodatafoundSvg').removeClass('d-none');
                }
            });
        }

    });
    function clearSelectizeDropdown(selector) {
        const instance = $(selector)[0]?.selectize;
        if (instance) {
            instance.clear();
            instance.clearOptions();
        }
    }
    $(document).on('click', '.dx-menu-item-wrapper', function () {
        var exportOptions = document.querySelectorAll('.dxrd-preview-export-item-text');
        exportOptions.forEach(function (option) {
            var text = option.textContent.trim().toLowerCase();
            if (text == 'pdf') {
                option.offsetParent.click();
            }
        });
        $(".dx-submenu").hide();
    });

    function DefaultImageLoader() {
        defaultimageSvg.classList.remove('d-none');
        $('#nodatafoundSvg').addClass('d-none');
        biareporthtml.style.display = 'none';
    }
});
