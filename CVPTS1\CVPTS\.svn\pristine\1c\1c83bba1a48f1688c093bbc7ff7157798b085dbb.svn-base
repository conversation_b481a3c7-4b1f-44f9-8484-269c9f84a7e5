﻿@model BCM.BusinessClasses.Impact

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<form asp-action="EditImpact" method="post">
    <div class="row row-cols-1">
        <div class="col">
            <div class="form-group" hidden>              
                <div class="input-group">                    
                    <input type="hidden" class="form-control"  asp-for="ImpactID">
                </div>               
            </div>
            <div class="form-group">
                <label class="form-label">Impact Category</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-impact_category"></i></span>
                    @*   <select class="form-select-sm form-select">
                    <option value="value">Select Impact Category</option>
                    </select> *@
                    <select class="form-select form-control" id="headlist" autocomplete="off" aria-label="Default select example" asp-for="ImpactTypeID" required>
                        <option selected disabled value="">-- All ImpactCategory --</option>
                        @foreach (var objImpactCategory in ViewBag.ImpactCategory)
                        {
                            <option value="@objImpactCategory.Value">@objImpactCategory.Text</option>
                        }
                    </select>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group w-100">
                <label class="form-label">Impact Detail</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-impact_detail"></i></span>
                    <textarea class="form-control" placeholder="Activity Details" style="height:0px" asp-for="ImpactDetails" required></textarea>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group w-100">
                <label class="form-label">Matrix ID</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-customer-id"></i></span>
                    <input type="text" class="form-control" placeholder="Enter The Matrix ID" asp-for="MatrixId" required />
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group w-100">
                <label class="form-label">Impact Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-impact_name"></i></span>
                    <input type="text" class="form-control" placeholder="Enter The Impact Name" asp-for="ImpactName" required />
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group w-100">
                <label class="form-label">Sequence</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-sequence"></i></span>
                    <input type="text" class="form-control" placeholder="Enter The Sequence" asp-for="Sequence" required/>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Update</button>
        </div>
    </div>
</form>
