﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model BCM.BusinessClasses.MenuRights
@{
    int CIndex = 1;
    int AIndex = 1;
    int BIndex = 1;
    int iSequence = 30;

}
@{
    <form asp-action="SavePageMaster" method="post">
        <div class="modal-header">
            <h6 class="Page-Title">Configure Page</h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="row">
            <div class="col-md-6 col-lg-6 col-xl-6">
                <div>
                    <input type="hidden" asp-for="PageURL" />
                </div>
                <div>
                    <input type="hidden" asp-for="PageDetails"/>
                </div>
                <div>
                    <input type="hidden" asp-for="PageID" />
                </div>
                <div class="form-group">
                    <label class="form-label">Area</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select class="form-select form-control" asp-for="AreaName" autocomplete="off" id="ddlArea" aria-label="Default select example" required>
                            <option selected disabled value="">-- Select Area --</option>
                            @foreach (var item in ViewBag.ListAreas)
                            {
                                <option value="@item">@item</option>
                                BIndex++;
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Action</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select class="form-select form-control" autocomplete="off" asp-for="ActionName" id="ddlAction" aria-label="Default select example" required>
                            <option selected disabled value="">-- Select Action --</option>
                            @foreach (var item in ViewBag.lstlstActins)
                            {
                                <option value="@item">@item</option>
                                AIndex++;
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Page Details</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <textarea class="form-control" asp-for="PageDetails" placeholder="NA" required></textarea>
                    </div>
                </div>

            </div>
            <div class="col-md-6 col-lg-6 col-xl-6">
                <div class="form-group">
                    <label class="form-label">Controllers</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select class="form-select form-control" autocomplete="off" asp-for="ControllerName" id="ddlController" aria-label="Default select example" required>
                            <option selected disabled value="">-- Select Controller --</option>
                            @foreach (var item2 in ViewBag.lstControllers)
                            {
                                <option value="@item2">@item2</option>
                                CIndex++;
                            }

                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Page Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <input class="form-control" type="text" id="txtPageName" asp-for="PageName" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Sequence</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select class="form-select form-control" asp-for="Sequence" autocomplete="off" id="ddlSequence" aria-label="Default select example" required>
                            <option selected disabled value="">-- Select Sequence --</option>
                            @for (int i = 1; i <= iSequence; i++)
                            {
                                <option value="@i">@i</option>
                            }
                        </select>
                    </div>
                </div>


            </div>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
            <div>
                <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary btn-sm me-1">Update</button>
            </div>
        </div>
    </form>
}
<Script>
    $(document).ready(function () {
        $('#ddlController').change(function () {
            var strControllerName = $(this).val();
            if (strControllerName) {
                $.ajax({
                    url: '@Url.Action("GetAllAction", "PageMaster")',
                    type: 'GET',
                    data: { strControllerName: strControllerName },
                    success: function (data) {
                         var ddlAction = $('#ddlAction');
                        // var PageName = strControllerName.replace('Controller', '')
                        $('#txtPageName').val(PageName)
                        ddlAction.empty();
                        ddlAction.append('<option value="0">-- All Action --</option>');
                        $.each(data, function (index, item) {
                            ddlAction.append('<option value="' + item + '">' + item + '</option>')
                        });
                    }
                })
            }
        })

    })
</Script>

