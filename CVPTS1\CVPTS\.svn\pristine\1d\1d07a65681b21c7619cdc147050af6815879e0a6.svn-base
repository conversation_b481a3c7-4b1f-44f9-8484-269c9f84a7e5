﻿@model IEnumerable<BCM.BusinessClasses.SubFunction>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    if (Model != null && Model.Any())
    {
        int index = 1;
        foreach (var DepartmentInfo in Model)
        {
            <tr>
                <td>@index</td>
                <td>@DepartmentInfo.SubFunctionName</td>
                <td>
                    <table>
                        <tbody>
                            <tr style="display:none;">
                                @*       <td class="fw-semibold"><i class="cv-organization"></i> </td>
                                        <td>:</td> *@
                                <td>@DepartmentInfo.OrganizationName</td>
                            </tr>
                            <tr>
                                @*       <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                        <td>:</td> *@
                                <td>@DepartmentInfo.UnitName</td>
                            </tr>

                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr>
                                @*    <td class="fw-semibold"><i class="cv-department"></i> </td>
                                        <td>:</td> *@
                                <td>@DepartmentInfo.FunctionName</td>
                            </tr>

                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr>
                                @*            <td class="fw-semibold"><i class="cv-user"></i></td>
                                        <td> : </td> *@
                                <td>@DepartmentInfo.OwnerName</td>
                            </tr>
                            <tr style="display:none;">
                                <td class="fw-semibold"><i class="cv-mail"></i></td>
                                <td>:</td>
                                <td>@DepartmentInfo.OwnerEmail</td>
                            </tr>
                            <tr style="display:none;">
                                <td class="fw-semibold"><i class="cv-phone"></i></td>
                                <td>:</td>
                                <td id="HeadMobilePhone">@DepartmentInfo.OwnerMobile</td>
                            </tr>

                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr>
                                @*         <td class="fw-semibold"><i class="cv-user"></i></td>
                                        <td> : </td> *@
                                <td>@DepartmentInfo.AltownerName</td>
                            </tr>
                            <tr style="display:none;">
                                <td class="fw-semibold"><i class="cv-mail"></i></td>
                                <td>:</td>
                                <td>@DepartmentInfo.AltownerEmail</td>
                            </tr>
                            <tr style="display:none;">
                                <td class="fw-semibold"><i class="cv-phone"></i></td>
                                <td>:</td>
                                <td>@DepartmentInfo.AltownerMobile</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <div class="d-flex align-items-center gap-2">
                        <div class="dropdown dropstart">
                            <span class="btn-action" role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false"><i class="cv-activity-details" title="View Details"></i></span>
                            <div class="dropdown-menu border-0">
                                @* <h6 class="dropdown-header fw-semibold text-dark pb-0">Ptech Pune LTD</h6> *@
                                <table class="table mb-0 table-borderless">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <table class="table table-sm mb-0 table-borderless">
                                                    <tbody>
                                                        <tr>
                                                            <th class="fw-semibold text-primary" colspan="3">Owner Detail</th>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-user"></i></td>
                                                            <td> : </td>
                                                            <td>@DepartmentInfo.OwnerName</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                            <td>:</td>
                                                            <td>@DepartmentInfo.OwnerEmail</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                            <td>:</td>
                                                            <td>@DepartmentInfo.OwnerMobile</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                            <td>
                                                <table class="table table-sm mb-0 table-borderless">
                                                    <tbody>
                                                        <tr>
                                                            <th class="fw-semibold text-primary" colspan="3">Alt Owner Detail</th>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-user"></i></td>
                                                            <td> : </td>
                                                            <td>@DepartmentInfo.AltownerName</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                            <td>:</td>
                                                            <td>@DepartmentInfo.AltownerEmail</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                            <td>:</td>
                                                            <td>@DepartmentInfo.AltownerMobile</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@DepartmentInfo.SubFunctionID"><i class="cv-edit" title="Edit"></i></span>
                        <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@DepartmentInfo.SubFunctionID"><i class="cv-delete text-danger" title="Delete"></i></span>
                    </div>
                </td>
            </tr>
            index++;
        }
    }
    else
    {
        <tr>
            <td colspan="10" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <img src="~/img/Isomatric/no_records_found.svg" alt="No Records Found" style="width: 120px; height: auto; margin-bottom: 1rem;">
                </div>
            </td>
        </tr>
    }
}