﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}



<style>
    .table > :not(caption) > * > * {
        padding: 7px 5px;
    }
</style>
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">
        Process Compliance
    </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</button>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>
<div class="Page-Condant  border-0 pe-2" style="height: calc(100vh - 108px);overflow: auto;">
    <div class="card-body">


        <div class="row g-3">
            <div class="col-3">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div>
                                    <ul class="ps-0 mb-0">
                                        <li class="list-group-item text-muted"><small>Score Name</small></li>
                                        <li class="list-group-item fw-bold text-primary">
                                            Process Initiated
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tr>
                                    <td class="text-muted">Weightage</td>
                                    <td>:</td>
                                    <td>0.10</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Description</td>
                                    <td>:</td>
                                    <td>
                                      test
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">
                                        Sequence
                                    </td>
                                    <td>:</td>
                                    <td>
                                       1
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
           
        </div>

    </div>


</div>


<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Process Compliance  Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Score Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-name"></i></span>
                                <input class="form-control" type="text" />
                            </div>
                        </div>
                        <div class="form-group w-100">
                            <label class="form-label">Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <textarea class="form-control" placeholder="Activity Details" style="height:0px"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Weightage</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-weightage"></i></span>
                                <input class="form-control" type="text" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Sequence</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-sequence"></i></span>
                                <input class="form-control" type="text" />
                            </div>
                        </div>
                    </div>
                </div>
              
            </div>  <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!-- Delete Modal -->
@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/delete.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div> *@


<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
                <div class="modal-header p-0">
                    <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
                </div>
                <div class="modal-body d-grid px">
                    <span class="fw-semibold">Do you really want to delete</span>
                    <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
                </div>
                <div class="modal-footer justify-content-center p-0">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm">Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- End Delete Modal -->