﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Security.Helper;
using BCM.Shared;
using BCM.UI.Controllers;
using DevExpress.Charts.Native;
using Humanizer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore.Metadata;
using MySqlX.XDevAPI;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using System.Text.Json;

namespace BCM.UI.Areas.BCMFunctionRecoveryPlan.Controllers;
[Area("BCMFunctionRecoveryPlan")]
//[Route("WorkflowConfiguration/[action]")]
public class WorkflowConfigurationController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    private readonly ILoggerFactory? _LoggerFactory;
    private int iPlanID = 0;

    public WorkflowConfigurationController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }


    #region Diagram Code Start

    public IActionResult ManageWorkflowConfiguration(string strPlanID)
    {
        strPlanID = strPlanID=="0" ? HttpContext.Session.GetString("Planid") : CryptographyHelper.Decrypt(strPlanID.ToString());
        ViewData["PlanId"]=strPlanID;
        HttpContext.Session.SetString("Planid", strPlanID);
        try
        {
            PopulateDropDown(strPlanID);
            //GetWorkflowActionById(240);
            return View();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            //return NotFound("No Records Found.");
            return BadRequest(ex.Message);
        }

    }

    [HttpGet]
    public string GetWorkflowConfigurationDiagram(string strPlanID)
    {
        try
        {
            // Get workflow actions for the plan
            var workflowActions = _ProcessSrv.GetRecoveryPlanByID(Convert.ToInt32(strPlanID));
            return workflowActions.XML;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return ex.Message;
        }
    }

    [HttpPost]
    public int SaveWorkflowConfigurationDiagram([FromBody] Workflow objPlanInfo)
    {
        int iPlanID = 0;
        try
        {
            objPlanInfo.ChangedBy = _UserDetails.UserID.ToString();
            iPlanID = _ProcessSrv.WorkflowToRecoveryPlan_XMLSave(objPlanInfo, Convert.ToInt32(objPlanInfo.Id));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return iPlanID;
    }

    #endregion  Diagram Code End


    #region Step Configuration Code Start

    [HttpGet]
    public IActionResult GetWorkflowActionById(int iID)
    {
        WorkflowActionInfo objWorkflowActionInfo = new WorkflowActionInfo();
        var Planid = HttpContext.Session.GetString("Planid");
        try
        {
            PopulateDropDown(Planid);
            objWorkflowActionInfo = _ProcessSrv.WorkflowAction_getbyID(iID);
            List<RecoveryStepsDependentMapping> objStepsDetails = _ProcessSrv.RecoveryStepsDependentMapping_ByStepID(iID.ToString());
            objWorkflowActionInfo.objDependedOnCheckListNew = objStepsDetails.Select(step => step.DependentStepID).ToArray();
            if (objWorkflowActionInfo != null)
            {
                return Json(new
                {
                    Name = objWorkflowActionInfo.Name,
                    Description = objWorkflowActionInfo.Description,
                    StepOwnerId = objWorkflowActionInfo.Stepownerid,
                    AlterStepOwnerID = objWorkflowActionInfo.Aleternatespetownerid,
                    GoToStep = objWorkflowActionInfo.GoToStep,
                    EstimationTime = objWorkflowActionInfo.Estimationtime,
                    TimeUnit = objWorkflowActionInfo.TimeUnit,
                    InterDependancy = objWorkflowActionInfo.InterDependancy,
                    objDependedOnCheckListNew = objWorkflowActionInfo.objDependedOnCheckListNew,
                    APIProfileId = objWorkflowActionInfo.APIProfileID,
                    CPProfileId = objWorkflowActionInfo.CPProfileID,
                });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpPost]
    public string SaveWorkflowAction([FromBody] WorkflowActionInfo objWorkflowActionInfo)
    {
        int iSuccess = 0;
        string strPlanId = HttpContext.Session.GetString("Planid");
        objWorkflowActionInfo.PlanID = strPlanId;
        objWorkflowActionInfo.ChangedBy = _UserDetails.UserID.ToString();
        objWorkflowActionInfo.IsUsedinWorkflow = "1";
        objWorkflowActionInfo.Estimationtime = objWorkflowActionInfo.Estimationtime=="" ? "0" : objWorkflowActionInfo.Estimationtime;
        //objWorkflowActionInfo.CPProfileID = "0";
        objWorkflowActionInfo.TimeUnit = objWorkflowActionInfo.TimeUnit=="1" ? "1" : objWorkflowActionInfo.TimeUnit;

        if (objWorkflowActionInfo.InterDependancy!= null ||objWorkflowActionInfo.InterDependancy!= "")
        {
            objWorkflowActionInfo.InterDependancy = objWorkflowActionInfo.InterDependancy == "true" ? "1" : "0";
        }
        else
        {
            objWorkflowActionInfo.InterDependancy = "0";
        }

        if (objWorkflowActionInfo.ID == null || objWorkflowActionInfo.ID.ToString() == "0" ||objWorkflowActionInfo.ID.ToString() == "")
        {
            iSuccess = _ProcessSrv.WorkflowAction_Save(objWorkflowActionInfo);
        }

        else
        {
            iSuccess = _ProcessSrv.WorkflowAction_Update(objWorkflowActionInfo);
        }

        if (iSuccess > 0 && Convert.ToInt32(objWorkflowActionInfo.InterDependancy) > 0)
        {
            if (objWorkflowActionInfo.objDependedOnCheckListNew != null && objWorkflowActionInfo.objDependedOnCheckListNew.Count() > 0)
            {
                int iRes = _ProcessSrv.RecoveryStepsDependentMapping_Save(Convert.ToInt32(iSuccess), objWorkflowActionInfo.objDependedOnCheckListNew);
            }
        }

        else if (Convert.ToInt32(objWorkflowActionInfo.InterDependancy) == 0)
        {
            bool iRes = _ProcessSrv.RecoveryStepsDependentMapping_DeleteByStepID(iSuccess.ToString());
        }

        return iSuccess.ToString();
    }

    public void PopulateDropDown(string strPlanID = "0")
    {
        try
        {
            ViewBag.ResourceList = _Utilities.GetAllResourceList();
            ViewBag.ddlLoadProperty = _ProcessSrv.WorkflowAction_LoadActions(strPlanID);
            ViewBag.ApiList = _ProcessSrv.GetAPIDetails_OrgUnitLevel(_UserDetails.OrgID);

            // Get all workflow actions
            var allSteps = _ProcessSrv.WorkflowAction_LoadActions(strPlanID);

            // Get diagram data to filter steps
            var diagramStepIds = GetDiagramStepIds(strPlanID);

            // Filter steps to only include those present in the diagram
            var filteredSteps = allSteps.Where(step => diagramStepIds.Contains(step.ID)).ToList();

            ViewBag.DependentOnList = filteredSteps;

            //ViewBag.ddlWorkflow = _ProcessSrv.GetAllCPWorkflows();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private List<string> GetDiagramStepIds(string strPlanID)
    {
        var stepIds = new List<string>();
        try
        {
            // Get the diagram XML/JSON data
            var workflowActions = _ProcessSrv.GetRecoveryPlanByID(Convert.ToInt32(strPlanID));
            if (!string.IsNullOrEmpty(workflowActions.XML))
            {
                // Parse the JSON to extract step IDs
                var diagramData = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(workflowActions.XML);
                if (diagramData != null)
                {
                    foreach (var item in diagramData)
                    {
                        if (item.stepId != null && !string.IsNullOrEmpty(item.stepId.ToString()))
                        {
                            stepIds.Add(item.stepId.ToString());
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return stepIds;
    }

    [HttpGet]
    public JsonResult GetAllStepsForInterdependencyBinding()
    {
        try
        {
            string Planid = HttpContext.Session.GetString("Planid");
            var objStepList = _ProcessSrv.WorkflowAction_LoadActions(Planid);
            return Json(objStepList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    #endregion  Step Configuration Code Start


    #region CP Profile API Integration

    [HttpGet]
    public async Task<JsonResult> CheckWorkflowServiceStatus()
    {
        try
        {
            // Configure HttpClientHandler with SSL bypass
            using var handler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (_, _, _, _) => true
            };

            using var client = new HttpClient(handler);

            // Configure headers and timeout using configuration class
            client.DefaultRequestHeaders.Add("x-api-key", SusanAIWorkflowConfig.ApiKey);
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(
                new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
            client.Timeout = TimeSpan.FromSeconds(SusanAIWorkflowConfig.TimeoutSeconds);

            // Make API request using configuration
            using var response = await client.GetAsync(SusanAIWorkflowConfig.FullUrl);

            if (!response.IsSuccessStatusCode)
            {
                return CreateErrorResponse($"API call failed with status: {response.StatusCode} - {response.ReasonPhrase}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();

            // Handle empty response
            if (string.IsNullOrWhiteSpace(responseContent))
            {
                return CreateErrorResponse("API returned empty response");
            }

            var apiResponse = JsonSerializer.Deserialize<WorkflowServiceStatusResponse>(responseContent,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            // Validate response and active nodes
            if (apiResponse?.Success != true || apiResponse.ActiveNodes?.Any() != true)
            {
                return CreateErrorResponse("Workflow service is not running or no active nodes found");
            }

            // Convert active nodes to CP Profile options
            var cpProfiles = apiResponse.ActiveNodes
                .Where(node => !string.IsNullOrWhiteSpace(node))
                .Select((node, index) => new CPProfileOption
                {
                    Value = (index + 1).ToString(),
                    Text = node.Trim()
                })
                .ToList();

            if (!cpProfiles.Any())
            {
                return CreateErrorResponse("No valid CP Profiles found in active nodes");
            }

            return Json(new
            {
                success = true,
                data = cpProfiles,
                message = $"Workflow service is running. {cpProfiles.Count} CP Profile(s) loaded successfully.",
                timestamp = DateTime.UtcNow
            });
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _CVLogger.LogErrorApp(ex);
            return CreateErrorResponse($"Request timeout after {SusanAIWorkflowConfig.TimeoutSeconds} seconds");
        }
        catch (HttpRequestException ex)
        {
            _CVLogger.LogErrorApp(ex);
            return CreateErrorResponse($"Network error: {ex.Message}");
        }
        catch (JsonException ex)
        {
            _CVLogger.LogErrorApp(ex);
            return CreateErrorResponse("Invalid JSON response from workflow service");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return CreateErrorResponse($"Unexpected error: {ex.Message}");
        }
    }

    /// <summary>
    /// Creates a standardized error response for the workflow service API
    /// </summary>
    private JsonResult CreateErrorResponse(string message)
    {
        return Json(new
        {
            success = false,
            data = new List<CPProfileOption>(),
            message,
            timestamp = DateTime.UtcNow
        });
    }

    [HttpGet]
    public async Task<JsonResult> GetCPProfiles()
    {
        try
        {
            // First check workflow service status
            var result = await CheckWorkflowServiceStatus();

            // If successful, get all workflow profile names
            if (result.Value != null)
            {
                var resultData = result.Value.GetType().GetProperty("success")?.GetValue(result.Value);
                if (resultData is bool success && success)
                {
                    // Get workflow profile names and merge with status result
                    var profileNamesResult = await GetWorkflowProfileNames();
                    return profileNamesResult;
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return CreateErrorResponse($"Error getting CP Profiles: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets all workflow profile names from SusanAI service
    /// </summary>
    [HttpGet]
    public async Task<JsonResult> GetWorkflowProfileNames()
    {
        try
        {
            // Configure HttpClientHandler with SSL bypass
            using var handler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (_, _, _, _) => true
            };

            using var client = new HttpClient(handler);

            // Configure headers and timeout using configuration class
            client.DefaultRequestHeaders.Add("x-api-key", SusanAIWorkflowConfig.ApiKey);
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(
                new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
            client.Timeout = TimeSpan.FromSeconds(SusanAIWorkflowConfig.TimeoutSeconds);

            // Build URL for workflow profile names endpoint
            var profileNamesUrl = $"https://{SusanAIWorkflowConfig.ServerHost}:{SusanAIWorkflowConfig.Port}/api/SusanAI/workflow-profile-names";

            // Make API request
            using var response = await client.GetAsync(profileNamesUrl);

            if (!response.IsSuccessStatusCode)
            {
                return CreateErrorResponse($"Workflow profile names API call failed with status: {response.StatusCode} - {response.ReasonPhrase}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();

            // Handle empty response
            if (string.IsNullOrWhiteSpace(responseContent))
            {
                return CreateErrorResponse("Workflow profile names API returned empty response");
            }

            var profilesResponse = JsonSerializer.Deserialize<List<WorkflowProfile>>(responseContent,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            // Validate response and profiles
            if (profilesResponse?.Any() != true)
            {
                return CreateErrorResponse("No workflow profiles found");
            }

            // Convert workflow profiles to CP Profile options
            var cpProfiles = profilesResponse
                .Where(profile => !string.IsNullOrWhiteSpace(profile.profileName))
                .Select((profile, index) => new CPProfileOption
                {
                    Value =  ConvertGuidToInteger(profile.Id , index + 1).ToString(),
                    Text = profile.profileName.Trim()
                })
                .ToList();

            if (!cpProfiles.Any())
            {
                return CreateErrorResponse("No valid workflow profiles found");
            }

            return Json(new
            {
                success = true,
                data = cpProfiles,
                message = $"Successfully loaded {cpProfiles.Count} workflow profile(s) from SusanAI service.",
                timestamp = DateTime.UtcNow,
                source = "workflow-profile-names"
            });
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _CVLogger.LogErrorApp(ex);
            return CreateErrorResponse($"Workflow profile names request timeout after {SusanAIWorkflowConfig.TimeoutSeconds} seconds");
        }
        catch (HttpRequestException ex)
        {
            _CVLogger.LogErrorApp(ex);
            return CreateErrorResponse($"Network error while getting workflow profile names: {ex.Message}");
        }
        catch (JsonException ex)
        {
            _CVLogger.LogErrorApp(ex);
            return CreateErrorResponse("Invalid JSON response from workflow profile names service");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return CreateErrorResponse($"Unexpected error getting workflow profile names: {ex.Message}");
        }
    }

    /// <summary>
    /// Test endpoint for SusanAI workflow service API integration
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> TestWorkflowServiceAPI()
    {
        try
        {
            var result = await CheckWorkflowServiceStatus();
            ViewBag.TestResult = result.Value;
            return View();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            ViewBag.TestResult = new { success = false, message = ex.Message };
            return View();
        }
    }

    /// <summary>
    /// Test endpoint specifically for workflow profile names API
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> TestWorkflowProfileNames()
    {
        try
        {
            var result = await GetWorkflowProfileNames();
            ViewBag.TestResult = result.Value;
            ViewBag.EndpointName = "Workflow Profile Names";
            ViewBag.EndpointUrl = $"https://{SusanAIWorkflowConfig.ServerHost}:{SusanAIWorkflowConfig.Port}/api/SusanAI/workflow-profile-names";
            return View("TestWorkflowServiceAPI"); // Reuse the same view
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            ViewBag.TestResult = new { success = false, message = ex.Message };
            ViewBag.EndpointName = "Workflow Profile Names";
            ViewBag.EndpointUrl = $"https://{SusanAIWorkflowConfig.ServerHost}:{SusanAIWorkflowConfig.Port}/api/SusanAI/workflow-profile-names";
            return View("TestWorkflowServiceAPI");
        }
    }

    #endregion CP Profile API Integration




    #region GUID to Integer Mapping Methods


    /// <summary>
    /// Converts GUID to a consistent integer value for database storage
    /// Uses a hash-based approach to ensure the same GUID always maps to the same integer
    /// </summary>
    /// <param name="guidString">The GUID string from SusanAI API</param>
    /// <param name="fallbackIndex">Fallback index if GUID is null or invalid</param>
    /// <returns>Integer representation of the GUID</returns>
    private int ConvertGuidToInteger(string? guidString, int fallbackIndex)
    {
        if (string.IsNullOrWhiteSpace(guidString))
        {
            return fallbackIndex;
        }

        try
        {
            // Parse the GUID to ensure it's valid
            if (Guid.TryParse(guidString, out Guid guid))
            {
                // Use a consistent hash-based approach to convert GUID to integer
                // This ensures the same GUID always produces the same integer
                var hashCode = guid.GetHashCode();

                // Ensure positive integer and avoid conflicts with system IDs (0-100)
                var result = Math.Abs(hashCode % 900000) + 100001;

                // Store the mapping for reverse lookup
                StoreGuidMapping(result, guidString);

                return result;
            }
            else
            {
                // If GUID parsing fails, use fallback
                return fallbackIndex;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return fallbackIndex;
        }
    }

    /// <summary>
    /// Stores the mapping between integer IDs and GUID values for reverse lookup
    /// This is useful when we need to convert back from integer to GUID
    /// </summary>
    private static readonly Dictionary<int, string> _guidMappingCache = new Dictionary<int, string>();

    /// <summary>
    /// Stores the GUID mapping for reverse lookup
    /// </summary>
    /// <param name="integerId">The integer ID</param>
    /// <param name="guidString">The original GUID string</param>
    private void StoreGuidMapping(int integerId, string guidString)
    {
        if (!string.IsNullOrWhiteSpace(guidString))
        {
            _guidMappingCache[integerId] = guidString;
        }
    }

    /// <summary>
    /// Retrieves the original GUID from the integer ID
    /// </summary>
    /// <param name="integerId">The integer ID</param>
    /// <returns>The original GUID string or null if not found</returns>
    private string? GetGuidFromInteger(int integerId)
    {
        return _guidMappingCache.TryGetValue(integerId, out string? guid) ? guid : null;
    }

    /// <summary>
    /// API endpoint to get workflow profile GUID by integer ID
    /// This can be used by frontend or other services that need to convert back to GUID
    /// </summary>
    /// <param name="integerId">The integer ID</param>
    /// <returns>JSON response with GUID information</returns>
    [HttpGet]
    public JsonResult GetWorkflowProfileGuid(int integerId)
    {
        try
        {
            var guid = GetGuidFromInteger(integerId);
            if (!string.IsNullOrWhiteSpace(guid))
            {
                return Json(new
                {
                    success = true,
                    integerId = integerId,
                    guid = guid,
                    message = "GUID found successfully"
                });
            }
            else
            {
                return Json(new
                {
                    success = false,
                    integerId = integerId,
                    guid = (string?)null,
                    message = "No GUID mapping found for the provided integer ID"
                });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new
            {
                success = false,
                integerId = integerId,
                guid = (string?)null,
                message = $"Error retrieving GUID: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Test endpoint to demonstrate GUID to integer conversion
    /// Shows how the conversion works with sample data
    /// </summary>
    //[HttpGet]
    //public JsonResult TestGuidToIntegerConversion()
    //{
    //    try
    //    {
    //        // Sample GUIDs from the API response you showed earlier
    //        var sampleGuids = new[]
    //        {
    //            "8c4fbd28-8118-4a62-99b1-1fecdd2d970a", // WPM_MSSQL_DB_MIRROR_SB
    //            "e59ebf2c-eb6d-4a60-ab01-a0eb3feb801b", // WPM_MSSQL_DB_MIRROR_SO
    //            "1ca79a2d-7431-46a9-99eb-77512724f486", // WPM_MSSQL_AO_SB
    //            "906ad26a-4d0a-4699-b1e1-4a441ceb6b99", // WPM_MSSQL_AO_SO
    //            "3a8eb485-84e0-4f2f-883d-9637d6002461"  // WPM_Test
    //        };

    //        var profileNames = new[]
    //        {
    //            "WPM_MSSQL_DB_MIRROR_SB",
    //            "WPM_MSSQL_DB_MIRROR_SO",
    //            "WPM_MSSQL_AO_SB",
    //            "WPM_MSSQL_AO_SO",
    //            "WPM_Test"
    //        };

    //        var conversions = new List<object>();

    //        for (int i = 0; i < sampleGuids.Length; i++)
    //        {
    //            var integerId = ConvertGuidToInteger(sampleGuids[i], i + 1);
    //            conversions.Add(new
    //            {
    //                originalGuid = sampleGuids[i],
    //                profileName = profileNames[i],
    //                convertedInteger = integerId,
    //                canRetrieveGuid = GetGuidFromInteger(integerId) == sampleGuids[i]
    //            });
    //        }

    //        return Json(new
    //        {
    //            success = true,
    //            message = "GUID to integer conversion test completed",
    //            conversions = conversions,
    //            mappingCacheSize = _guidMappingCache.Count,
    //            timestamp = DateTime.UtcNow
    //        });
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //        return Json(new
    //        {
    //            success = false,
    //            message = $"Error in GUID conversion test: {ex.Message}",
    //            timestamp = DateTime.UtcNow
    //        });
    //    }
    //}
    #endregion
}


