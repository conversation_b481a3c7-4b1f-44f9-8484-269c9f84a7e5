﻿@model BCM.BusinessClasses.OrgUnit
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}
<!-- Configuration Modal -->
<form id="addBusinessUnitForm" asp-action="AddBusinessUnit" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="modal-body pt-0">
    @* Unit Form *@
    <div class="row row-cols-2">
        <div class="col-12">
            <blockquote class="blockquote mt-0">Unit Head</blockquote>
        </div>
        <div class="col">
            <div class="form-group">
                <label for="validationCustom01" class="form-label">Unit Code</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-login-code"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Unit Code" id="UnitCode" asp-for="UnitCode" required>
                </div>
                <div class="invalid-feedback">Enter Unit Code</div>
            </div>
            <div class="form-group">
                <label class="form-label">Organization Name</label>
                <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select class="form-select form-control selectized" asp-for="OrgID" id="OrgID" autocomplete="off" aria-label="Default select example" required>
                        <option selected value="0">-- All Organizations --</option>
                        @foreach (var organization in ViewBag.OrgInfo)
                        {
                            <option value="@organization.Value">@organization.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Enter Organization Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-mail"></i></span>
                    <input type="text" id="CompanyEmail" class="form-control" placeholder="Enter Email" asp-for="CompanyEmail" readonly>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Unit Name</label>
                <div class="input-group">
                        <span class="input-group-text"><i class="cv-unit"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Unit Name" id="UnitName" asp-for="UnitName" required>
                </div>
                <div class="invalid-feedback">Enter Unit Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Unit Head</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" id="ddlUnitHead" asp-for="UnitHeadID" autocomplete="off" aria-label="Default select example" required>
                        <option selected value="0">-- All Resources --</option>
                        @foreach (var resource in ViewBag.ResourceInfo)
                        {
                            <option value="@resource.Value">@resource.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Enter Unit Head</div>
            </div>
            <div class="form-group">
                <label class="form-label">Mobile</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-phone"></i></span>
                    <input type="text" id="MobilePhone" class="form-control" placeholder="Enter Mobile" asp-for="MobilePhone" readonly>
                </div>
            </div>
        </div>
    </div>
    @* Alternate Unit Head Form *@
    <div class="row row-cols-2">
        <div class="col-12">
            <blockquote class="blockquote">Alternate Unit Head</blockquote>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Alternate Unit Head</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" id="ddlAltUnitHead" asp-for="AltUnitHeadID" autocomplete="off" aria-label="Default select example" required>
                        <option selected value="0">-- All Resources --</option>
                        @foreach (var resource in ViewBag.ResourceInfo)
                        {
                            <option value="@resource.Value">@resource.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Alternate Unit Head</div>
            </div>

            <div class="form-group">
                <label class="form-label">Mobile</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-phone"></i></span>
                    <input type="text" id="AltUHeadMobilePhone" class="form-control" placeholder="Enter Mobile" asp-for="AltUHeadMobilePhone" readonly>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-mail"></i></span>
                    <input type="text" id="AltUHeadCompanyEmail" class="form-control" placeholder="Enter Email" asp-for="AltUHeadCompanyEmail" readonly>
                </div>
            </div>
        </div>
    </div>
    @* BCP Coordinator Form *@
    <div class="row row-cols-2">
        <div class="col-12">
            <blockquote class="blockquote">BCP Coordinator</blockquote>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">BCP Coordinator</label>
                <div class="input-group">
                        <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" id="ddlUnitBCPCor" asp-for="UnitBCPCorID" autocomplete="off" aria-label="Default select example" required>
                        <option selected value="0">-- All Resources --</option>
                        @foreach (var resource in ViewBag.ResourceInfo)
                        {
                            <option value="@resource.Value">@resource.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select BCP Coordinator</div>
            </div>
            <div class="form-group">
                <label class="form-label">Mobile</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-phone"></i></span>
                    <input type="text" id="BcpCoordinatorMobile" class="form-control" placeholder="Enter Mobile" asp-for="BCPCoordinatorMobile" readonly>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-mail"></i></span>
                    <input type="text" id="BcpCoordinatorMail" class="form-control" placeholder="Enter Email" asp-for="BCPCoordinatorMail" readonly>
                </div>
            </div>
        </div>
    </div>
    @* Alternate BCP Coordinator Form *@
    <div class="row row-cols-2">
        <div class="col-12">
            <blockquote class="blockquote">Alternate BCP Coordinator</blockquote>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Alternate BCP Coordinator</label>
                <div class="input-group">
                        <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" id="ddlAltBCPCor" asp-for="AltBCPCorID" autocomplete="off" aria-label="Default select example" required>
                        <option selected value="0">-- All Resources --</option>
                        @foreach (var resource in ViewBag.ResourceInfo)
                        {
                            <option value="@resource.Value">@resource.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Alt BCP Coordinator</div>
            </div>
           <div class="form-group">
                <label class="form-label">Mobile</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-phone"></i></span>
                    <input type="text" id="AltUBCPCoordinatorMobile" class="form-control" placeholder="Enter Mobile" asp-for="AltUBCPCoordinatorMobile" readonly>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-mail"></i></span>
                    <input type="text" id="AltUBCPCoordinatorMail" class="form-control" placeholder="Enter Email" asp-for="AltUBCPCoordinatorMail" readonly>
                </div>
            </div>
        </div>
    </div>

    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>
<!--End Configuration Modal -->

<script>
    $(document).ready(function () {
        console.log("AddBusinessUnit form ready");

        // Handle dropdown change events to populate related fields
        $('#ddlUnitHead,#ddlAltUnitHead,#ddlUnitBCPCor,#ddlAltBCPCor').change(function () {
            var selectedDDL = $(this).attr('id');
            var resourceID = $(this).val();
            $.ajax({
                // url: '/BCMOrgStructure/BusinessUnit/GetResourceMaster_ByUserID' + '/' + resourceID,
                url: '@Url.Action("GetResourceMaster_ByUserID", "BusinessUnit", new { area = "BCMOrgStructure" })',
                type: "GET",
                data: { resourceID: resourceID },
                success: function (data) {
                    if (data) {
                        if (selectedDDL == "ddlUnitHead") {
                            $("#CompanyEmail").val(data.mail);
                            $("#MobilePhone").val(data.mobile);
                        } else if (selectedDDL == "ddlAltUnitHead") {
                            $("#AltUHeadCompanyEmail").val(data.mail);
                            $("#AltUHeadMobilePhone").val(data.mobile);
                        } else if (selectedDDL == "ddlUnitBCPCor") {
                            $("#BcpCoordinatorMail").val(data.mail);
                            $("#BcpCoordinatorMobile").val(data.mobile);
                        } else if (selectedDDL == "ddlAltBCPCor") {
                            $("#AltUBCPCoordinatorMail").val(data.mail);
                            $("#AltUBCPCoordinatorMobile").val(data.mobile);
                        }
                    }
                    else {
                        console.log("Error while binding data.");
                    }
                },
                error: function (data) {
                    console.log('error is invoked');
                }
            });
        });

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for AddBusinessUnit form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Setup unit name validation
                setupUnitNameValidation();

                // Get the form element
                const form = document.getElementById('addBusinessUnitForm');
                if (!form) {
                    console.error("Form not found with ID: addBusinessUnitForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                const feedbackElements = {};

                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        const message = element.textContent.trim();

                        // Primary key: Use input ID (now that all required fields have IDs)
                        if (input.id) {
                            customMessages[input.id] = message;
                            feedbackElements[input.id] = element;
                            console.log("Stored custom message for ID", input.id, ":", message);
                        }

                        // Fallback: Use asp-for attribute
                        const aspFor = input.getAttribute('asp-for');
                        if (aspFor) {
                            customMessages[aspFor] = message;
                            feedbackElements[aspFor] = element;
                            console.log("Stored custom message for asp-for", aspFor, ":", message);
                        }

                        // Store original message on element for direct access
                        element.originalMessage = message;
                    }
                });

                // Helper function to restore custom message for an input
                function restoreCustomMessage(input) {
                    let message = null;
                    let feedbackElement = null;

                    // Priority 1: Use input ID (most reliable now)
                    if (input.id && customMessages[input.id]) {
                        message = customMessages[input.id];
                        feedbackElement = feedbackElements[input.id];
                        console.log("Found custom message by ID", input.id, ":", message);
                    }
                    // Priority 2: Use asp-for attribute
                    else if (input.getAttribute('asp-for') && customMessages[input.getAttribute('asp-for')]) {
                        const aspFor = input.getAttribute('asp-for');
                        message = customMessages[aspFor];
                        feedbackElement = feedbackElements[aspFor];
                        console.log("Found custom message by asp-for", aspFor, ":", message);
                    }
                    // Priority 3: Find feedback element in the same form group
                    else {
                        const formGroup = input.closest('.form-group');
                        feedbackElement = formGroup?.querySelector('.invalid-feedback');
                        if (feedbackElement && feedbackElement.originalMessage) {
                            message = feedbackElement.originalMessage;
                            console.log("Found custom message by form group for", input.id || input.getAttribute('asp-for'), ":", message);
                        }
                    }

                    if (message && feedbackElement) {
                        feedbackElement.textContent = message;
                        // Only show if the input is actually invalid
                        if (input.classList.contains('is-invalid')) {
                            feedbackElement.style.display = 'block';
                        }
                        console.log("✅ Restored custom message:", message, "for input:", input.id || input.getAttribute('asp-for'));
                        return true;
                    } else {
                        console.warn("❌ Could not restore custom message for input:", input.id || input.getAttribute('asp-for'));
                        return false;
                    }
                }

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Skip email and mobile fields from validation
                    const skipFields = [
                        "CompanyEmail", "MobilePhone",
                        "BcpCoordinatorMail", "BcpCoordinatorMobile",
                        "AltUHeadCompanyEmail", "AltUHeadMobilePhone",
                        "AltUBCPCoordinatorMail", "AltUBCPCoordinatorMobile"
                    ];

                    if (skipFields.includes(input.id)) {
                        console.log("Skipping validation for", input.id);
                        return true; // Always return true for skipped fields
                    }

                    // For select fields, only validate if forced or if they have value "0"
                    if (input.tagName === 'SELECT' && !forceValidation) {
                        const value = input.value;
                        if (value && value !== "0") {
                            // Has valid selection, clear any validation errors
                            input.classList.remove('is-invalid');
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement && !feedbackElement.textContent.includes('cannot be the same person')) {
                                feedbackElement.style.display = 'none';
                            }
                            return true;
                        }
                    }

                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        restoreCustomMessage(input);
                    }

                    return result;
                };

                // Override the validateEmail function similarly
                const originalValidateEmail = window.BCMValidation.validateEmail;
                window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                    // Skip email fields from validation
                    const skipFields = [
                        "CompanyEmail", "BcpCoordinatorMail",
                        "AltUHeadCompanyEmail", "AltUBCPCoordinatorMail"
                    ];

                    if (skipFields.includes(input.id)) {
                        console.log("Skipping email validation for", input.id);
                        return true; // Always return true for skipped fields
                    }

                    // Get the result from the original function
                    const result = originalValidateEmail(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        restoreCustomMessage(input);
                    }

                    return result;
                };

                // Override the validatePatternInput function similarly
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                    // Skip mobile fields from validation
                    const skipFields = [
                        "MobilePhone", "BcpCoordinatorMobile",
                        "AltUHeadMobilePhone", "AltUBCPCoordinatorMobile"
                    ];

                    if (skipFields.includes(input.id)) {
                        console.log("Skipping pattern validation for", input.id);
                        return true; // Always return true for skipped fields
                    }

                    // Get the result from the original function
                    const result = originalValidatePatternInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        restoreCustomMessage(input);
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        restoreCustomMessage(input);
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Always prevent default submission initially
                    event.preventDefault();
                    event.stopPropagation();

                    // Get form data
                    var unitName = document.getElementById('UnitName').value.trim();
                    var orgId = document.getElementById('OrgID').value;
                    var unitId = 0; // For add mode, unitId is always 0

                    console.log("Unit Name:", unitName);
                    console.log("Organization ID:", orgId);

                    // Step 1: Check unit name if provided and organization is selected
                    if (unitName && unitName.length >= 2 && orgId && orgId !== "0") {
                        console.log("=== STEP 1: CHECKING UNIT NAME ===");

                        checkUnitName(unitName, orgId, unitId, function(exists) {
                            if (exists) {
                                console.log("=== UNIT NAME EXISTS - BLOCKING ===");
                                alert("This Unit Name already exists in the selected organization. Please choose another name.");
                                document.getElementById('UnitName').focus();
                            } else {
                                console.log("=== UNIT NAME UNIQUE - CONTINUING ===");
                                validateAndSubmit(form);
                            }
                        });
                    } else {
                        console.log("=== NO UNIT NAME CHECK NEEDED ===");
                        validateAndSubmit(form);
                    }
                });

                // Function to validate and submit form
                function validateAndSubmit(form) {
                    console.log("=== STEP 2: FORM VALIDATION ===");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);

                    // Validate that roles are not assigned to the same person
                    const rolesValid = validateRolesDifferent();

                    console.log("Form validation result:", isValid);
                    console.log("Roles validation result:", rolesValid);

                    if (!isValid || !rolesValid) {
                        console.log("=== FORM VALIDATION FAILED ===");
                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                        return;
                    }

                    // All validation passed - submit form
                    console.log("=== STEP 3: SUBMITTING FORM ===");

                    // Create a temporary form to submit without event listeners
                    var tempForm = document.createElement('form');
                    tempForm.method = form.method;
                    tempForm.action = form.action;

                    // Copy all form data
                    var formData = new FormData(form);
                    for (var pair of formData.entries()) {
                        var input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = pair[0];
                        input.value = pair[1];
                        tempForm.appendChild(input);
                    }

                    document.body.appendChild(tempForm);
                    tempForm.submit();
                }

            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        // Function to check unit name via AJAX
        function checkUnitName(unitName, orgId, unitId, callback) {
            var checkUrl = '@Url.Action("CheckUnitNameExists", "BusinessUnit", new { area = "BCMOrgStructure" })';
            console.log("Checking unit name at:", checkUrl);

            $.ajax({
                url: checkUrl,
                type: 'GET',
                data: {
                    unitName: unitName,
                    orgId: orgId,
                    unitId: unitId
                },
                timeout: 10000,
                success: function(response) {
                    console.log("=== UNIT NAME CHECK SUCCESS ===");
                    console.log("Response:", response);
                    callback(response && response.exists === true);
                },
                error: function(xhr, status, error) {
                    console.log("=== UNIT NAME CHECK ERROR ===");
                    console.error("Status:", status);
                    console.error("Error:", error);
                    console.error("Response Text:", xhr.responseText);
                    alert("Error checking unit name. Please try again.");
                    callback(false); // Assume unique on error
                }
            });
        }

        // Function to setup real-time unit name validation
        function setupUnitNameValidation() {
            var unitNameTimeout;

            $('#UnitName').on('input', function() {
                var unitName = $(this).val().trim();
                var orgId = $('#OrgID').val();
                var unitId = 0; // For add mode, unitId is always 0

                clearTimeout(unitNameTimeout);

                if (unitName.length >= 2 && orgId && orgId !== "0") {
                    unitNameTimeout = setTimeout(function() {
                        console.log("Real-time validation for unit:", unitName, "in org:", orgId);

                        checkUnitName(unitName, orgId, unitId, function(exists) {
                            var input = $('#UnitName');
                            var feedback = input.closest('.form-group').find('.invalid-feedback');

                            if (exists) {
                                // Show error
                                input.addClass('is-invalid');
                                input.closest('.input-group').addClass('is-invalid');
                                feedback.text('This Unit Name already exists in the selected organization. Please choose another.').show();
                            } else {
                                // Clear error if no other validation issues
                                if (input[0].checkValidity()) {
                                    input.removeClass('is-invalid');
                                    input.closest('.input-group').removeClass('is-invalid');
                                    feedback.hide();
                                }
                            }
                        });
                    }, 500);
                }
            });

            // Also validate when organization changes
            $('#OrgID').on('change', function() {
                var unitName = $('#UnitName').val().trim();
                var orgId = $(this).val();

                if (unitName.length >= 2 && orgId && orgId !== "0") {
                    setTimeout(function() {
                        console.log("Organization changed, re-validating unit name");
                        checkUnitName(unitName, orgId, 0, function(exists) {
                            var input = $('#UnitName');
                            var feedback = input.closest('.form-group').find('.invalid-feedback');

                            if (exists) {
                                input.addClass('is-invalid');
                                input.closest('.input-group').addClass('is-invalid');
                                feedback.text('This Unit Name already exists in the selected organization. Please choose another.').show();
                            } else {
                                if (input[0].checkValidity()) {
                                    input.removeClass('is-invalid');
                                    input.closest('.input-group').removeClass('is-invalid');
                                    feedback.hide();
                                }
                            }
                        });
                    }, 100);
                }
            });
        }

        // Function to validate that roles are assigned to different people
        function validateRolesDifferent() {
            var unitHeadID = $('#ddlUnitHead').val();
            var altUnitHeadID = $('#ddlAltUnitHead').val();
            var bcpCoordinatorID = $('#ddlUnitBCPCor').val();
            var altBcpCoordinatorID = $('#ddlAltBCPCor').val();

            var isValid = true;

            // Clear previous error states for role validation
            clearRoleValidationErrors();

            // Validate Unit Head vs Alt Unit Head
            if (unitHeadID && altUnitHeadID && unitHeadID !== "0" && altUnitHeadID !== "0" && unitHeadID === altUnitHeadID) {
                showRoleValidationError('#ddlUnitHead', 'Unit Head and Alternate Unit Head cannot be the same person');
                showRoleValidationError('#ddlAltUnitHead', 'Unit Head and Alternate Unit Head cannot be the same person');
                isValid = false;
            }

            // Validate Unit Head vs BCP Coordinator
            if (unitHeadID && bcpCoordinatorID && unitHeadID !== "0" && bcpCoordinatorID !== "0" && unitHeadID === bcpCoordinatorID) {
                showRoleValidationError('#ddlUnitHead', 'Unit Head and BCP Coordinator cannot be the same person');
                showRoleValidationError('#ddlUnitBCPCor', 'Unit Head and BCP Coordinator cannot be the same person');
                isValid = false;
            }

            // Validate Unit Head vs Alt BCP Coordinator
            if (unitHeadID && altBcpCoordinatorID && unitHeadID !== "0" && altBcpCoordinatorID !== "0" && unitHeadID === altBcpCoordinatorID) {
                showRoleValidationError('#ddlUnitHead', 'Unit Head and Alternate BCP Coordinator cannot be the same person');
                showRoleValidationError('#ddlAltBCPCor', 'Unit Head and Alternate BCP Coordinator cannot be the same person');
                isValid = false;
            }

            // Validate Alt Unit Head vs BCP Coordinator
            if (altUnitHeadID && bcpCoordinatorID && altUnitHeadID !== "0" && bcpCoordinatorID !== "0" && altUnitHeadID === bcpCoordinatorID) {
                showRoleValidationError('#ddlAltUnitHead', 'Alternate Unit Head and BCP Coordinator cannot be the same person');
                showRoleValidationError('#ddlUnitBCPCor', 'Alternate Unit Head and BCP Coordinator cannot be the same person');
                isValid = false;
            }

            // Validate Alt Unit Head vs Alt BCP Coordinator
            if (altUnitHeadID && altBcpCoordinatorID && altUnitHeadID !== "0" && altBcpCoordinatorID !== "0" && altUnitHeadID === altBcpCoordinatorID) {
                showRoleValidationError('#ddlAltUnitHead', 'Alternate Unit Head and Alternate BCP Coordinator cannot be the same person');
                showRoleValidationError('#ddlAltBCPCor', 'Alternate Unit Head and Alternate BCP Coordinator cannot be the same person');
                isValid = false;
            }

            // Validate BCP Coordinator vs Alt BCP Coordinator
            if (bcpCoordinatorID && altBcpCoordinatorID && bcpCoordinatorID !== "0" && altBcpCoordinatorID !== "0" && bcpCoordinatorID === altBcpCoordinatorID) {
                showRoleValidationError('#ddlUnitBCPCor', 'BCP Coordinator and Alternate BCP Coordinator cannot be the same person');
                showRoleValidationError('#ddlAltBCPCor', 'BCP Coordinator and Alternate BCP Coordinator cannot be the same person');
                isValid = false;
            }

            return isValid;
        }

        // Function to show role validation error
        function showRoleValidationError(selector, message) {
            var element = $(selector);
            element.addClass('is-invalid').removeClass('is-valid');

            var formGroup = element.closest('.form-group');
            var feedbackElement = formGroup.find('.invalid-feedback');
            if (feedbackElement.length > 0) {
                feedbackElement.text(message);
                // Use CSS classes instead of forcing display
                feedbackElement.addClass('custom-validation show').show();
            }
        }

        // Function to clear role validation errors
        function clearRoleValidationErrors() {
            var roleSelectors = ['#ddlUnitHead', '#ddlAltUnitHead', '#ddlUnitBCPCor', '#ddlAltBCPCor'];

            roleSelectors.forEach(function(selector) {
                var element = $(selector);
                var formGroup = element.closest('.form-group');
                var feedbackElement = formGroup.find('.invalid-feedback');

                // Only clear if the current message is a role validation error
                if (feedbackElement.length > 0 && feedbackElement.text().includes('cannot be the same person')) {
                    element.removeClass('is-invalid');
                    // Use CSS classes for proper hiding
                    feedbackElement.removeClass('custom-validation show').hide();

                    // Restore original message
                    var originalMessages = {
                        '#ddlUnitHead': 'Select Unit Head',
                        '#ddlAltUnitHead': 'Select Alternate Unit Head',
                        '#ddlUnitBCPCor': 'Select BCP Coordinator',
                        '#ddlAltBCPCor': 'Select Alt BCP Coordinator'
                    };

                    if (originalMessages[selector]) {
                        feedbackElement.text(originalMessages[selector]);
                    }
                }
            });
        }

        // Add event listeners for real-time validation
        $('#ddlUnitHead, #ddlAltUnitHead, #ddlUnitBCPCor, #ddlAltBCPCor').on('change', function() {
            var $this = $(this);

            // Clear validation state when user makes a selection
            if ($this.val() && $this.val() !== "0") {
                $this.removeClass('is-invalid');
                var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be the same person')) {
                    feedbackElement.removeClass('custom-validation show').hide();
                }
            }

            // Small delay to allow the change to complete, then validate roles
            setTimeout(function() {
                validateRolesDifferent();
            }, 100);
        });

        // Prevent validation messages from showing on page load
        $(window).on('load', function() {
            // Hide all validation messages that might be showing incorrectly
            $('.invalid-feedback').each(function() {
                var $feedback = $(this);
                var $input = $feedback.closest('.form-group').find('input, select');

                // Only hide if the input is not actually invalid
                if ($input.length > 0 && !$input.hasClass('is-invalid')) {
                    $feedback.hide();
                }
            });
        });
    });
</script>