﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Org.BouncyCastle.Asn1.Ocsp;
using static BCM.Shared.BCPEnum;

namespace BCM.UI.Areas.BCMProcessBIAForms.Controllers;
[Area("BCMProcessBIAForms")]
public class BIADependentEntitiesController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    int iBIAID = 0;
    int iProcessID = 0;
    int iSectionID = 0;
    int iIsBCMEntity = 0;
    public BIADependentEntitiesController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    [HttpGet]
    public IActionResult BIADependentEntities(string strBIAID, string strProcessID, string strSectionID, string strIsBCMEntity)
    {
        List<BIADependentEntitiesInfo> lstBIADependentEntitiesInfo = new List<BIADependentEntitiesInfo>();
        try
        {

            iBIAID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strBIAID));
            iProcessID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strProcessID));
            iSectionID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strSectionID));
            iIsBCMEntity = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strIsBCMEntity));

            BusinessProcessInfo objBusinessProcess = new BusinessProcessInfo();
            objBusinessProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(iProcessID.ToString() == "" ? "0" : iProcessID), (int)BCPEnum.EntityType.BusinessProcess);

            HttpContext.Session.SetString("SectionID", iSectionID.ToString());
            HttpContext.Session.SetString("ProcessID", iProcessID.ToString());
            HttpContext.Session.SetString("IsBCMEntity", iIsBCMEntity.ToString());
            HttpContext.Session.SetString("BIAID", iBIAID.ToString());

            ViewBag.EntityName = new SelectList(_Utilities.PopulateOtherBCMEntities(_UserDetails.OrgID, 0, 0, 0), "ID", "EntityName");
            ViewBag.Impact = new SelectList(_Utilities.PopulateBiaSectionImpactStatus(), "ImpactID", "ImpactName");
            ViewBag.Questions = _ProcessSrv.GetBIASurveyQuestionListBySectionID(Convert.ToInt32(HttpContext.Session.GetString("SectionID")));
            ViewBag.ProcessName = objBusinessProcess.ProcessName;
            ViewBag.ProcessCode = objBusinessProcess.ProcessCode;
            ViewBag.ProcessVersion = objBusinessProcess.ProcessVersion;
            BIASection objBIASection = _ProcessSrv.GetBIASurveySectionById(iSectionID, _UserDetails.OrgID);
            ViewBag.Description = objBIASection.SectionDescription;


            ViewBag.ButtonAccess = _Utilities.ShowButtonsByAccess((objBusinessProcess.Status).ToString(), objBusinessProcess.ApproverID.ToString(), objBusinessProcess.ProcessOwnerID.ToString(),
                                                        objBusinessProcess.AltProcessOwnerID.ToString(), _UserDetails.UserID.ToString(),
                                                        Convert.ToInt32(BCPEnum.PrivilegeID.Create));

            lstBIADependentEntitiesInfo = GetDependentEntityBIA();

            GetRecordIDByProcessID();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstBIADependentEntitiesInfo);
    }



    public List<BIADependentEntitiesInfo> GetDependentEntityBIA()
    {
        string IsCriticals = string.Empty;
        var lstBIADependentEntitiesInfo = new List<BIADependentEntitiesInfo>();
        try
        {
            lstBIADependentEntitiesInfo = _ProcessSrv.BIADependentEntities_GetByBIAID(Convert.ToInt32(HttpContext.Session.GetString("BIAID")));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return null;
        }
        return lstBIADependentEntitiesInfo;
    }



    private void GetRecordIDByProcessID()
    {
        BusinessProcessInfo objBusinessProcessInfo = new BusinessProcessInfo();
        try
        {
            objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), (int)BCPEnum.EntityType.BusinessProcess);
            HttpContext.Session.SetString("RecordID", objBusinessProcessInfo.RecordID.ToString());
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }



    [HttpPost]
    public IActionResult AddOrEditBIADependentEntities(BIADependentEntitiesInfo objBIADependentEntitiesInfo)
    {
        BIADependentEntitiesInfo bIADependentEntitiesInfo = new BIADependentEntitiesInfo();

        int IsComplete = 0;
        bool iSuccess;
        string strError = string.Empty;
        try
        {
            if (Convert.ToInt32(objBIADependentEntitiesInfo.EntityID) > 0)
            {
                IsComplete = 1;
            }
            else
            {
                IsComplete = 0;
            }
            bIADependentEntitiesInfo.ID = objBIADependentEntitiesInfo.ID;
            bIADependentEntitiesInfo.EntityID = objBIADependentEntitiesInfo.EntityID;
            bIADependentEntitiesInfo.Description = objBIADependentEntitiesInfo.Description;
            bIADependentEntitiesInfo.QuestionID = objBIADependentEntitiesInfo.QuestionID;
            bIADependentEntitiesInfo.IscriticalForProcess = objBIADependentEntitiesInfo.IsCritical.ToString();
            bIADependentEntitiesInfo.BIAID = GetCurrentBIAID();
            bIADependentEntitiesInfo.CreatedBy = _UserDetails.UserID;
            bIADependentEntitiesInfo.ChangedBy = _UserDetails.UserID;
            bIADependentEntitiesInfo.BIAFinding = objBIADependentEntitiesInfo.BIAFinding;
            bIADependentEntitiesInfo.Impact = objBIADependentEntitiesInfo.Impact;
            bIADependentEntitiesInfo.IsComplete = IsComplete;

            iSuccess = Convert.ToInt32(_ProcessSrv.BIADependent_Entities_SaveAndUpdate(bIADependentEntitiesInfo)) > 0;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BIADependentEntities", new
        {
            strBIAID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")),
            strProcessID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID")),
            strSectionID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID")),
            strIsBCMEntity = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity"))
        });
    }



    private int GetCurrentBIAID()
    {
        BIASection objBIASection = new BIASection();
        int iBIAIDForBIADependentEntities = Convert.ToInt32(HttpContext.Session.GetString("BIAID"));
        try
        {
            if (iBIAIDForBIADependentEntities == 0)
            {
                objBIASection.Version = "1.0";
                objBIASection.VersionChangeDescription = "";
                objBIASection.ApprovalStatus = ((int)BCPEnum.ApprovalType.Initiated).ToString();
                objBIASection.ProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
                objBIASection.SectionID = Convert.ToInt32(HttpContext.Session.GetString("SectionID"));
                objBIASection.IsBCMEntity = 0;
                objBIASection.IsEffective = 1;
                objBIASection.CreatedBy = _UserDetails.UserID;
                objBIASection.ChangedBy = _UserDetails.UserID;
                iBIAIDForBIADependentEntities = _ProcessSrv.ProcessBIASectionSave(objBIASection);
                ViewBag.BIAID = iBIAIDForBIADependentEntities;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return iBIAIDForBIADependentEntities;
    }

    [HttpGet]
    public IActionResult EditBIADependentEntities(int iID)
    {
        BIADependentEntitiesInfo objBIADependentEntitiesInfo = new BIADependentEntitiesInfo();
        try
        {
            if (iID > 0)
            {
                objBIADependentEntitiesInfo = _ProcessSrv.BIADependentEntities_GetByID(iID);
            }
            return Json(objBIADependentEntitiesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return BadRequest("Error occurred");
        }
    }


    [HttpGet]
    public IActionResult DeleteBIADependentEntities(int iID)
    {
        BIADependentEntitiesInfo objBIADependentEntitiesInfo = new BIADependentEntitiesInfo();
        try
        {
            if (iID > 0)
            {
                objBIADependentEntitiesInfo = _ProcessSrv.BIADependentEntities_GetByID(iID);               
            }
            
            return PartialView("_DeleteBIADependentEntities", objBIADependentEntitiesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BIADependentEntities", new
        {
            strBIAID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")),
            strProcessID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID")),
            strSectionID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID")),
            strIsBCMEntity = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity"))
        });
    }

    [HttpPost]
    public IActionResult DeleteBIADependentEntities(BIADependentEntitiesInfo objBIADependentEntitiesInfo)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.BIADependentEntities_DeleteByID(objBIADependentEntitiesInfo.ID,_UserDetails.UserID);
            if (bSuccess)
            {
                int iBIAIDAfterDelete = _ProcessSrv.ProcessBIAUpdateByBIAID(Convert.ToInt32(HttpContext.Session.GetString("BIAID")), _UserDetails.UserID);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BIADependentEntities", new
        {
            strBIAID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")),
            strProcessID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID")),
            strSectionID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID")),
            strIsBCMEntity = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity"))
        });
    }
}

