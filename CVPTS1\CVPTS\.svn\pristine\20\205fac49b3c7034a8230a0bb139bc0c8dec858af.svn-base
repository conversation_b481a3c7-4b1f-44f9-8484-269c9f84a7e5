﻿@model BCM.BusinessClasses.SubResourceModel
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<form asp-action="AddBCMGroupMember" method="post" id="addBCMGroupMember">
    <section>
        <div class="row align-items-center">
            <div class="col">
                <div class="form-group">
                    @* <input type="hidden" value="@ViewData["orgID"]" /> *@
                    @* <input type="hidden" value="@ViewData["grpID"]" /> *@
                    <label class="form-label">Unit</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-unit"></i></span>
                        <select class="form-select form-select-sm selectized" id="ddlUnit">
                            <option selected value="0">-- Select Unit --</option>
                            @foreach (var unit in ViewBag.UnitList)
                            {
                                <option value="@unit.Value">@unit.Text</option>
                                @* <!option value="@unit.Value" @(unit.Value == selectedUnitID.ToString() ? "selected=\"selected\"" : "")>@unit.Text</!option> *@
                            }
                        </select>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Department</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-department"></i></span>
                        <select class="form-select form-select-sm selectized" id="ddlDepartment">
                            <option selected value="0">-- Select Department --</option>
                            @foreach (var department in ViewBag.DepartmentList)
                            {
                                <option value="@department.Value">@department.Text</option>
                            }
                        </select>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Site</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                        <select class="form-select form-select-sm selectized" id="ddlFacilities">
                            <option selected value="0">-- Select Site --</option>
                            @foreach (var site in ViewBag.Facilities)
                            {
                                <option value="@site.Value">@site.Text</option>
                            }
                        </select>
                    </div>
                </div>

            </div>
            <div class="col align-self-end">
                <div class="form-group">
                    <a class="btn btn-sm btn-primary" href="@Url.Action("BCMGroups", "BCMGroups", new { area = "BCMTeams" })">View All Groups</a>
                    @* <button type="submit" class="btn btn-primary btn-sm mx-2">Save Member</button> *@
                </div>
            </div>
        </div>
        <div class="row align-items-center" id="tblData">
            <div class="col">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                    <label class="form-check-label" for="flexCheckDefault">
                        Check all
                    </label>
                </div>
            </div>
            <div class="col-3">
                <div class="input-group">
                    <span class="input-group-text py-1"><i class="cv-search"></i></span>
                    <input id="search-inp1" type="text" class="form-control" placeholder="Search">
                </div>
            </div>
            <div class="col-12">
                <table class="table table-hover datatable table-sm">
                    <thead>
                        <tr>
                            <th>@Html.DisplayName("User Name")</th>
                            <th>@Html.DisplayName("Email")</th>
                            <th>@Html.DisplayName("Mobile")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model != null)
                        {
                            foreach (var item in Model.MainResources)
                            {
                                var objIsChecked = Model.SubBCMResourcesID.Contains(item.ResourceId);
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input userCheckBox" name="selectedResourcesID" data-user-id="@item.ResourceId" type="checkbox" id="<EMAIL>"
                                            value="@item.ResourceId" @(objIsChecked ? "checked disabled" : "")>
                                            <label class="form-check-label" for="<EMAIL>">
                                                <input type="hidden" asp-for="@item.UserID" />
                                                @item.ResourceName
                                            </label>
                                        </div>
                                    </td>
                                    <td><i class="cv-mail me-1 align-middle"></i>@item.CompanyEmail</td>
                                    <td><i class="cv-Mobile me-1 align-middle"></i>@item.MobilePhone</td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </section>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <input type="hidden" id="checkedCheckBoxes" name="checkedCheckBoxes" />
            <button type="submit" class="btn btn-primary btn-sm">Save Member</button>
        </div>
    </div>
</form>
<script type="text/javascript">
    $(document).ready(function () {
        var table = $('.datatable').DataTable();
        $('#search-inp1').on('keyup', function () {
            table.search($(this).val()).draw();
        });

            // // Handle click on "Select all" control
            // $('#flexCheckDefault').on('click', function () {
            //     $// Get all rows with search applied
            //     var rows = table.rows({ 'search': 'applied' }).nodes();
            //     // Check/uncheck checkboxes for all rows in the table
            //     $(rows).find('input[type="checkbox"]').not(':disabled').prop('checked', this.checked);
            // });

            // // Handle click on checkbox to set state of "Select all" control
            // $('.datatable tbody').on('change', 'input[type="checkbox"]', function () {
            //     // If checkbox is not checked
            //     if (!this.checked) {
            //         var el = $('#flexCheckDefault').get(0);
            //         // If "Select all" control is checked and has 'indeterminate' property
            //         if (el && el.checked && ('indeterminate' in el)) {
            //             // Set visual state of "Select all" control
            //             // as 'indeterminate'
            //             el.indeterminate = true;
            //         }
            //     }
            // });

            var checkedCheckBoxes = new Set();

        $('#flexCheckDefault').on('click', function () {
            var rows = table.rows({ 'search': 'applied' }).nodes();
            $(rows).find('input[type="checkbox"]').not(':disabled').prop('checked', this.checked);
            $(rows).find('input[type="checkbox"]').not(':disabled').each(function () {
                var userId = $(this).val();
                if (this.checked) {
                    checkedCheckBoxes.add(userId);
                } else {
                    checkedCheckBoxes.delete(userId);
                }
            });
            updateHiddenInput();
        });

        $('.datatable tbody').on('change', 'input[type="checkbox"]', function () {
            var userId = $(this).val();
            if (this.checked) {
                checkedCheckBoxes.add(userId);
            } else {
                checkedCheckBoxes.delete(userId);
            }

            if (!this.checked) {
                var el = $('#flexCheckDefault').get(0);
                if (el && el.checked && ('indeterminate' in el)) {
                    el.indeterminate = true;
                }
            }
            updateHiddenInput();
        });

        function updateHiddenInput() {
            var hiddenInput = $('#checkedCheckBoxes');
            hiddenInput.val(Array.from(checkedCheckBoxes).join(','));
        }

        $('#addBCMGroupMember').on('submit', function () {
            updateHiddenInput();
        })
    });
</script>

<script>

    $(document).ready(function () {

        $('#ddlUnit').change(function () {
            
            var UnitID = $(this).val();
            var DepartmentID = 0;
            var FacilityID = 0;

            if (UnitID) {
                $.ajax({
                    url: '@Url.Action("GetAllDepartments", "BCMGroups")',
                    type: 'GET',
                    data: { iUnitID: UnitID },
                    // success: function (response) {
                    //     let selectizeInstance = $('#ddlDepartment')[0].selectize;
                    //     selectizeInstance.clear();
                    //     selectizeInstance.clearOptions();
                    //     selectizeInstance.addOption({ value: "0", text: "-- Select Department --" });
                    //     selectizeInstance.addItem("0");

                    //     response && response.forEach(({ departmentID, departmentName }) => {
                    //          if (departmentID && departmentName) {
                    //              selectizeInstance.addOption({ value: departmentID, text: departmentName });
                    //          }
                    //     });
                    // }
                    success: function (data) {
                        var department = $('#ddlDepartment');
                        department.empty();
                        department.append('<option value="0">-- Select Department --</option>');
                        $.each(data, function (index, item) {
                            department.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                        });
                    }
                })

                $.ajax({
                    url: '@Url.Action("GetAllFacilities", "BCMGroups")',
                    type: 'GET',
                    data: { iUnitID: UnitID },
                    // success: function (response) {
                    //                     let selectizeInstance = $('#ddlFacilities')[0].selectize;
                    //                     selectizeInstance.clear();
                    //                     selectizeInstance.clearOptions();
                    //                     selectizeInstance.addOption({ value: "0", text: "-- Select Site --" });
                    //                     selectizeInstance.addItem("0");

                    //                     response && response.forEach(({ facilityID, facilityName }) => {
                    //                          if (facilityID && facilityName) {
                    //                              selectizeInstance.addOption({ value: facilityID, text: facilityName });
                    //                          }
                    //                     });
                    //                 }
                    success: function (data) {
                        var facilities = $('#ddlFacilities');
                        facilities.empty();
                        facilities.append('<option value="0">-- Select Site --</option>');
                        $.each(data, function (index, item) {
                            facilities.append('<option value="' + item.facilityID + '">' + item.facilityName + '</option>')
                        });
                    }
                })

                $.ajax({
                    url: '@Url.Action("FilteredUserList", "AddBCMGroupMembers")',
                    type: 'GET',
                    data: { iUnitID: UnitID, iDepartmentID: DepartmentID, iFacilityID: FacilityID },
                    success: function (data) {
                        var tableData = $('#tblData');
                        tableData.empty();
                        $('#tblData').html(data);
                    },
                    error: function (error) {
                        console.log("Error : ", error);
                    }
                });

            }
        });

        $('#ddlDepartment').change(function () {
            var DepartmentID = $(this).val();
            var UnitID = $('#ddlUnit').val();
            var FacilityID = $('#ddlFacilities').val();

            if (DepartmentID) {
                $.ajax({
                    url: '@Url.Action("FilteredUserList", "AddBCMGroupMembers")',
                    type: 'GET',
                    data: { iUnitID: UnitID, iDepartmentID: DepartmentID, iFacilityID: FacilityID },
                    success: function (data) {
                        $('#tblBody').html(data);
                    },
                    error: function (error) {
                        console.log("Error : ", error);
                    }
                });
            }
        });

    });

</script>