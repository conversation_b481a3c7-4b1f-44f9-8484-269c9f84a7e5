﻿
using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace BCM.CVWebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CVWebAPIController : ControllerBase
    {
        private readonly ProcessSrv _ProcessSrv;
        private readonly BCMMail _BCMMail;
        private readonly CVLogger _CVLogger;
        private readonly Utilities _Utilities;
        private readonly IConfiguration _configuration;

        public CVWebAPIController(ProcessSrv ProcessSrv, BCMMail BCMMail, CVLogger CVLogger, Utilities Utilities, IConfiguration configuration)
        {
            _ProcessSrv = ProcessSrv;
            _BCMMail = BCMMail;
            _CVLogger = CVLogger;
            _Utilities = Utilities;
            _configuration = configuration;
        }

        #region Login
        [HttpPost]
        [Route("Login")]
        public IActionResult Login([FromBody] ManageUserLoginDetails objManageUserLoginDetails)
        {
            ManageUsersDetails objManageUsersDetails = new ManageUsersDetails();
            try
            {
                string strUtype = "U";
                if (objManageUserLoginDetails.Username != null && objManageUserLoginDetails.Password != null && objManageUserLoginDetails.OrgCode != null)
                {
                    objManageUsersDetails = _ProcessSrv.GetManageUserByLoginNameLoginCodeUserType(objManageUserLoginDetails.Username, objManageUserLoginDetails.OrgCode, strUtype);
                    HttpContext.Session.SetString("LoginResourceDetails", JsonConvert.SerializeObject(objManageUsersDetails));

                    if (objManageUsersDetails.Password != null && objManageUsersDetails.Password.Equals(objManageUserLoginDetails.Password))
                    {
                        if (objManageUsersDetails.OrganizationCode != null && objManageUsersDetails.OrganizationCode.ToString().Equals(objManageUserLoginDetails.OrgCode))
                        {
                            return Ok(objManageUsersDetails);
                        }
                        else
                        {
                            return BadRequest("Invalid Org Group Code");
                        }
                    }
                    else
                    {
                        return BadRequest("Invalid User Name or Password");
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);

            }
            return Ok(objManageUsersDetails);
        }
        #endregion

        #region BCMOrgStructure

        #region Organisation
        [HttpGet]
        [Route("OrganizationGroupCode")]
        public IActionResult OrganizationGroupCode()
        {
            List<OrganizationMaster> lstOrganizationMaster = new List<OrganizationMaster>();
            try
            {
                lstOrganizationMaster = _ProcessSrv.GetOrgGroupCode();

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);

            }
            return Ok(lstOrganizationMaster);
        }

        [HttpGet]
        [Route("GetManageOrgGroup")]
        public IActionResult GetManageOrgGroup(int RoleID, int UserID)
        {
            List<OrgGroup> objOrgGroup = new List<OrgGroup>();
            try
            {
                objOrgGroup = _ProcessSrv.GetOrgGroupListByOrgAccessLevel(Convert.ToString(RoleID), Convert.ToString(UserID));
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(objOrgGroup);
        }

        [HttpGet]
        [Route("GetAllOrganisation")]
        public IActionResult GetAllOrganisation()
        {
            List<OrgInfo> lstOrgInfo = new List<OrgInfo>();
            try
            {
                lstOrgInfo = _ProcessSrv.GetOrganizationMasterList_ByOrgGroupID("10");
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }

            return Ok(lstOrgInfo);
        }

        [HttpGet]
        [Route("ManageOrganization")]
        public IActionResult ManageOrganization()
        {
            List<OrgInfo> lstOrgInfo = new List<OrgInfo>();
            try
            {
                lstOrgInfo = _ProcessSrv.GetOrganizationMasterList();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstOrgInfo);
        }
        #endregion

        #region Org Unit

        [HttpGet]
        [Route("GetAllUnit")]
        public IActionResult GetAllUnit(int OrgID)
        {
            List<OrgUnit> lstOrgUnit = new List<OrgUnit>();
            try
            {
                lstOrgUnit = _ProcessSrv.GetOrganizationUnitList_New(OrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstOrgUnit);
        }

        #endregion

        #region Org Department

        [HttpGet]
        [Route("GetAllDepartment")]
        public IActionResult GetAllDepartment()
        {
            List<DepartmentInfo> lstDepartmentInfo = new List<DepartmentInfo>();
            try
            {
                lstDepartmentInfo = _ProcessSrv.GetAllDepartment();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstDepartmentInfo);
        }

        [HttpGet]
        [Route("GetAllDepartmentList")]
        public IActionResult GetAllDepartmentList()
        {
            List<DepartmentInfo> lstDepartmentInfo = new List<DepartmentInfo>();
            try
            {
                lstDepartmentInfo = _ProcessSrv.GetAllDepartment();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }

            return Ok(lstDepartmentInfo);
        }
        #endregion

        #region Org Sub-Department
        [HttpGet]
        [Route("GetAllSubDepartment")]
        public IActionResult GetAllSubDepartment()
        {
            List<SubFunction> lstSubFunction = new List<SubFunction>();
            try
            {
                lstSubFunction = _ProcessSrv.GetSubFunctionList_New();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstSubFunction);
        }

        [HttpGet]
        [Route("SubDepartment")]
        public IActionResult SubDepartment(string id)
        {
            SubFunction objSubFunction = new SubFunction();
            try
            {
                objSubFunction = _ProcessSrv.GetSubFunctionBySubFunctionId(id);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(objSubFunction);

        }

        [HttpGet]
        [Route("GetAllSubDepartmentList")]
        public IActionResult GetAllSubDepartmentList()
        {
            List<SubFunction> lstSubFunction = new List<SubFunction>();
            try
            {
                lstSubFunction = _ProcessSrv.GetSubFunctionList_New();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstSubFunction);
        }
        #endregion

        #region Facility

        [HttpGet]
        [Route("GetAllFacilities")]
        public IActionResult GetAllFacilities(int OrgID)
        {
            List<Facility> lstFacility = new List<Facility>();
            try
            {
                lstFacility = _ProcessSrv.GetFacilitiesList(OrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstFacility);
        }

        [HttpGet]
        [Route("Facilities")]
        public IActionResult Facilities(int iId)
        {
            Facility objFacility = new Facility();
            try
            {
                objFacility = _ProcessSrv.GetFacilitiesById(iId);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(objFacility);
        }

        [HttpPost]
        [Route("Location_ByID")]
        public IActionResult Location_ByID(int iId)
        {
            LocationMaster objLocationMaster = new LocationMaster();
            try
            {
                objLocationMaster = _ProcessSrv.GetLocationById(iId);

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(objLocationMaster);
        }
        #endregion

        #endregion

        #region BCMResourceManagement

        #region BCM Resource

        [HttpGet]
        [Route("GetAllResource")]
        public IActionResult GetAllResource()
        {
            List<ResourcesInfo> lstResourcesInfo = new List<ResourcesInfo>();
            try
            {
                lstResourcesInfo = _ProcessSrv.GetResourceDetailsByOrgID();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }

            return Ok(lstResourcesInfo);
        }

        [HttpGet]
        [Route("GetUserProfileData")]
        public IActionResult GetUserProfileData(int UserID)
        {
            ResourcesInfo objResourcesInfo = new ResourcesInfo();
            try
            {
                objResourcesInfo = _ProcessSrv.GetResourcesByResourceID(UserID);
                if (objResourcesInfo != null)
                {
                    return Ok(objResourcesInfo);
                }
                else
                {
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [Route("GetAllUserActivities")]
        public IActionResult GetAllUserActivities()
        {
            List<UserActivitiesInfo> lstUserActivitiesInfo = new List<UserActivitiesInfo>();
            try
            {
                lstUserActivitiesInfo = _ProcessSrv.UserActivities(); // UserActivities_GetAll();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstUserActivitiesInfo);
        }

        [HttpGet]
        [Route("GetAllUserLoginList")]
        public IActionResult GetAllUserLoginList(int OrgGroupID)
        {
            List<ResourcesInfo> lstResourcesInfo = new List<ResourcesInfo>();
            try
            {
                lstResourcesInfo = _ProcessSrv.GetResourceMasterLoginDetailsByOrgGrpID(OrgGroupID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstResourcesInfo);
        }
        #endregion

        #region BCMGroup
        [HttpGet]
        [Route("GetAllBCMGroup")]
        public IActionResult GetBCMGroupAllList()
        {
            List<BCMGroupInfo> lstBCMGroupInfo = new List<BCMGroupInfo>();
            try
            {
                lstBCMGroupInfo = _ProcessSrv.GetBCMGroupAllList();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstBCMGroupInfo);
        }
        #endregion

        #endregion

        #region Forgot Password
        [HttpPost]
        [Route("ForgotPassword")]
        public IActionResult ForgotPassword(ForgetPassword objForgetPassword)
        {
            try
            {
                if (!string.IsNullOrEmpty(objForgetPassword.EmailID))
                {
                    bool bValidEmail = _BCMMail.IsValidEmail(Convert.ToString(objForgetPassword.EmailID));

                    if (bValidEmail)
                    {
                        ManageUsersDetails objManageUsersDetails = _ProcessSrv.GetPWDBYEmailID(objForgetPassword.EmailID.Trim());
                        int iOrgId = objManageUsersDetails.OrgID;

                        if (!string.IsNullOrEmpty(objManageUsersDetails.Password))
                        {
                            VaultSettings objVaultSetting = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt16(iOrgId));

                            if (!string.IsNullOrEmpty(objVaultSetting.DevMode))
                            {
                                string body = "<HTML><HEAD></HEAD><BODY>Hello " + objManageUsersDetails.UserName + ",";
                                body += "<br /><br /> To initiate the process for resetting the password for your Account.";
                                body += "<br />Email Address :<u><font color='blue'>" + objForgetPassword.EmailID + "</font></u>";
                                string? strUName = objManageUsersDetails.UserName;

                                string strLink = "http://localhost:8100/ResetNewPassword?EmailID=" + objForgetPassword.EmailID.Trim();

                                body += "<br /> Please <a href =";
                                body += strLink + "> Click Here </a>to proceed...";

                                body += "<br />This is notification mail, no need to reply.";
                                body += "<br /><br />Thanks. <br /><br /><br /><br /><b>Admin</b><br />Continuity Vault</body></HTML>";

                                bool bSuccess = _BCMMail.SendMail("Password Change Notification", body, (objForgetPassword.EmailID.Trim()), string.Empty, string.Empty, string.Empty, Convert.ToString(iOrgId));

                                if (bSuccess)
                                {
                                    return Ok("mail has been send successfully");
                                }
                            }
                        }
                    }
                    else
                    {
                        return Ok("Please enter valid Email ID");
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok();
        }
        #endregion

        #region Reset New Password
        [HttpPost]
        [Route("ResetNewPassword")]
        public IActionResult ResetNewPassword([FromBody] ResetNewPassword objResetNewPassword)
        {
            try
            {
                string? PasswordLength = _configuration.GetValue<string>("Enums:PasswordLength");
                string strResetNewPasswordResponse = string.Empty;

                ManageUsersDetails objManageUsersDetails = _ProcessSrv.GetPWDBYEmailID(objResetNewPassword.EmailID != null ? objResetNewPassword.EmailID.Trim() : string.Empty);
                int iOrgIDByEmailID = objManageUsersDetails.OrgID;
                VaultSettings objVaultSetting = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt16(iOrgIDByEmailID));

                if (objManageUsersDetails.Password != null)
                {
                    ManageUsersDetails oDetails = _ProcessSrv.GetOldPassword(objManageUsersDetails.Password, Convert.ToInt32(objManageUsersDetails.UserID));
                    string strUserNameVal = oDetails.LoginName != null ? oDetails.LoginName : string.Empty;

                    if (objResetNewPassword.NewPassword != null && objResetNewPassword.ConfirmNewPassword != "")
                    {
                        if (objResetNewPassword.NewPassword == objResetNewPassword.ConfirmNewPassword)
                        {
                            if (objResetNewPassword.NewPassword.Trim().Length >= Convert.ToInt32(PasswordLength))
                            {
                                string? countVal = "0";
                                if (objVaultSetting != null)
                                {
                                    countVal = objVaultSetting.PasswordHistory;
                                }

                                var passwordHistoryList = _ProcessSrv.GetPasswordHistoryListByUserIDAndOrgID(Convert.ToInt32(objManageUsersDetails.OrgID.ToString()), Convert.ToInt32(objManageUsersDetails.UserID), Convert.ToInt32(countVal));

                                foreach (PasswordHistory passwordHistory in passwordHistoryList)
                                {
                                    if (Convert.ToInt32(countVal) > 0)
                                    {
                                        if (passwordHistory.Password == objResetNewPassword.NewPassword)
                                        {
                                            strResetNewPasswordResponse = "New password can not be same as your last " + countVal + " password(s)";
                                        }
                                    }
                                }

                                bool bUpdate = false;

                                if (!objResetNewPassword.NewPassword.Contains(strUserNameVal))
                                {
                                    bUpdate = _ProcessSrv.ManageUserResetPasswordByOLDPassword(objManageUsersDetails.Password, objResetNewPassword.NewPassword, objManageUsersDetails.UserID.ToString(), DateTime.UtcNow);
                                }
                                else
                                {
                                    strResetNewPasswordResponse = "User Name and Password can not be similar";
                                }

                                if (bUpdate == true)
                                {
                                    PasswordHistory objPasswordHistory = new PasswordHistory();
                                    objPasswordHistory.UserID = objManageUsersDetails.UserID.ToString();
                                    objPasswordHistory.Password = objResetNewPassword.NewPassword;
                                    objPasswordHistory.OrgID = Convert.ToString(iOrgIDByEmailID);
                                    var result = _ProcessSrv.PasswordHistorySave(objPasswordHistory);

                                    strResetNewPasswordResponse = "Password Changed Successfully.";

                                    return Ok(result);
                                }

                                else
                                {
                                    strResetNewPasswordResponse = "Password must contain: Minimum 8 and Maximum 12 characters atleast 1 UpperCase Alphabet, 1 LowerCase Alphabet, 1 Number and 1 Special Character in $@!%*?&";

                                }
                            }
                            else
                            {
                                strResetNewPasswordResponse = "Password Length should be Greater than 7 Characters!!!";
                            }
                        }
                        else
                        {
                            strResetNewPasswordResponse = "New Password and Confirm Password Does Not Match !!!";
                        }
                    }
                    else
                    {
                        strResetNewPasswordResponse = "New Password and Confirm Password Does Not Match !!!";
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }

            return BadRequest("Invalid request");
        }

        [HttpGet]
        [Route("GetUserDetailsByEmailID")]
        public IActionResult GetUserDetailsByEmailID(string strEmailID)
        {
            ManageUsersDetails objManageUsersDetails = new ManageUsersDetails();
            try
            {
                objManageUsersDetails = _ProcessSrv.GetPWDBYEmailID(strEmailID.Trim());
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }

            return Ok(objManageUsersDetails);
        }

        #endregion

        #region  Reset Password
        [HttpPost]
        [Route("ResetPassword")]
        public IActionResult ResetPassword([FromBody] ResetPassword objResetPassword)
        {
            int iOrgID = 0;
            ManageUsersDetails objDetails = _ProcessSrv.GetOldPassword(objResetPassword.OldPassword == null ? string.Empty : objResetPassword.OldPassword, Convert.ToInt32(objResetPassword.UserID));
            string strUserNameVal = objDetails.LoginName == null ? string.Empty : objDetails.LoginName;
            string strResetNewPasswordResponse = string.Empty;

            try
            {
                if (objResetPassword.OldPassword != "" && objResetPassword.NewPassword != "" && objResetPassword.ConfirmNewPassword != "")
                {
                    if (objResetPassword.OldPassword == objDetails.Password && objResetPassword.NewPassword == objResetPassword.ConfirmNewPassword)
                    {
                        List<ResourcesInfo> resourceList = _ProcessSrv.GetResourceInfoByUserIDAndOldPassword(string.IsNullOrEmpty(objResetPassword.OldPassword).ToString(), Convert.ToInt32(objResetPassword.UserID));
                        foreach (ResourcesInfo resouce in resourceList)
                        {
                            iOrgID = resouce.OrgID;
                        }

                        string strCount = "0";
                        VaultSettings objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt32(iOrgID));
                        if (objVaultSettings != null)
                        {
                            strCount = objVaultSettings.PasswordHistory == null ? string.Empty : objVaultSettings.PasswordHistory;
                        }

                        List<PasswordHistory> lstPasswordHistory = _ProcessSrv.GetPasswordHistoryListByUserIDAndOrgID(Convert.ToInt32(iOrgID.ToString()), Convert.ToInt32(objResetPassword.UserID), Convert.ToInt32(strCount));

                        foreach (PasswordHistory passwordHistory in lstPasswordHistory)
                        {
                            if (Convert.ToInt32(strCount) > 0)
                            {
                                if (passwordHistory.Password == objResetPassword.NewPassword)
                                {
                                    strResetNewPasswordResponse = "New password can not be same as your last " + strCount + " password(s)";
                                }
                            }
                        }

                        bool bIsUpdate = false;

                        string strNewPassword = objResetPassword.NewPassword == null ? string.Empty : objResetPassword.NewPassword;

                        if (!strNewPassword.Contains(strUserNameVal.ToString()))
                        {
                            bIsUpdate = _ProcessSrv.ManageUserResetPasswordByOLDPassword(string.IsNullOrEmpty(objResetPassword.OldPassword).ToString(), strNewPassword, Convert.ToString(objResetPassword.UserID), DateTime.Now);
                        }
                        else
                        {
                            strResetNewPasswordResponse = "User Name and Password can not be similar";
                        }

                        if (bIsUpdate == true)
                        {
                            PasswordHistory objPasswordHistory = new PasswordHistory();

                            objPasswordHistory.UserID = Convert.ToString(objResetPassword.UserID);
                            objPasswordHistory.Password = strNewPassword;
                            objPasswordHistory.OrgID = Convert.ToString(objResetPassword.OrgID);

                            bool bSuccess = _ProcessSrv.PasswordHistorySave(objPasswordHistory);

                            strResetNewPasswordResponse = "Password Changed Successfully.";
                        }
                        else
                        {
                            strResetNewPasswordResponse = "Password Change failed.";
                        }
                    }
                    else
                    {
                        strResetNewPasswordResponse = "Invalid User Name or Password!!!";

                        string strcountVal = "0";
                        string strtempAttempts = "0";

                        ManageUsersDetails objDetailsNew = _ProcessSrv.GetManageUserByLoginNameColl(strUserNameVal);

                        if (objDetailsNew.InValidAttempts != null)
                        {
                            strtempAttempts = objDetailsNew.InValidAttempts;
                        }

                        VaultSettings objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt32(objDetailsNew.OrgID));
                        if (objVaultSettings != null)
                        {
                            strcountVal = (objVaultSettings.WrongLoginAttempts == null ? "5" : objVaultSettings.WrongLoginAttempts);
                        }
                        ManageUsersDetails objManageUsers = new ManageUsersDetails();
                        objManageUsers.UserID = objDetailsNew.UserID;
                        objManageUsers.InValidAttempts = strtempAttempts;
                        bool success1 = _ProcessSrv.UpdateWrongPswdAttemptsCount(objManageUsers);
                        objDetailsNew = _ProcessSrv.GetManageUserByLoginNameColl(strUserNameVal);

                        if (Convert.ToInt32(strtempAttempts) > Convert.ToInt32(strcountVal))
                        {
                            bool bSuccess = false;

                            ManageUsersDetails objManageUserDetails = new ManageUsersDetails();
                            objManageUserDetails.UserID = objDetails.UserID;
                            objManageUserDetails.InValidAttempts = "0";

                            bSuccess = _ProcessSrv.UpdateUsersDetailsForWrongPswdAttempts(objManageUserDetails);

                            if (bSuccess)
                            {
                                strResetNewPasswordResponse = "User Account is Disabled, Please Contact Application Admin";

                            }
                        }
                    }
                }
                else
                {
                    strResetNewPasswordResponse = "Please Enter Password !!!";
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(strResetNewPasswordResponse);
        }
        #endregion

        #region BCMProcessBIA

        #region ManageBusinessProcess
        [HttpGet]
        [Route("ManageBusinessProcess")]
        public IActionResult ManageBusinessProcess(int OrgID)
        {
            List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
            try
            {
                lstBusinessProcessInfo = _ProcessSrv.GetBIAProcess_OrgUnitLevel(OrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstBusinessProcessInfo);
        }

        #endregion

        #endregion

        #region BCMAdministration
        [HttpGet]
        [Route("GetAllEscalationMatrix")]
        public IActionResult GetAllEscalationMatrix()
        {
            List<EscalationMatrix> lstEscalationMatrix = new List<EscalationMatrix>();
            try
            {
                List<EscalationMatrix> lstEscalationMatrixNew = new List<EscalationMatrix>();
                lstEscalationMatrixNew = _ProcessSrv.EscalationMatrix_GetAll();
                foreach (EscalationMatrix objEscalationMatrix in lstEscalationMatrixNew)
                {
                    string strEscType = string.Empty;
                    if (Convert.ToInt32(objEscalationMatrix.EscMatType) == (int)BCPEnum.EscalationMatrixType.Approval)
                    {
                        strEscType = BCPEnum.EscalationMatrixType.Approval.ToString();
                    }
                    else if (Convert.ToInt32(objEscalationMatrix.EscMatType) == (int)BCPEnum.EscalationMatrixType.Review)
                    {
                        strEscType = BCPEnum.EscalationMatrixType.Review.ToString();
                    }
                    else if (Convert.ToInt32(objEscalationMatrix.EscMatType) == (int)BCPEnum.EscalationMatrixType.TaskAssignment)
                    {
                        strEscType = BCPEnum.EscalationMatrixType.TaskAssignment.ToString();
                    }

                    lstEscalationMatrix = (from objEscalationMatrixNew in lstEscalationMatrixNew
                                           select new EscalationMatrix
                                           {
                                               EscMatCode = objEscalationMatrixNew.EscMatCode,
                                               EscMatID = objEscalationMatrixNew.EscMatID,
                                               EscMatName = objEscalationMatrixNew.EscMatName,
                                               EscMatType = strEscType,
                                               EscMatStatus = objEscalationMatrixNew.EscMatStatus,
                                               CreatedBy = objEscalationMatrixNew.CreatedBy,
                                               CreatedDate = objEscalationMatrixNew.CreatedDate,
                                               UpdatedBy = objEscalationMatrixNew.UpdatedBy,
                                               UpdatedDate = objEscalationMatrixNew.UpdatedDate,
                                               CreatedByName = objEscalationMatrixNew.CreatedByName,
                                               UpdatedByName = objEscalationMatrixNew.UpdatedByName,
                                               MatrixCount = objEscalationMatrixNew.MatrixCount,
                                               EscMatDesc = objEscalationMatrixNew.EscMatDesc,
                                               EscMatOwnerID = objEscalationMatrixNew.EscMatOwnerID,
                                               EscMatApproverID = objEscalationMatrixNew.EscMatApproverID,
                                               EscMatApprovedDate = objEscalationMatrixNew.EscMatApprovedDate,
                                               EscMatApproverName = objEscalationMatrixNew.EscMatApproverName,
                                               OrgID = objEscalationMatrixNew.OrgID,
                                               UnitID = objEscalationMatrixNew.UnitID,
                                               count = objEscalationMatrixNew.count,
                                               UserRoleID = objEscalationMatrixNew.UserRoleID,
                                               OrgGroupID = objEscalationMatrixNew.OrgGroupID,
                                           }).ToList();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstEscalationMatrix);
        }

        [HttpGet]
        [Route("DocumentAndPolicyControl")]
        public IActionResult DocumentAndPolicyControl(int OrgID)
        {
            string strApprovalStatus = string.Empty;
            List<Attachments> lstAttachment = new List<Attachments>();
            try
            {
                string strAttachmentType = Convert.ToString(Convert.ToInt32(BCPEnum.AttachmentType.DocumentPolicyControl));

                lstAttachment = _ProcessSrv.GetBCMPoliciesAttachmentListByType(strAttachmentType, OrgID, (int)BCPEnum.EntityType.policy);

                var objAttachmentList = lstAttachment.Select(attachment => new
                {
                    attachment.AttchmentName,
                    attachment.version,
                    attachment.Description,
                    attachment.CreatedDate,
                    attachment.CreatedBy,
                    attachment.UpdatedBy,
                    attachment.UpdatedDate,
                    attachment.ApproverName,
                    attachment.App_usermail,
                    attachment.App_Usermobile,
                    attachment.OrgName,
                    attachment.UnitName,
                    attachment.DepartmentName,
                    attachment.SubFunctionName,
                    attachment.username,
                    attachment.usermail,
                    attachment.Usermobile,
                    attachment.CreatedByName,
                    attachment.UpdatedByName,
                    attachment.DocCode,
                    strApprovalStatus = GetApprovalStatus(Convert.ToInt32(attachment.Status)),
                });

                return Ok(objAttachmentList);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
        }
        #endregion

        #region Manage Vendor
        [HttpGet]
        [Route("ManageVendor")]
        public IActionResult ManageVendor(int OrgID)
        {
            List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
            try
            {
                lstBusinessProcessInfo = _ProcessSrv.GetBIAVendor_OrgUnitLevel(OrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstBusinessProcessInfo);
        }
        #endregion

        #region Other BCM Document
        [HttpGet]
        [Route("OtherBCMDocument")]
        public IActionResult OtherBCMDocument(int OrgID)
        {
            List<Attachments> lstAttachmentInfo = new List<Attachments>();
            try
            {
                string strAttachmentType = Convert.ToString(Convert.ToInt32(BCPEnum.AttachmentType.OtherBCMDocument));
                lstAttachmentInfo = _ProcessSrv.GetBCMPoliciesAttachmentListByType(strAttachmentType, OrgID, (int)BCPEnum.EntityType.policy);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstAttachmentInfo);
        }

        [HttpGet]
        [Route("MyDocument")]
        public IActionResult MyDocument()
        {
            string? StrEntityid = Convert.ToString((int)BCPEnum.EntityType.MyDocuments);
            List<Attachments> lstAttachmentInfo = new List<Attachments>();
            try
            {
                lstAttachmentInfo = _ProcessSrv.GetCVaultMyDocument(StrEntityid);

                var objAttachmentList = lstAttachmentInfo.Select(document => new
                {
                    document.AttchmentName,
                    document.Description,
                    document.FileSize,
                    document.CreatedByName,
                    document.CreatedDate,
                    document.UpdatedByName,
                    document.UpdatedDate
                });

                return Ok(objAttachmentList);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
        }
        #endregion

        #region BCM Policy
        [HttpGet]
        [Route("BCMPolicy")]
        public IActionResult BCMPolicy(int OrgID)
        {
            List<Attachments> lstAttachmentInfo = new List<Attachments>();
            try
            {
                string StrAttachmentType = Convert.ToString(Convert.ToInt32(BCPEnum.AttachmentType.BCMPolicy));
                lstAttachmentInfo = _ProcessSrv.GetBCMPoliciesAttachmentListByType(StrAttachmentType, OrgID, (int)BCPEnum.EntityType.policy);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstAttachmentInfo);
        }
        #endregion

        #region BCM Manual
        [HttpGet]
        [Route("BCMManual")]
        public IActionResult BCMManual(int OrgID)
        {
            List<Attachments> lstAttachments = new List<Attachments>();
            try
            {
                string strAttachmentType = Convert.ToString(Convert.ToInt32(BCPEnum.AttachmentType.BCMManual));
                lstAttachments = _ProcessSrv.GetBCMPoliciesAttachmentListByType(strAttachmentType, OrgID, (int)BCPEnum.EntityType.policy);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstAttachments);
        }
        #endregion

        #region BCM Audit Policy
        [HttpGet]
        [Route("AuditPolicy")]
        public IActionResult AuditPolicy(int OrgID)
        {
            List<Attachments> lstAttachmentInfo = new List<Attachments>();
            try
            {
                string strAttachmentType = Convert.ToString(Convert.ToInt32(BCPEnum.AttachmentType.AuditPolicy));
                lstAttachmentInfo = _ProcessSrv.GetBCMPoliciesAttachmentListByType(strAttachmentType, OrgID, (int)BCPEnum.EntityType.policy);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstAttachmentInfo);
        }
        #endregion

        #region BCMRiskAssessment

        #region BCMRiskTrend
        [HttpGet]
        [Route("GetRiskTrendAll")]
        public IActionResult GetRiskTrendAll(int OrgID)
        {
            List<RiskTrendInfo> lstRiskTrendInfo = new List<RiskTrendInfo>();
            try
            {
                lstRiskTrendInfo = _ProcessSrv.GetRiskTrendAll_ByOrgID(OrgID);
                if (lstRiskTrendInfo != null)
                {
                    int i = 0;
                    foreach (RiskTrendInfo objTrend in lstRiskTrendInfo)
                    {
                        i++;
                        string strRatingText = string.Empty;
                        string strRatingValue = string.Empty;

                        strRatingText = GetRiskRatingDescription(Convert.ToInt32(objTrend.ResidualRiskSeverity), OrgID);
                        strRatingValue = GetRiskRatingValue(Convert.ToInt32(objTrend.ResidualRiskSeverity));
                        objTrend.ResidualRiskSeverityValue = strRatingValue;
                        objTrend.ResiRiskSeverity = Convert.ToInt32(objTrend.ResidualRiskSeverity);

                        int iImprReqd;
                        if (objTrend.RiskTrendStatus == 0)
                        {
                            iImprReqd = 0;
                            objTrend.ImprovementRequired = iImprReqd;
                        }
                        else if (objTrend.RiskTrendStatus == 1)
                        {
                            iImprReqd = 1;
                            objTrend.ImprovementRequired = iImprReqd;
                        }
                        else if (objTrend.RiskTrendStatus == 2)
                        {
                            iImprReqd = 2;
                            objTrend.ImprovementRequired = iImprReqd;
                        }

                        #region Code for Calculating Improvement Status for Trend

                        int iCount = 0;
                        int iTotal = 0;
                        int iInPrgCount = 0;
                        int iApprovedCount = 0;
                        int iCompletedCount = 0;

                        if (objTrend.RiskTrendStatus == Convert.ToInt32(BCPEnum.RiskTrendStatus.NoChange))
                        {
                            objTrend.TreatmentPlan = "0"; objTrend.ImprovementRequiredText = "No";
                        }
                        else if (objTrend.RiskTrendStatus == Convert.ToInt32(BCPEnum.RiskTrendStatus.Upward))
                        {
                            objTrend.TreatmentPlan = "1"; objTrend.ImprovementRequiredText = "Yes";
                        }
                        else if (objTrend.RiskTrendStatus == Convert.ToInt32(BCPEnum.RiskTrendStatus.Downward))
                        {
                            objTrend.TreatmentPlan = "2"; objTrend.ImprovementRequiredText = "NA";
                        }

                        List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
                        lstRiskManagement = _ProcessSrv.GetRiskDetails_PACAStatus(objTrend.RiskID);

                        if (lstRiskManagement != null)
                        {
                            foreach (RiskManagement objRiskManagement in lstRiskManagement)
                            {
                                iTotal++;
                                if (string.IsNullOrEmpty(objRiskManagement.PACAStatus).Equals(Convert.ToInt32(BCPEnum.PACAStatus.InProgress)))
                                {
                                    iInPrgCount++;
                                    iCount++;
                                    objTrend.ImprovementStatusText = objRiskManagement.PACAStatus;
                                }
                                else if (string.IsNullOrEmpty(objRiskManagement.PACAStatus).Equals(Convert.ToInt32(BCPEnum.PACAStatus.Completed)))
                                {
                                    iCompletedCount++;
                                    iCount++;
                                    objTrend.ImprovementStatusText = objRiskManagement.PACAStatus;
                                }
                                else if (string.IsNullOrEmpty(objRiskManagement.PACAStatus).Equals(Convert.ToInt32(BCPEnum.PACAStatus.Approved)))
                                {
                                    iApprovedCount++;
                                    iCount++;
                                    objTrend.ImprovementStatusText = objRiskManagement.PACAStatus;
                                }
                            }
                        }

                        if (iInPrgCount > 0)
                        {
                            objTrend.ImprovementStatus = Convert.ToInt32(BCPEnum.PACAStatus.InProgress);
                            objTrend.ImprovementStatusText = BCPEnum.PACAStatus.InProgress.ToString();
                        }
                        else if (iTotal == iCompletedCount)
                        {
                            objTrend.ImprovementStatus = Convert.ToInt32(BCPEnum.PACAStatus.Completed);
                            objTrend.ImprovementStatusText = BCPEnum.PACAStatus.Completed.ToString();
                        }
                        else if (iTotal == iApprovedCount)
                        {
                            objTrend.ImprovementStatus = Convert.ToInt32(BCPEnum.PACAStatus.Approved);
                            objTrend.ImprovementStatusText = BCPEnum.PACAStatus.Approved.ToString();
                        }
                        else
                        {
                            objTrend.ImprovementStatus = Convert.ToInt32(BCPEnum.PACAStatus.Draft);
                            objTrend.ImprovementStatusText = BCPEnum.PACAStatus.Draft.ToString();
                        }
                        #endregion Code for Calculating Improvement Status for Trend  

                        objTrend.ResidualRiskSeverity = strRatingText;
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstRiskTrendInfo);
        }

        private string GetRiskRatingDescription(int RatingVal, int OrgID)
        {
            string Text = string.Empty;

            try
            {
                int DefaultProfileID = _Utilities.DefaultRiskProfileID;
                List<RiskImpactMaster> objImpactColl = new List<RiskImpactMaster>();

                objImpactColl = _ProcessSrv.GetAllRiskSeverityDetails(OrgID, DefaultProfileID);

                if (RatingVal >= 0)
                {

                    if (objImpactColl != null)
                    {
                        var Rating = (from RiskImpactMaster objImpact in objImpactColl
                                      where RatingVal >= Convert.ToInt32(objImpact.FromImpactRange)
                                      && RatingVal <= Convert.ToInt32(objImpact.ToImpactRange)
                                      select objImpact).FirstOrDefault();

                        if (Rating != null)
                        {
                            Text = Rating.RiskSeverityName + "(" + RatingVal.ToString() + ")";
                        }
                    }
                }
                else
                {
                    Text = "NA";
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return Text;
        }

        private string GetRiskRatingValue(int RatingVal)
        {
            string? Val = string.Empty;
            try
            {
                List<RiskImpactMaster> objImpactColl = new List<RiskImpactMaster>();
                int DefaultProfileID = _Utilities.RiskProfileID;

                objImpactColl = _ProcessSrv.GetAllRiskSeverityDetails(1, DefaultProfileID);

                if (objImpactColl != null)
                {
                    var Rating = (from RiskImpactMaster objImpact in objImpactColl
                                  where
                               RatingVal >= Convert.ToInt32(objImpact.FromImpactRange) && RatingVal <= Convert.ToInt32(objImpact.ToImpactRange)
                                  select objImpact).FirstOrDefault();

                    if (Rating != null)
                    {
                        Val = string.IsNullOrEmpty(Rating.Id).ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return Val;
        }
        #endregion BCMRiskTrend

        #region ManageRisk
        [HttpGet]
        [Route("GetAllBCMRisk")]
        public IActionResult GetAllBCMRisk(int OrgID)
        {
            List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
            string strApprovalStatus = string.Empty;
            string strRiskCloseStatus = string.Empty;
            string strProbability = string.Empty;
            string strLastReviewDate = string.Empty;
            try
            {
                lstRiskManagement = _ProcessSrv.GetBCMRiskDetails(OrgID);

                #region with linq
                var objRiskManagement = lstRiskManagement.Select(r =>
                               new
                               {
                                   r.RiskCode,
                                   r.RiskCategory,
                                   r.RiskType,
                                   r.RiskItemCategory,
                                   r.RiskItemSubCategory,
                                   r.RiskToItem,
                                   r.CloseDate,
                                   r.RiskTo,
                                   r.Impact,
                                   r.LikliHood,
                                   r.RiskRating,
                                   r.ResidualImpact,
                                   r.ResidualLikeliHood,
                                   r.ResidualRiskRating,
                                   r.RiskOwner,
                                   r.NextReviewDate,
                                   r.OrgName,
                                   r.UnitName,
                                   r.DeptName,
                                   r.SubDeptName,
                                   r.RiskName,
                                   r.ProcessCode,
                                   strApprovalStatus = GetApprovalStatus(Convert.ToInt32(r.Status)),
                                   strRiskCloseStatus = GetRiskCloseStatus(Convert.ToString(r.RiskCloseStatus)),
                                   r.RiskOwnerName,
                                   r.IncidentTypeName,
                                   r.RiskTypeName,
                                   strProbability = GetProbabilityWeightage(Convert.ToInt32(r.LikliHood)),
                                   r.RiskEntryDate,
                                   strLastReviewDate = GetDate(Convert.ToString(r.LastReviewDate)),
                                   r.ImpactName
                               });
                return Ok(objRiskManagement);
                #endregion
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
        }

        public static string GetApprovalStatus(int strApprovalID)
        {
            string? strStatus = string.Empty;

            if (Convert.ToInt32(strApprovalID).Equals((int)BCPEnum.ApprovalType.Initiated))
            {
                strStatus = BCPEnum.ApprovalType.Initiated.ToString();
            }
            else if (Convert.ToInt32(strApprovalID).Equals(((int)BCPEnum.ApprovalType.WaitingforApproval).ToString()))
            {
                strStatus = BCPEnum.ApprovalType.WaitingforApproval.ToString();
            }
            else if (Convert.ToInt32(strApprovalID).Equals(((int)BCPEnum.ApprovalType.Approved).ToString()))
            {
                strStatus = BCPEnum.ApprovalType.Approved.ToString();
            }
            else if (Convert.ToInt32(strApprovalID).Equals(((int)BCPEnum.ApprovalType.Disapproved).ToString()))
            {
                strStatus = BCPEnum.ApprovalType.Disapproved.ToString();
            }

            return strStatus;
        }

        public static string GetRiskCloseStatus(string strRiskStatus)
        {
            string strText = string.Empty;
            if (strRiskStatus != "" || strRiskStatus != null)
            {
                string status = strRiskStatus;

                if (status == "0")
                {
                    strText = "Open";
                }
                else if (status == "1")
                {
                    strText = "Monitor";
                }
                else if (status == "2")
                {
                    strText = "Close";
                }
                else
                {
                    strText = "";
                }
            }
            else
            {
                strText = "";
            }

            return strText;
        }

        private string GetProbabilityWeightage(int iId)
        {
            string strId = string.Empty;
            int DefaultProfileID = _Utilities.RiskProfileID;
            RiskImpactMaster objriskimpactmaster = _ProcessSrv.RiskProbabilitymaster_GetByID(DefaultProfileID, iId);
            if (objriskimpactmaster.ProbabilityName != null)
            {
                if (iId >= 0)
                {
                    strId = Convert.ToString(objriskimpactmaster.ProbabilityName) != null ? objriskimpactmaster.ProbabilityName : "NA";
                }
            }

            return strId;
        }

        private string GetDate(string strDate)
        {
            string strGetDate = strDate;

            string strDateNew = string.Empty;

            if (strDate != "" || strDate != null)
            {
                if (strDate == "1/1/0001 12:00:00 AM")
                {
                    strDateNew = "N/A";
                }
                else
                {
                    strDateNew = strDate;
                }
            }

            return strDateNew;
        }
        #endregion ManageRisk

        #endregion

        #region notify incident / latest timeline
        [HttpGet]
        [Route("GetAllLatestIncidentData")]
        public IActionResult GetAllLatestIncidentData(int OrgID)
        {
            List<IncidentManagement> lstIncidentManagement = _ProcessSrv.GetIncidentManagementList(OrgID);
            try
            {
                var lstIncidentManagementNew = lstIncidentManagement.Select(objIncidentManagement =>
                new
                {
                    objIncidentManagement.Id,
                    objIncidentManagement.DisasterName,
                    objIncidentManagement.EventName,
                    objIncidentManagement.NotifiedAs,
                    objIncidentManagement.DisasterTime,
                    objIncidentManagement.NotificationTime,
                    objIncidentManagement.EstimatedRecoveryTime,
                    objIncidentManagement.ActualRecoveryTime,
                    objIncidentManagement.NotifierName,
                    objIncidentManagement.Status,
                    objIncidentManagement.IsNotified
                });
                return Ok(lstIncidentManagementNew);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [Route("GetAllIncidentTimeLine")]
        public IActionResult GetAllIncidentTimeLine(int iIncidentID, int iDisasterId, int OrgID)
        {
            string? strDisasterName = string.Empty;
            string? strIncidentCode = string.Empty;
            string? strEventName = string.Empty;
            string? strNotificationTime = string.Empty;
            string? strNotifierName = string.Empty;
            string? strNotifierEmail = string.Empty;
            string? strNotifierMobile = string.Empty;
            string? strNotifiedAs = string.Empty;
            string strIncidentStatus = string.Empty;

            string EscalationStatus = string.Empty;
            IncidentManagement objIncidentManagement = new IncidentManagement();
            List<RecoveryTaskStepInfo> lstIncidentColl = new List<RecoveryTaskStepInfo>();
            try
            {
                objIncidentManagement = _ProcessSrv.GetIncidentManagementGetByIncidentID(Convert.ToInt32(iIncidentID));

                List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfoNew = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(Convert.ToInt32(iIncidentID), 0);
                if (objIncidentManagement != null)
                {
                    ResourcesInfo objResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt16(objIncidentManagement.NotifiedBy));

                    List<EscalationMatrix> lstEscalationMatrix = _ProcessSrv.EscalationMatrixMapping_GetByOrgID(OrgID);
                    List<EscalationLevelConfig> lstEscalationLevelConfig = _ProcessSrv.EscalationLevelConfigResult_GetAll();

                    int iEscStatus = 0;
                    int iMatMapID = 0;
                    int iMatID = 0;
                    int icount = 0;

                    if (lstRecoveryTaskStepInfoNew != null)
                    {
                        foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in lstRecoveryTaskStepInfoNew)
                        {
                            if (lstEscalationLevelConfig != null)
                            {
                                var EscStatus_StepWiseForProfile = from EscalationLevelConfig EscStepStatus_profile in lstEscalationLevelConfig
                                                                   where Convert.ToInt32(EscStepStatus_profile.IncidentStepID) == Convert.ToInt32(objRecoveryTaskStepInfo.IncidentStepID)
                                                                   select EscStepStatus_profile;

                                if (EscStatus_StepWiseForProfile.Any())
                                {
                                    List<EscalationLevelConfig> obj1 = EscStatus_StepWiseForProfile.ToList();
                                    foreach (EscalationLevelConfig item in obj1)
                                    {
                                        icount++;
                                        iEscStatus = Convert.ToInt32(item.EscLevStatus) > 0 ? Convert.ToInt32(item.EscLevStatus) : 0;
                                        iMatMapID = Convert.ToInt32(item.EscMatMapID) > 0 ? Convert.ToInt32(item.EscMatMapID) : 0;
                                        iMatID = Convert.ToInt32(item.EscMatID) > 0 ? Convert.ToInt32(item.EscMatID) : 0;
                                    }
                                }
                                if (icount > 0)
                                {
                                    if (iEscStatus.Equals((int)BCPEnum.EscalationMatrixStatus.Escalated))
                                    {
                                        EscalationStatus = "Yes";
                                    }
                                    else
                                    {
                                        EscalationStatus = "No";
                                    }
                                }
                                else
                                {
                                    EscalationStatus = "No";
                                }
                            }
                        }
                    }

                    int EscStatusProfile = 0;
                    int MatMapIDProfile = 0;
                    int MatIDProfile = 0;

                    if (lstEscalationMatrix != null)
                    {
                        var EscMatStatus_ProfileLevel = from EscalationMatrix esc11 in lstEscalationMatrix
                                                        where Convert.ToInt32(esc11.BCMEntityID) == Convert.ToInt32(iDisasterId)
                                                        &&
                                                        Convert.ToInt32(esc11.EscMatType) == (int)BCPEnum.EscalationMatrixType.TaskAssignment
                                                        &&
                                                        Convert.ToInt32(esc11.BCMEntityType) == (int)BCPEnum.EntityType.RiskRecoveryProfile
                                                        select esc11;

                        if (EscMatStatus_ProfileLevel.Any())
                        {
                            List<EscalationMatrix> obj1 = EscMatStatus_ProfileLevel.ToList();
                            foreach (EscalationMatrix item in obj1)
                            {
                                EscStatusProfile = Convert.ToInt32(item.EscMatStatus) > 0 ? Convert.ToInt32(item.EscMatStatus) : 0;
                                MatMapIDProfile = Convert.ToInt32(item.EscMapID) > 0 ? Convert.ToInt32(item.EscMapID) : 0;
                                MatIDProfile = Convert.ToInt32(item.EscMatID) > 0 ? Convert.ToInt32(item.EscMatID) : 0;
                            }
                        }
                    }

                    strDisasterName = objIncidentManagement.DisasterName;
                    strIncidentCode = objIncidentManagement.IncidentCode;
                    strEventName = objIncidentManagement.EventName;
                    strNotificationTime = objIncidentManagement.NotificationTime;
                    strNotifierName = objIncidentManagement.NotifierName;
                    strNotifierEmail = objResource.CompanyEmail;
                    strNotifierMobile = objResource.MobilePhone;
                    strNotifiedAs = "";
                    if (objIncidentManagement.NotifiedAs != "" && objIncidentManagement.NotifiedAs != null)
                    {
                        strNotifiedAs = GetNotificationType(objIncidentManagement.NotifiedAs.ToString());
                    }

                    if (objIncidentManagement.Status != null)
                    {
                        strIncidentStatus = GetIncidentStatus(objIncidentManagement.Status.ToString());
                    }
                    else
                    {
                        strIncidentStatus = string.Empty;
                    }
                }

                lstIncidentColl = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(Convert.ToInt32(iIncidentID), 0);
                string strSuccessStep = string.Empty;
                string strFailedStep = string.Empty;
                string strSuccessStepID = string.Empty;

                if (lstIncidentColl != null && objIncidentManagement != null)
                {
                    foreach (RecoveryTaskStepInfo objStep in lstIncidentColl)
                    {
                        string strSuccessStepIDNew = objStep.SuccessStepID == null ? string.Empty : objStep.SuccessStepID;
                        string FailureStepID = objStep.FailureStepID == null ? string.Empty : objStep.FailureStepID;

                        strSuccessStep = string.IsNullOrEmpty(GetIncidentStepIDBySequence(strSuccessStepIDNew, lstIncidentColl)) ? "NA" : GetIncidentStepIDBySequence(strSuccessStepIDNew, lstIncidentColl);
                        strFailedStep = GetIncidentStepIDBySequence(FailureStepID, lstIncidentColl);
                        strSuccessStepID = string.IsNullOrEmpty(GetIncidentStepIDBySeq(lstIncidentColl, strSuccessStepIDNew)) ? "0" : GetIncidentStepIDBySeq(lstIncidentColl, strSuccessStepIDNew);

                        string strCompletionTime = string.Empty;
                        string CompletionTime = objStep.CompletionTime != null ? objStep.CompletionTime : string.Empty;
                        if (CompletionTime.Contains("0001"))
                        {
                            strCompletionTime = " ";
                        }
                        else
                        {
                            strCompletionTime = CompletionTime;
                        }

                        string strDependentOn = string.Empty;
                        string strDependent = objStep.DependentOn != null ? objStep.DependentOn : string.Empty;
                        if (strDependent.Equals("0"))
                        {
                            strDependentOn = "NO";
                        }
                        else
                        {
                            strDependentOn = strDependent;

                            foreach (RecoveryTaskStepInfo objStep1 in lstIncidentColl)
                            {
                                if (objStep1.StepID == strDependent)
                                {
                                    strDependent = objStep1.StepName == null ? string.Empty : objStep1.StepName;
                                }
                            }
                        }

                        string strCPProfileName = string.Empty;
                        string strStepId = string.Empty;
                        string strParallelDROperationId = string.Empty;
                        string strDRProfileStatus = string.Empty;

                        if (objStep.CPProfileID != "0")
                        {
                            parallel_profile objparallelprof = _ProcessSrv.GetParallellProfilesByID(Convert.ToInt32(objStep.CPProfileID));
                            strCPProfileName = objparallelprof.ProfileName == null ? "NA" : objparallelprof.ProfileName;
                            strStepId = objStep.StepID == null ? string.Empty : objStep.StepID;
                            strParallelDROperationId = objStep.CPParallelDROperationId == null ? string.Empty : objStep.CPParallelDROperationId;
                            if (strParallelDROperationId != "0")
                            {
                                parallel_profile DRparallelprof = _ProcessSrv.GetDRProfileStatusByDR_ID(Convert.ToInt32(strParallelDROperationId));
                                strDRProfileStatus = DRparallelprof.Status == null ? string.Empty : DRparallelprof.Status;
                            }
                            else
                            {
                                strDRProfileStatus = "NA";
                            }

                        }
                        else
                        {
                            strCPProfileName = "NA";
                            strDRProfileStatus = "NA";
                        }


                        string? strNotificationTimeNew = string.Empty;
                        if (strNotificationTime != null)
                        {
                            strNotificationTimeNew = strNotificationTime.Remove(0, 10);
                        }


                        var dtList = lstIncidentColl.Select(r => new
                        {
                            objStep.IncidentID,
                            objStep.IncidentName,
                            strIncidentStatus,
                            objStep.TaskName,
                            objStep.StepName,
                            strDisasterName,
                            strIncidentCode,
                            strEventName,
                            strNotificationTime,
                            strNotifierName,
                            strNotifierEmail,
                            strNotifierMobile,
                            strNotifiedAs,
                            objStep.StepDescription,
                            objStep.NotificationSentTime,
                            CompletionTime,
                            objStep.StepOwnerName,
                            objStep.StepOwnerMobileNo,
                            objStep.StepOwnerEmail,
                            objStep.AltStepOwnerName,
                            objStep.AltStepOwnerMobileNo,
                            objStep.AltStepOwnerEmail,
                            objStep.StepStatus,
                            objStep.EstTime,
                            objStep.TimeUnit,
                            objIncidentManagement.Status,
                            objStep.ExecutedBy,
                            objStep.UpdatedBy,
                            strSuccessStep,
                            strFailedStep,
                            strDependent,
                            objStep.PlanName,
                            objStep.MobileVerifiedTooltip,
                            objStep.EmailVerifiedTooltip,
                            objStep.MobileVerifiedTooltip_Alt,
                            objStep.EmailVerifiedTooltip_Alt,
                            objStep.ReassignedTo,
                            objStep.ReassignedOwnerName,
                            objStep.ReassignedOwnerEmail,
                            objStep.ReassignedOwnerMobileNo,
                            objStep.MobileVerifiedTooltip_Reassigned,
                            objStep.EmailVerifiedTooltip_Reassigned,
                            objStep.OwnerReceivedEmailStatus,
                            objStep.AltOwnerReceivedEmailStatus,
                            objStep.OwnerReceivedSMSStatus,
                            objStep.AltOwnerReceivedSMSStatus,
                            strSuccessStepID,
                            objStep.ReassignedOwnerReceivedEmailStatus,
                            objStep.ReassignedOwnerReceivedSMSStatus,
                            objStep.CPProfileID,
                            strCPProfileName,
                            strDRProfileStatus
                        });

                        return Ok(dtList);
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok();
        }
        private string GetNotificationType(string? strNotifiedAs)
        {
            string strStatus = "--";

            try
            {
                if (string.IsNullOrEmpty(strNotifiedAs).ToString().Equals(((int)BCPEnum.IncidentNotfnType.Live).ToString()))
                {
                    strStatus = BCPEnum.IncidentNotfnType.Live.ToString();
                }
                else if (string.IsNullOrEmpty(strNotifiedAs).ToString().Equals(((int)BCPEnum.IncidentNotfnType.Drill).ToString()))
                {
                    strStatus = BCPEnum.IncidentNotfnType.Drill.ToString();
                }
                else if (string.IsNullOrEmpty(strNotifiedAs).ToString().Equals(((int)BCPEnum.IncidentNotfnType.TableTop).ToString()))
                {
                    strStatus = BCPEnum.IncidentNotfnType.TableTop.ToString();
                    if (strStatus == "TableTop")
                    {
                        strStatus = "Table-Top";
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return strStatus;
        }
        private string GetIncidentStatus(string? strStatusID)
        {
            string strStatus = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(strStatusID))
                    return strStatus;

                if (strStatusID.Equals(((int)BCPEnum.IncidentStatus.InActive).ToString()))
                {
                    strStatus = BCPEnum.IncidentStatus.InActive.ToString();
                }
                if (strStatusID.Equals(((int)BCPEnum.IncidentStatus.InProgress).ToString()))
                {
                    strStatus = BCPEnum.IncidentStatus.InProgress.ToString();
                }
                if (strStatusID.Equals(((int)BCPEnum.IncidentStatus.Completed).ToString()))
                {
                    strStatus = BCPEnum.IncidentStatus.Completed.ToString();
                }
                if (strStatusID.Equals(((int)BCPEnum.IncidentStatus.InComplete).ToString()))
                {
                    strStatus = BCPEnum.IncidentStatus.InComplete.ToString();
                }
                if (strStatusID.Equals(((int)BCPEnum.IncidentStatus.TaskAssigned).ToString()))
                {
                    strStatus = BCPEnum.IncidentStatus.TaskAssigned.ToString();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return strStatus;
        }
        private string GetIncidentStepIDBySequence(string seq, List<RecoveryTaskStepInfo> lstIncidentColl)
        {
            string strRecoveryTaskStepInfo = string.Empty;
            try
            {
                foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in lstIncidentColl)
                {
                    if (seq.Equals(objRecoveryTaskStepInfo.Sequence))
                    {
                        strRecoveryTaskStepInfo = objRecoveryTaskStepInfo.StepName + "(" + objRecoveryTaskStepInfo.IncidentStepID + ") ";
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return strRecoveryTaskStepInfo;
        }
        private string GetIncidentStepIDBySeq(List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfo, string strSequence)
        {
            string strRecoveryTaskStepInfo = string.Empty;
            foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in lstRecoveryTaskStepInfo)
            {
                if (strSequence.Equals(objRecoveryTaskStepInfo.Sequence))
                {
                    string strIncidentStepID = objRecoveryTaskStepInfo.IncidentStepID == null ? string.Empty : objRecoveryTaskStepInfo.IncidentStepID;
                    strRecoveryTaskStepInfo = strIncidentStepID;
                    break;
                }
            }
            return strRecoveryTaskStepInfo;
        }

        [HttpGet]
        [Route("NotifyToUsers")]
        public IActionResult NotifyToUsers(int iIncidentID, int OrgID)
        {
            List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfo = new List<RecoveryTaskStepInfo>();
            try
            {
                lstRecoveryTaskStepInfo = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(Convert.ToInt32(iIncidentID), OrgID);

                string? strCompletedStepName = string.Empty;
                string? strInProgressStepName = string.Empty;
                string? strStepName = string.Empty;

                string? strExpectedTime = string.Empty;
                string? strStepOwner = string.Empty;

                for (int i = 0; i < lstRecoveryTaskStepInfo.Count - 1; i++)
                {
                    RecoveryTaskStepInfo objRecoveryTaskStepInfo = lstRecoveryTaskStepInfo[i];

                    if (objRecoveryTaskStepInfo.StepStatus == Convert.ToInt32((BCPEnum.StepStatus.InProgress)).ToString())
                    {
                        strInProgressStepName = objRecoveryTaskStepInfo.StepName;
                        if (i > 0)
                        {
                            RecoveryTaskStepInfo objRecoveryTaskStepInfoPrevious = lstRecoveryTaskStepInfo[i - 1];
                            if (string.IsNullOrEmpty(objRecoveryTaskStepInfoPrevious.StepStatus).Equals(((int)BCPEnum.StepStatus.Completed).ToString()))
                            {
                                strCompletedStepName = objRecoveryTaskStepInfoPrevious.StepName;
                            }
                        }
                        break;
                    }
                }
                NotifyRemainingUsers_Tehread(lstRecoveryTaskStepInfo, strCompletedStepName, Convert.ToString(OrgID));
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
            return Ok(lstRecoveryTaskStepInfo);
        }
        private void NotifyRemainingUsers_Tehread(List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfo, string? strCompletedStepName, string strOrgID)
        {
            try
            {
                Thread NotifyThread = new Thread(delegate ()
                {
                    SendIncidentStatusToUsers(lstRecoveryTaskStepInfo, strCompletedStepName, Convert.ToString(strOrgID));
                });

                NotifyThread.Start();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                BadRequest(ex.Message);
            }
        }
        private void SendIncidentStatusToUsers(List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfo, string? strCompletedStepName, string strOrgID)
        {
            try
            {
                foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in lstRecoveryTaskStepInfo)
                {
                    string strMailBody1 = "Step " + strCompletedStepName + " has been completed.";

                    string strMailBody2 = "Please find the details for the INCIDENT";
                    string strMailBody3 = string.Empty;
                    string strMailBody5 = string.Empty;

                    string strMailBody6 = CreateIncidentEmailBody_ByPlan(Convert.ToInt32(objRecoveryTaskStepInfo.IncidentID), Convert.ToString(strOrgID));

                    DateTime dtNow = DateTime.Now;

                    string? strTimeUnit = string.Empty;
                    string? strMailBody4 = string.Empty;
                    string? strMailBody = string.Empty;
                    string? strMailBodyText = string.Empty;
                    string? strAltMailBody = string.Empty;
                    string? strReassignedMailBody = string.Empty;
                    bool bSuccess = false;

                    if (string.IsNullOrEmpty(strCompletedStepName))
                    {
                        strMailBodyText = strMailBody2 + strMailBody3 + strMailBody4 + " at " + dtNow.ToString();
                    }
                    else
                    {
                        strMailBodyText = strMailBody1 + strMailBody2 + strMailBody3 + strMailBody4 + " at " + dtNow.ToString();
                    }

                    if (!string.IsNullOrEmpty(objRecoveryTaskStepInfo.EmailTemplateText))
                    {
                        strMailBody = "Dear " + objRecoveryTaskStepInfo.StepOwnerName + ",<br /><br /> " + strMailBodyText + "<br /><br> " + strMailBody6 + "<br/>  <br /> <br /> <br /> <b> Remarks : </b> <br/> " + objRecoveryTaskStepInfo.EmailTemplateText + "<br/><br /> <br /> <br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                        strAltMailBody = "Dear " + objRecoveryTaskStepInfo.AltStepOwnerName + ",<br /><br /> " + strMailBodyText + strMailBody5 + "<br /><br> " + strMailBody6 + "<br/> <br /> <br /> <br /> <b> Remarks : </b> <br/>" + objRecoveryTaskStepInfo.EmailTemplateText + "<br/> <br /> <br /> <br /> Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                        strReassignedMailBody = "Dear " + objRecoveryTaskStepInfo.ReassignedOwnerName + ",<br /><br /> " + strMailBodyText + strMailBody5 + "<br /><br> " + strMailBody6 + "<br/> <br /> <br /> <br /> <b> Remarks : </b> <br/>" + objRecoveryTaskStepInfo.EmailTemplateText + "<br/> <br /> <br /> <br /> Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                    }
                    else
                    {
                        strMailBody = "Dear " + objRecoveryTaskStepInfo.StepOwnerName + ",<br /><br /> " + strMailBodyText + "<br /><br> " + strMailBody6 + "<br/>Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                        strAltMailBody = "Dear " + objRecoveryTaskStepInfo.AltStepOwnerName + ",<br /><br /> " + strMailBodyText + strMailBody5 + "<br /><br> " + strMailBody6 + "<br/>Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                        strReassignedMailBody = "Dear " + objRecoveryTaskStepInfo.ReassignedOwnerName + ",<br /><br /> " + strMailBodyText + strMailBody5 + "<br /><br> " + strMailBody6 + "<br/>Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                    }

                    string strSubject = objRecoveryTaskStepInfo.IncidentCode + "-FYI-Incident Status-" + objRecoveryTaskStepInfo.IncidentName;
                    string strMailCC = GetAllTeamIncidentResources(Convert.ToInt32(objRecoveryTaskStepInfo.IncidentID));
                    string strMailTo = string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepOwnerEmail).ToString();
                    string strMailBCC = string.Empty;
                    string strAttachment = string.Empty;

                    bSuccess = _BCMMail.SendMail(strSubject, strMailBody, strMailTo, strMailCC, strMailBCC, strAttachment, strOrgID);
                    AddIncidentNotification(string.IsNullOrEmpty(objRecoveryTaskStepInfo.IncidentID).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.IncidentStepID).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepID).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.ChangedBy).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepOwnerID).ToString(),
                    strSubject, strMailBody, Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (bSuccess) ? "1" : "0", "0", string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString());

                    string? strAltMailTo = string.IsNullOrEmpty(objRecoveryTaskStepInfo.AltStepOwnerEmail).ToString();

                    bSuccess = _BCMMail.SendMail(strSubject, strAltMailBody, strAltMailTo, strMailCC, strMailBCC, strAttachment, strOrgID);
                    AddIncidentNotification(string.IsNullOrEmpty(objRecoveryTaskStepInfo.IncidentID).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.IncidentStepID).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepID).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.ChangedBy).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.AltStepOwnerID).ToString(),
                    strSubject, strAltMailBody, Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (bSuccess) ? "1" : "0", "0", string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString());

                    if (!string.IsNullOrEmpty(objRecoveryTaskStepInfo.ReassignedTo).Equals("0"))
                    {
                        string? strReassignedMailTo = string.IsNullOrEmpty(objRecoveryTaskStepInfo.ReassignedOwnerEmail).ToString();

                        bSuccess = _BCMMail.SendMail(strSubject, strReassignedMailBody, strReassignedMailTo, strMailCC, strMailBCC, strAttachment, strOrgID);
                        AddIncidentNotification(string.IsNullOrEmpty(objRecoveryTaskStepInfo.IncidentID).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.IncidentStepID).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepID).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.ChangedBy).ToString(), string.IsNullOrEmpty(objRecoveryTaskStepInfo.ReassignedTo).ToString(),
                        strSubject, strReassignedMailBody, Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (bSuccess) ? "1" : "0", "0", string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
        }
        private string CreateIncidentEmailBody_ByPlan(int iIncidentId, string iID)
        {
            StringBuilder objStringBuilder = new StringBuilder();
            try
            {
                IncidentManagement objIncidentManagement = _ProcessSrv.GetIncidentManagementGetByIncidentID(iIncidentId);
                List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfo = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(iIncidentId, 0);
                DateTime dtNow = DateTime.Now;

                if (lstRecoveryTaskStepInfo != null && objIncidentManagement != null)
                {
                    objStringBuilder.Append("<html xmlns=#unknown>");
                    objStringBuilder.Append("<head><title>Continuity Vault Email Notification</title></head>");
                    objStringBuilder.Append("<body>");
                    objStringBuilder.Append("<table runat='server' style='margin-bottom: 0; width: 100%; border: 1px solid #DDDDDD; left: 0; padding: 0; position: relative; top: 0; background-color: rgba(0, 0, 0, 0); border-collapse: collapse; border-spacing: 0; max-width: 100%;'  class='table'>");
                    objStringBuilder.Append("<thead>");
                    objStringBuilder.Append("<tr ><th colspan='11' style='border: 1px solid #DDDDDD; color: #005580; font-family: Arial; border-bottom: 1px solid #DDDDDD; font-size:16px; font-weight:bold; text-align:left; line-height: 20px; padding-top: 8px; padding-bottom: 8px; padding-left: 5px; background: none repeat scroll 0 0 #F1F1F1;  '>Incident Progress Status</th></tr>");
                    objStringBuilder.Append("<tr>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle; font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Step Name</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Step Description</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Status</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Previous Step(s)</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Next Step(s)</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Estimated Time to Complete</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Step Assign Time</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Actual Completion Time</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Step Owner</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Alt.Step Owner</th>");
                    objStringBuilder.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Updated By</th>");
                    objStringBuilder.Append("</thead>");


                    foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in lstRecoveryTaskStepInfo)
                    {
                        if ((iID != null))
                        {
                            objStringBuilder.Append("<tr>");

                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objRecoveryTaskStepInfo.StepName + " (" + objRecoveryTaskStepInfo.IncidentStepID + ") " + "</td>"); //step Name

                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objRecoveryTaskStepInfo.StepDescription + "</td>"); // Step Description

                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>");  // Status

                            if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) == (int)BCPEnum.StepStatus.Acknowledged)
                            {
                                objStringBuilder.Append("<div style='color: #41546f; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: yellow;'>" + GetStatus(string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString()) + "</div>");
                            }
                            if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) == (int)BCPEnum.StepStatus.NotInitiated)
                            {
                                objStringBuilder.Append("<div style=' color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #3A87AD;'>" + GetStatus(string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString()) + "</div>");
                            }
                            if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) == (int)BCPEnum.StepStatus.InProgress)
                            {
                                objStringBuilder.Append("<div style='color: #41546f; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #00e600;'>" + GetStatus(string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString()) + "</div>");
                            }
                            if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) == (int)BCPEnum.StepStatus.Completed)
                            {
                                objStringBuilder.Append("<div style='color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #004d00;'>" + GetStatus(string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString()) + "</div>");
                            }
                            if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) == (int)BCPEnum.StepStatus.Failed)
                            {
                                objStringBuilder.Append("<div style='color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #004d00;'>" + GetStatus(string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString()) + "</div>");
                            }

                            if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) == (int)BCPEnum.StepStatus.TaskAssigned)
                            {
                                objStringBuilder.Append("<div style='color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #ff8000;'>" + GetStatus(string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString()) + "</div>");
                            }

                            if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) == (int)BCPEnum.StepStatus.ReInitiated)
                            {
                                objStringBuilder.Append("<div style='color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #33adff;'>" + GetStatus(string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepStatus).ToString()) + "</div>");
                            }

                            objStringBuilder.Append("</td>");  // Status

                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>");  // Previous steps

                            objStringBuilder.Append(CheckForCurrentSteps_DependentOnSteps(string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepID).ToString(), lstRecoveryTaskStepInfo));

                            objStringBuilder.Append("</td>");   // Previous steps

                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>");  // Next steps


                            if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) == (int)BCPEnum.StepStatus.Completed)
                            {
                                objStringBuilder.Append(getDependentStepSteps_OnCuerrentStep(string.IsNullOrEmpty(objRecoveryTaskStepInfo.StepID).ToString(), lstRecoveryTaskStepInfo));
                            }
                            else if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) == (int)BCPEnum.StepStatus.Failed)
                            {
                                objStringBuilder.Append(GetIncidentStepIDBySequence(string.IsNullOrEmpty(objRecoveryTaskStepInfo.FailureStepID).ToString(), lstRecoveryTaskStepInfo));
                            }
                            else
                            {
                                objStringBuilder.Append("NA");
                            }

                            objStringBuilder.Append("</td>");   // Next steps

                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objRecoveryTaskStepInfo.EstTime + " " + GetTimeValue(string.IsNullOrEmpty(objRecoveryTaskStepInfo.TimeUnit)) + "</td>"); // Estimated Time and Unit

                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objRecoveryTaskStepInfo.NotificationSentTime + "</td>"); // Step Assigned time

                            string? DateTimeFormat = _configuration.GetValue<string>("ConfigurationValue:DateTimeFormat");
                            string CompletionTime = string.IsNullOrEmpty(objRecoveryTaskStepInfo.CompletionTime) == false ? objRecoveryTaskStepInfo.CompletionTime != DateTime.MinValue.ToString(DateTimeFormat) ? objRecoveryTaskStepInfo.CompletionTime : "NA" : "NA";

                            if (Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) != ((int)BCPEnum.StepStatus.Completed) && Convert.ToInt32(objRecoveryTaskStepInfo.StepStatus) != ((int)BCPEnum.StepStatus.Failed))
                            {
                                CompletionTime = "NA";
                            }

                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + CompletionTime + "</td>");  // Completion Time    //strTime + "<br />" +
                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objRecoveryTaskStepInfo.StepOwnerName + "<br/>" + objRecoveryTaskStepInfo.StepOwnerMobileNo + "<br/>" + objRecoveryTaskStepInfo.StepOwnerEmail + "</td>");
                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objRecoveryTaskStepInfo.AltStepOwnerName + "<br/>" + objRecoveryTaskStepInfo.AltStepOwnerMobileNo + "<br/>" + objRecoveryTaskStepInfo.AltStepOwnerEmail + "</td>");
                            objStringBuilder.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + GetUpdatedStatus(string.IsNullOrEmpty(objRecoveryTaskStepInfo.UpdatedBy)) + "</td>");
                            objStringBuilder.Append("</tr>");
                        }
                    }
                    objStringBuilder.Append("</table>");
                    objStringBuilder.Append("</body>");
                    objStringBuilder.Append("</html>");
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }

            return objStringBuilder.ToString();
        }
        private string CheckForCurrentSteps_DependentOnSteps(string iStepID, List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfo)
        {
            string strDepedentSteps = "";

            try
            {
                List<RecoveryStepsDependentMapping> lstRecoveryStepsDependentMapping = new List<RecoveryStepsDependentMapping>();

                List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfoNew = new List<RecoveryTaskStepInfo>();

                lstRecoveryStepsDependentMapping = _ProcessSrv.RecoveryStepsDependentMapping_ByStepID(iStepID);

                if (lstRecoveryTaskStepInfo != null && lstRecoveryStepsDependentMapping != null && lstRecoveryTaskStepInfo.Count > 0 && lstRecoveryStepsDependentMapping.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in lstRecoveryTaskStepInfo)
                    {
                        foreach (RecoveryStepsDependentMapping objRecoveryStepsDependentMapping in lstRecoveryStepsDependentMapping)
                        {
                            if (objRecoveryStepsDependentMapping.DependentStepID == objRecoveryTaskStepInfo.StepID)
                            {
                                lstRecoveryTaskStepInfoNew.Add(objRecoveryTaskStepInfo);
                            }
                        }
                    }
                }

                if (lstRecoveryTaskStepInfoNew != null && lstRecoveryTaskStepInfoNew.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in lstRecoveryTaskStepInfoNew)
                    {
                        strDepedentSteps += objRecoveryTaskStepInfo.StepName + " (" + objRecoveryTaskStepInfo.IncidentStepID + ") ,";
                    }
                }

                if (string.IsNullOrEmpty(strDepedentSteps))
                {
                    strDepedentSteps = "NA";
                }
                else
                {
                    strDepedentSteps = strDepedentSteps.TrimEnd(',');
                }

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }

            return strDepedentSteps;

        }
        private string getDependentStepSteps_OnCuerrentStep(string istepID, List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfo)
        {
            string strDepedentSteps = string.Empty;

            try
            {
                List<RecoveryStepsDependentMapping> lstRecoveryStepsDependentMapping = new List<RecoveryStepsDependentMapping>();

                List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfoNew = new List<RecoveryTaskStepInfo>();

                lstRecoveryStepsDependentMapping = _ProcessSrv.RecoveryStepsDependentMapping_ByDependentStepID(istepID);

                if (lstRecoveryTaskStepInfo != null && lstRecoveryStepsDependentMapping != null && lstRecoveryTaskStepInfo.Count > 0 && lstRecoveryStepsDependentMapping.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in lstRecoveryTaskStepInfo)
                    {
                        foreach (RecoveryStepsDependentMapping objRecoveryStepsDependentMapping in lstRecoveryStepsDependentMapping)
                        {
                            if (objRecoveryStepsDependentMapping.StepID == objRecoveryTaskStepInfo.StepID)
                            {
                                lstRecoveryTaskStepInfoNew.Add(objRecoveryTaskStepInfo);
                            }
                        }
                    }
                }

                if (lstRecoveryTaskStepInfoNew != null && lstRecoveryTaskStepInfoNew.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in lstRecoveryTaskStepInfoNew)
                    {
                        strDepedentSteps += objRecoveryTaskStepInfo.StepName + " (" + objRecoveryTaskStepInfo.IncidentStepID + ") ,";
                    }
                }

                if (string.IsNullOrEmpty(strDepedentSteps))
                {
                    strDepedentSteps = "NA";
                }
                else
                {
                    strDepedentSteps = strDepedentSteps.TrimEnd(',');
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }

            return strDepedentSteps;
        }
        private string GetAllTeamIncidentResources(int iIncidentId)
        {
            string strResources = string.Empty;
            try
            {
                List<IncidentNotificationTeam> lstIncidentNotificationTeam = _ProcessSrv.GetBCMGroupMemberResourcesListByIncidentId(iIncidentId);
                foreach (IncidentNotificationTeam objIncTeam in lstIncidentNotificationTeam)
                {
                    strResources = string.IsNullOrEmpty(objIncTeam.ResourceMail).ToString();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return strResources;
        }
        private int AddIncidentNotification(string strIncidentID, string strIncidentStepID, string strStepID, string strNotifiedBy, string strNotifiedTo, string strSubject, string strBody, string strCommunicationID, string strTeamID, string strStatus, string strInBound, string StepExecutionStatus)
        {
            int iSuccess = 0;
            try
            {
                IncidentNotificationHistory objIncidentNotificationHistory = new IncidentNotificationHistory();
                objIncidentNotificationHistory.IncidentID = Convert.ToInt32(strIncidentID);
                objIncidentNotificationHistory.IncidentStepID = Convert.ToInt32(strIncidentStepID);
                objIncidentNotificationHistory.StepID = Convert.ToInt32(strStepID);
                objIncidentNotificationHistory.CommunicationID = Convert.ToInt32(strCommunicationID);
                objIncidentNotificationHistory.NotifiedBy = Convert.ToInt32(strNotifiedBy);
                objIncidentNotificationHistory.Subject = strSubject;
                objIncidentNotificationHistory.Body = strBody;
                objIncidentNotificationHistory.TeamID = Convert.ToInt32(strTeamID);
                objIncidentNotificationHistory.NotifiedTo = strNotifiedTo;
                objIncidentNotificationHistory.Status = strStatus;
                objIncidentNotificationHistory.IsInbound = Convert.ToInt32(strInBound);
                objIncidentNotificationHistory.StepExecutionStatus = StepExecutionStatus;

                iSuccess = _ProcessSrv.IncidentNotificationHistorySave(objIncidentNotificationHistory);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return iSuccess;
        }
        private string GetStatus(object objStatus)
        {
            string strStatus = string.Empty;
            try
            {
                if (objStatus != null)
                {
                    if (string.IsNullOrEmpty(Convert.ToString(objStatus)).Equals(((int)BCPEnum.StepStatus.NotInitiated).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.NotInitiated.ToString();
                    }
                    if (string.IsNullOrEmpty(Convert.ToString(objStatus)).Equals(((int)BCPEnum.StepStatus.Acknowledged).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.Acknowledged.ToString();
                    }
                    if (string.IsNullOrEmpty(Convert.ToString(objStatus)).Equals(((int)BCPEnum.StepStatus.InProgress).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.InProgress.ToString();
                    }
                    if (string.IsNullOrEmpty(Convert.ToString(objStatus)).Equals(((int)BCPEnum.StepStatus.Completed).ToString()))
                    {   
                        strStatus = BCPEnum.StepStatus.Completed.ToString();
                    }
                    if (string.IsNullOrEmpty(Convert.ToString(objStatus)).Equals(((int)BCPEnum.StepStatus.Failed).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.Failed.ToString();
                    }
                    if (string.IsNullOrEmpty(Convert.ToString(objStatus)).Equals(((int)BCPEnum.StepStatus.TaskAssigned).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.TaskAssigned.ToString();
                    }
                    if (string.IsNullOrEmpty(Convert.ToString(objStatus)).Equals(((int)BCPEnum.StepStatus.ReInitiated).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.ReInitiated.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return strStatus;
        }
        private string GetTimeValue(object objTime)
        {
            string strName = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(Convert.ToString(objTime)).Equals(((int)BCPEnum.StepTimeUnit.Minute).ToString()))
                {
                    strName = BCPEnum.StepTimeUnit.Minute.ToString() + "(s)";
                }
                if (string.IsNullOrEmpty(Convert.ToString(objTime)).Equals(((int)BCPEnum.StepTimeUnit.Hour).ToString()))
                {
                    strName = BCPEnum.StepTimeUnit.Hour.ToString() + "(s)";
                }
                if (string.IsNullOrEmpty(Convert.ToString(objTime)).Equals(((int)BCPEnum.StepTimeUnit.Day).ToString()))
                {
                    strName = BCPEnum.StepTimeUnit.Day.ToString() + "(s)";
                }
                if (string.IsNullOrEmpty(Convert.ToString(objTime)).Equals(((int)BCPEnum.StepTimeUnit.Month).ToString()))
                {
                    strName = BCPEnum.StepTimeUnit.Month.ToString() + "(s)";
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return strName;
        }
        private string GetUpdatedStatus(object objUpdateStatus)
        {
            string strStatus = string.Empty;
            try
            {
                if (objUpdateStatus != null)
                {
                    if (string.IsNullOrEmpty(Convert.ToString(objUpdateStatus)).Equals(((int)BCPEnum.NotificationType.EMail).ToString()))
                    {
                        strStatus = BCPEnum.NotificationType.EMail.ToString();
                    }
                    if (string.IsNullOrEmpty(Convert.ToString(objUpdateStatus)).Equals(((int)BCPEnum.NotificationType.SMS).ToString()))
                    {
                        strStatus = BCPEnum.NotificationType.SMS.ToString();
                    }
                    if (string.IsNullOrEmpty(Convert.ToString(objUpdateStatus)).Equals(((int)BCPEnum.NotificationType.Call).ToString()))
                    {
                        strStatus = BCPEnum.NotificationType.Call.ToString();
                    }
                    if (string.IsNullOrEmpty(Convert.ToString(objUpdateStatus)).Equals(((int)BCPEnum.NotificationType.CVault).ToString()))
                    {
                        strStatus = BCPEnum.NotificationType.CVault.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return strStatus;
        }
        #endregion

        #region Manage Incident Management Records
        [HttpGet]
        [Route("GetIncidentManagementRecords")]
        public IActionResult GetIncidentManagementRecords(int OrgID)
        {
            string strIncidentStatus = string.Empty;
            string strNotifiedAs = string.Empty;
            List<IncidentManagement> lstIncidentManagement = new List<IncidentManagement>();
            try
            {
                lstIncidentManagement = _ProcessSrv.GetIncidentManagementList(OrgID);

                var IncidentTimeLineList = lstIncidentManagement.Select(objIncidentManagement => new
                {
                    objIncidentManagement.Id,
                    objIncidentManagement.IncidentCode,
                    objIncidentManagement.DisasterName,
                    objIncidentManagement.EventName,
                    objIncidentManagement.DisasterTime,
                    objIncidentManagement.NotificationTime,
                    objIncidentManagement.EstimatedRecoveryTime,
                    objIncidentManagement.ActualRecoveryTime,
                    objIncidentManagement.NotifierName,
                    strIncidentStatus = GetIncidentStatus(objIncidentManagement.Status),
                    objIncidentManagement.UnitName,
                    objIncidentManagement.CostExpenditure,
                    objIncidentManagement.OrgID,
                    objIncidentManagement.UnitID,
                    objIncidentManagement.OwnerEmail,
                    objIncidentManagement.ActivationModeEmail,
                    objIncidentManagement.EmailVerifiedTooltip,
                    objIncidentManagement.OwnersMobile,
                    objIncidentManagement.ActivationModeMobile,
                    objIncidentManagement.MobileVerifiedTooltip,
                    objIncidentManagement.OrgGroupID,
                    strNotifiedAs = GetNotificationType(objIncidentManagement.NotifiedAs)
                });
                return Ok(IncidentTimeLineList);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return BadRequest(ex.Message);
            }
        }
        #endregion

    }
}
