﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class ImpactTypeMasterController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public ImpactTypeMasterController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult ImpactTypeMaster()
    {
        List<ImpactType> lstImpact = new List<ImpactType>();

        try
        {
            PopulateDropdown();
            lstImpact = _ProcessSrv.GetImpactTypeList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View(lstImpact);
    }
    

    [HttpGet]
    public IActionResult AddImpactTypeMaster()
    {
        try
        {
            PopulateDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_AddImpactTypeMaster", new ImpactType());
    }

    [HttpPost]
    public IActionResult AddImpactTypeMaster(ImpactType objImpact)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.ImapctTypeSave(objImpact, _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ImpactTypeMaster");
    }


    [HttpGet]
    public IActionResult EditImpactTypeMaster(int iId)
    {
        var objImpact = new ImpactType();

        try
        {
            PopulateDropdown();
            objImpact = _ProcessSrv.GetImpactTypeByID(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }


        return PartialView("_EditImpactTypeMaster", objImpact);
    }

    [HttpPost]
    public IActionResult EditImpactTypeMaster(ImpactType objImpact)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.ImpactTypeUpdate(objImpact, _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ImpactTypeMaster");
    }


    [HttpGet]
    public IActionResult DeleteImpactTypeMaster(int iId)
    {
        var objImpact = new ImpactType();

        try
        {
            objImpact = _ProcessSrv.GetImpactTypeByID(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteImpactTypeMaster", objImpact);
    }

    [HttpPost]
    public IActionResult DeleteImpactTypeMaster(ImpactType objImpact)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.ImpactTypeDelete(objImpact.ImpactTypeID, _UserDetails.UserID.ToString());
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ImpactTypeMaster");
    }


    public void PopulateDropdown()
    {
        try
        {
            ViewBag.ImpactCategory = new SelectList(_Utilities.BindImpactType(), "ImpactTypeID", "ImpactTypeName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
}

