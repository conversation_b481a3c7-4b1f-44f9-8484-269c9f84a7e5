body {
    background-color: var(--bs-body-bg) !important;
    font-family: SF Pro Display;
}

.card {
    --bs-card-bg: var(--bs-white);
    --bs-card-cap-bg: var(--bs-white);
    border: none;
    border-radius: var(--bs-border-radius);
}


.LoginLeft-bg {
    background: linear-gradient(-45deg, #881048, #8c1557, #4e0a6c, #b82c3b);
    background-size: 400% 400%;
    animation: gradient 10s ease infinite;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.LoginRight-bg {
    background-color: #f0f2f5;
}

.bg-primary {
    background-color: #e63875 !important;
}

.btn-primary {
    background: rgb(230,56,117);
    background: linear-gradient(114deg, rgba(230,56,117,1) 4%, rgba(50,2,132,1) 100%);
    border: none;
}

    .btn-primary:hover {
        background: rgb(230,56,117);
        background: linear-gradient(242deg, rgba(230,56,117,1) 0%, rgba(50,2,132,1) 100%);
        border: none;
    }

/* Form Input & Select Style */

.input-group {
    border-bottom: var(--bs-border-width) solid var(--bs-border-color);
    background-color: transparent;
}

    .input-group .form-control {
        border: none;
        background-color: transparent;
    }

    .input-group .form-select {
        border: none;
        background-color: transparent;
    }

    .input-group .input-group-text {
        border: none;
        background-color: transparent;
    }

    .input-group:focus {
        color: var(--bs-body-color);
        background-color: transparent;
        border-bottom: 1px solid var(--bs-primary);
    }

    .input-group:focus-within {
        color: var(--bs-body-color);
        background-color:transparent;
        border-bottom: 1px solid var(--bs-primary);
    }

.input-group-text:focus-within {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-primary);
    border: none;
}

.input:-internal-autofill-selected{

}

.form-control {
    font-size: var(--bs-body-font-size) !important;
    color: var(--bs-body-color);
    background-color: transparent;
}

.form-select {
    font-size: var(--bs-body-font-size) !important;
    color: var(--bs-body-color);
    background-color: transparent;
}

.form-control:focus {
    color: var(--bs-body-color);
    background-color: transparent;
    border-color: transparent;
    outline: 0;
    box-shadow: none;
}

.form-select:focus {
    border-color: transparent;
    box-shadow: none;
}


/* Selectize Select */

.selectize-input.full {
    background-color: transparent;
}

.selectize-input {
    border: 0px solid #ced4da;
}

.selectize-input {
    min-height: calc(1.5em + 0.75rem + 0px);
}

    .selectize-input.focus {
        border-color: transparent;
        box-shadow: none;
    }

.selectize-input, .selectize-control.single .selectize-input.input-active {
    background: transparent;
}

.selectize-dropdown .selected {
    background-color: var(--bs-primary);
    color: var(--bs-white);
}

/* End Form Input & Select Style */

