﻿@using BCM.Shared
@inject BCM.Shared.Utilities _Utilities
@using BCM.BusinessClasses
@using BCM.BusinessFacadeSrv
@inject BCM.BusinessFacadeSrv.ProcessSrv _ProcessSrv
@using Newtonsoft.Json

@using Microsoft.AspNetCore.Http

@inject IHttpContextAccessor HttpContextAccessor
@{
    int RoleId;
    int UserId;
    string? UserRole;

    ManageUsersDetails? _UserDetails = _Utilities.LoginUserDetails();

    if (_UserDetails == null)
    {
        Context.Response.Redirect(Url.Action("Login", "Login", new { area = "" }));
        return;
    }
    var objRoleMaster = _ProcessSrv.GetUserRoleId(_UserDetails.UserRole);
    UserRole = objRoleMaster.UserRoleName;
    if (objRoleMaster.UserRoleID > 0)
        RoleId = objRoleMaster.UserRoleID;
    if (_UserDetails.UserID > 0)
        UserId = _UserDetails.UserID;

    var LoginUserDetailsJSON = HttpContextAccessor.HttpContext.Session.GetString("MenuData");

    if (LoginUserDetailsJSON != null)
    {
        var objUserDetails = JsonConvert.DeserializeObject<List<MenuRights>>(LoginUserDetailsJSON);

        //objUserDetails = objUserDetails.Where(Menu => Menu.MenuID > 0).ToList();
        ViewBag.AllMenuData = objUserDetails;
        var lstMainMenu = objUserDetails.GroupBy(x => x.MenuName).Select(x => x.FirstOrDefault());
        ViewBag.MainMenu = lstMainMenu;

        var lstSubMainMenu = objUserDetails.GroupBy(x => x.SubMenuName).Select(x => x.FirstOrDefault());
        ViewBag.SubMenu = lstSubMainMenu;



        var lstPage = objUserDetails.GroupBy(x => x.PageID).Select(x => x.FirstOrDefault());
        ViewBag.lstPage = lstPage;

        ViewBag.OrgNizationLogoName = HttpContextAccessor.HttpContext.Session.GetString("OrgNizationLogoName");

    }
    if (ViewBag.OrgNizationLogoName == string.Empty)
    {
        ViewBag.OrgNizationLogoName = "info.png";
    }
    int i = 1;
}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - CVBCM</title>
    <link rel="icon" type="image/ico" href="/img/Logo/Favicon.svg">
    <!-- 5.0 Bootstrap CSS -->
    <link href="~/lib/ui-lightness/jquery-ui.css" rel="stylesheet" />
    <link href="~/lib/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <link href="~/lib/datatables/css/datatables.bootstrap5.min.css" rel="stylesheet" />
    <link href="~/lib/selectize/selectize.bootstrap5.css" rel="stylesheet" />
    <link href="~/fonts/San-Francisco-Pro/SFPro-Font.css" rel="stylesheet" />
    <link href="~/cv-font-icons/cv-icons.css" rel="stylesheet" />
    <link href="~/css/wizard-steps.css" rel="stylesheet" />
    <!--Custom Css-->
    <link href="~/css/Theme.css" rel="stylesheet" />
    <link href="~/css/style.css" rel="stylesheet" />
    <link href="~/css/navbar.css" rel="stylesheet" />
    <link href="~/css/SideMenu.css" rel="stylesheet" />
    <link href="~/css/form.css" rel="stylesheet" />
    <link href="~/css/table.css" rel="stylesheet" />
    <link href="~/css/chatbot.css" rel="stylesheet" />
    <link href="~/css/timeline.css" rel="stylesheet" />
    <link href="~/css/validation.css" rel="stylesheet" />
    <style>
        .accordion-button {
        padding: 8px 10px;
        }

        #draggable {
        width: 10px;
        bottom: 0px;
        height: 0px;
        position: absolute;
        cursor: pointer;
        right: 0%;
        z-index:9999;
        }
    </style>
</head>
<body class="position-relative vh-100" id="container">

   

    <section class="home-section">
        <div id="cvheader" class="sticky-top">

            <section class="clearfix header-box  page-height z-index-full " id="headerDemo1">
                <!-- Header Star -->
                <header class="site-header">

                    <!-- Main Header -->
                    <div class="main-bar-wraper">
                        <div class="main-bar clearfix">
                            <div class=" inner-bar clearfix">

                                <!-- Website Logo -->
                                <div class="logo-header">
                                    <a href="@Url.Action("Index", "Dashboard",new { area="" })">  <img src="~/img/logo/cv_logo_2025.svg" width="180" /></a>
                                    @* <a href="javascript:void(0);"> <img src="~/img/logo/perpetuutit-logo.svg" width="130" /></a> *@
                                </div>

                                <!-- Nav Toggle Button -->
                                <button class="xmenu-toggler navicon" type="button" data-target="#xMenuNav">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </button>

                                <!-- Main Nav -->
                                <div class="menu-close fade-overlay"></div>
                                <div class="header-nav xmenu xmenu-center mo-left" id="xMenuNav">
                                    <div class="logo-header">
                                        <a href="@Url.Action("Index", "Dashboard",new { area="" })"><img src="images/logo.svg" alt=""></a>
                                    </div>
                                    <ul class="nav navbar-nav">
                                        <li class="active">
                                            <a href="@Url.Action("Index", "Dashboard",new { area="" })">
                                                <span class="d-flex align-items-center gap-1"><span class="nav-circle-icon"><i class="cv-home-2 align-middle fs-6"></i></span><span>Dashboard</span></span>

                                            </a>
                                        </li>
                                        @foreach (MenuRights Main in ViewBag.MainMenu)
                                        {
                                            if (Main.MenuID > 0)
                                            {
                                                var objUserDetails = JsonConvert.DeserializeObject<List<MenuRights>>(LoginUserDetailsJSON);
                                                List<MenuRights> lstSubMainMenu = objUserDetails.GroupBy(x => x.SubMenuName).Select(x => x.FirstOrDefault()).ToList();

                                                <li class="has-mega-menu sub-menu-down  menu-center auto-width">
                                                    <a href="javascript:void(0);">
                                                        <span class="d-flex align-items-center gap-1">
                                                            <span class="nav-circle-icon"><i class="@Main.MenuIcon align-middle fs-6"></i></span><span>@Main.MenuName</span>
                                                        </span>

                                                    </a>
                                                    <div class="mega-menu">
                                                        <ul>
                                                            @* @foreach (MenuRights SubMenu in ViewBag.SubMenu)
                                                            { *@
                                                            @for (int h = 0; h < lstSubMainMenu.Count; h++)
                                                            {
                                                                if (@Main.MenuID == @lstSubMainMenu[h].MenuID)
                                                                {
                                                                    <li>
                                                                        <span class="menu-title">@lstSubMainMenu[h].SubMenuName</span>
                                                                        <ul class="list-hover1">

                                                                            @foreach (MenuRights Page in ViewBag.AllMenuData)
                                                                            {
                                                                                if (@Page.SubMenuID == @lstSubMainMenu[h].SubMenuID)
                                                                                {
                                                                                    <li>
                                                                                        <a href="@Url.Action(@Page.ActionName,@Page.ControllerName, new { area = @Page.AreaName })">
                                                                                            <i class="@Page.IconClass align-middle fs-6 me-2"></i>@Page.PageName
                                                                                        </a>
                                                                                    </li>
                                                                                }
                                                                            }

                                                                        </ul>
                                                                        @if (h <= lstSubMainMenu.Count)
                                                                        {
                                                                            h++;
                                                                            if (h == lstSubMainMenu.Count)
                                                                            {
                                                                                h--;
                                                                                break;
                                                                            }
                                                                            if (@Main.MenuID == @lstSubMainMenu[h].MenuID)
                                                                            {
                                                                                <span class="menu-title">
                                                                                    @lstSubMainMenu[h].SubMenuName
                                                                                </span>
                                                                                <ul class="list-hover1">
                                                                                    @foreach (MenuRights Page in ViewBag.AllMenuData)
                                                                                    {
                                                                                        if (@Page.SubMenuID == @lstSubMainMenu[h].SubMenuID)
                                                                                        {
                                                                                            <li>
                                                                                                <a href="@Url.Action(@Page.ActionName,@Page.ControllerName, new { area = @Page.AreaName })">
                                                                                                    <i class="@Page.IconClass align-middle fs-6 me-2"></i>@Page.PageName
                                                                                                </a>
                                                                                            </li>
                                                                                        }
                                                                                    }
                                                                                </ul>
                                                                            }
                                                                        }
                                                                        else
                                                                        {

                                                                        }

                                                                        @* <span class="menu-title">
                                                                            Manage BCM Applications
                                                                        </span>
                                                                        <ul class="list-hover1">
                                                                            <li>
                                                                                <a href="@Url.Action("ManageApplication","ManageApplication",new { area="BCMApplicationBIA" })">
                                                                                    <i class="cv-application align-middle fs-6 me-2"></i>Manage
                                                                                    Applications
                                                                                </a>
                                                                            </li>
                                                                        </ul> *@

                                                                    </li>
                                                                }

                                                            }
                                                            @*  } *@

                                                        </ul>
                                                    </div>
                                                </li>
                                            }
                                        }                                       
                                        <li>
                                            <a href="@Url.Action("List", "PreBuildReport",new { area="" })">
                                                <span class="d-flex align-items-center gap-1"><span class="nav-circle-icon"><i class="cv-reports align-middle fs-6"></i></span><span>Reports</span></span>

                                            </a>
                                        </li>
                                    </ul>

                                </div>
                                @*  <li class="has-mega-menu sub-menu-down  menu-center auto-width">
                                            <a href="javascript:void(0);">
                                                <span class="d-flex align-items-center gap-1">
                                                    <span class="nav-circle-icon"><i class="cv-manage align-middle fs-6"></i></span><span>Manage</span>
                                                </span>

                                            </a>
                                            <div class="mega-menu">
                                                <ul>
                                                    <li>
                                                        <span class="menu-title">Manage BCM Users & Team</span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("BCMGroups","BCMGroups",new { area="BCMTeams" })">
                                                                    <i class="cv-bcm-teams align-middle fs-6 me-2"></i>Manage BCM Teams

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("ManageBCMResource","ManageBCMResource",new { area="BCMResourceManagement" })">
                                                                    <i class="cv-resources-2 align-middle fs-6 me-2"></i>BCM Resources

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("ManageUsers","ManageUsers",new { area="BCMResourceManagement" })">
                                                                    <i class="cv-bcm-user-login align-middle fs-6 me-2"></i>Manage BCM User Login

                                                                </a>
                                                            </li>
                                                        </ul>
                                                        <span class="menu-title">
                                                            Escalation Matrix & Vendor
                                                        </span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("ManageVendor","ManageVendor",new { area="BCMThirdParty" })">
                                                                    <i class="cv-escalation-matrix align-middle fs-6 me-2"></i>Escalation Matrix

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("ManageEscalationMatrix","EscalationMatrix",new { area="BCMAdministration" })">
                                                                    <i class="cv-vendor-configure align-middle fs-6 me-2"></i>Configure Vendor

                                                                </a>
                                                            </li>
                                                        </ul>

                                                    </li>
                                                    <li>
                                                        <span class="menu-title">Facility Management</span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("ManageBCMFacility","ManageFacility",new { area="BCMFacility" })">
                                                                    <i class="cv-bcm-facility align-middle fs-6 me-2"></i>
                                                                    BCM Facility

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("ManageBCMFacility","ManageFacility",new { area="BCMFacility" })">
                                                                    <i class="cv-manage-location align-middle fs-6 me-2"></i>Manage Location

                                                                </a>
                                                            </li>

                                                        </ul>
                                                        <span class="menu-title">
                                                            Audit Trail

                                                        </span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("AuditLogDetails","AuditLogDetails",new { area="BCMAudit" })">
                                                                    <i class="cv-audit-log align-middle fs-6 me-2"></i>Audit Log Details
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("UserActivitiesForm","UserActivitiesForm",new { area="BCMResourceManagement" })">
                                                                    <i class="cv-user-activity align-middle fs-6 me-2"></i>User Activities Form
                                                                </a>
                                                            </li>
                                                        </ul>

                                                    </li>
                                                    <li>
                                                        <span class="menu-title">
                                                            BCM Policy & Procedure
                                                        </span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("AuditPolicy","AuditPolicy",new { area="BCMDocuments" })">
                                                                    <i class="cv-audit-policy align-middle fs-6 me-2"></i>Audit Policy

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("AuditLogDetails","AuditLogDetails",new { area="BCMAudit" })">
                                                                    <i class="cv-bcm-policy align-middle fs-6 me-2"></i>BCM Policy

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("AuditLogDetails","AuditLogDetails",new { area="BCMAudit" })">
                                                                    <i class="cv-bcm-document align-middle fs-6 me-2"></i>Other BCM Document

                                                                </a>
                                                            </li>

                                                        </ul>
                                                        <span class="menu-title">
                                                            Manage BCM Risk
                                                        </span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("BCMGroups","BCMGroups",new { area="BCMTeams" })">
                                                                    <i class="cv-manage-bcm-risk align-middle fs-6 me-2"></i>Manage BCM Teams

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("ManageRecoveryPlans","ManageRecoveryPlans",new { area="BCMFunctionRecoveryPlan" })">
                                                                    <i class="cv-develop-Plan align-middle fs-6 me-2"></i>Recovery Plan

                                                                </a>
                                                            </li>

                                                        </ul>
                                                    </li>

                                                </ul>
                                            </div>


                                        </li>
                                        <li>

                                            <a href="javascript:void(0);">
                                                <span class="d-flex align-items-center gap-1">
                                                    <span class="nav-circle-icon"><i class="cv-workflow-flow align-middle fs-6"></i></span><span>Workflow</span>
                                                </span>

                                            </a>
                                        </li>
                                        <li class="has-mega-menu sub-menu-down  menu-center auto-width">

                                            <a href="javascript:void(0);">
                                                <span class="d-flex align-items-center gap-1">
                                                    <span class="nav-circle-icon"><i class="cv-settings align-middle fs-6"></i></span><span>Settings</span>
                                                </span>

                                            </a>
                                            <div class="mega-menu">
                                                <ul>
                                                    <li>
                                                        <span class="menu-title">Configure Org BCM Settings</span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("RTOMTRConfiguration","RTOMTRConfiguration",new { area="BCMAdministration" })">
                                                                    <i class="cv-RTO align-middle fs-6 me-2"></i>Configure RTO MAO
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("ManageBIAProfile","ManageBIAProfile",new { area="BCMBIAProfile" })">
                                                                    <i class="cv-bia-profile1 align-middle fs-6 me-2"></i>Manage BIA Profile

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("ProcessComplianceIndex","ProcessComplianceIndex",new { area="BCMAdministration" })">
                                                                    <i class="cv-process-compliance align-middle fs-6 me-2"></i>Process Compliance Index

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("BusinessParameterProfile","BusinessParameterProfile",new { area="BCMAdministration" })">
                                                                    <i class="cv-parameter align-middle fs-6 me-2"></i>Business Parameter Profile
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("ManageBIAProfile","ManageBIAProfile",new { area="BCMBIAProfile" })">
                                                                    <i class="cv-configure-bia-profile align-middle fs-6 me-2"></i>Configure BIA Profile
                                                                </a>
                                                            </li>
                                                        </ul>

                                                    </li>
                                                    <li>
                                                        <span class="menu-title">
                                                            BIA Survey & Incident Setup
                                                        </span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("BIASurveySection","BIASurveySection",new { area="BCMAdministration" })">
                                                                    <i class="cv-survey-section align-middle fs-6 me-2"></i>Config BIA Survey Section
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("BIAQuestions","BIAQuestions",new { area="BCMAdministration" })">
                                                                    <i class="cv-survey-questions align-middle fs-6 me-2"></i>Config BIA Survey Questions

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("BIASurveySectionMapping","BIASurveySectionMapping",new { area="BCMAdministration" })">
                                                                    <i class="cv-bia-survey-section align-middle fs-6 me-2"></i>BIA Survey Section Mapping
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("DisasterTypeMaster","DisasterTypeMaster",new { area="BCMAdministration" })">
                                                                    <i class="cv-incident-type align-middle fs-6 me-2"></i>Configure Incident Type

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("IncidentTypeMaster","IncidentTypeMaster",new { area="BCMAdministration" })">
                                                                    <i class="cv-incident-detail align-middle fs-6 me-2"></i>Configure Incident Details

                                                                </a>
                                                            </li>
                                                        </ul>


                                                    </li>
                                                    <li>
                                                        <span class="menu-title">
                                                            Configure Menu & Vault
                                                        </span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("MenuMaster","MenuMaster",new { area="BCMAdministration" })">
                                                                    <i class="cv-configure-menu align-middle fs-6 me-2"></i>Configure Menu/Page
                                                                </a>
                                                            </li>
                                                            @* <li>
                                                                <a href="@Url.Action("PageMaster","PageMaster",new { area="BCMAdministration" })">
                                                                    <i class="cv-configure-menu align-middle fs-6 me-2"></i>Configure Page
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("SubMenusMaster","SubMenusMaster",new { area="BCMAdministration" })">
                                                                    <i class="cv-configure-menu align-middle fs-6 me-2"></i>Configure SubMenu
                                                                </a>
                                                            </li> 
                                                            <li>
                                                                <a href="@Url.Action("ConfigSettings","ConfigSettings",new { area="BCMConfiguration" })">
                                                                    <i class="cv-vault align-middle fs-6 me-2"></i>Configure Vault Settings
                                                                </a>
                                                            </li>

                                                        </ul>
                                                        <span class="menu-title">
                                                            Configure BIA Impacts
                                                        </span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="@Url.Action("IncidentTypeMaster","IncidentTypeMaster",new { area="BCMAdministration" })">
                                                                    <i class="cv-impact align-middle fs-6 me-2"></i>Config BIA Impact Types
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="@Url.Action("ImpactMaster","ImpactMaster",new { area="BCMAdministration" })">
                                                                    <i class="cv-impact_detail align-middle fs-6 me-2"></i>Config BIA Impact Details

                                                                </a>
                                                            </li>

                                                        </ul>
                                                    </li>

                                                </ul>
                                            </div>

                                        </li>
                                        <li class="has-mega-menu sub-menu-down  menu-center auto-width">

                                            <a href="javascript:void(0);">
                                                <span class="d-flex align-items-center gap-1">
                                                    <span class="nav-circle-icon"><i class="cv-reports align-middle fs-6"></i></span><span>Reports</span>
                                                </span>

                                            </a>
                                            <div class="mega-menu">
                                                <ul>
                                                    <li>
                                                        <span class="menu-title">All Reports</span>
                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-kpi align-middle fs-6 me-2"></i>
                                                                    KPI Report

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-bia-report align-middle fs-6 me-2"></i>BIA Report
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-business-impact-result align-middle fs-6 me-2"></i>Business&nbsp;Impact&nbsp;Analysis&nbsp;Result

                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-pci-report align-middle fs-6 me-2"></i>PCI Report
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-process-recovery-report align-middle fs-6 me-2"></i>Process&nbsp;Recovery&nbsp;Priority&nbsp;Report
                                                                </a>
                                                            </li>
                                                        </ul>


                                                    </li>
                                                    <li>

                                                        <ul class="list-hover1">
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-process-compliant-report align-middle fs-6 me-2"></i>Process Complaint Report
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-application-rto align-middle fs-6 me-2"></i>Application RTO Summary
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-hr-report align-middle fs-6 me-2"></i>HR Report

                                                                </a>
                                                            </li>

                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-primary-resource-report align-middle fs-6 me-2"></i>Primary Resource Report
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-risk-assessment-report align-middle fs-6 me-2"></i>RA Summary Report
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="javascript:void(0);">
                                                                    <i class="cv-risk-assessment-report align-middle fs-6 me-2"></i>Incident Summary Report
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </li>

                                                </ul>
                                            </div>
                                        </li> *@



                                <div class="d-flex align-items-center gap-3" role="search">

                                    <div class="nav-item dropdown">
                                        <a class="nav-link dropdown-toggle d-flex align-items-center gap-2" href="#" role="button" data-bs-toggle="dropdown"
                                           aria-expanded="false">
                                            <img class="Profile-img" src="~/img/profile-img/usericon.svg" alt="Profile" />
                                            <div class="d-grid">
                                                <span class="fw-semibold">@_UserDetails.UserName</span>

                                            </div>
                                        </a>
                                        <ul class="dropdown-menu dropdown-menu-end border-0 mt-2">
                                            <li><a class="dropdown-item" href="@Url.Action("UserProfile","UserProfile",new { area="UserProfile" })">Profile</a></li>
                                            <li><a class="dropdown-item" href="@Url.Action("ChangePassword", "ChangePassword",new { area="" })">Change Password</a></li>

                                            <li><a class="dropdown-item" href="@Url.Action("Logout", "Logout",new { area="" })">logout</a></li>
                                        </ul>
                                    </div>
                                    <div class="vr mx-2 align-self-center" style="height:30px;"></div>
                                    <img src="~/img/logo/info.png" height="30" />
                                    @* <img src="~/img/logo/@ViewBag.OrgNizationLogoName" width="130" height="40" /> *@
                                </div>                              
                            </div>
                        </div>
                    </div>
                    <!-- Main Header End -->

                </header>
                <!-- Header End -->
            </section>

            <nav class="navbar bg-white shadow-sm d-none">
                <div class="container-fluid">
                    <img src="~/img/logo/perpetuutit-logo.svg" width="130" />

                    <a class="navbar-brand text-primary fw-semibold"></a>
                    <div class="d-flex align-items-center gap-3" role="search">

                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center gap-2" href="#" role="button" data-bs-toggle="dropdown"
                               aria-expanded="false">
                                <img class="Profile-img" src="~/img/profile-img/usericon.svg" alt="Profile" />
                                <div class="d-grid">
                                    <span class="fw-semibold">@_UserDetails.UserName</span>

                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end border-0 mt-2">
                                <li><a class="dropdown-item" href="@Url.Action("UserProfile","UserProfile",new { area="UserProfile" })">Profile</a></li>
                                <li><a class="dropdown-item" href="@Url.Action("ChangePassword", "ChangePassword",new { area="" })">Change Password</a></li>

                                <li><a class="dropdown-item" href="@Url.Action("Logout", "Logout",new { area="" })">logout</a></li>
                            </ul>
                        </div>
                        <div class="vr mx-2 align-self-center" style="height:30px;"></div>

                        <img src="~/img/logo/@ViewBag.OrgNizationLogoName" width="130" height="40" />
                    </div>
                </div>
            </nav>
        </div>
        <main role="main" class="container-fluid" >
            @RenderBody()
        </main>
    </section>
    <div id="draggable" class="position-fixed">
        @* Chatbot Windows *@
        <div class="chatbot__button">
            <span class="material-symbols-outlined">
                <img src="~/img/chatbot-img/Frame.svg" width="50" />
            </span>
            <span class="material-symbols-outlined">
                <img src="~/img/chatbot-img/Frame.svg" width="50" />
            </span>
        </div>
        <div class="chatbot">
            <div class="chatbot__header pt-0">
                <img src="~/img/chatbot-img/susan_3.png" height="80px;" />
                <h6 class="chatbox__title">Susan</h6>
                <p class="text-primary fw-normal mb-0">BCM Consultant</p>
                <span class="material-symbols-outlined">
                    <button type="button" class="btn-close" aria-label="Close"></button>
                </span>
            </div>

            <ul class="chatbot__box">
                @*  <li class="chatbot__chat incoming">
                <img src="~/img/chatbot-img/chaticon.svg" />
                <div class="AI-Message">
                <p>Hi Neeraj</p>
                <p>I have listed your “How many BCM entities are within the scope” as by your request</p>
                <p>Totally <span class="text-primary">27 BCM entities.</span> are within the scope.</p>
                </div>
                </li>
                <li class="chatbot__chat outgoing">
                <div class="User-Message">
                <span>Hi</span>
                </div>
                <img src="~/img/profile-img/usericon.svg" />
                </li> *@
            </ul>

            <div class="chatbot__input-box">
                <input class="chatbot__textarea" placeholder="Enter a message..." required />
                <span id="send-btn" class="material-symbols-outlined">
                    <img src="~/img/chatbot-img/chatarrow.svg" />
                </span>
            </div>
        </div>
    </div>
    @* Toast message start *@
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div id="liveToast" class="toast bg-success text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex" data-bs-theme="dark">
                <div class="toast-body">
                    <div class="d-flex align-items-center gap-1">
                        <span><i class="cv-success align-middle fs-5 "></i></span>
                        <span>
                            Organization Group Updated Successfully
                        </span>
                    </div>
                </div>
                <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>

    @* Toast message end *@


    @* <script src="./lib/jquery/jquery.min.js"></script>
    <script src="./lib/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="./lib/jquery.steps/jquery.steps.min.js"></script>
    <script src="./lib/datatables/js/jquery.datatables.min.js"></script>
    <script src="./lib/datatables/js/datatables.bootstrap5.min.js"></script>
    <script src="./lib/selectize/selectize.min.js"></script>

    <script src="./js/ManageRecoveryPlan.js"></script>
    <script src="./js/sidemenu.js"></script>
    <script src="./js/chatbot.js"></script> *@

    <script src="~/lib/jquery/jquery.min.js"></script>
    <script src="~/lib/ui-lightness/jquery-ui.min.js"></script>
    <script src="~/lib/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery.steps/jquery.steps.min.js"></script>
    <script src="~/lib/datatables/js/jquery.datatables.min.js"></script>
    <script src="~/lib/datatables/js/datatables.bootstrap5.min.js"></script>
    <script src="~/lib/selectize/selectize.min.js"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <script src="~/js/global-validation.js"></script>
    <script src="~/js/managerecoveryplan.js"></script>



    <script src="~/js/chatbot.js"></script>

    <script src="~/js/password_toggle.js"></script>

    <!-- Menu Active Class Script -->
    <script>
        $(document).ready(function () {
            function resetMenuHighlighting() {
                $('#xMenuNav .nav > li').removeClass('active');
                $('#xMenuNav .nav > li > a span').css('color', '');
                $('.mega-menu a').removeClass('active'); // Also reset active state from submenus
            }

            function highlightMenu($menuItem, $subMenuLink) {
                $menuItem.addClass('active');
                $menuItem.find('> a span').css('color', '#E63875');

                let menuName;
                if ($subMenuLink && $subMenuLink.length > 0) {
                    $subMenuLink.addClass('active'); // Highlight the clicked submenu item
                    menuName = $subMenuLink.text().trim();
                } else {
                    menuName = $menuItem.find('> a span:last').text().trim();
                }

                sessionStorage.setItem('activeMainMenu', menuName);
            }

            // Mega-menu item click
            $(document).on('click', '.mega-menu a', function (e) {
                var $subMenuLink = $(this);
                var $parentMainMenu = $subMenuLink.closest('li.has-mega-menu');
                if ($parentMainMenu.length > 0) {
                    resetMenuHighlighting();
                    highlightMenu($parentMainMenu, $subMenuLink);
                }
            });

            // Regular menu item click
            $(document).on('click', '#xMenuNav .nav > li:not(.has-mega-menu) a', function (e) {
                var $parentMenu = $(this).closest('li');
                resetMenuHighlighting();
                highlightMenu($parentMenu);
            });

            // Restore menu state on page load
            var activeMenu = sessionStorage.getItem('activeMainMenu');
            if (activeMenu) {
                $('#xMenuNav .nav > li').each(function () {
                    var $li = $(this);
                    var topText = $li.find('> a span:last').text().trim();
                    if (topText === activeMenu) {
                        resetMenuHighlighting();
                        highlightMenu($li);
                    } else {
                        $li.find('.mega-menu a').each(function () {
                            var $link = $(this);
                            if ($link.text().trim() === activeMenu) {
                                resetMenuHighlighting();
                                highlightMenu($li, $link);
                            }
                        });
                    }
                });
            }
        });


    </script>
    <script>
        $(document).ready(function () {

            var isDragging = false;
            var offsetX, offsetY;

            // Mouse Down Event to start dragging
            $('#draggable').on('mousedown', function (e) {
                isDragging = true;
                offsetX = e.clientX - $(this).offset().left;
                offsetY = e.clientY - $(this).offset().top;
                $(this).css('cursor', 'move');
            });

            // Mouse Move Event to drag the div
            $(document).on('mousemove', function (e) {
                if (isDragging) {
                    var left = e.clientX - offsetX;
                    var top = e.clientY - offsetY;

                    // Constrain movement inside container bounds
                    var containerOffset = $('#container').offset();
                    var containerWidth = $('#container').width();
                    var containerHeight = $('#container').height();

                    // Ensure the div stays within the container
                    left = Math.max(containerOffset.left, Math.min(left, containerOffset.left + containerWidth - $('#draggable').width()));
                    top = Math.max(containerOffset.top, Math.min(top, containerOffset.top + containerHeight - $('#draggable').height()));

                    $('#draggable').css({
                        left: left - containerOffset.left,
                        top: top - containerOffset.top
                    });
                }
            });

            // Mouse Up Event to stop dragging
            $(document).on('mouseup', function () {
                isDragging = false;
                $('#draggable').css('cursor', 'pointer');
            });
        });
    </script>

    <!-- Auto Logout Script -->
    @* <script>
        $(document).ready(function() {
            // Auto logout configuration
            var autoLogoutTimer;

            var logoutTimeoutDuration = 30000;
            //var logoutTimeoutDuration = 1200000; // 20 Minutes in milliseconds
            var isLoggingOut = false;

            // Function to clear session and redirect to logout
            function performAutoLogout() {
                if (isLoggingOut) return; // Prevent multiple logout attempts
                isLoggingOut = true;

                console.log('Auto logout triggered - redirecting to logout page');

                // Clear session via AJAX call
                $.ajax({
                    url: '@Url.Action("Logout", "Logout")',
                    type: 'POST',
                    async: false, // Synchronous to ensure session is cleared before redirect
                    success: function() {
                        // Clear any local storage or session storage
                        sessionStorage.clear();
                        localStorage.clear();

                        // Redirect to logout page
                        window.location.href = '/Logout/Logout';
                    },
                    error: function() {
                        // Even if AJAX fails, redirect to logout page
                        sessionStorage.clear();
                        localStorage.clear();
                        window.location.href = '/Login/Login';
                    }
                });
            }

            // Function to reset the auto logout timer
            function resetAutoLogoutTimer() {
                if (isLoggingOut) return; // Don't reset if already logging out

                // Clear existing timer
                if (autoLogoutTimer) {
                    clearTimeout(autoLogoutTimer);
                }

                // Set new timer
                autoLogoutTimer = setTimeout(function() {
                    performAutoLogout();
                }, logoutTimeoutDuration);

                console.log('Auto logout timer reset - ' + (logoutTimeoutDuration / 1000) + ' seconds until logout');
            }

            // Events that should reset the timer
            var resetEvents = [
                'mousedown',    // Mouse click
                'mousemove',    // Mouse movement
                'keypress',     // Key press
                'keydown',      // Key down
                'keyup',        // Key up
                'scroll',       // Scrolling
                'touchstart',   // Touch events for mobile
                'touchmove',
                'touchend',
                'focus',        // Window focus
                'blur'          // Window blur (reset when coming back)
            ];

            // Attach event listeners to reset timer
            resetEvents.forEach(function(eventName) {
                $(document).on(eventName, function() {
                    resetAutoLogoutTimer();
                });
            });

            // Also listen for window focus/blur events
            $(window).on('focus', function() {
                resetAutoLogoutTimer();
            });

            $(window).on('blur', function() {
                // Optional: You might want to pause the timer when window loses focus
                // For now, we'll keep it running
            });

            // Initialize the timer when page loads
            resetAutoLogoutTimer();

            // Optional: Show warning before logout (uncomment if needed)
            /*
            function showLogoutWarning() {
                if (confirm('You will be logged out due to inactivity. Click OK to stay logged in.')) {
                    resetAutoLogoutTimer();
                } else {
                    performAutoLogout();
                }
            }

            // Show warning 5 seconds before logout
            function setWarningTimer() {
                setTimeout(function() {
                    if (!isLoggingOut) {
                        showLogoutWarning();
                    }
                }, logoutTimeoutDuration - 5000);
            }
            */
        });
    </script> *@
    <!-- Auto Logout Script -->
    <script>
        $(document).ready(function() {
            // Auto logout configuration
            var autoLogoutTimer;

            //var logoutTimeoutDuration = 30000; // 30 Seconds in milliseconds
            var logoutTimeoutDuration = 1200000; // 20 Minutes in milliseconds
            var isLoggingOut = false;

            // Function to clear session and redirect to logout
            function performAutoLogout() {
                if (isLoggingOut) return; // Prevent multiple logout attempts
                isLoggingOut = true;

                //console.log('Auto logout triggered - redirecting to login page');

                // First AJAX call to set the session expiry message
                $.ajax({
                    url: '@Url.Action("SetSessionExpiredMessage", "Login")',
                    type: 'POST',
                    success: function() {
                        // Second AJAX call to logout
                        $.ajax({
                            url: '@Url.Action("Logout", "Logout")',
                            type: 'POST',
                            success: function() {
                                // Clear any local storage or session storage
                                sessionStorage.clear();
                                localStorage.clear();
                                
                                // Redirect to login page
                                window.location.href = '@Url.Action("Login", "Login", new { area = "" })';
                            },
                            error: function() {
                                // Even if AJAX fails, redirect to login page
                                sessionStorage.clear();
                                localStorage.clear();
                                window.location.href = '@Url.Action("Login", "Login", new { area = "" })';
                            }
                        });
                    },
                    error: function() {
                        // If setting message fails, still try to logout
                        sessionStorage.clear();
                        localStorage.clear();
                        window.location.href = '@Url.Action("Login", "Login", new { area = "" })';
                    }
                });
            }

            // Function to reset the auto logout timer
            function resetAutoLogoutTimer() {
                if (isLoggingOut) return; // Don't reset if already logging out

                // Clear existing timer
                if (autoLogoutTimer) {
                    clearTimeout(autoLogoutTimer);
                }

                // Set new timer
                autoLogoutTimer = setTimeout(function() {
                    performAutoLogout();
                }, logoutTimeoutDuration);
            }

            // Events that should reset the timer
            var resetEvents = [
                'mousedown',    // Mouse click
                'mousemove',    // Mouse movement
                'keypress',     // Key press
                'keydown',      // Key down
                'keyup',        // Key up
                'scroll',       // Scrolling
                'touchstart',   // Touch events for mobile
                'touchmove',
                'touchend',
                'focus',        // Window focus
                'blur'          // Window blur (reset when coming back)
            ];

            // Attach event listeners to reset timer
            resetEvents.forEach(function(eventName) {
                $(document).on(eventName, function() {
                    resetAutoLogoutTimer();
                });
            });

            // Also listen for window focus/blur events
            $(window).on('focus', function() {
                resetAutoLogoutTimer();
            });

            $(window).on('blur', function() {
                // Optional: You might want to pause the timer when window loses focus
                // For now, we'll keep it running
            });

            // Initialize the timer when page loads
            resetAutoLogoutTimer();
        });
    </script>

    <script>
        const toastTrigger = document.getElementById('liveToastBtn')
        const toastLiveExample = document.getElementById('liveToast')

        if (toastTrigger) {
            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
            toastTrigger.addEventListener('click', () => {
                toastBootstrap.show()
            })
        }
        //Time script
        // function checkTime(i) {
        //     if (i < 10) {
        //         i = "0" + i;
        //     }
        //     return i;
        // }
        // function startTime() {
        //     var today = new Date();
        //     var month = today.getMonth() + 1;
        //     var date = today.getDate() + "/" + month + "/" + today.getFullYear() + " ";
        //     var h = today.getHours();
        //     var m = today.getMinutes();
        //     var s = today.getSeconds();
        //     // add a zero in front of numbers<10
        //     m = checkTime(m);
        //     s = checkTime(s);
        //     document.getElementById('time').innerHTML = date + "  " + h + ":" + m + ":" + s;
        //     t = setTimeout(function () {
        //         startTime()
        //     }, 500);
        // }
        // startTime();
    </script>

    @await RenderSectionAsync("Scripts", required: false)

</body>
</html>

