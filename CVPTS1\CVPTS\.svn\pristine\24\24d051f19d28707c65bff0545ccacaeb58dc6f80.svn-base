﻿@model BCM.BusinessClasses.BCMTrainingMaster
@{
    ViewData["Title"] = "BCMTrainingMasterConfiguration";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">BCM Training Configuration</h6>
    </div>
</div>


@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers


<form id="editBCMTrainingMasterForm" asp-action="EditBCMTrainingMaster" method="post" class="needs-validation"
    enctype="multipart/form-data" novalidate>
    <div class="Page-Condant card border-0">
        <div style="height: calc(100vh - 134px);overflow-y: auto;padding: 12px;">
            @*  <h6 class="Sub-Title">Target Audience</h6> *@
            <div class="row row-cols-2">
                <div class="col">
                    <div class="form-group" hidden>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-login-code"></i></span>
                            <input type="hidden" class="form-control" asp-for="ID">
                            <input type="hidden" class="form-control" asp-for="TrainingMasterID">
                        </div>
                        <div class="invalid-feedback">Enter Department Name</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Organization</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <select class="form-select form-control selectized" asp-for="OrgID" autocomplete="off"
                                id="ddlOrganization" aria-label="Default select example"
                                asp-items="@(new SelectList(ViewBag.OrgInfo ?? new List<object>(), "Id", "OrganizationName", Model?.OrgID))"
                                required>
                                <option disabled value="0">-- Select Organizations --</option>

                                @* <select id="orglist" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="OrgID" required>
                                <option selected disabled value="">-- All Organizations --</option>
                                @if (ViewBag.OrgInfo != null)
                                {
                                    @foreach (var objOrg in ViewBag.OrgInfo)
                                    {
                                        <option value="@objOrg.Value">@objOrg.Text</option>
                                    }
                                }*@
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Department</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-department"></i></span>
                            <select class="form-select form-control selectized ddlDepartment" asp-for="DepartmentID"
                                autocomplete="off" id="ddlDepartment" aria-label="Default select example"
                                asp-items="@(new SelectList(ViewBag.DepartmentInfo ?? new List<object>(), "DepartmentID", "DepartmentName", Model?.DepartmentID))">
                                <option value="0">-- Select Departments --</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Priority</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <select class="form-select form-select-sm" asp-for="Priority" required>
                                <option disabled value="0">-- Select --</option>
                                <option value="1">Mandatory</option>
                                <option value="2">Not Mandatory</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Training Validity</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-entity"></i></span>
                            <input class="form-control" type="date" asp-format="yyyy-MM-dd" asp-for="ValidityDate"
                                required />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Training Frequency</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-unit"></i></span>
                            <select class="form-select form-select-sm" name="TrainingFrequency"
                                id="ddlTrainingFrequency" required>
                                <option disabled value="0">-- Select --</option>
                                <option value="1">Monthly</option>
                                <option value="3">Quarterly</option>
                                <option value="6">By Annually</option>
                                <option value="12">Annually</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Trainng Approver</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-unit"></i></span>
                            <select class="form-select form-control" id="headlist" autocomplete="off"
                                aria-label="Default select example" asp-for="ApproverID"
                                asp-items="@(new SelectList(ViewBag.ResourcesInfo ?? new List<object>(), "ResourceId", "ResourceName", Model?.ApproverID))"
                                required>
                                <option disabled value="0">-- Select Approver --</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label class="form-label">Unit</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-unit"></i></span>
                            <select class="form-select form-control selectized ddlUnit" asp-for="UnitID"
                                autocomplete="off" id="ddlUnit" aria-label="Default select example"
                                asp-items="@(new SelectList(ViewBag.OrgUnit ?? new List<object>(), "UnitID", "UnitName", Model?.UnitID))">
                                <option disabled value="0">-- Select Units --</option>
                            </select>
                            @* <select id="unitlist" class="form-select form-control" autocomplete="off"
                                aria-label="Default select example" asp-for="UnitID" required>
                                <option selected value="0">-- All Units --</option>
                                @if (ViewBag.OrgUnit != null)
                                {
                                    @foreach (var objUnit in ViewBag.OrgUnit)
                                    {
                                        <option value="@objUnit.Value">@objUnit.Text</option>
                                    }
                                }
                            </select> *@
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Training Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-entity"></i></span>
                            <input class="form-control" type="text" placeholder="Training Name" asp-for="TrainingName"
                                required />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Training Owner</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-department"></i></span>
                            <select class="form-select form-control selectized" asp-for="OwnerID" autocomplete="off"
                                id="ddlOwner" aria-label="Default select example"
                                asp-items="@(new SelectList(ViewBag.ResourcesInfo ?? new List<object>(), "ResourceId", "ResourceName", Model?.OwnerID))">
                                <option disabled value="0">-- Select Owner --</option>
                            </select>
                            @* <select class="form-select form-control" id="headlist" autocomplete="off"
                                aria-label="Default select example" asp-for="OwnerID" required>
                                <option selected disabled value="">-- All Resources --</option>
                                @if (ViewBag.ResourcesInfo != null)
                                {
                                    @foreach (var objResource in ViewBag.ResourcesInfo)
                                    {
                                        <option value="@objResource.Value">@objResource.Text</option>
                                    }
                                }
                            </select> *@
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Training Duration</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-entity"></i></span>
                            <input class="form-control" type="text" placeholder="Training Duration"
                                asp-for="TrainingDuration" required />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Review Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-entity"></i></span>
                            <input class="form-control" type="date" asp-format="yyyy-MM-dd" asp-for="RevieweDate"
                                required />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Version</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-entity"></i></span>
                            <input class="form-control" disabled type="text" asp-for="Version" required />
                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <div class="form-group w-100">
                        <label class="form-label">Training Material Attachment</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-attachment"></i></span>
                            <input type="file" class="form-control" name="attachmentFile" id="attachmentFile"
                                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.gif,.txt">
                            <input type="hidden" asp-for="AttachmentName" id="hiddenAttachmentName">
                        </div>
                        <div class="mt-2" id="currentAttachmentInfo" style="display: none;">
                            <small class="text-muted">
                                <i class="cv-file"></i> Current file:
                                <span id="currentAttachmentName" class="fw-bold"></span>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2"
                                    onclick="downloadCurrentAttachment()">
                                    <i class="cv-download"></i> Download
                                </button>
                                
                                <button type="button" class="btn btn-sm btn-outline-danger ms-1"
                                    onclick="removeCurrentAttachment()">
                                    <i class="cv-delete"></i> Remove
                                </button>
                            </small>
                        </div>
                        <small class="form-text text-muted">
                            Supported formats: PDF, Word, Excel, PowerPoint, Images, Text files
                        </small>
                    </div>
                </div>

                <div class="col-12">
                    <div class="form-group w-100">
                        <label class="form-label">Purpose</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-remarks"></i></span>
                            <textarea class="form-control" placeholder="Purpose" rows="4" style="height:0px" asp-for="Purpose"
                                required></textarea>
                        </div>
                    </div>
                </div>

            </div>

        </div>
        <div class="modal-footer d-flex justify-content-between">
            <div class="d-flex align-items-center">
                <span class="fst-italic d-flex align-items-center text-secondary me-3">
                    <i class="cv-note me-1"></i>
                    <small>Note: All fields are mandatory except optional</small>
                </span>
                @if (!string.IsNullOrEmpty(ViewBag.StatusMessage as string))
                {
                    <span class="badge @((bool)ViewBag.IsEditable ? "bg-success" : "bg-warning") text-dark">
                        <i class="cv-info-circle me-1"></i>
                        @ViewBag.StatusMessage
                    </span>
                }
            </div>
            <div>
                <button type="button" class="btn btn-secondary btn-sm me-1" onclick="goBack()">
                    <i class="cv-arrow-left me-1"></i> Back
                </button>

                @if ((bool)ViewBag.IsEditable)
                {
                    <button id="btnsave" type="submit" class="btn btn-primary btn-sm">
                        @(Model != null && !string.IsNullOrEmpty(Model.ID) ? "Update" : "Save")
                    </button>
                    <button id="btnForceSubmit" type="button" class="btn btn-warning btn-sm" style="display:none;">Force
                        Submit</button>
                }
                else
                {
                    <button type="button" class="btn btn-secondary btn-sm" disabled
                        title="Training cannot be edited. Only owners can edit when status is 'Initiated' or 'Sent Back'.">
                        @(Model != null && !string.IsNullOrEmpty(Model.ID) ? "Update" : "Save")
                    </button>
                }

                @if (Model != null && !string.IsNullOrEmpty(Model.ID))
                {
                    @if ((bool)ViewBag.IsEditable)
                    {
                        <a class="btn btn-sm btn-primary" asp-action="QuestionsAndOptions"
                            asp-controller="AddQuestionsAndOptions"
                            asp-route-iID="@BCM.Security.Helper.CryptographyHelper.Encrypt(Model.ID.ToString())">
                            Add Questions
                        </a>
                    }
                    else
                    {
                        <button type="button" class="btn btn-sm btn-secondary" disabled
                            title="Questions cannot be added. Only owners can add questions when status is 'Initiated' or 'Sent Back'.">
                            Add Questions
                        </button>
                    }
                }
                else
                {
                    <button type="button" class="btn btn-sm btn-secondary" disabled
                        title="Save the training first to add questions">
                        Add Questions
                    </button>
                }

            </div>
        </div>
    </div>
</form>


@section Scripts {
    <script>
        $(document).ready(function () {
            // Set dropdown values from model after page load
            setDropdownValues();

            // Initialize unit dropdown change event
            initializeUnitDropdownChange();

            $('.btnEndReviewNew').click(function () {

                $('#EndReviewModal').modal('show');
            });
        // Function to initialize unit dropdown change event
        function initializeUnitDropdownChange() {
            console.log('Initializing unit dropdown change event');

            // Remove any existing event handlers to prevent duplicates
            $('#ddlUnit, .ddlUnit').off('change.unitDepartment');

            // Unit dropdown change event - using both ID and class selectors
            $('#ddlUnit, .ddlUnit').on('change.unitDepartment', function () {
                var unitID = $(this).val();
                console.log('Unit changed to:', unitID);

                // Clear department dropdown first
                var $departmentDropdown = $('#ddlDepartment');

                // Handle both regular select and selectize
                if ($departmentDropdown[0].selectize) {
                    // Selectize dropdown
                    var selectizeInstance = $departmentDropdown[0].selectize;
                    selectizeInstance.clear();
                    selectizeInstance.clearOptions();
                    selectizeInstance.addOption({ value: "0", text: "-- Select Departments --" });
                    selectizeInstance.setValue("0");
                } else {
                    // Regular select dropdown
                    $departmentDropdown.empty();
                    $departmentDropdown.append('<option value="0">-- Select Departments --</option>');
                    $departmentDropdown.val("0");
                }

                // If no unit selected, stop here
                if (!unitID || unitID == "0") {
                    console.log('No unit selected, departments cleared');
                    return;
                }

                // Load departments for selected unit
                $.ajax({
                    url: '@Url.Action("GetAllDepartments", "BCMTrainingMasterConfiguration")',
                    type: 'GET',
                    data: { iUnitID: unitID },
                    beforeSend: function() {
                        console.log('Loading departments for unit:', unitID);
                        // Show loading state
                        if ($departmentDropdown[0].selectize) {
                            $departmentDropdown[0].selectize.clearOptions();
                            $departmentDropdown[0].selectize.addOption({ value: "0", text: "Loading departments..." });
                            $departmentDropdown[0].selectize.setValue("0");
                        } else {
                            $departmentDropdown.empty();
                            $departmentDropdown.append('<option value="0">Loading departments...</option>');
                        }
                    },
                    success: function (data) {
                        console.log('Departments response:', data);

                        if ($departmentDropdown[0].selectize) {
                            // Selectize dropdown
                            var selectizeInstance = $departmentDropdown[0].selectize;
                            selectizeInstance.clear();
                            selectizeInstance.clearOptions();
                            selectizeInstance.addOption({ value: "0", text: "-- Select Departments --" });

                            if (data && Array.isArray(data) && data.length > 0) {
                                data.forEach(function(dept) {
                                    if (dept.departmentID && dept.departmentName) {
                                        selectizeInstance.addOption({
                                            value: dept.departmentID,
                                            text: dept.departmentName
                                        });
                                    }
                                });
                                console.log('Added', data.length, 'departments to selectize dropdown');
                            } else {
                                console.log('No departments found for unit:', unitID);
                            }
                            selectizeInstance.setValue("0");
                        } else {
                            // Regular select dropdown
                            $departmentDropdown.empty();
                            $departmentDropdown.append('<option value="0">-- Select Departments --</option>');

                            if (data && Array.isArray(data) && data.length > 0) {
                                $.each(data, function(index, dept) {
                                    if (dept.departmentID && dept.departmentName) {
                                        $departmentDropdown.append('<option value="' + dept.departmentID + '">' + dept.departmentName + '</option>');
                                    }
                                });
                                console.log('Added', data.length, 'departments to regular dropdown');
                            } else {
                                console.log('No departments found for unit:', unitID);
                            }
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('AJAX Error:', error);
                        console.error('Status:', status);
                        console.error('Response:', xhr.responseText);

                        // Show error state
                        if ($departmentDropdown[0].selectize) {
                            var selectizeInstance = $departmentDropdown[0].selectize;
                            selectizeInstance.clear();
                            selectizeInstance.clearOptions();
                            selectizeInstance.addOption({ value: "0", text: "Error loading departments" });
                            selectizeInstance.setValue("0");
                        } else {
                            $departmentDropdown.empty();
                            $departmentDropdown.append('<option value="0">Error loading departments</option>');
                        }

                        alert('Error loading departments. Please try again.');
                    }
                });
            });

            console.log('Unit dropdown change event initialized successfully');
        }

            // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function () {
                        //console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function () {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                //console.log("Initializing validation for add BCMVendor form");

                if (window.BCMValidation) {
                    //console.log("BCMValidation found, initializing");
                    window.BCMValidation.init();

                    // Manually validate the form on load to check for any initial errors
                    const form = document.getElementById('editBCMTrainingMasterForm');
                    if (form) {
                        //console.log("Found form with ID:", form.id);

                        // Add a manual validation trigger on form submission
                        form.addEventListener('submit', function (event) {
                            console.log("Form submission triggered");

                            // Log form data before validation
                            const formData = new FormData(form);
                            console.log("Form data being submitted:");
                            for (let [key, value] of formData.entries()) {
                                console.log(`${key}: ${value}`);
                            }

                            const isValid = window.BCMValidation.validateForm(form);
                            console.log("Form validation result:", isValid);

                            if (!isValid) {
                                console.log("Preventing form submission due to validation errors");
                                // Show force submit button for debugging
                                document.getElementById('btnForceSubmit').style.display = 'inline-block';
                                event.preventDefault();
                                event.stopPropagation();
                            } else {
                                console.log("Form validation passed, allowing submission");
                            }
                        });

                        // Force submit handler for debugging
                        document.getElementById('btnForceSubmit').addEventListener('click', function () {
                            console.log("Force submit clicked - bypassing validation");

                            // Remove validation event listeners temporarily
                            const newForm = form.cloneNode(true);
                            form.parentNode.replaceChild(newForm, form);

                            // Submit the form directly
                            newForm.submit();
                        });

                        // Manually validate all inputs on page load
                        const inputs = form.querySelectorAll('[required]');
                        // console.log("Found", inputs.length, "required inputs");

                        inputs.forEach(function (input) {
                            if (input.type === 'email') {
                                //console.log("Setting up email validation for", input.id || input.name);
                                window.BCMValidation.validateEmail(input);

                                // Add specific event listeners for email fields
                                input.addEventListener('input', function () {
                                    window.BCMValidation.validateEmail(this);
                                });

                                input.addEventListener('blur', function () {
                                    window.BCMValidation.validateEmail(this);
                                });
                            } else {
                                // console.log("Setting up validation for", input.id || input.name);
                                window.BCMValidation.validateInput(input);

                                // Add specific event listeners for other fields
                                input.addEventListener('input', function () {
                                    window.BCMValidation.validateInput(this);
                                });

                                input.addEventListener('blur', function () {
                                    window.BCMValidation.validateInput(this);
                                });
                            }
                        });
                    } else {
                        console.error("Form not found with ID: editBCMTrainingMasterForm");

                        // Try to find the form by class as a fallback
                        const formByClass = document.querySelector('form.needs-validation');
                        if (formByClass) {
                            console.log("Found form by class instead");
                            // Same validation setup as above
                            // (Code omitted for brevity)
                        } else {
                            console.error("Form not found by class either!");
                        }
                    }
                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }

        function setDropdownValues() {
            try {
                // Get model values from server-side
                var modelData = {
                    OrgID: @(Model?.OrgID ?? 0),
                    UnitID: @(Model?.UnitID ?? 0),
                    DepartmentID: @(Model?.DepartmentID ?? 0),
                    OwnerID: @(Model?.OwnerID ?? 0),
                    ApproverID: @(Model?.ApproverID ?? 0),
                    Priority: @(Model?.Priority ?? 0),
                    ValidityDate: '@(Model?.ValidityDate ?? "")',
                    RevieweDate: '@(Model?.RevieweDate ?? "")',
                    AttachmentName: '@(Model?.AttachmentName ?? "")'
                };

                console.log('Setting dropdown values:', modelData);

                // Set dropdown values if they exist and are not 0
                if (modelData.OrgID > 0) {
                    $('#ddlOrganization').val(modelData.OrgID);
                    if ($('#ddlOrganization')[0].selectize) {
                        $('#ddlOrganization')[0].selectize.setValue(modelData.OrgID);
                    }
                }

                if (modelData.UnitID > 0) {
                    $('#ddlUnit').val(modelData.UnitID);
                    if ($('#ddlUnit')[0].selectize) {
                        $('#ddlUnit')[0].selectize.setValue(modelData.UnitID);
                    }

                    // Load departments for the selected unit and then set the department value
                    if (modelData.DepartmentID > 0) {
                        loadDepartmentsAndSetValue(modelData.UnitID, modelData.DepartmentID);
                    }
                } else if (modelData.DepartmentID > 0) {
                    // If no unit but department is set, just set the department value
                    $('#ddlDepartment').val(modelData.DepartmentID);
                    if ($('#ddlDepartment')[0].selectize) {
                        $('#ddlDepartment')[0].selectize.setValue(modelData.DepartmentID);
                    }
                }

                if (modelData.OwnerID > 0) {
                    $('#ddlOwner').val(modelData.OwnerID);
                    if ($('#ddlOwner')[0].selectize) {
                        $('#ddlOwner')[0].selectize.setValue(modelData.OwnerID);
                    }
                }

                if (modelData.ApproverID > 0) {
                    $('#headlist').val(modelData.ApproverID);
                }

                if (modelData.Priority > 0) {
                    $('select[name="Priority"]').val(modelData.Priority);
                }

                // Set date values
                if (modelData.ValidityDate) {
                    // Convert date format if needed (assuming it comes as dd/MM/yyyy or similar)
                    var validityDate = formatDateForInput(modelData.ValidityDate);
                    $('input[name="ValidityDate"]').val(validityDate);
                }

                if (modelData.RevieweDate) {
                    var reviewDate = formatDateForInput(modelData.RevieweDate);
                    $('input[name="RevieweDate"]').val(reviewDate);
                }

                // Set attachment name display
                console.log('Setting attachment info - AttachmentName:', modelData.AttachmentName);
                if (modelData.AttachmentName) {
                    $('#hiddenAttachmentName').val(modelData.AttachmentName);
                    $('#currentAttachmentName').text(modelData.AttachmentName);
                    $('#currentAttachmentInfo').show();
                    console.log('Attachment info displayed:', modelData.AttachmentName);

                    // Update file input label to show current file
                    updateFileInputLabel(modelData.AttachmentName);
                } else {
                    $('#currentAttachmentInfo').hide();
                    console.log('No attachment found, hiding attachment info');
                }

                console.log('Dropdown and field values set successfully');
            } catch (error) {
                console.error('Error setting dropdown values:', error);
            }
        }

        // Helper function to format date for HTML date input (yyyy-MM-dd)
        function formatDateForInput(dateString) {
            if (!dateString) return '';

            try {
                // Handle different date formats
                var date;
                if (dateString.includes('/')) {
                    // Assume dd/MM/yyyy or MM/dd/yyyy format
                    var parts = dateString.split('/');
                    if (parts.length === 3) {
                        // Try dd/MM/yyyy first
                        date = new Date(parts[2], parts[1] - 1, parts[0]);
                        // If invalid, try MM/dd/yyyy
                        if (isNaN(date.getTime())) {
                            date = new Date(parts[2], parts[0] - 1, parts[1]);
                        }
                    }
                } else if (dateString.includes('-')) {
                    // Assume yyyy-MM-dd format
                    date = new Date(dateString);
                } else {
                    date = new Date(dateString);
                }

                if (isNaN(date.getTime())) {
                    console.warn('Invalid date format:', dateString);
                    return '';
                }

                // Format as yyyy-MM-dd for HTML date input
                var year = date.getFullYear();
                var month = String(date.getMonth() + 1).padStart(2, '0');
                var day = String(date.getDate()).padStart(2, '0');

                return `${year}-${month}-${day}`;
            } catch (error) {
                console.error('Error formatting date:', dateString, error);
                return '';
            }
        }

        // Function to update file input label with current attachment name
        function updateFileInputLabel(attachmentName) {
            if (attachmentName) {
                // Create a custom label showing current file
                var fileInput = $('#attachmentFile');
                var customLabel = `Current: ${attachmentName} (Choose new file to replace)`;
                fileInput.attr('title', customLabel);

                // Add a data attribute to track current file
                fileInput.attr('data-current-file', attachmentName);
            }
        }







        // Handle file input change to show selected file name
        $(document).ready(function () {
            $('#attachmentFile').on('change', function () {
                var fileName = this.files[0] ? this.files[0].name : '';
                if (fileName) {
                    $(this).attr('title', 'Selected: ' + fileName);
                }
            });
        });



        // Helper function to load departments and set a specific value
        function loadDepartmentsAndSetValue(unitID, departmentID) {
            if (!unitID || unitID == "0") {
                console.log('No unit ID provided for department loading');
                return;
            }

            console.log('Loading departments for unit:', unitID, 'and setting department:', departmentID);

            $.ajax({
                url: '@Url.Action("GetAllDepartments", "BCMTrainingMasterConfiguration")',
                type: 'GET',
                data: { iUnitID: unitID },
                success: function (response) {
                    console.log('Departments loaded for setting value:', response);

                    if ($(".ddlDepartment")[0] && $(".ddlDepartment")[0].selectize) {
                        let selectizeInstance = $(".ddlDepartment")[0].selectize;

                        // Clear existing options
                        selectizeInstance.clear();
                        selectizeInstance.clearOptions();

                        // Add default option
                        selectizeInstance.addOption({ value: "0", text: "-- Select Departments --" });

                        // Add department options
                        if (response && Array.isArray(response)) {
                            response.forEach(function(department) {
                                if (department && department.departmentID && department.departmentName) {
                                    selectizeInstance.addOption({
                                        value: department.departmentID,
                                        text: department.departmentName
                                    });
                                }
                            });

                            // Set the specific department value
                            if (departmentID && departmentID > 0) {
                                selectizeInstance.setValue(departmentID);
                                console.log('Department value set to:', departmentID);
                            } else {
                                selectizeInstance.setValue("0");
                            }
                        } else {
                            console.log('No departments found for unit:', unitID);
                            selectizeInstance.setValue("0");
                        }
                    } else {
                        console.error('Department selectize instance not found');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error loading departments for value setting:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);
                }
            });
        }

        // Function to disable all form fields when training is not editable
        function disableFormFields() {
            try {
                // Disable all input fields
                $('#BCMTrainingMasterForm input[type="text"], #BCMTrainingMasterForm input[type="date"], #BCMTrainingMasterForm input[type="file"], #BCMTrainingMasterForm textarea').prop('disabled', true);

                // Disable all select dropdowns
                $('#BCMTrainingMasterForm select').prop('disabled', true);

                // Disable selectize dropdowns if they exist
                $('#BCMTrainingMasterForm select').each(function () {
                    if (this.selectize) {
                        this.selectize.disable();
                    }
                });

                // Add visual indication that form is read-only
                $('#BCMTrainingMasterForm .form-control').addClass('bg-light');
                $('#BCMTrainingMasterForm .form-select').addClass('bg-light');

                // Hide attachment upload/remove buttons
                $('#currentAttachmentInfo button').hide();

                console.log('Form fields disabled due to training status');
            } catch (error) {
                console.error('Error disabling form fields:', error);
            }
        }

        }); // End of $(document).ready

        // Global function to handle back navigation
        function goBack() {
            try {
                // Check if there's a referrer and it's from the same domain
                if (document.referrer && document.referrer.indexOf(window.location.hostname) !== -1) {
                    // Go back to the previous page
                    window.history.back();
                } else {
                    // Fallback: redirect to the training management page
                    window.location.href = '@Url.Action("ManageBCMTrainingForm", "ManageBCMTrainingForm", new { area = "BCMTraining" })';
                }
            } catch (error) {
                console.error('Error in goBack function:', error);
                // Fallback: redirect to the training management page
                window.location.href = '@Url.Action("ManageBCMTrainingForm", "ManageBCMTrainingForm", new { area = "BCMTraining" })';
            }
        }

        // Global function to download current attachment
        function downloadCurrentAttachment() {
            try {
                // Try multiple ways to get TrainingMasterID
                var trainingMasterID = $('input[name="ID"]').val() ||
                                     $('input[name="ID"]').val() ||
                                     $('#TrainingMasterID').val() ||
                                     $('#ID').val() ||
                                     $('input[asp-for="TrainingMasterID"]').val() ||
                                     $('input[asp-for="ID"]').val();

                console.log('Download attempt - TrainingMasterID from input:', trainingMasterID);

                if (!trainingMasterID || trainingMasterID == '0' || trainingMasterID == '') {
                    alert('No training record found. Please save the training first before downloading attachments.');
                    console.error('Invalid TrainingMasterID for download:', trainingMasterID);
                    return;
                }

                // Check if there's actually an attachment to download
                var attachmentName = $('#currentAttachmentName').text();
                if (!attachmentName || attachmentName.trim() === '') {
                    alert('No attachment found to download.');
                    console.error('No attachment name found');
                    return;
                }

                console.log('Downloading attachment:', attachmentName, 'for TrainingMasterID:', trainingMasterID);

                // Create download URL
                var downloadUrl = '@Url.Action("DownloadTrainingAttachment", "BCMTrainingMasterConfiguration")?trainingMasterID=' + trainingMasterID;
                console.log('Download URL:', downloadUrl);

                // Trigger download
                window.location.href = downloadUrl;

            } catch (error) {
                console.error('Error in downloadCurrentAttachment:', error);
                alert('An error occurred while trying to download the attachment. Please try again.');
            }
        }

        // Global function to test download info (for debugging)
        function testDownloadInfo() {
            try {
                console.log('=== DOWNLOAD DEBUG INFO ===');

                // Check TrainingMasterID from various sources
                var trainingMasterID1 = $('input[name="ID"]').val();
                var trainingMasterID2 = $('input[name="ID"]').val();
                var trainingMasterID3 = $('#TrainingMasterID').val();
                var trainingMasterID4 = $('#ID').val();

                console.log('TrainingMasterID from input[name="ID"]:', trainingMasterID1);
                console.log('TrainingMasterID from input[name="ID"]:', trainingMasterID2);
                console.log('TrainingMasterID from #TrainingMasterID:', trainingMasterID3);
                console.log('TrainingMasterID from #ID:', trainingMasterID4);

                // Check attachment info
                var attachmentName = $('#currentAttachmentName').text();
                var hiddenAttachmentName = $('#hiddenAttachmentName').val();
                var attachmentInfoVisible = $('#currentAttachmentInfo').is(':visible');

                console.log('Current attachment name (display):', attachmentName);
                console.log('Hidden attachment name:', hiddenAttachmentName);
                console.log('Attachment info visible:', attachmentInfoVisible);

                // Test download URL
                var finalTrainingMasterID = trainingMasterID1 || trainingMasterID2 || trainingMasterID3 || trainingMasterID4;
                var downloadUrl = '@Url.Action("DownloadTrainingAttachment", "BCMTrainingMasterConfiguration")?trainingMasterID=' + finalTrainingMasterID;
                console.log('Final TrainingMasterID:', finalTrainingMasterID);
                console.log('Download URL would be:', downloadUrl);

                console.log('=== END DEBUG INFO ===');

                // Show alert with key info
                alert('Debug Info:\n' +
                      'TrainingMasterID: ' + finalTrainingMasterID + '\n' +
                      'Attachment Name: ' + attachmentName + '\n' +
                      'Attachment Visible: ' + attachmentInfoVisible + '\n' +
                      'Check console for detailed info');

            } catch (error) {
                console.error('Error in testDownloadInfo:', error);
                alert('Error getting debug info: ' + error.message);
            }
        }

        // Global function to remove current attachment
        function removeCurrentAttachment() {
            if (confirm('Are you sure you want to remove the current attachment?')) {
                var trainingMasterID = $('input[name="TrainingMasterID"]').val() || $('input[name="ID"]').val();
                if (trainingMasterID) {
                    // Call controller method to delete attachment
                    $.ajax({
                        url: '@Url.Action("DeleteTrainingAttachmentByTrainingMasterID", "BCMTrainingMasterConfiguration")',
                        type: 'POST',
                        data: { trainingMasterID: trainingMasterID },
                        success: function (response) {
                            if (response.success) {
                                // Clear the attachment display
                                $('#hiddenAttachmentName').val('');
                                $('#currentAttachmentName').text('');
                                $('#currentAttachmentInfo').hide();
                                $('#attachmentFile').removeAttr('data-current-file').removeAttr('title');

                                alert('Attachment removed successfully.');
                            } else {
                                alert('Failed to remove attachment: ' + response.message);
                            }
                        },
                        error: function () {
                            alert('Error occurred while removing attachment.');
                        }
                    });
                } else {
                    alert('No training record found. Please save the training first.');
                }
            }
        }

    </script>
}