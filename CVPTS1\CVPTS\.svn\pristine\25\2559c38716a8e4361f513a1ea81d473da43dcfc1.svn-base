﻿let queryData = "";
let usedIds = new Set();
let widgettitle = "";
let Xaxis = "";
let Yaxis = "";
let tableArray = "";
let dropHandlerInitialized = false;


async function GetUserList() {
    try {

        const response = await $.ajax({
            url: '/BCMAdministration/DashboardList/GetUserData',
            type: "GET",
            dataType: "json"
        });

        if (response?.success) {
            const $dropdown = $('#userDropdown');

            // Destroy previous instance if already initialized
            if ($dropdown[0].selectize) {
                $dropdown[0].selectize.destroy();
            }

            // Initialize selectize
            $dropdown.selectize({
                valueField: 'userID',
                labelField: 'loginName',
                searchField: 'loginName',
                options: response.data.filter(u => u.loginName && u.userID),
                placeholder: 'Select a user...',
                create: false
            }); 
        } else {
            console.error("Server error:", response.message);

        }

    } catch (error) {
        alert("Something went wrong while fetching dashboard list.");
    }

}

async function loadDashboardList() {
    try {
    
        const response = await $.ajax({
            url: '/BCMAdministration/DashboardList/DashboardBuilderList',
            type: "GET",
            dataType: "json"
        });

        if (response?.success) {
            const tbody = document.getElementById("log-table-body");
     
            if (response.data.length > 0) {
                $("#log-table-body").empty();
                response.data.forEach((item, index) => {
                    let props = {};
                    try {
                        props = JSON.parse(item.properties || "{}");
                    } catch (e) {
                        console.warn("Invalid properties JSON for item:", item, e);
                    }

                    const tr = document.createElement("tr");

                    tr.innerHTML = `
            <td>${index + 1}</td>
            <td>${item.name}</td>         
            <td>${item.description || '-'}</td>
            <td>
              <div class="d-flex align-items-center gap-2">
                <span 
                    class="btn-action btnEdit" type="button"   data-edit='${encodeURIComponent(item.properties)}'>
                  <i class="cv-edit" title="Edit"></i>
                </span>
                <span 
                    class="btn-action btnDelete text-danger" 
                    type="button" 
                    data-bs-toggle="modal" 
                    data-bs-target="#Modal" 
                    data-id="${item.referenceId}">
                  <i class="cv-delete" title="Delete"></i>
                </span>
              </div>
            </td>
        `;
                    tbody.appendChild(tr);
                });
            }      

        } else {
            console.error("Server error:", response.message);
          
        }
 
    } catch (error) {
        alert("Something went wrong while fetching dashboard list.");
    }
    
}
function generateUniqueNumericId() {
    let id;
    do {
        const length = Math.floor(Math.random() * 4) + 4; // Length: 4–7
        const min = Math.pow(10, length - 1);
        const max = Math.pow(10, length) - 1;
        id = Math.floor(Math.random() * (max - min + 1)) + min;
    } while (usedIds.has(id));
    usedIds.add(id);
    return id;
}

async function loadWidgetList() {
    try {

        const response = await $.ajax({
            url: '/BCMAdministration/DashboardList/GetWidgetList',
            type: "GET",
            dataType: "json"
        });

        if (response?.success && Array.isArray(response.data)) {
            const widgetList = response.data;
            const $ul = $(".prebuildreport-leftsidebar ul");
            $ul.empty();           
            widgetList.forEach(widget => {
                const numericId = generateUniqueNumericId();
                const props = JSON.parse(widget.properties || '{}');
                const encodedProps = encodeURIComponent(JSON.stringify(props));

                const listItem = `
                    <li class="report-item dragItem" draggable="true" data-name="${widget.name}" data-props="${encodedProps}"  data-id="${numericId}">
                        <div>
                            <img src="${props.hrefImage || '/img/default-thumbnail.jpg'}" style="max-width: 80%;height: 54px;" alt="${widget.name}">
                         <p class="mb-0 text-truncate" style="max-width: 121px;" title="${widget.name}">${widget.name}</p>
                        </div>
                    </li>
                `;

                $ul.append(listItem);
            })
        }
            else {
            console.error("Server error:", response.message);

        }

    } catch (error) {
        alert("Something went wrong while fetching dashboard list.");
    }

}

async function loadDashboard(properties) {
    const widgetManager = window.WidgetManager;

    if (!widgetManager.grid) {
        widgetManager.initializeGrid();
    } else {
        widgetManager.grid.removeAll(); // Clear current widgets
    }

    properties.forEach(async prop => {
        if (prop.type === "empty-card") {
            // Create empty card
            const $card = $(`
                <div class="dashboard-widget bcm-box card grid-stack-item" gs-w="${prop.width}" gs-h="${prop.hight}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="bcm-title" contenteditable="false">${prop.title}</div>
                    </div>
                    <div class="card-body">
                        <div class="empty-card-body" style="min-height: 150px;">
                            <div class="nested-grid grid-stack"></div>
                        </div>
                    </div>
                    <div class="widget-toolbar text-end p-2">
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-rename-empty-card" title="Rename Card">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-delete-empty-card" title="Delete Card">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `);

            const gridItem = widgetManager.grid.addWidget($card[0], {
                id: prop.id,
                x: prop.x,
                y: prop.y,
                w: prop.width,
                h: prop.hight
            });

            const nestedGridEl = $card.find('.nested-grid')[0];
            const nestedGrid = GridStack.init({
                cellHeight: 70,
                margin: 5,
                disableResize: false,
                disableDrag: false,
                float: false,
                column: 12
            }, nestedGridEl);

            prop.children.forEach(child => {
                queryData = []; // Or reload query here if needed

                const contentHtml = widgetManager.getWidgetContent(child.type, null);
                const widgetHtml = $(`
                    <div class="grid-stack-item" gs-w="${child.width}" gs-h="${child.hight}">
                        <div class="grid-stack-item-content mini-widget p-2 border rounded shadow-sm"
                             data-widget-type="${child.type}"
                             data-widgetquery="${child.query || ''}"
                             data-widgetxaxis="${child.widgetXaxis || ''}"
                             data-widgetyaxis="${child.widgetYaxis || ''}"
                             ${child.type === 'table' && child.tableArray ? `data-widgettablearray='${encodeURIComponent(JSON.stringify(child.tableArray))}'` : ''}
                             style="resize: both; overflow: auto; min-height: 150px;">
                            <div id="BodyContainer">${contentHtml}</div>
                        </div>
                        <div class="widget-toolbar text-end p-2">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-danger btn-delete-widget" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `);

                widgetHtml.find('.btn-delete-widget').on('click', function () {
                    nestedGrid.removeWidget($(this).closest('.grid-stack-item')[0]);
                });

                nestedGrid.addWidget(widgetHtml[0]);

                if (child.type.startsWith("amchart-")) {
                    const chartId = $(contentHtml).find('[id^="amchart-"]').attr('id');
                    setTimeout(() => {
                        const chartDiv = document.getElementById(chartId);
                        const $container = $(chartDiv).closest('.grid-stack-item-content');
                        widgetManager.initializeAmChart($container, child.type);
                    }, 100);
                }
            });
        }
        else {  
        
            if (prop.type == "table") {
                tableArray = prop.tableArray;                
                
            } else {
                Xaxis = prop.widgetXaxis;
                Yaxis = prop.widgetYaxis;
            }
            if (prop.query) {
                try {
                    const response = await $.ajax({
                        url: '/BCMAdministration/DashboardList/getQueryData',
                        type: "GET",
                        data: { query: prop.query },
                        dataType: "json"
                    });

                    if (response?.success && typeof response.data === "string") {
                        queryData = JSON.parse(response.data);
                    }
                } catch (error) {
                    alert("Something went wrong while fetching dashboard list.");
                }
            }

            widgetManager.createWidget(prop.type, prop.title, prop.id, prop.query, Xaxis, Yaxis, tableArray, {
                x: prop.x,
                y: prop.y,
                w: prop.width,
                h: prop.hight
            });
        }
    });
}

$(document).ready(async function () {

    await loadDashboardList();

    $(".btnEdit").on("click", async function () {
        const encodedProps = $(this).attr('data-edit');
        const decodedProps = decodeURIComponent(encodedProps);
        const properties = JSON.parse(decodedProps);

        $("#dashboardListModal").addClass('d-none');
        $("#createModalDashboardList").removeClass('d-none');
        await loadWidgetList();
  
        const dragItem = document.querySelectorAll('.dragItem');
        const dropZone = document.getElementById('dashboard-grid');

        dragItem.forEach(item => {
            item.setAttribute('draggable', 'true');
            item.addEventListener('dragstart', function (e) {
                e.dataTransfer.clearData();
                e.dataTransfer.setData('props', item.getAttribute('data-props'));
                e.dataTransfer.setData('Id', item.getAttribute('data-id'));
                e.dataTransfer.setData('title', item.getAttribute('data-name'));
            });
        });

        dropZone.addEventListener('dragover', function (e) {
            e.preventDefault();
        });

        dropZone.addEventListener('drop', async function (e) {
            e.preventDefault();
            const itemProps = JSON.parse(decodeURIComponent(e.dataTransfer.getData('props')));
            Xaxis = itemProps.xaxis;
            Yaxis = itemProps.yaxis;
            widgettitle = e.dataTransfer.getData('title');

            if (itemProps.datasetstoredQuery) {
                try {
                    const response = await $.ajax({
                        url: '/BCMAdministration/DashboardList/getQueryData',
                        type: "GET",
                        data: { query: itemProps.datasetstoredQuery },
                        dataType: "json"
                    });

                    if (response?.success && typeof response.data === "string") {
                        queryData = JSON.parse(response.data);
                    }
                } catch (error) {
                    alert("Something went wrong while fetching dashboard list.");
                }
            }

            const widgetManager = window.WidgetManager;
            if (!widgetManager.grid) widgetManager.initializeGrid();
            const widgetId = `widget-${Date.now()}-${Math.floor(Math.random() * 10000)}`;

            if (itemProps.WidgetType === 'table') {
                widgetManager.createWidget("table", widgettitle, widgetId, itemProps.datasetstoredQuery, "", "", tableArray);
            } else if (itemProps.ChartType === 'line') {
                widgetManager.createWidget("amchart-line", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            } else if (itemProps.ChartType === 'bar') {
                widgetManager.createWidget("amchart-bar", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            } else if (itemProps.ChartType === 'pie') {
                widgetManager.createWidget("amchart-pie", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            } else if (itemProps.ChartType === 'donut') {
                widgetManager.createWidget("amchart-donut", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            }
        });

        loadDashboard(properties);
    });


    //$(".btnEdit").on("click", async function () {
    //        // Get encoded data-edit attribute
    //    const encodedProps = $(this).attr('data-edit');

    //    const dragItem = document.querySelectorAll('.dragItem');
    //    const dropZone = document.getElementById('dashboard-grid');


    //        // Decode and parse the properties JSON
    //        const decodedProps = decodeURIComponent(encodedProps);
    //        const properties = JSON.parse(decodedProps);


    //        $("#dashboardListModal").addClass('d-none');
    //        $("#createModalDashboardList").removeClass('d-none');
    //        await loadWidgetList();

    //    dragItem.forEach(item => {
    //        item.setAttribute('draggable', 'true');
    //        item.addEventListener('dragstart', function (e) {
    //            e.dataTransfer.clearData();
    //            e.dataTransfer.setData('props', item.getAttribute('data-props'));
    //            e.dataTransfer.setData('Id', item.getAttribute('data-id'));
    //            e.dataTransfer.setData('title', item.getAttribute('data-name'));
    //        });
    //    });
    //        dropZone.addEventListener('dragover', function (e) {
    //            e.preventDefault();
    //        });

    //        dropZone.addEventListener('drop', async function (e) {
    //            e.preventDefault();
    //            const itemProps = JSON.parse(decodeURIComponent(e.dataTransfer.getData('props')));
    //            Xaxis = itemProps.xaxis;
    //            Yaxis = itemProps.yaxis;
    //            widgettitle = e.dataTransfer.getData('title');
    //            if (itemProps.datasetstoredQuery) {
    //                try {
    //                    const response = await $.ajax({
    //                        url: '/BCMAdministration/DashboardList/getQueryData',
    //                        type: "GET",
    //                        data: { query: itemProps.datasetstoredQuery },
    //                        dataType: "json"
    //                    });

    //                    if (response?.success && typeof response.data === "string") {

    //                        queryData = JSON.parse(response.data);
    //                    }
    //                    else {
    //                        console.error("Server error:", response.message);
    //                    }

    //                } catch (error) {
    //                    alert("Something went wrong while fetching dashboard list.");
    //                }
    //            }



    //            const widgetManager = new WidgetManager();
    //            widgetManager.initializeGrid();
    //            const widgetId = `widget-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    //            gridOptions = null;

    //            if (itemProps.WidgetType == 'table') {
    //                widgetManager.createWidget("table", widgettitle, widgetId, itemProps.datasetstoredQuery, "", "", tableArray)
    //            }
    //          else if (itemProps.ChartType === 'line') {

    //                widgetManager.createWidget("amchart-line", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
    //            } else if (itemProps.ChartType === 'bar') {
    //                widgetManager.createWidget("amchart-bar", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
    //            } else if (itemProps.ChartType === 'pie') {
    //                widgetManager.createWidget("amchart-pie", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
    //            } else {
    //                console.warn("Unknown chart type:", itemProps.ChartType);
    //            }


    //        });

    //        loadDashboard(properties);
        
    //});

    $("#addEmptyCardBtn").on("click", function () {
        const widgetId = `widget-${Date.now()}`;
        const widgetManager = window.WidgetManager;

        if (!widgetManager.grid) {
            widgetManager.initializeGrid();
        }

        // Create the empty card with nested grid
        const emptyCard = $(`
    <div class="dashboard-widget bcm-box card grid-stack-item" gs-w="6" gs-h="6">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="bcm-title" contenteditable="false">Empty Card</div>
        </div>
        <div class="card-body">
            <div class="empty-card-body" style="min-height: 150px;">
                <div class="nested-grid grid-stack"></div>
            </div>
        </div>
        <div class="widget-toolbar text-end p-2" style="top:-1px;">
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-secondary btn-rename-empty-card" title="Rename Card">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-outline-danger btn-delete-empty-card" title="Delete Card">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    </div>
`);

        // Append to main dashboard grid
        const gridItem = widgetManager.grid.addWidget(emptyCard[0], {
            id: widgetId,
            x: 0, y: 0, w: 6, h: 6
        });

        // Initialize nested GridStack
        const nestedGridEl = emptyCard.find('.nested-grid')[0];
        const nestedGrid = GridStack.init({
            cellHeight: 70,
            margin: 5,
            disableResize: false,
            disableDrag: false,
            float: false,
            column: 12
        }, nestedGridEl);

 
        emptyCard.find('.btn-delete-empty-card').on('click', function () {
            const $card = $(this).closest('.grid-stack-item');
      
                widgetManager.grid.removeWidget($card[0]);
                window.DashboardStorage?.showNotification?.('Empty card deleted', 'success');
            
        });

        emptyCard.find('.btn-rename-empty-card').on('click', function () {
            const $title = $(this).closest('.dashboard-widget').find('.bcm-title');

            // Enable contenteditable and focus
            $title.attr('contenteditable', 'true').focus();

            // Optional: Add visual cue
            $title.addClass('editing-title');

            // Save on Enter or Blur
            $title.on('keydown', function (e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); 
                    $(this).blur();
                }
            });

            $title.on('blur', function () {
                const newText = $(this).text().trim();

                if (newText === '') {
                    $(this).text('Untitled Card');
                }

                $(this).removeAttr('contenteditable').removeClass('editing-title');
                $(this).off('keydown');
                $(this).off('blur');
                window.DashboardStorage?.showNotification?.('Card renamed', 'success');
            });
        });
        // Setup drop on nested grid
        nestedGridEl.addEventListener("dragover", function (e) {
            e.preventDefault();
            this.classList.add("highlight-drop");
        });

        nestedGridEl.addEventListener("dragleave", function () {
            this.classList.remove("highlight-drop");
        });

        nestedGridEl.addEventListener("drop", async function (e) {
            e.preventDefault();
            this.classList.remove("highlight-drop");

            const itemProps = JSON.parse(decodeURIComponent(e.dataTransfer.getData('props')));
             widgettitle = e.dataTransfer.getData('title');
            const query = itemProps.datasetstoredQuery || "";
            if (itemProps.hasOwnProperty('tableArray')) {
                tableArray = itemProps.tableArray;
            } else if (itemProps.hasOwnProperty('xaxis')) {
                Xaxis = itemProps.xaxis;
                Yaxis = itemProps.yaxis;
            }
            let chartData = null;
            if (query) {
                try {
                    const response = await $.ajax({
                        url: '/BCMAdministration/DashboardList/getQueryData',
                        type: "GET",
                        data: { query },
                        dataType: "json"
                    });
                    if (response?.success && typeof response.data === "string") {
                        chartData = JSON.parse(response.data);
                    }
                } catch (err) {
                    console.error("Query fetch failed:", err);
                }
            }

            const type = itemProps.WidgetType === 'table'
                ? 'table'
                : itemProps.ChartType ? `amchart-${itemProps.ChartType}` : null;

            if (!type) {
                console.warn('Unknown widget type dropped');
                return;
            }

            // Check max widget limit
            if (nestedGrid.engine.nodes.length >= 5) {
                alert("Only 5 widgets are allowed inside this card.");
                return;
            }

            queryData = chartData || [];
            const tableArrayAttr = Array.isArray(tableArray) && tableArray.length > 0
                ? `data-widgettableArray='${encodeURIComponent(JSON.stringify(tableArray))}'`
                : "";

            const queryAttr = query ? `data-widgetquery="${query}"` : "";
            const xAxisAttr = Xaxis ? `data-widgetXaxis="${Xaxis}"` : "";
            const yAxisAttr = Yaxis ? `data-widgetYaxis="${Yaxis}"` : "";
            const typeAttr = type ? `data-widget-type="${type}"` : "";

            const contentHtml = widgetManager.getWidgetContent(type, null);

            const widgetHtml = $(`
            <div class="grid-stack-item" gs-w="6" gs-h="4">
                <div class="grid-stack-item-content mini-widget p-2 border rounded shadow-sm" ${tableArrayAttr}  ${queryAttr}  ${xAxisAttr}  ${yAxisAttr}  ${typeAttr} style="resize: both; overflow: auto; min-height: 150px;">            
                   
                <div id="BodyContainer">
                    ${contentHtml}
                </div>     
                </div>
                  <div class="widget-toolbar text-end p-2">
                <div class="btn-group btn-group-sm" role="group">                
                    <button type="button" class="btn btn-outline-danger btn-delete-widget" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            </div>
        `);
            widgetHtml.find('.btn-delete-widget').on('click', function () {
                const $gridItem = $(this).closest('.grid-stack-item');
                nestedGrid.removeWidget($gridItem[0]);
            });
            nestedGrid.addWidget(widgetHtml[0]);

            // Optional: initialize amChart inside the dropped widget
            if (type.startsWith("amchart-")) {
                const $html = $('<div>').append(contentHtml);
                const chartId = $html.find('[id^="amchart-"]').attr("id");

                if (chartId) {
                    setTimeout(() => {
                        const chartDiv = document.getElementById(chartId);
                        const $container = $(chartDiv).closest('.grid-stack-item-content');
                        widgetManager.initializeAmChart($container, type);
                    }, 100);
                } else {
                    console.warn("Chart ID not found for initialization.");
                }
            }
        });
    });

      $("#dashboardListCreate").on("click", async function () {
        queryData = "";
        usedIds = new Set();
        widgettitle = "";
        Xaxis = "";
        Yaxis = "";
        tableArray = "";


        $("#dashboardListModal").addClass('d-none');
        $("#createModalDashboardList").removeClass('d-none');
        await loadWidgetList();

        const dragItem = document.querySelectorAll('.dragItem');
        const dropZone = document.getElementById('dashboard-grid');
        dragItem.forEach(item => {
            item.addEventListener('dragstart', function (e) {
                e.dataTransfer.clearData();
                e.dataTransfer.setData('props', item.getAttribute('data-props'));
                e.dataTransfer.setData('Id', item.getAttribute('data-id'));
                e.dataTransfer.setData('title', item.getAttribute('data-name'));
            });
        });
        dropZone.addEventListener('dragover', function (e) {
            e.preventDefault();
        });


        if (!dropHandlerInitialized) {
            dropHandlerInitialized = true;
            dropZone.addEventListener('drop', async function (e) {
                e.preventDefault();
                if (e.target.closest('.nested-grid')) return;
            const itemProps = JSON.parse(decodeURIComponent(e.dataTransfer.getData('props')));

            if (itemProps.hasOwnProperty('tableArray')) {
                tableArray = itemProps.tableArray;
            } else if (itemProps.hasOwnProperty('xaxis')) {
                Xaxis = itemProps.xaxis;
                Yaxis = itemProps.yaxis;
            }

            widgettitle = e.dataTransfer.getData('title');
            if (itemProps.datasetstoredQuery) {
                try {
                    const response = await $.ajax({
                        url: '/BCMAdministration/DashboardList/getQueryData',
                        type: "GET",
                        data: { query: itemProps.datasetstoredQuery },
                        dataType: "json"
                    });

                    if (response?.success && typeof response.data === "string") {

                        queryData = JSON.parse(response.data);
                    }
                    else {
                        console.error("Server error:", response.message);
                    }

                } catch (error) {
                    alert("Something went wrong while fetching dashboard list.");
                }
            }



            const widgetManager = new WidgetManager();
            widgetManager.initializeGrid();
            const widgetId = `widget-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
            gridOptions = null;
       
            if (itemProps.WidgetType == 'table') {
                widgetManager.createWidget("table", widgettitle, widgetId, itemProps.datasetstoredQuery, "", "", tableArray)
            }
           else if (itemProps.ChartType === 'line') {
                widgetManager.createWidget("amchart-line", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            }
             else if (itemProps.ChartType === 'bar') {
                widgetManager.createWidget("amchart-bar", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            } else if (itemProps.ChartType === 'pie') {
                widgetManager.createWidget("amchart-pie", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            } else if (itemProps.ChartType === 'donut') {
                widgetManager.createWidget("amchart-donut", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            } else {
                console.warn("Unknown chart type:", itemProps.ChartType);
            }


        });

    }
    });


    $("#backBtn").on("click", async function () {
        $('#dashboard-grid .dashboard-widget').each(function () {
            const gridItem = $(this).closest('.grid-stack-item'); // Ensure parent is removed from grid
            if (gridItem.length) {
                // Remove from gridstack if using GridStack instance
                const grid = GridStack.init(); // Or use your existing grid reference
                grid.removeWidget(gridItem[0]);
            } else {
                // Fallback: just remove the element
                $(this).remove();
            }
        });
        $("#createModalDashboardList").addClass('d-none');
        $("#dashboardListModal").removeClass('d-none');      
    });

    $("#saveBtn").on("click", async function () {
        await GetUserList();
        $("#AddModal").modal('show');
    });

    $("#dashboardBuilderSaveBtn").on("click", async function () {
        const properties = [];

        $('#dashboard-grid > .grid-stack-item').each(function () {
            const $item = $(this);
            const isEmptyCard = $item.find('.nested-grid').length > 0;

            const widgetId = $item.attr('gs-id');
            const x = parseInt($item.attr('gs-x'));
            const y = parseInt($item.attr('gs-y'));
            const width = parseInt($item.attr('gs-w'));
            const height = parseInt($item.attr('gs-h'));
            const title = $item.find('.bcm-title').text().trim();

            if (isEmptyCard) {
                const nestedWidgets = [];

                // Loop over child widgets inside nested grid
                $item.find('.nested-grid .grid-stack-item').each(function () {
                    const $nestedWidget = $(this);
                    const nestedContent = $nestedWidget.find('.grid-stack-item-content');

                    const nestedWidgetType = nestedContent.data('widget-type');
                    const nestedQuery = nestedContent.data('widgetquery');
                    const nestedX = parseInt($nestedWidget.attr('gs-x'));
                    const nestedY = parseInt($nestedWidget.attr('gs-y'));
                    const nestedW = parseInt($nestedWidget.attr('gs-w'));
                    const nestedH = parseInt($nestedWidget.attr('gs-h'));

                    const nestedWidget = {
                        id: null,
                        type: nestedWidgetType,
                        query: nestedQuery,
                        x: nestedX,
                        y: nestedY,
                        width: nestedW,
                        hight: nestedH
                    };

                    if (nestedWidgetType === 'table') {
                        const encodedArray = nestedContent.attr('data-widgettablearray');
                        nestedWidget.tableArray = encodedArray ? JSON.parse(decodeURIComponent(encodedArray)) : [];

                    } else {
                        nestedWidget.widgetXaxis = nestedContent.data('widgetxaxis');
                        nestedWidget.widgetYaxis = nestedContent.data('widgetyaxis');
                    }

                    nestedWidgets.push(nestedWidget);
                });

                properties.push({
                    id: widgetId,
                    type: 'empty-card',
                    title,
                    x,
                    y,
                    width,
                    hight: height,
                    children: nestedWidgets
                });

            } else {
                const widgetType = $item.data('widget-type');
                const widgetQuery = $item.data('widgetquery');

                const baseProps = {
                    id: widgetId,
                    type: widgetType,
                    query: widgetQuery,
                    x,
                    y,
                    width,
                    hight: height,
                    title
                };

                if (widgetType === 'table') {
                    const encodedArray = $item.attr('data-widgettablearray');
                    const tableArray = encodedArray ? JSON.parse(decodeURIComponent(encodedArray)) : [];

                    baseProps.tableArray = tableArray;
                } else {
                    baseProps.widgetXaxis = $item.data('widgetxaxis');
                    baseProps.widgetYaxis = $item.data('widgetyaxis');
                }

                properties.push(baseProps);
            }
        });

        const dashboardData = {
            Id: null,
            ReferenceId: "45",
            Name: $("#dashboardName").val(),
            CreatedBy: "admin",
            CreatedDate: new Date().toISOString(),
            LastModifiedBy: "admin",
            LastModifiedDate: new Date().toISOString(),
            IsActive: true,
            IsPublish: true,
            IsLock: false,
            Properties: JSON.stringify(properties),
            UserName: $('#userDropdown option:selected').text(),
            UserId: $('#userDropdown').val(),
            Description: $('#description').val()
        };

        try {
            const response = await $.ajax({
                url: '/BCMAdministration/DashboardList/SaveDashboardBuilder',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(dashboardData),
            });

            if (response.success) {
                $("#AddModal").modal('hide');
                alert(response.message);
            } else {
                alert("Failed: " + response.message);
            }
        } catch (err) {
            console.error("Error saving dashboard:", err);
            alert("Unexpected error while saving dashboard.");
        }
    });


    //$("#dashboardBuilderSaveBtn").on("click", async function () {

    //    const properties = [];

    //    $('#dashboard-grid .grid-stack-item').each(function () {
    //        const $widget = $(this);

    //        const widgetType = $widget.data('widget-type');
    //        const widgetQuery = $widget.data('widgetquery');
    //        const widgetId = $widget.attr('gs-id');
    //        const x = parseInt($widget.attr('gs-x'));
    //        const y = parseInt($widget.attr('gs-y'));
    //        const width = parseInt($widget.attr('gs-w'));
    //        const hight = parseInt($widget.attr('gs-h'));
    //        const title = $widget.find('#headerName').text().trim();

    //        const baseProps = {
    //            id: widgetId,
    //            type: widgetType,
    //            query: widgetQuery,
    //            x,
    //            y,
    //            width,
    //            hight,
    //            title
    //        };

    //        if (widgetType === 'table') {
    //            const encodedArray = $widget.attr('data-widgettablearray');
    //            const tableArray = encodedArray ? JSON.parse(decodeURIComponent(encodedArray)) : [];

    //            baseProps.tableArray = tableArray; // attach only for table
    //        } else {
    //            baseProps.widgetXaxis = $widget.data('widgetxaxis');
    //            baseProps.widgetYaxis = $widget.data('widgetyaxis');
    //        }

    //        properties.push(baseProps);
    //    });

    //    const dashboardData = {
    //        Id: null, 
    //        ReferenceId:"45",
    //        Name: $("#dashboardName").val(),           
    //        CreatedBy: "admin", 
    //        CreatedDate: new Date().toISOString(),
    //        LastModifiedBy: "admin",
    //        LastModifiedDate: new Date().toISOString(),
    //        IsActive: true,
    //        IsPublish:true,
    //        IsLock: false,
    //        Properties: JSON.stringify(properties),
    //        UserName: $('#userDropdown option:selected').text(),
    //        UserId: $('#userDropdown').val(),
    //        Description: $('#description').val()
    //     };

    //        try {
    //            const response = await $.ajax({
    //                url: '/BCMAdministration/DashboardList/SaveDashboardBuilder',
    //                type: 'POST',
    //                contentType: 'application/json', 
    //                data: JSON.stringify(dashboardData), 
    //            });


    //            if (response.success) {
    //            $("#AddModal").modal('hide');
    //            alert(response.message); 
    //        } else {
    //            alert("Failed: " + response.message); // error
    //        }
    //    } catch (err) {
    //        console.error("Error saving dashboard:", err);
    //        alert("Unexpected error while saving dashboard.");
    //    }
    //});


    $('#widghtSearch').on('keyup', function () {
        const searchTerm = $(this).val().toLowerCase();
        $('.prebuildreport-leftsidebar ul li').each(function () {
            const itemText = $(this).text().toLowerCase();
            $(this).toggle(itemText.indexOf(searchTerm) > -1);
        });
    });

    setTimeout(() => {
        window.WidgetManager.initializeAllAmCharts();
    }, 2000);


});
