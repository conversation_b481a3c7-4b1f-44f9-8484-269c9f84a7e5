﻿// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

console.log('ReviewMeetings_Chart.js loaded - v3');

// Create chart instance
var reviewMeetingsChart = am4core.create("ReviewMeetings_Chart", am4charts.XYChart);
if (reviewMeetingsChart.logo) {
    reviewMeetingsChart.logo.disabled = true;
}

// Make chart responsive
reviewMeetingsChart.responsive.enabled = true;
reviewMeetingsChart.responsive.rules.push({
    relevant: function (target) {
        if (target.pixelWidth <= 400) {
            return true;
        }
        return false;
    },
    state: function (target, stateId) {
        if (target instanceof am4charts.Chart) {
            var state = target.states.create(stateId);
            state.properties.paddingTop = 0;
            state.properties.paddingRight = 15;
            state.properties.paddingBottom = 5;
            state.properties.paddingLeft = 15;
            return state;
        }
        if (target instanceof am4charts.AxisRendererY) {
            var state = target.states.create(stateId);
            state.properties.inside = true;
            state.properties.maxLabelPosition = 0.99;
            return state;
        }
        return null;
    }
});


reviewMeetingsChart.data = [{
    "category": "Complete",
    "value": 7,
}, {
    "category": "Initiated",
    "value": 4,
}, {
    "category": "Scheduled",
    "value": 6,
    }];


// Set chart padding to ensure labels fit - reduced padding
reviewMeetingsChart.paddingTop = 0;
reviewMeetingsChart.paddingBottom = 0;
reviewMeetingsChart.paddingLeft = 0;
reviewMeetingsChart.paddingRight = 0;

// Reduce background area
reviewMeetingsChart.background.fill = am4core.color("#ffffff");
reviewMeetingsChart.background.fillOpacity = 0;

// Create axes
var categoryAxis = reviewMeetingsChart.xAxes.push(new am4charts.CategoryAxis());
categoryAxis.dataFields.category = "category";
categoryAxis.renderer.grid.template.location = 0;
categoryAxis.renderer.labels.template.fontSize = 12;
categoryAxis.renderer.minGridDistance = 30;
categoryAxis.renderer.grid.template.disabled = true;

var valueAxis = reviewMeetingsChart.yAxes.push(new am4charts.ValueAxis());
valueAxis.min = 0;
valueAxis.max = 100; // Set y-axis range from 0 to 100 as requested
valueAxis.strictMinMax = true;
valueAxis.renderer.labels.template.fontSize = 12;
valueAxis.renderer.grid.template.strokeOpacity = 0; // Remove grid lines
valueAxis.renderer.minGridDistance = 50; // Increased for better spacing with larger range
valueAxis.renderer.grid.template.disabled = true;

// Create series
var series = reviewMeetingsChart.series.push(new am4charts.ColumnSeries());
series.dataFields.valueY = "value";
series.dataFields.categoryX = "category";
series.columns.template.tooltipText = "{categoryX}: {valueY}";
series.columns.template.propertyFields.fill = "color";
series.columns.template.strokeWidth = 0;
series.columns.template.column.cornerRadiusTopLeft = 25;
series.columns.template.column.cornerRadiusTopRight = 25;

// Make columns wider for better visibility
series.columns.template.width = am4core.percent(30);





// Add labels on top of columns
var labelBullet = series.bullets.push(new am4charts.LabelBullet());
labelBullet.label.text = "{valueY}";
labelBullet.label.fill = am4core.color("#000000");
labelBullet.label.fontSize = 12;
labelBullet.label.fontWeight = "bold";
labelBullet.label.dy = -5;
labelBullet.locationY = 1;

// Add background to labels
labelBullet.label.background = new am4core.RoundedRectangle();
labelBullet.label.background.fill = am4core.color("#ffffff");
labelBullet.label.background.fillOpacity = 0.7;
//labelBullet.label.background.cornerRadius(3, 3, 3, 3);
labelBullet.label.padding(5, 5, 5, 5);
