﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Org.BouncyCastle.Asn1.Ocsp;
using static BCM.Shared.BCPEnum;

namespace BCM.UI.Areas.BCMProcessBIAForms.Controllers;
[Area("BCMProcessBIAForms")]
public class BIAFacilityController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    int iBIAID = 0;
    int iProcessID = 0;
    int iSectionID = 0;
    int iIsBCMEntity = 0;
    public BIAFacilityController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    [HttpGet]
    public IActionResult BIAFacility(string strBIAID, string strProcessID, string strSectionID, string strIsBCMEntity)
    {
        List<ProcessBIAFacility> lstProcessBIAFacility = new List<ProcessBIAFacility>();
        try
        {

            iBIAID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strBIAID));
            iProcessID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strProcessID));
            iSectionID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strSectionID));
            iIsBCMEntity = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strIsBCMEntity));

            HttpContext.Session.SetString("SectionID", iSectionID.ToString());
            HttpContext.Session.SetString("ProcessID", iProcessID.ToString());
            HttpContext.Session.SetString("IsBCMEntity", iIsBCMEntity.ToString());
            HttpContext.Session.SetString("BIAID", iBIAID.ToString());

            BusinessProcessInfo objBusinessProcess = new BusinessProcessInfo();
            objBusinessProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(iProcessID.ToString() == "" ? "0" : iProcessID), (int)BCPEnum.EntityType.BusinessProcess);
            ViewBag.ProcessName = objBusinessProcess.ProcessName;
            ViewBag.ProcessCode = objBusinessProcess.ProcessCode;
            ViewBag.ProcessVersion = objBusinessProcess.ProcessVersion;

            ViewBag.Facility = new SelectList(_Utilities.GetFacilityListByUnitID(_UserDetails.OrgID), "FacilityID", "FacilityName");
            ViewBag.FacilityType = new SelectList(_Utilities.PopulatFacilityType(), "FacilityID", "FacilityType");
            ViewBag.Impact = new SelectList(_Utilities.PopulateBiaSectionImpactStatus(), "ImpactID", "ImpactName");
            ViewBag.Questions = _ProcessSrv.GetBIASurveyQuestionListBySectionID(Convert.ToInt32(HttpContext.Session.GetString("SectionID")));

            BIASection objBIASection = _ProcessSrv.GetBIASurveySectionById(iSectionID, _UserDetails.OrgID);
            ViewBag.Description = objBIASection.SectionDescription;


            lstProcessBIAFacility = GetBIAFacility();

            GetRecordIDByProcessID();

            ViewBag.ButtonAccess = _Utilities.ShowButtonsByAccess((objBusinessProcess.Status).ToString(), objBusinessProcess.ApproverID.ToString(), objBusinessProcess.ProcessOwnerID.ToString(),
                                                        objBusinessProcess.AltProcessOwnerID.ToString(), _UserDetails.UserID.ToString(),
                                                        Convert.ToInt32(BCPEnum.PrivilegeID.Modify));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstProcessBIAFacility);
    }



    public List<ProcessBIAFacility> GetBIAFacility()
    {
        string IsCriticals = string.Empty;
        var lstProcessBIAFacility = new List<ProcessBIAFacility>();
        try
        {
            lstProcessBIAFacility = _ProcessSrv.ProcessBIAFacilityGetByBIAID(Convert.ToInt32(HttpContext.Session.GetString("BIAID")));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return null;
        }
        return lstProcessBIAFacility;
    }



    private void GetRecordIDByProcessID()
    {
        BusinessProcessInfo objBusinessProcessInfo = new BusinessProcessInfo();
        try
        {
            objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), (int)BCPEnum.EntityType.BusinessProcess);
            HttpContext.Session.SetString("RecordID", objBusinessProcessInfo.RecordID.ToString());
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }



    [HttpPost]
    public IActionResult AddOrEditBIAFacility(ProcessBIAFacility objProcessBIAFacility)
    {
        ProcessBIAFacility bIAProcessBIAFacility = new ProcessBIAFacility();

        int IsComplete = 0;
        bool bSuccess = false;
        string strError = string.Empty;
        try
        {
            if (Convert.ToInt32(objProcessBIAFacility.FacilityID) > 0 || Convert.ToInt32(objProcessBIAFacility.FacilityType) > 0)
            {
                IsComplete = 1;
            }
            else
            {
                IsComplete = 0;
            }
            GetCurrentBIAID();
            bIAProcessBIAFacility.ID = objProcessBIAFacility.ID;
            bIAProcessBIAFacility.FacilityID = objProcessBIAFacility.FacilityID;
            bIAProcessBIAFacility.FacilityType = objProcessBIAFacility.FacilityType;
            bIAProcessBIAFacility.QuestionID = objProcessBIAFacility.QuestionID;
            bIAProcessBIAFacility.BIAID = Convert.ToInt32(HttpContext.Session.GetString("BIAID"));
            bIAProcessBIAFacility.CreatedBy = _UserDetails.UserID;
            bIAProcessBIAFacility.ChangedBy = _UserDetails.UserID;
            bIAProcessBIAFacility.iBIAFindings = objProcessBIAFacility.iBIAFindings;
            bIAProcessBIAFacility.Impact = objProcessBIAFacility.Impact;
            bIAProcessBIAFacility.TotalPlatformByHCLoacation = objProcessBIAFacility.TotalPlatformByHCLoacation;
            bIAProcessBIAFacility.TeamDistribution = objProcessBIAFacility.TeamDistribution;
            bIAProcessBIAFacility.CurrentProcessSLA = objProcessBIAFacility.CurrentProcessSLA;
            bIAProcessBIAFacility.MemberConnectFromHome = objProcessBIAFacility.MemberConnectFromHome;
            bIAProcessBIAFacility.ResourceMovedToDRSite = objProcessBIAFacility.ResourceMovedToDRSite;
            bIAProcessBIAFacility.ResourceMovedToAnotherLocation = objProcessBIAFacility.ResourceMovedToAnotherLocation;
            bIAProcessBIAFacility.ResourceMovedToEGSLocations = objProcessBIAFacility.ResourceMovedToEGSLocations;
            bIAProcessBIAFacility.IncreasingShiftTiming = objProcessBIAFacility.IncreasingShiftTiming;
            bIAProcessBIAFacility.IsComplete = IsComplete;

            bSuccess = _ProcessSrv.ProcessBIAFacilityInfoSaveandUpdate(bIAProcessBIAFacility);


        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            if (objProcessBIAFacility.ID > 0)
            {
                return Json(new { success = bSuccess, message = bSuccess ? /*objOrgInfo.OrgInfo.OrganizationName +*/ "Facility Updated Successfully" : "Failed To Update Facility." });
            }
            else
            {
                return Json(new { success = bSuccess, message = bSuccess ? /*objOrgInfo.OrgInfo.OrganizationName +*/ "Facility Added Successfully" : "Failed To Add Facility." });
            }
        }
        return RedirectToAction("BIAFacility", new
        {
            strBIAID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")),
            strProcessID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID")),
            strSectionID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID")),
            strIsBCMEntity = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity"))

        });
    }


    //[HttpPost]
    //public IActionResult AddOrEditBIAFacility(ProcessBIAFacility objProcessBIAFacility)
    //{
    //    ProcessBIAFacility bIAProcessBIAFacility = new ProcessBIAFacility();

    //    int IsComplete = 0;
    //    bool iSuccess;
    //    string strError = string.Empty;
    //    try
    //    {
    //        if (Convert.ToInt32(objProcessBIAFacility.FacilityID) > 0 || Convert.ToInt32(objProcessBIAFacility.FacilityType) > 0)
    //        {
    //            IsComplete = 1;
    //        }
    //        else
    //        {
    //            IsComplete = 0;
    //        }
    //        bIAProcessBIAFacility.ID = objProcessBIAFacility.ID;
    //        bIAProcessBIAFacility.FacilityID = objProcessBIAFacility.FacilityID;
    //        bIAProcessBIAFacility.FacilityType = objProcessBIAFacility.FacilityType;
    //        bIAProcessBIAFacility.QuestionID = objProcessBIAFacility.QuestionID;
    //        bIAProcessBIAFacility.BIAID = GetCurrentBIAID();
    //        bIAProcessBIAFacility.CreatedBy = _UserDetails.UserID;
    //        bIAProcessBIAFacility.ChangedBy = _UserDetails.UserID;
    //        bIAProcessBIAFacility.iBIAFindings = objProcessBIAFacility.iBIAFindings;
    //        bIAProcessBIAFacility.Impact = objProcessBIAFacility.Impact;
    //        bIAProcessBIAFacility.TotalPlatformByHCLoacation = objProcessBIAFacility.TotalPlatformByHCLoacation;
    //        bIAProcessBIAFacility.TeamDistribution = objProcessBIAFacility.TeamDistribution;
    //        bIAProcessBIAFacility.CurrentProcessSLA = objProcessBIAFacility.CurrentProcessSLA;
    //        bIAProcessBIAFacility.MemberConnectFromHome = objProcessBIAFacility.MemberConnectFromHome;
    //        bIAProcessBIAFacility.ResourceMovedToDRSite = objProcessBIAFacility.ResourceMovedToDRSite;
    //        bIAProcessBIAFacility.ResourceMovedToAnotherLocation = objProcessBIAFacility.ResourceMovedToAnotherLocation;
    //        bIAProcessBIAFacility.ResourceMovedToEGSLocations = objProcessBIAFacility.ResourceMovedToEGSLocations;
    //        bIAProcessBIAFacility.IncreasingShiftTiming = objProcessBIAFacility.IncreasingShiftTiming;
    //        bIAProcessBIAFacility.IsComplete = IsComplete;

    //        iSuccess = _ProcessSrv.ProcessBIAFacilityInfoSaveandUpdate(objProcessBIAFacility);
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }
    //    return RedirectToAction("BIAFacility", new
    //    {
    //        strBIAID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")),
    //        strProcessID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID")),
    //        strSectionID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID")),
    //        strIsBCMEntity = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity"))
    //    });
    //}
    private int GetCurrentBIAID()
    {
        BIASection objBIASection = new BIASection();
        int iBIAIDForBIADependentEntities = Convert.ToInt32(HttpContext.Session.GetString("BIAID"));
        try
        {
            if (iBIAIDForBIADependentEntities == 0)
            {
                objBIASection.Version = "1.0";
                objBIASection.VersionChangeDescription = "";
                objBIASection.ApprovalStatus = ((int)BCPEnum.ApprovalType.Initiated).ToString();
                objBIASection.ProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
                objBIASection.SectionID = Convert.ToInt32(HttpContext.Session.GetString("SectionID"));
                objBIASection.IsBCMEntity = 0;
                objBIASection.IsEffective = 1;
                objBIASection.CreatedBy = _UserDetails.UserID;
                objBIASection.ChangedBy = _UserDetails.UserID;
                iBIAIDForBIADependentEntities = _ProcessSrv.ProcessBIASectionSave(objBIASection);
                HttpContext.Session.SetString("BIAID", iBIAIDForBIADependentEntities.ToString());
                ViewBag.BIAID = iBIAIDForBIADependentEntities;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return iBIAIDForBIADependentEntities;
    }

    [HttpGet]
    public IActionResult EditBIAFacility(int iID)
    {
        ProcessBIAFacility objProcessBIAFacility = new ProcessBIAFacility();
        try
        {
            if (iID > 0)
            {
                objProcessBIAFacility = _ProcessSrv.ProcessBIAFacilityGetByID(iID);
            }
            return Json(objProcessBIAFacility);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return BadRequest("Error occurred");
        }
    }


    [HttpGet]
    public IActionResult DeleteProcessBIAFacility(int iID)
    {
        ProcessBIAFacility objProcessBIAFacility = new ProcessBIAFacility();
        try
        {
            if (iID > 0)
            {
                objProcessBIAFacility = _ProcessSrv.ProcessBIAFacilityGetByID(iID);
            }            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    
        return PartialView("_DeleteProcessBIAFacility", objProcessBIAFacility);       
    }

    [HttpPost]
    public IActionResult DeleteProcessBIAFacility(ProcessBIAFacility objProcessBIAFacility)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.ProcessBIAFacilityInfoDeleteById(objProcessBIAFacility.ID, _UserDetails.UserID);
            if (bSuccess)
            {
                int iBIAIDAfterDelete = _ProcessSrv.ProcessBIAUpdateByBIAID(Convert.ToInt32(HttpContext.Session.GetString("BIAID")), _UserDetails.UserID);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? /*objOrgInfo.OrgInfo.OrganizationName +*/ "Facility Deleted Successfully" : "Failed To Delete Facility." });            
        }
        return RedirectToAction("BIAFacility", new
        {
            strBIAID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")),
            strProcessID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID")),
            strSectionID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID")),
            strIsBCMEntity = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity"))
        });
    }

    [HttpGet]
    public IActionResult GetFacilities_ByID(int iID)
    {
        Facility objFacility = new Facility();

        objFacility = _ProcessSrv.GetFacilitiesById(iID);

        return Json(objFacility);
    }
}

