﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Serilog;
using System.Collections.Generic;
using System.Security.Cryptography;

namespace BCM.UI.Areas.OrgStructure.Controllers;
[Area("BCMOrgStructure")]
public class SubDepartmentController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public SubDepartmentController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }    

    public IActionResult ManageSubDepartment()
    {
        List<SubFunction> lstSubFunction = new List<SubFunction>();
        try
        {
            PopulateDropdown();
            ViewBag.UserOrgID = _UserDetails.OrgID.ToString();
            lstSubFunction = _ProcessSrv.GetSubFunctionList_New(_UserDetails.OrgID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstSubFunction);
    }

    [HttpGet]
    public IActionResult AddSubDepartment()
    {
        try
        {
            PopulateDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_AddSubDepartment", new SubFunction());
    }

    [HttpPost]
    public IActionResult AddSubDepartment(SubFunction objSubFunction)
    {
        int iSubFunctionID = 0;
        bool bSuccess = false;
        try
        {
            iSubFunctionID = _ProcessSrv.SubFunctionSave(objSubFunction, _UserDetails.UserID);
            bSuccess = iSubFunctionID > 0 ? true : false;
            if (iSubFunctionID > 0)
            {
                #region "SubDeptLevelUser"
                List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                OrgRoleRights objrights = new OrgRoleRights();
                DepartmentInfo objDepartmentInfo = _ProcessSrv.GetDepartmentBydeptId(Convert.ToInt32(objSubFunction.DepartmentID));
                
                objrights.UserID = string.IsNullOrEmpty(objSubFunction.OwnerId) ? 0 : Convert.ToInt32(objSubFunction.OwnerId);//Enter access for  SubDept Head 
                objrights.CreatedBy = _UserDetails.UserID;
                objrights.UpdatedBy = _UserDetails.UserID;
                objrights.OrgGroupID = (_UserDetails.OrgGroupID.Equals(null) || _UserDetails.OrgGroupID.ToString() == "") ? 0 : _UserDetails.OrgGroupID;
                objrights.OrgID = (_UserDetails.OrgID.Equals(null) || _UserDetails.OrgID.ToString() == "") ? 0 : _UserDetails.OrgID;
                objrights.UnitID = string.IsNullOrEmpty(objDepartmentInfo.UnitID.ToString()) ? 0 : Convert.ToInt32(objDepartmentInfo.UnitID);
                objrights.DeptID = string.IsNullOrEmpty(objSubFunction.DepartmentID) ? 0 : Convert.ToInt32(objSubFunction.DepartmentID);
                objrights.SubDeptID = iSubFunctionID < 0 ? 0 : iSubFunctionID;
                objrightscoll.Add(objrights);
                int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                objrights.UserID = string.IsNullOrEmpty(objSubFunction.AlternateOwnerId) ? 0 : Convert.ToInt32(objSubFunction.AlternateOwnerId);//Enter access for Alternate SubDept Head
                ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                objrights.UserID = _UserDetails.UserID;//Enter access for CreatedBy
                ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                #endregion
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objSubFunction.SubFunctionName + " Added Successfully" : "Failed To Add Sub Department." });
        }
        return RedirectToAction("ManageSubDepartment");
    }


    [HttpGet]
    public IActionResult EditSubDepartment(string iId)
    {
        var objSubFunction = new SubFunction();
        try
        {
            PopulateDropdown();
            objSubFunction = _ProcessSrv.GetSubFunctionBySubFunctionId(iId);
            ViewBag.DepartmentInfo = new SelectList(_Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), objSubFunction.UnitID.ToString()), "DepartmentID", "DepartmentName");
            //ViewBag.selectedOrgID = objSubFunction.OrgID;
            //ViewBag.selectedUnitID = objSubFunction.UnitID;
            //ViewBag.selectedDepartmentID = objSubFunction.DepartmentID;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_EditSubDepartment", objSubFunction);
    }

    [HttpPost]
    public IActionResult EditSubDepartment(SubFunction objSubFunction)
    {
        bool bSuccess = false;
        int iSubFunctionID = 0;
        try
        {
            bSuccess = _ProcessSrv.SubFunctionUpdate(objSubFunction, _UserDetails.UserID);
            if (bSuccess)
            {
                iSubFunctionID = Convert.ToInt32(objSubFunction.SubFunctionID);
                #region "SubDeptLevelUser"
                List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                OrgRoleRights objrights = new OrgRoleRights();
                
                objrights.UserID = string.IsNullOrEmpty(objSubFunction.OwnerId) ? 0 : Convert.ToInt32(objSubFunction.OwnerId);//Enter access for  SubDept Head 
                objrights.CreatedBy = _UserDetails.UserID;
                objrights.UpdatedBy = _UserDetails.UserID;
                objrights.OrgGroupID = (_UserDetails.OrgGroupID.Equals(null) || _UserDetails.OrgGroupID.ToString() == "") ? 0 : _UserDetails.OrgGroupID;
                objrights.OrgID = (_UserDetails.OrgID.Equals(null) || _UserDetails.OrgID.ToString() == "") ? 0 : _UserDetails.OrgID;
                objrights.UnitID = string.IsNullOrEmpty(objSubFunction.UnitID) ? 0 : Convert.ToInt32(objSubFunction.UnitID);
                objrights.DeptID = string.IsNullOrEmpty(objSubFunction.DepartmentID) ? 0 : Convert.ToInt32(objSubFunction.DepartmentID);
                objrights.SubDeptID = iSubFunctionID < 0 ? 0 : iSubFunctionID;
                objrightscoll.Add(objrights);
                int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                objrights.UserID = string.IsNullOrEmpty(objSubFunction.AlternateOwnerId) ? 0 : Convert.ToInt32(objSubFunction.AlternateOwnerId);//Enter access for Alternate SubDept Head
                ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                objrights.UserID = _UserDetails.UserID;//Enter access for CreatedBy
                ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                #endregion
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objSubFunction.SubFunctionName + " Updated Successfully" : "Failed To Update Sub Department." });
        }
        return RedirectToAction("ManageSubDepartment");
    }


    [HttpGet]
    public IActionResult DeleteSubDepartment(string iId)
    {
        var objSubFunction = new SubFunction();

        try
        {
            objSubFunction = _ProcessSrv.GetSubFunctionBySubFunctionId(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteSubDepartment", objSubFunction);
    }

    [HttpPost]
    public IActionResult DeleteSubDepartment(SubFunction objSubFunction)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.SubFunctionDelete(Convert.ToInt32(objSubFunction.SubFunctionID), _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objSubFunction.SubFunctionName + " Deleted Successfully" : "Failed To Delete." });
        }
        return RedirectToAction("ManageSubDepartment");
    }

    public IActionResult GetResourceDetails(int iId)
    {
        try
        {
            var objResourcesInfo = _ProcessSrv.GetResourcesByResourceID(iId);
            if (objResourcesInfo != null)
            {
                return Json(new { mail = objResourcesInfo.CompanyEmail, mobile = objResourcesInfo.MobilePhone });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return Json(null);
    }


    public IActionResult GetDepartmentByID(int iDepartmentId)
    {
        try
        {
            List<SubFunction> lstSubFunction = _ProcessSrv.GetSubFunctionList_New();
            if (iDepartmentId > 0)
            {
                lstSubFunction = lstSubFunction.Where(x => Convert.ToInt32(x.DepartmentID) == iDepartmentId).ToList();
                if (lstSubFunction == null || !lstSubFunction.Any())
                {
                    //return NotFound("No Records Found.");
                    return PartialView("_FilterSubDepartment", lstSubFunction);
                }
            }
            return PartialView("_FilterSubDepartment", lstSubFunction);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageSubDepartment");
    }

    public IActionResult GetsubDepartmentByID(int iSubDepartmentId)
    {
        try
        {
            List<SubFunction> lstSubFunction = _ProcessSrv.GetSubFunctionList_New();
            if (iSubDepartmentId > 0)
            {
                lstSubFunction = lstSubFunction.Where(x => Convert.ToInt32(x.SubFunctionID) == iSubDepartmentId).ToList();
                if (lstSubFunction == null || !lstSubFunction.Any())
                {
                    //return NotFound("No Records Found.");
                    return PartialView("_FilterSubDepartment", lstSubFunction);
                }
            }
            return PartialView("_FilterSubDepartment", lstSubFunction);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageSubDepartment");
    }

    [HttpGet]
    public JsonResult GetAllUnits(int iOrgID)
    {
        try
        {
            //var objDepartmentList = _Utilities.BindUnit(iOrgID);
            var objDepartmentList = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());

            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID)
    {
        try
        {
            //var objDepartmentList = _Utilities.BindFunction(iUnitID);
            var objDepartmentList = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(),_UserDetails.UserRoleID.ToString(), iUnitID.ToString());

            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllSubDepartments(int iDepartmentID)
    {
        try
        {
            //var objSubDepartmentList = _Utilities.BindSubFunction(iDepartmentID);
            var objSubDepartmentList = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID.ToString());

            return Json(objSubDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    public IActionResult GetUnitByID(int iUnitId)
    {
        try
        {
            //List<DepartmentInfo> objDepartmentList = _ProcessSrv.GetDepartmentByUnitId(iUnitId);
            List<SubFunction> lstSubFunction = _ProcessSrv.GetSubFunctionList_New();
            if (iUnitId > 0)
            {
                lstSubFunction = lstSubFunction.Where(x => Convert.ToInt32(x.UnitID) == iUnitId).ToList();
                if (lstSubFunction == null || !lstSubFunction.Any())
                {
                    //return NotFound("No Records Found.");
                    return PartialView("_FilterSubDepartment", lstSubFunction);
                }
            }
            return PartialView("_FilterSubDepartment", lstSubFunction);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageSubDepartment");
    }

    public IActionResult GetFileredApplication(int OrgID = 0, int UnitID = 0, int DepartmentID = 0)
    {
        List<SubFunction> lstSubFunction = new List<SubFunction>();
        try
        {
            lstSubFunction = _ProcessSrv.GetSubFunctionList_New();
            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    

                }
                else
                {

                    
                }
            }

            string strFilter = "x => 1==1";
            
                if (OrgID > 0)
                {
                    strFilter = " && x => x.OrgID == " + OrgID;
                    lstSubFunction = lstSubFunction.Where(x => x.OrgID == OrgID.ToString()).ToList();
                }
                if (UnitID > 0)
                {
                    strFilter = " && x => x.UnitID == " + UnitID;
                    lstSubFunction = lstSubFunction.Where(x => x.UnitID == UnitID.ToString()).ToList();
                }
                if (DepartmentID > 0)
                {
                    strFilter = " && x => x.DepartmentID == " + DepartmentID;
                    lstSubFunction = lstSubFunction.Where(x => x.DepartmentID == DepartmentID.ToString()).ToList();
                }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_FilterSubDepartment", lstSubFunction);
    }

    public void PopulateDropdown()
    {
        try
        {
            ViewBag.OrgUnit = new SelectList(_Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString()), "UnitID", "UnitName");
            ViewBag.DepartmentInfo = new SelectList(_Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString()), "DepartmentID", "DepartmentName");

            ViewBag.SubFunction = new SelectList(_Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString()),"SubFunctionID", "SubFunctionName");
            ViewBag.OrgInfo = new SelectList(_Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString()), "Id", "OrganizationName"); ;
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetResources(_UserDetails.OrgID), "ResourceId", "ResourceName");
            ViewBag.OrgGroup = new SelectList(_Utilities.GetOrgGroupList(), "OrgGroupID", "OrganizationGroupName");
            ///ViewBag.DepartmentInfo = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString());
            //ViewBag.SubFunction = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString());
            //ViewBag.OrgInfo = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());
            //ViewBag.ResourcesInfo = _Utilities.GetAllResourceList();
            //ViewBag.OrgUnit = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString());
            //ViewBag.OrgGroup = _Utilities.GetOrgGroupList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
}

