﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;


namespace BCM.UI.Areas.BCMAdministration.Controllers
{
    [Area("BCMAdministration")]
    public class DashboardListController : BaseController
    {
        private ProcessSrv _ProcessSrv;
        readonly Utilities _Utilities;
        readonly CVLogger _CVLogger;
        public DashboardListController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
        {
            _ProcessSrv = ProcessSrv;
            _Utilities = Utilities;
            _CVLogger = CVLogger;
        }
        public IActionResult DashboardList()
        {
            return View();
        }


        [HttpGet]
        public IActionResult DashboardBuilderList()
        {
            List<DashboardInfo> lstWidgetBuilder = new List<DashboardInfo>();
            try
            {
                //PopulateDropDown();

                lstWidgetBuilder = _ProcessSrv.GetDashboardBuilderList();

                return Json(new { success = true, data = lstWidgetBuilder });
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                return Json(new { success = false, Message = ex });
            }

        }


        [HttpPost]
        public IActionResult SaveDashboardBuilder([FromBody] DashboardInfo objInfo)
        {

            try
            {
                int iSuccess = _ProcessSrv.DashboardBuilder_Save_Upadte(objInfo);

                // Correcting the syntax for JsonResult usage
                return Json(new { success = true, Message = "Dashboard save successfuly" });

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);

                return Json(new { success = false, Message = ex });
            }

        }


    }
}
