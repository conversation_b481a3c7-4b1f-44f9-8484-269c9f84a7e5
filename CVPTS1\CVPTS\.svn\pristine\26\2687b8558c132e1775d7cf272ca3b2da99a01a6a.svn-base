﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class ImpactMasterController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    ManageUsersDetails? _UserDetails = new ManageUsersDetails();
    readonly CVLogger _CVLogger;

    public ImpactMasterController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult ImpactMaster()
    {
        List<Impact> lstImpact = new List<Impact>();

        try
        {
            PopulateDropdown();
            lstImpact = _ProcessSrv.GetImpactList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View(lstImpact);
    }


    [HttpGet]
    public IActionResult AddImpact()
    {
        try
        {
            PopulateDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_AddImpact", new Impact());
    }

    [HttpPost]
    public IActionResult AddImpact(Impact objImpact)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.ImapctSave(objImpact, _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ImpactMaster");
    }


    [HttpGet]
    public IActionResult EditImpact(int iId)
    {
        var objImpact = new Impact();

        try
        {
            PopulateDropdown();
            objImpact = _ProcessSrv.GetImpactByID(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }


        return PartialView("_EditImpact", objImpact);
    }

    [HttpPost]
    public IActionResult EditImpact(Impact objImpact)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.ImpactUpdate(objImpact, _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ImpactMaster");
    }


    [HttpGet]
    public IActionResult DeleteImpact(int iId)
    {
        var objImpact = new Impact();

        try
        {
            objImpact = _ProcessSrv.GetImpactByID(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteImpact", objImpact);
    }

    [HttpPost]
    public IActionResult DeleteImpact(Impact objImpact)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.ImpactDelete(objImpact.ImpactID, _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ImpactMaster");
    }


    public void PopulateDropdown()
    {
        try
        {
            ViewBag.ImpactCategory = new SelectList(_Utilities.BindImpactType(), "ImpactTypeID", "ImpactTypeName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
}

