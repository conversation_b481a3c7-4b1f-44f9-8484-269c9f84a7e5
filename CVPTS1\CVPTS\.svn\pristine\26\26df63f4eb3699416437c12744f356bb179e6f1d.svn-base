﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .table > :not(caption) > * > * {
    
        padding: .3rem .3rem;
    }
</style>

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">
     Business Parameter
    </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <a class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</a>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>
<div class="Page-Condant  border-0">
    <div class="card-body" style="height:calc(100vh - 140px);overflow:auto;overflow-x:hidden">


        <div class="row g-2">
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item text-muted"><small>Parameter ID</small></li>
                                    <li class="list-group-item fw-bold">
                                        BP-2017-1
                                    </li>
                                </ul>
                            </div>

                            <span>
                                <span type="button"><i class="cv-edit" title="Edit"></i></span>
                                <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>

                            </span>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td class="text-muted">Parameter Name</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Move resource to other EGS location</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Category</td>
                                    <td>:</td>
                                    <td>
                                        Objective
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Description</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Move resource to other EGS location</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Cost as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Time as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" />
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item text-muted"><small>Parameter ID</small></li>
                                    <li class="list-group-item fw-bold">
                                        BP-2017-2
                                    </li>
                                </ul>
                            </div>

                            <span>
                                <span type="button"><i class="cv-edit" title="Edit"></i></span>
                                <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>

                            </span>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td class="text-muted">Parameter Name</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Move resource to other EGS location</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Category</td>
                                    <td>:</td>
                                    <td>
                                        Objective
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Description</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Move resource to other EGS location</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Cost as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked/>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Time as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked />
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </div>
            </div>    
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item text-muted"><small>Parameter ID</small></li>
                                    <li class="list-group-item fw-bold">
                                        BP-2017-3
                                    </li>
                                </ul>
                            </div>

                            <span>
                                <span type="button"><i class="cv-edit" title="Edit"></i></span>
                                <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>

                            </span>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td class="text-muted">Parameter Name</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Move resource to other location</span>

                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Category</td>
                                    <td>:</td>
                                    <td>
                                        Sensitivity
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Description</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Move resource to other location</span>

                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Cost as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked/>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Time as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked />
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item text-muted"><small>Parameter ID</small></li>
                                    <li class="list-group-item fw-bold">
                                        BP-2017-4
                                    </li>
                                </ul>
                            </div>

                            <span>
                                <span type="button"><i class="cv-edit" title="Edit"></i></span>
                                <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>

                            </span>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td class="text-muted">Parameter Name</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">People WFH</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Category</td>
                                    <td>:</td>
                                    <td>
                                        Sensitivity
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Description</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">People WFH</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Cost as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Time as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked />
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item text-muted"><small>Parameter ID</small></li>
                                    <li class="list-group-item fw-bold">
                                        BP-2017-5
                                    </li>
                                </ul>
                            </div>

                            <span>
                                <span type="button"><i class="cv-edit" title="Edit"></i></span>
                                <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>

                            </span>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td class="text-muted">Parameter Name</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Increasing shift time at DR Site</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Category</td>
                                    <td>:</td>
                                    <td>
                                        Objective
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Description</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Increasing shift time at DR Site</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Cost as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Time as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked />
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item text-muted"><small>Parameter ID</small></li>
                                    <li class="list-group-item fw-bold">
                                        BP-2017-6
                                    </li>
                                </ul>
                            </div>

                            <span>
                                <span type="button"><i class="cv-edit" title="Edit"></i></span>
                                <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>

                            </span>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td class="text-muted">Parameter Name</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Increasing shift time at unimpacted site</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Category</td>
                                    <td>:</td>
                                    <td>
                                        Objective
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Parameter Description</td>
                                    <td>:</td>
                                    <td>
                                        <span class="text-truncate" style="max-width:90%;display:inline-block">Increasing shift time at unimpacted site</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Cost as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Time as Input</td>
                                    <td>:</td>
                                    <td>
                                        <input type="checkbox" class="form-check" checked />
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>



</div>
<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Business Parameter Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-6">
                        <div class="form-group">
                            <label class="form-label">Parameter ID</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-parameter"></i></span>
                                <input class="form-control" type="text" placeholder="Enter Parameter ID" />
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label class="form-label">Parameter Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-page-name"></i></span>
                                <input class="form-control" type="text" placeholder="Enter Parameter Name" />
                            </div>
                        </div>

                    </div>
                    <div class="col-12">
                        <div class="form-group w-100">
                            <label class="form-label">Parameter Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <textarea class="form-control" placeholder="Parameter Description" style="height:0px"></textarea>
                            </div>
                        </div>

                    </div>
                    <div class="col-6">
                        <div class="form-group w-100">
                            <label class="form-label">Parameter Category</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-home"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select Parameter Category</option>
                                    <option></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 d-flex align-items-end">
                        <div class="form-group w-100">
                            <label class="form-label">&nbsp;</label>
                            <div class="input-group">

                                <div class="form-check form-check-inline">
                                    <label class="form-check-label" for="inlineCheckbox1">: Cost as Input</label>
                                    <input class="form-check-input" type="checkbox" id="inlineCheckbox1" value="option1">
                                </div>
                                <div class="form-check form-check-inline">
                                    <label class="form-check-label" for="inlineCheckbox2">: Time as Input</label>
                                    <input class="form-check-input" type="checkbox" id="inlineCheckbox2" value="option2">
                                </div>
                               
                            </div>
                        </div>
                    </div>
                </div>

            </div> <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/delete.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->