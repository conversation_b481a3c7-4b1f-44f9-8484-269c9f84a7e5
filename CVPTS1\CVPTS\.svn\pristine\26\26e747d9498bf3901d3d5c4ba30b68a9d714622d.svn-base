﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BCM.UI.Areas.BCMViewInfo.Controllers;
[Area("BCMViewInfo")]
public class ViewApplicationsController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    List<ProcessBIAApplicationInfo> lstProcessBIAApplication = new List<ProcessBIAApplicationInfo>();
    public ViewApplicationsController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult ViewApplications()
    {
        try
        {
            PopulateDropdowns();
            GetTotalApplications(_UserDetails.OrgID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
    }

    public void PopulateDropdowns()
    {
        try
        {
            ViewBag.selectedOrgID = _UserDetails.OrgID;
            //Organization
            ViewBag.OrgName = new SelectList(_Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.UserRoleID.ToString()), "Id", "OrganizationName");
            //Unit
            ViewBag.Unit = new SelectList(_Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString()), "UnitID", "UnitName");
            //Department
            ViewBag.Department = new SelectList(_Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString()), "DepartmentID", "DepartmentName");
            //SubDepartment
            ViewBag.lstSubDepartment = new SelectList(_Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString()), "SubFunctionID", "SubFunctionName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public void GetTotalApplications(int iOrgID = 0, int iUnitID = 0, int iDepartmetID = 0, int iSubDepartmentID = 0)
    {
        List<ProcessBIAThirdParty> newProcessBIAApplicationInfoList = new List<ProcessBIAThirdParty>();
        try
        {
            lstProcessBIAApplication = _ProcessSrv.GetCriticalApplication_New(iOrgID, iUnitID, iDepartmetID, iSubDepartmentID);                        

            if (lstProcessBIAApplication.Count > 0)
            {
                foreach (ProcessBIAApplicationInfo objProcessBIAThirdParty in lstProcessBIAApplication)
                {
                    newProcessBIAApplicationInfoList.Add(new ProcessBIAApplicationInfo
                    {
                        ThirdPartyID = objProcessBIAThirdParty.ThirdPartyID,
                        ThirdPartyName = objProcessBIAThirdParty.ThirdPartyName,
                        ProcessOwnerName = objProcessBIAThirdParty.ProcessOwnerName,
                        OwnerMail = objProcessBIAThirdParty.OwnerMail,
                        OwnerMobile = objProcessBIAThirdParty.OwnerMobile,
                        ProcessID = objProcessBIAThirdParty.ProcessID,
                        ProcessName = objProcessBIAThirdParty.ProcessName,
                        ProcessCode = objProcessBIAThirdParty.ProcessCode,
                        OrgID = objProcessBIAThirdParty.OrgID,
                        OrganizationName = objProcessBIAThirdParty.OrganizationName,
                        UnitID = objProcessBIAThirdParty.UnitID,
                        UnitName = objProcessBIAThirdParty.UnitName,
                        DepartmentID = objProcessBIAThirdParty.DepartmentID,
                        DepartmentName = objProcessBIAThirdParty.DepartmentName,
                        SubfunctionID = objProcessBIAThirdParty.SubfunctionID,
                        SubFunctionName = objProcessBIAThirdParty.SubFunctionName,
                        MinimumRTO = GetMinimumRTOByApplicationID(objProcessBIAThirdParty.ThirdPartyID),
                        MinimumRPO = GetMinimumRPOByApplicationID(objProcessBIAThirdParty.ThirdPartyID),
                        OwnerRTO = objProcessBIAThirdParty.OwnerRTO,
                        RPO = objProcessBIAThirdParty.RPO,
                        IsCritical = objProcessBIAThirdParty.IsCritical,
                        StatusID = objProcessBIAThirdParty.StatusID,
                        Version = objProcessBIAThirdParty.Version,
                    });
                }
            }

            ViewBag.ProcessList = newProcessBIAApplicationInfoList;
            var filteredList = newProcessBIAApplicationInfoList.DistinctBy(x => x.ThirdPartyID);

            ViewBag.ApplicationsList = filteredList;
            ViewBag.TotalCount = filteredList.Count();
            ViewBag.CriticalCount = filteredList.Count(x => x.IsCritical == 1);
            ViewBag.NonCriticalCount = filteredList.Count(x => x.IsCritical == 0);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public int GetMinimumRTOByApplicationID(int itrThirdPartyId)
    {
        return _ProcessSrv.GetMinimumRTOByApplicationID(itrThirdPartyId);
    }

    public int GetMinimumRPOByApplicationID(int itrThirdPartyId)
    {
        return _ProcessSrv.GetMinimumRPOByApplicationID(itrThirdPartyId);
    }

    public JsonResult BindFunction(int iUnitID = 0)
    {
        try
        {
            var objDepartmentInfo = _Utilities.BindFunction(iUnitID);
            return Json(objDepartmentInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    public JsonResult BindSubFunction(int iDepartmetID)
    {
        try
        {
            var objSubDepartmentInfo = _Utilities.BindSubFunction(iDepartmetID);
            return Json(objSubDepartmentInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    public PartialViewResult BindFilteredList(int iOrgID, int iUnitID, int iDepartmetID, int iSubDepartmentID)
    {
        try
        {
            GetTotalApplications(iOrgID, iUnitID, iDepartmetID, iSubDepartmentID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_ApplicationsList");
    }
}

