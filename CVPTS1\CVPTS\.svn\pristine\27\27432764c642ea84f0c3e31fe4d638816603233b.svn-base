﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Linq;
using System.Text;
using static BCM.Shared.BCPEnum;

namespace BCM.UI.Areas.BCMProcessBIAForms.Controllers;
[Area("BCMProcessBIAForms")]
public class BIAQualitativeImpactMatrixController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    int iBIAID = 0;
    int iProcessID = 0;
    int iSectionID = 0;
    int iIsBCMEntity = 0;
    int iBpProfileID = 0;
    List<BIAProfileMaster> lstBIAProfileMaster = new List<BIAProfileMaster>();

    public BIAQualitativeImpactMatrixController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult BIAQualitativeImpactMatrix(string strSectionID, string strProcessID, string strIsBCMEntity, string strBIAID)
    {
        try
        {
            #region Page_Init Methods

            iBIAID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strBIAID));
            iProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
            iSectionID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strSectionID));
            iIsBCMEntity = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strIsBCMEntity));
            BusinessProcessInfo objBusinessProcessInfo = new BusinessProcessInfo();
            if (iIsBCMEntity == 0)
            {
                objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(iProcessID, 1);
                iBpProfileID = objBusinessProcessInfo.ProfileID;
            }

            if (iIsBCMEntity == 1)
            {
                BCMEntityInfo objBCMEntityInfo = _ProcessSrv.GetBCMEntityByEntityID(iProcessID);
                iBpProfileID = Convert.ToInt32(objBCMEntityInfo.ProfileID);
            }

            HttpContext.Session.SetString("SectionID", iSectionID.ToString());
            HttpContext.Session.SetString("IsBCMEntity", iIsBCMEntity.ToString());
            HttpContext.Session.SetString("BIAID", iBIAID.ToString());

            ViewBag.ProcessNameWithCode = HttpContext.Session.GetString("ProcessNameWithCode") == null ? "" : HttpContext.Session.GetString("ProcessNameWithCode");

            ViewBag.ImpactSeverity = new SelectList(_Utilities.PopulateBIAImpactSeverityType(), "ImpSeverityID", "ImpSeverityValue");
            ViewBag.ProcessName = objBusinessProcessInfo.ProcessName;
            ViewBag.ProcessCode = objBusinessProcessInfo.ProcessCode;
            ViewBag.ProcessVersion = objBusinessProcessInfo.ProcessVersion;
            GetBIAProfileDetails_ForMatrix(iProcessID, iBpProfileID);
            GenerateDataGridView();
            GetMTRRatingMAOAndConfiguredRTO();

            #endregion

            #region Page_Load Methods

            ViewBag.ImpactSeverityId = GetHighestSeverity();

            AssignDataToMatrix();

            ViewBag.ButtonAccess = _Utilities.ShowButtonsByAccess((objBusinessProcessInfo.Status).ToString(), objBusinessProcessInfo.ApproverID.ToString(), objBusinessProcessInfo.ProcessOwnerID.ToString(),
                                                        objBusinessProcessInfo.AltProcessOwnerID.ToString(), _UserDetails.UserID.ToString(),
                                                        Convert.ToInt32(BCPEnum.PrivilegeID.Create));
            #endregion

            ViewBag.ButtonAccess = _Utilities.ShowButtonsByAccess((objBusinessProcessInfo.Status).ToString(),
                objBusinessProcessInfo.ApproverID.ToString(), objBusinessProcessInfo.ProcessOwnerID.ToString(),
                objBusinessProcessInfo.AltProcessOwnerID.ToString(), _UserDetails.UserID.ToString(), Convert.ToInt32(BCPEnum.PrivilegeID.Create));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
    }

    private void AssignDataToMatrix()
    {
        Dictionary<(int rowId, int colId), int> ddlValues = new Dictionary<(int rowId, int colId), int>();
        try
        {
            List<Impact> lstImpact = _ProcessSrv.BIAProfileImpactTypesByProfID(iBpProfileID);

            var timeIntervalIds = lstBIAProfileMaster.DistinctBy(x => x.TimeIntervalID).Select(u => u.TimeIntervalID).ToArray();
            foreach (Impact objImpact in lstImpact)
            {
                for (int i = 0; i < timeIntervalIds.Length; i++)
                {
                    if (objImpact.ImpactID > 0 && timeIntervalIds[i] > 0 && ViewBag.ImpactSeverityId > 0)
                    {
                        string strCost = GetRTOData(objImpact.ImpactID, timeIntervalIds[i], ViewBag.ImpactSeverityId);
                        if (string.IsNullOrEmpty(strCost))
                        {
                            strCost = "0";
                        }
                        ddlValues.Add((objImpact.ImpactID, timeIntervalIds[i]), Convert.ToInt32(strCost));
                    }
                }                
            }

            ViewBag.DropDownValues = ddlValues;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public class RTOMAOCalculation
    {
        public int iRTO { get; set; }
        public int iMTO { get; set; }
        public int iConfiguredRTO { get; set; }
        public string? strIsCritical { get; set; }
        public string? strCalculatedRTO { get; set; }
    }

    [HttpPost]
    public string BIAQualitativeImpactMatrix([FromBody] RTOMAOCalculation objRTOMAOCalculation)
    {
        StringBuilder sb = new StringBuilder();
        try
        {
            bool bIsCritical = (objRTOMAOCalculation.iRTO <= objRTOMAOCalculation.iConfiguredRTO && objRTOMAOCalculation.iRTO > 0);
            //string strMessage = bIsCritical ? "This BCM Entity is Critical RTO is - " + _Utilities.GetFormattedRTO_New(iRTO) : "This BCM Entity is Not Critical RTO is - " + _Utilities.GetFormattedRTO_New(iRTO);
            
            sb.Append(objRTOMAOCalculation.strIsCritical.Equals("Yes") ? "This BCM Entity " : "This BCM Entity ");
            sb.Append("RTO is - " + objRTOMAOCalculation.strCalculatedRTO + ", do you wish to save this RTO?");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return sb.ToString();
    }

    [HttpGet]
    public JsonResult Confirmation(int iRowCount, int iSelectedVal, int iDDLImpactSeverity, string strTimeIntevalIds, string strRowWiseValues, int iCalculateRTO, int iCalculateMTO)
    {
        bool bSuccess = false;
        try
        {
            ViewBag.ImpactSeverityId = iDDLImpactSeverity;
            int iHighest = GetHighestSeverity();

            if (iDDLImpactSeverity >= iHighest)
            {
                bSuccess = GetDataFromGridToCollection(iRowCount, iSelectedVal, iDDLImpactSeverity, strTimeIntevalIds, strRowWiseValues, iCalculateRTO, iCalculateMTO);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        
        // Single return statement
        return Json(new { success = bSuccess, message = bSuccess ? "BIA Matrix Saved Successfully" : "Failed To Save." });
    }

    private bool GetDataFromGridToCollection(int iRowCount, int iSelectedVal, int iDDLImpactSeverity, string strTimeIntevalIds, string strRowWiseValues,
        int iCalculateRTO, int iCalculateMTO)
    {
        int iSaveRecCount = 0;
        bool bSuccess = false;
        try
        {
            int[][] deserializedParentArray = JsonConvert.DeserializeObject<int[][]>(strRowWiseValues);
            int[] deserializedTimeIntervalIds = JsonConvert.DeserializeObject<int[]>(strTimeIntevalIds);

            bool bRes = _ProcessSrv.BPBIAMAtrixDeleteByBPIdImpactSevID(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), iDDLImpactSeverity, 0, Convert.ToInt32(HttpContext.Session.GetString("BIAID")));
            

            List<UserActivitiesInfo> lstUserActivitiesInfo = _ProcessSrv.UserActivities_GetBySessionActive(_UserDetails.UserID, 1);
            int iIdValue = 0;
            string strIPAddress = string.Empty;

            foreach (UserActivitiesInfo objUserActivitiesInfo in lstUserActivitiesInfo)
            {
                iIdValue = objUserActivitiesInfo.ID;
                strIPAddress = objUserActivitiesInfo.IPAddress;
            }

            Auditlog_Process_Details objAuditlog = new Auditlog_Process_Details();
            objAuditlog.ModuleID = 42;
            objAuditlog.RecordID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));

            if (Convert.ToInt32(HttpContext.Session.GetString("BIAID")) == 0)
                objAuditlog.ActionID = "I";
            else
                objAuditlog.ActionID = "U";

            string strProcessName = HttpContext.Session.GetString("ProcessNameWithCode");


            objAuditlog.CreatedBy = _UserDetails.UserID;
            objAuditlog.RecordName = strProcessName.Substring(0, strProcessName.LastIndexOf(" ("));
            objAuditlog.IPAddress = strIPAddress;
            objAuditlog.IDValue = iIdValue;

            int iSuccess = _ProcessSrv.SaveAuditlog_Process(objAuditlog);

            if (iIsBCMEntity == 0)
            {
                BusinessProcessInfo objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), 1);
                iBpProfileID = objBusinessProcessInfo.ProfileID;
            }

            if (iIsBCMEntity == 1)
            {
                BCMEntityInfo objBCMEntityInfo = _ProcessSrv.GetBCMEntityByEntityID(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")));
                iBpProfileID = Convert.ToInt32(objBCMEntityInfo.ProfileID);
            }
            List<Impact> lstImpact = _ProcessSrv.BIAProfileImpactTypesByProfID(iBpProfileID);
            BPBIAMatrix objBPBIAMatrix = new BPBIAMatrix();

            int iCount = 0;
            foreach (Impact objImpact in lstImpact)
            {
                
                objBPBIAMatrix.ImpactID = objImpact.ImpactID;
                objBPBIAMatrix.BusinessProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
                objBPBIAMatrix.BIAID = GetCurrentBIAID();
                objBPBIAMatrix.ImpactSeverityID = iDDLImpactSeverity;
                objBPBIAMatrix.ImpactTypeID = objImpact.ImpactTypeID;
                objBPBIAMatrix.UpdatorId = _UserDetails.UserID;
                objBPBIAMatrix.CreatorId = _UserDetails.UserID;
                objBPBIAMatrix.IsQuantitative = 0;

                int BFBIAMatrixID = _ProcessSrv.BPBIAMatrixSave(objBPBIAMatrix);

                int[] childArray = deserializedParentArray[iCount];

                for (int i = 0; i < childArray.Length; i++) { 
                    if (childArray[i] > 0)
                    {
                        BPBIAMatrixDetails objBPBIAMatrixDetails = new BPBIAMatrixDetails();

                        objBPBIAMatrixDetails.BPMatrixID = BFBIAMatrixID;
                        objBPBIAMatrixDetails.Cost = childArray[i];
                        objBPBIAMatrixDetails.TimeIntervalID = deserializedTimeIntervalIds[i];

                        int iBPBIAMatrixDetailsID = _ProcessSrv.BPBIAMatrixDetailsSave(objBPBIAMatrixDetails);

                        if (iBPBIAMatrixDetailsID > 0)
                        {
                            iSaveRecCount++;
                        }

                        SaveAuditTrail(iSuccess, objBPBIAMatrixDetails.Cost.ToString(), objBPBIAMatrixDetails.TimeIntervalID, "0", iDDLImpactSeverity, objImpact.ImpactTypeID.ToString(), objImpact.ImpactID,
                            HttpContext.Session.GetString("ProcessID"), GetCurrentBIAID().ToString());
                    }                    
                }
                iCount++;
            }

            if(iSaveRecCount > 0)
            {
                bSuccess = true;
                UpdateBusinessProcessData(iCalculateRTO, iCalculateMTO);
                UpdateBusinessProcessBIAByBIAID();
                AssignDataToMatrix();
            }
            else
            {

            }
            return bSuccess;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return bSuccess;
        }
    }

    private void SaveAuditTrail(int iDetailsID, string strNewValue, int iTimeIntervalID, string strIsQuantitative, int iSeverityID, 
        string strImpactTypeID, int iImpactID, string strBusinessProcessID, string strBIAIDForAudit)
    {
        try
        {
            string strOldValue = GetRTOData(iImpactID, iTimeIntervalID, iSeverityID);

            if (strOldValue != strNewValue)
            {
                BIATimeInterval objTimeInterval = _ProcessSrv.GetBIATimeIntervalById(iTimeIntervalID.ToString());
                Auditlog_Process_Details objAuditlogDetails = new Auditlog_Process_Details();
                List<Impact> lstImpact = _ProcessSrv.BIAProfileImpactTypesByProfID(iBpProfileID);

                var objImpactDtl = lstImpact.FirstOrDefault(x => x.ImpactID == iImpactID);
                string strImpactName = string.Empty;
                string strImpactTypeName = string.Empty;
                if (objImpactDtl != null) {
                    strImpactName= objImpactDtl.ImpactName;
                    strImpactTypeName = objImpactDtl.ImpactTypeName;
                }

                strOldValue = strOldValue == "0" ? "" : System.Enum.GetName(typeof(BCPEnum.ImpactRating), Convert.ToInt32(strOldValue));
                strNewValue = strNewValue == "0" ? "" : System.Enum.GetName(typeof(BCPEnum.ImpactRating), Convert.ToInt32(strNewValue));
                var impSeverityValue = _Utilities.PopulateBIAImpactSeverityType().FirstOrDefault(x => x.ImpSeverityID == iSeverityID);
                objAuditlogDetails.DetailsID = iDetailsID;
                objAuditlogDetails.ChangeDescription = "Value for Severity - " + impSeverityValue.ImpSeverityValue + ", ImpactTypeName - " + strImpactTypeName + ", Impact - " 
                    + strImpactName + ", TimeInterval - " + objTimeInterval.TimeIntervalText;
                objAuditlogDetails.PreviousValue = strOldValue;
                objAuditlogDetails.NewValue = strNewValue;

                int iSuccessID = _ProcessSrv.SaveAuditlog_Process_Details(objAuditlogDetails);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private void UpdateBusinessProcessData(int iCalculateRTO, int iCalculateMTO)
    {
        try
        {
            bool bIsCritical = false;
            string strIsCritical = string.Empty;
            if (Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity")) == 1)
            {
                BCMEntityInfo objBCMEntity = _ProcessSrv.GetBCMEntityByEntityID(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")));
                if (Convert.ToInt32(objBCMEntity.BCMEntityType) > 0)
                {
                    bIsCritical = _Utilities.IsProcessCritical(iCalculateRTO, objBCMEntity.OrgID);
                    strIsCritical = bIsCritical ? "This BCM Entity is Critical RTO is - " + _Utilities.GetFormattedRTO_New(iCalculateRTO) :
                        "This BCM Entity is Not Critical RTO is - " + _Utilities.GetFormattedRTO_New(iCalculateRTO);
                    objBCMEntity.IsCritical = bIsCritical ? 1 : 0;
                    objBCMEntity.OwnerRTO = iCalculateRTO.ToString();
                    objBCMEntity.MTR = iCalculateMTO.ToString();
                    if (Convert.ToInt32(_ProcessSrv.BCMEntityInfoSaveUpdate(objBCMEntity)) > 0)
                    {
                        _Utilities.AddPCIScore(objBCMEntity.BCMEntityID.ToString(), ((int)BCPEnum.PCIScore.ProcessBIACompleted).ToString(), "1", _UserDetails.UserID.ToString(),
                            _UserDetails.UserID.ToString());
                        _Utilities.AddPCIScoreSummary(objBCMEntity.BCMEntityID.ToString(), "0", ((int)BCPEnum.PCIScore.ProcessBIACompleted).ToString()
                            , _Utilities.GetPCIScoreWeightage(((int)BCPEnum.PCIScore.ProcessBIACompleted)), _Utilities.GetPCIScoreWeightage(((int)BCPEnum.PCIScore.ProcessBIACompleted)));
                        _Utilities.AddLog(_UserDetails.UserID, "", "BIA Survey", "Impact Analysis", BCPEnum.ActionType.Saved.ToString(), 0, "0", _UserDetails.UserID, 0);
                    }

                    if (Convert.ToInt32(objBCMEntity.OwnerRTO) != 0 && Convert.ToInt32(objBCMEntity.OwnerMTR) != 0)
                    {

                    }
                }
            }
            else if (Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity")) == 0)
            {
                BusinessProcessInfo objBussinessProc = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), 1);
                if (objBussinessProc.ProcessID > 0)
                {
                    bIsCritical = _Utilities.IsProcessCritical(iCalculateRTO, objBussinessProc.OrgID);
                    strIsCritical = bIsCritical ? "This BIA Process is Critical RTO is - " + _Utilities.GetFormattedRTO_New(iCalculateRTO) :
                        "This BIA Process is Not Critical RTO is - " + _Utilities.GetFormattedRTO_New(iCalculateRTO);
                    objBussinessProc.IsCritical = bIsCritical ? 1 : 0;
                    objBussinessProc.OwnerRTO = iCalculateRTO.ToString();
                    objBussinessProc.MTR = iCalculateMTO.ToString();

                    if (_ProcessSrv.BusinessprocessUpdate(objBussinessProc))
                    {
                        _Utilities.AddPCIScore(objBussinessProc.ProcessID.ToString(), ((int)BCPEnum.PCIScore.ProcessBIACompleted).ToString(), "1", _UserDetails.UserID.ToString(),
                            _UserDetails.UserID.ToString());
                        _Utilities.AddPCIScoreSummary(objBussinessProc.ProcessID.ToString(), "0", ((int)BCPEnum.PCIScore.ProcessBIACompleted).ToString()
                            , _Utilities.GetPCIScoreWeightage(((int)BCPEnum.PCIScore.ProcessBIACompleted)), _Utilities.GetPCIScoreWeightage(((int)BCPEnum.PCIScore.ProcessBIACompleted)));
                        _Utilities.AddLog(_UserDetails.UserID, "", "BIA Survey", "Impact Analysis", BCPEnum.ActionType.Saved.ToString(), 0, "0", _UserDetails.UserID, 0);
                    }

                    if(Convert.ToInt32(objBussinessProc.OwnerRTO)!=0 && Convert.ToInt32(objBussinessProc.OwnerMTR) != 0)
                    {

                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    protected int UpdateBusinessProcessBIAByBIAID()
    {
        try
        {
            return _ProcessSrv.ProcessBIAUpdateByBIAID(Convert.ToInt32(HttpContext.Session.GetString("BIAID")), _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return 0;
        }
    }

    private string GetRTOData(int iImpactID, int iRTOID,int iSeverityID)
    {
        string strCost = string.Empty;
        try
        {
            BIAProfileMaster newBIAProfileMasterList = new BIAProfileMaster();
            if (lstBIAProfileMaster.Count > 0)
            {
                newBIAProfileMasterList = lstBIAProfileMaster.Where(x => x.ImpactID == iImpactID && x.TimeIntervalID == iRTOID && x.ImpactSeverityID == iSeverityID).FirstOrDefault();
                if (newBIAProfileMasterList != null)
                {
                    strCost = newBIAProfileMasterList.Cost;
                    strCost = string.IsNullOrEmpty(strCost) ? "0" : strCost;
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return strCost;
    }

    public int GetHighestSeverity()
    {
        int iHighestSeverity = 1;
        try
        {
            if (lstBIAProfileMaster.Count == 0)
            {
                iIsBCMEntity = Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity"));

                if (iIsBCMEntity == 0)
                {
                    BusinessProcessInfo objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), 1);
                    iBpProfileID = objBusinessProcessInfo.ProfileID;
                }

                if (iIsBCMEntity == 1)
                {
                    BCMEntityInfo objBCMEntityInfo = _ProcessSrv.GetBCMEntityByEntityID(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")));
                    iBpProfileID = Convert.ToInt32(objBCMEntityInfo.ProfileID);
                }
                lstBIAProfileMaster = _ProcessSrv.GetBIAProfileImpactMatrixByProfID(iBpProfileID, Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), 0, Convert.ToInt32(HttpContext.Session.GetString("BIAID")));
            }
            List<BIAProfileMaster> lstBIAProfileMaster_1 = lstBIAProfileMaster.Where(x => x.ImpactSeverityID == 1).ToList();

            if (lstBIAProfileMaster_1 !=null&& lstBIAProfileMaster_1.Count>0)            
               iHighestSeverity = 1;

            List<BIAProfileMaster> lstBIAProfileMaster_2 = lstBIAProfileMaster.Where(x => x.ImpactSeverityID == 2).ToList();

            if (lstBIAProfileMaster_2 != null && lstBIAProfileMaster_2.Count > 0)
                iHighestSeverity = 2;

            List<BIAProfileMaster> lstBIAProfileMaster_3 = lstBIAProfileMaster.Where(x => x.ImpactSeverityID == 3).ToList();

            if (lstBIAProfileMaster_3 != null && lstBIAProfileMaster_3.Count > 0)
                iHighestSeverity = 3;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return iHighestSeverity;
    }

    private void GetBIAProfileDetails_ForMatrix(int iBPID, int iProfileID)
    {
        try
        {
            lstBIAProfileMaster = _ProcessSrv.GetBIAProfileImpactMatrixByProfID(iProfileID, iBPID, 0, Convert.ToInt32(HttpContext.Session.GetString("BIAID")));            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private void GenerateDataGridView()
    {
        try
        {
            ViewBag.ColumnHeaders = lstBIAProfileMaster.DistinctBy(x => x.TimeIntervalID).Select(u => new { u.TimeIntervalID, u.TimeIntervalText }).ToList();
            GetImpactByProfileID(iBpProfileID);
            PopulateImpactRating();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private void GetImpactByProfileID(int iBpProfileID)
    {
        try
        {
            List<Impact> lstImpact = _ProcessSrv.BIAProfileImpactTypesByProfID(iBpProfileID);
            ViewBag.ImpactTypeName = lstImpact.DistinctBy(x => x.ImpactTypeID).ToList();
            ViewBag.ImpactName = lstImpact;            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private void PopulateImpactRating()
    {
        try
        {
            ViewBag.ImpactRatings = new SelectList(_Utilities.PopulateImpactRating(), "ImpactRatingID", "ImpactRatingName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    [HttpGet]
    public void GetMTRRatingMAOAndConfiguredRTO()
    {
        try
        {
            RTOMTRConfigurations objRTOMTRConfigurations = _ProcessSrv.GetConfiguredRTOMTR(_UserDetails.OrgID);
            int iMTRRatingMAO = objRTOMTRConfigurations.MTRRating;
            ViewBag.MTRRatingMAO = iMTRRatingMAO == 0 ? 0 : iMTRRatingMAO;

            RTOMTRConfigurations objRTOMTR = _ProcessSrv.GetRtoMtrByOrgID(_UserDetails.OrgID.ToString());
            if (objRTOMTR != null && objRTOMTR.ConfiguredRTO != null)
                ViewBag.ConfiguredRTO = objRTOMTR.ConfiguredRTO;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    [HttpGet]
    public IActionResult CalculateRTOMTR(int iCurrentColumnCount, int iCurrentColumnHeaderID, int iPreviousColumnHeaderId, int iIsRTOMAOCalculated, int iArraySelectedValue)//, int iSelectedValue , string iSelectedValuesArray
    {
        try
        {
            int iCalculatedMTO = 0;
            int iCalculatedRTO = 0;
            string strCalculatedMTO = string.Empty;
            string strCalculatedRTO = string.Empty;
            string strIsCritical = string.Empty;
            int iDDLSelectedValue = 0;

            //var secondMethodData = GetRTOMAOAtInitialStage(iCurrentColumnHeaderID, iPreviousColumnHeaderId) as JsonResult;
            var secondMethodData = GetRTOMAOAtInitialStage(iCurrentColumnHeaderID, iPreviousColumnHeaderId).Value;

            RTOMTRConfigurations objRTOMTRConfigurations = _ProcessSrv.GetConfiguredRTOMTR(_UserDetails.OrgID);
            int iMTRRatingMAO = objRTOMTRConfigurations.MTRRating;

            if (iIsRTOMAOCalculated == 0)
            {                

                if (iArraySelectedValue > 0)
                {
                    iDDLSelectedValue = iArraySelectedValue;

                    //iCalculatedMTO = GetTimeByTimeIntervalID(iCurrentColumnHeaderID);
                    //strCalculatedMTO = _Utilities.GetFormattedRTO_New(iCalculatedMTO);

                    //int iLastColumnHederID = 0;

                    //if (iCurrentColumnCount > 1 && iPreviousColumnHeaderId > 0)
                    //    iLastColumnHederID = iPreviousColumnHeaderId;
                    //else
                    //    iLastColumnHederID = iCurrentColumnHeaderID;


                    //iCalculatedRTO = GetTimeByTimeIntervalID(iLastColumnHederID);
                    //strCalculatedRTO = _Utilities.GetFormattedRTO_New(iCalculatedRTO);


                    //if (Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity")) == 1)
                    //{
                    //    BCMEntityInfo objBCMEntityInfo = _ProcessSrv.GetBCMEntityByEntityID(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")));
                    //    if (objBCMEntityInfo.BCMEntityID > 0)
                    //    {
                    //        bool bIsCritical = _Utilities.IsProcessCritical(iCalculatedRTO, objBCMEntityInfo.OrgID);
                    //        strIsCritical = bIsCritical ? "1" : "0";
                    //    }
                    //}
                    //if (Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity")) == 0)
                    //{
                    //    BusinessProcessInfo objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), 1);
                    //    if (objBusinessProcessInfo.ProcessID > 0)
                    //    {
                    //        bool bIsCritical = _Utilities.IsProcessCritical(iCalculatedRTO, objBusinessProcessInfo.OrgID);
                    //        strIsCritical = bIsCritical ? "1" : "0";
                    //    }
                    //}
                    //strIsCritical = _Utilities.GetIsCriticalStatus(strIsCritical);
                    iIsRTOMAOCalculated++;
                }
            }

            var firstMethodData = new
            {
                iDDLSelectedValue = iDDLSelectedValue,
                iIsRTOMAOCalculated = iIsRTOMAOCalculated
            };

            var combineResult = new
            {
                firstMethodData = firstMethodData,
                secondMethodData = secondMethodData
            };
            //return Json(new
            //{
            //    calculatedRTO = strCalculatedRTO,
            //    calculatedMTO = strCalculatedMTO,
            //    isCritical = strIsCritical,
            //    iDDLSelectedValue = iDDLSelectedValue,
            //    iIsRTOMAOCalculated = iIsRTOMAOCalculated,
            //    iCalculatedMTO = iCalculatedMTO,
            //    iCalculatedRTO = iCalculatedRTO
            //});
            return Json(combineResult);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { error = ex.Message });
        }        
    }

    [HttpGet]
    public JsonResult GetRTOMAOAtInitialStage(int iCurrentColumnId, int iPreviousColumnId)
    {
        int iCalculatedMTO = 0;
        int iCalculatedRTO = 0;
        string strCalculatedMTO = string.Empty;
        string strCalculatedRTO = string.Empty;
        string strIsCritical = string.Empty;
        try
        {
            iCalculatedMTO = GetTimeByTimeIntervalID(iCurrentColumnId);
            strCalculatedMTO = _Utilities.GetFormattedRTO_New(iCalculatedMTO);

            int iLastColumnHederID = 0;

            if (iCurrentColumnId > 1 && iPreviousColumnId > 0)
                iLastColumnHederID = iPreviousColumnId;
            else
                iLastColumnHederID = iCurrentColumnId;


            iCalculatedRTO = GetTimeByTimeIntervalID(iLastColumnHederID);
            strCalculatedRTO = _Utilities.GetFormattedRTO_New(iCalculatedRTO);

            if (Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity")) == 1)
            {
                BCMEntityInfo objBCMEntityInfo = _ProcessSrv.GetBCMEntityByEntityID(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")));
                if (objBCMEntityInfo.BCMEntityID > 0)
                {
                    bool bIsCritical = _Utilities.IsProcessCritical(iCalculatedRTO, objBCMEntityInfo.OrgID);
                    strIsCritical = bIsCritical ? "1" : "0";
                }
            }
            if (Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity")) == 0)
            {
                BusinessProcessInfo objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), 1);
                if (objBusinessProcessInfo.ProcessID > 0)
                {
                    bool bIsCritical = _Utilities.IsProcessCritical(iCalculatedRTO, objBusinessProcessInfo.OrgID);
                    strIsCritical = bIsCritical ? "1" : "0";
                }
            }
            strIsCritical = _Utilities.GetIsCriticalStatus(strIsCritical);
            var data = new
            {
                calculatedRTO = strCalculatedRTO,
                calculatedMTO = strCalculatedMTO,
                isCritical = strIsCritical,
                iCalculatedMTO = iCalculatedMTO,
                iCalculatedRTO = iCalculatedRTO
            };
            
            //return Json(new
            //{
            //    calculatedRTO = strCalculatedRTO,
            //    calculatedMTO = strCalculatedMTO,
            //    isCritical = strIsCritical,
            //    iCalculatedMTO = iCalculatedMTO,
            //    iCalculatedRTO = iCalculatedRTO

            //});
            return Json(data);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { error = ex.Message });
        }
    }

    private int GetTimeByTimeIntervalID(int iIntvalID)
    {
        int iMaxTime = 0;
        int iMaxTimeUnit = 0;
        int iCalculatedMins = 0;
        try
        {            
            BIATimeInterval objBIATimeInterval = _ProcessSrv.GetBIATimeIntervalById(iIntvalID.ToString(), 1);
            if (objBIATimeInterval != null)
            {
                iMaxTime = Convert.ToInt32(objBIATimeInterval.MaxInterval);
                iMaxTimeUnit = Convert.ToInt32(objBIATimeInterval.MaxIntervalUnit);

                if (iMaxTimeUnit == (int)BCPEnum.TimeIntervalUnit.Min)
                    iCalculatedMins = iMaxTime;
                else if (iMaxTimeUnit == (int)BCPEnum.TimeIntervalUnit.Hour)
                    iCalculatedMins = iMaxTime * 60;
                else if (iMaxTimeUnit == (int)BCPEnum.TimeIntervalUnit.Day)
                    iCalculatedMins = iMaxTime * 60 * 24;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return iCalculatedMins;
    }

    private int GetCurrentBIAID()
    {
        BIASection objBIASection = new BIASection();
        int iBIAIDForQualitativeMatrix = 0;
        try
        {
            iBIAIDForQualitativeMatrix = Convert.ToInt32(HttpContext.Session.GetString("BIAID"));
            if (iBIAIDForQualitativeMatrix == 0)
            {
                objBIASection.Version = "1.0";
                objBIASection.VersionChangeDescription = "";
                objBIASection.ApprovalStatus = ((int)BCPEnum.ApprovalType.Initiated).ToString();
                objBIASection.ProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
                objBIASection.SectionID = Convert.ToInt32(HttpContext.Session.GetString("SectionID"));
                objBIASection.IsEffective = 1;
                objBIASection.CreatedBy = _UserDetails.UserID;
                objBIASection.ChangedBy = _UserDetails.UserID;
                objBIASection.IsBCMEntity = Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity"));

                iBIAIDForQualitativeMatrix = _ProcessSrv.ProcessBIASectionSave(objBIASection);

                HttpContext.Session.SetString("BIAID", iBIAIDForQualitativeMatrix.ToString());
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return iBIAIDForQualitativeMatrix;
    }
}
