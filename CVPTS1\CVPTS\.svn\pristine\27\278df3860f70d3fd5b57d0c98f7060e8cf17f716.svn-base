﻿@{
    ViewBag.Title = "Logout";
    Layout = "~/Views/Shared/_loginlayout.cshtml";
}

<div class="row justify-content-center align-items-center mx-0" style="height: calc(100vh - 1px);">
    <div class="col-6 col-xxl-5">
        <div class="card ">
            <div class="card-body">
                <img src="~/img/logo/logo_fullname.svg" />
                <div class="d-grid align-items-center justify-content-center">
                    <img class="my-3" src="~/img/isomatric/logout.svg"/>
                    <p class="text-muted text-center">
                        You’ve been successfully logged out. <br /> <span class="fs-6">Click below to login again.</span>
                    </p>
                </div>
                <div class="d-grid col-3 mx-auto mt-3 w-25">
                    @* <button class="btn btn-primary rounded-2" type="submit">Login</button> *@
                    <a class="btn btn-primary rounded-2" href="@Url.Action("Login", "Login",new { area="" })">Login</a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 text-center mb-3 position-fixed bottom-0">
        <span>Continuity Vault | © 2025-2026 Perpetuuiti - All Rights Reserved.</span>
    </div>
</div>
