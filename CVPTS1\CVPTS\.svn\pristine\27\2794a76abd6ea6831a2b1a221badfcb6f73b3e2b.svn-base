﻿@{
    ViewBag.Title = "View Applications";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var selectedOrgID = ViewBag.selectedOrgID;
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<div class="Page-Condant border-0">
</div>
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">View Applications</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="d-flex gap-2 justify-content-end align-items-end" style="width:95%;">
            <div class="vendor-section my-0 me-3">
                <div class="d-flex align-items-center gap-2 fs-6">
                    <span class="vendor-circle primary"><i class="cv-current-BCM-entites fs-14"></i></span><span>Total Applications</span><span class="count-primary">@ViewBag.TotalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-6">
                    <span class="vendor-circle warning"><i class="cv-warning fs-14"></i></span><span>Critical Applications</span><span class="count-warning">@ViewBag.CriticalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-6">
                    <span class="vendor-circle success"><i class="cv-success1 fs-14"></i></span><span>Non Critical Applications</span><span class="count-success">@ViewBag.NonCriticalCount</span>
                </div>
            </div>
            <div class="input-group Search-Input">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control" placeholder="Search">
            </div>
            <div class="dropdown">
                <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                    <i class="cv-filter align-middle" title="View Filter"></i>
                </button>
                <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                    <div class="mb-3">
                        <label>Organizations</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                            <select class="form-select form-control selectized" aria-label="Default select example" id="ddlOrg">
                                <option value="0" selected>All Organizations</option>
                                @{
                                    foreach (var objOrg in ViewBag.OrgName)
                                    {
                                        <!option value="@objOrg.Value" @(objOrg.Value == selectedOrgID.ToString() ? "selected=\"selected=\"" : "")>@objOrg.Text</!option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Units</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                            <select class="form-select form-control selectized" id="ddlUnit" aria-label="Default select example">
                                <option value="0" selected>All Units</option>
                                @{
                                    foreach (var objUnits in ViewBag.Unit)
                                    {
                                        <option value="@objUnits.Value">@objUnits.Text</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-department"></i></span>
                            <select class="form-select form-control selectized" id="ddlDepartment" aria-label="Default select example">
                                <option value="0" selected>All Departments</option>
                                @{
                                    foreach (var objDepartment in ViewBag.Department)
                                    {
                                        <option value="@objDepartment.Value">@objDepartment.Text</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Sub Departments</label>
                        <div class="input-group w-30">
                            <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
                            <select class="form-select form-control selectized" id="ddlSubDepartment" aria-label="Default select example">
                                <option value="0" selected>All Sub Departments</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="Page-Condant border-0" id="applicationsList">
    @await Html.PartialAsync("_ApplicationsList")
</div>

@section Scripts {
    <script>
        $(document).ready(function () {

            var orgID = "";
            var unitID = "";
            var deptID = "";
            var subDeptID = "";

            function filterApplicationsList(orgID, unitID, departmentID, subDepartmentID) {
                $.ajax({
                    type: 'POST',
                    url: '@Url.Action("BindFilteredList", "ViewApplications")',
                    data: {
                        iOrgID: orgID,
                        iUnitID: unitID,
                        iDepartmetID: departmentID,
                        iSubDepartmentID: subDepartmentID
                    },
                    success: function (response) {
                        var tableData = $('#applicationsList');
                        tableData.empty();
                        $('#applicationsList').html(response);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            }

            $('#ddlUnit').on('change', function (e) {
                e.preventDefault();
                orgID = $('#ddlOrg').val();
                unitID = $(this).val();
                deptID = $('#ddlDepartment').val();
                subDeptID = $('#ddlSubDepartment').val();

                $.ajax({
                    type: 'POST',
                    url: '@Url.Action("BindFunction", "ViewApplications")',
                    data: {
                        iUnitID: unitID
                    },
                    success: function (response) {
                        let selectizeInstance = $('#ddlDepartment')[0].selectize;
                        selectizeInstance.clear();
                        selectizeInstance.clearOptions();
                        selectizeInstance.addOption({ value: "0", text: "-- All Departments --" });
                        selectizeInstance.addItem("0");

                        response && response.forEach(({ departmentID, departmentName }) => {
                            if (departmentID && departmentName) {
                                selectizeInstance.addOption({ value: departmentID, text: departmentName });
                            }
                        });
                        filterApplicationsList(orgID, unitID, deptID, subDeptID);
                    },
                    // success: function (data) {
                    //     var department = $('#ddlDepartment');
                    //     department.empty();
                    //     department.append('<option value="0">All Departments</option>');
                    //     $.each(data, function (index, item) {
                    //         department.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                    //     });
                    //     filterApplicationsList(orgID, unitID, deptID, subDeptID);
                    // },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            })

            $('#ddlDepartment').on('change', function (e) {
                e.preventDefault();
                orgID = $('#ddlOrg').val();
                unitID = $('#ddlUnit').val();
                deptID = $(this).val();
                subDeptID = $('#ddlSubDepartment').val();

                $.ajax({
                    type: 'POST',
                    url: '@Url.Action("BindSubFunction", "ViewApplications")',
                    data: {
                        iDepartmetID: deptID
                    },
                    success: function (response) {
                        let selectizeInstance = $('#ddlSubDepartment')[0].selectize;
                        selectizeInstance.clear();
                        selectizeInstance.clearOptions();
                        selectizeInstance.addOption({ value: "0", text: "-- All Sub Departments --" });
                        selectizeInstance.addItem("0");

                        response && response.forEach(({ subFunctionID, subFunctionName }) => {
                            if (subFunctionID && subFunctionName) {
                                selectizeInstance.addOption({ value: subFunctionID, text: subFunctionName });
                            }
                        });
                        filterApplicationsList(orgID, unitID, deptID, subDeptID);
                    },
                    // success: function (data) {
                    //     var subDepartment = $('#ddlSubDepartment');
                    //     subDepartment.empty();
                    //     subDepartment.append('<option value="0">All Sub Departments</option>');
                    //     $.each(data, function (index, item) {
                    //         subDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>')
                    //     });
                    //     filterApplicationsList(orgID, unitID, deptID, subDeptID);
                    // },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            })

            $('#ddlSubDepartment').on('change', function (e) {
                e.preventDefault();
                orgID = $('#ddlOrg').val();
                unitID = $('#ddlUnit').val();
                deptID = $('#ddlDepartment').val();
                subDeptID = $(this).val();

                filterApplicationsList(orgID, unitID, deptID, subDeptID);
            })
        })
    </script>
}