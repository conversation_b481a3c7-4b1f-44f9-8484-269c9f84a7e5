﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using BCM.Security.Helper;
using static BCM.Shared.BCPEnum;

namespace BCM.Shared
{
    public class BCMMail
    {

        private ProcessSrv _objSrv;

        private static string SMTP = ""; //Utilities.VaultConfigurations.SMTPServer; //ConfigurationManager.AppSettings["SMTP"];//OutgoingMailServer

        private static string OutgoingMailServerPort = "";// Utilities.VaultConfigurations.OutGoingMailPort; //ConfigurationManager.AppSettings["OutgoingMailServerPort"];

        private static string sMailFrom = "";// Utilities.VaultConfigurations.MailFrom; //ConfigurationManager.AppSettings["MailFrom"];//MailFrom

        //static ILog MailErrors = CVLogger.GetLogger("CVaultMailLog");

        private readonly Utilities _utilities;

        public BCMMail(Utilities utilities, ProcessSrv objSrv)
        {
            _objSrv = objSrv;
            _utilities = utilities;
        }

        private string Send(MailMessage objMailMessage, string OrgID = "0")
        {
            VaultSettings objVaultSetting = new VaultSettings();
            string sMessage = string.Empty;
            try
            {
                OrgID = OrgID == "" ? "0" : OrgID;
                if (Convert.ToInt32(OrgID) <= 0)
                {
                    objVaultSetting = _utilities.VaultConfigurations;
                }
                else
                {
                    objVaultSetting = _objSrv.GetVaultSettingsByOrgID_new(Convert.ToInt16(OrgID));
                }
                if (objVaultSetting.SMTPServer != null && objVaultSetting.OutGoingMailPort != null && objVaultSetting.MailFrom != null)
                {
                    SMTP = objVaultSetting.SMTPServer;
                    OutgoingMailServerPort = objVaultSetting.OutGoingMailPort;
                    sMailFrom = objVaultSetting.MailFrom;

                    if (objVaultSetting.DevMode != null && objVaultSetting.DefaultMailID != null)
                        if (objVaultSetting.DevMode.Equals("1"))
                        {
                            if (objMailMessage.CC.Count > 0)
                            {
                                objMailMessage.CC.Clear();
                                objMailMessage.CC.Add(new MailAddress(objVaultSetting.DefaultMailID));
                            }
                            if (objMailMessage.Bcc.Count > 0)
                            {
                                objMailMessage.Bcc.Clear();
                                objMailMessage.Bcc.Add(new MailAddress(objVaultSetting.DefaultMailID));
                            }

                            objMailMessage.To.Clear();
                            objMailMessage.To.Add(new MailAddress(objVaultSetting.DefaultMailID));

                        }

                    if (objMailMessage.Body.Contains("@@@@@"))
                        objMailMessage.Body = objMailMessage.Body.Replace("@@@@@", objVaultSetting.ReplyNumber);

                    objMailMessage.From = new MailAddress(sMailFrom, "Continuity Vault");
                    objMailMessage.IsBodyHtml = true;
                    objMailMessage.Priority = MailPriority.High;

                    var objSMTP = new SmtpClient(SMTP, Convert.ToInt32(OutgoingMailServerPort)) { UseDefaultCredentials = false };
                    objSMTP.Credentials = new NetworkCredential(objVaultSetting.SMTPUserName, objVaultSetting.SMTPPassword);

                    objSMTP.EnableSsl = Convert.ToInt32(objVaultSetting.EnableSSL) > 0 ? true : false;

                    objSMTP.Send(objMailMessage);
                }
            }
            catch (Exception ex)
            {
                sMessage = ex.ToString();
            }
            return sMessage;
        }

        public bool SendMail(string strSubject, string strBody, string[] sMailTO, string[] sMailCC, string[] sMailBCC,
        string[] sAttachment, string OrgID = "0", List<Attachment> lstAttachment = null)
        {       
            string strMessage = string.Empty;
            try
            {
                MailMessage objMailMessage = new MailMessage();

                if (sMailTO!=null)
                {
                    foreach (string strMailTo in sMailTO)
                    {
                        if (!string.IsNullOrEmpty(strMailTo))
                        {
                            objMailMessage.To.Add(new MailAddress(strMailTo));
                        }
                    }
                }

                if (sMailCC != null)
                {
                    foreach (string strMailCC in sMailCC)
                    {
                        if (!string.IsNullOrEmpty(strMailCC))
                        {
                            objMailMessage.CC.Add(new MailAddress(strMailCC));
                        }
                    }
                }

                if (sMailBCC!=null)
                {
                    foreach (string strMailBcc in sMailBCC)
                    {
                        if (!string.IsNullOrEmpty(strMailBcc))
                        {
                            objMailMessage.Bcc.Add(new MailAddress(strMailBcc));
                        }
                    }
                }

                if (sAttachment != null && sAttachment.Length > 0)
                {
                    foreach (string strAttachment in sAttachment)
                    {
                        if (!string.IsNullOrEmpty(strAttachment))
                        {
                            objMailMessage.Attachments.Add(new Attachment(strAttachment));
                        }
                    }
                }
                if (lstAttachment!=null)
                {
                    if (lstAttachment.Count > 0)
                    {
                        foreach (Attachment objAttachment in lstAttachment)
                        {
                            if (objAttachment!=null)
                            {
                                objMailMessage.Attachments.Add(objAttachment);
                            }
                        }
                    }
                }
                
                objMailMessage.Subject = strSubject;
                objMailMessage.Body = strBody;
                strMessage = Send(objMailMessage, OrgID);
            }
            catch (Exception e)
            {
                strMessage = e.ToString();
            }
            return strMessage.Length == 0;
        }

        public bool SendMail(string strSubject, string strBody, string sMailTO, string sMailCC, string sMailBCC, string sAttachment, string OrgID = "0", 
            string sAttachmentID = "0", string sAttachmentName = "", string UserID = "0", string NotifiedToID = "0",
        string NotifiedCcID = "0", string NotifiedBccID = "0")
        {
            string sMessage = string.Empty;
            MailMessage objMailMessage = new MailMessage();
            int IsSuccess = 1;
            try
            {

                if (!string.IsNullOrEmpty(sMailTO))
                {
                    objMailMessage.To.Add(new MailAddress(sMailTO));
                }

                if (!string.IsNullOrEmpty(sMailCC))
                {
                    objMailMessage.To.Add(new MailAddress(sMailCC));
                }

                if (!string.IsNullOrEmpty(sMailBCC))
                {
                    objMailMessage.To.Add(new MailAddress(sMailBCC));
                }

                if (!string.IsNullOrEmpty(sAttachment))
                {
                    objMailMessage.Attachments.Add(new Attachment(sAttachment));
                }


                objMailMessage.Subject = strSubject;
                objMailMessage.Body = strBody;


                sMessage = Send(objMailMessage, OrgID);
            }
            catch (Exception e)
            {
                IsSuccess = 0;
                sMessage = e.ToString();
            }

            NotificationMasters notificationMasters = new NotificationMasters
            {
                NotificationMode = 1,
                Body = objMailMessage.Body,
                Subject = objMailMessage.Subject,
                NotifiedBy = Convert.ToInt32(UserID),
                NotificationType = 1,
                FYAI = 1,
                GUIDAttachment = "GUIDAttachment",
                IsSuccess = IsSuccess,
                NotificationTo = Convert.ToString(objMailMessage.To),
                NotificationCC = Convert.ToString(objMailMessage.CC),
                NotificationBCC = Convert.ToString(objMailMessage.Bcc),
                AttachmentID = sAttachmentID,
                AttachmentName = sAttachmentName,
                Attachment = sAttachment,
                OrgID = Convert.ToInt32(OrgID),
                SMNotifiedToID = NotifiedToID,
                SMNotifiedCcID = NotifiedCcID,
                SMNotifiedBccID = NotifiedBccID,
            };

            bool result = _objSrv.NotificationMasterSave(notificationMasters);

            return sMessage.Length == 0;
        }

        public bool IsValidEmail(string emailid)
        {
            try
            {
                MailAddress m = new MailAddress(emailid);

                return true;
            }
            catch (FormatException)
            {
                return false;
            }
        }

        #region for Risk Management 
        public bool SendMailForRiskAssessment(string strSubject, string strBody, string sMailTO, string sMailCC, string sMailBCC,
            string sAttachment, string OrgID = "0", string sAttachmentID = "0", string sAttachmentName = "", string UserID = "0", string NotifiedToID = "0",
            string NotifiedCcID = "0", string NotifiedBccID = "0")
        {
            string sMessage = string.Empty;
            int IsSuccess = 1;
            MailMessage objMailMessage = new MailMessage();
            try
            {
                if (!string.IsNullOrEmpty(sMailTO)) { objMailMessage.To.Add(new MailAddress(sMailTO)); }

                if (!string.IsNullOrEmpty(sMailCC)) { objMailMessage.To.Add(new MailAddress(sMailCC)); }

                if (!string.IsNullOrEmpty(sMailBCC)) { objMailMessage.To.Add(new MailAddress(sMailBCC)); }

                if (!string.IsNullOrEmpty(sAttachment)) { objMailMessage.Attachments.Add(new Attachment(sAttachment)); }

                objMailMessage.Subject = strSubject;
                objMailMessage.Body = strBody;
                sMessage = Send(objMailMessage, OrgID);
            }
            catch (Exception e)
            {
                IsSuccess = 0;
                sMessage = e.ToString();
            }
            NotificationMasters notificationMasters = new NotificationMasters
            {
                NotificationMode = 1,
                Body = objMailMessage.Body,
                Subject = objMailMessage.Subject,
                NotifiedBy = Convert.ToInt32(UserID),
                NotificationType = 1,
                FYAI = 1,
                GUIDAttachment = "GUIDAttachment",
                IsSuccess = IsSuccess,
                NotificationTo = Convert.ToString(objMailMessage.To),
                NotificationCC = Convert.ToString(objMailMessage.CC),
                NotificationBCC = Convert.ToString(objMailMessage.Bcc),
                AttachmentID = sAttachmentID,
                AttachmentName = sAttachmentName,
                Attachment = sAttachment,
                OrgID = Convert.ToInt32(OrgID),
                SMNotifiedToID = NotifiedToID,
                SMNotifiedCcID = NotifiedCcID,
                SMNotifiedBccID = NotifiedBccID,
            };

            bool result = _objSrv.NotificationMasterSave(notificationMasters);

            return sMessage.Length == 0;
        }
        #endregion

        public string SendTestMail(string strSubject, string strBody, string sMailTO, string SMTPUserName, string SMTPPassword,
        string SMTPServer, string OutGoingMailPort, string MailFrom, bool Enablessl, string OrgID = "0")
        {
            
            string sMessage = string.Empty;
            int IsSuccess = 1;
            //bool successmsg = true;
            MailMessage objMailMessage = new MailMessage();
            try
            {
                //Create an object of MailMessage class
                //MailMessage objMailMessage = new MailMessage();

                //Adding TO Recipients
                if (!string.IsNullOrEmpty(sMailTO)) { objMailMessage.To.Add(new MailAddress(sMailTO)); }

                //if (!string.IsNullOrEmpty(sMailCC)) { objMailMessage.To.Add(new MailAddress(sMailCC)); }

                //if (!string.IsNullOrEmpty(sMailBCC)) { objMailMessage.To.Add(new MailAddress(sMailBCC)); }

                //if (!string.IsNullOrEmpty(sAttachment)) { objMailMessage.Attachments.Add(new Attachment(sAttachment)); }


                objMailMessage.Subject = strSubject;
                objMailMessage.Body = strBody;


                SMTP = SMTPServer;
                OutgoingMailServerPort = OutGoingMailPort;
                sMailFrom = MailFrom;

                objMailMessage.From = new MailAddress(sMailFrom, "Continuity Vault");
                objMailMessage.IsBodyHtml = true;
                objMailMessage.Priority = MailPriority.High;


                //Create an object of SmtpClient by passing ur SMTP Server name and SMTP port to be used. By default port 25 can be used.
                var objSMTP = new SmtpClient(SMTP, Convert.ToInt32(OutgoingMailServerPort)) { UseDefaultCredentials = false };
                objSMTP.Credentials = new NetworkCredential(SMTPUserName, SMTPPassword);

                objSMTP.EnableSsl = Enablessl;

                //Gmail Example
                //SmtpClient smtp = new SmtpClient();
                // smtp.Host = "smtp.gmail.com";
                // smtp.Port = 587;

                // smtp.Credentials = new NetworkCredential(
                //     "<EMAIL>", "password");
                //  smtp.EnableSsl = true;


                //Call the Send  method of SmtpClient object and pass the MailMessage object, while will finally send the email message to the recipient's emailID

                objSMTP.Send(objMailMessage);


            }
            catch (Exception e)
            {
                IsSuccess = 0;
                sMessage = e.ToString();
                
            }
            //if (!string.IsNullOrEmpty(sMessage))
            //{
            //    IsSuccess = false;
            //}
            //else
            //{
            //    IsSuccess= true;
            //}

            NotificationMasters notificationMasters = new NotificationMasters
            {
                NotificationMode = 1,
                Body = objMailMessage.Body,
                Subject = objMailMessage.Subject,
                NotifiedBy = 1,
                NotificationType = 1,
                FYAI = 1,
                GUIDAttachment = "GUIDAttachment",
                IsSuccess = IsSuccess,
                NotificationTo = Convert.ToString(objMailMessage.To),
                NotificationCC = Convert.ToString(objMailMessage.CC),
                NotificationBCC = Convert.ToString(objMailMessage.Bcc),
                Attachment = "attachment",
                OrgID = Convert.ToInt32(OrgID)
            };

            bool result = _objSrv.NotificationMasterSave(notificationMasters);
            return sMessage;
        }

    }
}
