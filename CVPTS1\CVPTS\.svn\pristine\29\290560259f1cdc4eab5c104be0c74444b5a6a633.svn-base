﻿@model IEnumerable<BCM.BusinessClasses.ResourcesReminder>

@{
    ViewBag.Title = "UserReminder";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int iIndex = 0;
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Validate BCM Users</h6>

    <div class="d-flex gap-3 justify-content-end align-items-end">
        <div class="dropdown">
            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                <i class="cv-filter align-middle" title="View Filter"></i>
            </button>
            <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                <div class="mb-3">
                    <label>Organizations</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                        <select class="form-select form-control" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.orgInfo,"Id","OrganizationName"))">
                            <option selected value="0">-- All Organizations --</option>
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Units</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                        <select class="form-select form-control" autocomplete="off" id="unitSearch" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.unit,"UnitID","UnitName"))">
                            <option selected value="0">-- All Units --</option>
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Units</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-user"></i></span>
                        <select class="form-select form-control" id="unitHeadID" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.resourceInfo,"ResourceId","ResourceName"))">
                            <option selected value="0">-- All Resources --</option>
                        </select>
                    </div>
                </div>                                               
            </form>
        </div>
        <div class="input-group ">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>

        <button type="button" class="btn icon-btn btn-primary btn-sm" id="btnSendReminders" data-bs-toggle="modal" data-bs-target="#SendReminderModal">Send&nbsp;Reminders</button>
        <button type="button" class="btn btn-secondary btn-sm ms-2" id="btnDebugCount" hidden>Debug Count</button>
    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th>#</th>
                <th>User Details</th>
                <th>Is Active</th>
                <th>Active on SMS</th>
                <th>Active on Email</th>
                <th>SMS_Reminder Sent On</th>
                <th>Email_Reminder Sent On</th>
                <th>Next Reminder Due Date</th>
                <th hidden>SMS</th>
                <th hidden>Email</th>
                <th>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="selectAll">
                        <label class="form-check-label" for="selectAll">Select All</label>
                    </div>
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                iIndex++;
                <tr>
                    <td>@iIndex</td>
                    <td>
                        <table>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold"><i class="cv-user"></i></td>
                                    <td> : </td>
                                    <td>@item.ResourceName</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold"><i class="cv-phone"></i></td>
                                    <td>:</td>
                                    <td>@item.MobilePhone</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold"><i class="cv-mail"></i></td>
                                    <td>:</td>
                                    <td><a class="text-primary" href="#">@item.CompanyEmail</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                    <td></td>
                    <td>
                        @if (item.IsActive_Mobile == "1")
                        {
                            <i class="cv-check"></i>
                        }
                        else
                        {
                            <i class="cv-reject"></i>
                        }

                    </td>
                    <td>
                        @if (item.IsActive_Email == "1")
                        {
                            <i class="cv-check"></i>
                        }
                        else
                        {
                            <i class="cv-reject"></i>
                        }
                    </td>
                    <td>@item.lastSMSSentOn</td>
                    <td>@item.lastEmailSentOn</td>
                    <td></td>
                    <td hidden>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="<EMAIL>" @(string.IsNullOrEmpty(item.MobilePhone) ? "disabled" : "")>
                        </div>
                    </td>
                    <td hidden>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="<EMAIL>" @(string.IsNullOrEmpty(item.CompanyEmail) ? "disabled" : "")>
                        </div>
                    </td>
                    <td>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input user-checkbox" value="@item.ResourceID" id="<EMAIL>">
                        </div>
                    </td>
                </tr>
            }

        </tbody>
    </table>
</div>




<!-- Send Reminder Modal -->
<div class="modal fade" id="SendReminderModal" tabindex="-1" aria-labelledby="SendReminderModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="SendReminderModalLabel">Send User Reminders</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" tabindex="-1"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="cv-info me-2"></i>
                            <span id="selectedUsersCount">0</span> user(s) selected for reminder notification.
                            <div id="selectedUsersList" class="mt-2">
                                <small><strong>Selected Users:</strong> <span id="selectedUsersNames"></span></small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Notification Type</label>
                            <div class="d-flex gap-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sendEmail" checked>
                                    <label class="form-check-label" for="sendEmail">
                                        <i class="cv-mail me-1"></i>Email
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sendSMS" checked>
                                    <label class="form-check-label" for="sendSMS">
                                        <i class="cv-phone me-1"></i>SMS
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Custom Message (Optional)</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <textarea class="form-control" id="customMessage" rows="3" placeholder="Enter additional message to include in the reminder..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Preview</label>
                            <div class="border rounded p-3 bg-light">
                                <small class="text-muted">
                                    <strong>Email:</strong> A professional reminder email will be sent with account validation instructions.<br>
                                    <strong>SMS:</strong> A concise SMS reminder will be sent to mobile numbers.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary">
                    <i class="cv-note me-1"></i>
                    <small>Note: Only users with valid email/phone numbers will receive notifications</small>
                </span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnConfirmSendReminders">
                        <i class="cv-send me-1"></i>Send Reminders
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Send Reminder Modal -->
<!-- Progress Modal -->
<div class="modal fade" id="ProgressModal" tabindex="-1" aria-labelledby="ProgressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Sending Reminders</h6>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <div class="progress mb-3" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" id="progressBar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        <span id="progressText">0%</span>
                    </div>
                </div>
                <div class="mb-2">
                    <strong id="progressStatus">Initializing...</strong>
                </div>
                <div id="progressDetails" class="text-muted small">
                    Preparing to send reminders...
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Progress Modal -->

<script>
    // Ensure jQuery is loaded before running
    (function() {
        function initializeReminders() {
            if (typeof jQuery === 'undefined' || typeof $ === 'undefined') {
                console.error('jQuery is not loaded!');
                return;
            }

            console.log('jQuery loaded, initializing reminders...');

        // Simple function to get selected count
        function getSelectedCount() {
            return $('.user-checkbox:checked').length;
        }

        // Simple function to update all count displays
        function updateAllCounts() {
            debugger;
            var count = getSelectedCount();
            console.log('Updating count to:', count);

            // Update modal count
            $('#selectedUsersCount').text(count);

            // Update button text
            if (count > 0) {
                $('#btnSendReminders').html(`Send&nbsp;Reminders (${count})`);
            } else {
                $('#btnSendReminders').html('Send&nbsp;Reminders');
            }

            // Update user names display
            updateUserNamesDisplay(count);
        }

        // Function to update user names display
        function updateUserNamesDisplay(count) {
            if (count > 0) {
                var userNames = [];
                $('.user-checkbox:checked').each(function() {
                    var row = $(this).closest('tr');
                    var userName = row.find('td:eq(1) table tbody tr:first-child td:eq(2)').text().trim();
                    if (userName) {
                        userNames.push(userName);
                    }
                });

                var displayNames = userNames.slice(0, 3).join(', ');
                if (userNames.length > 3) {
                    displayNames += ` and ${userNames.length - 3} more`;
                }
                $('#selectedUsersNames').text(displayNames);
                $('#selectedUsersList').show();
            } else {
                $('#selectedUsersList').hide();
            }
        }

        // Select All functionality
        $(document).on('change', '#selectAll', function() {
            console.log('Select All clicked:', $(this).is(':checked'));
            $('.user-checkbox').prop('checked', $(this).is(':checked'));
            updateAllCounts();
        });

        // Individual checkbox change
        $(document).on('change', '.user-checkbox', function() {
            console.log('Checkbox changed:', $(this).val(), $(this).is(':checked'));

            // Update select all checkbox
            var totalCheckboxes = $('.user-checkbox').length;
            var checkedCheckboxes = $('.user-checkbox:checked').length;
            $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);

            updateAllCounts();
        });

        // Send Reminders button click - intercept before modal opens
        $(document).on('click', '#btnSendReminders', function(e) {
            console.log('Send Reminders button clicked');
            var selectedCount = getSelectedCount();
            console.log('Selected users on button click:', selectedCount);

            if (selectedCount === 0) {
                e.preventDefault();
                e.stopPropagation();
                showToast('Please select at least one user to send reminders.', 'warning');
                return false;
            }

            // Update counts before modal opens (let Bootstrap handle the opening)
            updateAllCounts();
            console.log('Allowing modal to open with', selectedCount, 'users selected');
        });

        // When modal opens, ensure counts are updated
        $('#SendReminderModal').on('shown.bs.modal', function() {
            console.log('Modal opened, updating counts...');
            updateAllCounts();
        });

        // Fix accessibility issue - handle modal close events
        $('#SendReminderModal').on('hide.bs.modal', function() {
            $(this).find(':focus').blur();
        });

        $('#SendReminderModal').on('hidden.bs.modal', function() {
            $('#customMessage').val('');
            $(this).removeAttr('aria-hidden');
        });

        // Confirm Send Reminders
        $('#btnConfirmSendReminders').click(function() {
            var selectedUserIds = [];
            $('.user-checkbox:checked').each(function() {
                selectedUserIds.push(parseInt($(this).val()));
            });

            if (selectedUserIds.length === 0) {
                showToast('Please select at least one user to send reminders.', 'warning');
                return;
            }

            var sendEmail = $('#sendEmail').is(':checked');
            var sendSMS = $('#sendSMS').is(':checked');

            if (!sendEmail && !sendSMS) {
                showToast('Please select at least one notification type (Email or SMS).', 'warning');
                return;
            }

            var customMessage = $('#customMessage').val().trim();

            var requestData = {
                SelectedUsers: selectedUserIds,
                SendEmail: sendEmail,
                SendSMS: sendSMS,
                CustomMessage: customMessage
            };

            // Start the reminder process
            startReminderProcess(requestData);
        });

        // Start reminder process with real-time tracking
        function startReminderProcess(requestData) {
            // Show progress modal
            showProgressModal();

            // Disable send button
            $('#btnConfirmSendReminders').prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i>Starting...');

            $.ajax({
                url: '@Url.Action("SendReminders", "UserReminder", new { area = "BCMResourceManagement" })',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    if (response.success) {
                        // Hide send modal and start tracking progress
                        $('#SendReminderModal').modal('hide');
                        trackProgress(response.jobId, response.totalUsers);
                    } else {
                        hideProgressModal();
                        showToast(response.message, 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    hideProgressModal();
                    console.error('Error starting reminders:', error);
                    showToast('An error occurred while starting reminders. Please try again.', 'danger');
                },
                complete: function() {
                    // Re-enable button
                    $('#btnConfirmSendReminders').prop('disabled', false).html('<i class="cv-send me-1"></i>Send Reminders');
                }
            });
        }

        // Track progress in real-time
        function trackProgress(jobId, totalUsers) {
            var progressInterval = setInterval(function() {
                $.ajax({
                    url: '@Url.Action("GetReminderProgress", "UserReminder", new { area = "BCMResourceManagement" })',
                    type: 'GET',
                    data: { jobId: jobId },
                    success: function(response) {
                        if (response.success) {
                            updateProgressDisplay(response.progress);

                            if (response.progress.isCompleted) {
                                clearInterval(progressInterval);
                                setTimeout(function() {
                                    hideProgressModal();
                                    showCompletionSummary(response.progress);
                                    resetForm();
                                }, 2000);
                            }
                        }
                    },
                    error: function() {
                        clearInterval(progressInterval);
                        hideProgressModal();
                        showToast('Error tracking progress', 'warning');
                    }
                });
            }, 1000); // Update every second
        }

        // Update progress display
        function updateProgressDisplay(progress) {
            $('#progressStatus').text(progress.status);
            $('#progressBar').css('width', progress.percentComplete + '%').attr('aria-valuenow', progress.percentComplete);
            $('#progressText').text(progress.percentComplete + '%');
            $('#progressDetails').html(
                `Processed: ${progress.processedUsers}/${progress.totalUsers} users<br>` +
                `Emails sent: ${progress.emailsSent}<br>` +
                `SMS sent: ${progress.smsSent}`
            );
        }

        // Show completion summary
        function showCompletionSummary(progress) {
            var message = `Reminders completed! Emails: ${progress.emailsSent}, SMS: ${progress.smsSent}`;
            if (progress.errors && progress.errors.length > 0) {
                message += ` (${progress.errors.length} errors)`;
            }
            showToast(message, progress.errors.length > 0 ? 'warning' : 'success');
        }

        // Reset form after completion
        function resetForm() {
            $('#customMessage').val('');
            $('.user-checkbox').prop('checked', false);
            $('#selectAll').prop('checked', false);
            updateAllCounts();
        }

        // Show progress modal
        function showProgressModal() {
            $('#ProgressModal').modal('show');
            // Reset progress display
            $('#progressBar').css('width', '0%').attr('aria-valuenow', 0);
            $('#progressText').text('0%');
            $('#progressStatus').text('Initializing...');
            $('#progressDetails').html('Preparing to send reminders...');
        }

        // Hide progress modal
        function hideProgressModal() {
            $('#ProgressModal').modal('hide');
        }

        // Toast notification function
        function showToast(message, type) {
            $('#liveToast .toast-body .d-flex span:last-child').text(message);
            const toastElement = $('#liveToast');
            toastElement.removeClass('bg-success bg-warning bg-danger');
            toastElement.addClass('bg-' + type);
            const toastLiveExample = document.getElementById('liveToast');
            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
            toastBootstrap.show();
        }

        // Debug button to test count
        $(document).on('click', '#btnDebugCount', function() {
            var totalCheckboxes = $('.user-checkbox').length;
            var checkedCheckboxes = $('.user-checkbox:checked').length;
            var selectAllChecked = $('#selectAll').is(':checked');

            console.log('=== DEBUG INFO ===');
            console.log('Total checkboxes found:', totalCheckboxes);
            console.log('Checked checkboxes:', checkedCheckboxes);
            console.log('Select All checked:', selectAllChecked);
            console.log('selectedUsersCount element exists:', $('#selectedUsersCount').length > 0);
            console.log('Current count text:', $('#selectedUsersCount').text());

            // Force update
            updateAllCounts();

            alert(`Debug Info:\nTotal: ${totalCheckboxes}\nChecked: ${checkedCheckboxes}\nSelect All: ${selectAllChecked}\nModal Count: ${$('#selectedUsersCount').text()}`);
        });

            // Initialize counts
            updateAllCounts();

            // Also initialize after a short delay to ensure DOM is ready
            setTimeout(function() {
                updateAllCounts();
                console.log('Initial count set after delay');
            }, 500);

            // Show progress modal
            function showProgressModal() {
                $('#ProgressModal').modal('show');
                // Reset progress display
                $('#progressBar').css('width', '0%').attr('aria-valuenow', 0);
                $('#progressText').text('0%');
                $('#progressStatus').text('Initializing...');
                $('#progressDetails').html('Preparing to send reminders...');
            }

            // Hide progress modal
            function hideProgressModal() {
                $('#ProgressModal').modal('hide');
            }

            // Toast notification function
            function showToast(message, type) {
                $('#liveToast .toast-body .d-flex span:last-child').text(message);
                const toastElement = $('#liveToast');
                toastElement.removeClass('bg-success bg-warning bg-danger');
                toastElement.addClass('bg-' + type);
                const toastLiveExample = document.getElementById('liveToast');
                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                toastBootstrap.show();
            }
        }

        // Try to initialize immediately if jQuery is available
        if (typeof jQuery !== 'undefined') {
            $(document).ready(initializeReminders);
        } else {
            // Wait for jQuery to load
            var checkJQuery = setInterval(function() {
                if (typeof jQuery !== 'undefined') {
                    clearInterval(checkJQuery);
                    $(document).ready(initializeReminders);
                }
            }, 100);

            // Timeout after 10 seconds
            setTimeout(function() {
                clearInterval(checkJQuery);
                console.error('jQuery failed to load within 10 seconds');
            }, 10000);
        }
    })();

    // Fallback: Simple vanilla JavaScript approach if jQuery fails
    if (typeof jQuery === 'undefined') {
        console.warn('jQuery not available, using vanilla JavaScript fallback');

        document.addEventListener('DOMContentLoaded', function() {
            // Simple vanilla JS fallback for basic functionality
            var sendButton = document.getElementById('btnSendReminders');
            if (sendButton) {
                sendButton.addEventListener('click', function(e) {
                    var checkboxes = document.querySelectorAll('.user-checkbox:checked');
                    if (checkboxes.length === 0) {
                        e.preventDefault();
                        e.stopPropagation();
                        alert('Please select at least one user to send reminders.');
                        return false;
                    }
                    console.log('Selected users:', checkboxes.length);
                });
            }
        });
    }
</script>