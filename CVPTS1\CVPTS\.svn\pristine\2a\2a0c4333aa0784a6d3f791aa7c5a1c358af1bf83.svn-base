﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <View_SelectedScaffolderID>RazorViewScaffolder</View_SelectedScaffolderID>
    <View_SelectedScaffolderCategoryPath>root/Common/MVC/View</View_SelectedScaffolderCategoryPath>
    <WebStackScaffolding_ViewDialogWidth>650</WebStackScaffolding_ViewDialogWidth>
    <WebStackScaffolding_IsLayoutPageSelected>True</WebStackScaffolding_IsLayoutPageSelected>
    <WebStackScaffolding_IsPartialViewSelected>False</WebStackScaffolding_IsPartialViewSelected>
    <WebStackScaffolding_IsReferencingScriptLibrariesSelected>False</WebStackScaffolding_IsReferencingScriptLibrariesSelected>
    <WebStackScaffolding_LayoutPageFile />
    <_SelectedScaffolderID>AreaScaffolder</_SelectedScaffolderID>
    <_SelectedScaffolderCategoryPath>root/Common</_SelectedScaffolderCategoryPath>
    <WebStackScaffolding_DependencyDialogWidth>650</WebStackScaffolding_DependencyDialogWidth>
    <Area_SelectedScaffolderID>AreaScaffolder</Area_SelectedScaffolderID>
    <Area_SelectedScaffolderCategoryPath>root/Common</Area_SelectedScaffolderCategoryPath>
    <Controller_SelectedScaffolderID>MvcControllerEmptyScaffolder</Controller_SelectedScaffolderID>
    <Controller_SelectedScaffolderCategoryPath>root/Common/MVC/Controller</Controller_SelectedScaffolderCategoryPath>
    <ActiveDebugProfile>http</ActiveDebugProfile>
    <NameOfLastUsedPublishProfile>E:\Solutions\CVRootCore\CVCoreAugment - Copy\BCM.UI\Properties\PublishProfiles\FolderProfile.pubxml</NameOfLastUsedPublishProfile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebuggerFlavor>ProjectDebugger</DebuggerFlavor>
  </PropertyGroup>
  <ItemGroup>
    <Compile Update="Areas\BCMReports\ReportTemplate\BCMEntitiesReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\BCMReports\ReportTemplate\BCMTrainingReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\BCMReports\ReportTemplate\BIAReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\BCMReports\ReportTemplate\HRReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\BCMReports\ReportTemplate\IncidentSummaryReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\BCMReports\ReportTemplate\KPIReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\BCMReports\ReportTemplate\ProcessRecoveryPriorityReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\BCMReports\ReportTemplate\RiskAssessmentReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\BCMReports\ReportTemplate\RTOSummaryReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\BCMReports\ReportTemplate\ServiceCriticalityReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
  </ItemGroup>
</Project>