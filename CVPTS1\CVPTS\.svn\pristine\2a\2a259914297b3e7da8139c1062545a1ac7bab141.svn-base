﻿@model IEnumerable<BCM.BusinessClasses.BusinessProcessInfo>
@using BCM.Shared
@{
    ViewBag.Title = "Manage Application";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers


@* <style>
    .dataTables_scrollBody {
        max-height: calc(100vh - 245px);
        height: calc(100vh - 245px);
    }
</style> *@

<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">
            BCM Application
        </h6>
        <div class="d-flex gap-3 justify-content-end align-items-end">
            <div class="vendor-section my-0">
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle primary"><i class="cv-current-BCM-entites fs-6"></i></span><span>Total Applications</span><span class="count-primary">@ViewBag.TotalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle bg-white"><i class="cv-all-facilities fs-6 text-primary"></i></span><span>Under BCM Scope</span><span class="text-primary">@ViewBag.UnderBCMCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle warning"><i class="cv-warning fs-6"></i></span><span>Critical Applications</span><span class="count-warning">@ViewBag.CriticalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle success"><i class="cv-success1 fs-6"></i></span><span>Non Critical Applications</span><span class="count-success">@ViewBag.NonCriticalCount</span>
                </div>
            </div>
            <div class="form-check" style="display:none">
                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="-1" checked>
                <label class="form-check-label" for="inlineRadio1">All Other BCM Entities</label>
            </div>
            <div class="form-check" style="display:none">
                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="1">
                <label class="form-check-label" for="inlineRadio2">Entities Under BCM Scope</label>
            </div>
            <div class="form-check" style="display:none">
                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="0">
                <label class="form-check-label" for="inlineRadio3">Entities Not Under BCM Scope</label>
            </div>
            <div class="input-group Search-Input">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control" placeholder="Search">
            </div>
            <div class="dropdown">
                <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                    <i class="cv-filter align-middle" title="View Filter"></i>
                </button>
                <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                    <div class="mb-3">
                        <label>Organizations</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                            <select class="form-select form-control selectized" autocomplete="off" id="ddlOrganization" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.OrgInfo,"Id","OrganizationName"))">
                                <option selected value="0">-- Select Organizations --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Units</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                            <select class="form-select form-control selectized" autocomplete="off" id="ddlUnit" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Unit,"UnitID","UnitName"))">
                                <option selected value="0">-- Select Units --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-department"></i></span>
                            <select class="form-select form-control selectized" autocomplete="off" id="ddlDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Department,"DepartmentID","DepartmentName"))">
                                <option selected value="0">-- select Departments --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Sub Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
                            <select class="form-select form-control selectized" autocomplete="off" id="ddlSubDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.SubDepartment,"SubFunctionID","SubFunctionName"))">
                                <option selected value="0">-- Select SubDepartments --</option>
                            </select>
                        </div>
                    </div>
                @*     <div class="text-end">
                        <button type="submit" class="btn btn-sm btn-primary">Search</button>
                    </div> *@
                </form>
            </div>
            <button type="button" class="btn icon-btn btn-primary btn-sm" id="btnCreate" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</button>
        </div>
    </div>
</div>


<div class="Page-Condant  border-0" >
    <div class="card">
        <div class="card-body py-0">
            <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
                <thead>
                    <tr>
                        <th class="SrNo_th">#</th>
                        <th>Application Name</th>
                        <th>Owner Details</th>
                        <th>Unit</th>
                        <th>Department</th>
                        <th>Sub Department</th>
                        <th style="display:none">Org Level</th>
                        @* <th>RTO / MAO / RPO</th> *@
                        <th>RTO</th>
                        <th>IsCritical</th>
                        <th>Status</th>
                        @* <th>View BIA</th> *@
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="tblBody">
                    @await Html.PartialAsync("_FilteredApplication")
                </tbody>
            </table>
        </div>
    </div>    
</div>
@* Configure Modal*@
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title"> Application Configuration</h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>
@* Configure Modal*@
@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div> *@

<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $(document).on("click", ".Closebtn", function(){
              location.reload();
            });

            $('#btnCreate').click(function () {
                debugger;
                //$.get('/BCMApplicationBIA/ManageApplication/AddBCMApplication', function (data) {
                $.get('@Url.Action("AddBCMApplication", "ManageApplication")', function (data) {
                    $('.modal-body').html(data);
                    $('#CreateModal').modal('show');
                });
            });

            $(document).on('click', '.btnDelete', function () {
                var id = $(this).data('id');
                var EntityTypeID = $(this).data('EntityTypeID');
                var Text = $(this).data('Text');

                //$.get('/BCMApplicationBIA/ManageApplication/DeleteBCMApplication/' + id, function (data) {
                $.get('@Url.Action("DeleteBCMApplication", "ManageApplication")' ,{id:id}, function (data) {
                    // $.get('/Dashboard/_Delete/' + id, function (data) {
                    $('.modal-body').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

            $(document).on('click', '.btnEdit', function () {
                var id = $(this).data('id');
                //$.get('/BCMApplicationBIA/ManageApplication/EditBCMApplication/' + id, function (data) {
                $.get('@Url.Action("EditBCMApplication", "ManageApplication")' ,{id:id}, function (data) {
                    $('.modal-body').html(data);
                    $('#CreateModal').modal('show');
                });
            });


            // $(document).on('click', '.filterButton', function () {
            //     debugger;
            //  if($('#btnTotal').data('clicked', true)) {
            // alert('btnTotal Clicked');
            //   }
            // if($('#btnCritical').data('clicked', true)) {
            //     alert('btnCritical Clicked');
            // }
            // if($('#btnNonCritical').data('clicked', true)) {
            //     alert('btnNonCritical Clicked');
            // }

            // });

             $(document).on('click', '#btnTotal', function () {
                 var ddlOrganizationVal = $('#ddlOrganization').val();
                 var ddlUnitnVal = $('#ddlUnit').val();
                  var ddlDepartmentVal = $('#ddlDepartment').val();
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                var iIsUnderBCM = -1;
                $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
                    $('#tblBody').html(data);
                });
            });

            $(document).on('click', '#btnCritical', function () {
                 var ddlOrganizationVal = $('#ddlOrganization').val();
                 var ddlUnitnVal = $('#ddlUnit').val();
                  var ddlDepartmentVal = $('#ddlDepartment').val();
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                var iIsUnderBCM = 1;
                $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
                      $('#tblBody').html(data);
                });
            });

            $(document).on('click', '#btnNonCritical', function () {
                 var ddlOrganizationVal = $('#ddlOrganization').val();
                 var ddlUnitnVal = $('#ddlUnit').val();
                  var ddlDepartmentVal = $('#ddlDepartment').val();
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                var iIsUnderBCM = 0;
                $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
                      $('#tblBody').html(data);
                });
            });
             
            $('#ddlOrganization').on('change', function () {
                var iOrgID = $('#ddlOrganization').val();
                var iUnitID = $('#ddlUnit').val();
                var iDepartmentID = $('#ddlDepartment').val();
                var iSubDepartmentID = $('#ddlSubDepartment').val();
                $.ajax({
                    url:'@Url.Action("GetAllUnits", "ManageApplication")',
                    type: 'GET',
                    data: {iOrgID: ddlOrganizationVal},
                    success: function (response) {
                        let selectizeInstance = $('#ddlUnit')[0].selectize;
                        selectizeInstance.clear();
                        selectizeInstance.clearOptions();
                        selectizeInstance.addOption({ value: "0", text: "-- All Units --" });
                        selectizeInstance.addItem("0");

                        response && response.forEach(({ unitID, unitName }) => {
                             if (unitID && unitName) {
                                 selectizeInstance.addOption({ value: unitID, text: unitName });
                             }
                        });

                    },                    
                    error: function (xhr, status, error) {
                        console.log("Error in binding dropdown list");
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            })

            $('#ddlUnit').on('change', function () {
                var iOrgID = $('#ddlOrganization').val();
                var iUnitID = $('#ddlUnit').val();
                var iDepartmentID = $('#ddlDepartment').val();
                var iSubDepartmentID = $('#ddlSubDepartment').val();
                $.ajax({
                    url:'@Url.Action("GetAllDepartments", "ManageApplication")',
                    type: 'GET',
                    data: { iUnitID: iUnitID },
                    success: function (response) {
                        let selectizeInstance = $('#ddlDepartment')[0].selectize;
                        selectizeInstance.clear();
                        selectizeInstance.clearOptions();
                        selectizeInstance.addOption({ value: "0", text: "-- All Department --" });
                        selectizeInstance.addItem("0");

                        response && response.forEach(({ departmentID, departmentName }) => {
                             if (departmentID && departmentName) {
                                 selectizeInstance.addOption({ value: departmentID, text: departmentName });
                             }
                        });

                    },                     
                    error: function (xhr, status, error) {
                        console.log("Error in binding dropdown list");
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            })

            $('#ddlDepartment').on('change', function () {
                var iOrgID = $('#ddlOrganization').val();
                var iUnitID = $('#ddlUnit').val();
                var iDepartmentID = $('#ddlDepartment').val();
                var iSubDepartmentID = $('#ddlSubDepartment').val();
                $.ajax({
                    url:'@Url.Action("GetAllSubDepartments", "ManageApplication")',
                    type: 'GET',
                    data: { iDepartmentID: iUnitID },
                    success: function (response) {
                        let selectizeInstance = $('#ddlSubDepartment')[0].selectize;
                        selectizeInstance.clear();
                        selectizeInstance.clearOptions();
                        selectizeInstance.addOption({ value: "0", text: "-- All SubDepartments --" });
                        selectizeInstance.addItem("0");

                        response && response.forEach(({ subFunctionID, subFunctionName }) => {
                             if (subFunctionID && subFunctionName) {
                                 selectizeInstance.addOption({ value: subFunctionID, text: subFunctionName });
                             }
                        });

                    },                     
                    error: function (xhr, status, error) {
                        console.log("Error in binding dropdown list");
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            })

            $('#ddlOrganization,#ddlUnit,#ddlDepartment,#ddlSubDepartment,#inlineRadio1,#inlineRadio2,#inlineRadio3').change(function () {
                var iIsUnderBCM;
                if ($("#inlineRadio1").prop("checked")) {

                        iIsUnderBCM = $('#inlineRadio1').val();
                    }
                    else if ($("#inlineRadio2").prop("checked")) {

                        iIsUnderBCM = $('#inlineRadio2').val();
                    }
                    else if ($("#inlineRadio3").prop("checked")) {

                        iIsUnderBCM = $('#inlineRadio3').val();
                    }
                var ddlOrganizationVal = $('#ddlOrganization').val();
                var ddlUnitnVal = $('#ddlUnit').val();
                var ddlDepartmentVal = $('#ddlDepartment').val();
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val();

                // alert(Radio + " " + ddlOrganizationVal + " " + ddlUnitnVal + " " + ddlDepartmentVal + " " + ddlSubDepartmentVal);
                //$.get('/BCMEntities/ManageBCMEntities/GetFileredProcess', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM, }, function (data) {
                $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
                        $('#tblBody').html(data);                      
                });
            });



             $('#search-inp').keypress(function(){
                     debugger;
                 var TextSearch = $(this).val();
                 var iIsUnderBCM;
                 var ddlDepartmentVal = $('#ddlDepartment').val();
                    var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                    var ddlUnitnVal = $('#ddlUnit').val();
                    var ddlOrganizationVal = $('#ddlOrganization').val();


                 if ($("#inlineRadio1").prop("checked")) {

                        iIsUnderBCM = $('#inlineRadio1').val();
                    }
                    else if ($("#inlineRadio2").prop("checked")) {

                        iIsUnderBCM = $('#inlineRadio2').val();
                    }
                    else if ($("#inlineRadio3").prop("checked")) {

                        iIsUnderBCM = $('#inlineRadio3').val();
                    }

                 //$.get('/BCMProcessBIA/ManageBusinessProcesses/GetSearchProcess', {textSearch : TextSearch,OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal }, function (data) {
                    $.get('@Url.Action("GetSearchApplication", "ManageApplication")', {textSearch : TextSearch,OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal }, function (data) {
                         $('#tblBody').html(data);
                    });
             });


            // $('#inlineRadio1').click(function () {
            //     var iIsUnderBCM = ($(this).val());
            //     var Searchtxt = $('#txtSearch').val();
            //     var ddlOrgSeletedValue = $('#ddlOrganization').val();
            //     var ddlDepartmentValue = $('#ddlDepartment').val();
            //     var ddlUnitValue = $('#ddlUnit').val();
            //     var ddlSubDepartmentValue = $('#ddlSubDepartment').val();

            //     $("#inlineRadio1").prop("checked", true)
            //     $("#inlineRadio2").prop("checked", false)
            //     $("#inlineRadio3").prop("checked", false)

            //     //alert(iIsUnderBCM, ddlOrgSeletedValue, ddlDepartmentValue, ddlUnitValue, ddlSubDepartmentValue)
            //    // $.get('/BCMApplicationBIA/ManageApplication/GetFileredApplication', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: "" }, function (data) {
            //     $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: "" }, function (data) {
            //         $('#example tbody').html(data);
            //     });
            // });

            // $('#inlineRadio2').click(function () {
            //     var Searchtxt = $('#txtSearch').val();
            //     var iIsUnderBCM = ($(this).val());
            //     var ddlOrgSeletedValue = $('#ddlOrganization').val();
            //     var ddlDepartmentValue = $('#ddlDepartment').val();
            //     var ddlUnitValue = $('#ddlUnit').val();
            //     var ddlSubDepartmentValue = $('#ddlSubDepartment').val();

            //     $("#inlineRadio1").prop("checked", false)
            //     $("#inlineRadio2").prop("checked", true)
            //     $("#inlineRadio3").prop("checked", false)

            //     //$.get('/BCMApplicationBIA/ManageApplication/GetFileredApplication', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: "" }, function (data) {
            //     $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: "" }, function (data) {
            //         $('#example tbody').html(data);
            //     });
            // });

            // $('#inlineRadio3').click(function () {
            //     var Searchtxt = $('#txtSearch').val();
            //     var iIsUnderBCM = ($(this).val());
            //     var ddlOrgSeletedValue = $('#ddlOrganization').val();
            //     var ddlDepartmentValue = $('#ddlDepartment').val();
            //     var ddlUnitValue = $('#ddlUnit').val();
            //     var ddlSubDepartmentValue = $('#ddlSubDepartment').val();

            //     $("#inlineRadio1").prop("checked", false)
            //     $("#inlineRadio2").prop("checked", false)
            //     $("#inlineRadio3").prop("checked", true)

            //     //$.get('/BCMApplicationBIA/ManageApplication/GetFileredApplication', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: "" }, function (data) {
            //     $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: "" }, function (data) {
            //         $('#example tbody').html(data);
            //     });
            // });

            // $('#btnSearch').click(function () {
            //     var IsUnderBCMScope = -1;

            //     if ($("#inlineRadio3").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio3").val();
            //     }

            //     if ($("#inlineRadio2").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio2").val();
            //     }

            //     var ddlOrgSeletedValue = $('#ddlOrganization').val();
            //     var ddlDepartmentValue = $('#ddlDepartment').val();
            //     var ddlUnitValue = $('#ddlUnit').val();
            //     var ddlSubDepartmentValue = $('#ddlSubDepartment').val();
            //     var ddlOrgSeletedValue = $('#ddlOrganization').val();

            //     //$.get('/BCMApplicationBIA/ManageApplication/GetFileredApplication', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //     $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //         $('#example tbody').html(data);
            //     });
            // });

            // $('#ddlDepartment').change(function () {
            //     var IsUnderBCMScope = -1;

            //     if ($("#inlineRadio3").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio3").val();
            //     }

            //     if ($("#inlineRadio2").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio2").val();
            //     }

            //     var ddlOrgSeletedValue = $('#ddlOrganization').val();
            //     var ddlDepartmentValue = $(this).val();
            //     var ddlUnitValue = $('#ddlUnit').val();
            //     var ddlSubDepartmentValue = $('#ddlSubDepartment').val();
            //     //$.get('/BCMApplicationBIA/ManageApplication/GetFileredApplication', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //     $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //         $('#example tbody').html(data);
            //     });
            // });

            // $('#ddlOrganization').change(function () {
            //     var IsUnderBCMScope = -1;

            //     if ($("#inlineRadio3").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio3").val();
            //     }

            //     if ($("#inlineRadio2").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio2").val();
            //     }
            //     var ddlOrgSeletedValue = $(this).val();
            //     var ddlUnitValue = $('#ddlUnit').val();
            //     var Searchtxt = $('#txtSearch').val();
            //     var ddlDepartmentValue = $('#ddlDepartment').val();
            //     var ddlSubDepartmentValue = $('#ddlSubDepartment').val();
            //     //$.get('/BCMApplicationBIA/ManageApplication/GetFileredApplication', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //     $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //         $('#example tbody').html(data);
            //     });
            // });

            // $('#ddlUnit').change(function () {
            //     var IsUnderBCMScope = -1;

            //     if ($("#inlineRadio3").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio3").val();
            //     }

            //     if ($("#inlineRadio2").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio2").val();
            //     }

            //     var ddlOrgSeletedValue = $('#ddlOrganization').val();
            //     var selectedValue = $(this).val();
            //     var Searchtxt = $('#txtSearch').val();
            //     $('#ddlDepartment').empty();
            //     var ddlDepartmentValue = $('#ddlDepartment').val();
            //     var ddlSubDepartmentValue = $('#ddlSubDepartment').val();
            //     //$.get('/BCMApplicationBIA/ManageApplication/GetFileredApplication', { OrgID: ddlOrgSeletedValue, UnitID: selectedValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //     $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrgSeletedValue, UnitID: selectedValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //         $('#example tbody').html(data);
            //     });
            // });

            // $('#ddlSubDepartment').change(function () {
            //     var IsUnderBCMScope = -1;

            //     if ($("#inlineRadio3").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio3").val();
            //     }

            //     if ($("#inlineRadio2").is(':checked')) {
            //         IsUnderBCMScope = $("#inlineRadio2").val();
            //     }

            //     var ddlOrgSeletedValue = $('#ddlOrganization').val();
            //     var ddlSubDepartmentValue = $(this).val();
            //     var Searchtxt = $('#txtSearch').val();
            //     var ddlUnitValue = $('#ddlUnit').val();
            //     var ddlDepartmentValue = $('#ddlDepartment').val();
            //     //$.get('/BCMApplicationBIA/ManageApplication/GetFileredApplication', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //     $.get('@Url.Action("GetFileredApplication", "ManageApplication")', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
            //         $('#example tbody').html(data);
            //     });
            // });

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    </script>
}