﻿@model BCM.BusinessClasses.OrgInfoAndAttachments

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}
<form id="editOrganizationForm" asp-action="AddOrg" enctype="multipart/form-data" method="post"
      class="needs-validation progressive-validation" novalidate>
    <div class="modal-body pt-0">

        <div class="row row-cols-2">
            <div class="col">
                <div class="form-group" hidden>
                    <label for="validationCustom01" class="form-label">OrgID</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-login-code"></i></span>
                        <input type="hidden" class="form-control" asp-for="OrgInfo.Id">
                    </div>
                </div>
                <div class="form-group">
                    <label for="validationCustom01" class="form-label">Organization Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Org Name" id="EditOrganizationName"
                               asp-for="OrgInfo.OrganizationName" required pattern="^[A-Za-z\s]+$"
                               title="Please enter alphabetic characters only.">
                    </div>
                    <div class="invalid-feedback">Enter Org Name</div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Organization Group Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                        <select id="editorglist" class="form-select form-control selectized" autocomplete="off"
                                aria-label="Default select example" asp-for="OrgInfo.OrgGroupID" required>
                            <option selected value="0">-- All Org Groups --</option>
                            @foreach (var objOrg in ViewBag.OrgGroup)
                            {
                                <option value="@objOrg.Value">@objOrg.Text</option>
                            }
                        </select>

                    </div>
                    <div class="invalid-feedback">Enter Organization Group Name</div>
                </div>
            </div>
        </div>
        <div class="row row-cols-2">
            <div class="col-12">
                <blockquote class="blockquote">Organization Details</blockquote>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Legal Entity</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-Legal-Entity"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Legal Entity" id="EditLegalEntity"
                               asp-for="OrgInfo.LegalEntity" required pattern="^[A-Za-z\s]+$"
                               title="Please enter alphabetic characters only.">
                    </div>
                    <div class="invalid-feedback">Enter Legal Entity</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Phone</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-phone"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Phone Number"
                               id="OrgHeadPhone" asp-for="OrgInfo.Mobile" required pattern="^(\+\d{1,4})?\d{7,15}$"
                               title="Please enter a valid phone number (with or without country code, e.g., +919876543210 or 9876543210).">
                    </div>
                    <div class="invalid-feedback">Enter Phone Number</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Login Code</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-login-code"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Login Code" id="EditLoginCode"
                               asp-for="OrgInfo.LoginCode" required>
                    </div>
                    <div class="invalid-feedback">Enter Login Code</div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Corporate Address</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-corporate-address"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Corporate Address"
                               asp-for="OrgInfo.CompanyAddress">
                    </div>
                    <div class="invalid-feedback">Enter Corporate Address</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Fax</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-fax"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Fax" id="EditFax"
                               asp-for="OrgInfo.Fax" pattern="^\d{10,15}$"
                               title="Please enter a valid fax number (10 to 15 digits).">
                    </div>
                    <div class="invalid-feedback">Enter Fax</div>
                </div>
            </div>
        </div>
        <div class="row row-cols-2">
            <div class="col-12">
                <blockquote class="blockquote">SPOC Details</blockquote>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">BCP Head</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                        <select class="form-select form-control selectized" id="editheadlist" autocomplete="off"
                                aria-label="Default select example" asp-for="OrgInfo.OrgHeadID">
                            <option selected value="0">-- All Resources --</option>
                            @foreach (var objResource in ViewBag.ResourcesInfo)
                            {
                                <option value="@objResource.Value">@objResource.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Enter Organization Group Name</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-mail"></i></span>
                        <input type="email" class="form-control" placeholder="Enter Email" id="EditSPOCEmail"
                               asp-for="OrgInfo.SPOCEmail" required>
                    </div>
                    <div class="invalid-feedback" id="MailError">Enter SPOC Email</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Upload Logo</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-upload"></i></span>
                        <input type="file" id="txtLogo" name="Attachments.AttachementFile"
                               class="form-control custom-file-input" accept="image/*"
                               asp-for="@Model.Attachments.AttachementFile">
                    </div>
                    <div class="invalid-feedback">Upload Logo</div>
                    <small class="form-text text-muted">Please select an image file (JPG, PNG, GIF, etc.)</small>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter SPOC Name" id="EditSPOCName"
                               asp-for="OrgInfo.SPOCName" required pattern="^[A-Za-z\s]+$"
                               title="Please enter alphabetic characters only.">
                    </div>
                    <div class="invalid-feedback">Enter SPOC Name</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Mobile</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-phone"></i></span>
                        <input type="tel" class="form-control" placeholder="Enter SPOC Mobile" id="EditSPOCMobile"
                               asp-for="OrgInfo.SPOCMobile" required pattern="^(\+\d{1,4})?\d{7,15}$"
                               title="Please enter a valid mobile number (with or without country code, e.g., +919876543210 or 9876543210).">
                    </div>
                    <div class="invalid-feedback">Enter Mobile</div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary">
            <i class="cv-note me-1"></i><small>
                Note : All
                fields are mandatory except optional
            </small>
        </span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>


<script>
    $(document).ready(function () {
        console.log("AddOrganization form ready");

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function () {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function () {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for EditOrganization form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('editOrganizationForm');
                if (!form) {
                    console.error("Form not found with ID: editOrganizationForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function (element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function (input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateEmail function similarly
                const originalValidateEmail = window.BCMValidation.validateEmail;
                window.BCMValidation.validateEmail = function (input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateEmail(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validatePatternInput function to show pattern-specific messages
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function (input, forceValidation = false) {
                    console.log("Custom validatePatternInput called for:", input.id || input.name, "pattern:", input.pattern, "value:", input.value);

                    const inputGroup = input.closest('.input-group');
                    const formGroup = input.closest('.form-group');
                    const feedbackElement = formGroup?.querySelector('.invalid-feedback');

                    if (!feedbackElement) {
                        console.warn("No feedback element found for pattern input:", input.id || input.name || "unnamed input");
                        return true;
                    }

                    // Skip validation if pending and not forced
                    if (!forceValidation && formGroup && formGroup.classList.contains('validation-pending')) {
                        return true;
                    }

                    const pattern = new RegExp(input.getAttribute('pattern'));

                    if (input.value === '') {
                        // Empty value - check if required
                        if (input.hasAttribute('required')) {
                            console.log("Required pattern input is empty");
                            input.classList.add('is-invalid');
                            if (inputGroup) inputGroup.classList.add('is-invalid');
                            feedbackElement.textContent = "This field is required";
                            feedbackElement.style.display = 'block';
                            return false;
                        } else {
                            // Not required and empty is fine
                            input.classList.remove('is-invalid');
                            if (inputGroup) inputGroup.classList.remove('is-invalid');
                            feedbackElement.style.display = 'none';
                            return true;
                        }
                    } else if (!pattern.test(input.value)) {
                        // Invalid pattern - show generic pattern message from title attribute
                        console.log("Pattern validation failed:", input.value);
                        input.classList.add('is-invalid');
                        if (inputGroup) inputGroup.classList.add('is-invalid');

                        // Use title attribute for pattern validation messages (as per user preference)
                        let errorMessage = input.title || "Please enter a valid value";

                        feedbackElement.textContent = errorMessage;
                        feedbackElement.style.display = 'block';
                        console.log("Showing pattern validation message:", errorMessage);
                        return false;
                    } else {
                        // Valid pattern
                        console.log("Pattern is valid");
                        input.classList.remove('is-invalid');
                        if (inputGroup) inputGroup.classList.remove('is-invalid');
                        feedbackElement.style.display = 'none';
                        return true;
                    }
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function (form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add event listeners for pattern validation on input/blur events
                const patternFields = form.querySelectorAll('input[pattern]');
                console.log("Setting up pattern validation for", patternFields.length, "fields");

                patternFields.forEach(function(field) {
                    console.log("Setting up pattern validation for field:", field.id || field.name, "pattern:", field.pattern);

                    // Add input event listener for real-time pattern validation
                    field.addEventListener('input', function() {
                        const formGroup = this.closest('.form-group');
                        if (formGroup && !formGroup.classList.contains('validation-pending')) {
                            console.log("PATTERN INPUT EVENT triggered for:", this.id || this.name, "value:", this.value, "pattern:", this.pattern);
                            window.BCMValidation.validatePatternInput(this);
                        }
                    });

                    // Add blur event listener for pattern validation when field loses focus
                    field.addEventListener('blur', function() {
                        const formGroup = this.closest('.form-group');
                        if (formGroup) {
                            formGroup.classList.remove('validation-pending');
                        }
                        console.log("PATTERN BLUR EVENT triggered for:", this.id || this.name, "value:", this.value, "pattern:", this.pattern);
                        window.BCMValidation.validatePatternInput(this);
                    });
                });

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function (event) {
                    console.log("Edit Form submission triggered");

                    // Debug: Check if file is selected
                    const fileInput = document.getElementById('txtLogo');
                    if (fileInput && fileInput.files.length > 0) {
                        console.log("File selected:", fileInput.files[0].name, "Size:", fileInput.files[0].size);
                    } else {
                        console.log("No file selected");
                    }

                    // Debug: Log form data
                    const formData = new FormData(form);
                    console.log("Form data entries:");
                    for (let [key, value] of formData.entries()) {
                        if (value instanceof File) {
                            console.log(key + ":", value.name, "(" + value.size + " bytes)");
                        } else {
                            console.log(key + ":", value);
                        }
                    }

                    // Check for organization name existence before proceeding
                    const orgNameInput = document.getElementById('EditOrganizationName');
                    const orgGroupSelect = document.getElementById('editorglist');

                    if (orgNameInput && orgGroupSelect) {
                        const orgName = orgNameInput.value.trim();
                        const orgGroupId = orgGroupSelect.value;

                        // If organization name shows as existing, prevent submission
                        if (orgNameInput.classList.contains('is-invalid')) {
                            const formGroup = orgNameInput.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');

                            if (feedbackElement && feedbackElement.textContent === 'Organization name already exists in the selected group') {
                                console.log("Preventing form submission - organization name already exists");
                                event.preventDefault();
                                event.stopPropagation();
                                orgNameInput.focus();
                                return;
                            }
                        }
                    }

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    if (!isValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        // Existing functionality - dropdown change handler
        $('#editheadlist').change(function () {
            var selectedDDL = $(this).attr('id');
            var iId = $(this).val();
            $.ajax({
                // url: '/BCMOrgStructure/Organization/GetResourceDetails/' + iId,
                url: '@Url.Action("GetResourceDetails", "Organization")',
                type: "GET",
                data: { iId: iId },
                success: function (data) {
                    if (data) {
                        if (selectedDDL == "editheadlist") {
                            $("#OrgHeadPhone").val(data.mobile);
                        }
                    } else {
                        console.log("Error While Binding Data.");
                    }
                },
                error: function () {
                    console.log('Error Is Invoked');
                }
            });
        });

        // Organization name validation on OrgGroupID change
        $('#editorglist').change(function () {
            var orgGroupId = $(this).val();
            var orgName = $('#EditOrganizationName').val().trim();

            console.log('OrgGroupID changed to:', orgGroupId, 'Current org name:', orgName);

            // Clear any existing validation state for organization name
            clearOrgNameValidation();

            // If both orgGroupId and orgName are provided, validate
            if (orgGroupId && orgGroupId !== "0" && orgName) {
                validateOrgNameExists(orgName, orgGroupId);
            }
        });

        // Organization name validation on input change
        $('#EditOrganizationName').on('input blur', function () {
            var orgName = $(this).val().trim();
            var orgGroupId = $('#editorglist').val();

            console.log('Org name changed to:', orgName, 'Current OrgGroupID:', orgGroupId);

            // Clear validation state first
            clearOrgNameValidation();

            // If both orgName and orgGroupId are provided, validate
            if (orgName && orgGroupId && orgGroupId !== "0") {
                // Add a small delay to avoid too many requests while typing
                clearTimeout(window.orgNameValidationTimeout);
                window.orgNameValidationTimeout = setTimeout(function() {
                    validateOrgNameExists(orgName, orgGroupId);
                }, 500);
            }
        });

        // Function to validate if organization name exists
        function validateOrgNameExists(orgName, orgGroupId) {
            console.log('Validating org name:', orgName, 'for OrgGroupID:', orgGroupId);

            var orgNameInput = $('#EditOrganizationName');
            var formGroup = orgNameInput.closest('.form-group');
            var feedbackElement = formGroup.find('.invalid-feedback');
            var inputGroup = orgNameInput.closest('.input-group');

            // Show loading state
            showOrgNameValidationLoading(true);

            $.ajax({
                url: '@Url.Action("IsOrgNameExist", "Organization")',
                type: "GET",
                data: {
                    OrgName: orgName,
                    iOrgGroupID: orgGroupId
                },
                success: function (data) {
                    console.log('Org name validation result:', data);

                    // Hide loading state
                    showOrgNameValidationLoading(false);

                    if (data === true) {
                        // Organization name exists - show error
                        orgNameInput.addClass('is-invalid');
                        if (inputGroup.length) inputGroup.addClass('is-invalid');

                        feedbackElement.text('Organization name already exists in the selected group');
                        feedbackElement.show();

                        console.log('Organization name already exists');
                    } else {
                        // Organization name is available - clear error
                        orgNameInput.removeClass('is-invalid');
                        if (inputGroup.length) inputGroup.removeClass('is-invalid');

                        feedbackElement.hide();

                        console.log('Organization name is available');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error validating organization name:', error);
                    // Hide loading state and clear validation state to avoid blocking the user
                    showOrgNameValidationLoading(false);
                    clearOrgNameValidation();
                }
            });
        }

        // Function to show/hide loading state for organization name validation
        function showOrgNameValidationLoading(show) {
            var orgNameInput = $('#EditOrganizationName');
            var inputGroup = orgNameInput.closest('.input-group');
            var loadingIndicator = inputGroup.find('.validation-loading');

            if (show) {
                if (loadingIndicator.length === 0) {
                    inputGroup.append('<span class="validation-loading position-absolute" style="right: 10px; top: 50%; transform: translateY(-50%); z-index: 10;"><i class="fas fa-spinner fa-spin text-muted"></i></span>');
                }
            } else {
                loadingIndicator.remove();
            }
        }

        // Function to clear organization name validation state
        function clearOrgNameValidation() {
            var orgNameInput = $('#EditOrganizationName');
            var formGroup = orgNameInput.closest('.form-group');
            var feedbackElement = formGroup.find('.invalid-feedback');
            var inputGroup = orgNameInput.closest('.input-group');

            // Clear any pending validation timeout
            if (window.orgNameValidationTimeout) {
                clearTimeout(window.orgNameValidationTimeout);
            }

            // Hide loading state
            showOrgNameValidationLoading(false);

            // Only clear the validation state if it's showing our custom message
            if (feedbackElement.text() === 'Organization name already exists in the selected group') {
                orgNameInput.removeClass('is-invalid');
                if (inputGroup.length) inputGroup.removeClass('is-invalid');
                feedbackElement.hide();
            }
        }

        // File upload handler for edit form - improved to show selected file name
        $("#txtLogo").change(function () {
            var fileName = $(this).val().split("\\").pop();
            if (fileName) {
                console.log('Logo file selected for edit:', fileName);
                // You can add visual feedback here if needed
                // For example, show the file name next to the input
                var fileInfo = $(this).closest('.form-group').find('.file-info');
                if (fileInfo.length === 0) {
                    $(this).closest('.form-group').append('<div class="file-info mt-1"><small class="text-success">Selected: <span class="filename"></span></small></div>');
                    fileInfo = $(this).closest('.form-group').find('.file-info');
                }
                fileInfo.find('.filename').text(fileName);
            }
        });

        // Additional file input styling for edit form
        $(".custom-file-input").on("change", function () {
            var fileName = $(this).val().split("\\").pop();
            // Update any custom file input labels if they exist
            var customLabel = $(this).siblings(".custom-file-label");
            if (customLabel.length > 0) {
                customLabel.html(fileName || "Choose file...");
            }
        });

        // Enhanced email validation that works with the framework
        const editEmailElement = document.getElementById("EditSPOCEmail");
        if (editEmailElement) {
            editEmailElement.addEventListener("input", function () {
                const feedbackMessage = document.getElementById("MailError");
                const emailPattern = /^[a-zA-Z0-9._+-]+@@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,9}$/;

                if (this.value.trim() === '') {
                    // If empty, let the framework handle required validation
                    if (feedbackMessage) feedbackMessage.style.display = 'none';
                    this.setCustomValidity("");
                } else if (!emailPattern.test(this.value)) {
                    // If not empty but invalid format
                    if (feedbackMessage) feedbackMessage.style.display = 'block';
                    this.setCustomValidity("Please enter a valid email address (e.g., <EMAIL>).");
                } else {
                    // Valid email
                    if (feedbackMessage) feedbackMessage.style.display = 'none';
                    this.setCustomValidity("");
                }
            });

            // Enhanced email validation on blur
            editEmailElement.addEventListener("blur", function () {
                if (window.BCMValidation) {
                    window.BCMValidation.validateEmail(this, true);
                }
            });
        }
    });
</script>