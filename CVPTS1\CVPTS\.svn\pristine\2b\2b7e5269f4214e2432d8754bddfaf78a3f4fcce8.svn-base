:root {
    /* Colors */
    --bs-body-bg: #f7f7f7;
    --bs-white: #fff;
    --bs-primary: #e63875;
    --bs-secondary: #7c8db5;
    --bs-secondary-rgb: 124, 141, 181;
    --bs-red: #ce2c4a;
    --bs-primary-bg-subtle: #d1dcff;
    --bs-placeholder-font-color: #929292;
    /* RGB Color */
    --bs-primary-rgb: 230, 56, 117;
    --bs-secondary-rgb:127,127,127;
    /* End Colors */
    /* Font */
    --bs-font-family: SF Pro Display;
    --bs-body-font-size-small: 12px;
    --bs-body-font-size: 13px;
    --bs-nav-link-font-size: 14px;
    --bs-modal-sub-title: 14px;
    --bs-sub-title: 14px;
    --bs-page-title-font-size: 16px;
    --bs-nav-icon-font-size: 20px;
    /* End Font */
    /* Border Radius */
    --bs-border-radius: 8px;
    --bs-border-radius-first-child: 8px 0 0 0;
    --bs-border-radius-last-child: 0px 8px 0 0;
    --bs-border-radius-lg: 1rem;
    /* End Border Radius */
}

body {
    font-size: var(--bs-body-font-size);
    transition: var(--tran-05);
    background-color: var(--bs-body-bg);
    font-family: var(--bs-font-family);
}

.sticky-top {
    background-color: var(--bs-body-bg);
}

.w-fit {
    width: fit-content;
}

.cv-na {
    color: var(--bs-red);
}

.card {
    --bs-card-bg: var(--bs-white);
    --bs-card-cap-bg: var(--bs-white);
    border: none;
    border-radius: var(--bs-border-radius);
}

    .card .card-header h6 {
        font-size: var(--bs-modal-sub-title);
        margin-bottom:0px;
    }

.Page-Title {
    font-size: var(--bs-page-title-font-size);
}

.Sub-Title {
    font-size: var(--bs-sub-title);
}

.modal .Sub-Title {
    font-size: var(--bs-modal-sub-title);
    margin-bottom:1rem;
}

.modal {
    --bs-modal-zindex: 1055;
    --bs-modal-width: 500px;
    --bs-modal-padding: 1rem;
    --bs-modal-margin: 0.5rem;
    --bs-modal-bg: var(--bs-white);
    --bs-modal-border-color: var(--bs-border-color-translucent);
    --bs-modal-border-width: none;
    --bs-modal-border-radius: var(--bs-border-radius-lg);
    --bs-modal-box-shadow: var(--bs-box-shadow-sm);
    --bs-modal-inner-border-radius: calc(var(--bs-border-radius-lg) -(var(--bs-border-width)));
    --bs-modal-header-padding-x: 1rem;
    --bs-modal-header-padding-y: 1rem;
    --bs-modal-header-padding: 1rem 1rem;
    --bs-modal-header-border-width: none;
    --bs-modal-footer-border-width: none;
}

    .modal .modal-header .delete-img {
        width: 60%;
        margin-top: -90px;
    }


.modal-dialog-scrollable .modal-body {
    max-height: calc(100vh - 130px);
}


.blockquote {
    margin: 10px 0;
    font-size: 14px;
    background-color: #efefef;
    border-bottom: 1px solid #e1e1e1;
    padding: 4px 10px;
}


.dropdown-menu {
    --bs-dropdown-font-size: var(--bs-body-font-size);
    --bs-dropdown-bg: var(--bs-white);
    box-shadow: var(--bs-box-shadow) !important;
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--bs-primary);
}

.btn-primary {
    background: rgb(230,56,117);
    background: linear-gradient(114deg, rgba(230,56,117,1) 4%, rgba(50,2,132,1) 100%);
    border: none;
}

    .btn-primary:hover {
        background: rgb(230,56,117);
        background: linear-gradient(242deg, rgba(230,56,117,1) 0%, rgba(50,2,132,1) 100%);
        border: none;
    }

.btn-outline-primary {
    --bs-btn-color: rgb(230 56 117);
    --bs-btn-border-color: rgb(230,56,117);
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: rgb(230,56,117);
    --bs-btn-hover-border-color: rgb(230,56,117);
    --bs-btn-focus-shadow-rgb: 13, 110, 253;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: rgb(230,56,117);
    --bs-btn-active-border-color: rgb(230,56,117);
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: rgb(230,56,117);
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: rgb(230,56,117);
    --bs-gradient: none;
}

a {
    text-decoration: none !important;
}

.accordion {
    --bs-accordion-color: var(--bs-body-color);
    --bs-accordion-bg: var(--bs-white);
    --bs-accordion-border-color: var(--bs-border-color);
    --bs-accordion-border-width: var(--bs-border-width);
    --bs-accordion-border-radius: var(--bs-border-radius);
    --bs-accordion-inner-border-radius: calc(var(--bs-border-radius) - (var(--bs-border-width)));
    --bs-accordion-btn-padding-x: 1.25rem;
    --bs-accordion-btn-padding-y: .6rem;
    --bs-accordion-btn-color: var(--bs-body-color);
    --bs-accordion-btn-bg: var(--bs-accordion-bg);
    --bs-accordion-btn-icon-width: 1rem;
    --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
    --bs-accordion-body-padding-x: 0.5rem;
    --bs-accordion-body-padding-y: 0.5rem;
    --bs-accordion-active-color: var(--bs-body-color);
    --bs-accordion-active-bg: var(--bs-secondary-bg);
    --bs-accordion-btn-focus-box-shadow: none;
}

.accordion-button {
    font-size: var(--bs-body-font-size);
}


.form-control.is-valid, .was-validated .form-control:valid {
    border-color: transparent;
    background-image: none;
}

.form-control.is-invalid, .was-validated .form-control:invalid {
    border-color: transparent;
    padding-right: calc(1.5em + .75rem);
    background-image: none;
    background-repeat: no-repeat;
    background-position: right calc(.375em + .1875rem) center;
    background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

.form-select.is-valid:focus, .was-validated .form-select:valid:focus{
    box-shadow:none !important;
    /*border-color:none !important;*/
}