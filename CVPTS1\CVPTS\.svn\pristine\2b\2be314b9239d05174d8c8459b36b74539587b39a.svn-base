﻿@model List<BCM.BusinessClasses.BCMTrainingMaster>
@{
    ViewData["Title"] = "Training and Examination";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Scripts
{
    <script>
        $(document).ready(function() {
            // Handle collapse toggle icons
            $('.collapse').on('show.bs.collapse', function () {
                $(this).prev('.card-header').find('.fas').removeClass('fa-chevron-down').addClass('fa-chevron-up');
            });

            $('.collapse').on('hide.bs.collapse', function () {
                $(this).prev('.card-header').find('.fas').removeClass('fa-chevron-up').addClass('fa-chevron-down');
            });

            // Handle Attend Training button click
            $('.attend-training-btn').on('click', function(e) {
                e.preventDefault();

                var trainingId = $(this).data('training-id');
                var publishid = $(this).data('publish-id');
                alert(publishid);
                var trainingName = $(this).data('training-name');
                var button = $(this);

                console.log('Training ID:', trainingId);
                console.log('Training Name:', trainingName);

                // Validate training ID
                if (!trainingId || trainingId === 'undefined') {
                    showNotification('Invalid training ID. Please try again.', 'error');
                    return;
                }

                // Show loading state
                button.prop('disabled', true);
                button.html('<i class="fas fa-spinner fa-spin me-1"></i>Loading...');

                // Build the URL for the training document
                // var trainingDocUrl = '@Url.Action("TrainingDoc", "TrainingDoc", new { Area = "BCMTraining" })' + '?id=' + trainingId,publishid = publishid;
                var trainingDocUrl = '@Url.Action("TrainingDoc", "TrainingDoc", new { Area = "BCMTraining" })' + '?id=' + trainingId + '&publishid=' + publishid;
                console.log('Redirecting to URL:', trainingDocUrl);

                // Show brief loading message
                showNotification('Loading training document...', 'success');

                // Redirect to training document page in same window
                setTimeout(function() {
                    window.location.href = trainingDocUrl;
                }, 500); // Small delay to show the loading message
            });
        });

        // Function to show notifications
        function showNotification(message, type) {
            var alertClass, iconClass;

            switch(type) {
                case 'success':
                    alertClass = 'alert-success';
                    iconClass = 'fa-check-circle';
                    break;
                case 'warning':
                    alertClass = 'alert-warning';
                    iconClass = 'fa-exclamation-triangle';
                    break;
                case 'error':
                default:
                    alertClass = 'alert-danger';
                    iconClass = 'fa-exclamation-triangle';
                    break;
            }

            var notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
                '<i class="fas ' + iconClass + ' me-2"></i>' + message +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>');

            $('body').append(notification);

            // Auto-hide after 7 seconds for warnings, 5 seconds for others
            var hideDelay = type === 'warning' ? 7000 : 5000;
            setTimeout(function() {
                notification.alert('close');
            }, hideDelay);
        }
    </script>
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex align-items-center mb-4">
        <span class="p-2 bg-white shadow-sm rounded-circle me-2">
            <i class="cv-training align-middle text-primary fs-4"></i>
        </span>
        <h4 class="mb-0 fw-normal">Training and Examination</h4>
    </div>

    <!-- Count Cards Section -->
    <div class="row g-3 mb-4">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center p-4">
                    <div class="p-3 bg-light rounded-circle d-inline-flex mb-3">
                        <i class="cv-total-course fs-2 text-primary"></i>
                    </div>
                    <h2 class="fw-bold mb-1">@ViewBag.TotalCourseCount</h2>
                    <p class="text-muted mb-0">Total Course</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center p-4">
                    <div class="p-3 bg-light rounded-circle d-inline-flex mb-3">
                        <i class="cv-course-pending fs-2 text-warning"></i>
                    </div>
                    <h2 class="fw-bold mb-1">@ViewBag.CoursePendingCount</h2>
                    <p class="text-muted mb-0">Course pending</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center p-4">
                    <div class="p-3 bg-light rounded-circle d-inline-flex mb-3">
                        <i class="cv-course-attended fs-2 text-success"></i>
                    </div>
                    <h2 class="fw-bold mb-1">@ViewBag.CourseAttendedCount</h2>
                    <p class="text-muted mb-0">Course Attended</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Pending Details Section -->
    <div class="card mb-3 border-0 shadow-sm">
        <div class="card-header bg-primary text-white border-0">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="cv-course-pending me-2"></i>
                    <h6 class="mb-0">Course Pending Details</h6>
                </div>
                <button class="btn btn-link text-white p-0" type="button" data-bs-toggle="collapse" data-bs-target="#coursePendingCollapse" aria-expanded="true" aria-controls="coursePendingCollapse">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>
        <div class="collapse show" id="coursePendingCollapse">
            <div class="card-body p-0">
                @if (ViewBag.CoursePendingDetails != null && ((List<BCM.BusinessClasses.BCMTrainingMaster>)ViewBag.CoursePendingDetails).Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">S.No.</th>
                                    <th class="border-0">Training Name</th>
                                    <th class="border-0">Attend Training</th>
                                </tr>
                            </thead>
                            <tbody>
                                @{int pendingIndex = 1;}
                                @foreach (var training in (List<BCM.BusinessClasses.BCMTrainingMaster>)ViewBag.CoursePendingDetails)
                                {
                                    <tr>
                                        <td>@pendingIndex</td>
                                        <td>@training.TrainingName</td>
                                        <td>
                                            <button class="btn btn-primary btn-sm attend-training-btn"
                                                    data-training-id="@training.ID"
                                                    data-publish-id="@training.PublishID"
                                                    data-training-name="@training.TrainingName">
                                                <i class="fas fa-play me-1"></i>
                                                Attend Training
                                            </button>
                                        </td>
                                    </tr>
                                    pendingIndex++;
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4 text-muted">
                        <i class="cv-no-data fs-1 mb-3 d-block"></i>
                        <p>No records to display</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Course Attended Details Section -->
    <div class="card mb-3 border-0 shadow-sm">
        <div class="card-header bg-primary text-white border-0">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="cv-course-attended me-2"></i>
                    <h6 class="mb-0">Course Attended Details</h6>
                </div>
                <button class="btn btn-link text-white p-0" type="button" data-bs-toggle="collapse" data-bs-target="#courseAttendedCollapse" aria-expanded="false" aria-controls="courseAttendedCollapse">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>
        <div class="collapse" id="courseAttendedCollapse">
            <div class="card-body p-0">
                @if (ViewBag.CourseAttendedDetails != null && ((List<BCM.BusinessClasses.BCMTrainingMaster>)ViewBag.CourseAttendedDetails).Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">S.No.</th>
                                    <th class="border-0">Training Name</th>
                                    <th class="border-0">View</th>
                                </tr>
                            </thead>
                            <tbody>
                                @{int attendedIndex = 1;}
                                @foreach (var training in (List<BCM.BusinessClasses.BCMTrainingMaster>)ViewBag.CourseAttendedDetails)
                                {
                                    <tr>
                                        <td>@attendedIndex</td>
                                        <td>@training.TrainingName</td>
                                        <td>
                                            <a href="@Url.Action("TrainingQuestionPaper", "TrainingandExamination", new { Area = "BCMTraining", TrainingID = training.TrainingMasterID != 0 ? training.TrainingMasterID : Convert.ToInt32(training.ID ?? "0"), watch = 1, PublishID = training.PublishID })"
                                               class="btn btn-info btn-sm">
                                                <i class="fas fa-eye me-1"></i>View
                                            </a>
                                        </td>
                                    </tr>
                                    attendedIndex++;
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4 text-muted">
                        <i class="cv-no-data fs-1 mb-3 d-block"></i>
                        <p>No records to display</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Total Course Details Section -->
    <div class="card mb-3 border-0 shadow-sm">
        <div class="card-header bg-primary text-white border-0">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="cv-total-course me-2"></i>
                    <h6 class="mb-0">Total Course Details</h6>
                </div>
                <button class="btn btn-link text-white p-0" type="button" data-bs-toggle="collapse" data-bs-target="#totalCourseCollapse" aria-expanded="false" aria-controls="totalCourseCollapse">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>
        <div class="collapse" id="totalCourseCollapse">
            <div class="card-body p-0">
                @if (ViewBag.TotalCourseDetails != null && ((List<BCM.BusinessClasses.BCMTrainingMaster>)ViewBag.TotalCourseDetails).Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">S.No.</th>
                                    <th class="border-0">Training Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                @{int totalIndex = 1;}
                                @foreach (var training in (List<BCM.BusinessClasses.BCMTrainingMaster>)ViewBag.TotalCourseDetails)
                                {
                                    <tr>
                                        <td>@totalIndex</td>
                                        <td>@training.TrainingName</td>
                                    </tr>
                                    totalIndex++;
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4 text-muted">
                        <i class="cv-no-data fs-1 mb-3 d-block"></i>
                        <p>No records to display</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Loading Training Document...</h5>
                <p class="text-muted mb-0">Please wait while we prepare your training material.</p>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
    }

    .collapse .card-body {
        border-top: 1px solid #dee2e6;
    }

    .table th {
        font-weight: 600;
        color: #495057;
    }

    .btn-link:focus {
        box-shadow: none;
    }

    .card-header .btn-link {
        text-decoration: none;
    }

    .card-header .btn-link:hover {
        text-decoration: none;
    }
</style>
