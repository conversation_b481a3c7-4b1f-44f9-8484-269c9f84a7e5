﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using BCM.Shared;
using System.Security.Cryptography;

using System.Linq;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;
using Microsoft.AspNetCore.Components.Routing;
using System.Data;
using static BCM.Shared.BCPEnum;
using Microsoft.VisualStudio.Web.CodeGenerators.Mvc.Templates.Blazor;
using Newtonsoft.Json;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMFacilities.Controllers;
[Area("BCMFacility")]
public class ManageFacilityController : BaseController
{

    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    private readonly IHttpContextAccessor _HttpContextAccessor;
    readonly int iEntityTypeID = Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.Facilities);
    public ManageFacilityController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger cVLogger, IHttpContextAccessor httpContextAccessor) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _HttpContextAccessor = httpContextAccessor;
        _CVLogger = cVLogger;
        _HttpContextAccessor = httpContextAccessor;
    }
    [HttpGet]
    public JsonResult GetAllUnits(int iOrgID=0)
    {
        try
        {
            // Debug logging
            _CVLogger.LogInfo($"GetAllUnits called with iOrgID: {iOrgID}");
            _CVLogger.LogInfo($"UserDetails - OrgGroupID: {_UserDetails.OrgGroupID}, OrgID: {_UserDetails.OrgID}, UserRoleID: {_UserDetails.UserRoleID}");

            var objDepartmentList = _Utilities.PupulateUnit( _UserDetails.OrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());

            _CVLogger.LogInfo($"GetAllUnits returned {objDepartmentList?.Count ?? 0} units");
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID,int iOrgID)
    {
        try
        {
            var objDepartmentList = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString());
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllSubDepartments(int iDepartmentID,int iOrgID)
    {
        try
        {
            var objSubDepartmentList = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(), iOrgID.ToString() == "0"?_UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID.ToString());

            return Json(objSubDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }
    public void PopulateDropDowns(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            ViewBag.OrgInfo = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());

            //ViewBag.Unit = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString() == "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());
            ViewBag.Unit = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString());

            ViewBag.Department = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString() == "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString());

            ViewBag.SubDepartment = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString() == "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID.ToString());

            ViewBag.FacilityList = _Utilities.GetFacilityListByUnitID(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

            ViewBag.ResourceList = _Utilities.GetResources(_UserDetails.OrgID);

            //ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();

            ViewBag.TypeMasterInfo = _ProcessSrv.GetTypeInfoByEntityID(iEntityTypeID);

            //ViewBag.LocationList = _ProcessSrv.GetLocationListByUnit(_UserDetails.OrgID, _UserDetails.UnitID);
            ViewBag.LocationList = _ProcessSrv.GetLocationMasterList(_UserDetails.OrgID);

            ViewBag.SelectedOrgID = iOrgID;
            ViewBag.SelectedUnitID = iUnitID;
            ViewBag.SelectedDepartmentID = iDepartmentID;
            ViewBag.SelectedSubDepartmentID = iSubDepartmentID;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public IActionResult ManageBCMFacility()
    {
        try
        {

            PopulateDropDowns();
            List<BusinessProcessInfo> lstBusinessProcess = new List<BusinessProcessInfo>();

            lstBusinessProcess = _ProcessSrv.GetBIAFacility_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID));
            if (_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole))
            {
                lstBusinessProcess = _ProcessSrv.GetBIAFacility_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID));
            }
            else
            {
                if (_Utilities.IsOrgHead(_UserDetails.UserID.ToString(), _UserDetails.OrgID.ToString()))
                {
                    //Create this method for List.
                    List<BusinessProcessInfo> lstBusinessProcess1 = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstBusinessProcess = lstBusinessProcess1;

                }
                if (_Utilities.IsUnitHead(_UserDetails.UserID.ToString()))
                {
                    lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstBusinessProcess = _Utilities.FilterListByUnitID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);

                }

                lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                //lstBusinessProcess = _Utilities.FilterListByRoleID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                //lstBusinessProcess = _Utilities.FilterListForOwner(lstBusinessProcess, OrgID, UnitID, DepartmentID, SubDepartmentID, Convert.ToInt32(BCPEnum.EntityType.BusinessProcess), IsUnderBCM);
            }

            ViewBag.Facility = lstBusinessProcess;
            ViewBag.ProcessCode = lstBusinessProcess
                              .GroupBy(p => new { p.ProcessCode, p.ProcessName }).Select(g => g.First())
                              .ToList();


            HttpContext.Session.SetString("TotalCount", lstBusinessProcess.Count.ToString());
            HttpContext.Session.SetString("UnderBCMCount", lstBusinessProcess.Where(x => x.ProcessCode != string.Empty).ToList().Count.ToString());
            HttpContext.Session.SetString("CriticalCount", lstBusinessProcess.Where(x => x.IsCritical == 1 && x.ProcessCode != string.Empty).ToList().Count.ToString());
            HttpContext.Session.SetString("NonCriticalCount", lstBusinessProcess.Where(x => x.IsCritical == 0 && x.ProcessCode != string.Empty).ToList().Count.ToString());

            ViewBag.Holidays = new List<Facility_Holiday>();
            ViewBag.TotalCount = lstBusinessProcess.Count;
            ViewBag.UnderBCMCount = lstBusinessProcess.Where(x => x.ProcessCode != string.Empty).ToList().Count;
            ViewBag.CriticalCount = lstBusinessProcess.Where(x => x.IsCritical == 1 && x.ProcessCode != string.Empty).ToList().Count;
            ViewBag.NonCriticalCount = lstBusinessProcess.Where(x => x.IsCritical == 0 && x.ProcessCode != string.Empty).ToList().Count;
            //ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View();
    }

    [HttpGet]
    public JsonResult GetDepartmentsByUnitID(int unitID)
    {
        try
        {
            var DepartmentList = _ProcessSrv.GetDepartmentByUnitId(unitID);
           // var DepartmentList = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString() == "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString());

            return Json(DepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetFacilitiesByUnit(int unitID)
    {
        try
        {
            _CVLogger.LogInfo($"GetFacilitiesByUnit called with unitID: {unitID}");

            // Use the method that actually filters by unit and org
            var FacilityList = _Utilities.GetFacilitieslistByUnitID(unitID, _UserDetails.OrgID, _UserDetails.OrgGroupID);

            _CVLogger.LogInfo($"GetFacilitiesByUnit returned {FacilityList?.Count ?? 0} facilities");
            return Json(FacilityList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public IActionResult AddBCMFacility()
    {
        FacilityAndHoliday FacilityAndHoliday = new FacilityAndHoliday();
        try
        {
            FacilityAndHoliday.Facility = new Facility();
            FacilityAndHoliday.Facility.OrgID = _UserDetails.OrgID;
            ViewBag.Holidays = new List<Facility_Holiday>();
            PopulateDropDowns();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddBCMFacility", FacilityAndHoliday);
    }

    [HttpPost]
    public IActionResult AddBCMFacility(FacilityAndHoliday objFacilityAndHolidayInfo)
    {
        try
        {
            //Locationmaster objLocationInfo = _ProcessSrv.GetLocationById(Convert.ToInt32(FacilityAndHolidayInfo.Facility.Location));

            objFacilityAndHolidayInfo.Facility.FacilityCreateDate = DateTime.Now;
            objFacilityAndHolidayInfo.Facility.FacilityUpdateDate = DateTime.Now;
            objFacilityAndHolidayInfo.Facility.FacilityID = 0;
            objFacilityAndHolidayInfo.Facility.FacilityStatus = "0";
            objFacilityAndHolidayInfo.Facility.FacilityNumberOfResources = "0";
            objFacilityAndHolidayInfo.Facility.FacilityAccomadatedResoures = "0";
            objFacilityAndHolidayInfo.Facility.FacilityDetails = "";
            objFacilityAndHolidayInfo.Facility.Field1 = "0";
            objFacilityAndHolidayInfo.Facility.Field2 = "0";
            objFacilityAndHolidayInfo.Facility.Field3 = "0";
            objFacilityAndHolidayInfo.Facility.Field4 = "0";
            objFacilityAndHolidayInfo.Facility.EntityTypeID = iEntityTypeID;
            objFacilityAndHolidayInfo.Facility.ChangedBy = _UserDetails.UserID;
            objFacilityAndHolidayInfo.Facility.FacilityIsActive = 1;            

            int facilityID = _ProcessSrv.FacilitiesSave(objFacilityAndHolidayInfo.Facility);
            objFacilityAndHolidayInfo.Facility_Holiday.FacilityID = facilityID;
            objFacilityAndHolidayInfo.Facility_Holiday.IsActive = 1;
            int Result_Fac_Holiday = _ProcessSrv.Facility_Holiday_Save(objFacilityAndHolidayInfo.Facility_Holiday);
            string Sucess = facilityID == 0 ? "false" : "true";
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMFacility");
    }

    [HttpGet]
    public IActionResult DeleteBCMFacility(string id)
    {
        Facility objFacilityInfo = new Facility();
        try
        {
            if (Convert.ToInt32(id) > 0)
            {
                objFacilityInfo = _ProcessSrv.GetFacilitiesById(Convert.ToInt32(id));
                PopulateDropDowns();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_DeleteBCMFacility", objFacilityInfo);
    }

    [HttpPost]
    public IActionResult DeleteBCMFacility(Facility objFacilityInfo)
    {
        try
        {
            bool Sucess = _ProcessSrv.FacilitiesDelete(objFacilityInfo.FacilityID, objFacilityInfo.EntityTypeID, objFacilityInfo.ChangedBy);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMFacility");
    }




    [HttpGet]
    public IActionResult EditBCMFacility(string id)
    {
        FacilityAndHoliday objFacilityInfo = new FacilityAndHoliday();
        try
        {
            if (Convert.ToInt32(Convert.ToInt32(id)) > 0)
            {
                objFacilityInfo.Facility = _ProcessSrv.GetFacilitiesById(Convert.ToInt32(id));
                //Facility_HolidayColl objFacilityColl = new Facility_HolidayColl();
                objFacilityInfo.Facility_Holiday = _ProcessSrv.GetAllFacility_Holidays_ByFacilityID(Convert.ToInt32(id));
                List<TypeMasterInfo> TypeMasterInfo = _ProcessSrv.GetTypeInfoByEntityID(iEntityTypeID);
                ViewBag.typeMasterInfo = TypeMasterInfo;
                ViewBag.facilityList = _Utilities.GetFacilityListByUnitID(objFacilityInfo.Facility.FacilityUnitID);
                PopulateDropDowns();
                //List<LocationMaster> lstLocation = _ProcessSrv.GetLocationListByUnit(_UserDetails.OrgID, Convert.ToInt32(objFacilityInfo.Facility.FacilityUnitID));
                List<LocationMaster> lstLocation =  _ProcessSrv.GetLocationMasterList(_UserDetails.OrgID);
                ViewBag.LocationList = lstLocation;
                List<ResourcesInfo> objresource = _ProcessSrv.GetAllResourcesList();
                ViewBag.ResourceList = objresource;


            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        
        return PartialView("_EditBCMFacility", objFacilityInfo);
    }

    [HttpPost]
    public IActionResult EditBCMFacility(FacilityAndHoliday objFacilityAndHolidayInfo)
    {
        LocationMaster objLocationInfo = new LocationMaster();
        try
        {
            objFacilityAndHolidayInfo.Facility.FacilityIsActive = 1;
            objLocationInfo = _ProcessSrv.GetLocationById(Convert.ToInt32(objFacilityAndHolidayInfo.Facility.Location));
            //int iFacilityID = objFacilityAndHolidayInfo.Facility.FacilityID;
            objFacilityAndHolidayInfo.Facility_Holiday.FacilityID = objFacilityAndHolidayInfo.Facility.FacilityID;
            objFacilityAndHolidayInfo.Facility.EntityTypeID = iEntityTypeID;
            bool facilityID = _ProcessSrv.FacilitiesUpdate(objFacilityAndHolidayInfo.Facility);
            if (objFacilityAndHolidayInfo.Facility_Holiday.ID == 0)
            {
                objFacilityAndHolidayInfo.Facility_Holiday.CreatedBy = _UserDetails.UserID;
                int Holiday = _ProcessSrv.Facility_Holiday_Save(objFacilityAndHolidayInfo.Facility_Holiday);
            }
            else
            {
                objFacilityAndHolidayInfo.Facility_Holiday.UpdatedBy = _UserDetails.UserID;
                objFacilityAndHolidayInfo.Facility_Holiday.IsActive = 1;
                int Result_Fac_Holiday = _ProcessSrv.Facility_Holiday_Update(objFacilityAndHolidayInfo.Facility_Holiday);
            }

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMFacility");
    }

    public PartialViewResult GetFileredFacility(string SearchText = "", int OrgID = 0, int UnitID = 0, int DepartmentID = 0, int SubDepartmentID = 0, int IsUnderBCM = -1,int iIsCritical = 0)
    {
        try
        {
            PopulateDropDowns(_UserDetails.OrgGroupID, OrgID, UnitID, DepartmentID, SubDepartmentID);
            List<BusinessProcessInfo> lstBusinessProcess = _ProcessSrv.GetBIAFacility_OrgUnitLevel(_UserDetails.OrgID);


            //if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
            //{
            //    lstBusinessProcess = _ProcessSrv.GetBIAFacility_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID));
            //}
            //else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
            //{
            //    lstBusinessProcess = _ProcessSrv.GetBIAFacility_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID));
            //}
            //else
            //{
            //    lstBusinessProcess = _ProcessSrv.GetBIAFacility_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID));
            //}

            if (_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole))
            {
                lstBusinessProcess = _ProcessSrv.GetBIAFacility_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID));
            }
            else
            {
                if (_Utilities.IsOrgHead(_UserDetails.UserID.ToString(), _UserDetails.OrgID.ToString()))
                {
                    //Create this method for List.
                    List<BusinessProcessInfo>  lstBusinessProcess1 = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID );
                    lstBusinessProcess = lstBusinessProcess1;

                }
                if (_Utilities.IsUnitHead(_UserDetails.UserID.ToString()))
                {
                    lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstBusinessProcess = _Utilities.FilterListByUnitID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);

                }


                lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                //lstBusinessProcess = _Utilities.FilterListByRoleID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                lstBusinessProcess = _Utilities.FilterListForOwner(lstBusinessProcess, OrgID, UnitID, DepartmentID, SubDepartmentID, Convert.ToInt32(BCPEnum.EntityType.Facilities), IsUnderBCM);
            }
            int CriticalCount = 0;
            if (iIsCritical != -1)
            {
                lstBusinessProcess = lstBusinessProcess.Where(x => x.IsCritical == iIsCritical && x.ProcessCode != string.Empty).ToList();
            }

            if (SearchText != string.Empty)
            {
                lstBusinessProcess = lstBusinessProcess.Where(x => x.ProcessName.Contains(SearchText) || x.UnitName.Contains(SearchText) || x.OrgName.Contains(SearchText)
                                  || x.OrgName.Contains(SearchText) || x.DepartmentName.Contains(SearchText) || x.SubFunctionName.Contains(SearchText)).ToList();

            }

            //ViewBag.Facility = lstBusinessProcess;
            ViewBag.ProcessCode = lstBusinessProcess
                              .GroupBy(p => new { p.ProcessCode, p.ProcessName }).Select(g => g.First())
                              .ToList();

            

            ViewBag.TotalCount = HttpContext.Session.GetString("TotalCount");
            ViewBag.UnderBCMCount = HttpContext.Session.GetString("UnderBCMCount");
            ViewBag.CriticalCount = HttpContext.Session.GetString("CriticalCount");
            ViewBag.NonCriticalCount = HttpContext.Session.GetString("NonCriticalCount");
            //ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();


            ViewBag.lstBusinessProcess = lstBusinessProcess;
            ViewBag.Facility = lstBusinessProcess;
            return PartialView("_FilteredFacility");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_FilteredFacility");
    }
}

