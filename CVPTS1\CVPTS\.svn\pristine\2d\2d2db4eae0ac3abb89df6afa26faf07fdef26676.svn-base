.chatbot__button {
    position: absolute;
    right: 10px;
    bottom: 10px;
    cursor: move;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    z-index: 999;
    background: var(--bs-primary);
    animation-name: pulse;
    animation-duration: 1.5s;
    animation-timing-function: ease-out;
    animation-iteration-count: infinite;
}

.AI-Message table thead tr th {
    background-color: #f1f1f1 !important;
    border-radius: 0;
    color: #000 !important;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 var(--bs-primary);
    }

    80% {
        box-shadow: 0 0 0 14px rgba(37, 211, 102, 0);
    }
}

.chatbot__button span {
    position: absolute;
}

    .show-chatbot .chatbot__button span:first-child,
    .chatbot__button span:last-child {
        opacity: 0;
    }

.show-chatbot .chatbot__button span:last-child {
    opacity: 1;
}

.chatbot {
    position: absolute;
    right: 30px;
    bottom: 20px;
    width: 28vw;
    border-radius: 15px;
    box-shadow: 0 0 128px 0 rgba(0, 0, 0, 0.1) 0 32px 64px -48px rgba(0, 0, 0, 0.5);
    transform: scale(0.5);
    transition: transform 0.3s ease;
    overflow: hidden;
    opacity: 0;
    pointer-events: none;
    background: rgb(244,228,239);
    background: linear-gradient(230deg, rgba(244,228,239,1) 0%, rgba(255,255,255,1) 50%, rgba(233,229,242,1) 100%);
    box-shadow: var(--bs-box-shadow-lg) !important;
}

.show-chatbot .chatbot {
    opacity: 1;
    pointer-events: auto;
    transform: scale(1);
    z-index: 9999;
}

.chatbot__header {
    position: relative;
    text-align: center;
    padding: 16px 0;
}

    .chatbot__header span {
        /* display: none;*/
        position: absolute;
        top: 15%;
        right: 20px;
        color: #202020;
        transform: translateY(-50%);
        cursor: pointer;
    }

.chatbot__box {
    height: 395px;
    overflow-y: auto;
    padding: 10px;
    margin-bottom: 0;
}
.chatbot__box p{
    margin-bottom: 5px;
}

.chatbot__chat {
    display: flex;
    align-items: flex-start;
}

.outgoing .User-Message {
    max-width: 75%;
    color: var(--bs-white);
    border-radius: 6px;
    padding: 6px 16px;
    margin-bottom: 0rem;
    background: rgb(230,56,117);
    background: linear-gradient(114deg, rgba(230,56,117,1) 4%, rgba(50,2,132,1) 100%);
    display: grid;
}

        .chatbot__chat p.error {
            /* color: #721c24; */
            /* background: #f8d7da; */
        }

.incoming .AI-Message {
    padding: 6px 16px;
    color: #202020;
    background: #ffffff;
    border-radius: 6px;
    box-shadow: var(--bs-box-shadow-sm) !important;
    display: grid;
    width: 100%;
}

.incoming span {
    /* width: 32px; */
    /* height: 32px; */
    /* line-height: 32px; */
    /* color: #f3f7f8; */
    /* background-color: #227ebb; */
    /* border-radius: 4px; */
    /* text-align: center; */
    /* align-self: flex-end; */
    /* margin: 0 10px 7px 0; */
}

.outgoing {
    justify-content: flex-end;
    margin: 20px 0;
}

.incoming {
    /*justify-content: flex-end;*/
    margin: 10px 0;
}

.chatbot__input-box {
    /* position: absolute; */
    bottom: 0;
    width: 100%;
    display: flex;
    gap: 5px;
    align-items: center;
    padding: 5px 20px;
    height: 42px !important;
    background-color: #fff;
}

.chatbot__textarea {
    width: 100%;
    /* min-height: 55px; */
    /* max-height: 180px; */
    padding: 10px 14px;
    color: #202020;
    border: none;
    outline: none;
    resize: none;
    background: transparent;
}

    .chatbot__textarea::placeholder {
        font-family: 'Poppins', sans-serif;
    }

.chatbot__input-box span {
    color: #202020;
    cursor: pointer;
    /*    visibility: hidden;*/
}

/*.chatbot__textarea:valid ~ span {
    visibility: visible;
}*/

.outgoing img {
    border: 4px solid #f7ebf3;
    border-radius: 25px;
    margin-left: -10px;
    margin-top: -10px;
}

.incoming img {
    border: 3px solid #ffffff;
    border-radius: 25px;
    margin-right: -10px;
    margin-top: -10px;
    z-index: 1;
}

@media (max-width: 490px) {
    .chatbot {
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;
    }

    .chatbot__box {
        height: 90%;
    }

    .chatbot__header span {
        display: inline;
    }
}

.btn-outline-primary {
    font-size: 13px !important;
}

.ChatBot-list .list-group-item {
    background-color: #fff;
    border-color: #e63875;
}

.ChatBot-list .active {
    background-color: #e63875 !important;
    border-color: #e63875 !important;
    color: #fff !important;
}

