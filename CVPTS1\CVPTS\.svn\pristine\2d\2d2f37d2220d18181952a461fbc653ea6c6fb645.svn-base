﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<BCM.BusinessClasses.Attachments>



@if (Model!=null)
{
    int iIndex = 0;
    foreach (var item in Model)
    {
        iIndex++;
        <tr>
            <td>@iIndex</td>
            <td><a class="text-primary" href="#">@item.DocCode</a></td>
            <td>
                @item.AttchmentName <br />
                <span>Version  : <span> @item.version</span></span>
            </td>
            <td>@item.Description</td>
            <td>
                <table>
                    <tbody>
                        <tr>
                            <td class="fw-semibold"><i class="cv-user"></i></td>
                            <td> : </td>
                            <td>@item.username</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                            <td>:</td>
                            <td>@item.Usermobile</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                            <td>:</td>
                            <td><a class="text-primary" href="#">@item.usermail</a></td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr title="RTO">
                            <td class="fw-semibold"><i class="cv-user"></i></td>
                            <td> : </td>
                            <td>
                                @item.CreaterName
                            </td>
                        </tr>
                        <tr title="User MTPOD">
                            <td class="fw-semibold"><i class="cv-calendar"></i> </td>
                            <td>:</td>
                            <td>@item.CreatedDate</td>
                        </tr>
                        <tr title="Calculated RTO">
                            <td class="fw-semibold"><i class="cv-user"></i></td>
                            <td> : </td>
                            <td>
                                @item.UpdaterName
                            </td>
                        </tr>
                        <tr title="Calculated MTPOD">
                            <td class="fw-semibold"><i class="cv-calendar"></i> </td>
                            <td>:</td>
                            <td>@item.UpdatedDate</td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr>
                            <td class="fw-semibold"><i class="cv-user"></i></td>
                            <td> : </td>
                            <td> @item.ApproverName</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                            <td>:</td>
                            <td>@item.App_Usermobile</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                            <td>:</td>
                            <td><a class="text-primary" href="#">@item.App_usermail</a></td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr title="Org Name">
                            <td><i class="cv-organization"></i></td>
                            <td> : </td>
                            <td>@item.OrgName</td>
                        </tr>
                        <tr title="Unit ">
                            <td><i class="cv-unit"></i></td>
                            <td>:</td>
                            <td>@item.UnitName</td>
                        </tr>
                        <tr title="Department">
                            <td><i class="cv-department"></i></td>
                            <td>:</td>
                            <td>@item.DepartmentName</td>
                        </tr>
                        <tr title="Sub Department">
                            <td><i class="cv-subdepartment"></i></td>
                            <td>:</td>
                            <td>@item.SubFunctionName</td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                <span class="text-danger">@item.Status </span>
            </td>
            <td>
                <span class="btn-action" type="button"><i class="cv-edit me-1 btnEdit" data-id="@item.AttachmentId"></i></span>
                <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
            </td>
        </tr>
    }
}