﻿@model BCM.BusinessClasses.KPIMeasurementMasters
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}

<form id="addKPIMeasurementMasterForm" asp-action="AddKPIMeasurementMaster" asp-controller="KPIMeasurementMaster" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="modal-body pt-0">
        <div class="row row-cols-2">
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Objective Measured By</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-measure"></i></span>
                        <input type="text" class="form-control" asp-for="Objective" placeholder="Enter Objective Measured By" required id="Objective" />
                    </div>
                    <div class="invalid-feedback">Enter Objective Measured By</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Effectiveness Criteria</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-criteria"></i></span>
                        <textarea class="form-control" placeholder="Enter Effectiveness Criteria" asp-for="Effectiveness" style="height:0px" required></textarea>
                    </div>
                    <div class="invalid-feedback">Enter Effectiveness Criteria</div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Target</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-target"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Target" asp-for="Target" required id="Target" />
                    </div>
                    <div class="invalid-feedback">Enter Target</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Effectiveness Rating</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-rating"></i></span>
                        <textarea class="form-control" placeholder="Enter Effectiveness Rating" asp-for="EffectivenessRating" style="height:0px" required></textarea>
                    </div>
                    <div class="invalid-feedback">Enter Effectiveness Rating</div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function () {
        console.log("=== KPI MEASUREMENT MASTER ADD FORM INITIALIZING ===");

        // KPI Measurement Master Form Validation - Clean Implementation
        window.KPIMeasurementMasterForm = {
            init: function() {
                console.log("Initializing KPI Measurement Master Form...");
                this.setupFormValidation();
                console.log("KPI Measurement Master Form initialized successfully");
            },

            // Setup form validation and submission
            setupFormValidation: function() {
                var self = this;
                var form = document.getElementById('addKPIMeasurementMasterForm');

                // Initialize BCM Validation if available
                if (typeof window.BCMValidation !== 'undefined') {
                    try {
                        // Store the original content of all invalid-feedback divs
                        const customMessages = {};
                        form.querySelectorAll('.invalid-feedback').forEach(function (element) {
                            // Find the associated input
                            const formGroup = element.closest('.form-group');
                            const input = formGroup?.querySelector('input, select, textarea');
                            if (input) {
                                // Store the custom message using the input's ID or name as the key
                                const key = input.id || input.name || input.getAttribute('asp-for');
                                if (key) {
                                    customMessages[key] = element.textContent.trim();
                                    console.log("Stored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        });

                        // Override the validateInput function to preserve custom messages
                        const originalValidateInput = window.BCMValidation.validateInput;
                        window.BCMValidation.validateInput = function (input, forceValidation = false) {
                            // Get the result from the original function
                            const result = originalValidateInput(input, forceValidation);

                            // If the input is invalid, restore the custom message
                            if (!result) {
                                const key = input.id || input.name || input.getAttribute('asp-for');
                                if (key && customMessages[key]) {
                                    const formGroup = input.closest('.form-group');
                                    const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                    if (feedbackElement) {
                                        // Restore the custom message
                                        feedbackElement.textContent = customMessages[key];
                                        feedbackElement.style.display = 'block';
                                        console.log("Restored custom message for", key, ":", customMessages[key]);
                                    }
                                }
                            }

                            return result;
                        };

                        // Override the validateForm function to restore all custom messages after validation
                        const originalValidateForm = window.BCMValidation.validateForm;
                        window.BCMValidation.validateForm = function (form) {
                            // Get the result from the original function
                            const result = originalValidateForm(form);

                            // Restore all custom messages for invalid inputs
                            form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                                const key = input.id || input.name || input.getAttribute('asp-for');
                                if (key && customMessages[key]) {
                                    const formGroup = input.closest('.form-group');
                                    const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                    if (feedbackElement) {
                                        // Restore the custom message
                                        feedbackElement.textContent = customMessages[key];
                                        feedbackElement.style.display = 'block';
                                        console.log("Restored custom message for", key, ":", customMessages[key]);
                                    }
                                }
                            });

                            return result;
                        };

                        window.BCMValidation.init();
                        window.BCMValidation.addRequiredFieldIndicators(form);
                        window.BCMValidation.addFormatIndicators(form);
                    } catch (e) {
                        console.error("BCM Validation error:", e);
                    }
                }

                // Form submission handler
                $('#addKPIMeasurementMasterForm').on('submit', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log("Form Submit - Validating KPI Measurement Master");
                    self.validateAndSubmit(form);
                });
            },

            // Validate and submit form
            validateAndSubmit: function(form) {
                var self = this;

                // Use BCM validation if available
                if (typeof window.BCMValidation !== 'undefined') {
                    try {
                        window.BCMValidation.showAllValidationMessages(form);
                        var isValid = window.BCMValidation.validateForm(form);

                        if (!isValid) {
                            var firstInvalid = form.querySelector('.is-invalid');
                            if (firstInvalid) {
                                firstInvalid.focus();
                            }
                            return;
                        }
                    } catch (e) {
                        console.error("BCM Validation error:", e);
                        // Fall back to HTML5 validation
                        if (!form.checkValidity()) {
                            form.reportValidity();
                            return;
                        }
                    }
                } else {
                    // Fallback to HTML5 validation
                    if (!form.checkValidity()) {
                        form.reportValidity();
                        return;
                    }
                }

                // All validation passed - submit form via AJAX to handle success/error toasts
                self.submitFormWithToast(form);
            },

            // Submit form with AJAX to show success/error toasts
            submitFormWithToast: function(form) {
                var formData = new FormData(form);

                $.ajax({
                    url: form.action,
                    type: form.method,
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // Show success toast
                        if (window.ToastManager) {
                            window.ToastManager.showSuccess('KPI Measurement Master added successfully!');
                        }

                        // Close modal if it exists
                        var modal = $('#addKPIMeasurementMasterForm').closest('.modal');
                        if (modal.length > 0) {
                            modal.modal('hide');
                        }

                        // Refresh the page or reload data table if needed
                        setTimeout(function() {
                            if (typeof window.location !== 'undefined') {
                                window.location.reload();
                            }
                        }, 1500);
                    },
                    error: function(xhr, status, error) {
                        // Show error toast
                        if (window.ToastManager) {
                            var errorMessage = 'Failed to add KPI Measurement Master. Please try again.';

                            // Try to get specific error message from response
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            } else if (xhr.responseText) {
                                try {
                                    var errorData = JSON.parse(xhr.responseText);
                                    if (errorData.message) {
                                        errorMessage = errorData.message;
                                    }
                                } catch (e) {
                                    // Use default error message
                                }
                            }

                            window.ToastManager.showError(errorMessage);
                        }
                    }
                });
            }
        };

        // Toast Implementation using existing #liveToast from layout
        if (typeof window.ToastManager === 'undefined') {
            window.ToastManager = {
                // Show success toast
                showSuccess: function(message) {
                    this.showToast(message, 'success');
                },

                // Show error toast
                showError: function(message) {
                    this.showToast(message, 'danger');
                },

                // Show warning toast
                showWarning: function(message) {
                    this.showToast(message, 'warning');
                },

                // Show info toast
                showInfo: function(message) {
                    this.showToast(message, 'info');
                },

                // Generic toast show function using existing #liveToast
                showToast: function(message, type) {
                    console.log("Showing toast:", message, "Type:", type);

                    // Update toast message using the existing structure
                    $('#liveToast .toast-body .d-flex span:last-child').text(message);

                    // Update toast color
                    const toastElement = $('#liveToast');
                    toastElement.removeClass('bg-success bg-warning bg-danger bg-info');
                    toastElement.addClass('bg-' + type);

                    // Show the toast using existing #liveToast
                    const toastLiveExample = document.getElementById('liveToast');
                    if (toastLiveExample) {
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();
                    } else {
                        console.error("liveToast element not found in layout");
                    }
                }
            };
        }

        // Wait for DOM and other scripts to load
        setTimeout(function() {
            console.log("Initializing KPI Measurement Master Form after delay...");
            window.KPIMeasurementMasterForm.init();
        }, 100);
    });
</script>