﻿@{
    ViewBag.Title = "Business Process Form";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var selectedRecordID = TempData["RecordID"];

}
@model BCM.BusinessClasses.BusinessProcessInfoAndReviewHistory
@using static BCM.Shared.Common;
@using static BCM.Shared.BCPEnum
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">@ViewBag.Title</h6>
    </div>
</div>
<form asp-action="BusinessPocessSave" method="post" id="businessPocessSave" class="needs-validation progressive-validation" novalidate>
    <input type="hidden" id="EntityID" class="form-control" name="EntityID" value="@Model.BusinessProcessInfo.EntityTypeID" />
    <input type="hidden" id="RecordID" class="form-control" name="RecordID" value="@Model.BusinessProcessInfo.RecordID" />
    <input type="hidden" id="ProcessID" class="form-control" name="ProcessID" value="@Model.BusinessProcessInfo.ProcessID" />
    <div class="Page-Condant card border-0">
        <div style="height: calc(100vh - 134px);overflow-y: auto;padding: 12px;">
            <div class="d-flex justify-content-between gap-5 mb-2 p-3">
                <div class="w-75">
                    <div>
                        <table class="w-100">
                            <tbody>
                                <tr>
                                    <td>
                                        <i class="cv-version me-1"></i>Version
                                    </td>
                                    <td>:</td>
                                    <td>
                                        @Model.BusinessProcessInfo.Version
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-organization me-1"></i>Organization
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="form-group">
                                            <div class="input-group">
                                                <select class="form-select form-control selectized" autocomplete="off" id="ddlOrganization" asp-for="@Model.BusinessProcessInfo.OrgID" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstOrg,"Id","OrganizationName"))" required>
                                                    <option selected value="0">-- Select Organizations --</option>
                                                </select>
                                            </div>
                                            <div class="invalid-feedback">Select Organization</div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-department me-1"></i>Department
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="form-group">
                                            <div class="input-group">
                                                <select autocomplete="off" class="form-select form-control selectized" id="ddlDepartment" asp-for="@Model.BusinessProcessInfo.DepartmentID" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstDepartment,"DepartmentID","DepartmentName"))" required>
                                                    <option selected disabled value="0">-- Select Departments --</option>
                                                </select>
                                            </div>
                                            <div class="invalid-feedback">Select Department</div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-entity-type me-1"></i>BCM Entity Type
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="form-group">
                                            <div class="input-group">
                                                <select class="form-select form-control selectized" id="ddlEntityTypes" asp-for="@Model.BusinessProcessInfo.EntityTypeID" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.EntityTypes,"BCMEntityID","BCMEntityName"))" required>
                                                    <option selected disabled value="0">-- All Entity Types --</option>
                                                </select>
                                            </div>
                                            <div class="invalid-feedback">Select BCM Entity Type</div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-approver me-1"></i>Approver
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <div class="form-group w-100">
                                                <div class="input-group">
                                                    @*  <select class="form-select form-control" id="ddlApprover" asp-for="ApproverID" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.lstResource,"ResourceId","ResourceName"))">
                                                    <option selected value="0">-- All Resources --</option>
                                                    </select> *@

                                                    <select id="ddlApprover" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example" asp-for="@Model.BusinessProcessInfo.ApproverID" required>
                                                        <option selected disabled value="0">-- All Resources --</option>
                                                        @foreach (var objResource in ViewBag.lstResource)
                                                        {
                                                            <option value="@objResource.Value">@objResource.Text</option>
                                                        }
                                                    </select>

                                                </div>
                                                <div class="invalid-feedback">Approver</div>
                                            </div>
                                            <button class="btn btn-sm btn-primary" style="display:none" data-bs-toggle="modal" data-bs-target="#EscalationModal"><i class="cv-escalation"></i></button>
                                        </div>
                                    </td>

                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-user-profile me-1"></i>BIA Profile
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <div class="form-group w-100">
                                                <div class="input-group">
                                                    <select id="ddlBIAProfile" class="form-select form-control selectized" asp-for="@Model.BusinessProcessInfo.ProfileID" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.lstBIAProfile,"ID","ProfileName"))" required>
                                                        <option selected value="0">-- All Profiles --</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">BIA Profile</div>
                                            </div>
                                            <button class="btn btn-sm btn-primary" style="display:none" data-bs-toggle="modal" data-bs-target="#BiaProfileModal"><i class="cv-profile-details"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="vr"></div>
                <div class="w-75">
                    <div>
                        <table class="w-100">
                            <tbody>
                                <tr>
                                    <td>
                                        <i class="cv-process-code me-1"></i>Process Code
                                    </td>
                                    <td>:</td>
                                    <td>
                                        @Model.BusinessProcessInfo.ProcessCode
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-unit me-1"></i>Unit
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="form-group">
                                            <div class="input-group">
                                                <select autocomplete="off" class="form-select form-control selectized " asp-for="@Model.BusinessProcessInfo.UnitID" id="ddlUnit" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstUnit,"UnitID","UnitName"))" required>
                                                    <option selected value="0">-- Select Units --</option>
                                                </select>
                                            </div>
                                            <div class="invalid-feedback">Select All Units</div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-subdepartment me-1"></i>SubDepartment
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="form-group">
                                            <div class="input-group">
                                                <select autocomplete="off" class="form-select form-control selectized" asp-for="@Model.BusinessProcessInfo.SubfunctionID" id="ddlSubDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstSubDepartment,"SubFunctionID","SubFunctionName"))" required>
                                                    <option selected value="0">-- Select SubDepartments --</option>
                                                </select>
                                            </div>
                                            <div class="invalid-feedback">Select SubDepartment</div>
                                        </div>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-business-process me-1"></i>Business Process
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="form-group">
                                            <div class="input-group">
                                                <select autocomplete="off" class="form-select form-control selectized" id="ddlEntity" aria-label="Default select example" required>
                                                    <option value="0" selected disabled>-- Select Process --</option>
                                                    @if (ViewBag.lstEntities != null)
                                                    {
                                                        @foreach (var objBusiProc in ViewBag.lstEntities)
                                                        {
                                                            @if (objBusiProc.Value == @Model.BusinessProcessInfo.RecordID.ToString())
                                                            {
                                                                <option value="@objBusiProc.Value" selected="selected">@objBusiProc.Text</option>
                                                            }
                                                            else
                                                            {
                                                                <option value="@objBusiProc.Value">@objBusiProc.Text</option>
                                                            }
                                                        }
                                                    }
                                                </select>
                                                <div style="display:none">
                                                    <input type="hidden" asp-for="@Model.BusinessProcessInfo.IsActive" />
                                                    <input type="hidden" asp-for="@Model.BusinessProcessInfo.ProcessName" />
                                                    <input type="hidden" asp-for="@Model.BusinessProcessInfo.ProcessID" />
                                                    <input type="hidden" id="lblEntityTypeID" asp-for="@Model.BusinessProcessInfo.EntityTypeID" />
                                                    <input type="hidden" id="lblRecordID" asp-for="@Model.BusinessProcessInfo.RecordID" />
                                                </div>
                                            </div>

                                            <div class="invalid-feedback">Select Business Process</div>
                                        </div>
                                    </td>
                                </tr>                                
                            </tbody>
                        </table>
                    </div>
                    <div class="form-group">
                        <label class="form-label"><i class="cv-rpo me-1">RPO</i></label>
                        <div class="input-group">
                            <input type="text" id="txtRPO" class="form-control" placeholder="Enter RPO" asp-for="@Model.BusinessProcessInfo.RPO" required pattern="[0-9]+" title="Only numbers allowed">
                            <select autocomplete="off" class="form-control selectized" asp-for="@Model.BusinessProcessInfo.RPOUnit" id="ddlRPOUnit" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstTimeUnit,"ID","Name"))" required>
                                <option selected value="0">-- Select Time Unit --</option>
                            </select>
                        </div>
                        <div class="invalid-feedback">Enter RPO</div>                                                                         
                    </div>

                    <div class="form-group">
                        <label class="form-label">Entity Description</label>
                        <div class="input-group">
                            <textarea class="form-control" asp-for="@Model.BusinessProcessInfo.Comments" placeholder="Entity Description"></textarea>
                        </div>
                        <div class="invalid-feedback">Entity Description</div>
                    </div>
                </div>

            </div>
            <div class="row p-3">
                <div class="col-md-6 col-lg-6 col-sm-12">
                    <div>
                        <table class="w-100">
                            <tbody>

                                <tr>
                                    <td style="width:42%">
                                        <i class="cv-transaction-volume me-1"></i>Total Transaction Volume
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="form-group">
                                            <div class="input-group">
                                                <input type="text" class="form-control" placeholder="Transaction Volume" asp-for="@Model.BusinessProcessInfo.PeakTranVolume">
                                            </div>
                                            <div class="invalid-feedback">Select Site</div>
                                        </div>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-6 col-lg-6 col-sm-12">
                    <div>


                        <table class="w-100">
                            <tbody>

                                <tr>
                                    <td style="width:42%">
                                        <i class="cv-team-size me-1"></i>Team Size
                                    </td>
                                    <td>:</td>
                                    <td>
                                        <div class="form-group">
                                            <div class="input-group">
                                                <input type="text" class="form-control" placeholder="Team Size" asp-for="@Model.BusinessProcessInfo.TeamSize">
                                            </div>
                                            <div class="invalid-feedback">Select Team Size</div>
                                        </div>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-12">
                    <div class="d-flex align-items-center gap-1 mb-2">
                        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                        <h6 class="mb-0">Owner Details</h6>
                    </div>
                    <div class="row collapse show p-3" id="collapseExample">
                        <div class="col-md-6 col-lg-6 col-sm-12 ">
                            <table class="w-100">
                                <tbody>
                                    <tr>
                                        <td style="width:42%">
                                            <i class="cv-owner me-1"></i>Select Owner

                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">

                                                    <select class="form-select form-control selectized" id="headlist" autocomplete="off" aria-label="Default select example" asp-for="@Model.BusinessProcessInfo.ProcessOwnerID" required>
                                                        <option selected value="0">-- All Resources --</option>
                                                        @foreach (var objResource in ViewBag.lstResource)
                                                        {
                                                            @if (objResource.Value == @Model.BusinessProcessInfo.ProcessOwnerID.ToString())
                                                            {
                                                                <option value="@objResource.Value" selected="selected">@objResource.Text</option>
                                                            }
                                                            else
                                                            {
                                                                <option value="@objResource.Value">@objResource.Text</option>
                                                            }
                                                        }
                                                    </select>
                                                    @*   <select class="form-select form-control" id="headlist" asp-for="ProcessOwner" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.lstResource,"ResourceId","ResourceName"))">
                                                    <option selected value="0">-- All Resources --</option>
                                                    </select> *@
                                                </div>
                                                <div class="invalid-feedback">Select Owner</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width:42%">
                                            <i class="cv-mail me-1"></i>Owner Email

                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">

                                                    <input type="text" id="companyEmail" class="form-control" placeholder="Owner Email" asp-for="@Model.BusinessProcessInfo.OwnerEmail">

                                                </div>
                                                <div class="invalid-feedback">Select Email</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width:42%">
                                            <i class="cv-Mobile me-1"></i>Owner Mobile

                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <input type="text" id="mobilePhone" class="form-control" placeholder="Owner Email" asp-for="@Model.BusinessProcessInfo.ProcessOwnerMobile">
                                                </div>
                                                <div class="invalid-feedback">Select Mobile</div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6 col-lg-6 col-sm-12">
                            <table class="w-100">
                                <tbody>
                                    <tr>
                                        <td style="width:42%">
                                            <i class="cv-owner me-1"></i>Alternate Owner

                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-control selectized" id="altheadlist" autocomplete="off" aria-label="Default select example" asp-for="@Model.BusinessProcessInfo.AltProcessOwnerID" required>
                                                        <option selected value="0">-- All Resources --</option>
                                                        @foreach (var objResource in ViewBag.lstResource)
                                                        {
                                                            <option value="@objResource.Value">@objResource.Text</option>
                                                        }
                                                    </select>
                                                    @*  <select class="form-select form-control" id="altheadlist" asp-for="AltProcessOwner" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.lstResource,"ResourceId","ResourceName"))">
                                                    <option selected value="0">-- All Resources --</option>
                                                    </select> *@
                                                </div>
                                                <div class="invalid-feedback">Select Alternate Owner</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width:42%">
                                            <i class="cv-mail me-1"></i>Alt Owner Email

                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <input type="text" id="altcompanyEmail" class="form-control" placeholder="Alt Owner Email" asp-for="@Model.BusinessProcessInfo.AltProcessOwnerEmail">
                                                </div>
                                                <div class="invalid-feedback">Select Email</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width:42%">
                                            <i class="cv-Mobile me-1"></i>Alt Owner Mobile

                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="altmobilePhone" placeholder="Alt Owner Mobile" asp-for="@Model.BusinessProcessInfo.AltProcessOwnerMobile">
                                                </div>
                                                <div class="invalid-feedback"> Alt Owner Mobile</div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    @*  <div class="d-flex align-items-center gap-1 my-2" style="display:none">
                        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#profile-details" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                        <h6 class="mb-0">Business Parameter Profile Details </h6>
                    </div>
                    <div style="display:none" class="row collapse p-3" id="profile-details">
                        <div class="col-md-6 col-lg-6 col-sm-12">
                            <table class="w-100">
                                <tbody>
                                    <tr>
                                        <td style="width:42%">
                                            <i class="cv-profile me-1"></i>Business Parameter Profile

                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-control" asp-for="@Model.BusinessProcessInfo.BPProfileID" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.lstParameterProfile,"ID","ProfileName"))">
                                                        <option selected value="0">-- All Parameter Profile --</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">Business Parameter Profile</div>
                                            </div>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>

                        </div>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <td>Sr.No</td>
                                    <td>Parameter Name</td>
                                    <td>Parameter Type</td>
                                    <td>Monitoring Enabled</td>
                                    <td>Active</td>
                                    <td>Schedule Time</td>
                                    <td>Edit/View</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>

                                    <td>01</td>
                                    <td>
                                        Move resource to other EGS location
                                    </td>
                                    <td>
                                        Object
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <a href="#"><i class="cv-edit"></i></a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>02</td>
                                    <td>
                                        Move resource to other EGS location
                                    </td>
                                    <td>
                                        Sensitivity
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <a href="#"><i class="cv-edit"></i></a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>03</td>
                                    <td>
                                        People WFH
                                    </td>
                                    <td>
                                        Sensitivity
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <a href="#"><i class="cv-edit"></i></a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>04</td>
                                    <td>
                                        Increasing shift time at DR Site
                                    </td>
                                    <td>
                                        Object
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <a href="#"><i class="cv-edit"></i></a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div> *@
                    <div class="d-flex align-items-center gap-1 my-2">
                        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" id="StartReview" type="button" data-bs-toggle="collapse" data-bs-target="#Process-Review" aria-expanded="true" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                        <h6 class="mb-0"> Review Section </h6>
                    </div>

                    <div class="row collapse show p-3 partialviewtable" id="Process-Review">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <span><i class="cv-calendar me-1"></i>Last Review Date :</span><span class="ms-1 text-primary">@Model.BusinessProcessInfo.LastReviewDate</span>
                            </div> <div class="d-flex align-items-center">
                                <span><i class="cv-calendar me-1"></i>Next Review Date :</span><span class="ms-1">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <input type="date" id="txtNextReviewDate" class="form-control" asp-for="@Model.BusinessProcessInfo.ReviewDate" required/>
                                        </div>
                                        <div class="invalid-feedback">Enter Next Review Date</div>
                                    </div>
                                </span>
                            </div>
                        </div>
                        <table class="table table-striped">
                            <thead>
                                <tr>

                                    <td>Cycle</td>
                                    <td>Start Date</td>
                                    <td>End Date</td>
                                    <td>Status</td>
                                    <td>Next Review Date</td>
                                    <td>Reviewer Name</td>
                                </tr>
                            </thead>
                            <tbody>
                                @await Html.PartialAsync("_ReviewHistoryGrid")
                            </tbody>
                        </table>
                        <div>
                            @if (Model.BusinessProcessInfo.ProcessCode != "")
                            {
                                @if (@ViewBag.startReview == "0")
                                {
                                            <button class="btn btn-sm btn-primary" id="btnStartReview">Start Review</button>
                                }
                                @if (@ViewBag.startReview == "1")
                                {
                                            <button type="button" id="btnEndReviewNew" class="btn btn-sm btn-primary btnEndReviewNew" data-bs-toggle="modal" data-bs-target="EndReviewModal">End Review</button>
                                }
                            }
                            @if (ViewBag.ShowHsitoryVisible == "Show")
                            {
                                        <button type="button" id="btnReviewHistory" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="ReviewHistory">Show History</button>
                            }     
                            @* <button class="btn btn-sm btn-primary">Show History</button> *@

                        </div>
                        @* <partial name="ReviewSection" for="@Model"> *@
                        @* @Html.RenderPartial("ReviewSection",Model) *@
                    </div>
                </div>

            </div>
        </div>
        <div>
            <div style="float:right">
                <a asp-action="PerformProcessBIA" class="btn btn-primary btn-sm" asp-controller="PerformProcessBIA" id="btnBack" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@Model.BusinessProcessInfo.ProcessID.ToString())">Back</a>
                @* <a class="btn btn-primary btn-sm" @Model.ButtonAcces.btnCreateVersion href="#" data-bs-toggle="modal" data-bs-target="#versionModal">Version</a> *@

                @if (Model.BusinessProcessInfo.Status != null)
                {
                        <a id="btnSendForApproval" @Model.ButtonAcces.btnSendForApproval class="btn btn-primary btn-sm" data-id="@BCM.Security.Helper.CryptographyHelper.Encrypt(@Model.BusinessProcessInfo.ProcessID.ToString())">
                            SendForApproval
                        </a>
                             <a id="btnReSendForApproval" @Model.ButtonAcces.btnReSendApprove class="btn btn-primary btn-sm" data-id="@BCM.Security.Helper.CryptographyHelper.Encrypt(@Model.BusinessProcessInfo.ProcessID.ToString())">
                             ReSendForApproval
                         </a>

                        <a id="btnApprove" @Model.ButtonAcces.btnApprove class="btn btn-primary btn-sm" data-id="@BCM.Security.Helper.CryptographyHelper.Encrypt(@Model.BusinessProcessInfo.ProcessID.ToString())">
                            Approve
                        </a>

                        <a id="btnDisApprove" @Model.ButtonAcces.btnDisApprove class="btn btn-primary btn-sm" data-id="@BCM.Security.Helper.CryptographyHelper.Encrypt(@Model.BusinessProcessInfo.ProcessID.ToString())">
                            DisApprove
                        </a>

                }



                @if (Model.BusinessProcessInfo.ProcessCode != "")
                {
                        <a asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" class="btn btn-primary btn-sm" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@Model.BusinessProcessInfo.ProcessID.ToString())">
                            View BIA/Dependencies
                        </a>

                        <button type="submit" id="btnUpdate" @Model.ButtonAcces.btnUpdate class="btn btn-primary btn-sm">@ViewBag.ButtonText</button>

                }
                else
                {
                    <button type="submit" id="btnAddtoBCM" class="btn btn-primary btn-sm">Add to BCM</button>

                }

                @* <button type="button" class="btn btn-primary btn-sm">View BIA/Dependencies</button> *@
                @* <button type="button" class="btn btn-secondary btn-sm me-1">Cancel</button> *@

            </div>
        </div>
    </div>
</form>

@* <!-- Configuration Modal --> *@
<div class="modal fade" id="ReviewHistoryModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle">Review History Details</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0 ReviewHisoryBody">
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="EndReviewModal" tabindex="-1" aria-labelledby="exampleModalLabel1" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-md modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle">End Review Details</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0">
                <div class="row row-cols-1">
                    <div class="col">
                        <div class="form-group w-100">
                            <label class="form-label">End Review Details <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-impact_detail"></i></span>
                                <textarea class="form-control" id="Details" placeholder="End Review Details" style="height:0px" required></textarea>
                            </div>
                            <div class="invalid-feedback" id="DetailsFeedBack">End Review Details is required</div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group w-100">
                            <label class="form-label">Next Review Date <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span><i class="cv-calendar me-1"></i></span>
                                <input type="date" id="txtNextReviewDateNew" class="form-control" required />
                            </div>
                            <div class="invalid-feedback" id="txtNextReviewDateNewFeedback">Next Review Date is required</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    @* <button type="button" class="btn btn-secondary btn-sm me-1" >Close</button> *@
                    <button type="button" class="btn btn-sm btn-primary" id="btnEndReview">End Review</button>
                </div>
            </div>
        </div>
    </div>
</div>


@* Escalation Matrix for Review Modal Start *@
<div class="modal fade" id="EscalationModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Escalation Matrix for Review</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="tab-design">
                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="EscalationDetails-tab" data-bs-toggle="tab" data-bs-target="#EscalationDetails-tab-pane" type="button" role="tab" aria-controls="EscalationDetails-tab-pane" aria-selected="true">
                                Add Escalation Details
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="EscalationFYI-tab" data-bs-toggle="tab" data-bs-target="#EscalationFYI-tab-pane" type="button" role="tab" aria-controls="EscalationFYI-tab-pane" aria-selected="false" tabindex="-1">
                                Add Resources for Escalation FYI Mail
                            </button>
                        </li>

                    </ul>
                </div>
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade  show active" id="EscalationDetails-tab-pane" aria-labelledby="EscalationDetails-tab" role="tabpanel" tabindex="0">
                        <div class="d-flex justify-content-between gap-5 mb-2 p-3">

                            <div class="w-100">
                                <table class="table table-borderless">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <i class="cv-escalation me-1"></i>Escalation Matrix Name
                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option>Select Escalation Matrix</option>
                                                            <option>Cerebrum IT Park</option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Escalation Matrix Name</div>
                                                </div>
                                            </td>
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                            <div class="w-100">
                                <table class="table table-borderless">
                                    <tbody>

                                        <tr>
                                            <td>
                                                <i class="cv-holiday-date me-1"></i>Escalation Date
                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <input type="datetime-local" class="form-control" />
                                                    </div>
                                                    <div class="invalid-feedback">Escalation Date</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>


                    </div>
                    <div class="tab-pane fade" id="EscalationFYI-tab-pane" aria-labelledby="EscalationFYI-tab" role="tabpanel" tabindex="0">
                        <div class="row">
                            <h6 class="Sub-Title my-2">Add Users & Teams for FYI :</h6>
                            <div class="tab-design">
                                <ul class="nav nav-tabs" id="myuser" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="Members-tab" data-bs-toggle="tab" data-bs-target="#Members-tab-pane" type="button" role="tab" aria-controls="Members-tab-pane" aria-selected="true">
                                            Members
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="Teams-tab" data-bs-toggle="tab" data-bs-target="#Teams-tab-pane" type="button" role="tab" aria-controls="Teams-tab-pane" aria-selected="false" tabindex="-1">
                                            Teams
                                        </button>
                                    </li>

                                </ul>
                            </div>
                            <div class="tab-content" id="myuserContent">
                                <div class="tab-pane fade  show active" id="Members-tab-pane" aria-labelledby="Members-tab" role="tabpanel" tabindex="0">
                                    <div class="card">
                                        <div class="card-header border-0 d-flex align-items-center justify-content-between">
                                            <h6 class="Sub-Title">User List for FYI</h6>
                                            <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

                                                <div class="input-group Search-Input">
                                                    <span class="input-group-text py-1"><i class="cv-search"></i></span>
                                                    <input id="search-inp" type="text" class="form-control" placeholder="Search">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
                                                <thead>
                                                    <tr>
                                                        <td>Sr.No</td>
                                                        <td>Select</td>
                                                        <td>Resource Name</td>
                                                        <td>Org Level </td>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>01</td>
                                                        <td><input type="checkbox" class="form-check" /></td>
                                                        <td>
                                                            <div class="d-flex">
                                                                <div class="User-icon">
                                                                    <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                                                                </div>
                                                                <div>
                                                                    <ul class="ps-0 mb-0">
                                                                        <li class="list-group-item fw-semibold">Arun Patil</li>
                                                                        <li class="list-group-item"><a class="text-primary" href="#"><EMAIL></a></li>
                                                                        <li class="list-group-item">9988337711</li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <ul class="ps-0 mb-0">
                                                                <li class="list-group-item"><i class="cv-organization me-1"></i>Perpetuuiti</li>
                                                                <li class="list-group-item"><i class="cv-unit me-1"></i></li>
                                                                <li class="list-group-item"><i class="cv-subdepartment-name me-1"></i></li>


                                                            </ul>
                                                        </td>


                                                    </tr>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="Teams-tab-pane" aria-labelledby="Teams-tab" role="tabpanel" tabindex="0">
                                    <div class="card">
                                        <div class="card-header border-0 d-flex align-items-center justify-content-between">
                                            <h6 class="Sub-Title">Team List for FY</h6>
                                            <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

                                                <div class="input-group Search-Input">
                                                    <span class="input-group-text py-1"><i class="cv-search"></i></span>
                                                    <input id="search-inp" type="text" class="form-control" placeholder="Search">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
                                                <thead>
                                                    <tr>
                                                        <td>Sr.No</td>
                                                        <td>Select</td>
                                                        <td>Group Name</td>
                                                        <td>Belongs To</td>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>01</td>
                                                        <td><input type="checkbox" class="form-check" /></td>
                                                        <td>
                                                            <div class="d-flex">
                                                                <div class="User-icon">
                                                                    <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                                                                </div>
                                                                <div>
                                                                    <ul class="ps-0 mb-0">
                                                                        <li class="list-group-item fw-semibold">Arun Patil</li>
                                                                        <li class="list-group-item"><a class="text-primary" href="#"><EMAIL></a></li>

                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <ul class="ps-0 mb-0">
                                                                <li class="list-group-item"><i class="cv-unit me-1"></i>Finance affairs</li>
                                                                <li class="list-group-item"><i class="cv-subdepartment-name me-1"></i>Product Development</li>
                                                            </ul>
                                                        </td>


                                                    </tr>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Attach Matrix Profile</button>
                </div>
            </div>
        </div>
    </div>
</div>
@* Escalation Matrix for Review Modal End *@

@* BIA Profile Modal Start *@
<div class="modal fade" id="BiaProfileModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">BIA Profile</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between gap-5 mb-2 p-3">
                    <div class="w-100">
                        <div>
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td>
                                            <i class="cv-organization me-1"></i>Organization
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            New_BIA  Cerebrum IT Park ( PRC 2022 160 )
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <i class="cv-approver me-1"></i>Approver
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            Anwar Chalamannil

                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="vr"></div>
                    <div class="w-100">
                        <div>
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td>
                                            <i class="cv-organization me-1"></i>Organization
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            Perpetuuiti
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <i class="cv-owner me-1"></i>Owner
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            Irene Solomon

                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
                <div class="p-3">
                    <table class="table">
                        <thead>
                            <tr>
                                <td>Sr.No</td>
                                <td>Section</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="2">
                                    <div class="text-center">
                                        <img src="/img/Isomatric/no_records_to_display.svg" class=" img-fluid" />
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="my-2">
                        <div class="d-flex align-items-center gap-3 my-2">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#ImpactTypeName" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                            <h6 class="mb-0">Impact Type Name: Financial Impact</h6>
                        </div>
                        <div class="collapse" id="ImpactTypeName">
                            <p class="text-primary">Competitive advantage</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Finish</button>
                </div>
            </div>
        </div>
    </div>
</div>
@* BIA Profile  Modal End *@



@* version Modal Start *@
<div class="modal fade" id="versionModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Create New Version</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Current Version</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-version"></i></span>
                        <input class="form-control" type="text" value="1.0" readonly />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Highest Version</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-version"></i></span>
                        <input class="form-control" type="text" value="1.0" readonly />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Version</label>
                    <div class="input-group">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="option1">
                            <label class="form-check-label" for="inlineRadio1">Minor</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="option2">
                            <label class="form-check-label" for="inlineRadio2">Major</label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Remarks</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-description"></i></span>
                        <textarea class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Create Version</button>
                </div>
            </div>
        </div>
    </div>
</div>
@* version Modal End *@



@* Business Process Modal Start *@
<div class="modal fade" id="versionModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Create New Version</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Create Version</button>
                </div>
            </div>
        </div>
    </div>
</div>
@* Business Process Modal End *@


@section Scripts {
        <script>

            $(document).ready(function () {

                        $('body').on('submit', 'form', function (e) {
                    e.preventDefault();
                    var form = $(this);
                    $.ajax({
                        type: form.attr('method'),
                        url: form.attr('action'),
                        data: form.serialize(),
                        success: function (data) {
                            $('#Modal').modal('hide');

                            // Update toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                            // Determine toast color based on operation result and type
                            const toastElement = $('#liveToast');
                            const formAction = form.attr('action').toLowerCase();

                            // Remove existing background classes
                            toastElement.removeClass('bg-success bg-warning bg-danger');

                            if (data && data.success) {
                                // Success: Add appropriate background class based on action
                                if (formAction.includes('submit')) {
                                    toastElement.addClass('bg-success');
                                } else {
                                    toastElement.addClass('bg-success');
                                }
                            } else {
                                // Failure: Add danger background class
                                toastElement.addClass('bg-danger');
                            }

                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();

                            // Delay reload to allow toast to be visible
                            setTimeout(function () {
                                location.reload();
                            }, 3000);
                        },
                        error: function (xhr, status, error) {
                            console.log(error);
                            console.error(xhr.status);
                            console.error(xhr.responseText);
                        }
                    });
                });

                // Force a check to see if global validation is loaded
                if (typeof window.BCMValidation === 'undefined') {
                    console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                    // Try to load it dynamically as a fallback
                    $.getScript('/js/global-validation.js')
                        .done(function() {
                            console.log("Successfully loaded global-validation.js dynamically");
                            initializeValidation();
                        })
                        .fail(function() {
                            console.error("Failed to load global-validation.js dynamically");
                        });
                } else {
                    console.log("BCMValidation is already defined");
                    initializeValidation();
                }

                // Function to initialize validation
                function initializeValidation() {
                    console.log("Initializing validation for businessPocessSave form");

                    if (window.BCMValidation) {
                        console.log("BCMValidation found, initializing");

                        // Get the form element
                        const form = document.getElementById('businessPocessSave');
                        if (!form) {
                            console.error("Form not found with ID: businessPocessSave");
                            return;
                        }

                        // Store the original content of all invalid-feedback divs
                        const customMessages = {};
                        form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                            // Find the associated input
                            const formGroup = element.closest('.form-group');
                            const input = formGroup?.querySelector('input, select, textarea');
                            if (input) {
                                // Store the custom message using multiple keys for better lookup
                                const aspFor = input.getAttribute('asp-for');
                                const id = input.id;
                                const name = input.name;

                                const message = element.textContent.trim();

                                if (aspFor) customMessages[aspFor] = message;
                                if (id) customMessages[id] = message;
                                if (name) customMessages[name] = message;

                                console.log("Stored custom message for", aspFor || id || name, ":", message);
                            }
                        });

                        // Helper function to restore custom message
                        function restoreCustomMessage(input) {
                            const aspFor = input.getAttribute('asp-for');
                            const id = input.id;
                            const name = input.name;

                            // Try multiple keys to find the custom message
                            let message = customMessages[aspFor] || customMessages[id] || customMessages[name];

                            if (message) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    feedbackElement.textContent = message;
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", aspFor || id || name, ":", message);
                                }
                            }
                        }

                        // Override the validateInput function to preserve custom messages
                        const originalValidateInput = window.BCMValidation.validateInput;
                        window.BCMValidation.validateInput = function(input, forceValidation = false) {
                            const result = originalValidateInput(input, forceValidation);
                            if (!result) {
                                restoreCustomMessage(input);
                            }
                            return result;
                        };

                        // Override the validateEmail function similarly
                        const originalValidateEmail = window.BCMValidation.validateEmail;
                        window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                            const result = originalValidateEmail(input, forceValidation);
                            if (!result) {
                                restoreCustomMessage(input);
                            }
                            return result;
                        };

                        // Override the validatePatternInput function similarly
                        const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                        window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                            const result = originalValidatePatternInput(input, forceValidation);
                            if (!result) {
                                restoreCustomMessage(input);
                            }
                            return result;
                        };

                        // Override the validateForm function to restore all custom messages after validation
                        const originalValidateForm = window.BCMValidation.validateForm;
                        window.BCMValidation.validateForm = function(form) {
                            const result = originalValidateForm(form);

                            // Restore all custom messages for invalid inputs
                            form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                                restoreCustomMessage(input);
                            });

                            return result;
                        };

                        // The global validation framework already initialized and added asterisks
                        // We need to clean them up and use our custom implementation
                        console.log("BusinessProcessForm: Setting up custom validation after global initialization");

                        // Custom function to add required field indicators for table-based layout
                        function addCustomRequiredFieldIndicators(form) {
                            const requiredInputs = form.querySelectorAll('[required]');
                            console.log("Adding custom required field indicators to", requiredInputs.length, "inputs");

                            requiredInputs.forEach(function(input) {
                                // Skip if asterisk already exists
                                const inputId = input.id;
                                if (inputId && !document.querySelector(`[data-asterisk-for="${inputId}"]`)) {

                                    // Special handling: Skip RPO Unit field - no asterisk needed
                                    if (inputId === 'ddlRPOUnit') {
                                        console.log("Skipping asterisk for RPO Unit field as requested");
                                        return;
                                    }

                                    // Find the table cell that contains the label text for this input
                                    let labelCell = null;

                                    // Look for the table cell that precedes the input's form-group
                                    const formGroup = input.closest('.form-group');
                                    if (formGroup) {
                                        const tableCell = formGroup.closest('td');
                                        if (tableCell) {
                                            // Find the previous cell that contains the label
                                            const row = tableCell.closest('tr');
                                            if (row) {
                                                const cells = row.querySelectorAll('td');
                                                // Usually the first cell contains the label
                                                if (cells.length > 0) {
                                                    labelCell = cells[0];
                                                }
                                            }
                                        }
                                    }

                                    // Alternative approach: find by text content for specific fields
                                    if (!labelCell) {
                                        const labelTexts = {
                                            'ddlOrganization': 'Organization',
                                            'ddlDepartment': 'Department',
                                            'ddlEntityTypes': 'BCM Entity Type',
                                            'ddlApprover': 'Approver',
                                            'ddlBIAProfile': 'BIA Profile',
                                            'ddlUnit': 'Unit',
                                            'ddlSubDepartment': 'SubDepartment',
                                            'ddlEntity': 'Business Process',
                                            'txtRPO': 'RPO',
                                            'headlist': 'Select Owner',
                                            'altheadlist': 'Alternate Owner',
                                            'txtNextReviewDate': 'Next Review Date'
                                        };

                                        const labelText = labelTexts[inputId];
                                        if (labelText) {
                                            // Find table cell containing this text
                                            const allCells = document.querySelectorAll('td, span');
                                            for (let cell of allCells) {
                                                const cellText = cell.textContent.trim();
                                                if (cellText.includes(labelText) && !cell.querySelector('input, select, textarea')) {
                                                    // Make sure this cell doesn't contain form controls
                                                    labelCell = cell;
                                                    break;
                                                }
                                            }
                                        }
                                    }

                                    // Special handling for fields that might need more specific targeting
                                    if (!labelCell) {
                                        switch(inputId) {
                                            case 'altheadlist':
                                                // For alternate owner, find the cell containing "Alternate Owner" text
                                                const altOwnerCells = document.querySelectorAll('td');
                                                for (let cell of altOwnerCells) {
                                                    if (cell.textContent.trim().includes('Alternate Owner')) {
                                                        labelCell = cell;
                                                        break;
                                                    }
                                                }
                                                break;
                                            case 'txtNextReviewDate':
                                                // For next review date, find the span containing "Next Review Date"
                                                const spans = document.querySelectorAll('span');
                                                for (let span of spans) {
                                                    if (span.textContent.trim().includes('Next Review Date')) {
                                                        labelCell = span;
                                                        break;
                                                    }
                                                }
                                                break;
                                        }
                                    }

                                    if (labelCell) {
                                        // Create asterisk element
                                        const asterisk = document.createElement('span');
                                        asterisk.className = 'required-asterisk text-danger ms-1';
                                        asterisk.textContent = '*';
                                        asterisk.style.fontWeight = 'bold';
                                        asterisk.setAttribute('data-asterisk-for', inputId);
                                        asterisk.title = 'Required field';

                                        // Append asterisk to the label cell
                                        labelCell.appendChild(asterisk);
                                        console.log("Added asterisk to label for:", inputId);
                                    } else {
                                        console.warn("No label cell found for required input:", inputId);
                                    }
                                }
                            });
                        }

                        // Function to thoroughly clean up any existing asterisks added by the framework
                        function cleanupExistingAsterisks(form) {
                            console.log("Cleaning up existing framework asterisks...");

                            // Remove any asterisks with the framework's required-field-indicator class
                            const existingIndicators = form.querySelectorAll('.required-field-indicator');
                            existingIndicators.forEach(function(indicator) {
                                indicator.remove();
                                console.log("Removed framework asterisk with required-field-indicator class");
                            });

                            // Remove any asterisks that are siblings of input groups
                            const inputGroups = form.querySelectorAll('.input-group');
                            inputGroups.forEach(function(inputGroup) {
                                // Check next sibling
                                let nextSibling = inputGroup.nextSibling;
                                while (nextSibling) {
                                    if (nextSibling.nodeType === Node.ELEMENT_NODE) {
                                        if (nextSibling.classList.contains('required-field-indicator') ||
                                            (nextSibling.tagName === 'SPAN' && nextSibling.textContent.trim() === '*' && !nextSibling.hasAttribute('data-asterisk-for'))) {
                                            const toRemove = nextSibling;
                                            nextSibling = nextSibling.nextSibling;
                                            toRemove.remove();
                                            console.log("Removed asterisk sibling of input group");
                                        } else {
                                            break;
                                        }
                                    } else if (nextSibling.nodeType === Node.TEXT_NODE && nextSibling.textContent.trim() === '') {
                                        // Skip whitespace text nodes
                                        nextSibling = nextSibling.nextSibling;
                                    } else {
                                        break;
                                    }
                                }
                            });

                            // Remove any asterisks that are children of form-groups but not our custom ones
                            const formGroups = form.querySelectorAll('.form-group');
                            formGroups.forEach(function(formGroup) {
                                const asterisks = formGroup.querySelectorAll('span');
                                asterisks.forEach(function(span) {
                                    if (span.textContent.trim() === '*' &&
                                        !span.hasAttribute('data-asterisk-for')) {
                                        // Check if it's a framework asterisk (not our custom one)
                                        if (span.classList.contains('required-field-indicator') ||
                                            span.style.color === 'red' ||
                                            span.className.includes('text-danger')) {
                                            span.remove();
                                            console.log("Removed framework asterisk from form-group");
                                        }
                                    }
                                });
                            });

                            // More aggressive cleanup - remove ANY asterisk span that's not ours
                            const allSpans = form.querySelectorAll('span');
                            allSpans.forEach(function(span) {
                                if (span.textContent.trim() === '*' && !span.hasAttribute('data-asterisk-for')) {
                                    // Check if it's likely a framework asterisk
                                    const parent = span.parentElement;
                                    const isInFormGroup = parent && parent.closest('.form-group');
                                    const isAfterInput = parent && (parent.classList.contains('input-group') || parent.querySelector('input, select, textarea'));

                                    if (isInFormGroup && isAfterInput) {
                                        span.remove();
                                        console.log("Removed framework asterisk near input field");
                                    }
                                }
                            });



                            console.log("Cleanup completed");
                        }

                        // Clean up any existing asterisks first
                        cleanupExistingAsterisks(form);

                        // Use our custom function instead of the default one
                        addCustomRequiredFieldIndicators(form);

                        // Set up a MutationObserver to watch for any new asterisks being added by the framework
                        const observer = new MutationObserver(function(mutations) {
                            let needsCleanup = false;
                            mutations.forEach(function(mutation) {
                                if (mutation.type === 'childList') {
                                    mutation.addedNodes.forEach(function(node) {
                                        if (node.nodeType === Node.ELEMENT_NODE) {
                                            // Check if a framework asterisk was added
                                            if ((node.tagName === 'SPAN' &&
                                                 node.classList.contains('required-field-indicator') &&
                                                 !node.hasAttribute('data-asterisk-for')) ||
                                                (node.tagName === 'SPAN' &&
                                                 node.textContent.trim() === '*' &&
                                                 !node.hasAttribute('data-asterisk-for'))) {
                                                needsCleanup = true;
                                            }
                                            // Also check if any child elements are framework asterisks
                                            const childAsterisks = node.querySelectorAll && node.querySelectorAll('span.required-field-indicator');
                                            if (childAsterisks && childAsterisks.length > 0) {
                                                childAsterisks.forEach(function(asterisk) {
                                                    if (!asterisk.hasAttribute('data-asterisk-for')) {
                                                        needsCleanup = true;
                                                    }
                                                });
                                            }
                                        }
                                    });
                                }
                            });

                            if (needsCleanup) {
                                console.log("Framework asterisk detected, cleaning up...");
                                setTimeout(function() {
                                    cleanupExistingAsterisks(form);
                                }, 10); // Small delay to ensure the DOM is stable
                            }
                        });

                        // Start observing
                        observer.observe(form, {
                            childList: true,
                            subtree: true
                        });

                        // Also run cleanup periodically for the first few seconds to catch any delayed additions
                        let cleanupCount = 0;
                        const cleanupInterval = setInterval(function() {
                            cleanupExistingAsterisks(form);
                            cleanupCount++;
                            if (cleanupCount >= 10) { // Run 10 times over 10 seconds for more thorough cleanup
                                clearInterval(cleanupInterval);
                                console.log("Periodic cleanup completed");
                            }
                        }, 1000);



                        // Add format indicators for pattern-based inputs
                        window.BCMValidation.addFormatIndicators(form);

                        // Add user interaction validation with input/blur event listeners for all required fields
                        form.querySelectorAll('input[required], select[required], textarea[required]').forEach(function(field) {
                            // Add event listeners for user interaction validation
                            field.addEventListener('input', function() {
                                window.BCMValidation.validateInput(this);
                            });

                            field.addEventListener('blur', function() {
                                window.BCMValidation.validateInput(this);
                            });

                            // For select elements, also listen to change event
                            if (field.tagName.toLowerCase() === 'select') {
                                field.addEventListener('change', function() {
                                    window.BCMValidation.validateInput(this);
                                });
                            }
                        });

                        // Add a manual validation trigger on form submission
                        form.addEventListener('submit', function(event) {
                            console.log("Form submission triggered");

                            // Show all validation messages
                            window.BCMValidation.showAllValidationMessages(form);

                            // Validate the form
                            const isValid = window.BCMValidation.validateForm(form);
                            console.log("Form validation result:", isValid);

                            // Also validate that owner and alternate owner are different
                            const ownersValid = validateOwners();
                            console.log("Owners validation result:", ownersValid);

                            // Also validate RPO fields specifically
                            const rpoValid = validateRPOFields();
                            console.log("RPO validation result:", rpoValid);

                            if (!isValid || !ownersValid || !rpoValid) {
                                console.log("Preventing form submission due to validation errors");
                                event.preventDefault();
                                event.stopPropagation();

                                // Focus the first invalid field
                                const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                                if (firstInvalidField) {
                                    firstInvalidField.focus();
                                }
                            }
                        });
                    } else {
                        console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                    }
                }

                // Function to validate that owner and alternate owner are different
                function validateOwners() {
                    const ownerSelect = document.getElementById('headlist');
                    const altOwnerSelect = document.getElementById('altheadlist');

                    if (!ownerSelect || !altOwnerSelect) {
                        return true; // If elements don't exist, skip validation
                    }

                    const ownerValue = ownerSelect.value;
                    const altOwnerValue = altOwnerSelect.value;

                    let isValid = true;

                    // Clear previous error states for role validation
                    clearOwnerValidationErrors();

                    // Only validate if both have values and are not the default "0" value
                    if (ownerValue && altOwnerValue && ownerValue !== "0" && altOwnerValue !== "0" && ownerValue === altOwnerValue) {
                        // Show validation error for both fields
                        showOwnerValidationError('#headlist', 'Owner and Alternate Owner cannot be the same person');
                        showOwnerValidationError('#altheadlist', 'Owner and Alternate Owner cannot be the same person');
                        isValid = false;
                    }

                    return isValid;
                }

                // Function to show owner validation error
                function showOwnerValidationError(selector, message) {
                    var element = $(selector);
                    element.addClass('is-invalid').removeClass('is-valid');

                    var formGroup = element.closest('.form-group');
                    var feedbackElement = formGroup.find('.invalid-feedback');
                    if (feedbackElement.length > 0) {
                        feedbackElement.text(message);
                        // Use CSS classes and show method for proper display
                        feedbackElement.addClass('custom-validation show').show();
                    }
                }

                // Function to clear owner validation errors
                function clearOwnerValidationErrors() {
                    var ownerSelectors = ['#headlist', '#altheadlist'];

                    ownerSelectors.forEach(function(selector) {
                        var element = $(selector);
                        var formGroup = element.closest('.form-group');
                        var feedbackElement = formGroup.find('.invalid-feedback');

                        // Only clear if the current message is an owner validation error
                        if (feedbackElement.length > 0 && feedbackElement.text().includes('cannot be the same person')) {
                            element.removeClass('is-invalid');
                            // Use CSS classes for proper hiding
                            feedbackElement.removeClass('custom-validation show').hide();

                            // Restore original message
                            var originalMessages = {
                                '#headlist': 'Select Owner',
                                '#altheadlist': 'Select Alternate Owner'
                            };

                            if (originalMessages[selector]) {
                                feedbackElement.text(originalMessages[selector]);
                            }
                        }
                    });
                }

                // Function to validate RPO fields specifically
                function validateRPOFields() {
                    const rpoInput = document.getElementById('txtRPO');
                    const rpoUnitSelect = document.getElementById('ddlRPOUnit');
                    let isValid = true;

                    // Validate RPO input
                    if (rpoInput && rpoInput.hasAttribute('required')) {
                        const rpoValue = rpoInput.value.trim();
                        if (!rpoValue) {
                            rpoInput.classList.add('is-invalid');
                            const rpoFeedback = rpoInput.closest('.form-group')?.querySelector('.invalid-feedback');
                            if (rpoFeedback) {
                                rpoFeedback.style.display = 'block';
                            }
                            isValid = false;
                        } else {
                            rpoInput.classList.remove('is-invalid');
                            const rpoFeedback = rpoInput.closest('.form-group')?.querySelector('.invalid-feedback');
                            if (rpoFeedback) {
                                rpoFeedback.style.display = 'none';
                            }
                        }
                    }

                    // Validate RPO Unit select
                    if (rpoUnitSelect && rpoUnitSelect.hasAttribute('required')) {
                        const rpoUnitValue = rpoUnitSelect.value;
                        if (!rpoUnitValue || rpoUnitValue === "0") {
                            rpoUnitSelect.classList.add('is-invalid');
                            const rpoUnitFeedback = rpoUnitSelect.closest('.form-group')?.querySelector('.invalid-feedback');
                            if (rpoUnitFeedback) {
                                rpoUnitFeedback.textContent = 'Select Time Unit';
                                rpoUnitFeedback.style.display = 'block';
                            }
                            isValid = false;
                        } else {
                            rpoUnitSelect.classList.remove('is-invalid');
                            const rpoUnitFeedback = rpoUnitSelect.closest('.form-group')?.querySelector('.invalid-feedback');
                            if (rpoUnitFeedback) {
                                rpoUnitFeedback.style.display = 'none';
                            }
                        }
                    }

                    return isValid;
                }

                // Add change event listeners to owner dropdowns for real-time validation
                $(document).on('change', '#headlist, #altheadlist', function() {
                    // First clear any existing validation state when user makes a selection
                    var $this = $(this);
                    if ($this.val() && $this.val() !== "0") {
                        $this.removeClass('is-invalid');
                        var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                        if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be the same person')) {
                            feedbackElement.hide();
                        }
                    }

                    // Then validate that owners are different
                    validateOwners();
                });

                // Add event listeners for RPO fields real-time validation
                $(document).on('input blur', '#txtRPO', function() {
                    validateRPOFields();
                });

                $(document).on('change', '#ddlRPOUnit', function() {
                    validateRPOFields();
                });

                $('#txtNextReviewDate').change(function (){

                    var selectedDate = $(this).val();
                    var today = new Date().toISOString();
                    var x = new Date(selectedDate);
                    var y = new Date(today.substring(0,10));
                    if(x < y){
                        alert("select Greater than today date");
                    }
                });

                $('.btnEndReviewNew').click(function () {
                    debugger;
                        $('#EndReviewModal').modal('show');
                });

                $('#btnReviewHistory').click(function () {
                    debugger;
                    var entityID = $('#EntityID').val();
                    var recordID = $('#RecordID').val();
                   // $.get('/BCMEntities/ManageOtherBCMEntities/AddOtherBCMEntities', function (data) {
                    $.get('@Url.Action("ReviweHistoryDetails", "BusinessProcessForm")',{ RecordID: recordID,EntityTypeID:entityID }, function (data) {
                        $('.ReviewHisoryBody').html(data);
                        $('#ReviewHistoryModal').modal('show');
                        //$('#modaltitle').text('Other BCM Entities Configuration');
                    });
                });
                //review section
                // $.get('@Url.Action("ReviewSection", "BusinessProcessForm")', function (data) {
                //     $('.partialviewtable').html(data);
                // });
            //     $(document).on('click', '#btnStartReview', function () {
            //     var entityID = $('#EntityID').val();
            //     var recordID = $('#RecordID').val();
            //     var nextReviewDate = $('#txtNextReviewDate').val();

            //     $.post(@Url.Action("StartReviewSection", "BusinessProcessForm")),
            //     {
            //         EntityID: entityID,
            //         RecordID: recordID,
            //         NextReviewDate: nextReviewDate
            //     },
            //     function (data) {
            //         $('.partialviewtable').load('/BCMProcessBIA/BusinessProcessForm/ReviewSection');
            //         console.log(data);
            //     }).fail(function (xhr, status, error) {
            //         console.error('Error:', status, error);
            //     });
            // });

            $('#btnStartReview').click(function (e)
                {
                    e.preventDefault();
                    var entityID = $('#EntityID').val();
                    var recordID = $('#RecordID').val();
                    var nextReviewDate = $('#txtNextReviewDate').val();

                        $.ajax({
                                    url: '@Url.Action("StartReviewSection", "BusinessProcessForm")',
                                    type: 'POST',
                                    data: { EntityID: entityID,
                    RecordID: recordID,
                    NextReviewDate: nextReviewDate },
                     success: function (data) {
                            console.log(data);

                            // Set toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                            // Configure toast appearance
                            const toastElement = $('#liveToast');

                            // Remove existing background classes
                            toastElement.removeClass('bg-success bg-warning bg-danger');

                            // Add success class
                            toastElement.addClass('bg-success');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();

                            // Hide buttons
                            // $("#btnSendForApproval").hide();
                            // $("#btnUpdate").hide();

                            //Delay reload to allow toast to be visible
                            setTimeout(function () {
                                location.reload();
                            }, 1500);
                        },
                        error: function (data) {
                            console.log('Error Is Invoked');

                            // Set error toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text('Failed to send process for approval');

                            // Configure toast appearance
                            const toastElement = $('#liveToast');
                            toastElement.removeClass('bg-success bg-warning bg-danger');
                            toastElement.addClass('bg-danger');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();
                        }
                        })


                 });

                 // Hide validation messages when user starts typing
                 $('#Details').on('input', function() {
                     if ($(this).val().trim() !== '') {
                         $(this).removeClass('is-invalid');
                         $('#DetailsFeedBack')[0].style.setProperty('display', 'none', 'important');
                     }
                 });

                 $('#txtNextReviewDateNew').on('change', function() {
                     var dateValue = $(this).val();
                     if (dateValue !== '') {
                         // Check if date is greater than today
                         var selectedDate = new Date(dateValue);
                         var today = new Date();
                         today.setHours(0, 0, 0, 0); // Set to start of day for comparison

                         if (selectedDate <= today) {
                             $(this).addClass('is-invalid');
                             $('#txtNextReviewDateNewFeedback').text('Next Review Date must be greater than today');
                             $('#txtNextReviewDateNewFeedback')[0].style.setProperty('display', 'block', 'important');
                         } else {
                             $(this).removeClass('is-invalid');
                             $('#txtNextReviewDateNewFeedback')[0].style.setProperty('display', 'none', 'important');
                         }
                     }
                 });

                 $('#btnEndReview').click(function (e)
                {
                    e.preventDefault();

                    // Get form field values
                    var details = $('#Details').val();
                    var nextReviewDate = $('#txtNextReviewDateNew').val();

                    // Simple validation
                    var isValid = true;

                    // Check Details field
                    if (!details || details.trim() === '') {
                        console.log('Details field is empty, showing error message');
                        $('#Details').addClass('is-invalid');
                        console.log('Before showing DetailsFeedBack - current display:', $('#DetailsFeedBack').css('display'));
                        $('#DetailsFeedBack')[0].style.setProperty('display', 'block', 'important');
                        console.log('After showing DetailsFeedBack - current display:', $('#DetailsFeedBack').css('display'));
                        console.log('DetailsFeedBack element exists:', $('#DetailsFeedBack').length > 0);
                        isValid = false;
                    } else {
                        console.log('Details field is valid, hiding error message');
                        $('#Details').removeClass('is-invalid');
                        $('#DetailsFeedBack')[0].style.setProperty('display', 'none', 'important');
                    }

                    // Check Date field
                    if (!nextReviewDate) {
                        console.log('Date field is empty, showing error message');
                        $('#txtNextReviewDateNew').addClass('is-invalid');
                        $('#txtNextReviewDateNewFeedback').text('Next Review Date is required');
                        console.log('Before showing txtNextReviewDateNewFeedback - current display:', $('#txtNextReviewDateNewFeedback').css('display'));
                        $('#txtNextReviewDateNewFeedback')[0].style.setProperty('display', 'block', 'important');
                        console.log('After showing txtNextReviewDateNewFeedback - current display:', $('#txtNextReviewDateNewFeedback').css('display'));
                        console.log('txtNextReviewDateNewFeedback element exists:', $('#txtNextReviewDateNewFeedback').length > 0);
                        isValid = false;
                    } else {
                        // Check if date is greater than today
                        var selectedDate = new Date(nextReviewDate);
                        var today = new Date();
                        today.setHours(0, 0, 0, 0); // Set to start of day for comparison

                        if (selectedDate <= today) {
                            console.log('Date is not greater than today, showing error message');
                            $('#txtNextReviewDateNew').addClass('is-invalid');
                            $('#txtNextReviewDateNewFeedback').text('Next Review Date must be greater than today');
                            $('#txtNextReviewDateNewFeedback')[0].style.setProperty('display', 'block', 'important');
                            isValid = false;
                        } else {
                            console.log('Date field is valid, hiding error message');
                            $('#txtNextReviewDateNew').removeClass('is-invalid');
                            $('#txtNextReviewDateNewFeedback')[0].style.setProperty('display', 'none', 'important');
                        }
                    }

                    // If validation fails, stop here
                    if (!isValid) {
                        return false;
                    }

                    // Get other values for AJAX
                    var entityID = $('#EntityID').val();
                    var recordID = $('#RecordID').val();

                        $.ajax({
                                    url: '@Url.Action("EndReviewSection", "BusinessProcessForm")',
                                    type: 'POST',
                                    data: { EntityID: entityID,
                    RecordID: recordID,
                    NextReviewDate: nextReviewDate,
                    Details: details },
                     success: function (data) {
                            console.log(data);

                            // Close modal and clear form
                            $('#EndReviewModal').modal('hide');
                            $('#Details').val('');
                            $('#txtNextReviewDateNew').val('');
                            $('#Details, #txtNextReviewDateNew').removeClass('is-invalid');
                            $('#DetailsFeedBack')[0].style.setProperty('display', 'none', 'important');
                            $('#txtNextReviewDateNewFeedback')[0].style.setProperty('display', 'none', 'important');

                            // Set toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                            // Configure toast appearance
                            const toastElement = $('#liveToast');

                            // Remove existing background classes
                            toastElement.removeClass('bg-success bg-warning bg-danger');

                            // Add success class
                            toastElement.addClass('bg-success');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();

                            // Hide buttons
                            // $("#btnSendForApproval").hide();
                            // $("#btnUpdate").hide();

                            //Delay reload to allow toast to be visible
                            setTimeout(function () {
                                location.reload();
                            }, 1500);
                        },
                        error: function (data) {
                            console.log('Error Is Invoked');

                            // Set error toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text('Failed to send process for approval');

                            // Configure toast appearance
                            const toastElement = $('#liveToast');
                            toastElement.removeClass('bg-success bg-warning bg-danger');
                            toastElement.addClass('bg-danger');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();
                        }

                        })
                 })


                $('#ddlOrganization').change(function ()
                {
                    var iUnitID = $(this).val();
                    if (iUnitID)
                    {
                        $.ajax({
                                    url: '@Url.Action("GetAllUnits", "BusinessProcessForm")',
                                    type: 'GET',
                                    data: { iOrgID: iUnitID },

                                    success: function (response) {                                    
                                        let selectizeInstance = $('#ddlUnit')[0].selectize;
                                        selectizeInstance.clear();
                                        selectizeInstance.clearOptions();
                                        selectizeInstance.addOption({ value: "0", text: "-- All Units --" });
                                        selectizeInstance.addItem("0");

                                        response && response.forEach(({ unitID, unitName }) => {
                                            if (unitID && unitName) {
                                                selectizeInstance.addOption({ value: unitID, text: unitName });
                                            }
                                        });
                                    }

                                    // success: function (data) {
                                    //     var ddlUnit = $('#ddlUnit');
                                    //     ddlUnit.empty();
                                    //     ddlUnit.append('<option value="0">-- All Units --</option>');
                                    //     $.each(data, function (index, item) {
                                    //         ddlUnit.append('<option value="' + item.unitID + '">' + item.unitName + '</option>')
                                    //     });
                                    // }
                        })
                    }

                 })

                $('#ddlUnit').change(function ()
                {
                    var iUnitID = $(this).val();
                    if (iUnitID)
                    {
                        $.ajax({
                                    url: '@Url.Action("GetAllDepartments", "BusinessProcessForm")',
                                    type: 'GET',
                                    data: { iUnitID: iUnitID },
                                    success: function (response) {
                                        let selectizeInstance = $('#ddlDepartment')[0].selectize;
                                        selectizeInstance.clear();
                                        selectizeInstance.clearOptions();
                                        selectizeInstance.addOption({ value: "0", text: "-- All Departments --" });
                                        selectizeInstance.addItem("0");

                                        response && response.forEach(({ departmentID, departmentName }) => {
                                            if (departmentID && departmentName) {
                                                selectizeInstance.addOption({ value: departmentID, text: departmentName });
                                            }
                                        });
                                    }

                                    // success: function (data) {
                                    //     var department = $('#ddlDepartment');
                                    //     department.empty();
                                    //     department.append('<option value="0">-- All Departments --</option>');
                                    //     $.each(data, function (index, item) {
                                    //         department.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                                    //     });
                                    // }
                        })
                    }

                 })

                 $('#ddlDepartment').change(function ()
                {
                    var iUnitID = $(this).val();
                    if (iUnitID)
                    {
                        $.ajax({
                                    url: '@Url.Action("GetAllSubDepartments", "BusinessProcessForm")',
                                    type: 'GET',
                                    data: { iDepartmentID: iUnitID },
                                    success: function (response) {
                                        let selectizeInstance = $('#ddlSubDepartment')[0].selectize;
                                        selectizeInstance.clear();
                                        selectizeInstance.clearOptions();
                                        selectizeInstance.addOption({ value: "0", text: "-- All SubDepartments --" });
                                        selectizeInstance.addItem("0");

                                        response && response.forEach(({ subFunctionID, subFunctionName }) => {
                                        if (subFunctionID && subFunctionName) {
                                                selectizeInstance.addOption({ value: subFunctionID, text: subFunctionName });
                                            }
                                        });
                                    }

                                    // success: function (data) {
                                    //     var Subdepartment = $('#ddlSubDepartment');
                                    //     Subdepartment.empty();
                                    //     Subdepartment.append('<option value="0">-- All SubDepartments --</option>');
                                    //     $.each(data, function (index, item) {
                                    //         Subdepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>')
                                    //     });
                                    // }
                        })
                    }

                 })


                // Combined event handler for AJAX and validation
                $('#headlist,#altheadlist').change(function () {
                    var selectedDDL = $(this).attr('id');
                    var iId = $(this).val();
                    var $this = $(this);

                    // Handle AJAX call for populating email/mobile fields
                    $.ajax({
                        url: '@Url.Action("GetResourceDetails", "BusinessProcessForm")' + '/' + iId,
                        type: "GET",
                        data: { iId: iId },
                        success: function (data) {
                            if (data) {
                                if (selectedDDL == "headlist") {
                                    $("#companyEmail").val(data.mail);
                                    $("#mobilePhone").val(data.mobile);
                                } else if (selectedDDL == "altheadlist") {
                                    $("#altcompanyEmail").val(data.mail);
                                    $("#altmobilePhone").val(data.mobile);
                                }
                            }
                            else {
                                console.log("Error While Binding Data.");
                            }
                        },
                        error: function (data) {
                            console.log('Error Is Invoked');
                        }
                    });

                    // Clear validation state when user makes a selection
                    if ($this.val() && $this.val() !== "0") {
                        $this.removeClass('is-invalid');
                        var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                        if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be the same person')) {
                            feedbackElement.hide();
                        }
                    }

                    // Validate that owners are different (with small delay to allow AJAX to complete)
                    setTimeout(function() {
                        validateOwners();
                    }, 100);
                });


                    $(document).on('click', '#btnReSendForApproval', function () {
                        var strID = $(this).data('id');
                            $('#btnReSendForApproval').hide();

                        $.ajax({
                            url: '@Url.Action("SendForApproval", "BusinessProcessForm")',
                            type: "GET",
                            data: { strProcessID: strID },
                            success: function (data) {
                                console.log(data);

                                // Set toast message
                                $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                                // Configure toast appearance
                                const toastElement = $('#liveToast');

                                // Remove existing background classes
                                toastElement.removeClass('bg-success bg-warning bg-danger');

                                // Add success class
                                toastElement.addClass('bg-success');

                                // Show the toast
                                const toastLiveExample = document.getElementById('liveToast');
                                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                                toastBootstrap.show();

                                // Hide buttons
                                // $("#btnSendForApproval").hide();
                                // $("#btnUpdate").hide();

                                //Delay reload to allow toast to be visible
                                setTimeout(function () {
                                    location.reload();
                                }, 1500);
                            },
                            error: function (data) {
                                console.log('Error Is Invoked');

                                // Set error toast message
                                $('#liveToast .toast-body .d-flex span:last-child').text('Failed to send process for approval');

                                // Configure toast appearance
                                const toastElement = $('#liveToast');
                                toastElement.removeClass('bg-success bg-warning bg-danger');
                                toastElement.addClass('bg-danger');

                                // Show the toast
                                const toastLiveExample = document.getElementById('liveToast');
                                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                                toastBootstrap.show();
                            }
                        });
                    });


                $(document).on('click', '#btnSendForApproval', function () {
                    var strID = $(this).data('id');
                    $('#btnSendForApproval').hide();
                    $('#btnUpdate').hide();
                    $('#btnAddtoBCM').hide();

                    $.ajax({
                        url: '@Url.Action("SendForApproval", "BusinessProcessForm")',
                        type: "GET",
                        data: { strProcessID: strID },
                        success: function (data) {
                            console.log(data);

                            // Set toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                            // Configure toast appearance
                            const toastElement = $('#liveToast');

                            // Remove existing background classes
                            toastElement.removeClass('bg-success bg-warning bg-danger');

                            // Add success class
                            toastElement.addClass('bg-success');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();

                            // Hide buttons
                            // $("#btnSendForApproval").hide();
                            // $("#btnUpdate").hide();

                            //Delay reload to allow toast to be visible
                            setTimeout(function () {
                                location.reload();
                            }, 1500);
                        },
                        error: function (data) {
                            console.log('Error Is Invoked');

                            // Set error toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text('Failed to send process for approval');

                            // Configure toast appearance
                            const toastElement = $('#liveToast');
                            toastElement.removeClass('bg-success bg-warning bg-danger');
                            toastElement.addClass('bg-danger');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();
                        }
                    });
                });

                $(document).on('click', '#btnApprove', function () {
                    var strID = $(this).data('id');
                  //  $.get('/BCMProcessBIA/BusinessProcessForm/SendForApproval/', { strProcessID: strID }, function (data) {
                    // $.get('@Url.Action("btnApproveClick", "BusinessProcessForm")', { strProcessID: strID }, function (data) {
                    // });
                    $('#btnApprove').hide();
                    $('#btnDisApprove').hide();
                    $.ajax({
                        url: '@Url.Action("btnApproveClick", "BusinessProcessForm")',
                        type: "GET",
                        data: { strProcessID: strID },
                        success: function (data) {
                              // Set toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                            // Configure toast appearance
                            const toastElement = $('#liveToast');

                            // Remove existing background classes
                            toastElement.removeClass('bg-success bg-warning bg-danger');

                            // Add success class
                            toastElement.addClass('bg-success');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();

                            // Hide buttons
                           //  $("#btnUpdate").hide();
                           // $("#btnApprove").hide();
                           // $("#btnDisApprove").hide();

                            //Delay reload to allow toast to be visible
                            setTimeout(function () {
                                location.reload();
                            }, 1500);
                        },
                        error: function (data) {
                            console.log('Error Is Invoked');

                            // Set error toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text('Failed to Approve process');

                            // Configure toast appearance
                            const toastElement = $('#liveToast');
                            toastElement.removeClass('bg-success bg-warning bg-danger');
                            toastElement.addClass('bg-danger');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();
                        }
                    });

                });

                $(document).on('click', '#btnDisApprove', function () {
                    var strID = $(this).data('id');
                             $('#btnDisApprove').hide();
                    //alert(strID);
                  //  $.get('/BCMProcessBIA/BusinessProcessForm/SendForApproval/', { strProcessID: strID }, function (data) {
                    // $.get('@Url.Action("btnDisApproveClick", "BusinessProcessForm")', { strProcessID: strID }, function (data) {
                    // });

                    $.ajax({
                        url: '@Url.Action("btnDisApproveClick", "BusinessProcessForm")',
                        type: "GET",
                        data: { strProcessID: strID },
                        success: function (data) {
                            // Set toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                            // Configure toast appearance
                            const toastElement = $('#liveToast');

                            // Remove existing background classes
                            toastElement.removeClass('bg-success bg-warning bg-danger');

                            // Add success class
                            toastElement.addClass('bg-success');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();

                            // Hide buttons
                           //  $("#btnUpdate").hide();
                           // $("#btnApprove").hide();
                           // $("#btnDisApprove").hide();

                            //Delay reload to allow toast to be visible
                            setTimeout(function () {
                                location.reload();
                            }, 1500);
                        },
                         error: function (data) {
                            console.log('Error Is Invoked');

                            // Set error toast message
                            $('#liveToast .toast-body .d-flex span:last-child').text('Failed to Approve process');

                            // Configure toast appearance
                            const toastElement = $('#liveToast');
                            toastElement.removeClass('bg-success bg-warning bg-danger');
                            toastElement.addClass('bg-danger');

                            // Show the toast
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();
                        }
                    });

                });

                $('#ddlEntityTypes').change(function () {
                    var EntityType = $(this).val();
                    var iOrgID = $('#ddlOrganization').val();
                    //debugger;
                    if (EntityType) {
                        $.ajax({
                            url: '@Url.Action("ddlEntitiesSelectedIndexChange", "BusinessProcessForm")',
                            type: 'GET',
                            data: { EntityTypeID: EntityType, OrgID: iOrgID },
                            success: function (response) {                         
                               let selectizeInstance = $('#ddlEntity')[0].selectize;
                               selectizeInstance.clear();
                               selectizeInstance.clearOptions();
                               selectizeInstance.addOption({ value: "0", text: "-- All Entities --" });
                               selectizeInstance.addItem("0");

                               response && response.forEach(({ value, text }) => {
                                   if (value && text) {
                                     selectizeInstance.addOption({ value: value, text: text });
                                   }  
                               });

                            },            
                            // success: function (data) {
                            //     var ddlEntity = $('#ddlEntity');
                            //     //debugger;
                            //     ddlEntity.empty();
                            //     ddlEntity.append('<option value="0">-- All Entities --</option>');
                            //     $.each(data, function (index, item) {

                            //         ddlEntity.append('<option value="' + item.value + '">' + item.text + '</option>')
                            //     });
                            // },
                            error: function (xhr, status, error) {
                                console.log(error);
                                console.error(xhr.status);
                                console.error(xhr.responseText);
                            }
                        })
                    }

                });
            });


        </script>
}
