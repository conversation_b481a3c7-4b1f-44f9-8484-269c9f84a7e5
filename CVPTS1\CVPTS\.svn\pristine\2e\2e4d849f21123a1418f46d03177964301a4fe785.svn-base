﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    i.cv-search{
        font-size:18px;
    }
</style>
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Manage BCM Risk</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end"><button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"> <i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>

<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                 <th>IT Service</th>
                <th>Threat Category/Threat</th>
                <th>Inherent Rating</th>
                <th>Risk Treatment Option</th>
                <th>Residual Rating</th>
                <th>Create/Review Date</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <span class="text-primary fw-semibold">Application(s)</span>
                </td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>
                    <span class="text-primary fw-semibold">Continuity vault</span>
                </td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td><button class="btn btn-sm btn-primary"><i class="cv-Plus" title="Create New"></i></button></td>
            </tr>
            <tr>
                <td>
                    <table>
                        <tbody>
                            <tr>
                                <span class="text-primary fw-semibold">RAO-2022-74</span>
                            </tr>
                            <tr>
                                <td class ="text-muted">RiskName</td>
                                <td>:</td>
                                <td>Probable Risk Scenarios </td>
                            </tr> 
                            <tr>
                                <td class="text-muted">RiskOwner</td>
                                <td>:</td>
                                <td>Aditya Upreti</td>
                            </tr>  
                            <tr>
                                <td class="text-muted">Approval Status</td>
                                <td>:</td>
                                <td class="text-success">Approved</td>
                            </tr> <tr>
                                <td class="text-muted">Risk Status</td>
                                <td>:</td>
                                <td class="text-success">Open</td>
                            </tr>
              
                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                         
                            <tr>
                                <td class="text-muted">Threat Category</td>
                                <td>:</td>
                                <td>System Failure</td>
                            </tr>
                            <tr>
                                <td class="text-muted">Threat</td>
                                <td>:</td>
                                <td>Software failure</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>

                            <tr>
                                <td class="text-muted">Probability</td>
                                <td>:</td>
                                <td>Unlikely</td>
                            </tr>
                            <tr>
                                <td class="text-muted">Impact</td>
                                <td>:</td>
                                <td>Medium</td>
                            </tr> 
                            <tr>
                                <td class="text-muted">Risk Rating</td>
                                <td>:</td>
                                <td>Medium(6)</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>Treat</td>
                <td>
                    <table>
                        <tbody>

                            <tr>
                                <td class="text-muted">Probability</td>
                                <td>:</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Impact</td>
                                <td>:</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Risk Rating</td>
                                <td>:</td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>

                            <tr>
                                <td class="text-muted">Risk Entry Date</td>
                                <td>:</td>
                                <td>6/1/2022 10:55:32 AM</td>
                            </tr>
                            <tr>
                                <td class="text-muted">Last Review Date</td>
                                <td>:</td>
                                <td>1/6/2023 3:43:48 PM</td>
                            </tr>
                            <tr>
                                <td class="text-muted">Next Review Date</td>
                                <td>:</td>
                                <td>1/31/2023 12:00:00 AM</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td><span><i class="cv-edit me-1" role="button"></i><i class="cv-delete me-1 text-danger" role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"></i><i class="cv-search "></i></span></td>
            </tr>
        </tbody>
        </table>
</div>


<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">
                    Manage BCM Risk
                    Configuration
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-6">
                        <div class="form-group">
                            <label class="form-label">Risk Item Category</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-risk-item-category"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select Risk Item Category</option>
                                </select>
                            </div>
                        </div> 
                        <div class="form-group">
                            <label class="form-label">Threat Category</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-threat-category"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select Threat Category</option>
                                </select>
                            </div>
                        </div>  
                        <div class="form-group">
                            <label class="form-label">Service Criticality value</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-service-criticality"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select Service Criticality value</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Risk Owner</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-risk-owner"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select Risk Owner</option>
                                </select>
                            </div>
                        </div>
                       
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label class="form-label">BCM Entity</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-entity-name"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select BCM Entity</option>
                                </select>
                            </div>
                        </div>
                      
                        <div class="form-group">
                            <label class="form-label">Threat</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-threat"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select Threat</option>
                                </select>
                            </div>
                        </div>
                      
                        <div class="form-group">
                            <label class="form-label">Vulnerability</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-vulnerability"></i></span>
                                <input class="form-control" type="text" placeholder="Enter Vulnerability" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Risk Champion</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-risk-champion"></i></span>
                                <input class="form-control" type="text" placeholder="Enter Risk Champion" />
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Probable Risk Scenarios</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-probable-risk"></i></span>
                                <textarea class="form-control" placeholder="Enter Probable Risk Scenarios" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Existing Control</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-existing-control"></i></span>
                                <textarea class="form-control" placeholder="Enter Existing Control" style="height:0px"></textarea>
                            </div>
                        </div>
                    </div>
                    <h6 class="Sub-Title">Inherent Risk Assessment</h6>
                    <div class="col-6">
                        
                        <div class="form-group">
                            <label class="form-label">Inherent Probability Rating</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-probability-ratings"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select Inherent Probability Rating</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Inherent Risk Value</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-risk-value"></i></span>
                                <input type="text" class="form-control" readonly />
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label class="form-label">Impact</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-impact"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Inherent Risk Rating</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-risk-rating"></i></span>
                                <input type="text" class="form-control" readonly />
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Risk Treatment Option</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-risk-option"></i></span>
                                <select class="form-select form-select-sm">
                                    <option>Select Risk Treatment Option</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex align-items-center gap-1 mb-2">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="true" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                            <h6 class="mb-0">Attach Recovery Plan</h6>
                        </div>
                        <div class="row collapse show" id="collapseExample" style="">
                          <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">Incident Name</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-incident"></i></span>
                                        <input type="text" class="form-control" />
                                    </div>
                                </div>
                          </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">Recovery Plan</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-recovery-plan"></i></span>
                                        <select class="form-select form-select-sm">
                                            <option>Select Recovery Plan</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-1 my-2">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample1" aria-expanded="true" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                            <h6 class="mb-0">Risk Review Section</h6>
                        </div>
                        <div class="row collapse" id="collapseExample1" style="">
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">Last Review Date</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                                        <input type="date" class="form-control" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">Next Review Date</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                                        <input type="date" class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>  <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/delete.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->