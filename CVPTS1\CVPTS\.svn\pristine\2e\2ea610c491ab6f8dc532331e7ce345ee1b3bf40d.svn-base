﻿
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@model BCM.BusinessClasses.BIASurveyQuestion
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<form asp-action="SaveBIAQuestion" method="post">
    <div class="row row-cols-1">
        <div class="col">
            <div class="form-group">
                <label class="form-label">Section</label>
                <div class="input-group">
                    <input class="form-control" type="hidden" asp-for="ID" />
                    <span class="input-group-text"><i class="cv-bia-section"></i></span>
                    <select class="form-control" asp-for="SectionID" id="ddlBIASection" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstBIASection,"SectionID","SectionName"))" required>
                        <option selected disabled value="">-- All BIA Section --</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Question</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-questions"></i></span>
                    <input class="form-control" type="text" placeholder="Question" asp-for="QuestionDetails" required/>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Sequence No.</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-questions"></i></span>
                    <input class="form-control" type="text" placeholder="Sequence No." asp-for="SequenceNo" required/>
                </div>
            </div>
        </div>
       
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>