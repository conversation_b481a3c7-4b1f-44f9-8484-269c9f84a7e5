﻿using AutoMapper;
using BCM.BusinessClasses;
using BCM.Shared;
using BCM.UI.Areas.BCMReports.ReportModels.BCMManagementReviewReport;
using BCM.UI.Areas.BCMReports.ReportModels.BIAReport;
using BCM.UI.Areas.BCMReports.ReportModels.HRReport;
using BCM.UI.Areas.BCMReports.ReportModels.KPIReport;
using BCM.UI.Areas.BCMReports.ReportModels.RiskAssessmentReport;

namespace BCM.UI.Areas.BCMReports.Mapper;

public class ReportProfile : Profile
{
    public static List<int> AutoIncrement { get; set; } = new();
    public ReportProfile()
    {
        //KPI Report Mapping
        CreateMap<PerformanceEvaluation, KPIReportModel>()
            .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrgID))
            .ForMember(dest => dest.StatusId, opt => opt.MapFrom(src => src.StatusID))
            .ForMember(dest => dest.MetricId, opt => opt.MapFrom(src => src.MetricID))
            .ForMember(dest => dest.MeasurementId, opt => opt.MapFrom(src => src.MeasurementID))
            .ForMember(dest => dest.ModeId, opt => opt.MapFrom(src => src.ModeID))
            .ForMember(dest => dest.AnalysisId, opt => opt.MapFrom(src => src.AnalysisID))
            .ForMember(dest => dest.FrequencyId, opt => opt.MapFrom(src => src.FrequencyID))
            .ForMember(dest => dest.ResourcesRequired, opt => opt.MapFrom(src => src.ResourcesReq))
            .ForMember(dest => dest.ResponsibleParties, opt => opt.MapFrom(src => src.ResponsibleParties))
            .ForMember(dest => dest.EffectiveCriteria, opt => opt.MapFrom(src => src.EffectiveCriteria))
            .ForMember(dest => dest.RemarkEffectivenessLevel, opt => opt.MapFrom(src => src.RemarkEffLevel))
            .ForMember(dest => dest.EffectiveRating, opt => opt.MapFrom(src => src.EffectiveRatingRpt))
            .ForMember(dest => dest.Effectiveness, opt => opt.MapFrom(src => src.Effectiveness))
            .ForMember(dest => dest.OverallKPI, opt => opt.MapFrom(src => src.OverallKPI))
            .ForMember(dest => dest.Target, opt => opt.MapFrom(src => src.Target))
            .ForMember(dest => dest.CorrectiveAction, opt => opt.MapFrom(src => src.CorrectiveAction))
            .ForMember(dest => dest.CurrentRisk, opt => opt.MapFrom(src => src.CurrentRisk))
            .ForMember(dest => dest.Remarks, opt => opt.MapFrom(src => src.Remarks))
            .ForMember(dest => dest.ITDRName, opt => opt.MapFrom(src => src.ITDRName))
            .ForMember(dest => dest.Objective, opt => opt.MapFrom(src => src.Objective))
            .ForMember(dest => dest.MeasureKPI, opt => opt.MapFrom(src => src.MeasureKPI))
            .ForMember(dest => dest.Rating, opt => opt.MapFrom(src => src.Rating))
            .ForMember(dest => dest.TargetedDate, opt => opt.MapFrom(src => src.Targeteddate))
            .ForMember(dest => dest.MeasuredByName, opt => opt.MapFrom(src => src.MeasuredByName));

        //HR Report Mapping
        CreateMap<TimeIntervalDataForReport, HRReportModel>()
            .ForMember(dest => dest.ASGName, opt => opt.MapFrom(src => src.ASGName))
            .ForMember(dest => dest.DepartmentName, opt => opt.MapFrom(src => src.DepartmentName))
            .ForMember(dest => dest.SubDepartmentName, opt => opt.MapFrom(src => src.SubDepartmentName))
            .ForMember(dest => dest.DivisionName, opt => opt.MapFrom(src => src.DivisionName))
            .ForMember(dest => dest.ProcessName, opt => opt.MapFrom(src => src.ProcessName))
            .ForMember(dest => dest.Time, opt => opt.MapFrom(src => src.Time))
            .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value))
            .ForMember(dest => dest.UnitID, opt => opt.MapFrom(src => src.UnitID))
            .ForMember(dest => dest.DepartmentID, opt => opt.MapFrom(src => src.DepartmentID))
            .ForMember(dest => dest.SubFunctionID, opt => opt.MapFrom(src => src.SubFunctionID))
            .ForMember(dest => dest.OrgID, opt => opt.MapFrom(src => src.OrgID))
            .ForMember(dest => dest.ProcessID, opt => opt.MapFrom(src => src.ProcessID));

        //Risk Assessment Report Mapping
        CreateMap<RiskAssesmentResult, RiskAssessmentReportModel>();

        //BIA Report
        CreateMap<BIAProfileMaster, ProcessIdentification>()
            .ForMember(dest => dest.ProcessName, opt => opt.MapFrom(src => src.ProcessName))
            .ForMember(dest => dest.ProcessOwner, opt => opt.MapFrom(src => src.ProcessOwnerName))
            .ForMember(dest => dest.CriticalityAssessment, opt => opt.MapFrom(src => src.UserEmail))
            .ForMember(dest => dest.ImpactName, opt => opt.MapFrom(src => src.ImpactName))
            .ForMember(dest => dest.ImpactTypeName, opt => opt.MapFrom(src => src.ImpactTypeName))
            .ForMember(dest => dest.PriorityActivityList, opt => opt.MapFrom(src => src.ApproverName));

        CreateMap<BIAVitalRecord, VitalRecords>()
            .ForMember(dest => dest.ProcessName, opt => opt.MapFrom(src => src.ProcessName))
            .ForMember(dest => dest.VitalRecordsName, opt => opt.MapFrom(src => src.VitalRecordName))
            .ForMember(dest => dest.BriefDescription, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.TypeOfRecord))
            .ForMember(dest => dest.MediaNature, opt => opt.MapFrom(src => src.Impact))
            .ForMember(dest => dest.PrimaryStorageLocation, opt => opt.MapFrom(src => src.Facility))
            .ForMember(dest => dest.SecondaryStorageLocation, opt => opt.MapFrom(src => src.SecondaryStorageLocation))
            .ForMember(dest => dest.AlternateSource, opt => opt.MapFrom(src => src.NeedBy));

        CreateMap<ProcessBIAFacility, WorkspaceRequirements>()
            .ForMember(dest => dest.ProcessName, opt => opt.MapFrom(src => src.ProcessName))
            .ForMember(dest => dest.PrimaryLocation, opt => opt.MapFrom(src => src.FacilityName))
            .ForMember(dest => dest.WorkTransferPossible, opt => opt.MapFrom(src => src.FacilityType))
            .ForMember(dest => dest.RemoteAccessDetails, opt => opt.MapFrom(src => src.RemoteAccessDetails))
            .ForMember(dest => dest.OptionPrimaryLocation, opt => opt.MapFrom(src => src.LocationName));

        CreateMap<ProcessBIAPeopleInfo, HRRequirements>()
            .ForMember(dest => dest.ProcessName, opt => opt.MapFrom(src => src.ProcessName))
            .ForMember(dest => dest.CurrentActualManPower, opt => opt.MapFrom(src => src.IsCritical))
            .ForMember(dest => dest.Day1, opt => opt.MapFrom(src => src.Day1))
            .ForMember(dest => dest.Day3, opt => opt.MapFrom(src => src.Day3))
            .ForMember(dest => dest.Day7, opt => opt.MapFrom(src => src.Day7))
            .ForMember(dest => dest.Day14, opt => opt.MapFrom(src => src.Day14))
            .ForMember(dest => dest.Day30, opt => opt.MapFrom(src => src.Day30))
            .ForMember(dest => dest.Beyond, opt => opt.MapFrom(src => src.Beyond))
            .ForMember(dest => dest.Primary, opt => opt.MapFrom(src => src.PrimaryResource))
            .ForMember(dest => dest.AssignedBackup, opt => opt.MapFrom(src => src.AssignedBackup))
            .ForMember(dest => dest.Activity, opt => opt.MapFrom(src => src.Activity))
            .ForMember(dest => dest.PotentialTeam, opt => opt.MapFrom(src => src.PotentialTeam));

        CreateMap<ProcessBIAApplicationInfo, ITRequirements>()
            .ForMember(dest => dest.ProcessName, opt => opt.MapFrom(src => src.ProcessName))
            .ForMember(dest => dest.ITApplication, opt => opt.MapFrom(src => src.ApplicationName))
            .ForMember(dest => dest.KeyUsage, opt => opt.MapFrom(src => src.KeyUsage))
            .ForMember(dest => dest.ITApplicationRTO, opt => opt.MapFrom(src => src.ApplicationRTOText))
            .ForMember(dest => dest.ITApplicationDataLoss, opt => opt.MapFrom(src => src.ApplicationRPOText))
            .ForMember(dest => dest.ManualWorkArounds, opt => opt.MapFrom(src => src.ManualWorkarounds));

        CreateMap<EquipmentSupply, WorkAreaRecovery>()
            .ForMember(dest => dest.ProcessName, opt => opt.MapFrom(src => src.ProcessName))
            .ForMember(dest => dest.EquipmentSupply, opt => opt.MapFrom(src => src.EquipmentName))
            .ForMember(dest => dest.Comments, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.Day1, opt => opt.MapFrom(src => src.Day1))
            .ForMember(dest => dest.Day3, opt => opt.MapFrom(src => src.Day3))
            .ForMember(dest => dest.Day7, opt => opt.MapFrom(src => src.Day7))
            .ForMember(dest => dest.Day14, opt => opt.MapFrom(src => src.Day14))
            .ForMember(dest => dest.Day30, opt => opt.MapFrom(src => src.Day30))
            .ForMember(dest => dest.Beyond, opt => opt.MapFrom(src => src.Beyond));

        CreateMap<ProcessBIAThirdParty, ThirdParties>()
            .ForMember(dest => dest.ProcessName, opt => opt.MapFrom(src => src.ProcessName))
            .ForMember(dest => dest.ThirdPartName, opt => opt.MapFrom(src => src.ThirdPartyName))
            .ForMember(dest => dest.NatureofService, opt => opt.MapFrom(src => src.NatureOfService))
            .ForMember(dest => dest.CriticalityCategory, opt => opt.MapFrom(src => src.CriticalityCategory))
            .ForMember(dest => dest.ContractualSupportArrangements, opt => opt.MapFrom(src => src.IsCritical))
            .ForMember(dest => dest.Contingencies, opt => opt.MapFrom(src => src.Contingencies))
            .ForMember(dest => dest.PotentialAlternateSources, opt => opt.MapFrom(src => src.PotentialAlternateSources));

        //BCM ManagementReviewReport
        CreateMap<ReviewReportMaster, ReviewReportMasterVm>()
            .ForMember(dest => dest.SrNo, source => source.MapFrom(src => GenerateId()))
            .ForMember(dest => dest.OrgName, source => source.MapFrom(src => src.OrganizationName))
            .ForMember(dest => dest.ReportName, source => source.MapFrom(src => src.ReportName))
            .ForMember(dest => dest.Status, source => source.MapFrom(src => src.Status))
            .ForMember(dest => dest.Owner, source => source.MapFrom(src => src.OwnerName))
            .ForMember(dest => dest.ReferenceNo, source => source.MapFrom(src => src.ReportCode))
            .ForMember(dest => dest.PlanDate, source => source.MapFrom(src => ValidateDateTime(src.PlanDate)))
            .ForMember(dest => dest.Describtion, source => source.MapFrom(src => src.Description));

        CreateMap<ReviewReportMomItem, ReviewReportMoMItemVm>()
            .ForMember(dest => dest.SrNo, source => source.MapFrom(src => GenerateId()))
            .ForMember(dest => dest.MOMItem, source => source.MapFrom(src => src.MomItem))
            .ForMember(dest => dest.ReviewType, source => source.MapFrom(src => src.ReviewType))
            .ForMember(dest => dest.Owner, source => source.MapFrom(src => src.OwnerName))
            .ForMember(dest => dest.ReviewDate, source => source.MapFrom(src => ValidateStringDateTime(src.ReviewDate)))
            .ForMember(dest => dest.ClosingDate, source => source.MapFrom(src => ValidateStringDateTime(src.ClosedDate)));

        CreateMap<BusinessProcessInfo, BCMPolicies>()
            .ForMember(dest => dest.SrNo, source => source.MapFrom(src => GenerateId()))
            .ForMember(dest => dest.BCMEntity, source => source.MapFrom(src => src.ProcessName))
            .ForMember(dest => dest.EntityType, source => source.MapFrom(src => src.BCMEntityType))
            .ForMember(dest => dest.Owner, source => source.MapFrom(src => src.ProcessOwner))
            .ForMember(dest => dest.ReviewDate, source => source.MapFrom(src => ValidateDateTime(src.ReviewDate)))
            .ForMember(dest => dest.LastReviewDate, source => source.MapFrom(src => ValidateDateTime(src.LastReviewDate)))
            .ForMember(dest => dest.RTO, source => source.MapFrom(src => Utilities.GetFormattedRTO(Convert.ToInt32(src.RTO))))
            .ForMember(dest => dest.RPO, source => source.MapFrom(src => Utilities.GetFormattedRTO(Convert.ToInt32(src.RPO))))
            .ForMember(dest => dest.MTR, source => source.MapFrom(src => Utilities.GetFormattedRTO(Convert.ToInt32(src.MTR))));

        CreateMap<InternalAuditProgramInfo, AuditVm>()
            .ForMember(dest => dest.SrNo, source => source.MapFrom(src => GenerateId()))
            .ForMember(dest => dest.AuditName, source => source.MapFrom(src => src.AuditName))
            .ForMember(dest => dest.AuditorName, source => source.MapFrom(src => src.AuditorName))
            .ForMember(dest => dest.AuditorEmail, source => source.MapFrom(src => src.AuditorEmail))
            .ForMember(dest => dest.AuditorMobile, source => source.MapFrom(src => src.AuditorMobile))
            .ForMember(dest => dest.AuditeeName, source => source.MapFrom(src => src.AuditeeName))
            .ForMember(dest => dest.AuditeeEmail, source => source.MapFrom(src => src.AuditeeEmail))
            .ForMember(dest => dest.AuditeeMobile, source => source.MapFrom(src => src.AuditeeMobile))
            .ForMember(dest => dest.PlanStartDate, source => source.MapFrom(src => ValidateDateTime(src.PlanStartDate)))
            .ForMember(dest => dest.PlanEndDate, source => source.MapFrom(src => ValidateDateTime(src.PlanEndDate)))
            .ForMember(dest => dest.ActualStartDate, source => source.MapFrom(src => ValidateDateTime(src.ActualStartDate)))
            .ForMember(dest => dest.ActualEndDate, source => source.MapFrom(src => ValidateDateTime(src.ActualEndDate)));

        CreateMap<BCM.BusinessClasses.BCMStrategy, BCMStrategyVm>()
            .ForMember(dest => dest.SrNo, source => source.MapFrom(src => GenerateId()))
            .ForMember(dest => dest.StrategyCode, source => source.MapFrom(src => src.StratCode))
            .ForMember(dest => dest.StrategyName, source => source.MapFrom(src => src.StrategyName))
            .ForMember(dest => dest.StrategyOwner, source => source.MapFrom(src => src.OwnerName))
            .ForMember(dest => dest.StrategyBelongsTo, source => source.MapFrom(src => src.EntityName))
            .ForMember(dest => dest.ReviewDate, source => source.MapFrom(src => ValidateDateTime(src.NextReviewDate)))
            .ForMember(dest => dest.LastReviewDate, source => source.MapFrom(src => ValidateDateTime(src.LastReviewDate)));


    }
    public static int GenerateId()
    {
        var increment = AutoIncrement.Count == 0 ? 1 : AutoIncrement.LastOrDefault() + 1;
        AutoIncrement.Add(increment);
        return increment;
    }

    public static void ClearAutoIncrement()
    {
        AutoIncrement.Clear();
    }

    private string ValidateStringDateTime(string? dateTime)
    {

        DateTime result;
        if (!string.IsNullOrWhiteSpace(dateTime) && DateTime.TryParse(dateTime, out result))
        {
            var date = result.ToString("dd-MM-yyyy");
            if (date.Equals("01-01-0001"))
            {
                return "NA";
            }
            return date;
        }
        else
        {
            return "NA";
        }
    }
    private string ValidateDateTime(DateTime? dateTime)
    {
        var date = dateTime?.ToString("dd-MM-yyyy");
        return string.IsNullOrWhiteSpace(date) || date.Equals("01-01-0001") ? "NA" : date;
    }
}