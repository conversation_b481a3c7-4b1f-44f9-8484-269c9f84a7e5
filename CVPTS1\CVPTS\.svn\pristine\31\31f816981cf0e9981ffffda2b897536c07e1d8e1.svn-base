﻿@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="Page-Header d-flex align-items-center justify-content-between mb-3">
    <h6 class="Page-Title">Dependent Organization Structure</h6>
</div>

<div class="Page-Condant card border-0">
    <div class="card-body">

        <div class="row">
            <div class="col-12">
                <p class="fw-semibold mb-2">Instructions and Guidelines</p>
                <div class="accordion accordion-flush" id="accordionFlushExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                Worksheet #1: Process Identification and Criticality Assessment
                            </button>
                        </h2>
                        <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                            <div class="accordion-body">
                                <p class="mb-2">Instructions and guidance for completion</p>
                                <ul class="ps-3">
                                    <li>
                                        Prioritized Activity List - What are the "must do" recoveries related activities to be performed once the process is resumed? What are the tasks that
                                        can be put on hold and postponed with minimal impact?
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <p class="fw-semibold">Configure Impact Of Org Structure for PN - 02 Nov-2022 ( PRC-2022-168 )</p>
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-lable">Version</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-actions"></i></span>
                                <input class="form-control" type="text" readonly value="1" />
                            </div>
                        </div>
                    </div>
                    <div class="col d-grid align-items-end">
                        <div class="form-group">
                            <label class="form-lable">Questions</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="option1">
                                <label class="form-check-label" for="inlineRadio1">Human Resources Requirements</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="card-group gap-3 mb-3">
                    <div class="card border rounded-2">
                        <div class="card-header border-0 fw-semibold">Orgnization Structure</div>
                        <div class="card-body">
                            <div class="tree-menu">
                                <ul class="tree">

                                    <li>
                                        <span role="button">
                                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                            Perpetuuiti Technosoft
                                        </span>
                                        <ul>
                                            <li>
                                                <span role="button">
                                                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                    Perpetuuiti
                                                </span>
                                                <ul class="sub-parent">
                                                    <li>
                                                        <span role="button">
                                                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                            Admin Group
                                                        </span>
                                                    </li>
                                                    <li>
                                                        <span>
                                                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                            Corporate affairs
                                                        </span>
                                                        <ul class="sub-parent">
                                                            <li>
                                                                <span>
                                                                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                                    Department
                                                                </span>
                                                            </li>
                                                            <li>
                                                                <span>
                                                                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                                    Operation and Logistics
                                                                </span>
                                                            </li>
                                                            <li>
                                                                <span>
                                                                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                                    Secretary General
                                                                </span>
                                                                <ul class="sub-parent">
                                                                    <li>
                                                                        <span>
                                                                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                                            Department
                                                                        </span>
                                                                    </li>
                                                                    <li>
                                                                        <span>
                                                                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                                            Operation and Logistics
                                                                        </span>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                    <li>
                                                        <span>
                                                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                            Finance affairs
                                                        </span>
                                                        <ul class="sub-parent">
                                                            <li>
                                                                <span>
                                                                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                                    Department
                                                                </span>
                                                            </li>
                                                            <li>
                                                                <span>
                                                                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                                                    Operation and Logistics
                                                                </span>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>

                                        </ul>

                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card border rounded-2">
                        <div class="card-header border-0 fw-semibold">Selected Organization Entities</div>
                        <div class="card-body">
                            <table class="table table-border">
                                <thead>
                                    <tr>
                                        <td>Department</td>
                                        <td>Sub Department</td>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="2">
                                            <div class="text-center">
                                                <img src="/img/Isomatric/no_records_to_display.svg" class="img-fluid w-25" />
                                            </div>
                                        </td>

                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 text-end">
                <button class="btn btn-sm btn-outline-primary">Back</button>
                <button class="btn btn-sm btn-secondary">Cancel</button>
                <button class="btn btn-sm btn-primary">View All</button>
            </div>
        </div>
    </div>
</div>