using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.BusinessProcessComponents;
using BCM.Shared;
using BCM.UI.Areas.BCMIncidentManagement.Services;
using BCM.UI.Services;
using DevExpress.AspNetCore;
using DevExpress.Drawing;

var builder = WebApplication.CreateBuilder(args);

DXFontRepository.Instance.AddFont(@"wwwroot\fonts\San-Francisco-UI\SFUIText-Regular.ttf");
DXFontRepository.Instance.AddFont(@"wwwroot\fonts\San-Francisco-UI\SFUIText-Bold.ttf");

builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Services.AddAutoMapper(typeof(Program).Assembly);
// Add services to the container.
builder.Services.AddControllersWithViews();
builder.Services.AddScoped<Utilities, Utilities>();
builder.Services.AddScoped<BIASurveyUtility, BIASurveyUtility>();
builder.Services.AddScoped<BCPEnum, BCPEnum>();
builder.Services.AddScoped<CVLogger,CVLogger>();
builder.Services.AddScoped<BCPIncidentNotification, BCPIncidentNotification>();
builder.Services.AddScoped<BCMMail, BCMMail>();
builder.Services.AddScoped<BCPSms, BCPSms>();
builder.Services.AddHttpClient<ApiService>();
builder.Services.AddScoped<CVIncidentManagement>();
//create and save logs on this location 
//builder.Services.AddLogging(logging =>
//{
//    // Add file logging
//    logging.ClearProviders();
//    logging.AddFile("C:\\dotNetCoreLogger\\logg.txt", fileSizeLimitBytes: 10);
//});


var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
builder.Services.Configure<ConfigSettings>(config.GetSection("ConfigurationValue"));

builder.Services.AddScoped<ProcessMgr>();
builder.Services.AddScoped<ProcessSrv>();

// Register encryption service
builder.Services.AddScoped<ISimpleEncryptionService, SimpleEncryptionService>();

builder.Services.AddDevExpressControls();
builder.Services.AddSession(Option =>
{
    Option.IdleTimeout = TimeSpan.FromMinutes(20);
    Option.Cookie.HttpOnly = true;
    Option.Cookie.IsEssential = true;
    Option.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest; // Changed from Always to SameAsRequest
});
builder.Services.AddHttpContextAccessor();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
}
app.UseSession();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthorization();
app.UseDevExpressControls();
app.UseDeveloperExceptionPage(); //Added By Shubham B.


app.UseEndpoints(endpoints =>
{

    endpoints.MapControllerRoute(
      name: "areas",
      pattern: "{area:exists}/{controller}/{action}/{id?}"
    );

    endpoints.MapControllerRoute(
            name: "default",
            pattern: "{controller=Login}/{action=Login}/{id?}"
          );
});

//app.UseEndpoints(endpoints =>
//{


//    endpoints.MapControllerRoute(
//            name: "default",
//            pattern: "{controller=Login}/{action=Login}/{id?}"
//          );
//    endpoints.MapAreaControllerRoute(
//      name: "areas",
//      areaName: "Dashboard",
//      pattern: "{area:exists}/{controller}/{action}/{id?}"
//    );

//});
app.Run();
