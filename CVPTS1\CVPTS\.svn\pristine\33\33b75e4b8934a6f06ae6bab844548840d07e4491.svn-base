﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Data.Odbc;
using System.Text;
using System.Text.Json;

namespace BCM.UI.Areas.BCMProcessBIA.Controllers;
[Area("BCMProcessBIA")]
public class BIAResultsController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly BIASurveyUtility _BIASurveyUtility;
    private readonly CVLogger _CVLogger;
    List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();

    public BIAResultsController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, BIASurveyUtility BIASurveyUtility) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _BIASurveyUtility = BIASurveyUtility;
    }

    [HttpGet]
    public IActionResult BIAResults()
    {
        try
        {
            PopulateDropdowns();
            PopulateBIAResults(_UserDetails.OrgID);
            AllProcesses();
            PopulateSurveyList(_UserDetails.OrgID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
    }

    public void PopulateDropdowns()
    {
        try
        {
            ViewBag.selectedOrgID = _UserDetails.OrgID;
            //Organization
            ViewBag.OrgName = new SelectList(_Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.UserRoleID.ToString()), "Id", "OrganizationName");
            //Unit
            ViewBag.Unit = new SelectList(_Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString()), "UnitID", "UnitName");
            //Department
            ViewBag.Department = new SelectList(_Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString()), "DepartmentID", "DepartmentName");
            //SubDepartment
            ViewBag.lstSubDepartment = new SelectList(_Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString()), "SubFunctionID", "SubFunctionName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }


    public JsonResult BindFunction(int iUnitID = 0)
    {
        try
        {
            ViewBag.ddlSelectedUnitID = iUnitID;
            var objDepartmentInfo = _Utilities.BindFunction(iUnitID);
            //BindSearch();
            return Json(objDepartmentInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);           
        }
        return Json(null);
    }

    public JsonResult BindSubFunction(int iFunctionID)
    {
        try
        {
            var objSubDepartmentInfo = _Utilities.BindSubFunction(iFunctionID);
            return Json(objSubDepartmentInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    //public void BindSearch()
    //{
    //    try
    //    {
    //        PopulateBIAResults(_UserDetails.OrgID);
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }
    //}
    
    public PartialViewResult PopulateBIAResults(int iOrgID, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            List<DepartmentInfo> lstDepartmentInfo = _ProcessSrv.GetOrgnizationForBIAResults(iOrgID);
 
            var filteredOrgInfo = lstDepartmentInfo
                .Select(d => new
                {
                    d.OrgGroupID,
                    d.OrgID,
                    d.OrganiZationName,
                    d.UnitID,
                    d.UnitName,
                    d.DepartmentID,
                    d.DepartmentName,
                    d.SubfunctionID,
                    d.SubfunctionName
                })
                .Distinct().ToList();

            if (iOrgID > 0)
            {
                filteredOrgInfo = (from item in filteredOrgInfo
                                   where item.OrgID == iOrgID
                                   select item).ToList();
            }

            if (iUnitID > 0)
            {
                filteredOrgInfo = (from item in filteredOrgInfo
                                   where item.UnitID == iUnitID
                                   select item).ToList();
            }

            if (iDepartmentID > 0)
            {
                filteredOrgInfo = (from item in filteredOrgInfo
                                   where item.DepartmentID == iDepartmentID
                                   select item).ToList();
            }

            if (iSubDepartmentID > 0)
            {
                filteredOrgInfo = (from item in filteredOrgInfo
                                   where item.SubfunctionID == iSubDepartmentID
                                   select item).ToList();
            }

            var objFilteredOrgInfo = new List<DepartmentInfo>();
            foreach (var item in filteredOrgInfo)
            {
                objFilteredOrgInfo.Add(new DepartmentInfo
                {
                    OrgGroupID = item.OrgGroupID,
                    OrgID = item.OrgID,
                    OrganiZationName = item.OrganiZationName,
                    UnitID = item.UnitID,
                    UnitName = item.UnitName,
                    DepartmentID = item.DepartmentID,
                    DepartmentName = item.DepartmentName,
                    SubfunctionID = item.SubfunctionID,
                    SubfunctionName = item.SubfunctionName,
                    TotalProcessCount = GetTotalNoOFProcess(item.OrgID, item.UnitID, item.DepartmentID, item.SubfunctionID),
                    NonCriticalProcessCount = GetNonCriticalProcess(item.OrgID, item.UnitID, item.DepartmentID, item.SubfunctionID),
                    CriticallProcessCount = GetCriticalProcess(item.OrgID, item.UnitID, item.DepartmentID, item.SubfunctionID),
                    ApprovedProcessCount = GetApprovedProcess(item.OrgID, item.UnitID, item.DepartmentID, item.SubfunctionID),
                    DisApprovedlProcessCount = GetDisApprovedProcess(item.OrgID, item.UnitID, item.DepartmentID, item.SubfunctionID),
                    WaitingForApproveProcessCount = GetPendingProcess(item.OrgID, item.UnitID, item.DepartmentID, item.SubfunctionID)
                });
            }
            ViewBag.DepartmentInfo = objFilteredOrgInfo;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_BIAResultsList");
    }

    public PartialViewResult PopulateSurveyList(int iOrgID, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            lstBusinessProcessInfo = _BIASurveyUtility.GetFilteredBIASurveyDataTable(GetFilterString(),
                _UserDetails.OrgID, (int)BCPEnum.EntityType.BusinessProcess);

            if (!_UserDetails.UserRole.Equals("ProductAdminRole"))
            {
                if (!_UserDetails.UserRole.Equals("SuperAdminRole"))
                {

                }
                else
                {

                }                
            }

            if (iOrgID > 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID
                                   select item).ToList();
            }

            if (iUnitID > 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.UnitID == iUnitID
                                   select item).ToList();
            }

            if (iDepartmentID > 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.DepartmentID == iDepartmentID
                                   select item).ToList();
            }

            if (iSubDepartmentID > 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.SubfunctionID == iSubDepartmentID
                                   select item).ToList();
            }
            ViewBag.SurveyList = lstBusinessProcessInfo;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorUtilities(ex);
        }
        return PartialView("_SurveyList");
    }

    //[HttpPost]
    //public JsonResult GetProcessCounts(int iOrgID, int iUnitID, int iDepartmentId, int iSubFunctionID)
    //{
    //    int iTotalProcesses = 0;
    //    int iCriticalProcesses = 0;
    //    int iNonCriticalProcess = 0;
    //    int iApprovedProcess = 0;
    //    int iDisApprovedProcess = 0;
    //    int iWaitingForApprovalProcess = 0;
    //    try
    //    {
    //        if (iDepartmentId > 0 && iSubFunctionID > 0)
    //        {
    //            iTotalProcesses = GetTotalNoOFProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
    //            iCriticalProcesses = GetNonCriticalProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
    //            iNonCriticalProcess = GetCriticalProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
    //            iApprovedProcess = GetApprovedProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
    //            iDisApprovedProcess = GetDisApprovedProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
    //            iWaitingForApprovalProcess = GetPendingProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
    //        }

    //        var result = new
    //        {
    //            Success = true,
    //            iTotalCnt = iTotalProcesses,
    //            iCriticalCnt = iCriticalProcesses,
    //            iNonCriticalCnt = iNonCriticalProcess,
    //            iApprovedCnt = iApprovedProcess,
    //            iDisApprovedCnt = iDisApprovedProcess,
    //            iWaitingForApprovalCnt = iWaitingForApprovalProcess
    //        };

    //        return Json(result);
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //        var errorResult = new
    //        {
    //            Success = false,
    //            Message = ex.Message
    //        };
    //        return Json(errorResult);
    //    }
    //}

    //public string GetConditionalString(int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    //{
    //    StringBuilder sbSearch = new StringBuilder();
    //    try
    //    {
    //        if (iOrgID > 0)
    //        {
    //            sbSearch.Append("OrgID ='" + iOrgID + "'");
    //        }
    //        if (iUnitID > 0)
    //        {
    //            sbSearch.Append(" AND UnitID ='" + iUnitID + "'");
    //        }
    //        if (iDepartmentID > 0)
    //        {
    //            sbSearch.Append(" And DepartmentID ='" + iDepartmentID + "'");
    //        }
    //        if (iSubDepartmentID > 0)
    //        {
    //            sbSearch.Append(" And SubfunctionID ='" + iSubDepartmentID + "'");
    //        }

    //        return sbSearch.ToString();
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }
    //    return sbSearch.ToString();
    //}

    public string GetConditionalString(List<DepartmentInfo> lstDepartmentInfo)
    {
        StringBuilder sbSearch = new StringBuilder();
        try
        {
            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return sbSearch.ToString();
    }

    private int GetTotalNoOFProcess(int iOrgID = 0, int iUnitID = 0, int iDepartmentId = 0, int iSubFunctionID = 0)
    {
        int iRowCount = 0;
        try
        {
            lstBusinessProcessInfo = AllProcesses();
            if (lstBusinessProcessInfo.Count > 0)
            {
                if (iSubFunctionID != 0)
                {
                    lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                              where item.SubfunctionID == iSubFunctionID
                                              select item).ToList(); //lstBusinessProcessInfo.Where(x => x.SubfunctionID == iSubFunctionID).ToList();
                }
                else if (iDepartmentId != 0 && iSubFunctionID == 0)
                {
                    lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                              where item.DepartmentID == iDepartmentId
                                              select item).ToList(); //lstBusinessProcessInfo.Where(x => x.DepartmentID == iDepartmentId).ToList();
                }
                else if (iUnitID != 0 && iDepartmentId == 0)
                {
                    lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                              where item.OrgID == iOrgID && item.DepartmentID == iDepartmentId && item.SubfunctionID == iSubFunctionID
                                              select item).ToList(); //lstBusinessProcessInfo.Where(x => x.OrgID == iOrgID && x.DepartmentID == iDepartmentId && x.SubfunctionID == iSubFunctionID).ToList();
                }
                else if (iUnitID == 0 && iDepartmentId == 0)
                {
                    lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                              where item.OrgID == iOrgID
                                              select item).ToList(); //lstBusinessProcessInfo.Where(x => x.OrgID == iOrgID).ToList();
                }
                ViewBag.SurveyList = lstBusinessProcessInfo;
                iRowCount = lstBusinessProcessInfo.Count();
            }            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return iRowCount;
        }
        return iRowCount;
    }

    private int GetNonCriticalProcess(int iOrgID = 0, int iUnitID = 0, int iDepartmentId = 0, int iSubFunctionID = 0)
    {
        int iRowCount = 0;
        try
        {
            lstBusinessProcessInfo = AllProcesses();
            if (iSubFunctionID != 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.SubfunctionID == iSubFunctionID && item.IsCritical != 1
                                          select item).ToList();
            }
            else if (iDepartmentId != 0 && iSubFunctionID == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.DepartmentID == iDepartmentId && item.IsCritical != 1
                                          select item).ToList();
            }
            else if (iUnitID != 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.UnitID == iUnitID && item.DepartmentID == iDepartmentId &&
                                          item.SubfunctionID == iSubFunctionID && item.IsCritical != 1
                                          select item).ToList();
            }
            else if (iUnitID == 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.IsCritical != 1
                                          select item).ToList();
            }
            ViewBag.SurveyList = lstBusinessProcessInfo;
            iRowCount = lstBusinessProcessInfo.Count();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return iRowCount;
        }
        return iRowCount;
    }

    private int GetCriticalProcess(int iOrgID = 0, int iUnitID = 0, int iDepartmentId = 0, int iSubFunctionID = 0)
    {
        int iRowCount = 0;
        try
        {
            lstBusinessProcessInfo = AllProcesses();
            if (iSubFunctionID != 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.SubfunctionID == iSubFunctionID && item.IsCritical == 1
                                          select item).ToList();
            }
            else if (iDepartmentId != 0 && iSubFunctionID == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.DepartmentID == iDepartmentId && item.IsCritical == 1
                                          select item).ToList();
            }
            else if (iUnitID != 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.UnitID == iUnitID && item.DepartmentID == iDepartmentId &&
                                          item.SubfunctionID == iSubFunctionID && item.IsCritical == 1
                                          select item).ToList();
            }
            else if (iUnitID == 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.IsCritical == 1
                                          select item).ToList();
            }
            ViewBag.SurveyList = lstBusinessProcessInfo;
            iRowCount = lstBusinessProcessInfo.Count();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return iRowCount;
        }
        return iRowCount;
    }

    private int GetApprovedProcess(int iOrgID = 0, int iUnitID = 0, int iDepartmentId = 0, int iSubFunctionID = 0)
    {
        int iRowCount = 0;
        try
        {
            lstBusinessProcessInfo = AllProcesses();
            if (iSubFunctionID != 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.SubfunctionID == iSubFunctionID && item.Status == (int)BCPEnum.ApprovalType.Approved
                                          select item).ToList();
            }
            else if (iDepartmentId != 0 && iSubFunctionID == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.DepartmentID == iDepartmentId && item.Status == (int)BCPEnum.ApprovalType.Approved
                                          select item).ToList();
            }
            else if (iUnitID != 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.UnitID == iUnitID && item.DepartmentID == iDepartmentId &&
                                          item.SubfunctionID == iSubFunctionID && item.Status == (int)BCPEnum.ApprovalType.Approved
                                          select item).ToList();
            }
            else if (iUnitID == 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.Status == (int)BCPEnum.ApprovalType.Approved
                                          select item).ToList();
            }
            ViewBag.SurveyList = lstBusinessProcessInfo;
            iRowCount = lstBusinessProcessInfo.Count();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return iRowCount;
        }
        return iRowCount;
    }

    private int GetDisApprovedProcess(int iOrgID = 0, int iUnitID = 0, int iDepartmentId = 0, int iSubFunctionID = 0)
    {
        int iRowCount = 0;
        try
        {
            lstBusinessProcessInfo = AllProcesses();
            if (iSubFunctionID != 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.SubfunctionID == iSubFunctionID && item.Status == (int)BCPEnum.ApprovalType.Disapproved
                                          select item).ToList();
            }
            else if (iDepartmentId != 0 && iSubFunctionID == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.DepartmentID == iDepartmentId && item.Status == (int)BCPEnum.ApprovalType.Disapproved
                                          select item).ToList();
            }
            else if (iUnitID != 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.UnitID == iUnitID && item.DepartmentID == iDepartmentId &&
                                          item.SubfunctionID == iSubFunctionID && item.Status == (int)BCPEnum.ApprovalType.Disapproved
                                          select item).ToList();
            }
            else if (iUnitID == 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.Status == (int)BCPEnum.ApprovalType.Disapproved
                                          select item).ToList();
            }
            ViewBag.SurveyList = lstBusinessProcessInfo;
            iRowCount = lstBusinessProcessInfo.Count();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return iRowCount;
        }
        return iRowCount;
    }

    private int GetPendingProcess(int iOrgID = 0, int iUnitID = 0, int iDepartmentId = 0, int iSubFunctionID = 0)
    {
        int iRowCount = 0;
        try
        {
            lstBusinessProcessInfo = AllProcesses();
            if (iSubFunctionID != 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.SubfunctionID == iSubFunctionID && item.Status == (int)BCPEnum.ApprovalType.WaitingforApproval
                                          select item).ToList();
            }
            else if (iDepartmentId != 0 && iSubFunctionID == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.DepartmentID == iDepartmentId && item.Status == (int)BCPEnum.ApprovalType.WaitingforApproval
                                          select item).ToList();
            }
            else if (iUnitID != 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.UnitID == iUnitID && item.DepartmentID == iDepartmentId &&
                                          item.SubfunctionID == iSubFunctionID && item.Status == (int)BCPEnum.ApprovalType.WaitingforApproval
                                          select item).ToList();
            }
            else if (iUnitID == 0 && iDepartmentId == 0)
            {
                lstBusinessProcessInfo = (from item in lstBusinessProcessInfo
                                          where item.OrgID == iOrgID && item.Status == (int)BCPEnum.ApprovalType.WaitingforApproval
                                          select item).ToList();
            }
            ViewBag.SurveyList = lstBusinessProcessInfo;
            iRowCount = lstBusinessProcessInfo.Count();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return iRowCount;
        }
        return iRowCount;
    }

    private List<BusinessProcessInfo> AllProcesses()
    {
        //List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
        try
        {
            if (_UserDetails.UserRole.Equals("ProductAdminRole") || _UserDetails.UserRole.Equals("ProductAdminRole"))
                lstBusinessProcessInfo = _BIASurveyUtility.GetFilteredBIASurveyDataTable(GetFilterString(), ViewBag.selectedOrgID, (int)BCPEnum.EntityType.BusinessProcess);
            else
                lstBusinessProcessInfo = _BIASurveyUtility.GetFilteredBIASurveyDataTable(GetFilterString(), _UserDetails.OrgID, (int)BCPEnum.EntityType.BusinessProcess);

            ViewBag.AllProcess = lstBusinessProcessInfo;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return lstBusinessProcessInfo;
    }

    private string GetFilterString()
    {
        int iUserID = _UserDetails.UserID;
        string strFilterString = string.Empty;
        string strUserFilterString = string.Empty;
        string strMainFilterString = string.Empty;
        try
        {
            string strIfOrgHead = " OrgHeadID = '" + iUserID + "'  OR OwnerID = '" + iUserID + "' OR  AltOwnerID = '" + iUserID + "' OR ApproverId='" + iUserID + "'  ";
            string strIfUnitHead = " UnitHeadID = '" + iUserID + "' OR UnitBCPCorID = '" + iUserID + "' OR AltBCPCorID = '" + iUserID + "' OR OwnerID = '" + iUserID + "' OR  AltOwnerID = '" + iUserID + "' OR ApproverId='" + iUserID + "'  ";
            string strIfFunctionHead = " DepartmentHeadID = '" + iUserID + "' OR AltDepartmentHeadID = '" + iUserID + "'  OR OwnerID = '" + iUserID + "' OR  AltOwnerID = '" + iUserID + "'  OR ApproverId='" + iUserID + "'  ";
            string strIfSubFunctionHead = " SubFunOwnerId = '" + iUserID + "' OR AltSubFunOwnerId = '" + iUserID + "'  OR OwnerID = '" + iUserID + "' OR  AltOwnerID = '" + iUserID + "'  OR ApproverId='" + iUserID + "'  ";

            string strIfNot;

            if (_UserDetails.UserRole.Equals("ProductAdminRole") || _UserDetails.UserRole.Equals("SuperAdminRole"))
                strIfNot = "1=1 ";
            else
                strIfNot = " OwnerID = '" + iUserID + "' OR AltOwnerID = '" + iUserID + "'  OR ApproverId='" + iUserID + "'  ";

            strFilterString = _Utilities.IsOrgHead(iUserID.ToString(), _UserDetails.OrgID.ToString()) == true ? strIfOrgHead : strIfNot;

            if (strFilterString == strIfNot)
                strFilterString = _Utilities.IsUnitHead(iUserID.ToString()) ==true ? strIfUnitHead : strIfNot;

            if (strFilterString == strIfNot)
                strFilterString = _Utilities.IsFunctionHead(iUserID.ToString()) == true ? strIfFunctionHead : strIfNot;

            if (strFilterString == strIfNot)
                strFilterString = _Utilities.IsSubFunctionHead(iUserID.ToString()) == true ? strIfSubFunctionHead : strIfNot;

            if (ViewBag.selectedOrgID > 0) strFilterString = strFilterString + " AND OrgID ='" + ViewBag.selectedOrgID;

            strMainFilterString = strFilterString;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorUtilities(ex);
        }
        return strMainFilterString;
    }

    [HttpPost]
    public PartialViewResult FilterSurveyList(int iOrgID = 0, int iUnitID = 0, int iDepartmentId = 0, int iSubFunctionID = 0, int iColumnID = 0)
    {
        try
        {
            if(iColumnID == 1)
            {
                GetTotalNoOFProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
            }
            else if (iColumnID == 2)
            {
                GetNonCriticalProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
            }
            else if (iColumnID == 3)
            {
                GetCriticalProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
            }
            else if (iColumnID == 4)
            {
                GetApprovedProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
            }
            else if (iColumnID == 5)
            {
                GetDisApprovedProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
            }
            else if (iColumnID == 6)
            {
                GetPendingProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
            }
            else if (iColumnID == 6)
            {
                GetPendingProcess(iOrgID, iUnitID, iDepartmentId, iSubFunctionID);
            }

            return PartialView("_SurveyList", ViewBag.SurveyList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorUtilities(ex);
            return PartialView("_SurveyList", ViewBag.SurveyList);
        }
    }
}

