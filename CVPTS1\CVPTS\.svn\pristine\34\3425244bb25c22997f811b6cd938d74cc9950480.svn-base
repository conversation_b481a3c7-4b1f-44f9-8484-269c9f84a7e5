﻿
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    var checkPagesID = ViewBag.CheckPagesId as List<int>;
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<div class="col">
    <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
        <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
            <span>
                <input class="form-check-input" name="selectedPages" type="checkbox" value="0">
            </span>
            <span class="">Check All</span>
        </span>
    </div>
</div><br />
@foreach (var item in ViewBag.lstWidges)
{
    bool objIsChecked = false;
    if (checkPagesID != null)
    {
        objIsChecked = ((List<int>)checkPagesID).Contains((int)item.WidgetId);
    }
    <div class="col d-grid">
        <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
            <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                <span>
                    <input class="form-check-input" name="selectedPages" type="checkbox" value="@item.WidgetId" id="@item.WidgetId" @(objIsChecked ? "checked" : "")>
                </span>
                <span class="">@item.WidgetName</span>
            </span>
        </div>
    </div>
}