﻿@model BCM.BusinessClasses.Facility
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<form asp-action="DeleteBCMFacility" method="post">
    <div>
        <input type="hidden" asp-for="FacilityID" />
        <input type="hidden" asp-for="EntityTypeID" />
    </div> 
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    <div class="modal-header d-grid text-center">
        <span class="fw-semibold">Do you really want to delete</span>
        @if (@Model.FacilityCode.ToString() != string.Empty)
        {
            <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.FacilityCode</span>" ?</span>
        }else{
            <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.FacilityName</span>" ?</span>
        }
        
    </div>
 
    <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>


<!-- Delete Modal -->
@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div> *@



