﻿@model BCM.BusinessClasses.RiskManagement
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}

<form asp-controller="ManageRisk" asp-action="AddRiskAssessmentProcessForm">
    <div class="row">
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">Risk Item Category</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-risk-item-category"></i></span>
                    <select class="form-select form-select-sm" asp-for="RiskItemCategory" name="RiskItemCategory" id="RiskItemCategory" asp-items="@(new SelectList(ViewBag.BCMEntitiesTypeMaster,"BCMEntityID","BCMEntityName"))" required>
                        <option selected disabled value="">Select Risk Item Category</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Threat Category</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-threat-category"></i></span>
                    <select class="form-select form-select-sm" asp-for="RiskCategory" id="RiskCategory" name="RiskCategory" asp-items="@(new SelectList(ViewBag.Disaster,"DisasterID","DisasterName"))" required>
                        <option selected disabled value="">Select Threat Category</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Service Criticality value</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-service-criticality"></i></span>
                    <select class="form-select form-select-sm" asp-for="ServiceCriticallyValue" id="ServiceCriticallyValue" name="ServiceCriticallyValue" required>
                        <option selected disabled value="">Select Service Criticality value</option>
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Risk Owner</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-risk-owner"></i></span>
                    <select class="form-select form-select-sm" name="RiskOwnerID" id="RiskOwnerID" asp-for="RiskOwnerID" asp-items="@(new SelectList(ViewBag.RiskChampion,"ResourceId","ResourceName"))" required>
                        <option selected disabled value="">Select Risk Owner</option>
                    </select>
                </div>
            </div>

        </div>
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">BCM Entity</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-entity-name"></i></span>
                    <select class="form-select form-select-sm" id="ProcessCode" asp-for="ProcessCode" required>
                        <option selected disabled value="">Select BCM Entity</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">Threat</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-threat"></i></span>
                    <select class="form-select form-select-sm" id="RiskType" asp-for="RiskType" name="RiskType" asp-items="@(new SelectList(ViewBag.Incident,"DisasterTypeID","DisasterTypeName"))" required>
                        <option selected disabled value="">Select Threat</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">Vulnerability</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-vulnerability"></i></span>
                    <input class="form-control" type="text" placeholder="Enter Vulnerability" asp-for="Vulnerability" name="Vulnerability" id="Vulnerability" readonly />
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Risk Champion</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-risk-champion"></i></span>
                    <select class="form-select form-select-sm" id="RiskChampionID" name="RiskChampionID" asp-for="RiskChampionID" asp-items="@(new SelectList(ViewBag.RiskChampion,"ResourceId","ResourceName"))" required>
                        <option selected disabled value="">Select Risk Champion</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                <label class="form-label">Probable Risk Scenarios</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-probable-risk"></i></span>
                    <textarea class="form-control" placeholder="Enter Probable Risk Scenarios" style="height:0px" asp-for="RiskDesCription" name="RiskDesCription" id="RiskDesCription" required></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Existing Control</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-existing-control"></i></span>
                    <textarea class="form-control" placeholder="Enter Existing Control" style="height:0px" asp-for="RiskHandleStatus" name="RiskHandleStatus" id="RiskHandleStatus" required></textarea>
                </div>
            </div>
        </div>
        <h6 class="Sub-Title">Inherent Risk Assessment</h6>
        <div class="col-6">

            <div class="form-group">
                <label class="form-label">Inherent Probability Rating</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-probability-ratings"></i></span>
                    <select class="form-select form-select-sm" id="LikliHood" asp-for="LikliHood" asp-items="@(new SelectList(ViewBag.RiskProbabilitymaster,"Id","ProbabilityName"))">
                        <option>Select Inherent Probability Rating</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Inherent Risk Value</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-risk-value"></i></span>
                    <input type="text" class="form-control" asp-for="RiskRating" name="RiskRating" id="RiskRating" readonly />
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">Impact</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-impact"></i></span>
                    <select class="form-select form-select-sm" asp-for="Impact" id="ImpactId" asp-items="@(new SelectList(ViewBag.AllRiskImpactmaster, "Id", "ImpactName"))">
                        <option>Select</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Inherent Risk Rating</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-risk-rating"></i></span>
                    <input type="text" class="form-control" id="ImpactRating" readonly />
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                <label class="form-label">Risk Treatment Option</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-risk-option"></i></span>
                    <select class="form-select form-select-sm" asp-for="MgmtDecision" name="MgmtDecision" id="MgmtDecision">
                        <option value="0">No Action</option>
                        <option value="1">Accept</option>
                        <option value="2">Avoid</option>
                        <option value="3">Mitigate</option>
                        <option value="4">Transfer</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="d-flex align-items-center gap-1 mb-2">
                <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="true" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                <h6 class="mb-0">Attach Recovery Plan</h6>
            </div>
            <div class="row collapse show" id="collapseExample" style="">
                <div class="col-6">
                    <div class="form-group">
                        <label class="form-label">Incident Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-incident"></i></span>
                            <input type="text" class="form-control" asp-for="IncidentDisplayName" name="IncidentDisplayName" id="IncidentDisplayName" />
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label class="form-label">Recovery Plan</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-recovery-plan"></i></span>
                            <select class="form-select form-select-sm" asp-for="PlanId" id="PlanId" asp-items="@(new SelectList(ViewBag.ApprovedRecoveryPlan, "ID", "PlanName"))">
                                <option>Select Recovery Plan</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex align-items-center gap-1 my-2">
                <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample1" aria-expanded="true" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                <h6 class="mb-0">Risk Review Section</h6>
            </div>
            <div class="row collapse" id="collapseExample1" style="">
                <div class="col-6">
                    <div class="form-group">
                        <label class="form-label">Last Review Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-calendar"></i></span>
                            <input type="date" class="form-control" id="LastReviewDate" asp-for="LastReviewDate" />
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label class="form-label">Next Review Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-calendar"></i></span>
                            <input type="date" class="form-control" id="NextReviewDate" asp-for="NextReviewDate" required/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <label id="successMessage" class="text-success">@ViewBag.SuccessMessage</label>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>


<script>

    $(document).ready(function () {
        debugger;

        var EntityId = $('#RiskItemCategory').val();

        $('#RiskItemCategory').change(function () {
            debugger;
            var iEntityId = $(this).val();

            // if (EntityId == 1) {  // process

            $.ajax({
                url: '@Url.Action("BindProcesses", "ManageRisk")',
                type: 'GET',
                data: { iEntityId: iEntityId },
                success: function (data) {
                    var BCMEntity = $('#ProcessCode');
                    BCMEntity.empty();
                    BCMEntity.append('<option value="0">-- All BCM Entity --</option>');
                    $.each(data, function (index, item) {
                        BCMEntity.append('<option value="' + item.processID + '">' + item.processName + '</option>')
                    });
                }
            })
        });

        $('#RiskCategory').change(function () {
            debugger;

            var strRiskCategory = $(this).find('option:selected').text();
            if (strRiskCategory != null) {
                $("#Vulnerability").val(strRiskCategory);
            }

            var iDisasterID = $(this).val();
            $.ajax({
                url: '@Url.Action("BindIncidentTypesByDisasterID", "ManageRisk")',
                type: 'GET',
                data: { iDisasterID: iDisasterID },
                success: function (data) {
                    var RiskType = $('#RiskType');
                    RiskType.empty();
                    RiskType.append('<option value="0">-- Select Threat --</option>');
                    $.each(data, function (index, item) {
                        RiskType.append('<option value="' + item.disasterTypeID + '">' + item.disasterTypeName + '</option>')
                    });
                }
            })
        });

        $('#LikliHood').change(function () {
            debugger;
            var ProbabilityID = $(this).val();
            var ImpactIDNew = $("#ImpactId").val();

            var Impact = ImpactIDNew == "Select" ? -1 : ImpactIDNew;

            $.ajax({
                url: '@Url.Action("CalculateRiskRating", "ManageRisk")',
                type: 'GET',
                data: { ProbabilityID: ProbabilityID, Impact: Impact },
                success: function (data) {
                    $("#RiskRating").val(data.riskRating);
                    $("#ImpactRating").val(data.impactRating);
                }
            })
        });

        $('#ImpactId').change(function () {
            debugger;
            var Impact = $(this).val();
            var ProbabilityIDNew = $("#LikliHood").val();

            var ProbabilityID = ProbabilityIDNew == "Select Inherent Probability Rating" ? -1 : ProbabilityIDNew;

            $.ajax({
                url: '@Url.Action("CalculateRiskRating", "ManageRisk")',
                type: 'GET',
                data: { ProbabilityID: ProbabilityID, Impact: Impact },
                success: function (data) {
                    $("#RiskRating").val(data.riskRating);
                    $("#ImpactRating").val(data.impactRating);
                }
            })
        });

        //send for approval
        $('#SendForApproval').click(function () {
            debugger;
            var iRiskId = $('#RiskItemCategory').val();
            $.ajax({
                url: '@Url.Action("SendForApproval", "ManageRisk")',
                type: 'GET',
                data: { iRiskId: iRiskId },
                success: function (data) {
                    console.log(data);
                }
            })
        });
    });
</script>