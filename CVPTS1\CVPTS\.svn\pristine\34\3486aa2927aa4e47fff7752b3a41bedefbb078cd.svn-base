﻿
@{
    ViewBag.Title = "ViewFacilityMap";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    #chartdiv {
        width: 100%;
        height:calc(100vh -  120px);
        overflow: hidden;
    }

    .critical-card{
        top:8%;
        right:1%;
    }
</style>

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Critical BCM Map</h6>
</div>
<div class="Page-Condant border-0 ">

        <div id="chartdiv"></div>
    
</div>


<div class="offcanvas offcanvas-end Map-Offcanvas" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasScrolling" aria-labelledby="offcanvasScrollingLabel">
    <div class="card card-design-none">
        <div class="card-header d-flex align-items-center justify-content-between">
            <h6 class="mb-0"><i class="cv-all-facilities me-1"></i>Facility Details</h6>
            <span role="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></span>
        </div>
        <div class="card-body">
            <h6 class="Sub-Title text-primary">Cerebrum IT Park</h6>
            <div class="my-2">
                <table class="table table-sm table-hover">
                    <thead>
                        <tr>
                            <th>Process Name</th>
                            <th>RTO</th>
                            <th>RPO</th>
                            <th>MAO</th>
                            <th>Critical</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="fw-semibold">Admin</td>
                            <td class="fw-semibold">1 Hrs</td>
                            <td class="fw-semibold">10 Mins</td>
                            <td class="fw-semibold">1 Hrs</td>
                            <td class="fw-semibold"><i class="cv-success text-success"></i></td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">FIFA 22</td>
                            <td class="fw-semibold">2 Hrs</td>
                            <td class="fw-semibold">20 Mins</td>
                            <td class="fw-semibold">2 Hrs</td>
                            <td class="fw-semibold"><i class="cv-success text-success"></i></td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">
                                Test License
                            </td>
                            <td class="fw-semibold">4 Hrs</td>
                            <td class="fw-semibold">20 Mins</td>
                            <td class="fw-semibold">2 Hrs</td>
                            <td class="fw-semibold"><i class="cv-error text-danger"></i></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <h6 class="Sub-Title">Risk Details</h6>
            <div class="my-2">
                <table class="table table-sm table-hover">
                    <thead>
                        <tr>
                            <th>Risk Name</th>
                            <th>Severity</th>
                            <th>Owner</th>

                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="fw-semibold">Risk 1</td>
                            <td class="fw-semibold"><span class="text-danger"><i class="me-1 cv-Critical"></i>Critical</span></td>
                            <td class="fw-semibold">Neeraj Sahu</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">Risk 2</td>
                            <td class="fw-semibold"><span class="text-warning"><i class="me-1 cv-low"></i>Low</span></td>
                            <td class="fw-semibold">Gordon Penney</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold">Risk 3</td>
                            <td class="fw-semibold"><span style="color:var(--bs-orange)"><i class="me-1 cv-medium"></i>Medium</span></td>
                            <td class="fw-semibold">Gordon Penney</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/map.js"></script>
<script src="~/lib/amcharts4/worldlow.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script>


    var chart = am4core.create("chartdiv", am4maps.MapChart);
    chart.logo.disabled = true;
    chart.homeZoomLevel = 3;
    chart.geodata = am4geodata_worldIndiaLow;
    chart.zoomControl = new am4maps.ZoomControl();
    chart.zoomControl.slider.height = 100;
    chart.projection = new am4maps.projections.Miller();


    chart.homeZoomLevel = 3;
    chart.homeGeoPoint = {
        latitude: 24,
        longitude: 80
    };

    var polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());
    polygonSeries.useGeodata = true;

    let polygonTemplate = polygonSeries.mapPolygons.template;


    polygonSeries.exclude = ["AQ"];
    polygonSeries.data = [{
        "id": "SA",
        //"name": "Saudi Arabia",
        //"value": 10000,
        "fill": am4core.color("#FFCC6A")
    }, {
        "id": "IN",
        //"name": "India",
        //"value": 500,
        "fill": am4core.color("#64CDCD")
    }];

    polygonTemplate.propertyFields.fill = "fill";


    var imageSeries = chart.series.push(new am4maps.MapImageSeries());
    var imageSeriesTemplate = imageSeries.mapImages.template;
    imageSeries.tooltip.label.interactionsEnabled = true;
    imageSeries.tooltip.keepTargetHover = true;



    function animateBullet(circle) {
        var animation = circle.animate([
            { property: "scale", from: 1 / chart.zoomLevel, to: 5 / chart.zoomLevel },
            { property: "opacity", from: 1, to: 0 }], 1000, am4core.ease.circleOut);
        animation.events.on("animationended", function (event) {
            animateBullet(event.target.object);
        })
    }

    var colorSet = new am4core.ColorSet();

    // Set property fields
    imageSeriesTemplate.propertyFields.latitude = "latitude";
    imageSeriesTemplate.propertyFields.longitude = "longitude";

    // Add data for the three cities
    imageSeries.data = [{
        "latitude": 24.6355,
        "longitude": 46.7102,
        "tag": "Cerebrum IT Park",
        "RTO": "2 Hrs",
        "RPO": "20 Mins",
        "MAO": "4 Hrs",
        "Critical": 000,
    }, {
        "latitude": 13.0827,
        "longitude": 80.2707,
        "tag": "Cerebrum IT Park",
        "RTO": "2 Hrs",
        "RPO": "20 Mins",
        "MAO": "4 Hrs",
        "Critical": 000,
    }];

    var circle = imageSeriesTemplate.createChild(am4core.Circle);

    imageSeriesTemplate.fill = am4core.color("#fff");
    imageSeriesTemplate.tooltipHTML = '<table class="Map-Table bg-white"><thead><tr><th class="text-primary" colspan="3">{tag}</th><th class="text-end"><i class="fs-6 btn btn-sm btn-primary cv-view" data-bs-toggle="offcanvas" data-bs-target="#offcanvasScrolling" aria-controls="offcanvasScrolling"></i></th></tr><tr><th>RTO</th><th>RPO</th><th>MAO</th><th class="fw-semibold">Critical</th></tr></thead><tbody><tr><td class="fw-semibold">{RTO}</td><td class="fw-semibold">{RPO}</td><td class="fw-semibold">{MAO}</td><td><i class="cv-success text-success fw-semibold"></i></td></tr></tbody></table>';


    circle.fill = am4core.color("#e63875");
    circle.radius = 5;
    circle.nonScaling = true;
    circle.align = "center";
    circle.valign = "middle";
    circle.name = "{RTO}";



    var circle2 = imageSeries.mapImages.template.createChild(am4core.Circle);
    circle2.fill = am4core.color("#e63875");
    circle2.radius = 5;
    circle2.name = "{RTO}";

    circle2.events.on("inited", function (event) {
        animateBullet(event.target);
    })






</script>