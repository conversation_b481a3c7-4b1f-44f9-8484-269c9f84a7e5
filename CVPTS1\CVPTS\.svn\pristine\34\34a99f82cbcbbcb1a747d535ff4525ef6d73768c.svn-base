﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using DevExpress.CodeParser;
using DevExpress.Web.Internal;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using static BCM.Shared.BCPEnum;

namespace BCM.UI.Areas.BCMCompliance.Controllers;
[Area("BCMCompliance")]
public class ReviewReportMasterFormController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public ReviewReportMasterFormController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult AddMomItem(int iReportID,int iReviewTypeID)
    {
        try
        {
            ViewBag.ReportID = iReportID;
            ViewBag.ReviewTypeID = iReviewTypeID;
            PopulateDropDown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddMomItem");
    }

    [HttpPost]
    public IActionResult AddMomItem(ReviewReportMomItem Obj, MomImprovements ObjImp)
    {
        bool bSuccess = false;
        try
        {
            int Success = 0;
            Obj.CreatedBy = Convert.ToString(_UserDetails.UserID);
            //Obj.OwnerID = Convert.ToString(_UserDetails.UserID);
            Obj.IsActionItem = Obj.IsActionItem.Equals("true") ? "1" : "0";
            Obj.IsClosed = Obj.IsClosed.Equals("true") ? "1" : "0";
            //Obj.IsImprovement = Obj.IsImprovement.Equals("true") ? "1" : "0";

            Success = _ProcessSrv.ReviewReportMapping_SaveUpdate(Obj, ObjImp);
            bSuccess = Success > 0 ? true : false;
            if (Success > 0)
            {
                //  ManagementReview();
            }
            else
            {
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? Obj.MomItem + " Added Successfully" : "Failed To Add Mom Item." });
        }
        return RedirectToAction("ManagementReview", "ManagementReview");
    }    

    public void PopulateDropDown(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {            
            ViewBag.ResourceList = _Utilities.GetAllResourceList();

            ViewBag.ReviewType = _ProcessSrv.GetReviewType(Convert.ToInt32(_UserDetails.OrgID));

            ViewBag.BCMEntities = new SelectList(_Utilities.PopulateBCMEntities(), "BCMEntityID", "BCMEntityName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    [HttpGet]
    public JsonResult BindEntity(int iEntityType)
    {
        try
        {
            List<SelectListItem> entities = new List<SelectListItem>();
            
            if (iEntityType > 0)
            {
                switch (iEntityType)
                {
                    case (int)BCPEnum.EntityType.BusinessProcess:
                        return Json(_Utilities.GetAllProcessListForDropdown(_UserDetails.OrgID)
                            .Select(x => new SelectListItem { Value = x.ProcessID.ToString(), Text = x.ProcessName }));

                    case (int)BCPEnum.EntityType.Location:
                        return Json(_Utilities.GetAllLocationListForDropDown(_UserDetails.OrgID)
                            .Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.LocationName }));

                    case (int)BCPEnum.EntityType.People:
                        return Json(_Utilities.GetAllPeopleListForDropDown(_UserDetails.OrgID)
                            .Select(x => new SelectListItem { Value = x.ResourceId.ToString(), Text = x.ResourceName }));

                    case (int)BCPEnum.EntityType.ThirdParty:
                        return Json(_Utilities.GetAllThirdPartyListForDropDown(_UserDetails.OrgID)
                            .Select(x => new SelectListItem { Value = x.CompanyID.ToString(), Text = x.CompanyName }));

                    case (int)BCPEnum.EntityType.Application:
                        return Json(_Utilities.GetAllApplicationListForDropDown(_UserDetails.OrgID)
                            .Select(x => new SelectListItem { Value = x.ApplicationId.ToString(), Text = x.ApplicationName }));

                    case (int)BCPEnum.EntityType.ITService:
                        return Json(new List<SelectListItem>()); // or implement if needed

                    default:
                        return Json(new List<SelectListItem>());
                }
            }
            return Json(new List<SelectListItem>());
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new List<SelectListItem>());
        }
    }

    [HttpGet]
    public IActionResult EditMomItem(int iMomID)
    {
        try
        {
            if (iMomID > 0)
            {
                ReviewReportMomItem momItem = _ProcessSrv.GetReportReviewMapping_ByID(iMomID.ToString());

                if (!string.IsNullOrEmpty(momItem.PlanStartDate) && DateTime.TryParse(momItem.PlanStartDate, out var planStart))
                {
                    momItem.PlanStartDate = planStart.ToString("yyyy-MM-dd");
                }
                if (!string.IsNullOrEmpty(momItem.PlanEndDate) && DateTime.TryParse(momItem.PlanEndDate, out var planEnd))
                {
                    momItem.PlanEndDate = planEnd.ToString("yyyy-MM-dd");
                }
                if (!string.IsNullOrEmpty(momItem.ActualStartDate) && DateTime.TryParse(momItem.ActualStartDate, out var actualStart))
                {
                    momItem.ActualStartDate = actualStart.ToString("yyyy-MM-dd");
                }
                if (!string.IsNullOrEmpty(momItem.ActualEndDate) && DateTime.TryParse(momItem.ActualEndDate, out var actualEnd))
                {
                    momItem.ActualEndDate = actualEnd.ToString("yyyy-MM-dd");
                }
                if (!string.IsNullOrEmpty(momItem.ReviewDate) && DateTime.TryParse(momItem.ReviewDate, out var reviewDate))
                {
                    momItem.ReviewDate = reviewDate.ToString("yyyy-MM-dd");
                }
                if (!string.IsNullOrEmpty(momItem.ClosedDate) && DateTime.TryParse(momItem.ClosedDate, out var closingDate))
                {
                    momItem.ClosedDate = closingDate.ToString("yyyy-MM-dd");
                }
                PopulateDropDown();
                return PartialView("_EditMomItem", momItem);
            }
            return RedirectToAction("ManagementReview", "ManagementReview");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return RedirectToAction("ManagementReview", "ManagementReview");
        }
    }

    [HttpPost]
    public IActionResult EditMomItem(ReviewReportMomItem Obj, MomImprovements ObjImp)
    {
        bool bSuccess = false;
        try
        {
            int Success = 0;
            Obj.CreatedBy = Convert.ToString(_UserDetails.UserID);
            //Obj.OwnerID = Convert.ToString(_UserDetails.UserID);

            Obj.IsActionItem = (Obj.IsActionItem != null && Obj.IsActionItem.Equals("on")) ? "1" : "0";
            Obj.IsClosed = (Obj.IsClosed != null && Obj.IsClosed.Equals("on")) ? "1" : "0";
            //Obj.IsImprovement = (Obj.IsImprovement != null && Obj.IsImprovement.Equals("on")) ? "1" : "0";

            Success = _ProcessSrv.ReviewReportMapping_SaveUpdate(Obj, ObjImp);
            bSuccess = Success > 0 ? true : false;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? Obj.MomItem + " Updated Successfully" : "Failed To Update Mom Item." });
        }
        return RedirectToAction("ManagementReview", "ManagementReview");
    }
}

