﻿using BCM.UI.Areas.BCMReports.Controllers;
using BCM.UI.Areas.BCMReports.Models.ServiceCriticalityReportModel;
using DevExpress.Utils.Filtering;
using DevExpress.XtraCharts;
using DevExpress.XtraReports.UI;
using Newtonsoft.Json;
using System.ComponentModel;
using System.Drawing;

namespace BCM.UI.Areas.BCMReports.ReportTemplate;

public partial class ServiceCriticalityReport : DevExpress.XtraReports.UI.XtraReport
{
    public List<ServiceCriticalityReportVm> _data;
    private int sNo = 1;
    private int currentRowIndex = 0;
    private int totalRows = 0;
    public ServiceCriticalityReport(string data)
    {
        try
        {


            _data = JsonConvert.DeserializeObject<List<ServiceCriticalityReportVm>>(data) ?? new List<ServiceCriticalityReportVm>();
            InitializeComponent();
            this.DataSource = _data.First().ServiceCriticalityReportData;

            serialNumber.BeforePrint += tableCell_SerialNumber_BeforePrint;
            ///Border
            this.Detail.BeforePrint += Detail_BeforePrint;

            var total = _data.First().ServiceCriticalityReportData.Count;
            totalRows = total;
            CvUserName.Text = _data.First().LoginName ?? "Unknown User";

            var groupedData = _data.First().ServiceCriticalityReportData
    .GroupBy(x => x.ServiceCriticallyValue)
    .ToDictionary(g => g.Key, g => g.Count());

            var roundedPercentages = GetAdjustedPercentage(groupedData);


            xrChart1.Series.Clear();

            Series series = new Series("Criticality", ViewType.Doughnut);

            // Add data points
            if (roundedPercentages.TryGetValue("High", out int high))
                series.Points.Add(new SeriesPoint("High", high) { Color = Color.Red });
            xrLabel10.Text = high.ToString() + "%";
            if (roundedPercentages.TryGetValue("Medium", out int medium))
                series.Points.Add(new SeriesPoint("Medium", medium) { Color = Color.Goldenrod });
            xrLabel11.Text = medium.ToString() + "%";
            if (roundedPercentages.TryGetValue("Low", out int low))
                series.Points.Add(new SeriesPoint("Low", low) { Color = Color.Green });
            xrLabel16.Text = low.ToString() + "%";
            // ✅ Modify existing label safely
            DoughnutSeriesLabel label = series.Label as DoughnutSeriesLabel;
            if (label != null)
            {
                label.TextPattern = "{A}: {V}%";
                label.Position = PieSeriesLabelPosition.Outside;
                label.BackColor = Color.Transparent;
                label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                // label.Font = new Font("Arial", 10, FontStyle.Bold);
                label.TextColor = Color.Empty;
                label.LineColor = Color.Empty;
            }

            // Set view
            DoughnutSeriesView view = new DoughnutSeriesView
            {
                MinAllowedSizePercentage = 80D,
                HoleRadiusPercent = 85
            };
            series.View = view;

            // Add series to chart
            xrChart1.Series.Add(series);
            xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            this.DisplayName = "ServiceCriticalityReport_" + DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss tt");
        }
        catch (Exception ex)
        {
            // Handle exceptions as needed
            throw new Exception("Error initializing ServiceCriticalityReport: " + ex.Message, ex);
        }

    }
    private Dictionary<string, int> GetAdjustedPercentage(Dictionary<string, int> groupedData)
    {
        int total = groupedData.Values.Sum();

        var rawPercentages = groupedData.ToDictionary(
            kvp => kvp.Key,
            kvp => (double)kvp.Value * 100 / total
        );

        var roundedPercentages = rawPercentages.ToDictionary(
            kvp => kvp.Key,
            kvp => (int)Math.Floor(kvp.Value)
        );

        int remaining = 100 - roundedPercentages.Values.Sum();

        if (remaining > 0)
        {
            var minKey = groupedData.OrderBy(kvp => kvp.Value).First().Key;
            roundedPercentages[minKey] += remaining;
        }

        return roundedPercentages;
    }

    private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
    {
        XRTableCell cell = (XRTableCell)sender;

        cell.Text = sNo.ToString();
        sNo++;
    }
    private void Detail_BeforePrint(object sender, CancelEventArgs e)
    {
        currentRowIndex++;
        bool isLastRow = currentRowIndex == totalRows;

        var borderColor = Color.FromArgb(242, 242, 242);

        // Serial number cell: left + bottom (if last row)
        serialNumber.Borders = (isLastRow ? DevExpress.XtraPrinting.BorderSide.Bottom : 0) | DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right;
        serialNumber.BorderWidth = 1;
        serialNumber.BorderColor = borderColor;
        serialNumber.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;

        //low,high,medium
        xrTableCell4.Borders = (isLastRow ? DevExpress.XtraPrinting.BorderSide.Bottom : 0) | DevExpress.XtraPrinting.BorderSide.Left;
        xrTableCell4.BorderWidth = 1;
        xrTableCell4.BorderColor = borderColor;
        xrTableCell4.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
        // Cell3: right + bottom (if last row)
        xrTableCell3.Borders = (isLastRow ? DevExpress.XtraPrinting.BorderSide.Bottom : 0) | DevExpress.XtraPrinting.BorderSide.Right;
        xrTableCell3.BorderWidth = 1;
        xrTableCell3.BorderColor = borderColor;
        xrTableCell3.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;

        // Apply bottom only to other cells
        foreach (var cell in new[] { xrTableCell1, xrTableCell6 })
        {
            cell.Borders = isLastRow ? DevExpress.XtraPrinting.BorderSide.Bottom : DevExpress.XtraPrinting.BorderSide.None;
            cell.BorderWidth = 1;
            cell.BorderColor = borderColor;
            cell.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
        }
    }


}







