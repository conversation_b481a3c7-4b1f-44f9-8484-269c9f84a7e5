﻿@{
    ViewData["Title"] = "User Mapping";
    Layout = "~/Views/Shared/_Layout.cshtml";
}



<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">User Mapping</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal">
            <i class="cv-Plus" title="Create New"></i>Create
        </button>
    </div>
</div>

<div class="Page-Condant card border-0 ">
    <table id="example" class="table table-hover" style="width:100%">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Dashboard</th>
                <th>User Name</th>
                <th>Role</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="log-table-body">
        </tbody>
    </table>

</div>



<div class="modal fade" id="AddModal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">User Mapping Configuration</h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-1">
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">User Mapping Type</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-user"></i></span>
                                <input type="text" class="form-control" placeholder="Enter User Mapping Type" name="database" value="">
                            </div>
                        </div>
                    </div>

                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Map by Role</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-workflow-dashline"></i></span>
                                <select class="form-select form-control selectized">
                                    <option></option>
                                    <option>Map by Role 1</option>
                                    <option>Map by Role 2</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Choose Dashboard Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-calculated"></i></span>
                                <select class="form-select form-control selectized">
                                    <option></option>
                                    <option>Dashboard 1</option>
                                    <option>Dashboard 2</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="inlineCheckbox1" value="option1">
                            <label class="form-check-label" for="inlineCheckbox1">Set as Default</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="inlineCheckbox2" value="option2">
                            <label class="form-check-label" for="inlineCheckbox2">View</label>
                        </div>
                    </div>

                </div>

            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary">Cancel</button>
                <button class="btn btn-primary">Save</button>
            </div>
        </div>
       
    </div>
</div>


<script>
    document.addEventListener("DOMContentLoaded", function () {
      const tbody = document.getElementById("log-table-body");

      for (let i = 1; i <= 10; i++) {
        const tr = document.createElement("tr");

        // Generate a random profile image
        const imgUrl = `https://randomuser.me/api/portraits/men/${i + 30}.jpg`;

        tr.innerHTML = `
          <td>${i}</td>
          <td>Dashboard ${i}</td>
          <td>
            <div class="d-flex align-items-center gap-2">
              <img src="${imgUrl}" alt="User" width="30" height="30" class="rounded-circle" style="object-fit: cover;">
              <span>User ${i}</span>
            </div>
          </td>
          <td>Role ${i}</td>
          <td>
           <div class="d-flex align-items-center gap-2">
              <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="#Modal" data-id="${i}">
                <i class="cv-edit" title="Edit"></i>
              </span>
              <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#Modal" data-id="${i}">
                <i class="cv-delete text-danger" title="Delete"></i>
              </span>
            </div>
          </td>
        `;

        tbody.appendChild(tr);
      }
    });
</script>

