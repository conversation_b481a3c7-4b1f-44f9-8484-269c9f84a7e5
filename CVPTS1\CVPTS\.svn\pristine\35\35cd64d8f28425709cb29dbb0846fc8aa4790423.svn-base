﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model BCM.BusinessClasses.AddCalendarActivity
@using BCM.BusinessClasses
@using BCM.Shared
@{
    List<ResourcesInfo> lstResourcelist = ViewBag.ResourceList ?? new List<ResourcesInfo>();
    var selectedOrgID = ViewBag.selectedOrgID ?? 0; 
    var userID = ViewBag.userID;
}
<style>
    .disabled-btn {
        pointer-events: none;
        opacity: 0.5;
        cursor: not-allowed;
    }
</style>

<div class="row">
    <div class="col-6">
        <div class="form-group">
            <label class="form-label">Activity Name</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-activity-name"></i></span>
                <input type="hidden" id="txtActivityID" asp-for="Id" />
                <input type="text" class="form-control" id="txtActivityName" placeholder="Enter Activity Name" asp-for="ActivityName"/>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Unit</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-unit"></i></span>
                <select class="form-select form-select-sm" id="ddlUnitID" asp-for="UnitID">
                    <option value="0" selected>-- All Units --</option>
                    @foreach (var unit in ViewBag.UnitList)
                    {
                        <option value="@unit.Value">@unit.Text</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Activity Type</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-activity-type"></i></span>
                <select class="form-select form-select-sm" id="ddlActivityType" asp-for="ActivityType">
                    <option value="0" selected>-- Activity Type --</option>
                    @{
                        foreach (var activityType in ViewBag.ActivityType)
                        {
                                                            <option value="@activityType.Value">@activityType.Text</option>
                        }
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Start Date</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-calendar"></i></span>
                <input type="date" class="form-control" id="txtStartDate" asp-for="ScheduledStartDate"/>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Organizer</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-organizer"></i></span>
                <select class="form-select form-select-sm" id="ddlOrganizer" asp-for="Responsibility">
                    <option value="value">-- All Organizer --</option>
                    @{
                        foreach (var organizer in ViewBag.Organizer)
                        {
                                                            <option value="@organizer.Value">@organizer.Text</option>
                        }
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Activity Details</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-activity-details" title="View Details"></i></span>
                <textarea class="form-control" placeholder="Activity Details" style="height:0px" id="txtActivityDetail" asp-for="ActivityDetails"></textarea>
            </div>
        </div>
    </div>
    <div class="col-6">
        <div class="form-group">
            <label class="form-label">Org Level</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-organization"></i></span>
                <select class="form-select form-select-sm" id="ddlOrgID" asp-for="OrgID">
                    <option value="0" selected>--  All Organization --</option>
                    @foreach (var organization in ViewBag.OrgInfo)
                    {
                        <!option value="@organization.Value" @(organization.Value == selectedOrgID.ToString() ? "selected=\"selected\"" : "")>@organization.Text</!option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Department</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-department"></i></span>
                <input type="hidden" asp-for="DepartmentID" id="deptID"/>
                <select class="form-select form-select-sm" id="ddlDeptID" asp-for="DepartmentID">
                    <option value="0" selected>-- All Departments --</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Status</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-status"></i></span>
                <select class="form-select form-select-sm" id="ddlStatus" asp-for="Status">
                    <option value="-1" selected>-- Calendar Status --</option>
                    @foreach (var status in ViewBag.CalendarStatus)
                    {
                        <option value="@status.Value">@status.Text</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">End Date</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-calendar"></i></span>
                <input type="date" class="form-control" id="txtEndDate" asp-for="ScheduledENDDate" />
            </div>
        </div>

        <div class="form-group w-100">
            <label class="form-label">File</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-upload"></i></span>
                <input type="file" class="form-control" asp-for="File" id="txtAttachment" />
            </div>
        </div>
        <div class="form-group w-100" id="attachmentDiv" style="display:none;">
            <label class="form-label">File Name</label>
            <input type="hidden" value="@((int)BCPEnum.EntityType.BCMCalender)" id="txtEntityID"/>
            <input type="hidden" id="txtAttachmentID" asp-for="AttachmentID"/>
            <input type="button" class="form-control" asp-for="AttachmentName" id="txtFileName" readonly title="Click to download file ..."/>
         </div>
    </div>
    <div class="col-12">
        <div class="mb-2">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="" id="chkActivity" @(Model.IsCal == 1 ? "checked" : "")>
                <label class="form-check-label" for="flexCheckDefault">
                    Email Activity as Calendar
                </label>
            </div>
        </div>
    </div>
    <div class="mb-2">
        <div class="d-flex align-items-center justify-content-between gap-3 mb-2">
            <div class="d-flex align-items-center gap-3">
                <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                    <i class="cv-Plus align-middle"></i>
                </span>
                <h6 class="mb-0">Notify Users (as FYA)</h6>
            </div>
            <div class="input-group Search-Input" style="width:400px !important;">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input data-search="usersFYADiv" type="text" class="form-control searchId" placeholder="Search">
            </div>
        </div>
        <div class="collapse" id="collapseExample">
            <div class="g-2 row row-cols-xl-4 row-cols-4 p-3" style="overflow-y: auto;height: 150px;" id="usersFYADiv">
                @await Html.PartialAsync("~/Areas/BCMTeams/Views/BCMGroupsNotification/_FilteredFYAUsers.cshtml", lstResourcelist)
            </div>
        </div>
    </div>
    <div class="mb-2">
        <div class="d-flex align-items-center justify-content-between gap-3 mb-2">
            <div class="d-flex align-items-center gap-3">
                <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#collapseUsersFYI" aria-expanded="false" aria-controls="collapseExample">
                    <i class="cv-Plus align-middle"></i>
                </span>
                <h6 class="mb-0">Notify Users (as FYI)</h6>
            </div>
            <div class="input-group Search-Input" style="width:400px !important;">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input data-search="usersFYIDiv" type="text" class="form-control searchId" placeholder="Search">
            </div>
        </div>
        <div class="collapse" id="collapseUsersFYI">
            <div class="g-2 row row-cols-xl-4 row-cols-4 p-3" style="overflow-y: auto;height: 150px;" id="usersFYIDiv">
                @await Html.PartialAsync("~/Areas/BCMTeams/Views/BCMGroupsNotification/_FilteredFYIUsers.cshtml", lstResourcelist)
            </div>
        </div>
    </div>
    <div class="mb-2 mt-3">
        <div class="d-flex align-items-center gap-3 mb-2">
            <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#collapseTeamsFYA" aria-expanded="false" aria-controls="collapseExample">
                <i class="cv-Plus align-middle"></i>
            </span>
            <h6 class="mb-0">
                Notify Teams (as FYA)
            </h6>
        </div>
        <div class="g-2 row row-cols-xl-4 row-cols-4 collapse p-3" id="collapseTeamsFYA">
            @{
                List<BCMCalenderDetailss> lstTeamsFYA = new List<BCMCalenderDetailss>();
                if (ViewBag.chkTeamFYA != null)
                {
                    lstTeamsFYA = ViewBag.chkTeamFYA;
                }
                foreach (var BCMTeamsAndResources in ViewBag.BCMGroupList)
                {
                    var checkedTeamsFYA = lstTeamsFYA.Any(x => x.TeamId == BCMTeamsAndResources.GroupID);
                    <div class="col">
                        <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
                            <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                                <span>
                                    <input class="form-check" type="checkbox" id="<EMAIL>"
                                           value="@BCMTeamsAndResources.GroupID" @(checkedTeamsFYA ? "checked" : "") name="teamsFYA" />
                                </span>
                                <span class="">@BCMTeamsAndResources.GroupName</span>
                            </span>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
    <div class="mb-2 mt-3">
        <div class="d-flex align-items-center gap-3 mb-2">
            <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#collapseTeamsFYI" aria-expanded="false" aria-controls="collapseExample">
                <i class="cv-Plus align-middle"></i>
            </span>
            <h6 class="mb-0">
                Notify Teams (as FYI)
            </h6>
        </div>
        <div class="g-2 row row-cols-xl-4 row-cols-4 collapse p-3" id="collapseTeamsFYI">            
            @{
                List<BCMCalenderDetailss> lstTeamsFYI = new List<BCMCalenderDetailss>();
                if (ViewBag.chkTeamFYI != null)
                {
                    lstTeamsFYI = ViewBag.chkTeamFYI;
                }
                foreach (var BCMTeamsAndResources in ViewBag.BCMGroupList)
                {
                    var checkedTeamsFYI = lstTeamsFYI.Any(x => x.TeamId == BCMTeamsAndResources.GroupID);
                            <div class="col">
                                <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
                                    <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                                        <span>
                                    <input class="form-check" type="checkbox" id="<EMAIL>"
                                           value="@BCMTeamsAndResources.GroupID" @(checkedTeamsFYI ? "checked" : "") name="teamsFYI" />
                                        </span>
                                        <span class="">@BCMTeamsAndResources.GroupName</span>
                                    </span>
                                </div>
                            </div>
                }
            }

        </div>
    </div>    
</div>
<div class="row">
    <div class="col-5">
        <div id="successMessage" class="alert alert-success d-none"></div>
        <div id="errorMessage" class="alert alert-danger d-none"></div>
    </div>
    <div class="col-7">
        <div class="modal-footer d-flex justify-content-between">
            <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
            <div>
                @{
                    var isDisabled = userID.ToString() != Model.Responsibility;
                }
                <button type="submit" class="btn btn-primary btn-sm @(isDisabled ? "disabled-btn" : "")" " id="btnNotify">Notify</button>
                <button type="submit" class="btn btn-primary btn-sm" id="btnSaveActivity">Save BCM Activity</button>
                <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>



<script>
    $(document).ready(function () {


        if ($('#txtActivityID').val() > 0) {
            $("#btnSaveActivity").html("Update BCM Activity");
            $('#btnNotify').show();
            var unitID = $('#ddlUnitID').val();
            if (unitID > 0) {
                BindFunction(unitID);
            }
            BindAttachmentName();
        } 
        else {
            $('#btnNotify').hide();
        }

        function BindAttachmentName() {
            var fileName = $('#txtFileName').val();
            if (fileName) {
                $('#attachmentDiv').show();
            }
        }

        $('#ddlUnitID').on('change', function (e) {
            e.preventDefault();
            BindFunction($(this).val());
        })

        getFYACheckboxesStates();

        getFYICheckboxesStates();

        document.addEventListener('change', function (event) {
            if (event.target.matches('input.fyaUser')) {
                updateFYACheckboxesStates(event.target);
            }
            else if (event.target.matches('input.fyiUser')) {
                updateFYICheckboxesStates(event.target);
            }
        });

        $(document).on('change', '.searchId', function () {
            const strSearchId = $(this).data('search');
            searchUsers(strSearchId, $(this).val());
        })

        $(document).on('click', '#txtFileName', function (e) {
            e.preventDefault();
            let attachmentID = $('#txtAttachmentID').val();
            if (!attachmentID) {
                alert("Attachment ID is missing.");
                return;
            }
            let downloadUrl = `/BCMDocuments/DownloadAttachment/DownloadBCMCAlendarAttachment?iAttachmentID=${attachmentID}`;
            // Create a hidden anchor tag and trigger download
            let link = document.createElement("a");
            link.href = downloadUrl;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });


        $(document).on('click', '#btnSaveActivity', function (e) {
            e.preventDefault();
            var url = '@Url.Action("BCMCalenderConfiguration", "BCMCalenderConfiguration", new { area = "BCMCalendar" })';
            SaveDetailsOrSendNotification(url);
        });

        $(document).on('click', '#btnNotify', function (e) {
            e.preventDefault();
            var url = '@Url.Action("SendNotificationsClick", "BCMCalenderConfiguration", new { area = "BCMCalendar" })';
            SaveDetailsOrSendNotification(url);
        });

        function displayFileName() {
            var fileInput = document.getElementById('txtAttachment');
            var fileName = fileInput.files[0] ? fileInput.files[0].name : '';
            document.getElementById('fileNameLabel').textContent = fileName;
        }
    })

    function SaveDetailsOrSendNotification(url) {
        var chkResponse = document.getElementById('chkActivity');

        var usersFYA = $('#usersFYADiv input[name="usersFYA"]:checked').map(function () {
            return parseInt($(this).val(), 10);
        }).get();

        var usersFYI = $('#usersFYIDiv input[name="usersFYI"]:checked').map(function () {
            return parseInt($(this).val(), 10);
        }).get();

        var teamsFYA = $('input[name="teamsFYA"]:checked').map(function () {
            return parseInt($(this).val(), 10);
        }).get();

        var teamsFYI = $('input[name="teamsFYI"]:checked').map(function () {
            return parseInt($(this).val(), 10);
        }).get();


        var iChkResponse = chkResponse.checked ? 1 : 0;

        var formData = new FormData();

        @* var fileInput = document.getElementById('txtAttachment');
        if (fileInput == null) {
            fileInput = document.getElementById('txtFileName');
        }
        if (fileInput.files.length > 0) {
            formData.append('File', fileInput.files[0]);
        } *@

        var fileInput = document.getElementById('txtAttachment');

        if (fileInput && fileInput.files.length > 0) {
            formData.append('File', fileInput.files[0]);
            fileName = fileInput.files[0].name;
        } else {
            var fileNameInput = document.getElementById('txtFileName');
            if (fileNameInput) {
                formData.append('AttachmentName', fileNameInput.value);
            }
        }

        usersFYA.forEach(function (user) {
            formData.append('UsersFYA', user);
        });
        usersFYI.forEach(function (user) {
            formData.append('UsersFYI', user);
        });
        teamsFYA.forEach(function (team) {
            formData.append('TeamsFYA', team);
        });
        teamsFYI.forEach(function (team) {
            formData.append('TeamsFYI', team);
        });
        formData.append('IsCal', iChkResponse);
        formData.append('OrgID', $('#ddlOrgID').val());
        formData.append('UnitID', $('#ddlUnitID').val());
        formData.append('DepartmentID', $('#ddlDeptID').val());
        formData.append('Id', $('#txtActivityID').val());
        formData.append('ActivityName', $('#txtActivityName').val().trim());
        formData.append('ActivityType', $('#ddlActivityType').val());
        formData.append('ScheduledStartDate', $('#txtStartDate').val());
        formData.append('ScheduledENDDate', $('#txtEndDate').val());
        formData.append('Responsibility', $('#ddlOrganizer').val());
        formData.append('ActivityDetails', $('#txtActivityDetail').val().trim());
        formData.append('Status', $('#ddlStatus').val());

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function (response) {
                if (response.success) {
                    $('#errorMessage').addClass('d-none'); // Hide the error message
                    $('#successMessage').text(response.message).removeClass('d-none');
                    $(':input[type="submit"]').prop('disabled', true);
                } else {
                    $('#successMessage').addClass('d-none'); // Hide the success message
                    $('#errorMessage').text(response.message).removeClass('d-none');
                }
            },
            error: function (xhr, status, error) {
                $('#successMessage').addClass('d-none'); // Hide the success message
                $('#errorMessage').text("An error occurred. Please try again.").removeClass('d-none');
                console.error("Error details:", xhr.responseText);
                console.log(error);
                console.error(xhr.status);
                console.error(xhr.responseText);
            }

        });
    }

    function BindFunction(unitID) {
        $.ajax({
            type: 'POST',
            url: '@Url.Action("BindFunction", "ViewThirdParties", new { area = "BCMViewInfo" })',
            data: {
                iUnitID: unitID
            },
            success: function (data) {
                var department = $('#ddlDeptID');
                department.empty();
                department.append('<option value="0" selected>-- All Departments --</option>');
                $.each(data, function (index, item) {
                    department.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                });
                var ddlSelect = $('#deptID').val();
                if (ddlSelect > 0) {
                    $('#ddlDeptID').val(ddlSelect);
                }
            },
            error: function (xhr, status, error) {
                console.log(error);
                console.error(xhr.status);
                console.error(xhr.responseText);
            }
        })
    }

    function searchUsers(strSearchId, strUserName) {
        $.ajax({
            url: '@Url.Action("SearchUsers", "BCMGroupsNotification", new { area = "BCMTeams" })',
            type: 'GET',
            data: {
                strUserName: strUserName,
                strSearchId: strSearchId
            },
            success: function (response) {
                $('#' + strSearchId).html(response);
                if (strSearchId == 'usersFYADiv') {
                    getFYACheckboxesStates();
                } else if (strSearchId == 'usersFYIDiv') {
                    getFYICheckboxesStates();
                }
            },
            error: function (xhr, status, error) {
                console.log(error);
                console.error(xhr.status);
                console.error(xhr.responseText);
            }
        });
    }

    function updateFYACheckboxesStates(fyaCheckbox) {
        let fyaCheckboxesState = JSON.parse(localStorage.getItem('fyaCheckboxesState')) || {};
        fyaCheckboxesState[fyaCheckbox.value] = fyaCheckbox.checked;
        localStorage.setItem('fyaCheckboxesState', JSON.stringify(fyaCheckboxesState));
    }

    function updateFYICheckboxesStates(fyiCheckbox) {
        let fyiCheckboxesState = JSON.parse(localStorage.getItem('fyiCheckboxesState')) || {};
        fyiCheckboxesState[fyiCheckbox.value] = fyiCheckbox.checked;
        localStorage.setItem('fyiCheckboxesState', JSON.stringify(fyiCheckboxesState));
    }

    function getFYACheckboxesStates() {
        let fyaCheckboxesStates = JSON.parse(localStorage.getItem('fyaCheckboxesState')) || {};
        document.querySelectorAll('input.fyaUser').forEach(function (fyaChkbox) {
            if (fyaCheckboxesStates.hasOwnProperty(fyaChkbox.value)) {
                fyaChkbox.checked = fyaCheckboxesStates[fyaChkbox.value];
            }
        });
    }

    function getFYICheckboxesStates() {
        let fyiCheckboxesStates = JSON.parse(localStorage.getItem('fyiCheckboxesState')) || {};
        document.querySelectorAll('input.fyiUser').forEach(function (fyiChkbox) {
            if (fyiCheckboxesStates.hasOwnProperty(fyiChkbox.value)) {
                fyiChkbox.checked = fyiCheckboxesStates[fyiChkbox.value];
            }
        });
    }    
</script>