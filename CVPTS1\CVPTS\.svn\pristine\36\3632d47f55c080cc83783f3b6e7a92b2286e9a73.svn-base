﻿@model BCM.BusinessClasses.ManageBIAProfileSectionMain
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="wizard-content">
    <form id="" class="tab-wizard wizard-circle wizard clearfix example-form">
        <h6>
            <span class="step"><i class="cv-edit"></i></span>
            <span class="step_title">Create BIA Profile</span>
        </h6>
        <section>
            <div class="row row-cols-2">
                <div class="col-12">
                    <blockquote class="blockquote">Create BIA Profile</blockquote>
                </div>
                <div class="col">
                    <div class="form-group" hidden>
                        <div class="input-group">
                            <input type="hidden" class="form-control" asp-for="ID" id="ID">
                            <input type="hidden" class="form-control" asp-for="Status" id="Status">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">BIA Profile Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-profile"></i></span>
                            <input type="text" class="form-control" id="ProfileName" asp-for="ProfileName" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Select Approver</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-approver"></i></span>
                            <select class="form-select form-control" id="ApproverID" asp-for="ApproverID" autocomplete="off" aria-label="Default select example">
                                <option selected value="0">-- Select Approver--</option>
                                @foreach (var objResource in ViewBag.ResourcesInfo)
                                {
                                    <option value="@objResource.Value">@objResource.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label class="form-label">Organization</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <select class="form-select form-control" id="OrgID" asp-for="OrgID" autocomplete="off" aria-label="Default select example">
                                <option selected value="0">-- All Organizations --</option>
                                @foreach (var objOrg in ViewBag.orgInfo)
                                {
                                    <option value="@objOrg.Value">@objOrg.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Select Owner</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-owner"></i></span>
                            <select class="form-select form-control" id="OwnerID" asp-for="OwnerID" autocomplete="off" aria-label="Default select example">
                                <option selected value="0">-- Select Owner --</option>
                                @foreach (var objResource in ViewBag.ResourcesInfo)
                                {
                                    <option value="@objResource.Value">@objResource.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
            </div>


            <div id="createbiaprofileButtons" style="display: none; margin-top: 10px;">
                <button type="button" id="btnFinishcreatebiaprofile" class="btn icon-btn btn-primary btn-sm">Save</button>
                <button type="button" id="btnCancelcreatebiaprofile" class="btn btn-secondary btn-sm ml-2">Cancel</button>
            </div>
        </section>
        <h6>
            <span class="step"><i class="cv-user"></i></span>
            <span class="step_title">BIA Section</span>
        </h6>
        <section>
            <div class="row">
                <div class="col-12">
                    <blockquote class="blockquote">BIA Section</blockquote>
                </div>
            </div>

            <label class="form-label">Select Section</label>
            <div class="g-2 row row-cols-xl-2 row-cols-2 p-3 collapse show" id="collapseExample" style="">
                @foreach (var objBIASection in ViewBag.BIASection)
                {
                    var objIsChecked = ViewBag.ArrBIASection.Contains(objBIASection.SectionID);
                    <div class="col">
                        <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
                            <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                                <span>
                                    @* <input class="form-check" type="checkbox" id="SectionID" name="SectionID">     @* id="@objBIASection.Value" *@

                                    <input class="form-check-input" name="SectionID" type="checkbox" value="@objBIASection.SectionID" id="@objBIASection.SectionID" @(objIsChecked ? "checked" : "")>
                                </span>
                                <span class="">@objBIASection.SectionName</span>
                            </span>
                        </div>
                    </div>
                }
            </div>

            <!-- BIA Section Edit Buttons -->

            <div id="sectionButtons" style="display: none; margin-top: 10px;">
                <button type="button" id="btnFinishSection" class="btn icon-btn btn-primary btn-sm">Save</button>
                <button type="button" id="btnCancelSection" class="btn btn-secondary btn-sm ml-2">Cancel</button>
            </div>
        </section>
        <h6>
            <span class="step"><i class="cv-info"></i></span>
            <span class="step_title">Time Interval/BIA Impact</span>
        </h6>
        <section>
            <div class="row">
                <div class="col-12">
                    <blockquote class="blockquote">Time Interval/BIA Impact</blockquote>
                </div>
            </div>
            <div>
                <label class="form-label">Select Time Interval</label>
                <div class="g-2 row row-cols-xl-3 row-cols-3 p-3 collapse show" id="collapseExample" style="">
                    @foreach (var objTimeInterval in ViewBag.TimeInterval)
                    {
                        var objIsChecked = ViewBag.ArrTimeInterval.Contains(objTimeInterval.ID);
                        <div class="col">
                            <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
                                <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                                    <span>
                                        @* <input class="form-check" type="checkbox" id="@objTimeInterval.Value" name="TimeIntervalID"> *@
                                        <input class="form-check-input" name="TimeIntervalID" type="checkbox" value="@objTimeInterval.ID" id="@objTimeInterval.ID" @(objIsChecked ? "checked" : "")>

                                    </span>
                                    <span class="">@objTimeInterval.TimeIntervalText</span>
                                </span>
                            </div>
                        </div>
                    }
                </div>
            </div>
            <div>
                <label class="form-label">
                    Select Impact
                </label>
                <div class="p-3">
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col">
                                    <input type="checkbox" class="form-check" />
                                </th>
                                <th scope="col">
                                    Impact Type
                                </th>
                                <th scope="col">
                                    Impact Category
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var objImpactCategory in ViewBag.ImpactCategory)
                            {
                                var objIsChecked = ViewBag.ArrImpactID.Contains(objImpactCategory.ImpactID);
                                <tr>
                                    <td>
                                        <input class="form-check-input" type="checkbox" name="ImpactDetails" data-impactId="@objImpactCategory.ImpactID" data-impactTypeId="@objImpactCategory.ImpactTypeID" @(objIsChecked ? "checked" : "")>
                                    </td>

                                    @* <td><input class="form-check chkImpactID" name="ImpactID" type="checkbox" value="@objImpactCategory.ImpactID" id="chkImpactID"></td> *@
                                    <td>@objImpactCategory.ImpactName</td>

                                    @* <td><input class="form-check chkImpactTypeID" name="ImpactTypeID" type="checkbox" value="@objImpactCategory.ImpactTypeID" id="chkImpactTypeID"></td>  *@
                                    <td>@objImpactCategory.ImpactTypeName</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Time Interval/Impacts Edit Buttons -->

            <div id="timeIntervalButtons" style="display: none; margin-top: 10px;">
                <button type="button" id="btnFinishTimeInterval" class="btn icon-btn btn-primary btn-sm">Save</button>
                <button type="button" id="btnCancelTimeInterval" class="btn btn-secondary btn-sm ml-2">Cancel</button>
            </div>
        </section>
        <h6>
            <span class="step"><i class="cv-description"></i></span>
            <span class="step_title">BIA Profile Summary</span>
        </h6>
        <section>
            <div class="row">
                <div class="col-12">
                    <blockquote class="blockquote">BIA Profile Summary</blockquote>
                </div>
            </div>
            <div class="d-flex justify-content-between gap-5 mb-2 p-3">
                <div class="w-100">
                    <div>
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <td style="width: 35%;">
                                        <i class="cv-organization me-1"></i>Profile Name
                                    </td>
                                    <td>:</td>
                                    <td class="text-primary">
                                        <input type="text" class="form-control" id="ProfileName1" asp-for="ProfileName" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-approver me-1"></i>Approver
                                    </td>
                                    <td>:</td>
                                    <td class="text-primary">
                                        <input type="text" class="form-control" id="ApproverName" asp-for="ApproverName" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="vr"></div>
                <div class="w-100">
                    <div>
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <td>
                                        <i class="cv-organization me-1"></i>Organization
                                    </td>
                                    <td>:</td>
                                    <td class="text-primary">
                                        <input type="text" class="form-control" id="OrgName" asp-for="OrgName" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cv-owner me-1"></i>Owner
                                    </td>
                                    <td>:</td>
                                    <td class="text-primary">
                                        <input type="text" class="form-control" id="OwnerName" asp-for="OwnerName" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <button type="button" id="btnCreateBIaProfile" class="btn icon-btn btn-primary btn-sm">Edit</button>
            <div class="Page-Condant card border-0">
                <table id="biasections" class="table table-hover" style="width:100%;vertical-align:middle">
                    <thead>
                        <tr>
                            <th>BIA Section</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @*    <span class="btn-action btnEdit" type="button"><i class="cv-edit" title="Edit"></i></span> *@
                                </div>
                            </td>
                        </tr>
                    </tbody>

                </table>
            </div>
            <button type="button" id="btnEditSection" class="btn icon-btn btn-primary btn-sm">Edit</button>

            <div class="Page-Condant card border-0">
                <table id="timeintervals" class="table table-hover align-middle" style="width:100%;vertical-align:middle">
                    <thead>
                        <tr>
                            <th>Impact</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @*    <span class="btn-action btnEdit" type="button"><i class="cv-edit" title="Edit"></i></span> *@
                                </div>
                            </td>
                        </tr>
                    </tbody>

                </table>
            </div>

            <button type="button" id="btnEditTimeInterval" class="btn icon-btn btn-primary btn-sm">Edit</button>
            <button type="button" id="btnViewAllProfile" class="btn icon-btn btn-primary btn-sm" style="margin-left: 85%;">View All Profile</button>

            @*   <div class="Page-Condant card border-0">
                <table id="impacts" class="table table-hover" style="width:100%;vertical-align:middle">
                    <thead>
                        <tr>
                            <th>Impact</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @*     <span class="btn-action btnEdit" type="button"><i class="cv-edit" title="Edit"></i></span>
                                </div>
                            </td>
                        </tr>
                    </tbody>

                </table>
            </div>
            <button type="button" id="btnEditImpact" class="btn icon-btn btn-primary btn-sm">Edit</button> *@
        </section>      
    </form>
</div>

<script src="~/lib/jquery/jquery.min.js"></script>
<script src="~/lib/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.min.js"></script>
<script src="~/lib/datatables/js/jquery.datatables.min.js"></script>
<script src="~/lib/datatables/js/datatables.bootstrap5.min.js"></script>
<script src="~/lib/selectize/selectize.min.js"></script>
<script src="~/js/managerecoveryplan.js"></script>
<script src="~/js/password_toggle.js"></script>

<script>
    $(document).ready(function () {
        // Store all available items when the page loads (following _PageAccess.cshtml pattern)
        var allBIASections = [];
        var allTimeIntervals = [];
        var allImpacts = [];
        var allImpactCategories = [];

        // Store names mapping for summary display
        var biaSectionNames = {};
        var timeIntervalNames = {};
        var impactNames = {};
        var updateTimeout; // For debouncing table updates

        // Collect all BIA sections with their names
        $('input[name="SectionID"]').each(function() {
            var sectionId = parseInt($(this).val());
            var sectionName = $(this).closest('.border').find('span:last').text().trim();
            allBIASections.push(sectionId);
            biaSectionNames[sectionId] = sectionName;
        });
        console.log("All BIA Sections:", allBIASections);
        console.log("BIA Section Names:", biaSectionNames);

        // Collect all time intervals with their names
        $('input[name="TimeIntervalID"]').each(function() {
            var intervalId = parseInt($(this).val());
            var intervalName = $(this).closest('.border').find('span:last').text().trim();
            allTimeIntervals.push(intervalId);
            timeIntervalNames[intervalId] = intervalName;
        });
        console.log("All Time Intervals:", allTimeIntervals);
        console.log("Time Interval Names:", timeIntervalNames);

        // Collect all impacts with their names
        $('input[name="ImpactDetails"]').each(function() {
            var impactId = parseInt($(this).attr('data-impactId'));
            var impactTypeId = parseInt($(this).attr('data-impactTypeId'));
            var $row = $(this).closest('tr');
            var impactName = $row.find('td:nth-child(2)').text().trim(); // Impact Type column
            var impactCategory = $row.find('td:nth-child(3)').text().trim(); // Impact Category column
            var combinedName = impactName + ' - ' + impactCategory;

            if (!allImpacts.includes(impactId)) {
                allImpacts.push(impactId);
                impactNames[impactId] = combinedName;
            }
            if (!allImpactCategories.includes(impactTypeId)) {
                allImpactCategories.push(impactTypeId);
            }
        });
        console.log("All Impacts:", allImpacts);
        console.log("All Impact Categories:", allImpactCategories);
        console.log("Impact Names:", impactNames);

        // Function to update BIA sections table in summary
        function updateBIASectionsTable() {
            var checkedSections = [];
            var uniqueSectionNames = new Set(); // Use Set to avoid duplicates

            $('input[name="SectionID"]:checked').each(function() {
                var sectionId = parseInt($(this).val());
                var sectionName = biaSectionNames[sectionId];
                if (sectionName && !uniqueSectionNames.has(sectionName)) {
                    uniqueSectionNames.add(sectionName);
                    checkedSections.push(sectionName);
                }
            });

            // Update the biasections table - completely rebuild it
            var tableBody = $('#biasections tbody');
            tableBody.html(''); // Use html('') instead of empty() for complete clearing

            if (checkedSections.length > 0) {
                var tableContent = '';
                checkedSections.forEach(function(sectionName) {
                    tableContent += '<tr><td>' + sectionName + '</td></tr>';
                });
                tableBody.html(tableContent);
            } else {
                tableBody.html('<tr><td><div class="d-flex align-items-center">No BIA sections selected</div></td></tr>');
            }
        }

        // Function to update Time Intervals table in summary (matrix design with Time Intervals as columns and Impacts as rows)
        function updateTimeIntervalsTable() {
            // Collect checked Time Intervals for columns (only selected ones)
            var selectedTimeIntervals = [];
            var uniqueIntervalIds = new Set();
            $('input[name="TimeIntervalID"]:checked').each(function() {
                var intervalId = parseInt($(this).val());
                var intervalName = timeIntervalNames[intervalId];
                if (intervalName && !uniqueIntervalIds.has(intervalId)) {
                    uniqueIntervalIds.add(intervalId);
                    selectedTimeIntervals.push({
                        id: intervalId,
                        name: intervalName
                    });
                }
            });

            // Collect checked Impacts for rows (prevent duplicates)
            var impactsByType = {};
            var uniqueImpactIds = new Set();
            $('input[name="ImpactDetails"]:checked').each(function() {
                var impactId = parseInt($(this).attr('data-impactId'));
                var impactTypeId = parseInt($(this).attr('data-impactTypeId'));

                // Skip if we've already processed this impact
                if (uniqueImpactIds.has(impactId)) {
                    return;
                }
                uniqueImpactIds.add(impactId);

                var $row = $(this).closest('tr');
                var impactName = $row.find('td:nth-child(2)').text().trim();
                var impactTypeName = $row.find('td:nth-child(3)').text().trim();

                if (!impactsByType[impactTypeId]) {
                    impactsByType[impactTypeId] = {
                        typeName: impactTypeName,
                        impacts: []
                    };
                }

                impactsByType[impactTypeId].impacts.push({
                    id: impactId,
                    name: impactName
                });
            });

            // Debug logging
            console.log("Selected Time Intervals:", selectedTimeIntervals);
            console.log("Impacts by Type:", impactsByType);
            console.log("Unique Impact IDs processed:", Array.from(uniqueImpactIds));

            // Update the timeintervals table - completely rebuild it
            var table = $('#timeintervals');
            var tableHead = table.find('thead');
            var tableBody = table.find('tbody');

            // Build header with ONLY selected Time Intervals as columns
            var headerContent = '<tr><th>Impact</th>';
            if (selectedTimeIntervals.length > 0) {
                selectedTimeIntervals.forEach(function(interval) {
                    headerContent += '<th>' + interval.name + '</th>';
                });
            }
            headerContent += '</tr>';
            tableHead.html(headerContent);

            // Build body with ONLY selected Impacts as rows
            var bodyContent = '';
            var hasContent = false;

            if (Object.keys(impactsByType).length > 0 && selectedTimeIntervals.length > 0) {
                hasContent = true;
                var mainRowCount = 0;

                Object.keys(impactsByType).forEach(function(impactTypeId) {
                    var impactType = impactsByType[impactTypeId];
                    mainRowCount++;

                    // Impact Type header row
                    bodyContent += '<tr>';
                    bodyContent += '<td colspan="' + (selectedTimeIntervals.length + 1) + '">';
                    bodyContent += '<p class="text-primary d-flex align-items-center gap-1 mb-0">';
                    bodyContent += '<span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" ';
                    bodyContent += 'data-bs-toggle="collapse" data-bs-target="#collapsequestion_' + mainRowCount + '" aria-expanded="false">';
                    bodyContent += '<i class="cv-minus align-middle"></i></span>';
                    bodyContent += 'Impact Type: ' + impactType.typeName;
                    bodyContent += '</p>';
                    bodyContent += '</td>';
                    bodyContent += '</tr>';

                    // Impact rows under this type (ensure no duplicates within type)
                    var processedImpactNames = new Set();
                    impactType.impacts.forEach(function(impact) {
                        // Skip if we've already processed this impact name within this type
                        if (processedImpactNames.has(impact.name)) {
                            return;
                        }
                        processedImpactNames.add(impact.name);

                        bodyContent += '<tr class="collapse show" id="collapsequestion_' + mainRowCount + '">';
                        bodyContent += '<td>' + impact.name + '</td>';

                        selectedTimeIntervals.forEach(function(interval) {
                            bodyContent += '<td>';
                            bodyContent += '<div class="form-group">';
                            bodyContent += '<div class="input-group">';
                            bodyContent += '<select class="form-select-sm form-control dropdown" disabled ';
                            bodyContent += 'data-impact-id="' + impact.id + '" data-interval-id="' + interval.id + '">';
                            bodyContent += '<option value="0">-- Select --</option>';
                            bodyContent += '<option value="1">Low</option>';
                            bodyContent += '<option value="2">Medium</option>';
                            bodyContent += '<option value="3">High</option>';
                            bodyContent += '<option value="4">Critical</option>';
                            bodyContent += '</select>';
                            bodyContent += '</div>';
                            bodyContent += '</div>';
                            bodyContent += '</td>';
                        });

                        bodyContent += '</tr>';
                    });
                });
            }

            if (!hasContent) {
                // Reset header to simple format when no content
                tableHead.html('<tr><th>Time Interval / Impact</th></tr>');
                bodyContent = '<tr><td><div class="d-flex align-items-center">No time intervals and impacts selected for matrix</div></td></tr>';
            }

            tableBody.html(bodyContent);
        }



        // Debounced versions to prevent multiple rapid updates
        function debouncedUpdateBIASectionsTable() {
            clearTimeout(updateTimeout);
            updateTimeout = setTimeout(function() {
                updateBIASectionsTable();
            }, 100); // 100ms delay
        }

        function debouncedUpdateTimeIntervalsTable() {
            clearTimeout(updateTimeout);
            updateTimeout = setTimeout(function() {
                updateTimeIntervalsTable();
            }, 100); // 100ms delay
        }

        // Combined debounced update for all tables (now only BIA sections and combined time intervals/impacts)
        function debouncedUpdateAllTables() {
            clearTimeout(updateTimeout);
            updateTimeout = setTimeout(function() {
                updateBIASectionsTable();
                updateTimeIntervalsTable();
            }, 100); // 100ms delay
        }

        // Implement check/uncheck logic similar to _PageAccess.cshtml
        // Track checkbox changes for BIA Sections - use off() to prevent multiple handlers
        $('input[name="SectionID"]').off('change.biasection').on('change.biasection', function() {
            var isChecked = $(this).is(':checked');
            var sectionId = $(this).val();
            console.log("BIA Section checkbox changed:", sectionId, "Checked:", isChecked);

            // Visual feedback for user
            if (isChecked) {
                $(this).closest('.border').addClass('border-primary');
            } else {
                $(this).closest('.border').removeClass('border-primary');
            }

            // Update the BIA sections table in summary using debounced version
            debouncedUpdateBIASectionsTable();
        });

        // Track checkbox changes for Time Intervals - use off() to prevent multiple handlers
        $('input[name="TimeIntervalID"]').off('change.timeinterval').on('change.timeinterval', function() {
            var isChecked = $(this).is(':checked');
            var intervalId = $(this).val();
            console.log("Time Interval checkbox changed:", intervalId, "Checked:", isChecked);

            // Visual feedback for user
            if (isChecked) {
                $(this).closest('.border').addClass('border-success');
            } else {
                $(this).closest('.border').removeClass('border-success');
            }

            // Update the Time Intervals table in summary
            debouncedUpdateTimeIntervalsTable();
        });

        // Track checkbox changes for Impacts - use off() to prevent multiple handlers
        $('input[name="ImpactDetails"]').off('change.impact').on('change.impact', function() {
            var isChecked = $(this).is(':checked');
            var impactId = $(this).attr('data-impactId');
            console.log("Impact checkbox changed:", impactId, "Checked:", isChecked);

            // Visual feedback for user
            if (isChecked) {
                $(this).closest('tr').addClass('table-active');
            } else {
                $(this).closest('tr').removeClass('table-active');
            }

            // Update the Time Intervals table (which now includes impacts) in summary
            debouncedUpdateTimeIntervalsTable();
        });

        // Add "Select All" functionality for impacts (following _PageAccess.cshtml pattern)
        $('thead input[type="checkbox"]').off('change.selectall').on('change.selectall', function() {
            var isChecked = $(this).is(':checked');
            $('input[name="ImpactDetails"]').prop('checked', isChecked).trigger('change');
            console.log("Select All Impacts:", isChecked);

            // Update the Time Intervals table (which now includes impacts) in summary after select all
            debouncedUpdateTimeIntervalsTable();
        });

        $('a').click(function (arrayOfValues) {
            var href = $(this).attr('href');

            // When clicking on the BIA Profile Summary tab
            if (href == "#-h-3") {
                // Get values from the first tab
                var profileName = $('#ProfileName').val();
                var approverText = $('#ApproverID option:selected').text();
                var orgText = $('#OrgID option:selected').text();
                var ownerText = $('#OwnerID option:selected').text();

                // Set values in the summary tab
                $('#ProfileName1').val(profileName);
                $('#ApproverName').val(approverText === '-- Select Approver--' ? '' : approverText);
                $('#OrgName').val(orgText === '-- All Organizations --' ? '' : orgText);
                $('#OwnerName').val(ownerText === '-- Select Owner --' ? '' : ownerText);

                // Force refresh the summary tables with current form data
                setTimeout(function() {
                    refreshSummaryData();
                    console.log("BIA Profile Summary tab refreshed with latest data");
                }, 100);
            }

            // When clicking on the Risk Review Section tab
            if (href == "#-h-4") {
                // Get values from the first tab
                var profileName = $('#ProfileName').val();
                var approverText = $('#ApproverID option:selected').text();
                var orgText = $('#OrgID option:selected').text();
                var ownerText = $('#OwnerID option:selected').text();

                // Set values in the last tab
                $('#ProfileName1').val(profileName);
                $('#ApproverName').val(approverText === '-- Select Approver--' ? '' : approverText);
                $('#OrgName').val(orgText === '-- All Organizations --' ? '' : orgText);
                $('#OwnerName').val(ownerText === '-- Select Owner --' ? '' : ownerText);

                // Update all tables in summary using debounced version
                debouncedUpdateAllTables();
            }

            // When clicking Finish button
            if (href == "#finish") {
                // Initialize arrays for ALL items with their checked status
                let AllBIASections = [];
                let AllTimeIntervals = [];
                let AllImpacts = [];
                let AllImpactCategories = [];

                // Collect ALL BIA Sections with their checked status
                $('input[name="SectionID"]').each(function () {
                    var sectionId = parseInt($(this).val());
                    var isChecked = $(this).is(':checked');

                    if (!AllBIASections.some(item => item.id === sectionId)) {
                        AllBIASections.push({
                            id: sectionId,
                            checked: isChecked
                        });
                    }
                });

                // Collect ALL Time Intervals with their checked status
                $('input[name="TimeIntervalID"]').each(function () {
                    var timeIntervalId = parseInt($(this).val());
                    var isChecked = $(this).is(':checked');

                    if (!AllTimeIntervals.some(item => item.id === timeIntervalId)) {
                        AllTimeIntervals.push({
                            id: timeIntervalId,
                            checked: isChecked
                        });
                    }
                });

                // Collect ALL Impacts with their checked status
                $('input[name="ImpactDetails"]').each(function () {
                    var impactId = parseInt(this.getAttribute('data-impactId'));
                    var impactTypeId = parseInt(this.getAttribute('data-impactTypeId'));
                    var isChecked = $(this).is(':checked');

                    if (!AllImpacts.some(item => item.id === impactId)) {
                        AllImpacts.push({
                            id: impactId,
                            checked: isChecked
                        });
                    }

                    if (!AllImpactCategories.some(item => item.id === impactTypeId)) {
                        AllImpactCategories.push({
                            id: impactTypeId,
                            checked: isChecked
                        });
                    }
                });

                console.log("All BIA Sections with status:", AllBIASections);
                console.log("All Time Intervals with status:", AllTimeIntervals);
                console.log("All Impacts with status:", AllImpacts);
                console.log("All Impact Categories with status:", AllImpactCategories);

                // Get values from the first tab for the last tab
                var profileName = $('#ProfileName').val();
                var approverText = $('#ApproverID option:selected').text();
                var orgText = $('#OrgID option:selected').text();
                var ownerText = $('#OwnerID option:selected').text();

                // Set values in the last tab
                $('#ProfileName1').val(profileName);
                $('#ApproverName').val(approverText === '-- Select Approver--' ? '' : approverText);
                $('#OrgName').val(orgText === '-- All Organizations --' ? '' : orgText);
                $('#OwnerName').val(ownerText === '-- Select Owner --' ? '' : ownerText);

                // Convert string values to integers where needed
                var id = parseInt($('#ID').val()) || 0;
                var approverID = parseInt($('#ApproverID').val()) || 0;
                var orgID = parseInt($('#OrgID').val()) || 0;
                var ownerID = parseInt($('#OwnerID').val()) || 0;

                var objdata = {
                    ID: id,
                    ApproverID: approverID,
                    OrgID: orgID,
                    OwnerID: ownerID,
                    Status: $('#Status').val(),
                    AllBIASectionsWithStatus: AllBIASections,
                    AllTimeIntervalsWithStatus: AllTimeIntervals,
                    AllImpactsWithStatus: AllImpacts,
                    AllImpactCategoriesWithStatus: AllImpactCategories,
                    ProfileName: $('#ProfileName').val(),
                    ApproverName: $('#ApproverName').val(),
                    OrgName: $('#OrgName').val(),
                    OwnerName: $('#OwnerName').val()
                }

                console.log("Data to submit:", JSON.stringify(objdata));

                // Critical debugging - log the exact state of checkboxes
                console.log("=== DEBUGGING CHECKBOX STATES ===");
                console.log("Total BIA Section checkboxes:", $('input[name="SectionID"]').length);
                console.log("Checked BIA Section checkboxes:", $('input[name="SectionID"]:checked').length);
                console.log("Total Time Interval checkboxes:", $('input[name="TimeIntervalID"]').length);
                console.log("Checked Time Interval checkboxes:", $('input[name="TimeIntervalID"]:checked').length);
                console.log("Total Impact checkboxes:", $('input[name="ImpactDetails"]').length);
                console.log("Checked Impact checkboxes:", $('input[name="ImpactDetails"]:checked').length);

                // Log specific unchecked items for debugging
                var uncheckedSections = [];
                $('input[name="SectionID"]:not(:checked)').each(function() {
                    uncheckedSections.push(parseInt($(this).val()));
                });
                console.log("Unchecked BIA Sections:", uncheckedSections);

                var uncheckedTimeIntervals = [];
                $('input[name="TimeIntervalID"]:not(:checked)').each(function() {
                    uncheckedTimeIntervals.push(parseInt($(this).val()));
                });
                console.log("Unchecked Time Intervals:", uncheckedTimeIntervals);

                var uncheckedImpacts = [];
                $('input[name="ImpactDetails"]:not(:checked)').each(function() {
                    uncheckedImpacts.push(parseInt($(this).attr('data-impactId')));
                });
                console.log("Unchecked Impacts:", uncheckedImpacts);
                console.log("=== END DEBUGGING ===");

                // Additional validation before sending (following _PageAccess.cshtml pattern)
                if (!objdata.ProfileName || objdata.ProfileName.trim() === '') {
                    alert("Please enter a Profile Name.");
                    return false;
                }

                if (objdata.ApproverID === 0) {
                    alert("Please select an Approver.");
                    return false;
                }

                if (objdata.OwnerID === 0) {
                    alert("Please select an Owner.");
                    return false;
                }

                // Show loading indicator
                console.log("Submitting BIA Profile update...");

                $.ajax({
                    type: "POST",
                    url: '/BCMBIAProfile/ManageBIAProfile/EditBIAProfile',
                    data: JSON.stringify(objdata),
                    contentType: 'application/json',
                    beforeSend: function() {
                        // Disable the finish button to prevent double submission
                        $('a[href="#finish"]').addClass('disabled').text('Updating...');
                    },
                    success: function (response) {
                        console.log("Success response:", response);
                        alert("BIA Profile updated successfully!");

                        // Show Edit buttons
                        showEditButtons();

                        // Navigate to BIA Profile Summary tab and update tables
                        navigateToLastTabOnly();
                        setTimeout(function() {
                            refreshSummaryData();
                        }, 100);

                        // Re-enable the finish button
                        $('a[href="#finish"]').removeClass('disabled').text('Finish');

                        console.log("Successfully saved and staying on BIA Profile Summary tab");
                    },
                    error: function (xhr, status, error) {
                        console.log('Error occurred during save:', xhr.responseText);
                        console.log('Status:', status);
                        console.log('Error:', error);
                        alert("An error occurred while saving. Please check the console for details and try again.");

                        // Re-enable the finish button
                        $('a[href="#finish"]').removeClass('disabled').text('Finish');
                    }
                });
            }
        });

        // Also update Risk Review Section when first tab fields change
        $('#ProfileName, #ApproverID, #OrgID, #OwnerID').on('change', function() {
            var profileName = $('#ProfileName').val();
            var approverText = $('#ApproverID option:selected').text();
            var orgText = $('#OrgID option:selected').text();
            var ownerText = $('#OwnerID option:selected').text();

            $('#ProfileName1').val(profileName);
            $('#ApproverName').val(approverText === '-- Select Approver--' ? '' : approverText);
            $('#OrgName').val(orgText === '-- All Organizations --' ? '' : orgText);
            $('#OwnerName').val(ownerText === '-- Select Owner --' ? '' : ownerText);
        });

        // Initial update of Risk Review Section
        var profileName = $('#ProfileName').val();
        var approverText = $('#ApproverID option:selected').text();
        var orgText = $('#OrgID option:selected').text();
        var ownerText = $('#OwnerID option:selected').text();

        $('#ProfileName1').val(profileName);
        $('#ApproverName').val(approverText === '-- Select Approver--' ? '' : approverText);
        $('#OrgName').val(orgText === '-- All Organizations --' ? '' : orgText);
        $('#OwnerName').val(ownerText === '-- Select Owner --' ? '' : ownerText);

        // Handle collapse/expand functionality for impact matrix
        $(document).on('click', '.toggle-password', function() {
            var $icon = $(this).find('i');
            if ($(this).hasClass('collapsed')) {
                $icon.removeClass('cv-minus').addClass('cv-plus');
            } else {
                $icon.removeClass('cv-plus').addClass('cv-minus');
            }
        });

        // Handle dropdown changes in the matrix (prevent changes for readonly dropdowns)
        $(document).on('change', '.dropdown', function() {
            var impactId = $(this).data('impact-id');
            var intervalId = $(this).data('interval-id');
            var selectedValue = $(this).val();
            console.log('Matrix dropdown changed - Impact:', impactId, 'Interval:', intervalId, 'Value:', selectedValue);

            // Prevent changes if dropdown is disabled
            if ($(this).prop('disabled')) {
                $(this).val($(this).data('original-value') || '0');
                return false;
            }

            // Here you can add logic to save the selection if needed
        });

        // Prevent click and focus events on disabled dropdowns
        $(document).on('click focus', '.dropdown:disabled', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).blur();
            return false;
        });

        // Handle Edit Section button click
        $(document).on('click', '#btnEditSection', function() {
            // Temporarily enable BIA Section tab for navigation
            enableBIASectionTabTemporarily();

            // Navigate to BIA Section tab
            navigateToBIASectionTab();

            // Show Finish and Cancel buttons for BIA Section in the BIA Section tab
            $('#sectionButtons').show();
            $(this).hide(); // Hide the Edit button

            // Enable editing for BIA Section tab
            $('input[name="SectionID"]').prop('disabled', false);

            // Ensure Save button is enabled and ready for use
            $('#btnFinishSection').removeClass('disabled').text('Save');

            console.log("BIA Section editing enabled and navigated to BIA Section tab");
        });

        // Function to temporarily enable BIA Section tab for editing
        function enableBIASectionTabTemporarily() {
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                var $steps = $wizard.find('.steps li');

                // Enable the BIA Section tab (index 1)
                $steps.eq(1).removeClass('disabled');
                $steps.eq(1).find('a').removeClass('disabled');
                $steps.eq(1).css('pointer-events', 'auto');
                $steps.eq(1).css('opacity', '1');

                console.log("BIA Section tab temporarily enabled for editing");
            }

            // Also enable the direct navigation link for BIA Section
            $('a[href="#-h-1"]').css('pointer-events', 'auto').removeClass('disabled');
        }

        // Function to navigate to BIA Section tab
        function navigateToBIASectionTab() {
            // Find the wizard and navigate to the BIA Section tab (step 1)
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                try {
                    // Navigate to step 1 (BIA Section tab - 0-based index)
                    $wizard.steps('setStep', 1);
                } catch (e) {
                    // Fallback method if jQuery Steps API is not available
                    $wizard.find('.steps li').removeClass('current done');
                    $wizard.find('.steps li:eq(1)').addClass('current done');
                    $wizard.find('.content .body').hide();
                    $wizard.find('.content .body:eq(1)').show();
                }

                console.log("Navigated to BIA Section Tab (step 1)");
            } else {
                // Alternative method for non-wizard tabs - navigate to BIA Section tab
                $('a[href="#-h-1"]').trigger('click');
                console.log("Navigated to BIA Section tab using direct link");
            }
        }

        // Function to navigate to BIA Section tab
        function navigateToBIASectionTab() {
            // Find the wizard and navigate to the BIA Section tab (step 1)
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                try {
                    // Navigate to step 1 (BIA Section tab - 0-based index)
                    $wizard.steps('setStep', 1);
                } catch (e) {
                    // Fallback method if jQuery Steps API is not available
                    $wizard.find('.steps li').removeClass('current done');
                    $wizard.find('.steps li:eq(1)').addClass('current done');
                    $wizard.find('.content .body').hide();
                    $wizard.find('.content .body:eq(1)').show();
                }

                console.log("Navigated to BIA Section Tab (step 1)");
            } else {
                // Alternative method for non-wizard tabs - navigate to BIA Section tab
                $('a[href="#-h-1"]').trigger('click');
                console.log("Navigated to BIA Section tab using direct link");
            }
        }

        // Handle Edit Time Interval button click
        $(document).on('click', '#btnEditTimeInterval', function() {
            // Temporarily enable Time Interval/BIA Impact tab for navigation
            enableTimeIntervalTabTemporarily();

            // Navigate to Time Interval/BIA Impact tab
            navigateToTimeIntervalTab();

            // Show Finish and Cancel buttons for Time Interval/Impacts in the Time Interval tab
            $('#timeIntervalButtons').show();
            $(this).hide(); // Hide the Edit button

            // Enable editing for Time Interval and Impact tabs
            $('input[name="TimeIntervalID"]').prop('disabled', false);
            $('input[name="ImpactDetails"]').prop('disabled', false);

            // Ensure Save button is enabled and ready for use
            $('#btnFinishTimeInterval').removeClass('disabled').text('Save');

            console.log("Time Interval/Impacts editing enabled and navigated to Time Interval/BIA Impact tab");
        });

        // Function to temporarily enable Time Interval/BIA Impact tab for editing
        function enableTimeIntervalTabTemporarily() {
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                var $steps = $wizard.find('.steps li');

                // Enable the Time Interval/BIA Impact tab (index 2)
                $steps.eq(2).removeClass('disabled');
                $steps.eq(2).find('a').removeClass('disabled');
                $steps.eq(2).css('pointer-events', 'auto');
                $steps.eq(2).css('opacity', '1');

                console.log("Time Interval/BIA Impact tab temporarily enabled for editing");
            }

            // Also enable the direct navigation link for Time Interval/BIA Impact
            $('a[href="#-h-2"]').css('pointer-events', 'auto').removeClass('disabled');
        }

        // Function to navigate to Time Interval/BIA Impact tab
        function navigateToTimeIntervalTab() {
            // Find the wizard and navigate to the Time Interval/BIA Impact tab (step 2)
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                try {
                    // Navigate to step 2 (Time Interval/BIA Impact tab - 0-based index)
                    $wizard.steps('setStep', 2);
                } catch (e) {
                    // Fallback method if jQuery Steps API is not available
                    $wizard.find('.steps li').removeClass('current done');
                    $wizard.find('.steps li:eq(2)').addClass('current done');
                    $wizard.find('.content .body').hide();
                    $wizard.find('.content .body:eq(2)').show();
                }

                console.log("Navigated to Time Interval/BIA Impact Tab (step 2)");
            } else {
                // Alternative method for non-wizard tabs - navigate to Time Interval tab
                $('a[href="#-h-2"]').trigger('click');
                console.log("Navigated to Time Interval/BIA Impact tab using direct link");
            }
        }

        // Function to navigate to Time Interval/Impacts tab
        function navigateToTimeIntervalTab() {
            // Find the wizard and navigate to the Time Interval/Impacts tab (step 2)
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                try {
                    // Navigate to step 2 (Time Interval/Impacts tab - 0-based index)
                    $wizard.steps('setStep', 2);
                } catch (e) {
                    // Fallback method if jQuery Steps API is not available
                    $wizard.find('.steps li').removeClass('current done');
                    $wizard.find('.steps li:eq(2)').addClass('current done');
                    $wizard.find('.content .body').hide();
                    $wizard.find('.content .body:eq(2)').show();
                }

                console.log("Navigated to Time Interval/Impacts Tab (step 2)");
            } else {
                // Alternative method for non-wizard tabs - navigate to Time Interval tab
                $('a[href="#-h-2"]').trigger('click');
                console.log("Navigated to Time Interval/Impacts tab using direct link");
            }
        }

        // Extract the original finish logic into a separate function
        function executeFinishLogic() {
            // Initialize arrays for ALL items with their checked status
            let AllBIASections = [];
            let AllTimeIntervals = [];
            let AllImpacts = [];
            let AllImpactCategories = [];

            // Collect ALL BIA Sections with their checked status
            $('input[name="SectionID"]').each(function () {
                var sectionId = parseInt($(this).val());
                var isChecked = $(this).is(':checked');

                if (!AllBIASections.some(item => item.id === sectionId)) {
                    AllBIASections.push({
                        id: sectionId,
                        checked: isChecked
                    });
                }
            });

            // Collect ALL Time Intervals with their checked status
            $('input[name="TimeIntervalID"]').each(function () {
                var timeIntervalId = parseInt($(this).val());
                var isChecked = $(this).is(':checked');

                if (!AllTimeIntervals.some(item => item.id === timeIntervalId)) {
                    AllTimeIntervals.push({
                        id: timeIntervalId,
                        checked: isChecked
                    });
                }
            });

            // Collect ALL Impacts with their checked status
            $('input[name="ImpactDetails"]').each(function () {
                var impactId = parseInt(this.getAttribute('data-impactId'));
                var impactTypeId = parseInt(this.getAttribute('data-impactTypeId'));
                var isChecked = $(this).is(':checked');

                if (!AllImpacts.some(item => item.id === impactId)) {
                    AllImpacts.push({
                        id: impactId,
                        checked: isChecked
                    });
                }

                if (!AllImpactCategories.some(item => item.id === impactTypeId)) {
                    AllImpactCategories.push({
                        id: impactTypeId,
                        checked: isChecked
                    });
                }
            });

            // Get values from the first tab
            var profileName = $('#ProfileName').val();
            var approverText = $('#ApproverID option:selected').text();
            var orgText = $('#OrgID option:selected').text();
            var ownerText = $('#OwnerID option:selected').text();

            // Set values in the last tab
            $('#ProfileName1').val(profileName);
            $('#ApproverName').val(approverText === '-- Select Approver--' ? '' : approverText);
            $('#OrgName').val(orgText === '-- All Organizations --' ? '' : orgText);
            $('#OwnerName').val(ownerText === '-- Select Owner --' ? '' : ownerText);

            // Convert string values to integers where needed
            var id = parseInt($('#ID').val()) || 0;
            var approverID = parseInt($('#ApproverID').val()) || 0;
            var orgID = parseInt($('#OrgID').val()) || 0;
            var ownerID = parseInt($('#OwnerID').val()) || 0;

            var objdata = {
                ID: id,
                ApproverID: approverID,
                OrgID: orgID,
                OwnerID: ownerID,
                Status: $('#Status').val(),
                AllBIASectionsWithStatus: AllBIASections,
                AllTimeIntervalsWithStatus: AllTimeIntervals,
                AllImpactsWithStatus: AllImpacts,
                AllImpactCategoriesWithStatus: AllImpactCategories,
                ProfileName: $('#ProfileName').val(),
                ApproverName: $('#ApproverName').val(),
                OrgName: $('#OrgName').val(),
                OwnerName: $('#OwnerName').val()
            }

            // Validation before sending
            if (!objdata.ProfileName || objdata.ProfileName.trim() === '') {
                alert("Please enter a Profile Name.");
                return false;
            }

            if (objdata.ApproverID === 0) {
                alert("Please select an Approver.");
                return false;
            }

            if (objdata.OwnerID === 0) {
                alert("Please select an Owner.");
                return false;
            }

            // Submit the data
            $.ajax({
                type: "POST",
                url: '/BCMBIAProfile/ManageBIAProfile/EditBIAProfile',
                data: JSON.stringify(objdata),
                contentType: 'application/json',
                beforeSend: function() {
                    $('#btnFinishSection, #btnFinishTimeInterval, #btnFinishcreatebiaprofile').addClass('disabled').text('Updating...');
                },
                success: function (response) {
                    console.log("Success response:", response);
                    alert("BIA Profile updated successfully!");

                    // Hide Save/Cancel buttons and show Edit buttons
                    $('#sectionButtons, #timeIntervalButtons, #createbiaprofileButtons').hide();
                    showEditButtons();

                    // Re-enable form fields (keep them enabled for future edits)
                    $('input[name="SectionID"], input[name="TimeIntervalID"], input[name="ImpactDetails"]').prop('disabled', false);

                    // Re-enable Save buttons for future edits
                    $('#btnFinishSection, #btnFinishTimeInterval, #btnFinishcreatebiaprofile').removeClass('disabled').text('Save');

                    // Navigate back to BIA Profile Summary tab
                    navigateToLastTabOnly();

                    // Force refresh summary data and update tables
                    setTimeout(function() {
                        refreshSummaryData();
                        console.log("Summary tables updated with latest data after save");
                    }, 200);

                    console.log("Successfully saved and returned to BIA Profile Summary tab");
                },
                error: function (xhr, status, error) {
                    console.log('Error occurred during save:', xhr.responseText);
                    alert("An error occurred while saving. Please try again.");
                    $('#btnFinishSection, #btnFinishTimeInterval, #btnFinishcreatebiaprofile').removeClass('disabled').text('Save');
                }
            });
        }

        // Handle Create BIA Profile button click
        $(document).on('click', '#btnCreateBIaProfile', function() {
            // Temporarily enable Create BIA Profile tab for navigation
            enableCreateBIAProfileTabTemporarily();

            // Navigate to Create BIA Profile tab
            navigateToCreateBIAProfileTab();

            // Show Finish and Cancel buttons for Create BIA Profile in the Create BIA Profile tab
            $('#createbiaprofileButtons').show();
            $(this).hide(); // Hide the Create button

            // Enable editing for Create BIA Profile tab
            $('input[name="SectionID"]').prop('disabled', false);

            // Ensure Save button is enabled and ready for use
            $('#btnFinishcreatebiaprofile').removeClass('disabled').text('Save');

            console.log("Create BIA Profile editing enabled and navigated to Create BIA Profile tab");
        });

        // Function to temporarily enable Create BIA Profile tab for editing
        function enableCreateBIAProfileTabTemporarily() {
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                var $steps = $wizard.find('.steps li');

                // Enable the Create BIA Profile tab (index 0)
                $steps.eq(0).removeClass('disabled');
                $steps.eq(0).find('a').removeClass('disabled');
                $steps.eq(0).css('pointer-events', 'auto');
                $steps.eq(0).css('opacity', '1');

                console.log("Create BIA Profile tab temporarily enabled for editing");
            }

            // Also enable the direct navigation link for Create BIA Profile
            $('a[href="#-h-0"]').css('pointer-events', 'auto').removeClass('disabled');
        }

        // Function to navigate to Create BIA Profile tab
        function navigateToCreateBIAProfileTab() {
            // Find the wizard and navigate to the Create BIA Profile tab (step 0)
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                try {
                    // Navigate to step 0 (Create BIA Profile tab - 0-based index)
                    $wizard.steps('setStep', 0);
                } catch (e) {
                    // Fallback method if jQuery Steps API is not available
                    $wizard.find('.steps li').removeClass('current done');
                    $wizard.find('.steps li:eq(0)').addClass('current done');
                    $wizard.find('.content .body').hide();
                    $wizard.find('.content .body:eq(0)').show();
                }

                console.log("Navigated to Create BIA Profile Tab (step 0)");
            } else {
                // Alternative method for non-wizard tabs - navigate to Create BIA Profile tab
                $('a[href="#-h-0"]').trigger('click');
                console.log("Navigated to Create BIA Profile tab using direct link");
            }
        }

        // Handle Finish button clicks (same logic as original finish functionality)
        $(document).on('click', '#btnFinishSection, #btnFinishTimeInterval, #btnFinishcreatebiaprofile', function() {
            // Execute the same finish logic as the original finish button
            executeFinishLogic();
        });

        // Handle Cancel button clicks
        $(document).on('click', '#btnCancelSection', function() {
            // Hide Finish and Cancel buttons, show Edit button
            $('#sectionButtons').hide();
            $('#btnEditSection').show();

            // Disable editing for BIA Section
            $('input[name="SectionID"]').prop('disabled', true);

            // Navigate back to BIA Profile Summary tab
            navigateToLastTabOnly();

            console.log("BIA Section editing cancelled and returned to BIA Profile Summary tab");
        });

        $(document).on('click', '#btnCancelTimeInterval', function() {
            // Hide Finish and Cancel buttons, show Edit button
            $('#timeIntervalButtons').hide();
            $('#btnEditTimeInterval').show();

            // Disable editing for Time Interval and Impacts
            $('input[name="TimeIntervalID"]').prop('disabled', true);
            $('input[name="ImpactDetails"]').prop('disabled', true);

            // Navigate back to BIA Profile Summary tab
            navigateToLastTabOnly();

            console.log("Time Interval/Impacts editing cancelled and returned to BIA Profile Summary tab");
        });

        // Handle Cancel Create BIA Profile button click
        $(document).on('click', '#btnCancelcreatebiaprofile', function() {
            // Hide Finish and Cancel buttons, show Create button
            $('#createbiaprofileButtons').hide();
            $('#btnCreateBIaProfile').show();

            // Disable editing for BIA Section
            $('input[name="SectionID"]').prop('disabled', true);

            // Navigate back to BIA Profile Summary tab
            navigateToLastTabOnly();

            console.log("Create BIA Profile editing cancelled and returned to BIA Profile Summary tab");
        });

        // Handle View All Profile button click
        $(document).on('click', '#btnViewAllProfile', function() {
            // Redirect to ManageBIAProfile form
            window.location.href = '/BCMBIAProfile/ManageBIAProfile/ManageBIAProfile';

            console.log("Redirecting to ManageBIAProfile form");
        });

        // Function to show Edit buttons in summary tab
        function showEditButtons() {
            $('#btnCreateBIaProfile').show();
            $('#btnEditSection').show();
            $('#btnEditTimeInterval').show();
            $('#btnViewAllProfile').show();
            console.log("Edit buttons are now visible in summary tab");
        }

        // Function to refresh data mappings and force update summary tables
        function refreshSummaryData() {
            // Re-collect all data mappings to ensure they're current
            biaSectionNames = {};
            timeIntervalNames = {};
            impactNames = {};

            // Re-collect BIA sections with their names
            $('input[name="SectionID"]').each(function() {
                var sectionId = parseInt($(this).val());
                var sectionName = $(this).closest('.border').find('span:last').text().trim();
                biaSectionNames[sectionId] = sectionName;
            });

            // Re-collect time intervals with their names
            $('input[name="TimeIntervalID"]').each(function() {
                var intervalId = parseInt($(this).val());
                var intervalName = $(this).closest('.border').find('span:last').text().trim();
                timeIntervalNames[intervalId] = intervalName;
            });

            // Re-collect impacts with their names
            $('input[name="ImpactDetails"]').each(function() {
                var impactId = parseInt($(this).attr('data-impactId'));
                var $row = $(this).closest('tr');
                var impactName = $row.find('td:nth-child(2)').text().trim();
                var impactCategory = $row.find('td:nth-child(3)').text().trim();
                var combinedName = impactName + ' - ' + impactCategory;
                impactNames[impactId] = combinedName;
            });

            // Force update all tables
            updateBIASectionsTable();
            updateTimeIntervalsTable();

            console.log("Summary data refreshed and tables updated");
        }

        // Function to navigate directly to the last tab (BIA Profile Summary Tab) and disable other tabs
        function navigateToLastTabOnly() {
            // Find the wizard and navigate to the last step
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                var totalSteps = $wizard.find('.steps li').length;
                var lastStepIndex = totalSteps - 1;

                // Disable all tabs except the last one
                disableAllTabsExceptLast();

                // Use jQuery Steps API to navigate to the last step
                try {
                    $wizard.steps('setStep', lastStepIndex);
                } catch (e) {
                    // Fallback method if jQuery Steps API is not available
                    $wizard.find('.steps li').removeClass('current done');
                    $wizard.find('.steps li:last').addClass('current done');
                    $wizard.find('.content .body').hide();
                    $wizard.find('.content .body:last').show();
                }

                console.log("Navigated directly to BIA Profile Summary Tab (step " + lastStepIndex + ") - Other tabs disabled");
            } else {
                // Alternative method for non-wizard tabs - navigate to last tab
                $('a[href="#-h-3"]').trigger('click'); // Assuming BIA Profile Summary is step 3
                console.log("Navigated to BIA Profile Summary tab using direct link");
            }
        }

        // Function to disable all tabs except the last one
        function disableAllTabsExceptLast() {
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                var $steps = $wizard.find('.steps li');
                var totalSteps = $steps.length;

                // Disable all tabs except the last one
                $steps.each(function(index) {
                    if (index < totalSteps - 1) {
                        $(this).addClass('disabled');
                        $(this).find('a').addClass('disabled');
                        $(this).css('pointer-events', 'none');
                        $(this).css('opacity', '0.5');
                    }
                });

                // Ensure the last tab is enabled and active
                $steps.last().removeClass('disabled');
                $steps.last().find('a').removeClass('disabled');
                $steps.last().css('pointer-events', 'auto');
                $steps.last().css('opacity', '1');

                console.log("All tabs disabled except BIA Profile Summary tab");
            }

            // Also disable direct tab navigation links
            $('a[href^="#-h-"]').not('a[href="#-h-3"]').css('pointer-events', 'none').addClass('disabled');
        }

        // Function to disable BIA Section tab after editing
        function disableBIASectionTab() {
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                var $steps = $wizard.find('.steps li');

                // Disable the BIA Section tab (index 1)
                $steps.eq(1).addClass('disabled');
                $steps.eq(1).find('a').addClass('disabled');
                $steps.eq(1).css('pointer-events', 'none');
                $steps.eq(1).css('opacity', '0.5');

                console.log("BIA Section tab disabled after editing");
            }

            // Also disable the direct navigation link for BIA Section
            $('a[href="#-h-1"]').css('pointer-events', 'none').addClass('disabled');
        }

        // Function to disable Time Interval/BIA Impact tab after editing
        function disableTimeIntervalTab() {
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                var $steps = $wizard.find('.steps li');

                // Disable the Time Interval/BIA Impact tab (index 2)
                $steps.eq(2).addClass('disabled');
                $steps.eq(2).find('a').addClass('disabled');
                $steps.eq(2).css('pointer-events', 'none');
                $steps.eq(2).css('opacity', '0.5');

                console.log("Time Interval/BIA Impact tab disabled after editing");
            }

            // Also disable the direct navigation link for Time Interval/BIA Impact
            $('a[href="#-h-2"]').css('pointer-events', 'none').addClass('disabled');
        }

        // Function to disable Create BIA Profile tab after editing
        function disableCreateBIAProfileTab() {
            var $wizard = $('.tab-wizard');
            if ($wizard.length > 0) {
                var $steps = $wizard.find('.steps li');

                // Disable the Create BIA Profile tab (index 0)
                $steps.eq(0).addClass('disabled');
                $steps.eq(0).find('a').addClass('disabled');
                $steps.eq(0).css('pointer-events', 'none');
                $steps.eq(0).css('opacity', '0.5');

                console.log("Create BIA Profile tab disabled after editing");
            }

            // Also disable the direct navigation link for Create BIA Profile
            $('a[href="#-h-0"]').css('pointer-events', 'none').addClass('disabled');
        }

        // Function to remove Previous and Next buttons from all tabs
        function removePreviousNextButtons() {
            // Remove wizard navigation buttons
            $('.wizard .actions').remove();
            $('.actions').remove();

            // Remove specific Previous and Next buttons
            $('a[href="#previous"]').remove();
            $('a[href="#next"]').remove();
            $('button[type="button"]:contains("Previous")').remove();
            $('button[type="button"]:contains("Next")').remove();

            // Remove any navigation buttons with common classes
            $('.btn-previous').remove();
            $('.btn-next').remove();
            $('.wizard-btn-previous').remove();
            $('.wizard-btn-next').remove();

            // Hide wizard footer/actions area
            $('.wizard-footer').hide();
            $('.wizard-actions').hide();
            $('.steps-actions').hide();

            console.log("Previous and Next buttons removed from all tabs");
        }

        // Function to check status and hide edit buttons if approved
        function checkStatusAndHideEditButtons() {
            // Try multiple ways to get the status value
            var status = $('#Status').val() ||
                        $('#Status').attr('value') ||
                        $('input[name="Status"]').val() ||
                        $('input[asp-for="Status"]').val() ||
                        '';

            // Also check if status is passed via URL parameters or data attributes
            var urlParams = new URLSearchParams(window.location.search);
            var urlStatus = urlParams.get('status');

            // Check for data attributes on the modal or form
            var dataStatus = $('.modal').attr('data-status') ||
                           $('form').attr('data-status') ||
                           $('body').attr('data-status');

            // Use the first available status value
            status = status || urlStatus || dataStatus || '';

            // Debug logging
            console.log("Status from #Status field:", $('#Status').val());
            console.log("Status from URL params:", urlStatus);
            console.log("Status from data attributes:", dataStatus);
            console.log("Final Status value:", status);
            console.log("Status type:", typeof status);

            // For testing purposes, also check if this is a default/template profile
            // You can modify this logic based on how you identify default BIA template profiles
            var profileName = $('#ProfileName').val() || '';
            var isDefaultTemplate = profileName.toLowerCase().includes('template') ||
                                  profileName.toLowerCase().includes('default') ||
                                  $('#ID').val() === '0' || // Assuming template profiles have ID 0
                                  $('.modal').hasClass('template-profile');

            console.log("Profile Name:", profileName);
            console.log("Is Default Template:", isDefaultTemplate);

            // Check if status is 'Approved' (case insensitive) OR if it's a default template
            if ((status && status.toLowerCase() === 'approved') || isDefaultTemplate) {
                // Add CSS class to hide buttons
                $('#btnCreateBIaProfile').addClass('hide-for-approved');
                $('#btnEditSection').addClass('hide-for-approved');
                $('#btnEditTimeInterval').addClass('hide-for-approved');

                // Also use style attribute as backup
                $('#btnCreateBIaProfile').attr('style', 'display: none !important');
                $('#btnEditSection').attr('style', 'display: none !important');
                $('#btnEditTimeInterval').attr('style', 'display: none !important');

                // Add approved status class to body for CSS targeting
                $('body').addClass('approved-status');

                // Ensure View All Profile button is visible
                $('#btnViewAllProfile').show();

                console.log("Status is Approved OR Default Template - Edit buttons hidden, only View All Profile button visible");
            } else {
                // Remove CSS classes and show edit buttons for non-approved status
                $('#btnCreateBIaProfile').removeClass('hide-for-approved');
                $('#btnEditSection').removeClass('hide-for-approved');
                $('#btnEditTimeInterval').removeClass('hide-for-approved');

                // Remove style attribute
                $('#btnCreateBIaProfile').removeAttr('style');
                $('#btnEditSection').removeAttr('style');
                $('#btnEditTimeInterval').removeAttr('style');

                // Remove approved status class from body
                $('body').removeClass('approved-status');

                $('#btnViewAllProfile').show();

                console.log("Status is not Approved and not Default Template - All buttons visible");
            }
        }

        // Initial setup: Navigate to last tab only, remove navigation buttons, and update tables on page load
        setTimeout(function() {
            // Remove Previous and Next buttons from all tabs
            removePreviousNextButtons();

            // Check status and hide edit buttons if approved
            checkStatusAndHideEditButtons();

            // Navigate to the last tab (BIA Profile Summary) only and disable other tabs
            navigateToLastTabOnly();

            // Then update the summary tables
            updateBIASectionsTable();
            updateTimeIntervalsTable(); // Now includes matrix design
        }, 500); // Small delay to ensure DOM is fully ready
    });
</script>

<style>
    #timeintervals .toggle-password {
        width: 20px;
        height: 20px;
        background-color: #007bff;
        color: white;
        border: none;
        font-size: 12px;
        cursor: pointer;
    }

        #timeintervals .toggle-password:hover {
            background-color: #0056b3;
        }

    #timeintervals .form-select-sm {
        font-size: 0.875rem;
        padding: 0.25rem 0.5rem;
    }

    /* Disabled dropdown styling for readonly appearance */
    #timeintervals .dropdown:disabled {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        cursor: not-allowed !important;
        opacity: 0.8 !important;
        border-color: #dee2e6 !important;
    }

        #timeintervals .dropdown:disabled:hover {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
        }

        #timeintervals .dropdown:disabled:focus {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
            box-shadow: none !important;
        }

    #timeintervals .text-primary {
        font-weight: 600;
    }

    #timeintervals td {
        vertical-align: middle;
    }

    /* Ensure our specific edit and action buttons are always visible */
    #btnEditSection,
    #btnEditTimeInterval,
    #btnCreateBIaProfile,
    #btnFinishSection,
    #btnFinishTimeInterval,
    #btnFinishcreatebiaprofile,
    #btnCancelSection,
    #btnCancelTimeInterval,
    #btnCancelcreatebiaprofile {
        display: inline-block !important;
        visibility: visible !important;
    }

    /* Style for the button containers */
    #sectionButtons,
    #timeIntervalButtons,
    #createbiaprofileButtons {
        margin-top: 10px;
    }

    /* Add some spacing between Finish and Cancel buttons */
    #btnCancelSection,
    #btnCancelTimeInterval,
    #btnCancelcreatebiaprofile {
        margin-left: 10px;
    }

    /* Disable all tabs except the last one */
    .tab-wizard .steps li.disabled,
    .tab-wizard .steps li.disabled a,
    a[href^="#-h-"].disabled {
        pointer-events: none !important;
        cursor: not-allowed !important;
        opacity: 0.5 !important;
        color: #6c757d !important;
    }

    /* Ensure the last tab (BIA Profile Summary) is always accessible */
    .tab-wizard .steps li:last-child,
    .tab-wizard .steps li:last-child a,
    a[href="#-h-3"] {
        pointer-events: auto !important;
        cursor: pointer !important;
        opacity: 1 !important;
    }

    /* Hide all Previous and Next navigation buttons */
    .wizard .actions,
    .actions,
    a[href="#previous"],
    a[href="#next"],
    .btn-previous,
    .btn-next,
    .wizard-btn-previous,
    .wizard-btn-next,
    .wizard-footer,
    .wizard-actions,
    .steps-actions,
    button[type="button"]:contains("Previous"),
    button[type="button"]:contains("Next") {
        display: none !important;
        visibility: hidden !important;
    }

    /* Hide any wizard navigation elements */
    .wizard .content .actions,
    .wizard .content .actions ul,
    .wizard .content .actions li,
    .wizard .content .actions a {
        display: none !important;
        visibility: hidden !important;
    }

    /* Override button visibility for approved status */
    .approved-status #btnCreateBIaProfile,
    .approved-status #btnEditSection,
    .approved-status #btnEditTimeInterval {
        display: none !important;
        visibility: hidden !important;
    }

    /* Force hide buttons when approved */
    #btnCreateBIaProfile.hide-for-approved,
    #btnEditSection.hide-for-approved,
    #btnEditTimeInterval.hide-for-approved {
        display: none !important;
        visibility: hidden !important;
    }
</style>
