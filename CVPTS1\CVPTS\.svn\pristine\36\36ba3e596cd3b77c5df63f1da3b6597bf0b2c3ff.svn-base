﻿@model BCM.BusinessClasses.OrgGroup
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!-- Configuration Modal -->

<form id="editOrgGroupForm" asp-action="EditOrgGroup" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="modal-body pt-0">
        <div class="row row-cols-2">
            <div class="col">
                <div>
                    <input type="hidden" asp-for="OrgGroupID" />
                </div>
                <div class="form-group">
                    <label for="validationCustom01" class="form-label required-field">Login Code</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-login-code"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Login Code" asp-for="OrganizationGroupCode" required title="Please enter login code.">
                    </div>
                    <div class="invalid-feedback">Enter Login Code</div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label required-field">Organization Group Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                        <input type="text" class="form-control" asp-for="OrganizationGroupName" placeholder="Enter Organization Group Name" required pattern="^[A-Za-z\s]+$" title="Please enter alphabetic characters only.">
                    </div>
                    <div class="invalid-feedback">Enter Organization Group Name</div>
                </div>
            </div>
        </div>

        <div class="row row-cols-2">
            <div class="col-12">
                <blockquote class="blockquote">Organization Group Details</blockquote>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label required-field">Legal Entity</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-Legal-Entity"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Legal Entity" asp-for="LegalEntity" title="Please enter a valid legal entity." required>
                    </div>
                    <div class="invalid-feedback">Enter Legal Entity</div>
                </div>
                <div class="form-group">
                    <label class="form-label required-field">Phone</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-phone"></i></span>
                        <input type="tel" class="form-control" placeholder="Enter Phone Number" asp-for="Mobile" required pattern="^(\+\d{1,4})?\d{7,15}$" title="Please enter a valid phone number (with or without country code, e.g., +919876543210 or 9876543210).">
                    </div>
                    <div class="invalid-feedback">Enter Phone Number</div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label required-field">Corporate Address</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-corporate-address"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Corporate Address" asp-for="CompanyAddress" title="Please enter a corporate address." required>
                    </div>
                    <div class="invalid-feedback">Enter Corporate Address</div>
                </div>

                <div class="form-group">
                    <label class="form-label">Fax</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-fax"></i></span>
                        <input type="tel" class="form-control" asp-for="Fax" placeholder="Enter Fax Number" pattern="^\d{10,15}$" title="Please enter a valid fax number (10 to 15 digits).">
                    </div>
                    <div class="invalid-feedback">Enter Fax Number</div>
                </div>
            </div>
        </div>
        <div class="row row-cols-2">
            <div class="col-12">
                <blockquote class="blockquote">SPOC Details</blockquote>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label required-field">Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter SPOC Name" asp-for="SPOCName" required pattern="^[A-Za-z\s]+$" title="Please enter alphabetic characters only.">
                    </div>
                    <div class="invalid-feedback">Enter SPOC Name</div>
                </div>
                <div class="form-group">
                    <label class="form-label required-field">Mobile</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-fax"></i></span>
                        <input type="tel" class="form-control" id="SPOCMobile" asp-for="SPOCMobile" placeholder="Enter Mobile" required pattern="^(\+\d{1,4})?\d{7,15}$" title="Please enter a valid mobile number (with or without country code, e.g., +919876543210 or 9876543210).">
                    </div>
                    <div class="invalid-feedback">Enter Mobile</div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label required-field">Email</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-mail"></i></span>
                        <input type="email"
                               id="SPOCMail"
                               class="form-control"
                               asp-for="SPOCEmail"
                               placeholder="Enter Email" title="Please enter an email."
                               required />
                    </div>
                    <div class="invalid-feedback" id="MailError">Enter Email</div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Update</button>
        </div>
    </div>
</form>
<script>
    $(document).ready(function () {
        //console.log("EditOrgGroup form ready");

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function () {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function () {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for EditOrgGroup form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('editOrgGroupForm');
                if (!form) {
                    console.error("Form not found with ID: editOrgGroupForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function (element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function (input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateEmail function similarly
                const originalValidateEmail = window.BCMValidation.validateEmail;
                window.BCMValidation.validateEmail = function (input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateEmail(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validatePatternInput function to show pattern-specific messages
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function (input, forceValidation = false) {
                    console.log("Custom validatePatternInput called for:", input.id || input.name, "pattern:", input.pattern, "value:", input.value);

                    const inputGroup = input.closest('.input-group');
                    const formGroup = input.closest('.form-group');
                    const feedbackElement = formGroup?.querySelector('.invalid-feedback');

                    if (!feedbackElement) {
                        console.warn("No feedback element found for pattern input:", input.id || input.name || "unnamed input");
                        return true;
                    }

                    // Skip validation if pending and not forced
                    if (!forceValidation && formGroup && formGroup.classList.contains('validation-pending')) {
                        return true;
                    }

                    const pattern = new RegExp(input.getAttribute('pattern'));

                    if (input.value === '') {
                        // Empty value - check if required
                        if (input.hasAttribute('required')) {
                            console.log("Required pattern input is empty");
                            input.classList.add('is-invalid');
                            if (inputGroup) inputGroup.classList.add('is-invalid');
                            feedbackElement.textContent = "This field is required";
                            feedbackElement.style.display = 'block';
                            return false;
                        } else {
                            // Not required and empty is fine
                            input.classList.remove('is-invalid');
                            if (inputGroup) inputGroup.classList.remove('is-invalid');
                            feedbackElement.style.display = 'none';
                            return true;
                        }
                    } else if (!pattern.test(input.value)) {
                        // Invalid pattern - show generic pattern message from title attribute
                        console.log("Pattern validation failed:", input.value);
                        input.classList.add('is-invalid');
                        if (inputGroup) inputGroup.classList.add('is-invalid');

                        // Use title attribute for pattern validation messages (as per user preference)
                        let errorMessage = input.title || "Please enter a valid value";

                        feedbackElement.textContent = errorMessage;
                        feedbackElement.style.display = 'block';
                        console.log("Showing pattern validation message:", errorMessage);
                        return false;
                    } else {
                        // Valid pattern
                        console.log("Pattern is valid");
                        input.classList.remove('is-invalid');
                        if (inputGroup) inputGroup.classList.remove('is-invalid');
                        feedbackElement.style.display = 'none';
                        return true;
                    }
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function (form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add event listeners for pattern validation on input/blur events
                const patternFields = form.querySelectorAll('input[pattern]');
                console.log("Setting up pattern validation for", patternFields.length, "fields");

                patternFields.forEach(function(field) {
                    console.log("Setting up pattern validation for field:", field.id || field.name, "pattern:", field.pattern);

                    // Add input event listener for real-time pattern validation
                    field.addEventListener('input', function() {
                        const formGroup = this.closest('.form-group');
                        if (formGroup && !formGroup.classList.contains('validation-pending')) {
                            console.log("PATTERN INPUT EVENT triggered for:", this.id || this.name, "value:", this.value, "pattern:", this.pattern);
                            window.BCMValidation.validatePatternInput(this);
                        }
                    });

                    // Add blur event listener for pattern validation when field loses focus
                    field.addEventListener('blur', function() {
                        const formGroup = this.closest('.form-group');
                        if (formGroup) {
                            formGroup.classList.remove('validation-pending');
                        }
                        console.log("PATTERN BLUR EVENT triggered for:", this.id || this.name, "value:", this.value, "pattern:", this.pattern);
                        window.BCMValidation.validatePatternInput(this);
                    });
                });

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function (event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    if (!isValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }
    });
</script>
