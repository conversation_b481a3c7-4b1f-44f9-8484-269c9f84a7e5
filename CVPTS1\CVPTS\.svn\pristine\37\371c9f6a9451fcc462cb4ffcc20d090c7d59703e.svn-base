﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class WidgetListController : BaseController
{
    private readonly Utilities _Utilities;
    private ProcessSrv _ProcessSrv;
    readonly CVLogger _CVLogger;
    private readonly ILoggerFactory? _LoggerFactory;
    public WidgetListController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    public IActionResult WidgetList()
    {
        return View();
    }

    [HttpPost]
    public IActionResult SaveWidgetBuilder([FromBody] DashboardInfo objInfo)
    {

        try
        {
            int iSuccess = _ProcessSrv.WidgetBuilder_Save_Upadte(objInfo);

            // Correcting the syntax for JsonResult usage
            return Json(new { success = true, Message = "Widget save successfuly" });

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

            return Json(new { success = false, Message = ex });
        }

    }



}