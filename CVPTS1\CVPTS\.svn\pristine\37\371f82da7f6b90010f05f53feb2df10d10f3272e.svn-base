﻿@{
    ViewBag.Title = "APIConfiguration";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Manage API Configuration</h6>
</div>

<div class="Page-Condant card border-0">
    <div>
        <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#apiBasicDetailsSection" aria-expanded="true" aria-controls="apiBasicDetailsSection">
                <i class="cv-minus align-middle"></i>
            </span>
            <h6 class="mb-0">API Basic Details</h6>
        </div>

        <div class="row p-3 row-cols-2 collapse show" id="apiBasicDetailsSection">
            <div class="col">
                <!-- API Name -->
                <div class="form-group">
                    <label class="form-label">API Name <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" id="txtAPIName" name="APIName" class="form-control" placeholder="Enter API Name (e.g., HRMS Get Employee)" required />
                    </div>
                    <div class="invalid-feedback">Enter API Name</div>
                </div>

                <!-- API Type -->
                <div class="form-group">
                    <label class="form-label">API Type <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-type"></i></span>
                        <select id="ddlAPIType" name="APIType" class="form-select form-select-sm" required>
                            <option selected value="0">-- Select API Type --</option>
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                            <option value="PATCH">PATCH</option>
                        </select>
                    </div>
                    <div class="invalid-feedback">Select API Type</div>
                </div>

                <!-- Base URL -->
                <div class="form-group">
                    <label class="form-label">Base URL <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-url"></i></span>
                        <input type="url" id="txtBaseURL" name="BaseURL" class="form-control" placeholder="Enter Base URL (e.g., https://api.hrms.com)" required />
                    </div>
                    <div class="invalid-feedback">Enter valid Base URL</div>
                </div>
            </div>

            <div class="col">
                <!-- Endpoint Path -->
                <div class="form-group">
                    <label class="form-label">Endpoint Path <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-description"></i></span>
                        <input type="text" id="txtEndpointPath" name="EndpointPath" class="form-control" placeholder="Enter Endpoint Path (e.g., /employees/{id})" required />
                    </div>
                    <div class="invalid-feedback">Enter Endpoint Path</div>
                </div>

                <!-- Full URL Preview -->
                <div class="form-group">
                    <label class="form-label">Full URL Preview</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-url"></i></span>
                        <input type="text" id="txtFullURLPreview" name="FullURLPreview" class="form-control" placeholder="Full URL will be displayed here" readonly />
                    </div>
                </div>

                <!-- Is Active -->
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="chkIsActive" name="IsActive" checked />
                        <label class="form-check-label" for="chkIsActive">
                            <strong>Is Active</strong>
                            <small class="text-muted d-block">Enable/Disable this API configuration</small>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Authentication Section -->
        <div class="mt-4">
            <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
                <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#authenticationSection" aria-expanded="true" aria-controls="authenticationSection">
                    <i class="cv-minus align-middle"></i>
                </span>
                <h6 class="mb-0">Authentication</h6>
            </div>

            <div class="row p-3 row-cols-2 collapse show" id="authenticationSection">
                <div class="col">
                    <!-- Auth Type -->
                    <div class="form-group">
                        <label class="form-label">Auth Type</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-security"></i></span>
                            <select id="ddlAuthType" name="AuthType" class="form-select form-select-sm">
                                <option selected value="None">None</option>
                                <option value="Basic">Basic</option>
                                <option value="APIKey">API Key</option>
                                <option value="OAuth2">OAuth 2.0</option>
                                <option value="JWT">JWT</option>
                            </select>
                        </div>
                    </div>

                    <!-- Username (for Basic Auth) -->
                    <div class="form-group auth-field basic-auth" style="display: none;">
                        <label class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-user"></i></span>
                            <input type="text" id="txtUsername" name="Username" class="form-control" placeholder="Enter Username" />
                        </div>
                    </div>

                    <!-- API Key Name -->
                    <div class="form-group auth-field api-key-auth" style="display: none;">
                        <label class="form-label">API Key Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-key"></i></span>
                            <input type="text" id="txtAPIKeyName" name="APIKeyName" class="form-control" placeholder="e.g., x-api-key, Authorization" />
                        </div>
                    </div>

                    <!-- Token URL (for OAuth2) -->
                    <div class="form-group auth-field oauth2-auth" style="display: none;">
                        <label class="form-label">Token URL</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-url"></i></span>
                            <input type="url" id="txtTokenURL" name="TokenURL" class="form-control" placeholder="Enter Token URL" />
                        </div>
                    </div>

                    <!-- Client ID (for OAuth2) -->
                    <div class="form-group auth-field oauth2-auth" style="display: none;">
                        <label class="form-label">Client ID</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-id"></i></span>
                            <input type="text" id="txtClientID" name="ClientID" class="form-control" placeholder="Enter Client ID" />
                        </div>
                    </div>
                </div>

                <div class="col">
                    <!-- Password (for Basic Auth) -->
                    <div class="form-group auth-field basic-auth" style="display: none;">
                        <label class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-password"></i></span>
                            <input type="password" id="txtPassword" name="Password" class="form-control" placeholder="Enter Password" />
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="cv-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- API Key Value -->
                    <div class="form-group auth-field api-key-auth" style="display: none;">
                        <label class="form-label">API Key Value</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-key"></i></span>
                            <input type="password" id="txtAPIKeyValue" name="APIKeyValue" class="form-control" placeholder="Enter API Key Value" />
                            <button class="btn btn-outline-secondary" type="button" id="toggleAPIKey">
                                <i class="cv-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Client Secret (for OAuth2) -->
                    <div class="form-group auth-field oauth2-auth" style="display: none;">
                        <label class="form-label">Client Secret</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-key"></i></span>
                            <input type="password" id="txtClientSecret" name="ClientSecret" class="form-control" placeholder="Enter Client Secret" />
                            <button class="btn btn-outline-secondary" type="button" id="toggleClientSecret">
                                <i class="cv-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Bearer Token (for JWT or static token) -->
                    <div class="form-group auth-field jwt-auth" style="display: none;">
                        <label class="form-label">Bearer Token</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-token"></i></span>
                            <input type="password" id="txtBearerToken" name="BearerToken" class="form-control" placeholder="Enter Bearer Token" />
                            <button class="btn btn-outline-secondary" type="button" id="toggleBearerToken">
                                <i class="cv-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Auth Preview -->
                    <div class="form-group">
                        <label class="form-label">Authentication Preview</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-preview"></i></span>
                            <textarea id="txtAuthPreview" name="AuthPreview" class="form-control" rows="3" placeholder="Authentication details will be displayed here" readonly></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Request Settings Section -->
        <div class="mt-4">
            <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
                <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#requestSettingsSection" aria-expanded="true" aria-controls="requestSettingsSection">
                    <i class="cv-minus align-middle"></i>
                </span>
                <h6 class="mb-0">Request Settings</h6>
            </div>

            <div class="p-3 collapse show" id="requestSettingsSection">
                <!-- Headers Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            <label class="form-label mb-0">Headers</label>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="btnAddHeader">
                                <i class="cv-plus me-1"></i>Add Header
                            </button>
                        </div>
                        <div class="border rounded p-2" style="min-height: 100px; max-height: 200px; overflow-y: auto;">
                            <div id="headersContainer">
                                <div class="row header-row mb-2">
                                    <div class="col-5">
                                        <input type="text" class="form-control form-control-sm header-key" placeholder="Header Name (e.g., Content-Type)" />
                                    </div>
                                    <div class="col-6">
                                        <input type="text" class="form-control form-control-sm header-value" placeholder="Header Value (e.g., application/json)" />
                                    </div>
                                    <div class="col-1">
                                        <button type="button" class="btn btn-outline-danger btn-sm remove-header" title="Remove Header">
                                            <i class="cv-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Query Parameters Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            <label class="form-label mb-0">Query Parameters</label>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="btnAddQueryParam">
                                <i class="cv-plus me-1"></i>Add Parameter
                            </button>
                        </div>
                        <div class="border rounded p-2" style="min-height: 100px; max-height: 200px; overflow-y: auto;">
                            <div id="queryParamsContainer">
                                <div class="row query-param-row mb-2">
                                    <div class="col-5">
                                        <input type="text" class="form-control form-control-sm query-key" placeholder="Parameter Name (e.g., limit)" />
                                    </div>
                                    <div class="col-6">
                                        <input type="text" class="form-control form-control-sm query-value" placeholder="Parameter Value (e.g., 10)" />
                                    </div>
                                    <div class="col-1">
                                        <button type="button" class="btn btn-outline-danger btn-sm remove-query-param" title="Remove Parameter">
                                            <i class="cv-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Path Parameters Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            <label class="form-label mb-0">Path Parameters</label>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="btnAddPathParam">
                                <i class="cv-plus me-1"></i>Add Parameter
                            </button>
                        </div>
                        <div class="border rounded p-2" style="min-height: 100px; max-height: 200px; overflow-y: auto;">
                            <div id="pathParamsContainer">
                                <div class="row path-param-row mb-2">
                                    <div class="col-5">
                                        <input type="text" class="form-control form-control-sm path-key" placeholder="Parameter Name (e.g., id)" />
                                    </div>
                                    <div class="col-6">
                                        <input type="text" class="form-control form-control-sm path-value" placeholder="Parameter Value (e.g., 123)" />
                                    </div>
                                    <div class="col-1">
                                        <button type="button" class="btn btn-outline-danger btn-sm remove-path-param" title="Remove Parameter">
                                            <i class="cv-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Request Body and Content-Type Section -->
                <div class="row">
                    <div class="col-6">
                        <!-- Content-Type -->
                        <div class="form-group">
                            <label class="form-label">Content-Type</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-type"></i></span>
                                <select id="ddlContentType" name="ContentType" class="form-select form-select-sm">
                                    <option selected value="">-- Select Content-Type --</option>
                                    <option value="application/json">application/json</option>
                                    <option value="application/xml">application/xml</option>
                                    <option value="application/x-www-form-urlencoded">application/x-www-form-urlencoded</option>
                                    <option value="multipart/form-data">multipart/form-data</option>
                                    <option value="text/plain">text/plain</option>
                                    <option value="text/html">text/html</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <!-- Request Body Template -->
                        <div class="form-group">
                            <label class="form-label">Request Body Template</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-code"></i></span>
                                <textarea id="txtRequestBody" name="RequestBody" class="form-control" rows="8" placeholder="Enter request body template (JSON, XML, etc.)&#10;Example:&#10;{&#10;  &quot;name&quot;: &quot;{{name}}&quot;,&#10;  &quot;email&quot;: &quot;{{email}}&quot;&#10;}"></textarea>
                            </div>
                            <small class="text-muted">Use {{variableName}} for dynamic values. Available for POST, PUT, PATCH methods.</small>
                        </div>
                    </div>
                </div>

                <!-- Request Preview -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Request Preview</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-preview"></i></span>
                                <textarea id="txtRequestPreview" name="RequestPreview" class="form-control" rows="6" placeholder="Complete request details will be displayed here" readonly></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Response Handling Section -->
        <div class="mt-4">
            <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
                <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#responseHandlingSection" aria-expanded="true" aria-controls="responseHandlingSection">
                    <i class="cv-minus align-middle"></i>
                </span>
                <h6 class="mb-0">Response Handling</h6>
            </div>

            <div class="p-3 collapse show" id="responseHandlingSection">
                <div class="row">
                    <div class="col-6">
                        <!-- Success Key Path -->
                        <div class="form-group">
                            <label class="form-label">Success Key Path</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-path"></i></span>
                                <input type="text" id="txtSuccessKeyPath" name="SuccessKeyPath" class="form-control" placeholder="e.g., data.id, result.employee.name" />
                            </div>
                            <small class="text-muted">JSON path to extract success data from response (dot notation)</small>
                        </div>

                        <!-- Expected Status Codes -->
                        <div class="form-group">
                            <label class="form-label">Expected Status Codes</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-status"></i></span>
                                <input type="text" id="txtExpectedStatusCodes" name="ExpectedStatusCodes" class="form-control" placeholder="e.g., 200, 201, 202" />
                            </div>
                            <small class="text-muted">Comma-separated list of HTTP status codes considered successful</small>
                        </div>

                        <!-- Error Key Path -->
                        <div class="form-group">
                            <label class="form-label">Error Key Path</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-error"></i></span>
                                <input type="text" id="txtErrorKeyPath" name="ErrorKeyPath" class="form-control" placeholder="e.g., error.message, errors[0].description" />
                            </div>
                            <small class="text-muted">JSON path to extract error message from response</small>
                        </div>
                    </div>

                    <div class="col-6">
                        <!-- Response Format -->
                        <div class="form-group">
                            <label class="form-label">Response Format</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-format"></i></span>
                                <select id="ddlResponseFormat" name="ResponseFormat" class="form-select form-select-sm">
                                    <option selected value="JSON">JSON</option>
                                    <option value="XML">XML</option>
                                    <option value="TEXT">Plain Text</option>
                                    <option value="HTML">HTML</option>
                                </select>
                            </div>
                        </div>

                        <!-- Response Timeout -->
                        <div class="form-group">
                            <label class="form-label">Response Timeout (seconds)</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-time"></i></span>
                                <input type="number" id="txtResponseTimeout" name="ResponseTimeout" class="form-control" placeholder="30" min="1" max="300" value="30" />
                            </div>
                            <small class="text-muted">Maximum time to wait for response (1-300 seconds)</small>
                        </div>

                        <!-- Retry Configuration -->
                        <div class="form-group">
                            <label class="form-label">Retry Attempts</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-retry"></i></span>
                                <input type="number" id="txtRetryAttempts" name="RetryAttempts" class="form-control" placeholder="3" min="0" max="10" value="3" />
                            </div>
                            <small class="text-muted">Number of retry attempts on failure (0-10)</small>
                        </div>
                    </div>
                </div>

                <!-- Response Field Mapping Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            <label class="form-label mb-0">Map Response Fields To Internal Schema</label>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="btnAddResponseMapping">
                                <i class="cv-plus me-1"></i>Add Mapping
                            </button>
                        </div>
                        <div class="border rounded p-2" style="min-height: 120px; max-height: 250px; overflow-y: auto;">
                            <div id="responseMappingContainer">
                                <div class="row response-mapping-row mb-2">
                                    <div class="col-4">
                                        <input type="text" class="form-control form-control-sm response-field" placeholder="Response Field Path (e.g., data.employee.id)" />
                                    </div>
                                    <div class="col-4">
                                        <select class="form-select form-select-sm internal-field">
                                            <option value="">-- Select Internal Field --</option>
                                            <option value="EmployeeID">Employee ID</option>
                                            <option value="EmployeeName">Employee Name</option>
                                            <option value="EmployeeEmail">Employee Email</option>
                                            <option value="DepartmentID">Department ID</option>
                                            <option value="DepartmentName">Department Name</option>
                                            <option value="ManagerID">Manager ID</option>
                                            <option value="JobTitle">Job Title</option>
                                            <option value="HireDate">Hire Date</option>
                                            <option value="Salary">Salary</option>
                                            <option value="Status">Status</option>
                                            <option value="Custom">Custom Field</option>
                                        </select>
                                    </div>
                                    <div class="col-3">
                                        <input type="text" class="form-control form-control-sm custom-field" placeholder="Custom field name" style="display: none;" />
                                    </div>
                                    <div class="col-1">
                                        <button type="button" class="btn btn-outline-danger btn-sm remove-response-mapping" title="Remove Mapping">
                                            <i class="cv-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <small class="text-muted">Map response fields to internal schema fields for data processing</small>
                    </div>
                </div>

                <!-- Response Preview -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Response Handling Preview</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-preview"></i></span>
                                <textarea id="txtResponsePreview" name="ResponsePreview" class="form-control" rows="6" placeholder="Response handling configuration will be displayed here" readonly></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test API Integration Section -->
        <div class="mt-4">
            <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
                <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#testAPISection" aria-expanded="true" aria-controls="testAPISection">
                    <i class="cv-minus align-middle"></i>
                </span>
                <h6 class="mb-0">Test API Integration</h6>
            </div>

            <div class="p-3 collapse show" id="testAPISection">
                <!-- Test Input Parameters -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            <label class="form-label mb-0">Test Input Parameters</label>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="btnRefreshTestParams">
                                <i class="cv-refresh me-1"></i>Refresh Parameters
                            </button>
                        </div>
                        <div class="border rounded p-3" style="min-height: 120px; max-height: 300px; overflow-y: auto;">
                            <div id="testParametersContainer">
                                <div class="text-muted text-center py-3">
                                    <i class="cv-info me-2"></i>
                                    Configure API settings above and click "Refresh Parameters" to generate test input fields
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Controls -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex align-items-center gap-3">
                            <button type="button" class="btn btn-success" id="btnSendTestRequest">
                                <i class="cv-send me-1"></i>Send Test Request
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="btnClearTestResults">
                                <i class="cv-clear me-1"></i>Clear Results
                            </button>
                            <div class="ms-auto">
                                <span class="badge bg-secondary" id="testStatusBadge" style="display: none;">Ready</span>
                                <span class="spinner-border spinner-border-sm ms-2" id="testSpinner" style="display: none;" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="row">
                    <div class="col-6">
                        <!-- Status Code and Response Info -->
                        <div class="form-group mb-3">
                            <label class="form-label">Response Status</label>
                            <div class="d-flex gap-2">
                                <div class="input-group flex-grow-1">
                                    <span class="input-group-text"><i class="cv-status"></i></span>
                                    <input type="text" id="txtResponseStatus" class="form-control" placeholder="HTTP Status Code" readonly />
                                </div>
                                <div class="input-group flex-grow-1">
                                    <span class="input-group-text"><i class="cv-time"></i></span>
                                    <input type="text" id="txtResponseTime" class="form-control" placeholder="Response Time" readonly />
                                </div>
                            </div>
                        </div>

                        <!-- Raw Response -->
                        <div class="form-group">
                            <div class="d-flex align-items-center justify-content-between mb-2">
                                <label class="form-label mb-0">Raw Response</label>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-secondary" id="btnFormatJSON">
                                        <i class="cv-format me-1"></i>Format JSON
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="btnCopyResponse">
                                        <i class="cv-copy me-1"></i>Copy
                                    </button>
                                </div>
                            </div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-code"></i></span>
                                <textarea id="txtRawResponse" class="form-control" rows="12" placeholder="Raw API response will be displayed here" readonly></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <!-- Parsed Output -->
                        <div class="form-group mb-3">
                            <div class="d-flex align-items-center justify-content-between mb-2">
                                <label class="form-label mb-0">Parsed Output</label>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-secondary" id="btnToggleView" data-view="structured">
                                        <i class="cv-view me-1"></i>Structured View
                                    </button>
                                </div>
                            </div>
                            <div class="border rounded p-2" style="height: 200px; overflow-y: auto;">
                                <div id="parsedOutputContainer">
                                    <div class="text-muted text-center py-3">
                                        <i class="cv-info me-2"></i>
                                        Parsed response data will be displayed here
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Field Mapping Results -->
                        <div class="form-group">
                            <label class="form-label">Field Mapping Results</label>
                            <div class="border rounded p-2" style="height: 200px; overflow-y: auto;">
                                <div id="fieldMappingResults">
                                    <div class="text-muted text-center py-3">
                                        <i class="cv-info me-2"></i>
                                        Field mapping results will be displayed here
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test History -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            <label class="form-label mb-0">Test History</label>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="btnClearHistory">
                                <i class="cv-clear me-1"></i>Clear History
                            </button>
                        </div>
                        <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                            <div id="testHistoryContainer">
                                <div class="text-muted text-center py-2">
                                    <small>Test history will be displayed here</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="justify-content-between d-flex">
            <span class="fst-italic d-flex align-items-center text-secondary">
                <i class="cv-note me-1"></i>
                <small>Note : All fields are mandatory except optional</small>
            </span>
            <div>
                <button type="button" class="btn btn-secondary btn-sm me-1" id="btnCancel">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm me-1" id="btnSave">Save Configuration</button>
                <button type="button" class="btn btn-success btn-sm" id="btnTest">Test API</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Auto-generate full URL preview when base URL or endpoint changes
            $('#txtBaseURL, #txtEndpointPath').on('input', function () {
                updateFullURLPreview();
            });

            // Toggle collapse icon - using Bootstrap's collapse events
            $('#apiBasicDetailsSection').on('show.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-Plus').addClass('cv-minus');
            });

            $('#apiBasicDetailsSection').on('hide.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-minus').addClass('cv-Plus');
            });

            $('#authenticationSection').on('show.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-Plus').addClass('cv-minus');
            });

            $('#authenticationSection').on('hide.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-minus').addClass('cv-Plus');
            });

            $('#requestSettingsSection').on('show.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-Plus').addClass('cv-minus');
            });

            $('#requestSettingsSection').on('hide.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-minus').addClass('cv-Plus');
            });

            $('#responseHandlingSection').on('show.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-Plus').addClass('cv-minus');
            });

            $('#responseHandlingSection').on('hide.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-minus').addClass('cv-Plus');
            });

            $('#testAPISection').on('show.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-Plus').addClass('cv-minus');
            });

            $('#testAPISection').on('hide.bs.collapse', function () {
                $(this).prev().find('.toggle-password i').removeClass('cv-minus').addClass('cv-Plus');
            });

            // Auth Type change event
            $('#ddlAuthType').change(function () {
                handleAuthTypeChange();
                updateAuthPreview();
            });

            // Auth field change events for preview update
            $('#txtUsername, #txtPassword, #txtAPIKeyName, #txtAPIKeyValue, #txtTokenURL, #txtClientID, #txtClientSecret, #txtBearerToken').on('input', function () {
                updateAuthPreview();
            });

            // Password toggle functionality
            $('#togglePassword').click(function () {
                togglePasswordVisibility('#txtPassword', '#togglePassword');
            });

            $('#toggleAPIKey').click(function () {
                togglePasswordVisibility('#txtAPIKeyValue', '#toggleAPIKey');
            });

            $('#toggleClientSecret').click(function () {
                togglePasswordVisibility('#txtClientSecret', '#toggleClientSecret');
            });

            $('#toggleBearerToken').click(function () {
                togglePasswordVisibility('#txtBearerToken', '#toggleBearerToken');
            });

            // Request Settings event handlers
            $('#btnAddHeader').click(function () {
                addHeaderRow();
            });

            $('#btnAddQueryParam').click(function () {
                addQueryParamRow();
            });

            $('#btnAddPathParam').click(function () {
                addPathParamRow();
            });

            // Dynamic event handlers for remove buttons (using event delegation)
            $(document).on('click', '.remove-header', function () {
                removeRow($(this), '.header-row', '#headersContainer');
                updateRequestPreview();
            });

            $(document).on('click', '.remove-query-param', function () {
                removeRow($(this), '.query-param-row', '#queryParamsContainer');
                updateRequestPreview();
            });

            $(document).on('click', '.remove-path-param', function () {
                removeRow($(this), '.path-param-row', '#pathParamsContainer');
                updateRequestPreview();
            });

            // Update request preview on input changes
            $(document).on('input', '.header-key, .header-value, .query-key, .query-value, .path-key, .path-value', function () {
                updateRequestPreview();
            });

            $('#ddlContentType, #txtRequestBody').on('input change', function () {
                updateRequestPreview();
            });

            // Update request preview when API details change
            $('#txtBaseURL, #txtEndpointPath, #ddlAPIType').on('input change', function () {
                updateRequestPreview();
            });

            // Response Handling event handlers
            $('#btnAddResponseMapping').click(function () {
                addResponseMappingRow();
            });

            // Dynamic event handlers for response mapping
            $(document).on('click', '.remove-response-mapping', function () {
                removeRow($(this), '.response-mapping-row', '#responseMappingContainer');
                updateResponsePreview();
            });

            $(document).on('change', '.internal-field', function () {
                var customField = $(this).closest('.response-mapping-row').find('.custom-field');
                if ($(this).val() === 'Custom') {
                    customField.show().attr('required', true);
                } else {
                    customField.hide().attr('required', false).val('');
                }
                updateResponsePreview();
            });

            // Update response preview on input changes
            $(document).on('input', '.response-field, .custom-field', function () {
                updateResponsePreview();
            });

            $('#txtSuccessKeyPath, #txtExpectedStatusCodes, #txtErrorKeyPath, #ddlResponseFormat, #txtResponseTimeout, #txtRetryAttempts').on('input change', function () {
                updateResponsePreview();
            });

            // Test API Integration event handlers
            $('#btnRefreshTestParams').click(function () {
                generateTestParameters();
            });

            $('#btnSendTestRequest').click(function () {
                sendTestRequest();
            });

            $('#btnClearTestResults').click(function () {
                clearTestResults();
            });

            $('#btnFormatJSON').click(function () {
                formatJSONResponse();
            });

            $('#btnCopyResponse').click(function () {
                copyResponseToClipboard();
            });

            $('#btnToggleView').click(function () {
                toggleParsedView();
            });

            $('#btnClearHistory').click(function () {
                clearTestHistory();
            });

            // Save button click event
            $('#btnSave').click(function () {
                if (validateForm()) {
                    saveAPIConfiguration();
                }
            });

            // Test button click event
            $('#btnTest').click(function () {
                if (validateForm()) {
                    testAPIConfiguration();
                }
            });

            // Cancel button click event
            $('#btnCancel').click(function () {
                resetForm();
            });

            // Initialize validation
            initializeValidation();
        });

        function updateFullURLPreview() {
            var baseURL = $('#txtBaseURL').val().trim();
            var endpointPath = $('#txtEndpointPath').val().trim();

            if (baseURL && endpointPath) {
                // Remove trailing slash from base URL if present
                baseURL = baseURL.replace(/\/$/, '');
                // Ensure endpoint starts with slash
                if (!endpointPath.startsWith('/')) {
                    endpointPath = '/' + endpointPath;
                }
                var fullURL = baseURL + endpointPath;
                $('#txtFullURLPreview').val(fullURL);
            }
            else {
                $('#txtFullURLPreview').val('');
            }
        }

        function validateForm() {
            debugger;
            var isValid = true;

            // Reset validation states
            $('.form-control, .form-select').removeClass('is-invalid');
            $('.invalid-feedback').hide();

            // Validate API Name
            if (!$('#txtAPIName').val().trim()) {
                $('#txtAPIName').addClass('is-invalid');
                $('#txtAPIName').next('.invalid-feedback').show();
                isValid = false;
            }

            // Validate API Type
            if ($('#ddlAPIType').val() === '0' || !$('#ddlAPIType').val()) {
                $('#ddlAPIType').addClass('is-invalid');
                $('#ddlAPIType').closest('.input-group').next('.invalid-feedback').show();
                isValid = false;
            }

            // Validate Base URL
            var baseURL = $('#txtBaseURL').val().trim();
            if (!baseURL || !isValidURL(baseURL)) {
                $('#txtBaseURL').addClass('is-invalid');
                $('#txtBaseURL').closest('.input-group').next('.invalid-feedback').show();
                isValid = false;
            }

            // Validate Endpoint Path
            if (!$('#txtEndpointPath').val().trim()) {
                $('#txtEndpointPath').addClass('is-invalid');
                $('#txtEndpointPath').closest('.input-group').next('.invalid-feedback').show();
                isValid = false;
            }

            return isValid;
        }

        function isValidURL(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }

        function saveAPIConfiguration() {
            var apiData = {
                APIName: $('#txtAPIName').val(),
                APIType: $('#ddlAPIType').val(),
                BaseURL: $('#txtBaseURL').val(),
                EndpointPath: $('#txtEndpointPath').val(),
                FullURL: $('#txtFullURLPreview').val(),
                IsActive: $('#chkIsActive').is(':checked'),
                // Authentication data
                AuthType: $('#ddlAuthType').val(),
                Username: $('#txtUsername').val(),
                Password: $('#txtPassword').val(),
                APIKeyName: $('#txtAPIKeyName').val(),
                APIKeyValue: $('#txtAPIKeyValue').val(),
                TokenURL: $('#txtTokenURL').val(),
                ClientID: $('#txtClientID').val(),
                ClientSecret: $('#txtClientSecret').val(),
                BearerToken: $('#txtBearerToken').val(),
                // Request Settings data
                Headers: collectKeyValuePairs('.header-row', '.header-key', '.header-value'),
                QueryParameters: collectKeyValuePairs('.query-param-row', '.query-key', '.query-value'),
                PathParameters: collectKeyValuePairs('.path-param-row', '.path-key', '.path-value'),
                ContentType: $('#ddlContentType').val(),
                RequestBody: $('#txtRequestBody').val(),
                // Response Handling data
                SuccessKeyPath: $('#txtSuccessKeyPath').val(),
                ExpectedStatusCodes: $('#txtExpectedStatusCodes').val(),
                ErrorKeyPath: $('#txtErrorKeyPath').val(),
                ResponseFormat: $('#ddlResponseFormat').val(),
                ResponseTimeout: $('#txtResponseTimeout').val(),
                RetryAttempts: $('#txtRetryAttempts').val(),
                ResponseFieldMappings: collectResponseMappings()
            };

            // TODO: Implement AJAX call to save API configuration
            console.log('Saving API Configuration:', apiData);

            // Placeholder for actual save implementation
            //alert('API Configuration saved successfully!\n\nData: ' + JSON.stringify(apiData, null, 2));

            $.ajax({
                url: '/BCMConfiguration/ConfigSettingApi/SaveWorkflowActionAPI',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(apiData),
                success: function (response) {
                    if (response && response !== "0") {
                        alert("API details saved successfully. ID: " + response);
                    } else {
                        alert("Failed to save API details.");
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error occurred while saving API details:", error);
                    alert("An error occurred: " + error);
                }
            });
        }

        function testAPIConfiguration() {
            // Generate test parameters first
            generateTestParameters();

            // Scroll to test section
            $('#testAPISection').collapse('show');
            $('html, body').animate({
                scrollTop: $('#testAPISection').offset().top - 100
            }, 500);
        }

        function resetForm() {
            // Reset form fields
            $('#txtAPIName').val('');
            $('#ddlAPIType').val('0');
            $('#txtBaseURL').val('');
            $('#txtEndpointPath').val('');
            $('#txtFullURLPreview').val('');
            $('#chkIsActive').prop('checked', true);

            // Reset authentication fields
            $('#ddlAuthType').val('None');
            $('#txtUsername').val('');
            $('#txtPassword').val('');
            $('#txtAPIKeyName').val('');
            $('#txtAPIKeyValue').val('');
            $('#txtTokenURL').val('');
            $('#txtClientID').val('');
            $('#txtClientSecret').val('');
            $('#txtBearerToken').val('');
            $('#txtAuthPreview').val('');

            // Reset request settings fields
            $('#ddlContentType').val('');
            $('#txtRequestBody').val('');
            $('#txtRequestPreview').val('');

            // Reset response handling fields
            $('#txtSuccessKeyPath').val('');
            $('#txtExpectedStatusCodes').val('');
            $('#txtErrorKeyPath').val('');
            $('#ddlResponseFormat').val('JSON');
            $('#txtResponseTimeout').val('30');
            $('#txtRetryAttempts').val('3');
            $('#txtResponsePreview').val('');

            // Reset key-value grids to single empty row
            resetKeyValueGrid('#headersContainer', 'header-row', 'header-key', 'header-value', 'Header Name (e.g., Content-Type)', 'Header Value (e.g., application/json)', 'remove-header');
            resetKeyValueGrid('#queryParamsContainer', 'query-param-row', 'query-key', 'query-value', 'Parameter Name (e.g., limit)', 'Parameter Value (e.g., 10)', 'remove-query-param');
            resetKeyValueGrid('#pathParamsContainer', 'path-param-row', 'path-key', 'path-value', 'Parameter Name (e.g., id)', 'Parameter Value (e.g., 123)', 'remove-path-param');
            resetResponseMappingGrid();

            // Hide all auth fields
            $('.auth-field').hide();

            // Reset validation states
            $('.form-control, .form-select').removeClass('is-invalid');
            $('.invalid-feedback').hide();
        }

        function initializeValidation() {
            // Initialize any custom validation if needed
            if (typeof BCMValidation !== 'undefined') {
                BCMValidation.init();
            }
        }

        function handleAuthTypeChange() {
            var authType = $('#ddlAuthType').val();

            // Hide all auth fields first
            $('.auth-field').hide();

            // Show relevant fields based on auth type
            switch (authType) {
                case 'Basic':
                    $('.basic-auth').show();
                    break;
                case 'APIKey':
                    $('.api-key-auth').show();
                    break;
                case 'OAuth2':
                    $('.oauth2-auth').show();
                    break;
                case 'JWT':
                    $('.jwt-auth').show();
                    break;
                case 'None':
                default:
                    // No additional fields needed
                    break;
            }
        }

        function updateAuthPreview() {
            var authType = $('#ddlAuthType').val();
            var preview = '';

            switch (authType) {
                case 'Basic':
                    var username = $('#txtUsername').val();
                    var password = $('#txtPassword').val();
                    if (username && password) {
                        preview = 'Authorization: Basic ' + btoa(username + ':' + password);
                    }
                    break;
                case 'APIKey':
                    var keyName = $('#txtAPIKeyName').val();
                    var keyValue = $('#txtAPIKeyValue').val();
                    if (keyName && keyValue) {
                        preview = keyName + ': ' + keyValue;
                    }
                    break;
                case 'OAuth2':
                    var tokenURL = $('#txtTokenURL').val();
                    var clientID = $('#txtClientID').val();
                    var clientSecret = $('#txtClientSecret').val();
                    if (tokenURL && clientID && clientSecret) {
                        preview = 'Token URL: ' + tokenURL + '\nClient ID: ' + clientID + '\nClient Secret: [HIDDEN]';
                    }
                    break;
                case 'JWT':
                    var bearerToken = $('#txtBearerToken').val();
                    if (bearerToken) {
                        preview = 'Authorization: Bearer ' + bearerToken.substring(0, 20) + '...';
                    }
                    break;
                case 'None':
                default:
                    preview = 'No authentication required';
                    break;
            }

            $('#txtAuthPreview').val(preview);
        }

        function togglePasswordVisibility(inputSelector, buttonSelector) {
            var input = $(inputSelector);
            var button = $(buttonSelector);
            var icon = button.find('i');

            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                icon.removeClass('cv-eye').addClass('cv-eye-slash');
            } else {
                input.attr('type', 'password');
                icon.removeClass('cv-eye-slash').addClass('cv-eye');
            }
        }

        // Request Settings Helper Functions
        function addHeaderRow() {
            var newRow = createKeyValueRow('header-row', 'header-key', 'header-value', 'Header Name (e.g., Content-Type)', 'Header Value (e.g., application/json)', 'remove-header');
            $('#headersContainer').append(newRow);
        }

        function addQueryParamRow() {
            var newRow = createKeyValueRow('query-param-row', 'query-key', 'query-value', 'Parameter Name (e.g., limit)', 'Parameter Value (e.g., 10)', 'remove-query-param');
            $('#queryParamsContainer').append(newRow);
        }

        function addPathParamRow() {
            var newRow = createKeyValueRow('path-param-row', 'path-key', 'path-value', 'Parameter Name (e.g., id)', 'Parameter Value (e.g., 123)', 'remove-path-param');
            $('#pathParamsContainer').append(newRow);
        }

        function createKeyValueRow(rowClass, keyClass, valueClass, keyPlaceholder, valuePlaceholder, removeClass) {
            return `
                        <div class="row ${rowClass} mb-2">
                            <div class="col-5">
                                <input type="text" class="form-control form-control-sm ${keyClass}" placeholder="${keyPlaceholder}" />
                            </div>
                            <div class="col-6">
                                <input type="text" class="form-control form-control-sm ${valueClass}" placeholder="${valuePlaceholder}" />
                            </div>
                            <div class="col-1">
                                <button type="button" class="btn btn-outline-danger btn-sm ${removeClass}" title="Remove">
                                    <i class="cv-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
        }

        function removeRow(button, rowSelector, containerSelector) {
            var container = $(containerSelector);
            var rows = container.find(rowSelector);

            if (rows.length > 1) {
                button.closest(rowSelector).remove();
            } else {
                // Clear the last row instead of removing it
                var row = button.closest(rowSelector);
                row.find('input').val('');
            }
        }

        function resetKeyValueGrid(containerSelector, rowClass, keyClass, valueClass, keyPlaceholder, valuePlaceholder, removeClass) {
            var container = $(containerSelector);
            container.empty();
            var newRow = createKeyValueRow(rowClass, keyClass, valueClass, keyPlaceholder, valuePlaceholder, removeClass);
            container.append(newRow);
        }

        function collectKeyValuePairs(rowSelector, keySelector, valueSelector) {
            var pairs = [];
            $(rowSelector).each(function () {
                var key = $(this).find(keySelector).val().trim();
                var value = $(this).find(valueSelector).val().trim();
                if (key && value) {
                    pairs.push({ key: key, value: value });
                }
            });
            return pairs;
        }

        function updateRequestPreview() {
            var apiType = $('#ddlAPIType').val();
            var fullURL = $('#txtFullURLPreview').val();
            var contentType = $('#ddlContentType').val();
            var requestBody = $('#txtRequestBody').val();

            if (!apiType || !fullURL) {
                $('#txtRequestPreview').val('Complete API Basic Details to see request preview');
                return;
            }

            var preview = '';

            // Request line
            preview += apiType + ' ' + fullURL;

            // Add query parameters to URL
            var queryParams = collectKeyValuePairs('.query-param-row', '.query-key', '.query-value');
            if (queryParams.length > 0) {
                var queryString = queryParams.map(p => p.key + '=' + encodeURIComponent(p.value)).join('&');
                preview += (fullURL.includes('?') ? '&' : '?') + queryString;
            }

            preview += '\n\n';

            // Headers
            preview += 'Headers:\n';
            var headers = collectKeyValuePairs('.header-row', '.header-key', '.header-value');

            // Add Content-Type if specified
            if (contentType) {
                preview += 'Content-Type: ' + contentType + '\n';
            }

            // Add custom headers
            headers.forEach(function (header) {
                preview += header.key + ': ' + header.value + '\n';
            });

            // Add authentication header if applicable
            var authType = $('#ddlAuthType').val();
            if (authType === 'Basic') {
                var username = $('#txtUsername').val();
                var password = $('#txtPassword').val();
                if (username && password) {
                    preview += 'Authorization: Basic ' + btoa(username + ':' + password) + '\n';
                }
            } else if (authType === 'APIKey') {
                var keyName = $('#txtAPIKeyName').val();
                var keyValue = $('#txtAPIKeyValue').val();
                if (keyName && keyValue) {
                    preview += keyName + ': ' + keyValue + '\n';
                }
            } else if (authType === 'JWT') {
                var bearerToken = $('#txtBearerToken').val();
                if (bearerToken) {
                    preview += 'Authorization: Bearer ' + bearerToken + '\n';
                }
            }

            // Path parameters info
            var pathParams = collectKeyValuePairs('.path-param-row', '.path-key', '.path-value');
            if (pathParams.length > 0) {
                preview += '\nPath Parameters:\n';
                pathParams.forEach(function (param) {
                    preview += param.key + ' = ' + param.value + '\n';
                });
            }

            // Request body for POST, PUT, PATCH
            if (['POST', 'PUT', 'PATCH'].includes(apiType) && requestBody) {
                preview += '\nRequest Body:\n';
                preview += requestBody;
            }

            $('#txtRequestPreview').val(preview);
        }

        // Response Handling Helper Functions
        function addResponseMappingRow() {
            var newRow = createResponseMappingRow();
            $('#responseMappingContainer').append(newRow);
        }

        function createResponseMappingRow() {
            return `
                        <div class="row response-mapping-row mb-2">
                            <div class="col-4">
                                <input type="text" class="form-control form-control-sm response-field" placeholder="Response Field Path (e.g., data.employee.id)" />
                            </div>
                            <div class="col-4">
                                <select class="form-select form-select-sm internal-field">
                                    <option value="">-- Select Internal Field --</option>
                                    <option value="EmployeeID">Employee ID</option>
                                    <option value="EmployeeName">Employee Name</option>
                                    <option value="EmployeeEmail">Employee Email</option>
                                    <option value="DepartmentID">Department ID</option>
                                    <option value="DepartmentName">Department Name</option>
                                    <option value="ManagerID">Manager ID</option>
                                    <option value="JobTitle">Job Title</option>
                                    <option value="HireDate">Hire Date</option>
                                    <option value="Salary">Salary</option>
                                    <option value="Status">Status</option>
                                    <option value="Custom">Custom Field</option>
                                </select>
                            </div>
                            <div class="col-3">
                                <input type="text" class="form-control form-control-sm custom-field" placeholder="Custom field name" style="display: none;" />
                            </div>
                            <div class="col-1">
                                <button type="button" class="btn btn-outline-danger btn-sm remove-response-mapping" title="Remove Mapping">
                                    <i class="cv-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
        }

        function resetResponseMappingGrid() {
            var container = $('#responseMappingContainer');
            container.empty();
            var newRow = createResponseMappingRow();
            container.append(newRow);
        }

        function collectResponseMappings() {
            var mappings = [];
            $('.response-mapping-row').each(function () {
                var responseField = $(this).find('.response-field').val().trim();
                var internalField = $(this).find('.internal-field').val();
                var customField = $(this).find('.custom-field').val().trim();

                if (responseField && internalField) {
                    var mapping = {
                        responseField: responseField,
                        internalField: internalField === 'Custom' ? customField : internalField
                    };
                    if (mapping.internalField) {
                        mappings.push(mapping);
                    }
                }
            });
            return mappings;
        }

        function updateResponsePreview() {
            var successKeyPath = $('#txtSuccessKeyPath').val().trim();
            var expectedStatusCodes = $('#txtExpectedStatusCodes').val().trim();
            var errorKeyPath = $('#txtErrorKeyPath').val().trim();
            var responseFormat = $('#ddlResponseFormat').val();
            var responseTimeout = $('#txtResponseTimeout').val();
            var retryAttempts = $('#txtRetryAttempts').val();
            var mappings = collectResponseMappings();

            var preview = '';

            // Response Configuration
            preview += 'Response Configuration:\n';
            preview += '- Format: ' + responseFormat + '\n';
            preview += '- Timeout: ' + responseTimeout + ' seconds\n';
            preview += '- Retry Attempts: ' + retryAttempts + '\n';

            if (expectedStatusCodes) {
                preview += '- Expected Status Codes: ' + expectedStatusCodes + '\n';
            }

            preview += '\n';

            // Success Handling
            if (successKeyPath) {
                preview += 'Success Data Extraction:\n';
                preview += '- Key Path: ' + successKeyPath + '\n';
                preview += '- Example: response.' + successKeyPath + '\n\n';
            }

            // Error Handling
            if (errorKeyPath) {
                preview += 'Error Data Extraction:\n';
                preview += '- Key Path: ' + errorKeyPath + '\n';
                preview += '- Example: response.' + errorKeyPath + '\n\n';
            }

            // Field Mappings
            if (mappings.length > 0) {
                preview += 'Response Field Mappings:\n';
                mappings.forEach(function (mapping) {
                    preview += '- ' + mapping.responseField + ' → ' + mapping.internalField + '\n';
                });
                preview += '\n';
            }

            // Sample Processing Logic
            if (successKeyPath || mappings.length > 0) {
                preview += 'Sample Processing Logic:\n';
                preview += '1. Check if status code is in expected range\n';
                if (successKeyPath) {
                    preview += '2. Extract success data from: ' + successKeyPath + '\n';
                }
                if (mappings.length > 0) {
                    preview += '3. Map response fields to internal schema:\n';
                    mappings.forEach(function (mapping) {
                        preview += '   - Set ' + mapping.internalField + ' = response.' + mapping.responseField + '\n';
                    });
                }
                if (errorKeyPath) {
                    preview += '4. On error, extract message from: ' + errorKeyPath + '\n';
                }
                preview += '5. Retry up to ' + retryAttempts + ' times on failure\n';
            }

            if (!preview.trim()) {
                preview = 'Configure response handling settings to see preview';
            }

            $('#txtResponsePreview').val(preview);
        }

        // Test API Integration Helper Functions
        function generateTestParameters() {
            var container = $('#testParametersContainer');
            container.empty();

            var hasParameters = false;
            var parametersHtml = '<div class="row">';

            // Generate path parameters
            var pathParams = collectKeyValuePairs('.path-param-row', '.path-key', '.path-value');
            if (pathParams.length > 0) {
                hasParameters = true;
                parametersHtml += '<div class="col-12 mb-3"><h6 class="text-primary">Path Parameters</h6>';
                pathParams.forEach(function (param) {
                    if (param.key && param.value) {
                        parametersHtml += `
                                    <div class="form-group mb-2">
                                        <label class="form-label">${param.key}</label>
                                        <input type="text" class="form-control form-control-sm test-path-param"
                                               data-key="${param.key}" value="${param.value}"
                                               placeholder="Enter value for ${param.key}" />
                                    </div>
                                `;
                    }
                });
                parametersHtml += '</div>';
            }

            // Generate query parameters
            var queryParams = collectKeyValuePairs('.query-param-row', '.query-key', '.query-value');
            if (queryParams.length > 0) {
                hasParameters = true;
                parametersHtml += '<div class="col-12 mb-3"><h6 class="text-primary">Query Parameters</h6>';
                queryParams.forEach(function (param) {
                    if (param.key && param.value) {
                        parametersHtml += `
                                    <div class="form-group mb-2">
                                        <label class="form-label">${param.key}</label>
                                        <input type="text" class="form-control form-control-sm test-query-param"
                                               data-key="${param.key}" value="${param.value}"
                                               placeholder="Enter value for ${param.key}" />
                                    </div>
                                `;
                    }
                });
                parametersHtml += '</div>';
            }

            // Generate request body template
            var apiType = $('#ddlAPIType').val();
            var requestBody = $('#txtRequestBody').val().trim();
            if (['POST', 'PUT', 'PATCH'].includes(apiType) && requestBody) {
                hasParameters = true;
                parametersHtml += `
                            <div class="col-12 mb-3">
                                <h6 class="text-primary">Request Body</h6>
                                <div class="form-group">
                                    <label class="form-label">Request Body (JSON)</label>
                                    <textarea class="form-control" id="testRequestBody" rows="6"
                                              placeholder="Enter request body JSON">${requestBody}</textarea>
                                    <small class="text-muted">Use actual values instead of template variables like {{variableName}}</small>
                                </div>
                            </div>
                        `;
            }

            parametersHtml += '</div>';

            if (hasParameters) {
                container.html(parametersHtml);
            } else {
                container.html(`
                            <div class="text-muted text-center py-3">
                                <i class="cv-info me-2"></i>
                                No parameters configured. The API will be called with basic settings only.
                            </div>
                        `);
            }
        }

        function sendTestRequest() {
            // Validate basic configuration
            if (!validateForm()) {
                alert('Please complete the API configuration before testing.');
                return;
            }

            // Show loading state
            $('#testSpinner').show();
            $('#testStatusBadge').removeClass('bg-success bg-danger bg-warning').addClass('bg-primary').text('Testing...').show();
            $('#btnSendTestRequest').prop('disabled', true);

            // Collect test data
            var testData = collectTestData();

            // Add to test history
            addToTestHistory('Testing...', new Date(), null, 'pending');

            // Simulate API call (replace with actual AJAX call)
            setTimeout(function () {
                simulateAPIResponse(testData);
            }, 2000);
        }

        function collectTestData() {
            var data = {
                method: $('#ddlAPIType').val(),
                url: buildTestURL(),
                headers: buildTestHeaders(),
                body: $('#testRequestBody').val() || null,
                timeout: parseInt($('#txtResponseTimeout').val()) || 30
            };
            return data;
        }

        function buildTestURL() {
            var baseURL = $('#txtBaseURL').val().trim();
            var endpointPath = $('#txtEndpointPath').val().trim();

            // Replace path parameters
            var url = baseURL + endpointPath;
            $('.test-path-param').each(function () {
                var key = $(this).data('key');
                var value = $(this).val();
                url = url.replace('{' + key + '}', encodeURIComponent(value));
            });

            // Add query parameters
            var queryParams = [];
            $('.test-query-param').each(function () {
                var key = $(this).data('key');
                var value = $(this).val();
                if (key && value) {
                    queryParams.push(key + '=' + encodeURIComponent(value));
                }
            });

            if (queryParams.length > 0) {
                url += (url.includes('?') ? '&' : '?') + queryParams.join('&');
            }

            return url;
        }

        function buildTestHeaders() {
            var headers = {};

            // Add content type
            var contentType = $('#ddlContentType').val();
            if (contentType) {
                headers['Content-Type'] = contentType;
            }

            // Add custom headers
            var customHeaders = collectKeyValuePairs('.header-row', '.header-key', '.header-value');
            customHeaders.forEach(function (header) {
                headers[header.key] = header.value;
            });

            // Add authentication headers
            var authType = $('#ddlAuthType').val();
            if (authType === 'Basic') {
                var username = $('#txtUsername').val();
                var password = $('#txtPassword').val();
                if (username && password) {
                    headers['Authorization'] = 'Basic ' + btoa(username + ':' + password);
                }
            } else if (authType === 'APIKey') {
                var keyName = $('#txtAPIKeyName').val();
                var keyValue = $('#txtAPIKeyValue').val();
                if (keyName && keyValue) {
                    headers[keyName] = keyValue;
                }
            } else if (authType === 'JWT') {
                var bearerToken = $('#txtBearerToken').val();
                if (bearerToken) {
                    headers['Authorization'] = 'Bearer ' + bearerToken;
                }
            }

            return headers;
        }

        function simulateAPIResponse(testData) {
            // This is a simulation - replace with actual AJAX call
            var simulatedResponse = {
                status: 200,
                statusText: 'OK',
                responseTime: Math.floor(Math.random() * 1000) + 200,
                data: {
                    success: true,
                    data: {
                        id: 12345,
                        employee: {
                            name: 'John Doe',
                            email: '<EMAIL>',
                            department: 'IT',
                            position: 'Software Developer'
                        },
                        timestamp: new Date().toISOString()
                    },
                    message: 'Request processed successfully'
                }
            };

            // Simulate different response scenarios
            var scenarios = ['success', 'error', 'timeout'];
            var scenario = scenarios[Math.floor(Math.random() * scenarios.length)];

            if (scenario === 'error') {
                simulatedResponse.status = 400;
                simulatedResponse.statusText = 'Bad Request';
                simulatedResponse.data = {
                    success: false,
                    error: {
                        message: 'Invalid request parameters',
                        code: 'INVALID_PARAMS'
                    }
                };
            } else if (scenario === 'timeout') {
                simulatedResponse.status = 408;
                simulatedResponse.statusText = 'Request Timeout';
                simulatedResponse.data = {
                    success: false,
                    error: {
                        message: 'Request timed out',
                        code: 'TIMEOUT'
                    }
                };
            }

            displayTestResults(simulatedResponse, testData);
        }

        function displayTestResults(response, testData) {
            // Hide loading state
            $('#testSpinner').hide();
            $('#btnSendTestRequest').prop('disabled', false);

            // Update status
            var statusClass = response.status >= 200 && response.status < 300 ? 'bg-success' : 'bg-danger';
            $('#testStatusBadge').removeClass('bg-primary bg-success bg-danger bg-warning')
                .addClass(statusClass)
                .text(response.status + ' ' + response.statusText);

            // Display response details
            $('#txtResponseStatus').val(response.status + ' ' + response.statusText);
            $('#txtResponseTime').val(response.responseTime + ' ms');
            $('#txtRawResponse').val(JSON.stringify(response.data, null, 2));

            // Parse and display structured output
            displayParsedOutput(response.data);

            // Display field mapping results
            displayFieldMappingResults(response.data);

            // Add to test history
            updateTestHistory(response, testData);
        }

        function displayParsedOutput(responseData) {
            var container = $('#parsedOutputContainer');
            var successKeyPath = $('#txtSuccessKeyPath').val().trim();

            if (successKeyPath) {
                try {
                    var extractedData = extractDataByPath(responseData, successKeyPath);
                    container.html(`
                                <div class="mb-2">
                                    <strong>Extracted Data (${successKeyPath}):</strong>
                                </div>
                                <pre class="bg-light p-2 rounded">${JSON.stringify(extractedData, null, 2)}</pre>
                            `);
                } catch (e) {
                    container.html(`
                                <div class="text-danger">
                                    <i class="cv-error me-1"></i>
                                    Error extracting data from path "${successKeyPath}": ${e.message}
                                </div>
                            `);
                }
            } else {
                container.html(`
                            <div class="text-muted">
                                <i class="cv-info me-1"></i>
                                Configure Success Key Path in Response Handling to see extracted data
                            </div>
                        `);
            }
        }

        function displayFieldMappingResults(responseData) {
            var container = $('#fieldMappingResults');
            var mappings = collectResponseMappings();

            if (mappings.length > 0) {
                var resultsHtml = '<div class="mb-2"><strong>Mapped Fields:</strong></div>';

                mappings.forEach(function (mapping) {
                    try {
                        var value = extractDataByPath(responseData, mapping.responseField);
                        resultsHtml += `
                                    <div class="d-flex justify-content-between align-items-center mb-1 p-1 bg-light rounded">
                                        <span class="text-muted small">${mapping.internalField}:</span>
                                        <span class="fw-bold">${JSON.stringify(value)}</span>
                                    </div>
                                `;
                    } catch (e) {
                        resultsHtml += `
                                    <div class="d-flex justify-content-between align-items-center mb-1 p-1 bg-danger bg-opacity-10 rounded">
                                        <span class="text-muted small">${mapping.internalField}:</span>
                                        <span class="text-danger small">Error: ${e.message}</span>
                                    </div>
                                `;
                    }
                });

                container.html(resultsHtml);
            } else {
                container.html(`
                            <div class="text-muted">
                                <i class="cv-info me-1"></i>
                                Configure Response Field Mappings to see mapped data
                            </div>
                        `);
            }
        }

        function extractDataByPath(obj, path) {
            return path.split('.').reduce((current, key) => {
                if (current === null || current === undefined) {
                    throw new Error(`Cannot access property '${key}' of ${current}`);
                }
                if (key.includes('[') && key.includes(']')) {
                    // Handle array notation like 'items[0]'
                    var arrayKey = key.substring(0, key.indexOf('['));
                    var index = parseInt(key.substring(key.indexOf('[') + 1, key.indexOf(']')));
                    if (current[arrayKey] && Array.isArray(current[arrayKey])) {
                        return current[arrayKey][index];
                    } else {
                        throw new Error(`Property '${arrayKey}' is not an array or doesn't exist`);
                    }
                }
                return current[key];
            }, obj);
        }

        function addToTestHistory(status, timestamp, response, type) {
            var container = $('#testHistoryContainer');
            var historyItem = `
                        <div class="d-flex justify-content-between align-items-center p-2 border-bottom test-history-item" data-type="${type}">
                            <div>
                                <span class="badge ${type === 'pending' ? 'bg-primary' : (type === 'success' ? 'bg-success' : 'bg-danger')}">${status}</span>
                                <small class="text-muted ms-2">${timestamp.toLocaleTimeString()}</small>
                            </div>
                            <div>
                                ${response ? `<small class="text-muted">${response.responseTime} ms</small>` : ''}
                            </div>
                        </div>
                    `;

            if (container.find('.test-history-item').length === 0) {
                container.empty();
            }

            container.prepend(historyItem);
        }

        function updateTestHistory(response, testData) {
            var type = response.status >= 200 && response.status < 300 ? 'success' : 'error';
            var status = response.status + ' ' + response.statusText;

            // Remove pending item and add completed item
            $('.test-history-item[data-type="pending"]').remove();
            addToTestHistory(status, new Date(), response, type);
        }

        function clearTestResults() {
            $('#txtResponseStatus').val('');
            $('#txtResponseTime').val('');
            $('#txtRawResponse').val('');
            $('#testStatusBadge').hide();

            $('#parsedOutputContainer').html(`
                        <div class="text-muted text-center py-3">
                            <i class="cv-info me-2"></i>
                            Parsed response data will be displayed here
                        </div>
                    `);

            $('#fieldMappingResults').html(`
                        <div class="text-muted text-center py-3">
                            <i class="cv-info me-2"></i>
                            Field mapping results will be displayed here
                        </div>
                    `);
        }

        function formatJSONResponse() {
            var rawResponse = $('#txtRawResponse').val();
            if (rawResponse) {
                try {
                    var parsed = JSON.parse(rawResponse);
                    $('#txtRawResponse').val(JSON.stringify(parsed, null, 2));
                } catch (e) {
                    alert('Invalid JSON format: ' + e.message);
                }
            }
        }

        function copyResponseToClipboard() {
            var rawResponse = $('#txtRawResponse').val();
            if (rawResponse) {
                navigator.clipboard.writeText(rawResponse).then(function () {
                    // Show temporary success message
                    var originalText = $('#btnCopyResponse').html();
                    $('#btnCopyResponse').html('<i class="cv-check me-1"></i>Copied!');
                    setTimeout(function () {
                        $('#btnCopyResponse').html(originalText);
                    }, 2000);
                }).catch(function () {
                    alert('Failed to copy to clipboard');
                });
            }
        }

        function toggleParsedView() {
            var button = $('#btnToggleView');
            var currentView = button.data('view');

            if (currentView === 'structured') {
                button.data('view', 'raw').html('<i class="cv-view me-1"></i>Raw View');
                // Switch to raw view of parsed data
            } else {
                button.data('view', 'structured').html('<i class="cv-view me-1"></i>Structured View');
                // Switch to structured view
            }
        }

        function clearTestHistory() {
            $('#testHistoryContainer').html(`
                        <div class="text-muted text-center py-2">
                            <small>Test history will be displayed here</small>
                        </div>
                    `);
        }

        function getAPIConfigurationData() {
            // Get data from API Basic Details Section
            const apiBasicDetails = {
                apiName: $('#txtAPIName').val().trim(),
                apiType: $('#ddlAPIType').val(),
                baseURL: $('#txtBaseURL').val().trim(),
                endpointPath: $('#txtEndpointPath').val().trim(),
                isActive: $('#chkIsActive').is(':checked')
            };

            // Get data from Authentication Section
            const authType = $('#ddlAuthType').val();
            const authentication = {
                authType: authType,
                username: authType === 'Basic' ? $('#txtUsername').val().trim() : null,
                password: authType === 'Basic' ? $('#txtPassword').val().trim() : null,
                apiKeyName: authType === 'APIKey' ? $('#txtAPIKeyName').val().trim() : null,
                apiKeyValue: authType === 'APIKey' ? $('#txtAPIKeyValue').val().trim() : null,
                tokenURL: authType === 'OAuth2' ? $('#txtTokenURL').val().trim() : null,
                clientID: authType === 'OAuth2' ? $('#txtClientID').val().trim() : null,
                clientSecret: authType === 'OAuth2' ? $('#txtClientSecret').val().trim() : null,
                bearerToken: authType === 'JWT' ? $('#txtBearerToken').val().trim() : null
            };

            return { apiBasicDetails, authentication };
        }
    </script>
}