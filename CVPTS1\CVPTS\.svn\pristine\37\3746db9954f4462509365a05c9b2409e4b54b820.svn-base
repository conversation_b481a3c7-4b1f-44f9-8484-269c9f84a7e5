﻿@using BCM.Shared
@inject BCM.Shared.Utilities _Utilities
@using BCM.BusinessClasses
@using BCM.BusinessFacadeSrv
@inject BCM.BusinessFacadeSrv.ProcessSrv _ProcessSrv
@using Newtonsoft.Json

@using Microsoft.AspNetCore.Http

<!DOCTYPE html>
<html lang="en">

<head>
    <script src="~/lib/jquery/jquery.min.js"></script>
    <script src="~/lib/ui-lightness/jquery-ui.min.js"></script>
    <script src="~/lib/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery.steps/jquery.steps.min.js"></script>
    <script src="~/lib/selectize/selectize.min.js"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <script src="~/js/global-validation.js"></script>
    <script src="~/js/managerecoveryplan.js"></script>
</head>
<body class=" position-relative" id="container">


    @* <main role="main" class="container-fluid">
        @RenderBody()
    </main> *@
    @RenderBody()

    @* @await RenderSectionAsync("Scripts", required: false) *@
  
</body>
</html>

