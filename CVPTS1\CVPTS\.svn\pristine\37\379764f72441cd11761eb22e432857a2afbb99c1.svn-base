﻿@model BCM.BusinessClasses.ManageBIAProfileSectionMain
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@* <form asp-action="DeleteBIAProfile" method="post">
    <div>
        <input type="hidden" id="ProfileId" asp-for="ID" />        
    </div>
    <div class="modal-header d-grid text-center">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold">@Model.ProfileName</span>" ?</span>
    </div>
    <div class="modal-body text-center">
        <img src="~/img/isomatric/delete.svg" width="260" />
    </div>
    <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" id="btndelete" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form> *@


<form asp-action="DeleteBIAProfile" method="post">
    <div>
        <input type="hidden" asp-for="ID" /> @* asp-for="ProfileId" *@
    </div>
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    <div class="modal-body d-grid px">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="OrgGroupSpan">@Model.ProfileName</span>" ?</span>
    </div>
    <div class="modal-footer justify-content-center p-0">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>



<script>
    $(document).ready(function () {
        debugger;
        $(document).ready(function () {
            $('#btndelete').click(function () {
                debugger;
                var objdata =
                {
                    ResourceId: $('#ProfileId').val(),
                }

                $.ajax({
                    type: "POST",
                    url: '/BCMBIAProfile/ManageBIAProfile/DeleteBIAProfile',
                    data: JSON.stringify(objdata),
                    contentType: 'application/json',
                    dataType: 'JSON',
                    success: function (response) {
                        debugger;
                        if (response.success) {
                        }
                        else {
                        }
                    },
                    error: function (data) {
                        console.log('error is invoked');
                    }
                });

            });
        });
    });
</script>