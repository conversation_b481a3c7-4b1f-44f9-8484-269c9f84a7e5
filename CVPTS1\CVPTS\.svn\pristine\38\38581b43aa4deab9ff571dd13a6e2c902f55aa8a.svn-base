﻿// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

// Create chart instance - ensure the element exists first
var overallKPIChart;

// Make sure the chart is created only if the element exists
if (document.getElementById("OverallKPI_Chart")) {
    overallKPIChart = am4core.create("OverallKPI_Chart", am4charts.PieChart);
    overallKPIChart.className = "pie-chart-improved";
    if (overallKPIChart.logo) {
        overallKPIChart.logo.disabled = true;
    }
} else {
    console.error("Element OverallKPI_Chart not found in the DOM");
}

// Make chart responsive - only if chart was created
if (overallKPIChart) {
    overallKPIChart.responsive.enabled = true;
    overallKPIChart.responsive.rules.push({
        relevant: function () {
            // Apply to all screen sizes
            return true;
        },
        state: function (target, stateId) {
            if (target instanceof am4charts.PieSeries) {
                var state = target.states.create(stateId);
                // Adjust inner radius based on screen size
                if (target.chart && target.chart.pixelWidth && target.chart.pixelWidth <= 400) {
                    state.properties.innerRadius = am4core.percent(40);
                }
                return state;
            }
            if (target instanceof am4charts.Legend) {
                var state = target.states.create(stateId);
                // Position legend based on screen size
                if (target.chart && target.chart.pixelWidth) {
                    if (target.chart.pixelWidth <= 600) {
                        state.properties.position = "bottom";
                        state.properties.width = undefined;
                        if (target.chart.pixelWidth <= 400) {
                           // state.properties.maxHeight = 80;
                        }
                    } else {
                        state.properties.position = "bottom";
                        //state.properties.width = 120;
                    }
                } else {
                    // Default position if pixelWidth is not available
                    state.properties.position = "bottom";
                    //state.properties.width = 120;
                }
                return state;
            }
            if (target instanceof am4charts.Chart) {
                var state = target.states.create(stateId);
                // Adjust padding based on screen size
                if (target.pixelWidth && target.pixelWidth <= 400) {
                    state.properties.paddingTop = 0;
                    state.properties.paddingRight = 0;
                    state.properties.paddingBottom = 0;
                    state.properties.paddingLeft = 0;
                } else {
                    state.properties.paddingTop = 10;
                    state.properties.paddingRight = 15;
                    state.properties.paddingBottom = 10;
                    state.properties.paddingLeft = 15;
                }
                return state;
            }
            return null;
        }
    });
}

if (overallKPIChart) {
    overallKPIChart.data = [{
        "category": "Completed",
        "value": 75,
        "percentage": 75,
        "color": am4core.color("#ffbd01")
    }, {
        "category": "In Progress",
        "value": 25,
        "percentage": 25,
        "color": am4core.color("#046cff")
    }];

    overallKPIChart.paddingTop = 10;
    overallKPIChart.paddingBottom = 10;
    overallKPIChart.paddingLeft = 15;
    overallKPIChart.paddingRight = 15;
    overallKPIChart.radius = am4core.percent(90); // Increased radius for better visibility

    overallKPIChart.background.fill = am4core.color("#ffffff");
    overallKPIChart.background.fillOpacity = 0;

    // Add and configure Series
    var pieSeries = overallKPIChart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "value";
    pieSeries.dataFields.category = "category";
    pieSeries.slices.template.propertyFields.fill = "color";
    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 1; // Default stroke width
    pieSeries.slices.template.strokeOpacity = 1;

    // Default hover effect
    pieSeries.slices.template.states.getKey("hover").properties.scale = 1.05;

    // Make slices bigger
    pieSeries.slices.template.tooltipText = "{category}: {value}";
    pieSeries.slices.template.cornerRadius = 5;

    // Let's cut a hole in our Pie chart the size of 30% the radius
    overallKPIChart.innerRadius = am4core.percent(70);

    // Add a legend
    overallKPIChart.legend = new am4charts.Legend();
    // Position will be set by responsive rules
    overallKPIChart.legend.fontSize = 12;
    overallKPIChart.legend.marginTop = 0;
    overallKPIChart.legend.marginLeft = 15; // Added left margin
    overallKPIChart.legend.markers.template.width = 12;
    overallKPIChart.legend.markers.template.height = 12;
    overallKPIChart.legend.contentAlign = "left"; // Align content to the left

    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;
    pieSeries.alignLabels = false;

    pieSeries.legendSettings.itemValueText = "{percentage}%";
    pieSeries.legendSettings.valueText = "{percentage}%";

    overallKPIChart.legend.valueLabels.template.fontSize = 12;
    overallKPIChart.legend.valueLabels.template.fontWeight = "bold";
    overallKPIChart.legend.labels.template.fontSize = 12;
    overallKPIChart.legend.useDefaultMarker = true;

}
