﻿using BCM.UI.Areas.BCMReports.ReportModels.BIAReport;
using BCM.UI.Controllers.PreBuildReport;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace BCM.UI.Areas.BCMReports.ReportTemplate;

public partial class BIAReport : DevExpress.XtraReports.UI.XtraReport
{
    public BIAReport(string biareportdata)
    {
        InitializeComponent();

        if (!string.IsNullOrWhiteSpace(biareportdata))
        {
            var reportDetails = JsonConvert.DeserializeObject<JObject>(biareportdata);

            if (reportDetails != null)
            {
                var worksheetOne = reportDetails["processIdentification"]?.ToObject<List<ProcessIdentificationAndCriticalityAssessment>>();
                var worksheetTwo = reportDetails["vitalRecords"]?.ToObject<List<VitalRecordsAndInformation>>();
                var worksheetThree = reportDetails["workspaceRequirements"]?.ToObject<List<BuildingAndWorkspaceRequirements>>();
                var worksheetFour = reportDetails["hrRequirements"]?.ToObject<List<HumanResourcesRequirements>>();
                var worksheetFive = reportDetails["itRequirements"]?.ToObject<List<ITRequirements>>();
                var worksheetSix = reportDetails["workAreaRecovery"]?.ToObject<List<WorkAreaRecoverySupplies>>();
                var worksheetSeven = reportDetails["thirdParties"]?.ToObject<List<ThirdParties>>();

                if (worksheetOne != null && worksheetOne.Count > 0)
                {
                    this.DetailReport.Visible = true;
                    this.DetailReport.DataSource = worksheetOne;
                }
                else
                {
                    this.DetailReport.Visible = false;
                }

                if (worksheetTwo != null && worksheetTwo.Count > 0)
                {
                    this.DetailReport1.Visible = true;
                    this.DetailReport1.DataSource = worksheetTwo;
                }
                else
                {
                    this.DetailReport1.Visible = false;
                }

                if (worksheetThree != null && worksheetThree.Count > 0)
                {
                    this.DetailReport2.Visible = true;
                    this.DetailReport2.DataSource = worksheetThree;
                }
                else
                {
                    this.DetailReport2.Visible = false;
                }

                if (worksheetFour != null && worksheetFour.Count > 0)
                {
                    this.DetailReport3.Visible = true;
                    this.DetailReport3.DataSource = worksheetFour;
                }
                else
                {
                    this.DetailReport3.Visible = false;
                }

                if (worksheetFive != null && worksheetFive.Count > 0)
                {
                    this.DetailReport4.Visible = true;
                    this.DetailReport4.DataSource = worksheetFive;
                }
                else
                {
                    this.DetailReport4.Visible = false;
                }

                if (worksheetSix != null && worksheetSix.Count > 0)
                {
                    this.DetailReport5.Visible = true;
                    this.DetailReport5.DataSource = worksheetSix;
                }
                else
                {
                    this.DetailReport5.Visible = false;
                }

                if (worksheetSeven != null && worksheetSeven.Count > 0)
                {
                    this.DetailReport6.Visible = true;
                    this.DetailReport6.DataSource = worksheetSeven;
                }
                else
                {
                    this.DetailReport6.Visible = false;
                }
            }
        }

        lblUserName.Text = PreBuildReportController.ReportGeneratedBy.ToString();

        this.DisplayName = "BIA Report_" + DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss tt");
    }
    public BIAReport(int orgId, int unitId, int departId, int subdepartId)
    {
        InitializeComponent();
    }
}
