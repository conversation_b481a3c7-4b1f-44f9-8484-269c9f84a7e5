﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using BCM.Shared;
using System.Security.Cryptography;

using System.Linq;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;
using Newtonsoft.Json;
using System.Collections.Generic;
using BCM.UI.Controllers;


namespace BCM.UI.Areas.BCMMenuAccess.Controllers;
[Area("BCMMenuAccess")]
public class ManageMenuSubPageLinkController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    private readonly IHttpContextAccessor _HttpContextAccessor;
    //IFormCollection _HttpContextAccessor;
    public ManageMenuSubPageLinkController(ProcessSrv iProcessSrv, Utilities Utilities, CVLogger cVLogger, IHttpContextAccessor HttpContextAccessor) : base(Utilities)
    {
        _ProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _HttpContextAccessor = HttpContextAccessor;
    }

    [HttpGet]
    public IActionResult ManageMenuSubPageLink()
    {
        //_HttpContextAccessor = httpContextAccessor;


        var HttpContext = _HttpContextAccessor.HttpContext;
        var LoginUserDetailsJSON = HttpContext.Session.GetString("MenuData");
        ViewBag.AllMenuData = _UserDetails;

        if (LoginUserDetailsJSON != null)
        {
            List<MenuRights> objMenuRights = _ProcessSrv.GetMenuDataByRoleID("0", "0");

            var objMenuRights1 = objMenuRights.Select(p => new { p.SubMenuID, p.SubMenuName }).Distinct().ToList();
            //var objUserDetails = JsonConvert.DeserializeObject<List<MenuRights>>(LoginUserDetailsJSON);

            
            ViewBag.AllMenuData = objMenuRights1;
            var lstMainMenu = objMenuRights.GroupBy(x => x.MenuName).Select(x => x.FirstOrDefault());
            ViewBag.MainMenu = lstMainMenu;

            var lstSubMainMenu = objMenuRights.GroupBy(x => x.SubMenuName).Select(x => x.FirstOrDefault());
            var lstPage = _ProcessSrv.GetPageNameAll();
            //var lstPage = objUserDetails.GroupBy(x => x.PageName).Select(x => x.FirstOrDefault());
            ViewBag.SubMenu = lstSubMainMenu;
            ViewBag.lstPage = lstPage;
            //ViewBag.RoleRights = new List<int>();
        }
        return View();
    }

    //[HttpGet]
    //public IActionResult PageList(int iMainMenuID, int iSubMenuID)
    //{
    //    ViewBag.lstPage = null;
    //    List<MenuRights> lstPage = _ProcessSrv.GetPageNameAll();
    //    var LoginUserDetailsJSON = HttpContext.Session.GetString("MenuData");
    //    List<MenuRights> RoleRights = new List<MenuRights>();

    //    RoleRights = _ProcessSrv.GetMenuSubMenuPageRoleRights(Convert.ToInt32(iMainMenuID), Convert.ToInt32(iSubMenuID), 0);

    //    ViewBag.SelectedPages = RoleRights;
    //    List<int> a = new List<int>();
    //    foreach (MenuRights item in RoleRights)
    //    {
    //        foreach (MenuRights item1 in lstPage)
    //        {
    //            if (item.PageID == item1.PageID)
    //            {
    //                item1.IsSelected = true;
    //                a.Add(item1.PageID);
    //            }
    //            else
    //            {
    //                item1.IsSelected = false;
    //            }
    //        }
    //    }
    //    ViewBag.lstPage = lstPage;
    //    ViewBag.RoleRights = a;
    //    ViewBag.RoleRights1 = RoleRights.Select(x=> x.PageID);
    //    //var lstPage = objUserDetails.GroupBy(x => x.).Select(x => x.FirstOrDefault());
    //    return PartialView("_PageList");
    //}

    [HttpGet]
    public IActionResult PageList(int iMainMenuID, int iSubMenuID)
    {
        ViewBag.lstPage = _ProcessSrv.GetPageNameAll();
        var LoginUserDetailsJSON = HttpContext.Session.GetString("MenuData");
        List<MenuRights> lstRoleRights = _ProcessSrv.GetMenuSubMenuPageRoleRights(Convert.ToInt32(iMainMenuID), Convert.ToInt32(iSubMenuID), 0);

        ViewBag.CheckPagesId = lstRoleRights.Select(x => x.PageID).ToList();
        return PartialView("_PageList");
    }



    [HttpPost]
    public IActionResult UpdateClick(List<int> selectedPages, MenuRoleRights objMenuRoleRights)
    {

        List<MenuRights> NewMenu = new List<MenuRights>();
        MenuRoleRights objMenuRoleRight = new MenuRoleRights();
        NewMenu = _ProcessSrv.GetMenuSubMenuPageRoleRights(Convert.ToInt32(objMenuRoleRights.MenuID), Convert.ToInt32(objMenuRoleRights.SubmenuID), 0);
        bool bSucess = false;
        if (objMenuRoleRights != null && selectedPages.Count > 0)
        {
            _ProcessSrv.Menu_Delete(Convert.ToInt32(objMenuRoleRights.MenuID), Convert.ToInt32(objMenuRoleRights.SubmenuID));

            foreach (var item in selectedPages)
            {
                objMenuRoleRight.RoleID = 0;
                objMenuRoleRight.MenuID = objMenuRoleRights.MenuID;
                objMenuRoleRight.SubmenuID = objMenuRoleRights.SubmenuID;
                objMenuRoleRight.PrivilageID = 0;
                objMenuRoleRight.UserID = 0;
                objMenuRoleRight.PageID = Convert.ToInt32(item.ToString());
                objMenuRoleRight.CreatedBy = _UserDetails.UserID;
                bSucess = _ProcessSrv.MenuRoleRightSave(objMenuRoleRight);
            }
        }

        var HttpContext = _HttpContextAccessor.HttpContext;
        var LoginUserDetailsJSON = HttpContext.Session.GetString("MenuData");

        var objUserDetails = JsonConvert.DeserializeObject<List<MenuRights>>(LoginUserDetailsJSON);

        //objUserDetails = objUserDetails.Where(Menu => Menu.MenuID > 0).ToList();
        ViewBag.AllMenuData = objUserDetails;
        var lstMainMenu = objUserDetails.GroupBy(x => x.MenuName).Select(x => x.FirstOrDefault());
        ViewBag.MainMenu = lstMainMenu;

        var lstSubMainMenu = objUserDetails.GroupBy(x => x.SubMenuName).Select(x => x.FirstOrDefault());

        var lstPage = objUserDetails.GroupBy(x => x.PageName).Select(x => x.FirstOrDefault());
        ViewBag.SubMenu = lstSubMainMenu;
        ViewBag.lstPage = lstPage;
        ManageMenuSubPageLink();
        return View("ManageMenuSubPageLink");
    }

    [HttpGet]
    public JsonResult GetAllSubMenu(int iMainMenuID)
    {
        try
        {
            List<MenuRights> objMenuRights = _ProcessSrv.GetMenuDataByRoleID("0", "0");

            var objMenuRights1 = objMenuRights.Select(p => new { p.SubMenuID, p.SubMenuName }).Distinct().ToList();
            var LoginUserDetailsJSON = HttpContext.Session.GetString("MenuData");
            var objUserDetails = JsonConvert.DeserializeObject<List<MenuRights>>(LoginUserDetailsJSON);
            //var lstSubMainMenu = objUserDetails.Where(x => x.MenuID == iMainMenuID).Select(p => new { p.SubMenuID, p.SubMenuName }).Distinct();
            var lstSubMainMenu = objUserDetails.Select(p => new { p.SubMenuID, p.SubMenuName }).Distinct();
            //var objDepartmentList = _ProcessSrv.GetDepartmentByUnitId(iUnitID);
            return Json(objMenuRights1);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    //[HttpGet]
    //public JsonResult GetAllNotIntegratedPages(int iMainMenuID)
    //{
    //    try
    //    {
    //        var LoginUserDetailsJSON = HttpContext.Session.GetString("MenuData");
    //        var objUserDetails = JsonConvert.DeserializeObject<List<MenuRights>>(LoginUserDetailsJSON);
    //        var lstSubMainMenu = objUserDetails.Where(x => x.MenuID == 0).Select(p => new { p.PageID, p.PageName }).Distinct();
    //        //var objDepartmentList = _ProcessSrv.GetDepartmentByUnitId(iUnitID);
    //        return Json(lstSubMainMenu);
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }
    //    return Json(null);
    //}

    [HttpGet]
    public IActionResult GetAllNotIntegratedPages(int iMainMenuID)
    {
        try
        {
            if (iMainMenuID == 1)
            {
                var lstPage = _ProcessSrv.GetPageNameAll();
                ViewBag.lstPage = lstPage;
                var LoginUserDetailsJSON = HttpContext.Session.GetString("MenuData");
                var objUserDetails = JsonConvert.DeserializeObject<List<MenuRights>>(LoginUserDetailsJSON);
                var lstSubMainMenu = objUserDetails.Where(x => x.MenuID == 0).Select(p => new { p.PageID, p.PageName });

                ViewBag.CheckPagesId = lstSubMainMenu.Select(x => x.PageID).ToList();
            }
            else
            {
                ManageMenuSubPageLink();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_PageList");
    }
}

