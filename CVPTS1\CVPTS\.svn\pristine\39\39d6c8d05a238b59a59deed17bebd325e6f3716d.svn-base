﻿@model BCM.BusinessClasses.ReviewType
@{
    ViewBag.Title = "Management Review";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int iCount = 1;
    // int iReviewCount = 1;
    // Group by ReportID/ReportCode first
    var reportGroups = Model.ReviewTypeMaster.GroupBy(x => x.ReportID);
}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Management Review</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" class="btn icon-btn btn-primary btn-sm" id="btnCreate"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant card border-0 ">
    <div class="card-body p-1">
        <div class="list-group list-group-horizontal fw-semibold bg-white">
            <div class="border-0 flex-fill p-2" style="width:50px"></div>
            <div class="border-0 w-25 flex-fill p-2">REPORT CODE</div>
            <div class="border-0 w-25 flex-fill p-2">REPORT NAME</div>
            <div class="border-0 w-25 flex-fill p-2">OWNER NAME</div>
            <div class="border-0 w-25 flex-fill p-2">PLAN DATE</div>
            <div class="border-0 w-25 flex-fill p-2">STATUS</div>
            <div class="border-0 w-25 flex-fill p-2">MOM ITEM SUMMARY</div>
            <div class="border-0 rounded-0 p-2 text-center" style="width: 12%;">Action</div>
        </div>
        <div style="height: calc(100vh - 150px);overflow: auto;">
            @foreach (var reportGroup in reportGroups)
            {
                var reportItem = reportGroup.First();                
                <div class="align-items-center border-top rounded-0 list-group list-group-horizontal">
                    <div class="flex-fill p-2 border-0" style="width:50px">
                        <span class="toggle-password d-block" role="button" data-bs-toggle="collapse" data-bs-target="#reviewtypeparent_@iCount" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                    </div>
                    <div class="w-25 flex-fill p-2 border-0">
                        @reportItem.ReportCode
                        <input type="hidden" name="name" value=" @reportItem.ReportID" />
                    </div>
                    <div class="w-25 flex-fill p-2 border-0">
                        @reportItem.ReportName
                        @* <a href="#" class="text-primary" id="reportName" data-report-id="@reportItem.ReportID">
                        </a> *@
                    </div>
                    <div class="w-25 flex-fill p-2 border-0">
                        <div class="d-flex">
                            <div>
                                <div class="ps-0 mb-0">
                                    <p class="mb-0">@* <i class="cv-user"></i> *@  @reportItem.OwnerName</p>
                                    @* <a class="text-primary" href="#"> <i class="cv-mail"></i> : @reportItem.OwnerEmail</a>
                                    <p><i class="cv-phone"></i> : @reportItem.OwnerMobile</p> *@
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-25 flex-fill p-2 border-0">
                        @reportItem.PlanDate
                    </div>
                    <div class="w-25 flex-fill p-2 border-0">
                        <div class="d-flex align-items-center">
                            @* <select class="form-select form-control selectized" autocomplete="off" id="ddlOrganization" asp-for=OrgID aria-label="Default select example" asp-items="@(new SelectList(ViewBag.OrgInfo,"Id","OrganizationName"))" required> *@
                            <select class="form-select form-control form-select-sm me-3 status-dropdown" id="<EMAIL>" aria-label="Default select example">
                                @{
                                    foreach (var status in ViewBag.ReportStatus)
                                    {
                                        <!option value="@status.Value" @(status.Value == reportItem.Status.ToString() ? "selected=\"selected=\"" : "")>@status.Text</!option>
                                    }
                                }
                            </select>
                            <button type="button" class="btn btn-primary btn-sm" data-report-id="@reportItem.ReportID" id="btnSaveStaus">Save</button>
                        </div>
                    </div>
                    <div class="w-25 flex-fill p-2 border-0">
                        <table class="">
                            <tbody>
                                <tr>
                                    <td>Total</td>
                                    <td>:</td>
                                    <td>@reportItem.TotalCount</td>

                                    <td>Open</td>
                                    <td>:</td>
                                    <td>@reportItem.OpenCount</td>

                                    <td>Closed</td>
                                    <td>:</td>
                                    <td>@reportItem.ClosedCount</td>

                                    @*<td>Previous</td>
                                    <td>:</td>
                                    <td>@reportItem.PreviousCount</td>*@
                                </tr>
                                
                            </tbody>
                        </table>
                    </div>
                    <div class="flex-fill p-2 border-0 d-flex gap-1" style="width:8%">
                        <span class="btn-action btnAddMomItem" type="button" data-report-id="@reportItem.ReportID"><i class="cv-Plus" title="Add Review Type & MOM"></i></span>
                        <span class="btn-action btnEdit" type="button" data-report-id="@reportItem.ReportID"><i class="cv-edit" title="Edit"></i></span>
                        <span class="btn-action btnDelete" type="button" data-report-id="@reportItem.ReportID"><i class="cv-delete text-danger" title="Delete"></i></span>
                        <span class="btn-action" type="button" id="reportName" data-report-id="@reportItem.ReportID"><i class="cv-view" title="Generate Report"></i></span>
                    </div>
                </div>
                <div class="ps-3 collapse" id="reviewtypeparent_@iCount">
                    <div class="list-group list-group-horizontal fw-semibold">
                        <div class="border-0 flex-fill p-2 list-group-item" style="width:100px"></div>
                        <div class="border-0 w-75 flex-fill p-2 list-group-item"> REVIEWTYPE</div>
                        <div class="border-0 w-25 flex-fill p-2 list-group-item">ITEM SUMMARY</div>
                        <div class="border-0 rounded-0 p-2 text-center list-group-item" style="width: 7%;">Action</div>
                    </div>
                    @{
                        // Group by ReviewTypeID within this report
                        var reviewTypeGroups = reportGroup.GroupBy(x => x.ReviewTypeID);
                        
                        foreach (var reviewTypeGroup in reviewTypeGroups)
                        {
                            var reviewItem = reviewTypeGroup.First();                            
                            <div class="align-items-center border-top rounded-0 list-group list-group-horizontal">                                
                                @{
                                    if (!string.IsNullOrEmpty(reviewItem.ReviewTypeID))
                                    {
                                        <div class="flex-fill p-2 border-0 " style="width:100px">
                                            <span class="toggle-password d-block" role="button" data-bs-toggle="collapse"
                                                  data-bs-target="#<EMAIL>" aria-expanded="false" aria-controls="collapseExample">
                                                <i class="cv-Plus align-middle"></i>
                                            </span>
                                        </div>
                                        <div class="w-75 flex-fill p-2 border-0 ">
                                            @reviewItem.ReviewType
                                            <input type="hidden" name="name" value="@reviewItem.ReviewTypeID" />
                                        </div>
                                        <div class="w-25 flex-fill p-2 border-0 ">
                                            <table class="">
                                                <tbody>
                                                    <tr>
                                                        <td>Total</td>
                                                        <td>:</td>
                                                        <td>@reviewItem.TotalCount_Review</td>
                                                        <td>Open</td>
                                                        <td>:</td>
                                                        <td>@reviewItem.OpenCount_Review</td>
                                                        <td>Closed</td>
                                                        <td>:</td>
                                                        <td>@reviewItem.ClosedCount_Review</td>
                                                    </tr>                                                  
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="flex-fill p-2 border-0 text-center" style="width:7%">
                                            <span class="btn-action btnAddMomItem" type="button" data-bs-toggle="modal"
                                                  data-bs-target="#MomItemModal"
                                                  data-review-type-id="@reviewItem.ReviewTypeID"
                                                  data-report-id="@reviewItem.ReportID">
                                                <i class="cv-Plus me-1" title="Add/Edit MOM"></i>
                                            </span>
                                        </div>
                                    }
                                }                                                               
                            </div>
                            <div class="ps-2 collapse" id="<EMAIL>">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Owner Name</th>
                                            <th>Closing Date</th>
                                            <th>Closed ?</th>
                                            <th>Action Item ?</th>
                                            @*<th>Is Improvement</th>*@
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody class="align-middle">
                                        @foreach (var momItem in reviewTypeGroup)
                                        {
                                            <tr>
                                                <td>
                                                    @momItem.MomItem
                                                </td>
                                                <td>
                                                    <div class="d-flex">
                                                        <div class="User-icon">
                                                            <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                                                        </div>
                                                        <div>
                                                            <ul class="ps-0 mb-0">
                                                                <li class="list-group-item fw-semibold">@momItem.MomOwnerName</li>
                                                                <li class="list-group-item"><a class="text-primary" href="#">@momItem.MomOwnerEmail</a></li>
                                                                <li class="list-group-item">@momItem.MomOwnerMobile</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    @momItem.CloseDate
                                                </td>
                                                <td>
                                                    @if (momItem.IsClosed.Equals("1"))
                                                    {
                                                        <label>Yes</label>
                                                    }
                                                    else
                                                    {
                                                        <label>No</label>
                                                    }
                                                </td>
                                                <td>
                                                    @if (momItem.IsActionItem.Equals("1"))
                                                    {
                                                        // <span class="p-2 bg-danger rounded">
                                                        //     <i class="text-white cv-success align-middle"></i>
                                                        // </span>
                                                        <label>Yes</label>
                                                    }
                                                    else
                                                    {
                                                        // <span class="p-2 bg-success rounded">
                                                        //     <i class="text-white cv-reject align-middle"></i>
                                                        // </span>
                                                        <label>No</label>
                                                    }
                                                </td>
                                                @*<td>
                                                    @if (momItem.IsImprovement.Equals("1"))
                                                    {
                                                        <span class="p-2 bg-danger rounded">
                                                            <i class="text-white cv-success align-middle"></i>
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="p-2 bg-success rounded">
                                                            <i class="text-white cv-reject align-middle"></i>
                                                        </span>
                                                    }
                                                </td>*@
                                                <td class="text-center">
                                                    <span class="btn-action btnEditMomItem" data-review-type-id="@momItem.ReviewTypeID"
                                                          data-report-id="@momItem.ReportID" data-mom-id="@momItem.MomID" type="button"><i class="cv-edit" title="Edit"></i></span>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                    }
                </div>
                iCount++;
                @* iReviewCount++; *@
            }
        </div>
    </div>
</div>



@* Create Modal Start *@
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Review Report Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>

@* Create Modal End *@



@* Add/Edit MOM Item Start *@
<div class="modal fade" id="MomItemModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Add/Edit MOM Item</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>

@* Add/Edit MOM Item End *@




<!-- Delete Modal -->
@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-header p-0">
                <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div> *@

<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->

<script src="~/lib/jquery/jquery.min.js"></script>
<script src="~/js/ManagementReview/management-review.js"></script>
@section Scripts {
    <script>
        $(document).ready(function () {

            $('#btnCreate').click(function () {

                //$.get('/BCMCompliance/ManagementReview/AddManagementReview', function (data) {
                $.get('@Url.Action("AddManagementReview", "ManagementReview")', function (data) {
                    $('.modal-body').html(data);
                    $('#CreateModal').modal('show');
                });
            });

            $('.btnEdit').click(function () {
                 var iReportID = $(this).data('report-id');
               $.get('@Url.Action("EditManagementReview", "ManagementReview")', {ReportID : iReportID}, function (data) {
                    $('.modal-body').html(data);
                    $('#CreateModal').modal('show');
                });
            });

            $(document).on('click', '.btnDelete', function () {
               var iReportID = $(this).data('report-id');
                  $.get('@Url.Action("DeleteManagementReview", "ManagementReview")', {ReportID : iReportID}, function (data) {
                    $('.modal-body').html(data);
                    $('#DeleteModal').modal('show');
                });
            });            

            $(document).on('click','.btnAddMomItem',function () {
                var iReportID = $(this).data('report-id');
                var iReviewTypeID = $(this).data('review-type-id')
                $.get('@Url.Action("AddMomItem", "ReviewReportMasterForm")',
                {iReportID:iReportID,iReviewTypeID:iReviewTypeID},
                function (data) {
                     $('.modal-body').html(data);
                     $('#MomItemModal').modal('show');
                });
            });

            $(document).on('click', '.btnEditMomItem', function () {
                var iMomID = $(this).data('mom-id');
                $.get('@Url.Action("EditMomItem", "ReviewReportMasterForm")', { iMomID: iMomID }, function (data) {
                    $('.modal-body').html(data);
                     $('#MomItemModal').modal('show');
                });
            });

            $(document).on('click','#btnSaveStaus',function(){
                var reportID = $(this).data('report-id');
                var status = $('#ddlStatus_' + reportID).val();

                $.ajax({
                    type:'POST',
                    url:'@Url.Action("ReportStatusSave", "ManagementReview")',
                    data : {
                        iReportId : reportID,
                        iStatus : status
                    },
                    success:function(response)
                    {
                        if (response.success)
                        {
                            $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                            const toastElement = $('#liveToast');
                            toastElement.removeClass('bg-success bg-warning bg-danger');
                            toastElement.addClass('bg-success');
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();
                        }
                        else
                        {
                            $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                            const toastElement = $('#liveToast');
                            toastElement.removeClass('bg-success bg-warning bg-danger');
                            toastElement.addClass('bg-danger');
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            })

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        // $('#Modal').modal('hide');
                        $('#CreateModal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    </script>
}
