﻿@model BCM.BusinessClasses.EscalationMatrix


@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}
<form id="editEscalationMatrixForm" asp-action="EditEscalationMatrix" method="post" class="needs-validation" novalidate>
    <div class="modal-body pt-0">
    <div class="row row-cols-2">
        <div class="col">
            <div class="form-group" hidden>
                <label for="validationCustom01" class="form-label">EscMatID</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-login-code"></i></span>
                    <input type="hidden" class="form-control" placeholder="Enter Department Name" asp-for="EscMatID">
                </div>
                <div class="invalid-feedback">Enter Department Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Organization</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select id="orglist" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example" asp-for="OrgID" required>
                        <option selected  value="0">-- All Organizations --</option>
                        @foreach (var objOrg in ViewBag.OrgInfo)
                        {
                            <option value="@objOrg.Value">@objOrg.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Organization</div>
            </div>
            <div class="form-group">
                <label for="validationCustom01" class="form-label">Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-name"></i></span>
                    <input type="text" class="form-control" placeholder="Enter Name" asp-for="EscMatName" required pattern="^[a-zA-Z0-9_ ]{0,50}$" title="Only Alphanumeric with Underscore and space is allowed (Max Limit 50)">
                </div>
                <div class="invalid-feedback">Enter Escalation Matrix Name</div>
            </div>

            <div class="form-group">
                <label class="form-label">Description</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <input type="text" class="form-control" placeholder="Enter Description" asp-for="EscMatDesc" required>
                </div>
                <div class="invalid-feedback">Enter Escalation Matrix Description</div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Type</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-type"></i></span>
                        <select class="form-select form-select-sm form-control selectized" asp-for="EscMatType" required>
                        <option selected  value="0">-- Select --</option>
                        <option value="1">Approval</option>
                        <option value="2">Review</option>
                        <option value="3">TaskAssignment</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Escalation Matrix Type</div>
            </div>
            <div class="form-group">
                <label class="form-label">Owner</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-owner"></i></span>
                        <select class="form-select form-control selectized" id="ownerlist" autocomplete="off" aria-label="Default select example" asp-for="EscMatOwnerID" required>
                        <option selected  value="0">-- All Resources --</option>
                        @foreach (var objResource in ViewBag.ResourcesInfo)
                        {
                            <option value="@objResource.Value">@objResource.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Escalation Matrix Owner</div>
            </div>
            <div class="form-group">
                <label class="form-label">Approver</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-approver"></i></span>
                        <select class="form-select form-control selectized" id="approverlist" autocomplete="off" aria-label="Default select example" asp-for="EscMatApproverID" required>
                        <option selected  value="0">-- All Resources --</option>
                        @foreach (var objResource in ViewBag.ResourcesInfo)
                        {
                            <option value="@objResource.Value">@objResource.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Escalation Matrix Approver</div>
            </div>
        </div>
    </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button id="btnsave" type="button" class="btn btn-primary btn-sm">Update</button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function () {
        // Ensure validation.css is loaded
        if (!document.querySelector('link[href*="validation.css"]')) {
            console.log("Adding validation.css dynamically");
            $('<link>').attr({
                rel: 'stylesheet',
                type: 'text/css',
                href: '/css/validation.css'
            }).appendTo('head');
        }

        // Load global-validation.js if not already loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Loading global-validation.js");
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    setTimeout(initializeValidation, 100); // Small delay to ensure script is fully processed
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing enhanced validation for EditEscalation form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('editEscalationMatrixForm');
                if (form) {
                    console.log("Found form with ID:", form.id);

                    // Ensure the form has the needs-validation class
                    if (!form.classList.contains('needs-validation')) {
                        form.classList.add('needs-validation');
                    }

                    // Add progressive validation class to the form
                    form.classList.add(window.BCMValidation.classes.progressiveValidationClass);

                    // Add required field indicators to all required fields
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for specific input types
                    window.BCMValidation.addFormatIndicators(form);

                    // Clear all validation messages initially
                    window.BCMValidation.clearAllValidationMessages(form);

                    // Add validation-pending class to form groups to hide validation messages initially
                    form.querySelectorAll('.form-group').forEach(function(formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                    });

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");
                        event.preventDefault(); // Always prevent default submission
                        event.stopPropagation();

                        // Show all validation messages
                        window.BCMValidation.showAllValidationMessages(form);

                        // Validate the form
                        const isValid = window.BCMValidation.validateForm(form);

                        if (isValid) {
                            console.log("Form is valid, submitting");
                            // Use setTimeout to allow UI to update before submitting
                            setTimeout(function() {
                                form.submit();
                            }, 100);
                        } else {
                            console.log("Form is invalid, not submitting");
                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                        }
                    });

                    // Set up progressive validation for all inputs
                    const allInputs = form.querySelectorAll('input, select, textarea');
                    allInputs.forEach(function(input) {
                        // Skip hidden inputs
                        if (input.type === 'hidden') return;

                        console.log("Setting up validation for:", input.id || input.name || "unnamed input");

                        // Setup touched state tracking
                        input.addEventListener('focus', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                formGroup.classList.add(window.BCMValidation.classes.fieldTouchedClass);
                            }
                        });

                        // Setup blur event to validate after user interaction
                        input.addEventListener('blur', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                // Remove validation-pending to show validation messages after interaction
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            // Validate based on input type
                            if (this.type === 'email') {
                                window.BCMValidation.validateEmail(this);
                            } else if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }
                        });

                        // For inputs with patterns, add specific validation
                        if (input.hasAttribute('pattern')) {
                            input.addEventListener('input', function() {
                                // Clear validation state during typing
                                this.classList.remove(window.BCMValidation.classes.invalidClass);
                                this.classList.remove(window.BCMValidation.classes.validClass);
                                const inputGroup = this.closest('.input-group');
                                if (inputGroup) {
                                    inputGroup.classList.remove(window.BCMValidation.classes.invalidClass);
                                    inputGroup.classList.remove(window.BCMValidation.classes.validClass);
                                }
                            });
                        }
                    });

                    // Special handling for Escalation Matrix Type field
                    const escMatTypeSelect = form.querySelector('select[asp-for="EscMatType"]');
                    if (escMatTypeSelect) {
                        console.log("Found Escalation Matrix Type select:", escMatTypeSelect.id || escMatTypeSelect.name || "unnamed select");

                        // Add change event listener
                        escMatTypeSelect.addEventListener('change', function() {
                            console.log("Escalation Matrix Type changed, value:", this.value);

                            // Validate the select on change
                            const formGroup = this.closest('.form-group');
                            if (formGroup && !formGroup.classList.contains(window.BCMValidation.classes.validationPendingClass)) {
                                window.BCMValidation.validateInput(this);
                            }
                        });
                    }

                    // Set up the Update button click handler
                    const saveButton = document.getElementById('btnsave');
                    if (saveButton) {
                        console.log("Found update button, setting up click handler");

                        saveButton.addEventListener('click', function(event) {
                            console.log("Update button clicked");

                            // Show all validation messages
                            window.BCMValidation.showAllValidationMessages(form);

                            // Validate the form
                            const isValid = window.BCMValidation.validateForm(form);

                            if (isValid) {
                                console.log("Form is valid, submitting");
                                form.submit();
                            } else {
                                console.log("Form is invalid, not submitting");
                                // Focus the first invalid field
                                const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                                if (firstInvalidField) {
                                    firstInvalidField.focus();
                                }
                            }
                        });
                    }
                } else {
                    console.error("Form not found with ID: editEscalationMatrixForm");
                }
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }
    });
</script>
