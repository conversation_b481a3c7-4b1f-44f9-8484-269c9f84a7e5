﻿am4core.useTheme(am4themes_animated);

var chart = am4core.create("DonutChart", am4charts.PieChart);
if (chart.logo) chart.logo.disabled = true;

chart.data = [
    { "country": "Completed", "litres": 1 },
    { "country": "In Progress", "litres": 3 },
    { "country": "Not Started", "litres": 1 }
];

chart.innerRadius = am4core.percent(60);

var pieSeries = chart.series.push(new am4charts.PieSeries());
pieSeries.dataFields.value = "litres";
pieSeries.dataFields.category = "country";
pieSeries.slices.template.tooltipText = "{category}: {value}";

pieSeries.slices.template.stroke = am4core.color("#fff");
pieSeries.slices.template.strokeWidth = 5;
pieSeries.slices.template.strokeOpacity = 1;
pieSeries.slices.template.cornerRadius = 20;
pieSeries.slices.template.innerCornerRadius = 20;

pieSeries.hiddenState.properties.opacity = 1;
pieSeries.hiddenState.properties.endAngle = -90;
pieSeries.hiddenState.properties.startAngle = -90;

// Initial color list
pieSeries.colors.list = [
    am4core.color("#09b96d"),
    am4core.color("#ffcf27"),
    am4core.color("#ff812e")
];

// Add legend
chart.legend = new am4charts.Legend();
chart.legend.position = "bottom";
chart.legend.valueLabels.template.text = "{value}";
chart.legend.markers.template.width = 12;
chart.legend.markers.template.height = 12;

// Add center label
let label5 = pieSeries.createChild(am4core.Label);
label5.text = "Total";
label5.horizontalCenter = "middle";
label5.verticalCenter = "middle";
label5.fontSize = 17;

// Legend toggle
var legendVisible = true;
function toggleLegend() {
    legendVisible = !legendVisible;
    chart.legend.parent = legendVisible ? chart.chartContainer : null;
    document.getElementById("legendBtn").innerText = legendVisible ? "Hide Legend" : "Show Legend";
}

// Labels toggle
var labelsVisible = true;
function toggleLabels() {
    labelsVisible = !labelsVisible;
    pieSeries.labels.template.disabled = !labelsVisible;
    document.getElementById("labelsBtn").innerText = labelsVisible ? "Hide Labels" : "Show Labels";
}

// Ticks toggle
var ticksVisible = true;
function toggleTicks() {
    ticksVisible = !ticksVisible;
    pieSeries.ticks.template.disabled = !ticksVisible;
    document.getElementById("ticksBtn").innerText = ticksVisible ? "Hide Ticks" : "Show Ticks";
}

// Color update
function updateColor(index, hexColor) {
    // Rebuild color list with updated color
    const newColors = pieSeries.colors.list.map((c, i) => i === index ? am4core.color(hexColor) : c);
    pieSeries.colors.list = newColors;

    // Update each slice fill to force repaint
    pieSeries.slices.each((slice, i) => {
        slice.fill = pieSeries.colors.getIndex(i);
    });
}