﻿am4core.useTheme(am4themes_animated);
var chart = am4core.create("ReviewinMeetings-Chart", am4charts.XYChart);
if (chart.logo) {
    chart.logo.disabled = true;
}
chart.hiddenState.properties.opacity = 0;

chart.data = [
    {
        country: "Completed",
        visits: 15
    },
    {
        country: "Initiated",
        visits: 12
    },
    {
        country: "Scheduled",
        visits: 14
    }
];

chart.colors.list = [
    am4core.color("#09b96d"),
    am4core.color("#07cedb"),
    am4core.color("#ffa014")
];

chart.padding(0, 0, 0, 0);

var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
categoryAxis.renderer.grid.template.location = 0;
categoryAxis.dataFields.category = "country";
categoryAxis.renderer.minGridDistance = 50;
categoryAxis.fontSize = 11;
categoryAxis.renderer.labels.template.dy = 5;
var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
valueAxis.min = 0;
valueAxis.renderer.minGridDistance = 50;
valueAxis.renderer.baseGrid.disabled = true;

valueAxis.renderer.labels.template.fill = am4core.color("#6c757d");
categoryAxis.renderer.labels.template.fill = am4core.color("#6c757d");

categoryAxis.renderer.grid.template.strokeWidth = 0;
valueAxis.renderer.grid.template.strokeWidth = 0;

var series = chart.series.push(new am4charts.ColumnSeries());
series.dataFields.categoryX = "country";
series.dataFields.valueY = "visits";
series.columns.template.tooltipText = "{valueY.value}";
series.columns.template.tooltipY = 0;
series.columns.template.strokeOpacity = 0;
series.columns.template.width = am4core.percent(20);

series.columns.template.column.cornerRadiusTopLeft = 15;
series.columns.template.column.cornerRadiusTopRight = 15;
series.columns.template.column.cornerRadiusBottomLeft = 15;
series.columns.template.column.cornerRadiusBottomRight = 15;

series.columns.template.adapter.add("fill", function (fill, target) {
    return chart.colors.getIndex(target.dataItem.index);
});

valueAxis.renderer.grid.template.strokeWidth = 0;
valueAxis.renderer.labels.template.disabled = true;

chart.legend = new am4charts.Legend();
chart.legend.position = "bottom";
chart.legend.labels.template.text = "{name}"; // show custom names
chart.legend.markers.template.width = 12;
chart.legend.markers.template.height = 12;

let legendData = [];

// Manually build legend items for each data point
chart.data.forEach((item, index) => {
    legendData.push({
        name: item.country,
        fill: chart.colors.getIndex(index)
    });
});

// Assign legend data
chart.legend.data = legendData;



