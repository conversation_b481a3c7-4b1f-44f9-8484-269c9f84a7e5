﻿@model BCM.BusinessClasses.SubResourceModel
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="col">
    <div class="form-check">
        <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault" onclick="ToggleCheckboxes(this)">
        <label class="form-check-label" for="flexCheckDefault">
            Check all
        </label>
    </div>
</div>
<div class="col-3">
    <div class="input-group">
        <span class="input-group-text py-1"><i class="cv-search"></i></span>
        <input id="search-inp1" type="text" class="form-control" placeholder="Search">
    </div>
</div>
<div class="col-12">
    <table class="table table-hover datatable table-sm">
        <thead>
            <tr>
                <th>@Html.DisplayName("User Name")</th>
                <th>@Html.DisplayName("Email")</th>
                <th>@Html.DisplayName("Mobile")</th>
            </tr>
        </thead>
        <tbody>
            @if (Model != null)
            {
                foreach (var item in Model.MainResources)
                {
                    var objIsChecked = Model.SubBCMResourcesID.Contains(item.ResourceId);
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input userCheckBox" name="selectedResourcesID" data-user-id="@item.ResourceId" type="checkbox" id="<EMAIL>" value="@item.ResourceId" @(objIsChecked ? "checked disabled" : "")>
                                <label class="form-check-label" for="flexCheckDefault">
                                    <input type="hidden" asp-for="@item.UserID" />
                                    @item.ResourceName
                                </label>
                            </div>
                        </td>
                        <td><i class="cv-mail me-1 align-middle"></i>@item.CompanyEmail</td>
                        <td><i class="cv-Mobile me-1 align-middle"></i>@item.MobilePhone</td>
                    </tr>
                }
            }
        </tbody>
    </table>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        var table = $('.datatable').DataTable();
        $('#search-inp1').on('keyup', function () {
            table.search($(this).val()).draw();
        });

        <!-- #region name-- >

            //     // Handle click on "Select all" control
            //     $('#flexCheckDefault').on('click', function () {
            //         $// Get all rows with search applied
            //         var rows = table.rows({ 'search': 'applied' }).nodes();
            //         // Check/uncheck checkboxes for all rows in the table
            //         $(rows).find('input[type="checkbox"]').not(':disabled').prop('checked', this.checked);
            //     });

            // // Handle click on checkbox to set state of "Select all" control
            // $('.datatable tbody').on('change', 'input[type="checkbox"]', function () {
            //     // If checkbox is not checked
            //     if (!this.checked) {

            //         var el = $('#flexCheckDefault').get(0);
            //         // If "Select all" control is checked and has 'indeterminate' property
            //         if (el && el.checked && ('indeterminate' in el)) {
            //             // Set visual state of "Select all" control
            //             // as 'indeterminate'
            //             el.indeterminate = true;
            //         }
            //     }
            // });

            <!-- #endregion -->




            var checkedCheckBoxes = new Set();

        $('#flexCheckDefault').on('click', function () {
            var rows = table.rows({ 'search': 'applied' }).nodes();
            $(rows).find('input[type="checkbox"]').not(':disabled').prop('checked', this.checked);
            $(rows).find('input[type="checkbox"]').not(':disabled').each(function () {
                var userId = $(this).val();
                if (this.checked) {
                    checkedCheckBoxes.add(userId);
                } else {
                    checkedCheckBoxes.delete(userId);
                }
            });
            updateHiddenInput();
        });

        $('.datatable tbody').on('change', 'input[type="checkbox"]', function () {
            var userId = $(this).val();
            if (this.checked) {
                checkedCheckBoxes.add(userId);
            } else {
                checkedCheckBoxes.delete(userId);
            }

            if (!this.checked) {
                var el = $('#flexCheckDefault').get(0);
                if (el && el.checked && ('indeterminate' in el)) {
                    el.indeterminate = true;
                }
            }
            updateHiddenInput();
        });

        function updateHiddenInput() {
            var hiddenInput = $('#checkedCheckBoxes');
            hiddenInput.val(Array.from(checkedCheckBoxes).join(','));
        }

        $('#addBCMGroupMember').on('submit', function () {
            updateHiddenInput();
        })

    });
</script>