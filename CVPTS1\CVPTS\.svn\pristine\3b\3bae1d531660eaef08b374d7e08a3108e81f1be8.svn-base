﻿@{
    ViewData["Title"] = "CustomDashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Dashboard Builder</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Gridstack CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/gridstack@9.2.0/dist/gridstack.min.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/CustomDasboard.css">
    <style>
        .prebuildreport-leftsidebar {
            height: calc(100vh - 180px);
            overflow: auto;
        }

            .prebuildreport-leftsidebar ul {
                margin: 0px;
                list-style: none;
                flex-direction: column;
                display: flex;
                align-items: center;
                gap: 15px;
                padding: 10px;
            }

                .prebuildreport-leftsidebar ul li {
                    width: 100%;
                    padding: 10px;
                    cursor: pointer;
                }

                    .prebuildreport-leftsidebar ul li.active, .prebuildreport-leftsidebar ul li:hover {
                        border-radius: 10px;
                        border: 1px solid #cc3178;
                    }

                    .prebuildreport-leftsidebar ul li div {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-direction: column;
                        gap: 8px;
                    }

        .disables {
            pointer-events: none;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="p-2">
        <div class="row">
            <div class="col-2">
                <div class="card card-custom gutter-b">
                    <div class="card-body">
                        <div>
                            <div class="input-group">
                                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                                <input id="search-inp" type="text" class="form-control" placeholder="Search">
                            </div>
                        </div>
                        <div class="prebuildreport-leftsidebar">
                            <ul>
                                <li class="active report-item" id="dragItem" draggable="true" type="amchart-line">
                                    <div draggable="true">
                                        <img src="/img/cv_report_thumbnail/1_risk_assessment_review.jpg" class="img-fluid">
                                        <p class="mb-0">Risk Assessment Report</p>
                                    </div>
                                </li>
                                <li class="report-item" id="dragItem"  draggable="true">
                                    <div>
                                        <img src="/img/cv_report_thumbnail/1_risk_assessment_review.jpg" class="img-fluid">
                                        <p class="mb-0">Service Criticality Report</p>
                                    </div>
                                </li>
                                <li class="report-item" id="dragItem" draggable="true">
                                    <div>
                                        <img src="/img/cv_report_thumbnail/3_rto_summary_report.jpg" class="img-fluid" id="rtoSummaryReport">
                                        <p class="mb-0">RTO Summary Report</p>
                                    </div>
                                </li>
                                <li class="report-item" id="dragItem" draggable="true">
                                    <div>
                                        <img src="/img/cv_report_thumbnail/5_bcm_training_report.jpg" class="img-fluid">
                                        <p class="mb-0">BCM Training Report</p>
                                    </div>
                                </li>
                                <li class="report-item" id="dragItem"  draggable="true">
                                    <div>
                                        <img src="/img/cv_report_thumbnail/6_process_recovery_priority_report.jpg" class="img-fluid">
                                        <p class="mb-0">Process Recovery Priority Report</p>
                                    </div>
                                </li>
                                <li class="report-item" id="dragItem" draggable="true">
                                    <div>
                                        <img src="/img/cv_report_thumbnail/8_kpi_report.jpg" class="img-fluid">
                                        <p class="mb-0">KPI Report</p>
                                    </div>
                                </li>
                                <li class="report-item" id="dragItem" draggable="true">
                                    <div>
                                        <img src="/img/cv_report_thumbnail/4_hr_report.jpg" class="img-fluid" id="hrreport">
                                        <p class="mb-0">HR Report</p>
                                    </div>
                                </li>
                                <li class="report-item" id="dragItem" draggable="true">
                                    <div>
                                        <img src="/img/cv_report_thumbnail/7_bia_report.jpg" class="img-fluid">
                                        <p class="mb-0">BIA Report</p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col">
                <!-- Navigation Bar -->
                <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="#">
                            <i class="fas fa-chart-line me-2"></i>Dashboard Builder
                        </a>

                        <div class="navbar-nav ms-auto">
                            <div class="nav-item dropdown me-3">
                                <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-plus me-1"></i>Add Widget
                                </button>
                                <ul class="dropdown-menu">
                                    <li><h6 class="dropdown-header">amCharts Widgets</h6></li>
                                    <li><a class="dropdown-item" href="#" data-widget="amchart-line"><i class="fas fa-chart-area me-2"></i>Line Chart</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="amchart-column"><i class="fas fa-chart-column me-2"></i>Column Chart</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="amchart-pie"><i class="fas fa-chart-pie me-2"></i>Pie Chart</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="amchart-radar"><i class="fas fa-chart-simple me-2"></i>Radar Chart</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="amchart-area"><i class="fas fa-chart-area me-2"></i>Area Chart</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="amchart-scatter"><i class="fas fa-braille me-2"></i>Scatter Chart</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">Basic Widgets</h6></li>
                                    <li><a class="dropdown-item" href="#" data-widget="kpi"><i class="fas fa-tachometer-alt me-2"></i>KPI Card</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="text"><i class="fas fa-font me-2"></i>Text Block</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="progress"><i class="fas fa-tasks me-2"></i>Progress Bar</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="table"><i class="fas fa-table me-2"></i>Data Table</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">Advanced Widgets</h6></li>
                                    <li><a class="dropdown-item" href="#" data-widget="weather"><i class="fas fa-cloud-sun me-2"></i>Weather Widget</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="calendar"><i class="fas fa-calendar me-2"></i>Calendar</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="clock"><i class="fas fa-clock me-2"></i>Digital Clock</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="gauge"><i class="fas fa-gauge me-2"></i>Gauge Chart</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="map"><i class="fas fa-map me-2"></i>Map Widget</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="news"><i class="fas fa-newspaper me-2"></i>News Feed</a></li>
                                    <li><a class="dropdown-item" href="#" data-widget="social"><i class="fas fa-share-alt me-2"></i>Social Media</a></li>
                                </ul>
                            </div>

                            <div class="btn-group me-3" role="group">
                                <button type="button" class="btn btn-outline-light" id="undoBtn" title="Undo">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light" id="redoBtn" title="Redo">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </div>

                            <div class="btn-group me-3" role="group">
                                <button type="button" class="btn btn-outline-light" id="copyBtn" title="Copy">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light" id="pasteBtn" title="Paste">
                                    <i class="fas fa-paste"></i>
                                </button>
                            </div>

                            <div class="btn-group me-3" role="group">
                                <button type="button" class="btn btn-outline-light" id="saveBtn" title="Save Dashboard">
                                    <i class="fas fa-save"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light" id="loadBtn" title="Load Dashboard">
                                    <i class="fas fa-folder-open"></i>
                                </button>
                            </div>

                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="previewMode">
                                <label class="form-check-label text-light" for="previewMode">
                                    Preview Mode
                                </label>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- Main Dashboard Container -->
                <div class="container-fluid p-4">
                    <div class="grid-stack" id="dashboard-grid">
                        <!-- Widgets will be dynamically added here -->
                    </div>
                </div>

                <!-- Widget Toolbar Template -->
                <div id="widget-toolbar-template" class="widget-toolbar d-none">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary widget-duplicate" title="Duplicate">
                            <i class="fas fa-clone"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary widget-copy" title="Copy">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger widget-delete" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- Loading Overlay -->
                <div id="loadingOverlay" class="loading-overlay d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

            </div>
        </div>
    </div>
   
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Gridstack JS -->
    <script src="https://cdn.jsdelivr.net/npm/gridstack@9.2.0/dist/gridstack-all.js"></script>

    <!-- amCharts 5 -->
    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

    <!-- Custom JavaScript -->
    <script src="~/js/customdashboard/dashboardbuilder/dashboardData.js"></script>
    <script src="~/js/customdashboard/dashboardbuilder/storage.js"></script>
    <script src="~/js/customdashboard/dashboardbuilder/widgets.js"></script>
    <script src="~/js/customdashboard/dashboardbuilder/dashboard.js"></script>

    <!-- amCharts initialization check -->
    <script>
        // Check if amCharts is loaded properly
        window.addEventListener('load', function() {
            console.log('Window loaded, checking amCharts...');
            console.log('am5 available:', typeof am5 !== 'undefined');
            console.log('am5xy available:', typeof am5xy !== 'undefined');
            console.log('am5percent available:', typeof am5percent !== 'undefined');
            console.log('am5radar available:', typeof am5radar !== 'undefined');
            console.log('am5themes_Animated available:', typeof am5themes_Animated !== 'undefined');

            if (typeof am5 !== 'undefined') {
                console.log('amCharts 5 version:', am5.version);
            }

            // Add debug function to global scope
            window.debugAmCharts = function() {
                if (window.WidgetManager) {
                    window.WidgetManager.debugAmCharts();
                } else {
                    console.log('WidgetManager not available');
                }
            };

            // Add force refresh function
            window.refreshAllCharts = function() {
                if (window.WidgetManager) {
                    console.log('Force refreshing all amCharts...');
                    window.WidgetManager.initializeAllAmCharts();
                } else {
                    console.log('WidgetManager not available');
                }
            };

            console.log('Debug: Type debugAmCharts() or refreshAllCharts() in console');
        });
    </script>
</body>

