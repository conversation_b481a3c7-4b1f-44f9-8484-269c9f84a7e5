﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Security.Helper;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;

namespace BCM.UI.Areas.BCMTraining.Controllers;

[Area("BCMTraining")]


public class BCMTrainingMasterConfigurationController : Controller
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    ManageUsersDetails? _UserDetails = new ManageUsersDetails();
    readonly CVLogger _CVLogger;

    public BCMTrainingMasterConfigurationController(ProcessSrv iProcessSrv, Utilities Utilities, CVLogger cVLogger)
    {
        _ProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _UserDetails = _Utilities.LoginUserDetails();
        _CVLogger = cVLogger;
    }
    public IActionResult BCMTrainingMasterConfiguration(string iId = "0")
    {
        BCMTrainingMaster objBCMTrainingMaster = new BCMTrainingMaster();

        try
        {
            // Decrypt the ID safely
            if (!string.IsNullOrEmpty(iId) && iId != "0")
            {
                iId = CryptographyHelper.Decrypt(iId.ToString());
            }
            objBCMTrainingMaster.TrainingMasterID = Convert.ToInt32(iId);
            // Always populate dropdowns first
            PopulateDropdown();

            // Get the training master if ID is provided
            if (iId != "0" && !string.IsNullOrEmpty(iId))
            {
                objBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getByID(Convert.ToInt32(iId));

                // Ensure the object is not null
                if (objBCMTrainingMaster == null)
                {
                    objBCMTrainingMaster = new BCMTrainingMaster();
                }
            }
            else
            {
                objBCMTrainingMaster.OrgID = _UserDetails.OrgID;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            // Ensure we have a valid object even if there's an error
            objBCMTrainingMaster = new BCMTrainingMaster();
            // Try to populate dropdowns again in case they failed
            try
            {
                PopulateDropdown();
            }
            catch (Exception dropdownEx)
            {
                _CVLogger.LogErrorApp(dropdownEx);
                // Initialize empty ViewBag properties if dropdown population fails
                InitializeEmptyViewBag();
            }
        }

        return View(objBCMTrainingMaster);
    }

    [HttpPost]
    public IActionResult EditBCMTrainingMaster(BCMTrainingMaster objBCMTrainingMaster)
    {
        bool bSuccess = false;
        string message = "Training master updated successfully.";

        try
        {
            if (objBCMTrainingMaster != null)
            {
                bSuccess = _ProcessSrv.SaveTrainingMaster(objBCMTrainingMaster);

                if (!bSuccess)
                {
                    message = "Failed to update training master. Please try again.";
                }
            }
            else
            {
                message = "Invalid training master data provided.";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            message = "An error occurred while updating training master.";
        }

        // For AJAX requests, return JSON response
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = message });
        }

        // For regular form submissions, redirect
        if (bSuccess)
        {
            TempData["SuccessMessage"] = message;
        }
        else
        {
            TempData["ErrorMessage"] = message;
        }

        return RedirectToAction("ManageBCMTrainingForm", "ManageBCMTrainingForm");
    }


    [HttpGet]
    public IActionResult GetTrainingQuestionarie(int iId)
    {
        List<BCMTrainingMaster> objQuestionsDetailsColl = new List<BCMTrainingMaster>();

        try
        {
            objQuestionsDetailsColl = _ProcessSrv.QuestionsDetailsGetByTrainingMasterID(iId);

            // Ensure the collection is not null
            if (objQuestionsDetailsColl == null)
            {
                objQuestionsDetailsColl = new List<BCMTrainingMaster>();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            // Initialize empty list in case of exception
            objQuestionsDetailsColl = new List<BCMTrainingMaster>();
        }

        return PartialView("TrainingQuestionarie", objQuestionsDetailsColl);
    }

    public void PopulateDropdown()
    {
        try
        {
            // Check if _UserDetails is not null before using it
            if (_UserDetails != null)
            {
                var departmentList = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString());
                ViewBag.DepartmentInfo = departmentList ?? new List<DepartmentInfo>();

                var orgList = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString());
                ViewBag.SelectedOrgID = _UserDetails.OrgID.ToString();
                ViewBag.OrgInfo = orgList ?? new List<OrgInfo>();

                var unitList = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString());
                ViewBag.OrgUnit = unitList ?? new List<OrgUnit>();

                var resourceList = _Utilities.GetAllResourceList();
                ViewBag.ResourcesInfo = resourceList ?? new List<ResourcesInfo>();

                var roleList = _Utilities.GetUserRoleMasterByOrgID(_UserDetails.OrgID);
                ViewBag.Role = roleList ?? new List<UserRoleMasterInfo>();
            }
            else
            {
                // Initialize empty ViewBag if _UserDetails is null
                InitializeEmptyViewBag();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            // Initialize empty ViewBag if any exception occurs
            InitializeEmptyViewBag();
        }
    }

   

    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID)
    {
        try
        {
            ViewBag.SelectedUnit = iUnitID.ToString();
            var objDepartmentList = _Utilities.BindFunction(iUnitID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    private void InitializeEmptyViewBag()
    {
        try
        {
            ViewBag.DepartmentInfo = new List<object>();
            ViewBag.OrgInfo = new List<object>();
            ViewBag.OrgUnit = new List<object>();
            ViewBag.ResourcesInfo = new List<object>();
            ViewBag.Role = new List<object>();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
}

