﻿@model IEnumerable<BCM.BusinessClasses.Auditlog_Process_Details>

@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int index = 1;
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers


<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Audit Log Details</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search audit logs">
        </div>
        <div class="dropdown">
            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                <i class="cv-filter align-middle" title="View Filter"></i>
            </button>
            <form class="dropdown-menu p-3 border-0" style="width:20rem;">
                <div class="mb-3">
                    <label>BCM Module</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-module"></i></span>
                        <select id="entitieslist" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example">
                            <option selected value="0">-- Select Module --</option>
                            @foreach (var objCVault in ViewBag.CVaultEntities)
                            {
                                <option value="@objCVault.Value">@objCVault.Text</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Actions</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-actions"></i></span>
                        <select class="form-select form-control selectized" id="actionlist" aria-label="Default select example">
                            <option value="0" selected>-- All Actions --</option>
                            <option value="1">Insertion</option>
                            <option value="2">Update</option>
                            <option value="3">Deletion</option>
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>User</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" id="resourcelist" autocomplete="off" aria-label="Default select example">
                            <option selected value="0">-- All Resources --</option>
                            @foreach (var objResource in ViewBag.ResourcesInfo)
                            {
                                <option value="@objResource.Value">@objResource.Text</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Start Date</label>
                    <div class="position-relative">
                        <input id="startDate" class="form-control" type="date" placeholder="Select Start Date" />
                        <div id="startDate-feedback" class="invalid-feedback"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <label>End Date</label>
                    <div class="position-relative">
                        <input id="endDate" class="form-control" type="date" placeholder="Select End Date" />
                        <div id="endDate-feedback" class="invalid-feedback"></div>
                    </div>
                </div>
                <div class="d-flex gap-2 justify-content-end">
                    <button type="button" id="btnClearFilters" class="btn icon-btn btn-secondary btn-sm">Clear Filters</button>
                    <button type="button" id="btnreset" class="btn icon-btn btn-primary btn-sm" onclick="location.href='@Url.Action("AuditLogDetails", "AuditLogDetails")'">Reset</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>BCM&nbsp;Module</th>
                <th>Record&nbsp;Name</th>
                <th>Action</th>
                <th>Field&nbsp;Changed</th>
                <th>Field&nbsp;Old&nbsp;Value</th>
                <th>Field&nbsp;New&nbsp;Value</th>
                <th>Login&nbsp;IP&nbsp;Address</th>
                <th>User</th>
                <th>User&nbsp;Role</th>
                <th>Date&nbsp;Time</th>
            </tr>
        </thead>
        <tbody>
            @if (Model != null)
            {
                @foreach (var AuditInfo in Model)
                {
                    <tr>
                        <td>@index</td>
                        <td>@AuditInfo.EntityName</td>
                        <td>@AuditInfo.RecordName</td>
                        <td>@AuditInfo.ActionID</td>
                        <td>@AuditInfo.ChangeDescription</td>
                        <td>
                            @if (AuditInfo.PreviousValue == "" || string.IsNullOrEmpty(AuditInfo.PreviousValue))
                            {
                               <span class="text-danger"><i class="cv-na" title="Not Available"></i></span>
                            }
                            else
                            {

                               <span>@AuditInfo.PreviousValue</span>
                            }
                        </td>
                        <td>
                            @if (AuditInfo.NewValue == "" || string.IsNullOrEmpty(AuditInfo.NewValue))
                            {
                                <span class="text-danger"><i class="cv-na" title="Not Available"></i></span>
                            }
                            else
                            {

                                <span>@AuditInfo.NewValue</span>
                            }
                        </td>
                        <td>
                            @if (AuditInfo.IPAddress == "" || string.IsNullOrEmpty(AuditInfo.IPAddress))
                            {
                                <span class="text-danger"><i class="cv-na" title="Not Available"></i></span>
                            }
                            else
                            {

                                <span>@AuditInfo.IPAddress</span>
                            }
                        </td>
                        <td>@AuditInfo.UserName</td>
                        <td>@AuditInfo.UserRoleName</td>
                        <td>@AuditInfo.CreatedOn</td>
                    </tr>
                    index++;
                }
            }
        </tbody>
    </table>
</div>
@section Scripts {
    <script>
        $(document).ready(function () {

            // Date validation function
            function validateDateRange() {
                var startDateInput = $('#startDate');
                var endDateInput = $('#endDate');
                var startDateStr = startDateInput.val();
                var endDateStr = endDateInput.val();
                var isValid = true;

                // Clear previous validation states
                startDateInput.removeClass('is-invalid is-valid');
                endDateInput.removeClass('is-invalid is-valid');
                $('#startDate-feedback').hide();
                $('#endDate-feedback').hide();

                // If both dates are provided, validate the range
                if (startDateStr && endDateStr) {
                    var startDate = new Date(startDateStr);
                    var endDate = new Date(endDateStr);

                    // Check if dates are valid
                    if (isNaN(startDate.getTime())) {
                        startDateInput.addClass('is-invalid');
                        $('#startDate-feedback').text('Invalid Start Date').show();
                        isValid = false;
                    }

                    if (isNaN(endDate.getTime())) {
                        endDateInput.addClass('is-invalid');
                        $('#endDate-feedback').text('Invalid End Date').show();
                        isValid = false;
                    }

                    // Check if end date is earlier than start date
                    if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime()) && endDate < startDate) {
                        endDateInput.addClass('is-invalid');
                        $('#endDate-feedback').text('End Date cannot be earlier than Start Date').show();
                        isValid = false;
                    }

                    // If validation passes, mark fields as valid
                    if (isValid && startDateStr && endDateStr) {
                        startDateInput.addClass('is-valid');
                        endDateInput.addClass('is-valid');
                    }
                }

                return isValid;
            }

            // Add event listeners for date validation
            $('#startDate, #endDate').on('change blur', function() {
                validateDateRange();
            });

            // Function to apply all filters
            function applyFilters() {
                var moduleId = $('#entitieslist').val() || 0;
                var actionId = $('#actionlist').val() || 0;
                var resourceId = $('#resourcelist').val() || 0;
                var startDate = $('#startDate').val() || '';
                var endDate = $('#endDate').val() || '';
                var searchTerm = $('#search-inp').val() || '';

                // Validate date range before applying filters
                if (!validateDateRange()) {
                    return;
                }

                $.get('@Url.Action("ApplyFilters", "AuditLogDetails")', {
                    moduleId: moduleId,
                    actionId: actionId,
                    resourceId: resourceId,
                    startDate: startDate,
                    endDate: endDate,
                    searchTerm: searchTerm
                }, function (data) {
                    $('#example tbody').html(data);
                }).fail(function() {
                    $('#example tbody').html('<tr><td colspan="11" class="text-center py-4"><div class="d-flex flex-column align-items-center justify-content-center"><i class="cv-no-data fs-48 text-muted mb-3"></i><h6 class="text-muted mb-2">Error Loading Data</h6><p class="text-muted small mb-0">Please try again later.</p></div></td></tr>');
                });
            }

            // Individual filter event handlers
            $('#entitieslist').change(function () {
                applyFilters();
            });

            $('#actionlist').change(function () {
                applyFilters();
            });

            $('#resourcelist').change(function () {
                applyFilters();
            });

            $('#startDate, #endDate').change(function () {
                applyFilters();
            });

            // Search functionality with debounce
            var searchTimeout;
            $('#search-inp').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    applyFilters();
                }, 500); // 500ms delay
            });

            // Clear all filters
            $('#btnClearFilters').click(function() {
                // Reset all form controls to default values
                $('#entitieslist').val('0').trigger('change.select2');
                $('#actionlist').val('0').trigger('change.select2');
                $('#resourcelist').val('0').trigger('change.select2');
                $('#startDate').val('');
                $('#endDate').val('');
                $('#search-inp').val('');

                // Clear validation states
                $('#startDate, #endDate').removeClass('is-invalid is-valid');
                $('#startDate-feedback, #endDate-feedback').hide();

                // Apply filters (which will show all data)
                applyFilters();
            });
        });
    </script>
}