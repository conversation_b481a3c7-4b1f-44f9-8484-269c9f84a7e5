﻿@model IEnumerable<BCM.BusinessClasses.ProcessBIAThirdParty>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor

@{
    ViewBag.Title = "Third Parties";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var ProcessName = HttpContextAccessor.HttpContext.Session.GetString("ProcessNameWithCode");
    var ProcessVersion = HttpContextAccessor.HttpContext.Session.GetString("ProcessVersion");
}

<div class="Page-Condant card border-0">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        <p style="padding-left:1%" class="fw-bold mb-2">Configure Third Parties for @ProcessName</p>
        <div class="align-items-right" style="padding-right:2%">
            @* <p class="fw-semibold" id="Version" name="Version">Version : @ViewBag.BIASectionVersion.Version</p> *@
            <p class="fw-semibold" id="Version" name="Version">Version : @ProcessVersion</p>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-12">                
                <div class="accordion accordion-flush" id="accordionFlushExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                Instructions and Guidelines
                            </button>
                        </h2>
                        <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                            <div class="accordion-body">                               
                                <div id="editor2" class="content-editable">
                                    @Html.Raw(ViewBag.Description)
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">               
                @foreach (var objQusetions in ViewBag.Questions)
                {
                    <p class="text-primary d-flex align-items-center gap-1">
                        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsequestion1" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                        Question:@objQusetions.QuestionDetails
                    </p>
                }
                
                <div class="ps-2 collapse show" id="collapsequestion1">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Third Party or Service Provider Name</th>
                                <th>Nature of Service</th>
                                <th>Criticality Category</th>
                                <th>Contractual Support Arrangements</th>
                                <th>Contingencies</th>
                                <th>Potential Alternate Sources</th>
                                @* <th>Completion Status</th> *@
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="tblBody">
                            @if (Model != null)
                            {
                                int iIndex = 0;
                                foreach (var item in Model)
                                {
                                    iIndex++;
                                    <tr>
                                        <td>
                                            @iIndex
                                        </td>
                                        <td>
                                            @item.ThirdPartyName                                            
                                        </td>
                                        <td>
                                            @item.NatureOfService
                                        </td>


                                        @foreach (var name in ViewBag.CriticalityCategories)
                                        {
                                            if (Convert.ToInt32(name.Value) == item.CriticalityCategory)
                                            {
                                                <td>
                                                    @name.Text
                                                </td>
                                            }
                                        }
                                        @{
                                            var status = item.IsCritical == 1 ? "Yes" : "No";
                                        }
                                        <td>
                                            @status
                                        </td>
                                        <td>@item.Contingencies</td>
                                        <td>@item.PotentialAlternateSources</td>
                                        @{
                                            var IsComplete = item.IsComplete == 1 ? "Complete" : "Incomplete";
                                        }
                                        @* <td><i class="cv-success me-1">@IsComplete</i> </td> *@
                                        <td>
                                            <span class="btn-action btnEdit" @ViewBag.ButtonAccess.btnUpdate type="button" data-id="@item.ID" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                            <span class="btn-action btnDelete" @ViewBag.ButtonAccess.btnImgDelete type="button" data-id="@item.ID" data-bs-toggle="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                                        </td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-12">
                <form asp-action="AddUpdateThirdParty" method="post" id="addUpdateThirdParty" class="needs-validation progressive-validation" novalidate>
                    <div class="row row-cols-2">
                        <div class="col">
                            @* <div class="form-group">
                                <label class="form-lable">Version</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-version"></i></span>
                                    <input class="form-control" type="text" readonly value="@ViewBag.BIASectionVersion.Version" />
                                </div>
                            </div> *@
                            <div class="form-group">
                                <label class="form-lable">Questions</label>
                                @foreach (var objQusetions in ViewBag.Questions)
                                {
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="radio" checked="checked" name="QuestionID" id="@objQusetions.ID" value="@objQusetions.ID">
                                        <label class="form-check-label" for="inlineRadio1">@objQusetions.QuestionDetails</label>
                                    </div>
                                }
                                @* <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="option1">
                                <label class="form-check-label" for="inlineRadio1">Third Parties</label>
                                </div> *@
                            </div>
                            <div class="form-group">
                                <input type="hidden" name="Id" id="Id" value="" />
                                <label class="form-lable">Nature of Service</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-service"></i></span>
                                    <textarea class="form-control" name="NatureOfService" id="NatureOfService" placeholder="Enter Facility Address" style="height:0px" required></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Nature of Service</div>
                               @*  <div class="text-end text-secondary">10000 characters left.</div> *@
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Contractual Support Arrangments</label>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="radio" name="IsCritical" id="IsCritical" value="1" required>
                                    <label class="form-check-label" for="inlineRadio1">Yes</label>
                                </div>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="radio" name="IsCritical" id="IsCritical" value="0" required>
                                    <label class="form-check-label" for="inlineRadio1">No</label>
                                </div>
                                <div class="invalid-feedback">
                                    Please select an option.
                                </div>
                            </div>

                            @*  <div class="form-group">
                            <label class="form-lable">Application Name</label>
                            <div class="input-group">
                            <span class="input-group-text"><i class="cv-application"></i></span>
                            <input type="text" class="form-control" placeholder="Enter Application Name" />
                            </div>
                            </div> *@
                            <div class="form-group">
                                <label class="form-lable">Potential Alternate sources</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-source"></i></span>
                                    <textarea class="form-control" name="PotentialAlternateSources" id="PotentialAlternateSources" placeholder="Enter Potential Alternate sources" style="height:0px" required pattern="[A-Za-z][a-zA-Z0-9'&quot;,._$?!+&=#%`~\\/<>;:|{}\*\-+=()\s]{0,499}$" title="Application name must starts with character"></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Potential Alternate sources</div>
                            @*     <div class="text-end text-secondary">10000 characters left.</div> *@
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Third Party or Service Provider Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-third-party"></i></span>
                                    <input type="hidden" name="ThirdPartyName" id="txtThirdPartyName" value=""/>
                                    <select class="form-select-sm form-control selectized" name="ThirdPartyID" id="ThirdPartyName" required>
                                        <option value="0" disabled selected>-- Select Assigned Backup --</option>
                                        @foreach (var name in ViewBag.ThirdPartyName)
                                        {
                                            <option value="@name.CompanyID">@name.CompanyName</option>
                                        }
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select Third Party or Service Provider Name</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Criticality Category</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-critical-category"></i></span>
                                    <select class="form-select-sm form-control selectized" name="CriticalityCategory" id="CriticalityCategory" required>
                                        <option disabled selected value="0">-- Select Assigned Backup --</option>
                                        @foreach (var name in ViewBag.CriticalityCategories)
                                        {
                                            <option value="@name.Value">@name.Text</option>
                                        }
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select Criticality Category</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Contingencies</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-contingencies"></i></span>
                                    <textarea class="form-control" name="Contingencies" placeholder="Enter Contingencies" style="height:0px" required></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Contingencies</div>
                                @* <div class="text-end text-secondary" id="textCal">10000 characters left.</div> *@
                            </div>
                        </div>
                       

                        <div class="col-12 ">
                              <div class="text-end me-4 pb-3">
                                <a class="btn btn-sm btn-outline-primary" role="button" formnovalidate asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>
                                @* <a role="button" class="btn btn-sm btn-primary" formnovalidate asp-action="ManageBusinessProcess" asp-controller="ManageBusinessProcesses" asp-area="BCMProcessBIA">View All</a> *@
                                <button type="submit" class="btn btn-sm btn-primary" @ViewBag.ButtonAccess.btnUpdate id="btnSubmit">Save</button>
                                <button class="btn btn-sm btn-secondary" id="btnCancel" formnovalidate >Cancel</button>                                

                             </div>
                        </div>                           
                    </div>
                </form>
            </div>


        </div>
    </div>
</div>
@* delete model *@
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
            </div>
        </div>
    </div>
</div>

@section Scripts
{
    <script>
        $(document).ready(function () {
            // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function() {
                        console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function() {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                console.log("Initializing validation for addUpdateThirdParty form");

                if (window.BCMValidation) {
                    console.log("BCMValidation found, initializing");

                    // Get the form element
                    const form = document.getElementById('addUpdateThirdParty');
                    if (!form) {
                        console.error("Form not found with ID: addUpdateThirdParty");
                        return;
                    }

                    // Store the original content of all invalid-feedback divs
                    const customMessages = {};
                    form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                        // Find the associated input
                        const formGroup = element.closest('.form-group');
                        const input = formGroup?.querySelector('input, select, textarea');
                        if (input) {
                            // Store the custom message using the input's ID or name as the key
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key) {
                                customMessages[key] = element.textContent.trim();
                                console.log("Stored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    // Override the validateInput function to preserve custom messages
                    const originalValidateInput = window.BCMValidation.validateInput;
                    window.BCMValidation.validateInput = function(input, forceValidation = false) {
                        // Get the result from the original function
                        const result = originalValidateInput(input, forceValidation);

                        // If the input is invalid, restore the custom message
                        if (!result) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        }

                        return result;
                    };

                    // // Override the validateEmail function similarly
                    // const originalValidateEmail = window.BCMValidation.validateEmail;
                    // window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                    //     // Get the result from the original function
                    //     const result = originalValidateEmail(input, forceValidation);

                    //     // If the input is invalid, restore the custom message
                    //     if (!result) {
                    //         const key = input.id || input.name || input.getAttribute('asp-for');
                    //         if (key && customMessages[key]) {
                    //             const formGroup = input.closest('.form-group');
                    //             const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                    //             if (feedbackElement) {
                    //                 // Restore the custom message
                    //                 feedbackElement.textContent = customMessages[key];
                    //                 feedbackElement.style.display = 'block';
                    //                 console.log("Restored custom message for", key, ":", customMessages[key]);
                    //             }
                    //         }
                    //     }

                    //     return result;
                    // };

                    // Override the validatePatternInput function similarly
                    const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                    window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                        // Get the result from the original function
                        const result = originalValidatePatternInput(input, forceValidation);

                        // If the input is invalid, restore the custom message
                        if (!result) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        }

                        return result;
                    };

                    // Override the validateForm function to restore all custom messages after validation
                    const originalValidateForm = window.BCMValidation.validateForm;
                    window.BCMValidation.validateForm = function(form) {
                        // Get the result from the original function
                        const result = originalValidateForm(form);

                        // Restore all custom messages for invalid inputs
                        form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        });

                        return result;
                    };

                    // Initialize the validation framework
                    window.BCMValidation.init();

                    // Add required field indicators (asterisks)
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for pattern-based inputs
                    window.BCMValidation.addFormatIndicators(form);

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");

                        // Show all validation messages
                        window.BCMValidation.showAllValidationMessages(form);

                        // Validate the form
                        const isValid = window.BCMValidation.validateForm(form);
                        console.log("Form validation result:", isValid);
                       
                        if (!isValid) {
                            
                            console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                        }                        
                    });
                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }
            
            $(document).on('click', '.btnEdit', function () {
                debugger;
                UpdateButtonLabel(); 

                var id = $(this).data('id');
                 // Clear validation errors before populating new values
                const form = document.getElementById('addUpdateThirdParty');
                if (form) {
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                       // feedback.textContent = '';
                        feedback.style.display = 'none';
                    });
                }
                
                //$.get('/BCMProcessBIAForms/BIAThirdParty/GetThiradPartyRecordByID/', { id: id })
                $.get('@Url.Action("GetThiradPartyRecordByID", "BIAThirdParty")', { id: id })

                    .done(function (data) {
                        if (data) {
                           
                            $('#Id').val(data.id);
                            $('#ThirdPartyName').val(data.thirdPartyID); 
                            $('#CriticalityCategory').val(data.criticalityCategory); 
                            $('input[name="QuestionID"][value="' + data.questionID + '"]').prop('checked', true); 
                            $('textarea[name="NatureOfService"]').val(data.natureOfService); 
                            $('textarea[name="Contingencies"]').val(data.contingencies); 
                            $('textarea[name="PotentialAlternateSources"]').val(data.potentialAlternateSources); 

                          
                            $('input[name="IsCritical"][value="' + data.isCritical + '"]').prop('checked', true);
                        }
                        UpdateButtonLabel();
                        if (window.BCMValidation && typeof window.BCMValidation.init === 'function') {
                                window.BCMValidation.init();
                        }
                    })
                    .fail(function () {
                        console.error('Failed to fetch data.');
                    });
            });

           
            $(document).on('click', '.btnDelete', function () {
                var id = $(this).data('id'); 

               
                //$.get('/BCMProcessBIAForms/BIAThirdParty/DeleteBIAThirdParty/', { id: id })
                $.get('@Url.Action("DeleteBIAThirdParty", "BIAThirdParty")', { id: id })
                    .done(function (data) {
                        $('.modal-body').html(data); 
                        $('#DeleteModal').modal('show'); 
                        $('#modelTitle').text('Delete Third Party'); 
                    })
                    .fail(function () {
                        console.error('Failed to fetch delete confirmation.');
                    });
            });

            $(document).on('click', '#btnCancel', function (event) {
                event.preventDefault();
                location.reload();
            });

            $(document).on('change', '#ThirdPartyName', function () {
                var ThirdPartyName = $(this).find('option:selected').text();
                $('#txtThirdPartyName').val(applicationName);
            });
           
            function UpdateButtonLabel() {
                
                var id = $('#ThirdPartyName').val();
                if (id && id > 0) {
                    $('#btnSubmit').text('Update');
                } else {
                    $('#btnSubmit').text('Submit');
                }
            }

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    </script>
}