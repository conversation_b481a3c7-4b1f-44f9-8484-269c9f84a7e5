﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using BCM.Shared;
//using Microsoft.EntityFrameworkCore.Metadata.Internal;
//using log4net.Core;
using System.Linq;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;
using Microsoft.AspNetCore.Components.Routing;
using System.ComponentModel.Design;
using System.Xml;

namespace BCM.UI.Areas.BCMThirdParty.Controllers;
[Area("BCMThirdParty")]
public class ManageVendorController : Controller
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;


    ManageUsersDetails _UserDetails = new ManageUsersDetails();
    int iEntityTypeID = 0;

    public ManageVendorController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _UserDetails = _Utilities.LoginUserDetails();
        iEntityTypeID = Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.ThirdParty);

        if (_UserDetails == null)
        {
            RedirectToAction("Login", "Login");
        }
        _CVLogger = CVLogger;
    }

    public IActionResult ManageVendor(int? upcoming, int? past)
    {
        List<BusinessProcessInfo> lstVendor = new List<BusinessProcessInfo>();
        try
        {
            PopulateDropDown();

            lstVendor = _ProcessSrv.GetBIAVendor_OrgUnitLevel(_UserDetails.OrgID);
            lstVendor = GetBusinessProcess(lstVendor);
            ViewBag.SelectedOrgID = _UserDetails.OrgID.ToString();


            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    lstVendor = _Utilities.FilterListByOrgGroupID(lstVendor, _UserDetails.OrgGroupID);

                }
                else
                {

                    lstVendor = _Utilities.FilterListByOrgID(lstVendor, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstVendor = _Utilities.FilterListByRoleID(lstVendor, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                }
            }
            //if (IsDepartmentBIA != 0)
            //{
            //    lstVendor = lstVendor.Where(x => x.DepartmentID == iDepartmentID).ToList();
            //}


            ViewBag.TotalCount = lstVendor.Count;
            ViewBag.UnderBCMCount = lstVendor.Where(x => x.ProcessCode != string.Empty).ToList().Count;
            ViewBag.CriticalCount = lstVendor.Where(x => x.IsCritical == 1 && x.ProcessCode != string.Empty).ToList().Count;
            ViewBag.NonCriticalCount = lstVendor.Where(x => x.IsCritical == 0 && x.ProcessCode != string.Empty).ToList().Count;

            ViewBag.ProcessCode = lstVendor
                              .GroupBy(p => new { p.ProcessCode, p.ProcessName }).Select(g => g.First())
                              .ToList();
            if (upcoming == 1)
            {
                var today = DateTime.Today;
                var next7 = today.AddDays(7);

                lstVendor = lstVendor
                .Where(r =>

                     r.ContractEndDate >= today && r.ContractEndDate <= next7
                )
                .ToList();
                ViewBag.Vendor = lstVendor;
            }
            

            if (past == 1)
            {
                var today = DateTime.Today;

                lstVendor = lstVendor
                .Where(r =>

                    r.ContractEndDate < today
                )
                .ToList();
                ViewBag.Vendor = lstVendor;
            }

            ViewBag.Vendor = lstVendor;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
         
        }
        //try
        //{
        //    PopulateDropDown();

        //    List<BusinessProcessInfo> lstVendor = _ProcessSrv.GetBIAVendor_OrgUnitLevel(0);

        //    iEntityTypeID = lstVendor.Select(x => x.EntityTypeID).FirstOrDefault();
        //    ViewBag.Vendor = lstVendor;
        //   // GetFileredVendor();
        //    //ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();
        //}
        //catch (Exception ex)
        //{
        //    _CVLogger.LogErrorApp(ex);
        //}

        return View();
    }





    [HttpGet]
    public IActionResult AddBCMVendor()
    {
        CompanyMasterInfo objVendorInfo = new CompanyMasterInfo();

        try
        {
            PopulateDropDown();
            objVendorInfo.OrgID = _UserDetails.OrgID;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddVendor", objVendorInfo);
    }


    [HttpPost]
    public IActionResult AddBCMVendor(CompanyMasterInfo objCompanyMaster)
    {
        try
        {
            objCompanyMaster.EntityTypeID = iEntityTypeID;
            objCompanyMaster.CreatedBy = _UserDetails.UserID;
            objCompanyMaster.UpdatedBy = _UserDetails.UserID;
            int iCompanyID = 0;
            iCompanyID = _ProcessSrv.CompanyMasterSave(objCompanyMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageVendor");
    }


    [HttpGet]
    public IActionResult EditBCMVendor(string id)
    {
        CompanyMasterInfo objVendorInfo = new CompanyMasterInfo();

        try
        {
            
            objVendorInfo = _ProcessSrv.GetCompanyByCopmanyId(Convert.ToInt32(id));
            PopulateDropDown(objVendorInfo.OrgGroupID, objVendorInfo.OrgID,objVendorInfo.UnitID,objVendorInfo.DepartmentID,objVendorInfo.DevisionID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_EditVendor", objVendorInfo);
    }


    [HttpPost]
    public IActionResult EditBCMVendor(CompanyMasterInfo objCompanyMaster)
    {
        try
        {
            objCompanyMaster.EntityTypeID = iEntityTypeID;
            objCompanyMaster.CreatedBy = _UserDetails.UserID;
            objCompanyMaster.UpdatedBy = _UserDetails.UserID;
            int iSucess = 0;
            iSucess = _ProcessSrv.CompanyMasterSave(objCompanyMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageVendor");
    }


    [HttpGet]
    public IActionResult DeleteBCMVendor(string id)
    {
        CompanyMasterInfo objVendorInfo = new CompanyMasterInfo();
        try
        {
            if (Convert.ToInt32(id) > 0)
            {
                objVendorInfo = _ProcessSrv.GetCompanyByCopmanyId(Convert.ToInt32(id));
                PopulateDropDown();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_DeleteVendor", objVendorInfo);
    }


    [HttpPost]
    public IActionResult DeleteBCMVendor(CompanyMasterInfo objFacilityInfo)
    {
        try
        {
            bool Sucess = _ProcessSrv.ComapnyDeleteByComapnyID(Convert.ToInt32(objFacilityInfo.CompanyID), iEntityTypeID, Convert.ToInt32(_UserDetails.UserID));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageVendor");
    }


    public void PopulateDropDown(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            ViewBag.OrgInfo = _Utilities.PupulateOrganisation(iOrgGroupID == 0 ?_UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());
            ViewBag.Unit = _Utilities.PupulateUnit(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID == 0 ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());

            ViewBag.Department = _Utilities.PupulateDepartment(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID == 0 ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID == 0 ? _UserDetails.UnitID.ToString() : iUnitID.ToString());
            ViewBag.Subdepartment = _Utilities.PupulateSubDepartment(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID == 0 ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID == 0 ? _UserDetails.DepartmentID.ToString() : iDepartmentID.ToString());
            ViewBag.ResourcesInfo = _Utilities.GetAllResourceList();
            ViewBag.lstorgGroupInfoList = _Utilities.GetOrgGroupList();
            ViewBag.ResourceList = _Utilities.GetResources(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);

            //ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());

            //ViewBag.Unit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);

            //ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

            //ViewBag.ResourceList = _Utilities.GetAllResourceList();

            //ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }


    public IActionResult GetFileredVendor(int OrgID = 0, int UnitID = 0, int DepartmentID = 0, int SubDepartmentID = 0, int IsUnderBCM = -1)
    {
        try
        {
            PopulateDropDown(_UserDetails.OrgGroupID, OrgID, UnitID, DepartmentID, SubDepartmentID);
            List<BusinessProcessInfo> lstVendor = _ProcessSrv.GetBIAVendor_OrgUnitLevel(0);

            //lstVendor = _ProcessSrv.GetBIAVendor_OrgUnitLevel(_UserDetails.OrgID);
            lstVendor = GetBusinessProcess(lstVendor);



            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    lstVendor = _Utilities.FilterListByOrgGroupID(lstVendor, _UserDetails.OrgGroupID);

                }
                else
                {

                    lstVendor = _Utilities.FilterListByOrgID(lstVendor, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstVendor = _Utilities.FilterListByRoleID(lstVendor, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                }
            }

            string strFilter = "x => 1==1";
            if (IsUnderBCM == -1)
            {
                if (OrgID > 0)
                {
                    strFilter = " && x => x.OrgID == " + OrgID;
                    lstVendor = lstVendor.Where(x => x.OrgID == OrgID).ToList();
                }
                if (UnitID > 0)
                {
                    strFilter = " && x => x.UnitID == " + UnitID;
                    lstVendor = lstVendor.Where(x => x.UnitID == UnitID).ToList();
                }
                if (DepartmentID > 0)
                {
                    strFilter = " && x => x.DepartmentID == " + DepartmentID;
                    lstVendor = lstVendor.Where(x => x.DepartmentID == DepartmentID).ToList();
                }
                if (SubDepartmentID > 0)
                {
                    strFilter = " && x => x.SubfunctionID == " + SubDepartmentID;
                    lstVendor = lstVendor.Where(x => x.SubfunctionID == SubDepartmentID).ToList();
                }
            }
            else
            {
                if (OrgID > 0)
                {
                    strFilter = " && x => x.OrgID == " + OrgID;
                    lstVendor = lstVendor.Where(x => x.OrgID == OrgID).ToList();
                }
                if (UnitID > 0)
                {
                    strFilter = " && x => x.UnitID == " + UnitID;
                    lstVendor = lstVendor.Where(x => x.UnitID == UnitID).ToList();
                }
                if (DepartmentID > 0)
                {
                    strFilter = " && x => x.DepartmentID == " + DepartmentID;
                    lstVendor = lstVendor.Where(x => x.DepartmentID == DepartmentID).ToList();
                }
                if (SubDepartmentID > 0)
                {
                    strFilter = " && x => x.SubfunctionID == " + SubDepartmentID;
                    lstVendor = lstVendor.Where(x => x.SubfunctionID == SubDepartmentID).ToList();
                }
                if (IsUnderBCM == 1)
                {
                    lstVendor = lstVendor.Where(x => x.ProcessCode != "").ToList();
                }
                if (IsUnderBCM == 0)
                {
                    lstVendor = lstVendor.Where(x => x.ProcessCode == "").ToList();
                }
            }

            ViewBag.Vendor = lstVendor;
            ViewBag.TotalCount = lstVendor.Count();
            ViewBag.CriticalCount = lstVendor.Count(x => x.IsCritical == 1);
            ViewBag.NonCriticalCount = lstVendor.Count(x => x.IsCritical == 0);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_FilteredVendor");
    }



    [HttpGet]
    public JsonResult GetAllUnits(int iOrgID)
    {
        try
        {
            var objDepartmentList = _Utilities.BindUnit(iOrgID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID)
    {
        try
        {
            var objDepartmentList = _Utilities.BindFunction(iUnitID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllSubDepartments(int iDepartmentID)
    {
        try
        {
            var objSubDepartmentList = _Utilities.BindSubFunction(iDepartmentID);
            return Json(objSubDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    public IActionResult GetSearchVendor(string textSearch, int OrgID = 0, int UnitID = 0, int DepartmentID = 0, int SubDepartmentID = 0)
    {
        List<BusinessProcessInfo> lstVendor = new List<BusinessProcessInfo>();
        try
        {
            try
            {
                lstVendor = _ProcessSrv.GetBIAVendor_OrgUnitLevel(_UserDetails.OrgID);
                lstVendor = GetBusinessProcess(lstVendor);

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    lstVendor = _Utilities.GetBusinessProcess(lstVendor, OrgID, UnitID, DepartmentID, SubDepartmentID, -1);
                }
                else
                {
                    lstVendor = _Utilities.FilterListByRoleID(lstVendor, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstVendor = _Utilities.FilterListForOwner(lstVendor, OrgID, UnitID, DepartmentID, SubDepartmentID, Convert.ToInt32(BCPEnum.EntityType.ThirdParty), -1);
                }

                // Apply case-insensitive text search filter with partial matching
                if (!string.IsNullOrWhiteSpace(textSearch))
                {
                    // Convert search text to lowercase for case-insensitive comparison
                    string searchLower = textSearch.ToLower();

                    lstVendor = lstVendor.Where(x =>
                        (x.ProcessName != null && x.ProcessName.ToLower().Contains(searchLower)) ||
                        (x.UnitName != null && x.UnitName.ToLower().Contains(searchLower)) ||
                        (x.OrgName != null && x.OrgName.ToLower().Contains(searchLower)) ||
                        (x.DepartmentName != null && x.DepartmentName.ToLower().Contains(searchLower)) ||
                        (x.SubFunctionName != null && x.SubFunctionName.ToLower().Contains(searchLower)) ||
                        (x.ProcessCode != null && x.ProcessCode.ToLower().Contains(searchLower)) ||
                        // Include owner details for more comprehensive search
                        (x.ProcessOwner != null && x.ProcessOwner.ToLower().Contains(searchLower)) ||
                        (x.OwnerEmail != null && x.OwnerEmail.ToLower().Contains(searchLower)) ||
                        (x.ProcessOwnerMobile != null && x.ProcessOwnerMobile.ToLower().Contains(searchLower))
                    ).ToList();
                }

                ViewBag.TotalCount = lstVendor.Count;
                ViewBag.CriticalCount = lstVendor.Where(x => x.IsCritical == 1).ToList().Count;
                ViewBag.NonCriticalCount = lstVendor.Where(x => x.IsCritical == 0).ToList().Count;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }

            ViewBag.ProcessCode = lstVendor
                              .GroupBy(p => new { p.ProcessCode, p.ProcessName }).Select(g => g.First())
                              .ToList();
            ViewBag.Vendor = lstVendor;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_FilteredVendor");
    }

    public List<BusinessProcessInfo> GetBusinessProcess(List<BusinessProcessInfo> lstVendor)
    {
        try
        {
            lstVendor = _ProcessSrv.GetBIAVendor_OrgUnitLevel(_UserDetails.OrgID);
            if (_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole))
            {
                lstVendor = _Utilities.GetBusinessProcess(lstVendor, 0, 0, 0, 0, -1);
            }
            else
            {
                //lstVendor = _Utilities.FilterListForOwner(lstVendor, 0, 0, 0, 0, Convert.ToInt32(BCPEnum.EntityType.ThirdParty), -1);
            }

        }
        catch (Exception)
        {

            throw;
        }
        return lstVendor;
    }

}

