﻿@model BCM.BusinessClasses.LocationMaster
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
    var selectedOrgID = ViewBag.selectedOrgID;
    var selectedOrgGrpID = ViewBag.selectedOrgGrpID;
}
<form asp-action="AddLocation" asp-controller="ManageLocation" id="addLocation" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="row row-cols-2">
        <div style="display:none">
            <input type="hidden" asp-for="Id" />
        </div>
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">Location Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-address"></i></span>
                    <input class="form-control" asp-for="LocationName" type="text" id="LocationName" name="LocationName" placeholder="Location Name" required pattern="[A-Za-z\s]+" title="Location name must contain only alphabetic characters and spaces." />
                </div>
                <div class="invalid-feedback">Enter Location Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Organization</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                    @* <select id="OrgID" name="OrgID" asp-for="OrgID" autocomplete="off" class="form-select form-select-sm" aria-label="Default select example">
                        <option selected value="0">All Organization Groups</option>
                        @foreach (var objOrgGroup in ViewBag.OrgInfo)
                        {
                            <option value="@objOrgGroup.Value">@objOrgGroup.Text</option>
                        }
                    </select> *@
                    <select class="form-select form-control selectized OrgID" autocomplete="off" asp-for="OrgID" id="ddlOrg" aria-label="Default select example" required>
                        <option value="0" selected>All Organizations</option>
                        @{
                            foreach (var objOrg in ViewBag.OrgName)
                            {
                                <!option value="@objOrg.Value" @(objOrg.Value == selectedOrgID.ToString() ? "selected=\"selected=\"" : "")>@objOrg.Text</!option>
                            }
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Organization</div>
            </div>

            <div class="form-group">
                <label class="form-label">Latitude</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-latitude"></i></span>
                    <input class="form-control" asp-for="Latitude" type="text" id="Latitude" placeholder="e.g., 37.7749" required pattern="^-?((1[0-7][0-9])|(\d{1,2})|(180))(\.\d+)?$" title="Please enter a valid latitude value between -90 and 90 degrees." />
                </div>
                <div class="invalid-feedback">Enter Latitude</div>
            </div>

        </div>
        <div class="col-6">

            <div class="form-group">
                <label class="form-label">Organization Group</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                    <select id="OrgGroupID" name="OrgGroupID" asp-for="OrgGroupID" autocomplete="off" class="form-select form-select-sm form-control selectized" aria-label="Default select example" required>
                        <option selected disabled value="">-- Select OrgGroup --</option>
                        @foreach (var objOrg in ViewBag.OrgGroup)
                        {                            
                            <!option value="@objOrg.Value" @(objOrg.Value == selectedOrgGrpID.ToString() ? "selected=\"selected=\"" : "")>@objOrg.Text</!option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Org Group</div>
            </div>
            <div class="form-group">
                <label class="form-label">Unit</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-unit"></i></span>
                    @* <select id="UnitID" name="UnitID" asp-for="UnitID" class="form-select form-select-sm" autocomplete="off" aria-label="Default select example">
                        <option selected value="0">Select Unit</option>
                        @foreach (var objUnit in ViewBag.OrgUnit)
                        {
                            <option value="@objUnit.Value">@objUnit.Text</option>
                        }
                    </select> *@
                    <select class="form-select form-control selectized ddlUnit" autocomplete="off" id="ddlUnit" asp-for="UnitID" aria-label="Default select example">
                        <option value="0" selected>All Units</option>
                        @{
                            foreach (var objUnits in ViewBag.Unit)
                            {
                                <option value="@objUnits.Value">@objUnits.Text</option>
                            }
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Unit</div>
            </div>
            <div class="form-group">
                <label for="validationCustom01" class="form-label">Longitude</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-longitude"></i></span>
                    <input class="form-control" asp-for="Longitude" type="text" id="Longitude" placeholder="e.g., -122.4194" required pattern="^-?((1[0-7][0-9])|(\d{1,2})|(180))(\.\d+)?$" title="Please enter a valid longitude value between -180 and 180 degrees." />
                </div>
                <div class="invalid-feedback">Enter Longitude</div>
            </div>           
        </div>

    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>

<script>

    $(document).ready(function() {

           // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for addLocation form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('addLocation');
                if (!form) {
                    console.error("Form not found with ID: addLocation");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateEmail function similarly
                const originalValidateEmail = window.BCMValidation.validateEmail;
                window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateEmail(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validatePatternInput function similarly
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidatePatternInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add user interaction validation for all required fields
                const requiredFields = form.querySelectorAll('[required]');
                requiredFields.forEach(function(field) {
                    // Add input event listener for real-time validation
                    field.addEventListener('input', function() {
                        if (this.value.trim() !== '') {
                            window.BCMValidation.validateInput(this, false);
                        }
                    });

                    // Add blur event listener for validation when field loses focus
                    field.addEventListener('blur', function() {
                        window.BCMValidation.validateInput(this, true);
                    });
                });

                // Add AJAX validation for Location Name field
                const locationNameField = form.querySelector('#LocationName');
                if (locationNameField) {
                    let validationTimeout;

                    locationNameField.addEventListener('input', function() {
                        const locationName = this.value.trim();
                        const input = this;
                        const inputGroup = input.closest('.input-group');
                        const formGroup = input.closest('.form-group');
                        const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                        const locationId = document.getElementById('Id')?.value || 0; // Get current location ID for edit mode

                        // Clear previous timeout
                        if (validationTimeout) {
                            clearTimeout(validationTimeout);
                        }

                        // Only validate if location name is not empty and has at least 2 characters
                        if (locationName.length >= 2) {
                            validationTimeout = setTimeout(function() {
                                // Make AJAX call to check if location name exists
                                $.ajax({
                                    url: '@Url.Action("CheckLocationNameExists", "ManageLocation")',
                                    type: 'GET',
                                    data: {
                                        locationName: locationName,
                                        locationId: locationId // Pass current location ID for edit mode
                                    },
                                    success: function(response) {
                                        if (response.exists) {
                                            // Location name already exists
                                            input.classList.add('is-invalid');
                                            if (inputGroup) inputGroup.classList.add('is-invalid');
                                            if (feedbackElement) {
                                                feedbackElement.textContent = "This Location Name already exists. Please choose another.";
                                                feedbackElement.style.display = 'block';
                                            }
                                        } else {
                                            // Location name is available - only clear if no other validation errors
                                            if (input.checkValidity()) {
                                                input.classList.remove('is-invalid');
                                                if (inputGroup) inputGroup.classList.remove('is-invalid');
                                                if (feedbackElement) {
                                                    feedbackElement.style.display = 'none';
                                                }
                                            }
                                        }
                                    },
                                    error: function() {
                                        console.error('Error checking location name availability');
                                    }
                                });
                            }, 500); // 500ms delay to avoid too many requests
                        }
                    });
                }

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Prevent default submission initially
                    event.preventDefault();
                    event.stopPropagation();

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form first
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    if (!isValid) {
                        console.log("Preventing form submission due to validation errors");
                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                        return;
                    }

                    // If basic validation passes, check location name availability via AJAX
                    const locationNameField = form.querySelector('#LocationName');
                    const locationName = locationNameField?.value.trim();
                    const locationId = document.getElementById('Id')?.value || 0; // Get current location ID for edit mode

                    if (locationName && locationName.length >= 2) {
                        console.log("Checking location name availability on submit");

                        $.ajax({
                            url: '@Url.Action("CheckLocationNameExists", "ManageLocation")',
                            type: 'GET',
                            data: {
                                locationName: locationName,
                                locationId: locationId // Pass current location ID for edit mode
                            },
                            success: function(response) {
                                if (response.exists) {
                                    // Location name already exists
                                    const inputGroup = locationNameField.closest('.input-group');
                                    const formGroup = locationNameField.closest('.form-group');
                                    const feedbackElement = formGroup?.querySelector('.invalid-feedback');

                                    locationNameField.classList.add('is-invalid');
                                    if (inputGroup) inputGroup.classList.add('is-invalid');
                                    if (feedbackElement) {
                                        feedbackElement.textContent = "This Location Name already exists. Please choose another.";
                                        feedbackElement.style.display = 'block';
                                    }
                                    locationNameField.focus();
                                    console.log("Form submission prevented - location name exists");
                                } else {
                                    // Location name is available, proceed with submission
                                    console.log("Location name is available, submitting form");
                                    form.submit();
                                }
                            },
                            error: function() {
                                console.error('Error checking location name availability on submit');
                                // On error, allow submission to proceed (fail gracefully)
                                form.submit();
                            }
                        });
                    } else {
                        // If no location name or too short, proceed with submission (other validation will catch it)
                        form.submit();
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        $('.OrgID').change(function () {
                var ddlOrgVal = $('.OrgID').val();
                debugger;
                if (ddlOrgVal) {
                    $.ajax({
                        url: '@Url.Action("GetAllUnits", "ManageLocation")',
                        type: 'GET',
                        data: { iOrgID: ddlOrgVal },
                        success: function (response) {
                            let selectizeInstance = $('.ddlUnit')[0].selectize;
                            selectizeInstance.clear();
                            selectizeInstance.clearOptions();
                            selectizeInstance.addOption({ value: "0", text: "-- All Units --" });
                            selectizeInstance.addItem("0");

                            response && response.forEach(({ unitID, unitName }) => {
                                 if (unitID && unitName) {
                                     selectizeInstance.addOption({ value: unitID, text: unitName });
                                 }
                            });

                        }
                    });
                }
            });
    });
</script>