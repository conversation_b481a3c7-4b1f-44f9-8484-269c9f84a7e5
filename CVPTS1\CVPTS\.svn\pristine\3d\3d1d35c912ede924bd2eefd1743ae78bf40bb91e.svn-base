﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata;
using System.Collections.Generic;
using System.Data;
using static BCM.Shared.BCPEnum;

namespace BCM.UI.Areas.BCMRiskAssessment.Controllers;
[Area("BCMRiskAssessment")]
public class RiskHeatmap_DynamicNewController : BaseController
{
    private readonly Utilities _Utilities;
    private ProcessSrv _ProcessSrv;
    readonly CVLogger _CvLogger;
    public RiskHeatmap_DynamicNewController(Utilities Utilities, ProcessSrv iProcessSrv, CVLogger cVLogger) : base(Utilities)
    {
        _ProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _CvLogger = cVLogger;
    }

    //public IActionResult RiskHeatmap_DynamicNew()
    //{
    //    int iDefaultProfileID = _Utilities.RiskProfileID;
    //    return View();
    //}

    [HttpGet]
    public IActionResult RiskHeatmap_DynamicNew(bool fromDashboard = false)
    {
        try
        {
            int iDefaultProfileID = _Utilities.RiskProfileID;
            string dataCondition = "0"; // Assign a default or appropriate value to dataCondition
            GetFilterRiskData(dataCondition);

            if (fromDashboard)
            {
                return PartialView("_RiskHeatMapDashboard");
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        return View();
    }


    [HttpGet]
    public IActionResult GetFilterRiskData(string dataCondition)
    {
       
        var lblImpactID = string.Empty;
        var lblProbabilityID = string.Empty;
        try
        {
            // Get risk data
            List<RiskManagement> objriskcoll = new List<RiskManagement>();

            // Add null check for _UserDetails.OrgID
            if (_UserDetails != null && !string.IsNullOrEmpty(Convert.ToString(_UserDetails.OrgID)))
            {
                objriskcoll = _ProcessSrv.GetBCMRiskDetails(Convert.ToInt32(_UserDetails.OrgID));

                if (objriskcoll != null && objriskcoll.Count > 0)
                {

                    IEnumerable<RiskManagement> RiskCodeList = null;
                    IEnumerable<RiskManagement> RiskCodeListResidual = null;
                    //  var RiskCodeList;// = new RiskManagementColl();
                    if (dataCondition == "1")
                    {
                        RiskCodeList = from RiskManagement objRisk in objriskcoll
                                       where
                                       objRisk.Impact.Equals(lblImpactID) && objRisk.LikliHood.Equals(lblProbabilityID)
                                       select objRisk;
                       
                    }
                    else if (dataCondition == "2")
                    {
                        RiskCodeList = from RiskManagement objRisk in objriskcoll
                                       where
                                       objRisk.ResidualImpact.Equals(lblImpactID) && objRisk.ResidualLikeliHood.Equals(lblProbabilityID)

                                       select objRisk;
                      
                    }
                    else if (dataCondition == "0")
                    {
                        RiskCodeList = from RiskManagement objRisk in objriskcoll
                                       where
                                       (objRisk.Impact.Equals(lblImpactID) && objRisk.LikliHood.Equals(lblProbabilityID))

                                       select objRisk;

                        RiskCodeListResidual = from RiskManagement objRisk in objriskcoll
                                               where
                                               (objRisk.ResidualImpact.Equals(lblImpactID) && objRisk.ResidualLikeliHood.Equals(lblProbabilityID))
                                               select objRisk;
                    }

                    // Pass risk data to view as JSON for the JavaScript to use
                    ViewBag.RiskDataJson = Newtonsoft.Json.JsonConvert.SerializeObject(objriskcoll);
                }

                
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        return PartialView("_RiskHeatMapDashboard");
    }



    #region Probability / Impact /Severity Table

    private DataTable BindProbabilityDataTable()
    {
        int DefaultProfileID = _Utilities.RiskProfileID;

        // Correcting the type to match the expected return type from _ProcessSrv.GetAllRiskProbabilitymaster
        List<RiskImpactMaster> ObjriskimpactmasterColl = _ProcessSrv.GetAllRiskProbabilitymaster(Convert.ToInt32(_UserDetails?.OrgID ?? 0), DefaultProfileID);

        DataTable _DtProbability = new DataTable();
        _DtProbability.Columns.Add("ID");
        _DtProbability.Columns.Add("Name");
        _DtProbability.Columns.Add("Description");
        _DtProbability.Columns.Add("Weightage");

        int Count = 1;

        // Correcting the null check and iteration logic
        if (ObjriskimpactmasterColl != null && ObjriskimpactmasterColl.Count > 0)
        {
            foreach (RiskImpactMaster item in ObjriskimpactmasterColl)
            {
                _DtProbability.Rows.Add(Count, item.ProbabilityName, item.ProbabilityDescription, item.ProbabilityWeightage);
                Count++;
            }
        }

        // Removing the invalid Session reference
        // If session storage is required, use HttpContext.Session or TempData instead
        TempData["DataTableProbability"] = _DtProbability;

        return _DtProbability;
    }

    private DataTable BindImpactDataTable()
    {
        // Correcting the type of ObjriskimpactmasterColl to match the expected return type
        List<RiskImpactMaster> ObjriskimpactmasterColl = new List<RiskImpactMaster>();
        int DefaultProfileID = _Utilities.RiskProfileID;

        // Fetching the data correctly
        ObjriskimpactmasterColl = _ProcessSrv.GetAllRiskImpactmaster(Convert.ToInt32(_UserDetails?.OrgID ?? 0), DefaultProfileID);

        // Creating the DataTable structure
        DataTable _DtImpact = CreateImpactDataTable();
        int Count = 1;

        // Correcting the null check and iteration logic
        if (ObjriskimpactmasterColl != null && ObjriskimpactmasterColl.Count > 0)
        {
            foreach (RiskImpactMaster item in ObjriskimpactmasterColl)
            {
                _DtImpact.Rows.Add(item.ImpactName, item.ImpactDescription, item.ImpactWeightage, item.RiskSeverityDescription, item.ColorCode, null, item.FromImpactRange, item.ToImpactRange);
                Count++;
            }
        }

        // Replacing ViewState with TempData for storing the DataTable
        TempData["DtImpact"] = _DtImpact;

        return _DtImpact;
    }

    private DataTable CreateImpactDataTable()
    {
        DataTable _DtImpact = new DataTable();
        _DtImpact.Columns.Add("Name");
        _DtImpact.Columns.Add("ImpactDescription");
        _DtImpact.Columns.Add("Weightage");
        _DtImpact.Columns.Add("RiskSeverityDescription");
        _DtImpact.Columns.Add("ColorCode");
        _DtImpact.Columns.Add("RiskSeverityCategory");
        _DtImpact.Columns.Add("MinRange");
        _DtImpact.Columns.Add("MaxRange");
        return _DtImpact;

    }

    private DataTable BuildRiskSeverityDataTable(int ProfileID)
    {
        // Correcting the type name to match the expected return type
        List<RiskImpactMaster> objRiskcoll = _ProcessSrv.GetAllRiskSeverityDetails(Convert.ToInt32(_UserDetails?.OrgID ?? 0), ProfileID);
        DataTable _DataTable = CreateRiskSeverityDataTable();

        // Correcting the null check and iteration logic
        if (objRiskcoll != null && objRiskcoll.Count > 0)
        {
            foreach (RiskImpactMaster item in objRiskcoll)
            {
                _DataTable.Rows.Add(item.Id, item.RiskSeverityName, item.RiskSeverityDescription, item.ColorCode);
            }
        }
        return _DataTable;
    }

    private DataTable CreateRiskSeverityDataTable()
    {
        DataTable _DataTable = new DataTable();
        _DataTable.Columns.Add("ID");
        _DataTable.Columns.Add("RiskSeverityName");
        _DataTable.Columns.Add("RiskSeverityDescription");
        _DataTable.Columns.Add("ColorCode");
        return _DataTable;
    }


    #endregion

    protected DataTable BuildTableStructure()
    {
        DataTable objtable = new DataTable();
        try
        {
            objtable.Columns.Add("Item", typeof(String));
            objtable.Columns.Add("ID", typeof(String));
            objtable.Columns.Add("ParentID", typeof(String));
            objtable.Columns.Add("ChangedOn", typeof(String));
            objtable.Columns.Add("Responsibility", typeof(String));
            objtable.Columns.Add("NxtRevDate", typeof(String));
            objtable.Columns.Add("Unit", typeof(String));
            objtable.Columns.Add("Depart", typeof(String));
            objtable.Columns.Add("SubDept", typeof(String));
            objtable.Columns.Add("Org", typeof(String));
            objtable.Columns.Add("RiskTo", typeof(String));
            objtable.Columns.Add("RiskRating", typeof(String));
            objtable.Columns.Add("RiskDescription", typeof(String));
            objtable.Columns.Add("RiskCategory", typeof(String));
            objtable.Columns.Add("RiskType", typeof(String));
            objtable.Columns.Add("IsEffective", typeof(String));
            objtable.Columns.Add("Impact", typeof(String));
            objtable.Columns.Add("Likelihood", typeof(String));
            objtable.Columns.Add("iRiskID", typeof(String));
            objtable.Columns.Add("ImageURL", typeof(String));
            objtable.Columns.Add("OrgId", typeof(String));
            objtable.Columns.Add("UnitID", typeof(String));
            objtable.Columns.Add("DepartmentId", typeof(String));
            objtable.Columns.Add("SubFunctionID", typeof(String));
            objtable.Columns.Add("UnitHeadId", typeof(String));
            objtable.Columns.Add("AltUnitHeadId", typeof(String));
            objtable.Columns.Add("UnitBCPCorId", typeof(String));
            objtable.Columns.Add("AltBCPCorId", typeof(String));
            objtable.Columns.Add("DeptHeadId", typeof(String));
            objtable.Columns.Add("AltDeptHeadId", typeof(String));
            objtable.Columns.Add("SubFunOwnerId", typeof(String));
            objtable.Columns.Add("AltSubFunOwnerId", typeof(String));
            objtable.Columns.Add("RiskOwner", typeof(String));
            objtable.Columns.Add("RiskItemCategory", typeof(String));
            objtable.Columns.Add("ProcessID", typeof(String));
            objtable.Columns.Add("RiskName", typeof(String));
            objtable.Columns.Add("ResidualImpact", typeof(String));
            objtable.Columns.Add("ResidualLikeliHood", typeof(String));
            objtable.Columns.Add("ResidualRiskRating", typeof(String));
            objtable.Columns.Add("RiskTreatmentStrategyAction", typeof(String));
            objtable.Columns.Add("Version", typeof(String));
            objtable.Columns.Add("RiskEntryDate", typeof(String));
            objtable.Columns.Add("IncidentTypeName", typeof(String));
            objtable.Columns.Add("LastRevDate", typeof(String));
            objtable.Columns.Add("RiskCloseStatus", typeof(String));
            objtable.Columns.Add("Status", typeof(String));
            objtable.Columns.Add("SubRiskRegID", typeof(String));
            return objtable;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return objtable;
        }
    }




    // This method is not used in the dynamic implementation and contains references to WebForms controls
    // It's kept here for reference but commented out to avoid compilation errors
    /*
    private void AssignDataToMatrix(string rblRRType)
    {
        // This method is not used in the dynamic implementation
        // It contains references to WebForms controls that don't exist in ASP.NET Core MVC
    }
    */




    private string GetBackColor(int totalimpact)
    {
        string Color = string.Empty;
        int DefaultProfileID = _Utilities.RiskProfileID;

        // Correcting the type name to match the expected return type from _ProcessSrv.GetAllRiskSeverityDetails
        List<RiskImpactMaster> objRiskcoll = _ProcessSrv.GetAllRiskSeverityDetails(Convert.ToInt32(_UserDetails?.OrgID ?? 0), DefaultProfileID);

        // Ensuring objRiskcoll is not null and has elements
        if (objRiskcoll != null && objRiskcoll.Count > 0)
        {
            // Correcting the type name in the LINQ query
            var filter = (from dr in objRiskcoll
                          where Convert.ToInt32(dr.FromImpactRange) <= totalimpact && Convert.ToInt32(dr.ToImpactRange) >= totalimpact
                          select dr).FirstOrDefault();

            if (filter != null)
            {
                Color = filter.ColorCode.ToString();
            }
        }
        return Color;
    }
}

