﻿
@{
    ViewData["Title"] = "ReportTemplateForm";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .tree-menu ul li .text-inside {
        max-width: 100%;
        width: 90% !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        vertical-align: middle;
    }
</style>

<div class="Page-Header d-flex align-items-center justify-content-between position-relative">
    <h6 class="Page-Title">Report Template</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <div class="dropdown">
            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                <i class="cv-filter align-middle" title="View Filter"></i>
            </button>
            <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                <div class="mb-3">
                    <label>Organizations</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-organization-group-name"></i></span>
                        <select class="form-select form-control selectized" autocomplete="off" aria-label="Default select example" tabindex="-1">
                            <option value="0" selected="selected">Select Organizations</option>
                        </select>
                       
                    </div>
                </div>
                <div class="mb-3">
                    <label>Units</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                        <select class="form-select form-control selectized" autocomplete="off"  aria-label="Default select example" tabindex="-1">
                            <option value="0" selected="selected">Select Units </option>
                        </select>
                     
                    </div>
                </div>
                <div class="mb-3">
                    <label>Departments</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-department"></i></span>
                        <select class="form-select form-control selectized" autocomplete="off"  aria-label="Default select example" tabindex="-1">
                            <option value="0" selected="selected">select Departments</option>
                        </select>
                       
                    </div>
                </div>
                <div class="mb-3">
                    <label>SubDepartments</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-subdepartment-name"></i></span>
                        <select class="form-select form-control " autocomplete="off" aria-label="Default select example" tabindex="-1" >
                            <option value="0" selected="selected">Select SubDepartments</option>
                        </select>
                        
                    </div>
                </div>
                <div class="text-end">
                    <button type="submit" class="btn btn-sm btn-primary">Search</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="Page-Condant card border-0">
    <table class="table table-bordered" style="table-layout:fixed">
        <tbody>
            <tr>
                <td >
                    <div>
                        <div class="tree-menu">
                            <ul class="tree">
                                <li>
                                    <span role="button" class="text-inside">Ptech Pune LTD-Org-Group</span>
                                    <ul>
                                        <li>
                                            <span role="button" class="text-inside">Perpetuuiti Technosoft Organization</span>
                                            <ul class="sub-parent">
                                                <li>
                                                    <span role="button" class="text-inside">Corporate affairs - Business Unit</span>
                                                </li>
                                                <li>
                                                    <span class="text-inside">Department( Weightage : 0 % )</span>
                                                    <ul class="sub-parent">
                                                        <li>
                                                            <span class="text-inside">Operation and Logistics Department ( Weightage : 0 % )</span>
                                                        </li>
                                                        <li>
                                                            <span class="text-inside">Logistic SubDepartment( Weightage : 0 % )</span>
                                                        </li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </li>
                                        <li>
                                            <span role="button" class="text-inside">Human Resource - Business Unit</span>
                                            <ul class="sub-parent">
                                                <li>
                                                    <span role="button" class="text-inside">Corporate affairs ( Weightage : 50 % )</span>
                                                </li>
                                                <li>
                                                    <span>Department( Weightage : 0 % )</span>
                                                    <ul class="sub-parent">
                                                        <li>
                                                            <span class="text-inside">Operation and Logistics Department ( Weightage : 0 % )</span>
                                                        </li>
                                                        <li>
                                                            <span class="text-inside">Logistic SubDepartment( Weightage : 0 % )</span>
                                                        </li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </tbody>
    </table>

</div>