﻿
@{
    ViewBag.Title = "MyDocuments";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">My Documents </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal"
        data-bs-target="#staticBackdrop">
        <i class="cv-Plus" title="Create New"></i>Create
        </button> *@
        <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"> <i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">Sr. No.</th>
                <th>Name</th>
                <th>Description</th>
                <th>FileSize (bytes)</th>
                <th>Created By </th>
                <th>Created On  </th>
                <th>Updated By</th>
                <th>Updated On</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>1</td>
                <td><a class="text-primary" href="#">ReportBook.pdf</a></td>
                <td>Risk Report</td>               
                <td>1519</td>
                <td>Neeraj Sahu</td>
                <td>3/11/2021 5:18:39 PM</td>
                <td>Neeraj Sahu</td>
                <td>3/11/2021 5:18:39 PM</td>
                <td>
                    <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                    <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                </td>
            </tr>
        </tbody>
    </table>
</div>


<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">My Documents Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-2">
                    <div class="col">
                            <div class="form-group">
                            <label class="form-label">Attach file</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-upload"></i></span>
                                    <input type="file" class="form-control">
                                </div>
                                <div class="invalid-feedback">Upload Logo</div>
                            </div>
                </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input class="form-control" type="text" />
                            </div>
                        </div>

                    </div>
            </div>
            
        </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
    </div>
</div>
</div>
<!--End Configuration Modal -->
<!--Notify Configuration Modal -->
<div class="modal fade" id="NotifyModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Notify Teams Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Org Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-organization"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Perpetuuiti</option>
                                    <option value="value">Perpetuuiti</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Notification Type</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notfication"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Select</option>

                                </select>
                            </div>
                        </div>

                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Unit Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-unit"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Admin Groups</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Incident to Notify</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notify-incident"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Select</option>
                                </select>
                            </div>
                        </div>

                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Team Notification Subject</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notfication"></i></span>
                                <textarea class="form-control" placeholder="Team Notification Subject" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Team Notification Message</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notify-team"></i></span>
                                <textarea class="form-control" placeholder="Team Notification Message" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-group d-flex align-items-center">
                                <span class="form-label mb-0"><i class="cv-user me-1"></i>User Response Required</span>

                                <input type="checkbox" class="form-check  ms-2" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Accept User Response for</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-user"></i></span>
                                <input type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group">
                            @* <label class="form-label">Accept User Response for</label> *@
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-file-size"></i></span>
                                <input type="file" class="form-control" />
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary btn-sm me-1">Filter</button>
                    <button type="submit" class="btn btn-primary btn-sm">Add Team</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Notify Configuration Modal -->
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-header p-0">
                <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->