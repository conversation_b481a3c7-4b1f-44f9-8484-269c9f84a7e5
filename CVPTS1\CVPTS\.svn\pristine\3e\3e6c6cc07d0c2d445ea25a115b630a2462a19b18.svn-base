﻿using AutoMapper;
using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Areas.BCMReports.Mapper;
using BCM.UI.Areas.BCMReports.ReportModels.BCMManagementReviewReport;
using BCM.UI.Areas.BCMReports.ReportTemplate;
using DevExpress.DocumentServices.ServiceModel.DataContracts;
using DevExpress.XtraReports;
using DevExpress.XtraReports.UI;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using System.Data;


namespace BCM.UI.Areas.BCMCompliance.Controllers;
[Area("BCMCompliance")]
public class ManagementReviewController : Controller
{


    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    readonly IMapper _mapper;


    ManageUsersDetails _UserDetails = new ManageUsersDetails();
    int iEntityTypeID = 0;


    public ManagementReviewController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, IMapper mapper)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _UserDetails = _Utilities.LoginUserDetails();

        if (_UserDetails == null)
        {
            RedirectToAction("Login", "Login");
        }
        _CVLogger = CVLogger;
        _mapper = mapper;
    }

    public IActionResult Index()
    {
        return View();
    }

    #region KPI_Measurement
    
    [HttpGet]
    public IActionResult KPIMeasurementMaster()
    {
        List<KPIMeasurementMasters> lstKPIMeasurementMasters = new List<KPIMeasurementMasters>();
        try
        {
            //PopulateDropDown();

            lstKPIMeasurementMasters = _ProcessSrv.GetKPIMeasurementMasterlist();


            //ViewBag.KPIDetails = lstKPIMeasurementMasters;

            //ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View(lstKPIMeasurementMasters);
    }

    #endregion

    #region tree parent
    [HttpGet]
    public IActionResult ManagementReview()
    {
        string totalcount =string.Empty;
        string OpenCount = string.Empty;
        string ClosedCount = string.Empty;
        string PreviousCount = string.Empty;
        
        ReviewType reviewType = new ReviewType();
        List<ReviewTypeMaster> newReviewList = new List<ReviewTypeMaster>();
        try
        {
            PopulateDropDown();
            reviewType.ReviewTypeMaster = _ProcessSrv.GetReviewReportMaster_GetAll(Convert.ToString(_UserDetails.OrgID));                        
            foreach (ReviewTypeMaster objReviewTypeMaster in reviewType.ReviewTypeMaster)
            {
                reviewType.MomTotal = BindReviewType(Convert.ToInt32(objReviewTypeMaster.ReportID), objReviewTypeMaster.CreatedDate);
                ReviewTypeMaster objReviewTypeMaster2 = BindMomItemCount(Convert.ToInt32(objReviewTypeMaster.ReportID), Convert.ToDateTime(objReviewTypeMaster.CreatedDate));
                if (objReviewTypeMaster2 != null)
                {
                    //totalcount = objReviewTypeMaster2.TotalCount;
                    //OpenCount = objReviewTypeMaster2.OpenCount;
                    //ClosedCount = objReviewTypeMaster2.ClosedCount;
                    //PreviousCount = objReviewTypeMaster2.PreviousCount;

                    objReviewTypeMaster.TotalCount = objReviewTypeMaster2.TotalCount;
                    objReviewTypeMaster.OpenCount = objReviewTypeMaster2.OpenCount;
                    objReviewTypeMaster.ClosedCount = objReviewTypeMaster2.ClosedCount;
                    objReviewTypeMaster.PreviousCount = objReviewTypeMaster2.PreviousCount;
                }
                //objReviewTypeMaster.ReportID = objReviewTypeMaster.ReportID;
                //objReviewTypeMaster.ReportCode = objReviewTypeMaster.ReportCode;
                //objReviewTypeMaster.ReportName = objReviewTypeMaster.ReportName;
                //objReviewTypeMaster.OwnerName = objReviewTypeMaster.OwnerName;
                //objReviewTypeMaster.OwnerEmail = objReviewTypeMaster.OwnerEmail;
                //objReviewTypeMaster.OwnerMobile = objReviewTypeMaster.OwnerMobile;
                //objReviewTypeMaster.OrgID = objReviewTypeMaster.OrgID;
                //objReviewTypeMaster.PlanDate = objReviewTypeMaster.PlanDate;
                //objReviewTypeMaster.IsActive = objReviewTypeMaster.IsActive;
                //objReviewTypeMaster.ImpLinks = objReviewTypeMaster.ImpLinks;
                //objReviewTypeMaster.MomItem = objReviewTypeMaster.MomItem;
                //objReviewTypeMaster.IsActionItem = objReviewTypeMaster.IsActionItem;
                //objReviewTypeMaster.IsClosed = objReviewTypeMaster.IsClosed;
                //objReviewTypeMaster.CloseDate = objReviewTypeMaster.CloseDate;
                //objReviewTypeMaster.TotalCount = totalcount;
                //objReviewTypeMaster.OpenCount = OpenCount;
                //objReviewTypeMaster.ClosedCount = ClosedCount;
                //objReviewTypeMaster.PreviousCount = PreviousCount;

                //objReviewTypeMaster.TotalCount = GetMomItemCount(Convert.ToInt32(objReviewTypeMaster.ReportID), objReviewTypeMaster.ReviewTypeID).ToString();
                //objReviewTypeMaster.OpenCount = GetMomItemCount(Convert.ToInt32(objReviewTypeMaster.ReportID), objReviewTypeMaster.ReviewTypeID, true).ToString();
                //objReviewTypeMaster.ClosedCount = GetMomItemCount(Convert.ToInt32(objReviewTypeMaster.ReportID), objReviewTypeMaster.ReviewTypeID, false).ToString();
                //objReviewTypeMaster.PreviousCount = GetMomItemCount(Convert.ToInt32(objReviewTypeMaster.ReportID), objReviewTypeMaster.ReviewTypeID, null, objReviewTypeMaster.CreatedDate).ToString();

                var (total, open, closed) = GetReviewCounts(Convert.ToInt32(objReviewTypeMaster.ReportID), objReviewTypeMaster.ReviewTypeID);
                objReviewTypeMaster.TotalCount_Review = total.ToString();
                objReviewTypeMaster.ClosedCount_Review = closed.ToString();
                objReviewTypeMaster.OpenCount_Review = open.ToString();
                newReviewList.Add(objReviewTypeMaster);
            }
            reviewType.ReviewTypeMaster = newReviewList;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(reviewType);
    }

    public ReviewTypeMaster BindMomItemCount(int iReportID, DateTime CreatedDate)
    {
        DataTable _DataTable = new DataTable();
        ReviewTypeMaster lstmomItemCount = new ReviewTypeMaster();
        _DataTable = _ProcessSrv.GetReviewReportMapping_ByRepordID(iReportID.ToString());
        int TotalCount = 0, OpenCount = 0, ClosedCount = 0;
        int PreviousCount = 0;

        //TotalCount
        TotalCount = _DataTable.Rows.Count;
        lstmomItemCount.TotalCount = Convert.ToString(TotalCount);

        //ClosedCount
        var ClosedCountFilter = (from DataRow dr in _DataTable.Rows
                                 where dr["IsClosed"].ToString() == "1"
                                 select dr);        
        ClosedCount = ClosedCountFilter.Any() ? ClosedCountFilter.Count() : 0;
        lstmomItemCount.ClosedCount = Convert.ToString(ClosedCount);

        //OpenCount
        OpenCount = TotalCount - ClosedCount;
        lstmomItemCount.OpenCount = Convert.ToString(OpenCount);

        ///////////////////////////////////////////

        //DataTable _DataTable = new DataTable();
        DataTable? _Dt = new DataTable();
        int OrgID = _UserDetails.OrgID;  //string.IsNullOrEmpty(Convert.ToString(_UserDetails.OrgID)) == false ? Convert.ToInt32(_UserDetails.OrgID) : 0;
        DataTable _DataTables = _ProcessSrv.GetReviewReportMomItem_OpenItem(OrgID);

        if (_DataTables != null && _DataTables.Rows.Count > 0)
        {
            //ViewState["OpenItem"] = _DataTable;
            var Filter = (from DataRow dr in _DataTables.Rows
                          where Convert.ToDateTime(dr["ReportCreatedDate"].ToString()) < Convert.ToDateTime(CreatedDate)
                          select dr);

            _Dt = Filter.Any() ? Filter.CopyToDataTable() : null;
            if (_Dt != null && _Dt.Rows.Count > 0)
            {
                string OpenCounts = _Dt.Rows.Count.ToString();
                PreviousCount = OpenCount;//lblPreviousCount.Text = OpenCount;
                lstmomItemCount.PreviousCount = Convert.ToString(PreviousCount);
            }
            else
            {
                PreviousCount = 0;
                lstmomItemCount.OpenCount = Convert.ToString(PreviousCount);
            }
        }


        return lstmomItemCount;
    }
    #endregion

    #region tree child
    [HttpGet]
    public List<MomTotal> BindReviewType(int iReportID, string createdDate)
    {
        List<MomTotal> lstReviewTypeMaster = new List<MomTotal>();
        List<MomTotal> lstReviewTypeMaster2 = new List<MomTotal>();
        try
        {
            lstReviewTypeMaster = _ProcessSrv.GetReviewType(Convert.ToInt32(_UserDetails.OrgID));
            if (lstReviewTypeMaster != null)
            {
                //var ObjOrgColl = (from MomTotal Obj in lstReviewTypeMaster
                //                  where Obj.OrgID == _UserDetails.OrgID.ToString()
                //                  select Obj);

                foreach (MomTotal ObjReviewType in lstReviewTypeMaster)
                {
                    //MomTotal reviewTypeMaster = BindMomItemCount_ReviewType(iReportID, ObjReviewType.ReviewTypeID);

                    ObjReviewType.ReportID = iReportID;
                    ObjReviewType.ReviewType = ObjReviewType.ReviewType;
                    ObjReviewType.ReviewTypeID = ObjReviewType.ReviewTypeID;
                    ObjReviewType.TotalCount = TotalMomItemCount(iReportID, ObjReviewType.ReviewTypeID);
                    ObjReviewType.ClosedCount = ClosedCountMomItemCount(iReportID, ObjReviewType.ReviewTypeID);
                    ObjReviewType.OpenCount = OpenCountMomItemCount(iReportID, ObjReviewType.ReviewTypeID);
                    ObjReviewType.PreviousCount = PrevioiusMomItemCount(iReportID, ObjReviewType.ReviewTypeID, createdDate);
                    lstReviewTypeMaster2.Add(ObjReviewType);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return lstReviewTypeMaster2;
    }

    public int TotalMomItemCount(int iReportID, string ReviewTypeID)
    {
        DataTable _DataTable = new DataTable();
        _DataTable = _ProcessSrv.GetReviewReportMapping_ByRepordID(iReportID.ToString());
        int TotalCount = 0;

        if (_DataTable != null && _DataTable.Rows.Count > 0)
        {
            var totalcountFilter = (from DataRow dr in _DataTable.Rows
                                    where dr["ReportID"].ToString().Equals(iReportID.ToString()) && dr["ReviewTypeID"].ToString() == ReviewTypeID
                                    select dr);
            TotalCount = totalcountFilter.Any() ? totalcountFilter.Count() : 0;
        }
        else
        {
                TotalCount = 0;
        }
        return TotalCount;
    }
    public int ClosedCountMomItemCount(int iReportID, string ReviewTypeID)
    {
        DataTable _DataTable = new DataTable();
        _DataTable = _ProcessSrv.GetReviewReportMapping_ByRepordID(iReportID.ToString());
        int ClosedCount = 0;
        if (_DataTable != null && _DataTable.Rows.Count > 0)
        {
            var ClosedCountFilter = (from DataRow dr in _DataTable.Rows
                                     where dr["IsClosed"].ToString() == "1" && dr["ReviewTypeID"].ToString() == ReviewTypeID && dr["ReportID"].ToString().Equals(iReportID.ToString())
                                     select dr);
            ClosedCount = ClosedCountFilter.Any() ? ClosedCountFilter.Count() : 0;
        }
        else
        {
            ClosedCount = 0;
        }
        return ClosedCount;
    }
    public int OpenCountMomItemCount(int iReportID, string ReviewTypeID)
    {
        DataTable _DataTable = new DataTable();
        _DataTable = _ProcessSrv.GetReviewReportMapping_ByRepordID(iReportID.ToString());
        int TotalCount = 0, OpenCount = 0, ClosedCount = 0;
        if (_DataTable != null && _DataTable.Rows.Count > 0)
        {
            var totalcountFilter = (from DataRow dr in _DataTable.Rows
                                    where dr["ReportID"].ToString().Equals(iReportID.ToString()) && dr["ReviewTypeID"].ToString() == ReviewTypeID
                                    select dr);
            TotalCount = totalcountFilter.Any() ? totalcountFilter.Count() : 0;

            var ClosedCountFilter = (from DataRow dr in _DataTable.Rows
                                     where dr["IsClosed"].ToString() == "1" && dr["ReviewTypeID"].ToString() == ReviewTypeID && dr["ReportID"].ToString().Equals(iReportID.ToString())
                                     select dr);
            ClosedCount = ClosedCountFilter.Any() ? ClosedCountFilter.Count() : 0;
            OpenCount = TotalCount - ClosedCount;           
        }
        else
        {
            OpenCount = 0;
        }
        return OpenCount;
    }
    public int PrevioiusMomItemCount(int iReportID, string ReviewTypeID, string createdDate)
    {
        int iPreviousCount = 0;
        try
        {
            DataTable _DataTable = new DataTable();
            _DataTable = _ProcessSrv.GetReviewReportMapping_ByRepordID(iReportID.ToString());

            if (_DataTable != null && _DataTable.Rows.Count > 0)
            {
                var Filter = (from DataRow dr in _DataTable.Rows
                              where Convert.ToDateTime(dr["CreatedDate"].ToString()) < Convert.ToDateTime(createdDate)
                              select dr);

                _DataTable = Filter.Any() ? Filter.CopyToDataTable() : null;
                if (_DataTable != null && _DataTable.Rows.Count > 0)
                {
                    string OpenCount = _DataTable.Rows.Count.ToString();
                    iPreviousCount = Convert.ToInt32(OpenCount);
                }
                else
                {
                    iPreviousCount = 0;
                }
            }
            else
            {
                iPreviousCount = 0;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return iPreviousCount;
    }
    
    #endregion

    #region Add_Management_Review   
    [HttpGet]
    public IActionResult AddManagementReview()
    {
        try
        {
            PopulateDropDown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }        
        return PartialView("_AddManagementReview", new ReviewTypeMaster());
    }

    [HttpPost]
    public IActionResult AddManagementReview(ReviewReportMaster Obj)
    {
        bool bSuccess = false;
        try
        {
            int Success = 0;

            Obj.OrgID = Convert.ToString(_UserDetails.OrgID);
            Obj.CreatedBy = Convert.ToString(_UserDetails.UserID);
            Obj.OwnerID = Obj.OwnerID;
            Obj.PlanDate = Convert.ToDateTime(DateTime.Now);
            //if (ViewState["ValidPlanDate"] != null && !ViewState["ValidPlanDate"].Equals("-1"))
            //{
            //    if (Convert.ToString(ViewState["ValidPlanDate"]) == "0") 
            //    { 
            //        CustValtxtEffdate.IsValid = false; return;
            //    }
            //    else if (Convert.ToString(ViewState["ValidPlanDate"]) == "1")
            //    { 
            //        CustValtxtEffdate.IsValid = true; 
            //    }
            //}
            //ReviewReportMaster Obj = BuildReviewReportEntity();
            Success = _ProcessSrv.ReviewReportMaster_SaveUpdate(Obj);
            bSuccess = Success > 0 ? true : false;
            if (Success > 0)
            {
            }
            else
            {
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? Obj.ReportName + " Added Successfully" : "Failed To Add Review." });
        }
        return RedirectToAction("ManagementReview");
    }

    #endregion

    #region Edit_Management_Review
    [HttpGet]
    public IActionResult EditManagementReview(int ReportID)
    {
        ReviewReportMaster ObjReview = new ReviewReportMaster();
        try
        {
            PopulateDropDown();
            ObjReview = _ProcessSrv.GetReviewReportMaster_ByReportID(ReportID.ToString(), "1");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        
        return PartialView("_EditManagementReview", ObjReview);
    }

    [HttpPost]
    public IActionResult EditManagementReview(ReviewReportMaster Obj)
    {
        bool bSuccess = false;
        try
        {
            int Success = 0;

            Obj.OrgID = Convert.ToString(_UserDetails.OrgID);
            Obj.CreatedBy = Convert.ToString(_UserDetails.UserID);
            Obj.OwnerID = Obj.OwnerID;
            //Obj.PlanDate = Convert.ToDateTime(DateTime.Now);
            //if (ViewState["ValidPlanDate"] != null && !ViewState["ValidPlanDate"].Equals("-1"))
            //{
            //    if (Convert.ToString(ViewState["ValidPlanDate"]) == "0") 
            //    { 
            //        CustValtxtEffdate.IsValid = false; return;
            //    }
            //    else if (Convert.ToString(ViewState["ValidPlanDate"]) == "1")
            //    { 
            //        CustValtxtEffdate.IsValid = true; 
            //    }
            //}
            //ReviewReportMaster Obj = BuildReviewReportEntity();
            Success = _ProcessSrv.ReviewReportMaster_SaveUpdate(Obj);
            bSuccess = Success > 0 ? true : false;
            if (Success > 0)
            {
                //  ManagementReview();
            }
            else
            {
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? Obj.ReportName + " Updated Successfully" : "Failed To Update Review." });
        }
        return RedirectToAction("ManagementReview");
    }

    #endregion

    #region Delete_Management_Review

    [HttpGet]
    public IActionResult DeleteManagementReview(int ReportID)
    {
        ReviewReportMaster ObjReview = new ReviewReportMaster();
        try
        {
            if (Convert.ToInt32(ReportID) > 0)
            {
                ObjReview = _ProcessSrv.GetReviewReportMaster_ByReportID(ReportID.ToString(), "1");
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_DeleteManagementReview", ObjReview);
    }


    [HttpPost]
    public IActionResult DeleteManagementReview(ReviewReportMaster ObjReview)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.DeleteReviewReportMaster(Convert.ToInt32(ObjReview.ReportID), Convert.ToInt32(ObjReview.OwnerID));  
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? ObjReview.ReportName + " Deleted Successfully" : "Failed To Delete Review." });
        }
        return RedirectToAction("ManagementReview");
    }

    #endregion

    //#region AddMOM

    //[HttpGet]
    //public IActionResult AddMOM(int iReportID)
    //{
    //    try
    //    {
    //        ViewBag.ReportID = iReportID;            
    //        PopulateDropDown();
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }        
    //    return PartialView("_AddMOM");
    //}

    //[HttpPost]
    //public IActionResult AddMOM(ReviewReportMomItem Obj,MomImprovements ObjImp)
    //{ 
    //    try
    //    {
    //        int Success = 0;
    //        Obj.CreatedBy = Convert.ToString(_UserDetails.UserID);
    //        Obj.OwnerID = Convert.ToString(_UserDetails.UserID);
    //        Obj.IsActionItem = Obj.IsActionItem.Equals("true") ? "1" : "0";
    //        Obj.IsClosed = Obj.IsClosed.Equals("true") ? "1" : "0";

    //        Success = _ProcessSrv.ReviewReportMapping_SaveUpdate(Obj, ObjImp);
    //        if (Success > 0)
    //        {
    //            //  ManagementReview();
    //        }
    //        else
    //        {
    //        }
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }
    //    return RedirectToAction("ManagementReview");
    //}



    //#endregion


    //#region Edit_MOM
    //[HttpGet]
    //public IActionResult EditMOM(int MomID)
    //{
    //    PopulateDropDown();
    //    ReviewReportMomItem ObjMOMReview = new ReviewReportMomItem();
    //    ObjMOMReview = _ProcessSrv.GetReportReviewMapping_ByID(MomID.ToString());

    //    return PartialView("_EditMOM", ObjMOMReview);
    //}

    //[HttpPost]
    //public IActionResult EditMOM(ReviewReportMomItem Obj, MomImprovements ObjImp)
    //{
    //    try
    //    {
    //        int Success = 0;

    //        Obj.CreatedBy = Convert.ToString(_UserDetails.UserID);
    //        Obj.OwnerID = Convert.ToString(_UserDetails.UserID);
    //        Obj.PlanStartDate = Convert.ToString(DateTime.Now);

    //        Success = _ProcessSrv.ReviewReportMapping_SaveUpdate(Obj, ObjImp);

    //        if (Success > 0)
    //        {

    //        }
    //        else
    //        {

    //        }
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }
    //    return RedirectToAction("ManagementReview");
    //}

    //#endregion

    public void PopulateDropDown(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());

            ViewBag.Unit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);

            ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

            ViewBag.ResourceList = _Utilities.GetAllResourceList();

            ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();

            ViewBag.ReviewType = _ProcessSrv.GetReviewType(Convert.ToInt32(_UserDetails.OrgID));

            ViewBag.BCMEntities = _Utilities.PopulateBCMEntities();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    #region New MOM Counts
    //public int GetMomItemCount(int iReportID, string ReviewTypeID, bool? isClosed = null, string createdDate = null)
    //{
    //    try
    //    {
    //        DataTable _DataTable = _ProcessSrv.GetReviewReportMapping_ByRepordID(iReportID.ToString());

    //        if (_DataTable == null || _DataTable.Rows.Count == 0)
    //            return 0;

    //        var filteredData = from DataRow dr in _DataTable.Rows
    //                           where dr["ReportID"].ToString().Equals(iReportID.ToString()) &&
    //                                 dr["ReviewTypeID"].ToString() == ReviewTypeID
    //                           select dr;

    //        if (isClosed.HasValue)
    //        {
    //            filteredData = filteredData.Where(dr => dr["IsClosed"].ToString() == (isClosed.Value ? "1" : "0"));
    //        }

    //        if (!string.IsNullOrEmpty(createdDate))
    //        {
    //            filteredData = filteredData.Where(dr => Convert.ToDateTime(dr["CreatedDate"].ToString()) < Convert.ToDateTime(createdDate));
    //        }

    //        return filteredData.Any() ? filteredData.Count() : 0;
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex); // Assuming _CVLogger is your logging mechanism
    //        return 0; // Returning a default value to ensure the program doesn't break
    //    }
    //}
    private (int TotalCount, int OpenCount, int ClosedCount) GetReviewCounts(int iReportID, string ReviewTypeID)
    {
        int TotalCount = 0, OpenCount = 0, ClosedCount = 0;
        try
        {
            DataTable _DataTable = _ProcessSrv.GetReviewReportMapping_ByRepordID(iReportID.ToString());

            if (_DataTable != null && _DataTable.Rows.Count > 0)
            {
                var totalcountFilter = from DataRow dr in _DataTable.Rows
                                       where dr["ReportID"].ToString().Equals(iReportID.ToString()) && dr["ReviewTypeID"].ToString() == ReviewTypeID
                                       select dr;
                TotalCount = totalcountFilter.Count();

                var closedCountFilter = from DataRow dr in totalcountFilter
                                        where dr["IsClosed"].ToString() == "1"
                                        select dr;
                ClosedCount = closedCountFilter.Count();

                OpenCount = TotalCount - ClosedCount;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return (TotalCount, OpenCount, ClosedCount);
    }
    #endregion

    #region ManagementReview Report
    public IActionResult GetManagementReviewReport(string reportID)
    {
        var reportsDirectory = string.Empty;
        try
        {
            BCMManagementReviewReportVms reportDetails = new BCMManagementReviewReportVms();

            var viewReportMaster = _ProcessSrv.GetReviewReportMaster_ByReportID(reportID, _UserDetails.OrgID.ToString());
            var momOpenItem = _ProcessSrv.GetReviewReportMomOpenItem_ByReportID(reportID);
            var bcmPolicies = _ProcessSrv.GetBCMPolicies_OrgUnitLevel(_UserDetails.OrgID);
            var auditProgram= _ProcessSrv.GetAuditProgramByID(_UserDetails.OrgID);
            var bcmStrategy = _ProcessSrv.BCMStrategy_GetAll(_UserDetails.OrgID);


            var viewReportMasterDto = _mapper.Map<ReviewReportMasterVm>(viewReportMaster);
            ReportProfile.ClearAutoIncrement();

            var momOpenItemDto = _mapper.Map<ReviewReportMoMItemVm>(momOpenItem);
            ReportProfile.ClearAutoIncrement();

            var bcmPoliciesDto = _mapper.Map<List<BCMPolicies>>(bcmPolicies);
            ReportProfile.ClearAutoIncrement();

            var auditProgramDto = _mapper.Map<List<AuditVm>>(auditProgram);
            ReportProfile.ClearAutoIncrement();

            var bcmStrategyDto = _mapper.Map<List<BCMStrategyVm>>(bcmStrategy);
            ReportProfile.ClearAutoIncrement();

            reportDetails.UserName = _UserDetails.UserName;
            reportDetails.ReviewReportMasterVms = viewReportMasterDto;
            reportDetails.ReviewReportMoMItemVms = momOpenItemDto;
            reportDetails.BCMPoliciesVms = bcmPoliciesDto;
            reportDetails.AuditInfoVms = auditProgramDto;
            reportDetails.BCMStrategyVms = bcmStrategyDto;

            var reportData = JsonConvert.SerializeObject(reportDetails);
            XtraReport report = new BCMManagementReviewReport(reportData);

            using (var ms = new MemoryStream())
            {
                report.ExportToPdf(ms);
                ms.Position = 0;
                var fileName = $"BCMManagementReviewReport_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                return File(ms.ToArray(), "application/pdf", fileName);
            }
            //return Json(new { Success = true, data = reportDetails });
        }
        catch(Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { Success = false });
        }
    }
    #endregion

}

