﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Common;
//using Microsoft.Practices.EnterpriseLibrary.Data;
using BCM.Security.Helper;
using BCM.BusinessProcessComponentsContext;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace BCM.BusinessProcessComponents
{
    public abstract class BaseDataAccess : MarshalByRefObject, IDisposable
    {

        #region Instance Variables

        private Context? _context;

        private Microsoft.Practices.EnterpriseLibrary.Data.Database _db;
        private Microsoft.Practices.EnterpriseLibrary.Data.Database _cpdb;

        private bool _isDisposed;
        private string _name = string.Empty;
        public static string ConnectionStringName = string.Empty;
        #endregion Instance Variables

        #region Properties

        protected virtual string Dbstring
        {
            get
            {
#if MSSQL
                return "?";
#else
                return "@";
#endif
            }
        }

        protected virtual Context Context
        {
           // [DebuggerStepThrough]
            get { return _context ?? (_context = new Context()); }
        }

        protected virtual Microsoft.Practices.EnterpriseLibrary.Data.Database Database
        {
            //[DebuggerStepThrough]
            get
            {
                if (_db == null)
                {
                    string type = typeof(Microsoft.Practices.EnterpriseLibrary.Data.Database).ToString();

                    if (Context.Contains(type))
                    {
                        return _db = (Microsoft.Practices.EnterpriseLibrary.Data.Database)Context[type];
                    }
                    else
                    {
                        ConnectionStringName = "MsSql";
                        IConfigurationRoot configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                        string decryptConnectionstring = configuration.GetConnectionString(ConnectionStringName);

                        string Encrypt = CryptographyHelper.Encrypt(decryptConnectionstring);
                        if (!string.IsNullOrEmpty(decryptConnectionstring))
                        {
                            _db = CustomDatabaseFactory.CreateDatabase(CryptographyHelper.Decrypt(decryptConnectionstring));
                        }
                        Context[type] = _db as IDisposable;
                    }
                }
                return _db;
            }
        }


        protected virtual Microsoft.Practices.EnterpriseLibrary.Data.Database CPDatabase
        {
            get
            {
                if (_cpdb == null)
                {
                    string? type = typeof(Microsoft.Practices.EnterpriseLibrary.Data.Database).ToString();

                    if (Context.Contains(type))
                    {
                        return _cpdb = (Microsoft.Practices.EnterpriseLibrary.Data.Database)Context[type];
                    }
                    else
                    {
                        //_db = CustomDatabaseFactory.CreateDatabase(encrypt);
                        //string? encrypt = ConfigurationManager.Connectionstring?s["CPCon"].Connectionstring?;
                        //string? decrypt = "Data Source=172.16.30.130;Initial Catalog=CVRoot;User ID=sa;Password=*********;TrustServerCertificate=True;";

                        //string? encrypt = CustomDatabaseFactory.CreateDatabase(CryptographyHelper.Decrypt(decrypt));

                        IConfigurationRoot configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                        string decryptConnectionstring = configuration.GetConnectionString("MsSql");

                        string encryptionConnectionstring = string.Empty;
                        if (decryptConnectionstring == null)
                        {
                            encryptionConnectionstring = string.Empty;
                        }
                        else
                        {
                            encryptionConnectionstring = CryptographyHelper.Encrypt(decryptConnectionstring);
                        }

                        if (!string.IsNullOrEmpty(encryptionConnectionstring))
                        {
                            //_db = CustomDatabaseFactory.CreateDatabase(decrypt);
                            _db = CustomDatabaseFactory.CreateDatabase(CryptographyHelper.Decrypt(encryptionConnectionstring));
                            // _cpdb = CustomDatabaseFactory.CreateDatabase(CryptographyHelper.BCPDecrypt(encrypt)); //CustomDatabaseFactory.CreateDatabase(encrypt);
                            // _cpdb = CustomDatabaseFactory.CreateDatabase(CryptographyHelper.Aes256Decrypt(encrypt)); 
                        }

                        Context[type] = _cpdb as IDisposable;
                    }
                }

                return _cpdb;
            }
        }

        protected string DbRoleName
        {
            get
            {
                if (string.IsNullOrEmpty(_name))
                {
#if ORACLE
                    _name = ConfigurationManager.AppSettings["DBRoleName"].Tostring?();
#endif
                }

                return _name;
            }
        }

        #endregion Properties

        #region Constructor & Destructor

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                }
            }

            _isDisposed = true;
        }

        #endregion Constructer & Destructer

        #region Protected Methods

        //[DebuggerStepThrough]
       // protected abstract IEntityBuilder<T> CreateEntityBuilder<T>() where T : BaseEntity;

        //[DebuggerStepThrough]
        //protected virtual int GetReturnCodeFromParameter(DbCommand command)
        //{
        //    return (int)Database.GetParameterValue(command, Constants.MySqlConstants.PrmReturnCode);
        //}

        //[DebuggerStepThrough]
        //protected virtual int GetNewIdFromParameter(DbCommand command, string? paramName)
        //{
        //    return (int)Database.GetParameterValue(command, string?.Concat("@" + paramName));
        //}

        //[DebuggerStepThrough]
        //protected virtual int GetTotalRowFromParameter(DbCommand command)
        //{
        //    return (int)Database.GetParameterValue(command, Constants.MySqlConstants.PrmTotalRow);
        //}

        //[DebuggerStepThrough]
        //protected virtual int GetTotalPageFromParameter(DbCommand command)
        //{
        //    return (int)Database.GetParameterValue(command, Constants.MySqlConstants.PrmTotalPage);
        //}

        //[DebuggerStepThrough]
        //protected virtual int GetCountFromParameter(DbCommand command)
        //{
        //    return (int)Database.GetParameterValue(command, Constants.MySqlConstants.PrmCount);
        //}

        //[DebuggerStepThrough]
        //protected void AddOutputParameter(DbCommand command)
        //{
        //    Database.AddOutParameter(command, "@ReturnCode", DbType.Int32, 4);
        //}


#if ORACLE 
 

        [DebuggerStepThrough]
        protected static OracleParameter BuildRefCursorParameter(string? name)
        {
            return new OracleParameter(name, OracleDbType.Cursor, 0, ParameterDirection.Output, true, 0, 0, string?.Empty, DataRowVersion.Default, DBNull.Value);
            //comment by Martin 27/08/2014
            //return new OracleParameter(name, OracleDbType.RefCursor, 0, ParameterDirection.Output, true, 0, 0, string?.Empty, DataRowVersion.Default, DBNull.Value);
        }

#endif

        #endregion Protected Methods
    }

    public static class CustomDatabaseFactory
    {
        private static DbProviderFactory? _dbProviderFactory;

        public static DbProviderFactory CustomDBFactory
        {
            get
            {
                
#if ORACLE
                return _dbProviderFactory ?? (_dbProviderFactory = DbProviderFactories.GetFactory("Devart.Data.Oracle"));
                //comment by Martin 27/08/2014
                //return _dbProviderFactory ?? (_dbProviderFactory = DbProviderFactories.GetFactory("Oracle.DataAccess.Client"));

#elif MSSQL

               
#else
               ////For MySQl Database Connection
               // DbProviderFactories.RegisterFactory("MySql.Data.MySqlClient", MySql.Data.MySqlClient.MySqlClientFactory.Instance);
              //  return _dbProviderFactory ?? (_dbProviderFactory = DbProviderFactories.GetFactory("MySql.Data.MySqlClient"));

                ////For MSSQL DataBase Connection

                DbProviderFactories.RegisterFactory("System.Data.SqlClient", System.Data.SqlClient.SqlClientFactory.Instance);

                return _dbProviderFactory ?? (_dbProviderFactory = DbProviderFactories.GetFactory("System.Data.SqlClient"));

                

#endif
            }
        }

        public static Microsoft.Practices.EnterpriseLibrary.Data.Database? CreateDatabase(string connectionstring)
        {
            return new GenericDatabase(connectionstring, CustomDBFactory);
        }
    }
}