﻿@model IEnumerable<BCM.BusinessClasses.EquipmentSupply>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor

@{
    ViewBag.Title = "Work Area Recovery Supplies";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var ProcessName = HttpContextAccessor.HttpContext.Session.GetString("ProcessNameWithCode");
    var rows = ViewBag.TimeMasterData as List<Dictionary<string, string>>;
    var ProcessVersion = HttpContextAccessor.HttpContext.Session.GetString("ProcessVersion");
}



<div class="Page-Condant card border-0">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        <p style="padding-left:1%" class="fw-bold mb-2">Configure Work Area Recovery Supplies for @ProcessName</p>
        <div class="align-items-right" style="padding-right:2%">
            @* <p class="fw-semibold">Version : @ViewBag.BIASectionVersion.Version</p> *@
            <p class="fw-semibold">Version : @ProcessVersion</p>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-12">                
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <div class="accordion accordion-flush" id="accordionFlushExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                            Instructions and Guidelines
                                        </button>
                                    </h2>
                                    <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                                        <div class="accordion-body">
                                            <div id="editor2" class="content-editable">
                                                @Html.Raw(ViewBag.Description)
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
        </div>

        <div class="col-12">
            <div>                
                <div class="">
                    @foreach (var objQusetions in ViewBag.Questions)
                    {
                        <p class="text-primary d-flex align-items-center gap-1">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsequestion1" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                            Question: @objQusetions.QuestionDetails
                        </p>
                    }

                    <div class="ps-2 collapse show" id="collapsequestion1">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Equipment/Supply</th>                                    
                                    <th>Day 1</th>
                                    <th>Day 3</th>
                                    <th>Day 7</th>
                                    <th>Day 14</th>
                                    <th>Day 30</th>
                                    <th>Beyond</th>
                                    <th>Comments</th>
                                    <th style="text-align:right !important;">Action</th>
                                </tr>
                            </thead>
                            <tbody id="tblBody">

                                @if (Model != null && Model.Count() != 0)
                                {
                                    int iIndex = 0;
                                    foreach (var item in Model)
                                    {
                                        iIndex++;
                                        <tr>
                                            <td>@iIndex</td>
                                            <td>@item.EquipmentName</td>                                           
                                            <td>@item.Day1</td>
                                            <td>@item.Day3</td>
                                            <td>@item.Day7</td>
                                            <td>@item.Day14</td>
                                            <td>@item.Day30</td>
                                            <td>@item.Beyond</td>
                                            <td>@item.Description</td>
                                            <td style="text-align:right !important;">
                                                <span class="btn-action btnEdit" type="button" @ViewBag.ButtonAccess.btnUpdate data-id="@item.ID" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                                <span class="btn-action btnDelete" type="button" @ViewBag.ButtonAccess.btnUpdate data-id ="@item.ID" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="10" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <img src="~/img/Isomatric/no_records_found.svg" alt="No Records Found" style="width: 120px; height: auto; margin-bottom: 1rem;">
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <form asp-action="AddUpdateWorkAreaRecovery" method="post" id="addUpdateWorkAreaRecovery" class="needs-validation progressive-validation" novalidate>
                <div class="row">
                    @* <div class="col">
                        <div class="form-group">
                            <label class="form-lable">Version</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-version"></i></span>
                                <input class="form-control" type="text" readonly value="@ViewBag.BIASectionVersion.Version" />
                            </div>
                        </div>
                    </div> *@
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Questions</label>
                            <div">
                            @foreach (var objQusetions in ViewBag.Questions)
                            {
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="radio" checked="checked" name="QuestionID" id="@objQusetions.ID" value="@objQusetions.ID">
                                        <label class="form-check-label" for="inlineRadio1">@objQusetions.QuestionDetails</label>
                                    </div>
                            }

                            </div">
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="form-group">
                             <input type="hidden" name="ID" id="equpimentID" value="" />
                            <label class="form-label">
                                Equipment/Supply
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-supply"></i></span>
                                <input type="hidden" name="EquipmentName" id="txtEquipmentName" />
                                <select class="form-select-sm form-control selectized" id="EquipmentName" name="EquipmentId" required>
                                    <option value="" disabled selected> --select-- </option>
                                        @foreach (var name in ViewBag.EquipmentData)
                                        {
                                        <option value="@name.ID">@name.EquipmentName</option>
                                        }
                                </select>
                                <div class="form-group">
                                    <button class="btn btn-sm btn-primary" type="button" data-id="" formnovalidate id="btnAddEquipment">Add New Equipment/Supply</button>
                                </div>
                            </div>
                            <div class="invalid-feedback">Select Equipment/Supply</div>
                        </div>
                    </div>
                   
                        @functions {
                            private string GenerateUniqueId(int rowIndex, string columnName)
                            {
                                return $"input_{rowIndex}_{columnName}";
                            }
                    }
                    <div class="col-12">
                        <table class="table table-hover align-middle border" id="tableId">
                            <thead>
                                <tr>
                                        @foreach (var columnName in ViewBag.ColumnNames)
                                        {
                                        <th>@columnName</th>
                                        }
                                </tr>
                            </thead>
                            <tbody>
                                    @for (int i = 0; i < rows.Count; i++)
                                    {
                                        var rowIndex = i; 
                                    <tr>
                                            @foreach (var columnName in ViewBag.ColumnNames)
                                            {
                                            <td>
                                                <div class="input-group">
                                                    <input class="form-control" id="@GenerateUniqueId(i, columnName)" type="text" name="@columnName" value="@rows[i][columnName]" />
                                                </div>
                                            </td>
                                            }
                                    </tr>
                                    }
                            </tbody>
                        </table>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <input type="hidden" name="ID" id="ID" value="" />
                            <label class="form-label">Comments</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-comment"></i></span>
                                <textarea class="form-control" name="Description" id="Description" placeholder="Enter Comments" style="height:0px" required></textarea>
                            </div>
                            <div class="invalid-feedback">Enter Comments</div>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="col-12">
                        <div class="text-end me-4 pb-3">
                            <a class="btn btn-sm btn-outline-primary" role="button" asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>
                            <button type="submit" @ViewBag.ButtonAccess.btnUpdate class="btn btn-sm btn-primary" id="btnSubmit">Save</button>
                            <button class="btn btn-sm btn-secondary" id="btnCancel" formnovalidate>Cancel</button>                            
                            @* <a role="button" class="btn btn-sm btn-primary" asp-action="ManageBusinessProcess" asp-controller="ManageBusinessProcesses" asp-area="BCMProcessBIA" formnovalidate>View All</a> *@
                           
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>


<div class="modal fade" id="ITModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">IT Services </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Service Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Service Name" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Owner</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                                <select class="form-select form-select-sm form-control selectized">
                                    <option></option>
                                    <option>Selvam</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Service Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                                <textarea class="form-control" placeholder="Enter Service Description"></textarea>
                            </div>
                        </div>
                        <div>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Service Name</th>
                                        <th>Description</th>
                                        <th>Owner</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>E-Mail Services</td>
                                        <td>E-Mail Services</td>
                                        <td>Alia Al Hosani</td>
                                        <td>
                                            <span class="btn-action btnEdit" type="button" data-id="6" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                            <span class="btn-action btnDelete" type="button" data-id="6"><i class="cv-delete text-danger" title="Delete"></i></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>File & Print services</td>
                                        <td>File & Print services</td>
                                        <td>Alia Al Hosani</td>
                                        <td>
                                            <span class="btn-action btnEdit" type="button" data-id="6" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                            <span class="btn-action btnDelete" type="button" data-id="6"><i class="cv-delete text-danger" title="Delete"></i></span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Model for add equipment-->

<div class="modal fade" id="AddEquipmentModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h5 class="modal-title" id="AddEquipmentModalLabel">Please enter the New Equipment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <!-- Modal Body -->          
            <div class="modal-body" id="ConfigModal">
            </div>                      
        </div>
    </div>
</div>

<!-- Delete Modal for Equipments -->
<div class="modal fade" id="DeleteModalForEquipmant" tabindex="-1" aria-labelledby="DeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
                
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->


@* delete model for delete Wrok area recovery*@
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBodyForWrokarearecovery">
            </div>
        </div>
    </div>
</div>

@section Scripts
{
    <script>
        $(document).ready(function () {

             // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function() {
                        console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function() {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                console.log("Initializing validation for addUpdateWorkAreaRecovery form");

                if (window.BCMValidation) {
                    console.log("BCMValidation found, initializing");

                    // Get the form element
                    const form = document.getElementById('addUpdateWorkAreaRecovery');
                    if (!form) {
                        console.error("Form not found with ID: addUpdateWorkAreaRecovery");
                        return;
                    }

                    // Store the original custom messages from invalid-feedback divs
                    const customMessages = {};
                    form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                        const originalMessage = element.textContent.trim();
                        console.log("Processing invalid-feedback element:", originalMessage);

                        // Find the associated input by looking in the form group
                        const formGroup = element.closest('.form-group');
                        let input = formGroup?.querySelector('input[required], select[required], textarea[required]');

                        // If not found, look for any input in the form group
                        if (!input) {
                            input = formGroup?.querySelector('input, select, textarea');
                        }

                        if (input) {
                            // Store the custom message using multiple keys for reliability
                            const keys = [
                                input.id,
                                input.name,
                                input.getAttribute('asp-for')
                            ].filter(key => key); // Remove null/undefined values

                            keys.forEach(key => {
                                customMessages[key] = originalMessage;
                                console.log("Stored custom message for key", key, ":", originalMessage);
                            });
                        } else {
                            console.log("No input found for invalid-feedback:", originalMessage);
                        }
                    });

                    // Function to restore custom message for an input
                    function restoreCustomMessage(input) {
                        const keys = [
                            input.id,
                            input.name,
                            input.getAttribute('asp-for')
                        ].filter(key => key);

                        for (let key of keys) {
                            if (customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                    return true;
                                }
                            }
                        }
                        return false;
                    }

                    // Function to restore all custom messages after validation
                    function restoreAllCustomMessages() {
                        form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                            // Small delay to ensure the global validation has finished
                            setTimeout(() => {
                                restoreCustomMessage(input);
                            }, 10);
                        });
                    }

                    // Initialize the validation framework
                    window.BCMValidation.init();

                    // Add required field indicators (asterisks)
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for pattern-based inputs
                    window.BCMValidation.addFormatIndicators(form);

                    // Add user interaction validation for inputs
                    const allInputs = form.querySelectorAll('input:not([type="hidden"]), select, textarea');
                    allInputs.forEach(function(input) {
                        // Add blur event listener to mark field as touched and validate
                        input.addEventListener('blur', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                // Mark field as touched and remove validation-pending
                                formGroup.classList.add(window.BCMValidation.classes.fieldTouchedClass);
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            // Validate the input using global validation
                            if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }

                            // Restore custom message after a short delay
                            setTimeout(() => {
                                restoreCustomMessage(this);
                            }, 20);
                        });

                        // Add input event listener for real-time validation (only after field is touched)
                        input.addEventListener('input', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup && formGroup.classList.contains(window.BCMValidation.classes.fieldTouchedClass)) {
                                // Validate the input
                                if (this.hasAttribute('pattern')) {
                                    window.BCMValidation.validatePatternInput(this);
                                } else {
                                    window.BCMValidation.validateInput(this);
                                }

                                // Restore custom message after a short delay
                                setTimeout(() => {
                                    restoreCustomMessage(this);
                                }, 20);
                            }
                        });
                    });

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");

                        // Show all validation messages
                        window.BCMValidation.showAllValidationMessages(form);

                        // Validate the form using global validation
                        const isValid = window.BCMValidation.validateForm(form);

                        // Restore all custom messages after validation
                        restoreAllCustomMessages();

                        console.log("Form validation result:", isValid);

                        if (!isValid) {
                            console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                        }
                    });
                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }

            $(document).on('change','#EquipmentName',function(){
                var EquipementName = $(this).find('option:selected').text();
                $('#txtEquipmentName').val(EquipementName);
            })


            $(document).on('click', '.btnEdit', function () {
                UpdateButtonLabel();

                var id = $(this).data('id');
                // Clear validation errors before populating new values
                const form = document.getElementById('addUpdateWorkAreaRecovery');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Add validation-pending class to hide messages until user interaction
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }


                //$.get('/BCMProcessBIAForms/WorkAreaRecovery/GetWorkAreaRecoveryID/', { id: id })
                $.get('@Url.Action("GetWorkAreaRecoveryID", "WorkAreaRecovery")', { id: id })
                    .done(function (data) {
                        if (data) {
                            $('#ID').val(data.id);
                            $('#equpimentID').val(data.id);
                            $('textarea[name="Description"]').val(data.description);
                            $('input[name="QuestionID"][value="' + data.questionID + '"]').prop('checked', true);
                            $('select[name="EquipmentId"]').val(data.equipmentId);
                            $('#txtEquipmentName').val(data.equipmentName);

                            $('#input_0_Day1').val(data.day1);
                            $('#input_0_Day3').val(data.day3);
                            $('#input_0_Day7').val(data.day7);
                            $('#input_0_Day14').val(data.day14);
                            $('#input_0_Day30').val(data.day30);
                            $('#input_0_Beyond').val(data.beyond);

                            UpdateButtonLabel();
                        }
                    })
                    .fail(function () {
                        console.error('Failed to fetch data.');
                    });
            });


            $(document).on('click', '.btnDelete', function () {
                var id = $(this).data('id');


                //$.get('/BCMProcessBIAForms/WorkAreaRecovery/DeleteWorkAreaRecovery/', { id: id })
                $.get('@Url.Action("DeleteWorkAreaRecovery", "WorkAreaRecovery")', { id: id })
                    .done(function (data) {
                        $('#DeleteBodyForWrokarearecovery').html(data);
                        $('#DeleteModal').modal('show');
                    })
                    .fail(function () {
                        console.error('Failed to fetch delete confirmation.');
                    });
            });

            $(document).on('click', '.btnDelete1', function () {

                var id = $(this).data('id');
                $.get('@Url.Action("DeleteEquipment", "WorkAreaRecovery")', { id: id }, function (data) {
                    $('#DeleteBody').html(data);
                    $('#DeleteModalForEquipmant').modal('show');
                });
            });

            $(document).on('click', '#btnAddEquipment', function () {
                var id = $("#EquipmentName").val();
                $.get('@Url.Action("AddEquipment", "WorkAreaRecovery")', { id: id }, function (data) {
                    $('#ConfigModal').html(data);
                    $('#AddEquipmentModal').modal('show');
                });
            });

            $(document).on('click', '#btnCancel', function (event) {
                event.preventDefault();

                // Clear validation errors when canceling
                const form = document.getElementById('addUpdateWorkAreaRecovery');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Reset validation state
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }

                location.reload();
            });

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

            function UpdateButtonLabel() {


                var id = $('#ID').val();
                if (id && id > 0) {
                    $('#btnSubmit').text('Update');
                } else {
                    $('#btnSubmit').text('Save');
                }
            }
        });
    </script>
}