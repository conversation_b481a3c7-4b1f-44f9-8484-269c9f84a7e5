﻿using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class DashboardBuilderController : BaseController
{
    private readonly Utilities _Utilities;
    public DashboardBuilderController(Utilities Utilities) : base(Utilities)
    {
        _Utilities = Utilities;
    }
    public IActionResult DashboardBuilder()
    {
        return View();
    }
}