﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.DirectoryServices;

namespace BCM.UI.Areas.BCMResourceManagement.Controllers;
[Area("BCMResourceManagement")]
public class ManageUsersController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    int iUnitID = 0;
    int iDepartmentID = 0;

    public ManageUsersController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult ManageUsers()
    {
        List<ResourcesInfo> lstResourcesInfo = new List<ResourcesInfo>();

        try
        {
            PopulateDropdown();         
            lstResourcesInfo = _ProcessSrv.GetResourcesListByGrpOrgUnitDept(Convert.ToInt32(_UserDetails.OrgGroupID), Convert.ToInt32(_UserDetails.OrgID), iUnitID, iDepartmentID);
            ViewBag.ResourceInfo = lstResourcesInfo;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
        //return View(lstResourcesInfo);
    }
  
    [HttpGet]
    public IActionResult ActiveManageUsers(int iId)
    {
        var objDepartmentInfo = new ManageUsersDetails();

        try
        {
            objDepartmentInfo = _ProcessSrv.GetManageUsersById(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }


        return PartialView("_ActiveManageUsers", objDepartmentInfo);
    }


    [HttpPost]
    public IActionResult ActiveManageUsers(ManageUsersDetails objResourcesInfo)
    {
        bool bSuccess = false;

        if (objResourcesInfo.LoginIsActive == 0)
        {
            bSuccess = _ProcessSrv.ManageUserUserStatusUpdate(objResourcesInfo.UserID, objResourcesInfo.LoginIsActive, Convert.ToDateTime(objResourcesInfo.DateOfDeactivation).ToString(), Convert.ToDateTime(objResourcesInfo.DateOfActivation).ToString());
        }
        else
        {
            bSuccess = _ProcessSrv.ManageUserUserStatusUpdate(objResourcesInfo.UserID, objResourcesInfo.LoginIsActive,Convert.ToDateTime(objResourcesInfo.DateOfDeactivation).ToString(), Convert.ToDateTime(objResourcesInfo.DateOfActivation).ToString());
        }
        return RedirectToAction("ManageUsers");
    }

    [HttpGet]
    public IActionResult EditManageUsers(int iId)
    {
        var objDepartmentInfo = new ManageUsersDetails();

        try
        {
            PopulateDropdown();
            objDepartmentInfo = _ProcessSrv.GetManageUsersById(iId);
            objDepartmentInfo.UserRoleID = Convert.ToInt32(objDepartmentInfo.UserRole);
            objDepartmentInfo.UserID = iId;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }


        return PartialView("_EditManageUsers", objDepartmentInfo);
    }


    [HttpPost]
    public IActionResult EditManageUsers(ManageUsersDetails objResourcesInfo)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.ManageUsersUpdate(objResourcesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageUsers");
    }

    [HttpGet]
    public IActionResult CheckLoginNameExists(string loginName, int userId = 0)
    {
        try
        {
            // Skip validation if it's the same user (for edit mode)
            if (userId > 0)
            {
                var currentUser = _ProcessSrv.GetManageUsersById(userId);
                if (currentUser != null && currentUser.LoginName == loginName)
                {
                    return Json(new { exists = false });
                }
            }

            // Check if login name exists using the existing method
            bool exists = _ProcessSrv.UserExist_ByLoginName_OrgGroupID(loginName, Convert.ToInt32(_UserDetails.OrgGroupID));
            return Json(new { exists = exists });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { exists = false, error = ex.Message });
        }
    }

    [HttpPost]
    public IActionResult FindDomains()
    {
        try
        {
           // _CVLogger.LogInfoApp("Starting domain discovery request");

            var domains = new List<SelectListItem>();

            try
            {
                // Get the current domain name from environment
                string currentDomain = Environment.UserDomainName;
                string machineName = Environment.MachineName;

                // Check if the machine is part of a domain and filter out machine domains
                if (!currentDomain.Equals(machineName, StringComparison.OrdinalIgnoreCase))
                {
                    // Validate that this is a legitimate network domain, not a machine domain
                    if (IsValidNetworkDomain(currentDomain, machineName))
                    {
                        domains.Add(new SelectListItem
                        {
                            Text = currentDomain,
                            Value = currentDomain
                        });
                        _CVLogger.LogInfo($"Discovered valid network domain: {currentDomain}");
                    }
                    else
                    {
                        _CVLogger.LogInfo($"Filtered out machine domain: {currentDomain}");
                    }

                    // Try to get additional domain information using DNS
                    try
                    {
                        // Get the DNS domain name if available
                        string dnsDomain = System.Net.NetworkInformation.IPGlobalProperties.GetIPGlobalProperties().DomainName;
                        if (!string.IsNullOrEmpty(dnsDomain) &&
                            !dnsDomain.Equals(currentDomain, StringComparison.OrdinalIgnoreCase) &&
                            IsValidNetworkDomain(dnsDomain, machineName))
                        {
                            domains.Add(new SelectListItem
                            {
                                Text = dnsDomain,
                                Value = dnsDomain
                            });
                            _CVLogger.LogInfo($"Discovered valid DNS domain: {dnsDomain}");
                        }
                        else if (!string.IsNullOrEmpty(dnsDomain))
                        {
                            _CVLogger.LogInfo($"Filtered out DNS machine domain: {dnsDomain}");
                        }
                    }
                    catch (Exception dnsEx)
                    {
                        _CVLogger.LogInfo($"Could not retrieve DNS domain: {dnsEx}");
                    }
                }
                else
                {
                    _CVLogger.LogInfo("Machine is not part of a domain environment - no domains to discover");
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }

            // Only add default option if we found actual domains
            if (domains.Count > 0)
            {
                domains.Insert(0, new SelectListItem
                {
                    Text = "-- Select Domain --",
                    Value = ""
                });
            }

            _CVLogger.LogInfo($"Domain discovery completed. Found {(domains.Count > 0 ? domains.Count - 1 : 0)} actual network domains");

            if (domains.Count > 0)
            {
                return Json(new {
                    success = true,
                    domains = domains,
                    message = $"Found {domains.Count - 1} network domain(s)"
                });
            }
            else
            {
                return Json(new {
                    success = false,
                    message = "No network domains found. Machine may not be domain-joined."
                });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new {
                success = false,
                message = "Error discovering domains. Please enter domain name manually.",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Validates if a domain name is a legitimate network domain and not a machine domain
    /// </summary>
    /// <param name="domainName">The domain name to validate</param>
    /// <param name="machineName">The machine name to compare against</param>
    /// <returns>True if it's a valid network domain, false if it's a machine domain</returns>
    private bool IsValidNetworkDomain(string domainName, string machineName)
    {
        if (string.IsNullOrWhiteSpace(domainName))
            return false;

        try
        {
            // Filter out domains that are clearly machine-based
            // 1. Domain should not be the same as machine name
            if (domainName.Equals(machineName, StringComparison.OrdinalIgnoreCase))
                return false;

            // 2. Domain should not start with machine name followed by common suffixes
            string[] machineBasedSuffixes = { ".local", ".domain", ".workgroup", ".home" };
            foreach (var suffix in machineBasedSuffixes)
            {
                if (domainName.StartsWith(machineName + suffix, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            // 3. Domain should contain at least one dot (proper FQDN format)
            if (!domainName.Contains("."))
                return false;

            // 4. Domain should not be common local/workgroup names
            string[] localDomainNames = { "workgroup", "workgroup.local", "home", "local" };
            if (localDomainNames.Any(local => domainName.Equals(local, StringComparison.OrdinalIgnoreCase)))
                return false;

            // 5. Basic DNS name validation - should not contain invalid characters
            if (domainName.Contains(" ") || domainName.Contains("_") || domainName.StartsWith(".") || domainName.EndsWith("."))
                return false;

            // 6. Try to validate domain format using basic regex pattern
            var domainPattern = @"^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$";
            if (!System.Text.RegularExpressions.Regex.IsMatch(domainName, domainPattern))
                return false;

            return true;
        }
        catch (Exception ex)
        {
            _CVLogger.LogInfo($"Error validating domain {domainName}: {ex.Message}");
            return false;
        }
    }

    [HttpPost]
    public IActionResult FindUsers(string domainName, string searchTerm = "")
    {
        try
        {
            _CVLogger.LogInfo($"Starting user discovery request for domain: {domainName}, search term: {searchTerm}");

            var users = GetDomainUsers(domainName, searchTerm);

            // Only add default option if we found actual users
            if (users.Count > 0)
            {
                users.Insert(0, new SelectListItem
                {
                    Text = "-- Select User --",
                    Value = ""
                });
            }

            _CVLogger.LogInfo($"User discovery completed. Found {(users.Count > 0 ? users.Count - 1 : 0)} actual users");

            if (users.Count > 0)
            {
                string message = string.IsNullOrEmpty(searchTerm)
                    ? $"Found {users.Count - 1} user(s) in domain {domainName}"
                    : $"Found {users.Count - 1} user(s) matching '{searchTerm}' in domain {domainName}";

                return Json(new {
                    success = true,
                    users = users,
                    message = message
                });
            }
            else
            {
                string message;
                if (string.IsNullOrEmpty(domainName))
                {
                    message = "Please select a domain first.";
                }
                else if (!string.IsNullOrEmpty(searchTerm))
                {
                    message = $"No users found matching '{searchTerm}' in domain {domainName}. Try a different search term or leave blank to see all users.";
                }
                else
                {
                    message = $"No users found in domain {domainName}. You may need to enter username manually.";
                }

                return Json(new {
                    success = false,
                    message = message
                });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new {
                success = false,
                message = "Error discovering users. Please enter username manually.",
                error = ex.Message
            });
        }
    }

    public IActionResult GetDepartmentByID(int iDepartmentId)
    {
        try
        {
            List<ResourcesInfo> lstResourcesInfo = _ProcessSrv.GetResourcesListByGrpOrgUnitDept(Convert.ToInt32(_UserDetails.OrgGroupID), Convert.ToInt32(_UserDetails.OrgID), iUnitID, iDepartmentId);
            if (iDepartmentId > 0)
            {
                lstResourcesInfo = lstResourcesInfo.Where(x => x.DepartmentID == iDepartmentId).ToList();
                if (lstResourcesInfo == null || !lstResourcesInfo.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterManageUsers", lstResourcesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageUsers");
    }

    
    public IActionResult GetFileredResourceList(int iOrgID = 0, int iUnitId = 0, int iDepartmentId = 0, int iUserRoleId = 0,string UserTypeID = "",int iCompanyId = 0 , int iLoginStatus = 0)
    {
        try
        {
            PopulateDropdown();
            List<ResourcesInfo> lstResourcesInfo = _ProcessSrv.GetResourcesListByGrpOrgUnitDept(_UserDetails.OrgGroupID, iOrgID, iUnitId, iDepartmentId);
            

           
            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {                    
                    if (_UserDetails.OrgGroupID > 0)
                    {
                        lstResourcesInfo = lstResourcesInfo.Where(x => x.OrgGroupID == _UserDetails.OrgGroupID || x.OrgGroupID == 0).ToList();
                    }
                    else
                    {
                        lstResourcesInfo = new List<ResourcesInfo>();
                    }

                }
                else
                {
                   
                    if (iOrgID > 0)
                    {                        
                        lstResourcesInfo = lstResourcesInfo.Where(x => x.OrgID == iOrgID).ToList();
                    }
                    if (iUnitId > 0)
                    {                        
                        lstResourcesInfo = lstResourcesInfo.Where(x => x.UnitID == iUnitId).ToList();
                    }
                    if (iDepartmentId > 0)
                    {                       
                        lstResourcesInfo = lstResourcesInfo.Where(x => x.DepartmentID == iDepartmentId).ToList();
                    }
                    if (iUserRoleId > 0)
                    {                       
                        lstResourcesInfo = lstResourcesInfo.Where(x => Convert.ToInt32(x.UserRole) == iUserRoleId).ToList();
                    }
                    if(UserTypeID != "" && UserTypeID !="-- Select Type --")
                    {
                        lstResourcesInfo = lstResourcesInfo.Where(x => x.TypeName == UserTypeID).ToList();
                    }
                    if(iCompanyId > 0)
                    {
                        lstResourcesInfo = lstResourcesInfo.Where(x => x.VendorID == iCompanyId).ToList();
                    }  
                   
                                       
                }
            }
            ViewBag.ResourceInfo = lstResourcesInfo;
            //return PartialView("_FilterManageUsers", lstResourcesInfo);
            //return Json(lstResourcesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_FilterManageUsers");        
    }


    public IActionResult GetResourceListByOrgID(int iOrgId)
    {
        try
        {
            List<ResourcesInfo> lstResourcesInfo = _ProcessSrv.GetResourcesListByGrpOrgUnitDept(Convert.ToInt32(_UserDetails.OrgGroupID),iOrgId, iUnitID, iDepartmentID);
            if (iOrgId > 0)
            {
                lstResourcesInfo = lstResourcesInfo.Where(x => x.OrgID == iOrgId).ToList();
                if (lstResourcesInfo == null || !lstResourcesInfo.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterManageUsers", lstResourcesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageUsers");
    }

    public IActionResult GetUnitByID(int iUnitId)
    {
        try
        {
            List<ResourcesInfo> lstResourcesInfo = _ProcessSrv.GetResourcesListByGrpOrgUnitDept(Convert.ToInt32(_UserDetails.OrgGroupID), Convert.ToInt32(_UserDetails.OrgID), iUnitId, iDepartmentID);
            if (iUnitId > 0)
            {
                lstResourcesInfo = lstResourcesInfo.Where(x => x.UnitID == iUnitId).ToList();
                if (lstResourcesInfo == null || !lstResourcesInfo.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterManageUsers", lstResourcesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageUsers");
    }


    public IActionResult GetUserRoleByID(int iUserRoleId)
    {
        try
        {
            List<ResourcesInfo> lstResourcesInfo = _ProcessSrv.GetResourcesListByGrpOrgUnitDept(Convert.ToInt32(_UserDetails.OrgGroupID), Convert.ToInt32(_UserDetails.OrgID), iUnitID, iDepartmentID);
            if (iUserRoleId > 0)
            {
                lstResourcesInfo = lstResourcesInfo.Where(x => Convert.ToInt32(x.UserRole) == iUserRoleId).ToList();
                if (lstResourcesInfo == null || !lstResourcesInfo.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterManageUsers", lstResourcesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageUsers");
    }


    public IActionResult GetCompanyMasterByID(int iCompanyId)
    {
        try
        {
            List<ResourcesInfo> lstResourcesInfo = _ProcessSrv.GetResourcesListByGrpOrgUnitDept(Convert.ToInt32(_UserDetails.OrgGroupID), Convert.ToInt32(_UserDetails.OrgID), iUnitID, iDepartmentID);
            if (iCompanyId > 0)
            {
                lstResourcesInfo = lstResourcesInfo.Where(x => x.VendorID == iCompanyId).ToList();
                if (lstResourcesInfo == null || !lstResourcesInfo.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterManageUsers", lstResourcesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageUsers");
    }

    private List<SelectListItem> GetDomainUsers(string domainName, string searchName = "")
    {
        var users = new List<SelectListItem>();

        try
        {
            _CVLogger.LogInfo($"Starting real-time LDAP query for domain: {domainName}, search: {searchName}");

            // Validate domain name
            if (string.IsNullOrEmpty(domainName))
            {
                _CVLogger.LogInfo("Domain name is empty, cannot retrieve users");
                return users;
            }

            // Construct LDAP path
            string ldapPath = $"LDAP://{domainName}";

            using (var entry = new DirectoryEntry(ldapPath))
            {
                using (var searcher = new DirectorySearcher(entry))
                {
                    // Build LDAP filter for active users
                    string baseFilter = "(&(objectClass=user)(objectCategory=person)(!userAccountControl:1.2.840.113556.1.4.803:=2))";

                    if (!string.IsNullOrEmpty(searchName))
                    {
                        searcher.Filter = $"(&{baseFilter}(|(cn=*{searchName}*)(sAMAccountName=*{searchName}*)(displayName=*{searchName}*)))";
                        _CVLogger.LogInfo($"Using search filter for: {searchName}");
                    }
                    else
                    {
                        searcher.Filter = baseFilter;
                        _CVLogger.LogInfo("Retrieving all active users from domain");
                    }

                    // Specify properties to load for performance
                    searcher.PropertiesToLoad.AddRange(new[] {
                        "sAMAccountName",
                        "displayName",
                        "cn",
                        "givenName",
                        "sn",
                        "userAccountControl"
                    });

                    // Set performance limits
                    searcher.SizeLimit = 100; // Maximum 100 users
                    searcher.ServerTimeLimit = TimeSpan.FromSeconds(30); // 30 second timeout
                    searcher.PageSize = 50; // Paging for large results

                    var userCount = 0;

                    // Execute LDAP query
                    using (var results = searcher.FindAll())
                    {
                        foreach (SearchResult result in results)
                        {
                            try
                            {
                                string samAccount = GetPropertyValue(result, "sAMAccountName");

                                // Skip if no username or is system account
                                if (string.IsNullOrEmpty(samAccount) || IsSystemAccount(samAccount))
                                    continue;

                                string displayName = GetUserDisplayName(result);

                                users.Add(new SelectListItem
                                {
                                    Text = displayName,
                                    Value = samAccount
                                });

                                userCount++;

                                // Safety limit
                                if (userCount >= 100)
                                {
                                    _CVLogger.LogInfo("Reached maximum user limit (100), stopping search");
                                    break;
                                }
                            }
                            catch (Exception userEx)
                            {
                                _CVLogger.LogInfo($"Error processing user: {userEx.Message}");
                                // Continue with next user
                            }
                        }
                    }
                }
            }

            _CVLogger.LogInfo($"Successfully retrieved {users.Count} real-time users from domain {domainName}");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            _CVLogger.LogInfo($"Failed to retrieve users from domain {domainName}: {ex.Message}");

            // Fallback: Add current user
            AddCurrentUserFallback(users);
        }

        return users;
    }

    public void PopulateDropdown()
    {
        try
        {                      
            ViewBag.DepartmentInfo = new SelectList(_Utilities.GetDepartmentAllListForDropdown(), "DepartmentID", "DepartmentName");
            ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString()), "Id", "OrganizationName");
            ViewBag.OrgGroup = new SelectList(_Utilities.GetOrgGroupList(), "OrgGroupID", "OrganizationGroupName");
            ViewBag.OrgUnit = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails.OrgID), "UnitID", "UnitName");
            ViewBag.UserRoleMasterInfo = new SelectList(_Utilities.GetUserRoleMasterByOrgID(Convert.ToInt32(_UserDetails.OrgID)), "UserRoleID", "UserRoleDetails");
            ViewBag.TypeMasterInfo = new SelectList(_Utilities.GetTypeInfoByEntityID(Convert.ToInt16(BCPEnum.EntityType.ResourcePlan)), "TypeID", "TypeName");
            ViewBag.CompanyMasterInfo = new SelectList(_Utilities.GetCompanyMasterInfoList(Convert.ToInt32(_UserDetails.OrgID)), "CompanyID", "CompanyName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private string GetPropertyValue(SearchResult result, string propertyName)
    {
        try
        {
            var property = result.Properties[propertyName];
            return property.Count > 0 ? property[0]?.ToString() : null;
        }
        catch
        {
            return null;
        }
    }

    private string GetUserDisplayName(SearchResult result)
    {
        try
        {
            string samAccount = GetPropertyValue(result, "sAMAccountName");

            // Return only sAMAccountName for AD login names
            return samAccount ?? "Unknown User";
        }
        catch (Exception ex)
        {
            _CVLogger.LogInfo($"Error getting display name: {ex.Message}");
            return GetPropertyValue(result, "sAMAccountName") ?? "Unknown User";
        }
    }

    private bool IsSystemAccount(string samAccountName)
    {
        if (string.IsNullOrEmpty(samAccountName))
            return true;

        // Common system account patterns
        string[] systemAccountPrefixes = {
            "krbtgt", "guest", "administrator", "defaultaccount",
            "wdagutilityaccount", "healthmailbox", "extest",
            "sm_", "svc_", "service_", "sql", "iis", "asp",
            "exchange", "sharepoint", "system", "network",
            "anonymous", "nobody", "daemon", "bin", "sync"
        };

        string lowerAccount = samAccountName.ToLower();

        // Check for system account patterns
        foreach (string prefix in systemAccountPrefixes)
        {
            if (lowerAccount.StartsWith(prefix))
                return true;
        }

        // Check for accounts ending with $ (computer accounts)
        if (lowerAccount.EndsWith("$"))
            return true;

        return false;
    }

    private void AddCurrentUserFallback(List<SelectListItem> users)
    {
        try
        {
            string currentUser = Environment.UserName;
            if (!string.IsNullOrEmpty(currentUser))
            {
                users.Add(new SelectListItem
                {
                    Text = $"{currentUser} (Current User - AD Connection Failed)",
                    Value = currentUser
                });
                _CVLogger.LogInfo($"Added current user as fallback: {currentUser}");
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogInfo($"Error adding current user fallback: {ex.Message}");
        }
    }

    [HttpGet]
    public JsonResult GetAllUnits(int iOrgID)
    {
        try
        {
            var objDepartmentList = _Utilities.BindUnit(iOrgID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID)
    {
        try
        {
            var objDepartmentList = _Utilities.BindFunction(iUnitID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllSubDepartments(int iDepartmentID)
    {
        try
        {
            var objSubDepartmentList = _Utilities.BindSubFunction(iDepartmentID);
            return Json(objSubDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }
}

