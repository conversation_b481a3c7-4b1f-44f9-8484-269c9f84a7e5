﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BCM.UI.Areas.BCMTeams.Controllers;
[Area("BCMTeams")]
public class AddBCMGroupMembersController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    List<ResourcesInfo> _lstResourcesInfo = new List<ResourcesInfo>();
    List<BCMGroupResources> _lstBCMGroupResources = new List<BCMGroupResources>();
    public AddBCMGroupMembersController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    #region AddBCMGroupMember

    [HttpGet]
    public IActionResult AddBCMGroupMembers()
    {
        int iGrpMapId = Convert.ToInt32(HttpContext.Session.GetString("objGrpMapID"));
        try
        {
            ViewBag.UnitList = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails.UserRoleID.ToString(), _UserDetails.OrgID), "UnitID", "UnitName");
            ViewBag.DepartmentList = new SelectList(_Utilities.GetDepartmentByUnitId(0), "DepartmentID", "DepartmentName");
            ViewBag.Facilities = new SelectList(_Utilities.GetFacilitieslistByUnitID(0, _UserDetails.OrgID, 0), "FacilityID", "FacilityName");
            var objFinalResourceList = GetFinalResourceList();
            return PartialView("_AddBCMGroupMember", objFinalResourceList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BCMGroupmembers", "BCMGroupmembers", new { iGrpMapID = iGrpMapId });
    }

    [HttpPost]
    public IActionResult AddBCMGroupMember(string checkedCheckBoxes)
    {
        BCMGroupResources objResource = new BCMGroupResources();
        try
        {
            var objCheckBoxIds = checkedCheckBoxes?.Split(',')?.ToList() ?? new List<string>();
            foreach (var resourceID in objCheckBoxIds)
            {
                objResource.ResourceId = Convert.ToInt32(resourceID);
                objResource.GroupMapID = Convert.ToInt32(HttpContext.Session.GetString("objGrpMapID"));
                objResource.OrgID = Convert.ToInt32(HttpContext.Session.GetString("objOrgID"));
                objResource.CreatedBy = _UserDetails.UserID;
                int iRecord = _ProcessSrv.BCMGroupResourceSave(objResource);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("BCMGroupmembers", "BCMGroupmembers", new { iGrpMapID = objResource.GroupMapID });
    }

    #endregion

    #region Normal And BCM  Resources List

    [HttpGet]
    public SubResourceModel GetFinalResourceList(int iUnitID = 0, int iDepartmentID = 0, int iFacilityID = 0)
    {
        SubResourceModel objSubResourceModel = new SubResourceModel();
        try
        {
            List<BCMGroupResources> lstBCMGroupResources = _ProcessSrv.GetBCMGroupMemberResourcesList(0, 0, Convert.ToInt32(HttpContext.Session.GetString("objGrpMapID")), 0, 0, 0, "");
            objSubResourceModel = new SubResourceModel
            {
                MainResources = GetResourcesInfo(iUnitID, iDepartmentID, iFacilityID),
                SubBCMResourcesID = lstBCMGroupResources.Select(x => x.ResourceId).ToList()
            };
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return objSubResourceModel;
    }

    [HttpGet]
    public List<ResourcesInfo> GetResourcesInfo(int iUnitID, int iDepartmentID, int iFacilityID)
    {
        try
        {
            //iUnitID = iUnitID == 0 ? _UserDetails.UnitID : iUnitID;
            int _iOrgIDSec = Convert.ToInt32(HttpContext.Session.GetString("objOrgID"));
            _iOrgIDSec = _iOrgIDSec == 0 ? _UserDetails.OrgID : _iOrgIDSec;
            _lstResourcesInfo = _ProcessSrv.GetResourcesListByUnitIDAndDepartmentID(_iOrgIDSec, iUnitID, iDepartmentID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return _lstResourcesInfo;
    }

    [HttpGet]
    public IActionResult FilteredUserList(int iUnitID = 0, int iDepartmentID = 0, int iFacilityID = 0)
    {
        int iGrpMapId = Convert.ToInt32(HttpContext.Session.GetString("objGrpMapID"));
        try
        {
            var objResource = GetFinalResourceList(iUnitID, iDepartmentID, iFacilityID);
            return PartialView("_FilteredUserList", objResource);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BCMGroupmembers", "BCMGroupmembers", new { iGrpMapID = iGrpMapId });

    }

    #endregion

}

