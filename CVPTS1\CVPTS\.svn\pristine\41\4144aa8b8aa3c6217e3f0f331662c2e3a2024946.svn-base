﻿using DevExpress.XtraRichEdit.Commands;

namespace BCM.UI.Areas.BCMReports.ReportModels.BCMManagementReviewReport;

public class BCMManagementReviewReportVms
{
    public string? UserName {  get; set; }
    public ReviewReportMasterVm? ReviewReportMasterVms { get; set; }
    public ReviewReportMoMItemVm? ReviewReportMoMItemVms { get; set; }
    public List<BCMPolicies> BCMPoliciesVms = new();
    public List<AuditVm> AuditInfoVms = new();
    public List<BCMStrategyVm> BCMStrategyVms = new();
}

public class ReviewReportMasterVm
{
    public int SrNo { get; set; }
    public string? OrgName { get; set; }
    public string? ReportName {  get; set; }
    public string? Status { get; set; }
    public string? Owner {  get; set; }
    public string? ReferenceNo { get; set; }
    public string? PlanDate {  get; set; }
    public string? Describtion {  get; set; }
}
public class ReviewReportMoMItemVm
{
    public int SrNo { get; set; }
    public string? MOMItem { get; set; }
    public string? ReviewType { get; set; }
    public string? Owner { get; set; }
    public string? ReviewDate { get; set; }
    public string? ClosingDate { get; set; }
}
public class BCMPolicies
{
    public int SrNo { get; set; }
    public string? BCMEntity { get; set; }
    public string? EntityType { get; set; }
    public string? Owner { get; set; }
    public string? ReviewDate { get; set; }
    public string? LastReviewDate { get; set; }
    public string? RTO { get; set; }
    public string? RPO { get; set; }
    public string? MTR { get; set; }
}
public class AuditVm
{
    public int SrNo { get; set; }
    public string? AuditName { get; set; }
    public string? AuditorName { get; set; }
    public string? AuditorMobile { get; set; }
    public string? AuditorEmail { get; set; }
    public string? AuditeeName { get; set;}    
    public string? AuditeeMobile { get; set; }
    public string? AuditeeEmail { get; set; }
    public string? PlanStartDate { get; set; }
    public string? PlanEndDate { get; set; }
    public string? ActualStartDate { get; set; }
    public string? ActualEndDate {  get; set; }
}
public class BCMStrategyVm
{
    public int SrNo { get; set; }
    public string? StrategyCode { get; set; }
    public string? StrategyName { get; set; }
    public string? StrategyOwner { get; set; }
    public string? StrategyBelongsTo { get; set; }
    public string? ReviewDate { get; set; }
    public string? LastReviewDate { get; set; }
}