﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Security.Cryptography;


namespace BCM.UI.Areas.BCMPerformanceEvaluation.Controllers;
[Area("BCMPerformanceEvaluation")]
public class KPIMeasurementMasterController : Controller
{

    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;


    ManageUsersDetails _UserDetails = new ManageUsersDetails();
    int iEntityTypeID = 0;


    public KPIMeasurementMasterController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _UserDetails = _Utilities.LoginUserDetails();

        if (_UserDetails == null)
        {
            RedirectToAction("Login", "Login");
        }
        _CVLogger = CVLogger;
    }

    public IActionResult Index()
    {
        return View();
    }

    [HttpGet]
    public IActionResult KPIMeasurementMaster()
    {
        List<KPIMeasurementMasters> lstKPIMeasurementMasters = new List<KPIMeasurementMasters>();
        try
        {
            //PopulateDropDown();

             lstKPIMeasurementMasters = _ProcessSrv.GetKPIMeasurementMasterlist();

            
            //ViewBag.KPIDetails = lstKPIMeasurementMasters;

            //ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View(lstKPIMeasurementMasters);
    }

    [HttpGet]
    public IActionResult AddKPIMeasurementMaster()
    {
        return PartialView("_AddKPIMeasurementMaster", new KPIMeasurementMasters());
    }

    [HttpPost]
    public IActionResult AddKPIMeasurementMaster(KPIMeasurementMasters kPIMeasurementMasters)
    { 
        try
        {
            kPIMeasurementMasters.OrgId = _UserDetails.OrgID;
            kPIMeasurementMasters.IsActive = 1;
            kPIMeasurementMasters.CreatedDate = DateTime.Now;
            kPIMeasurementMasters.CreatedBy = _UserDetails.UserID;



            int ID = _ProcessSrv.KPIMeasurementMasters_Save(kPIMeasurementMasters);
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("KPIMeasurementMaster");

    }

    [HttpGet]
    public IActionResult EditKPIMeasurementMaster(int iId)
    {
        //HttpContext.Session.SetInt32("iId", Convert.ToInt32(iId));
        KPIMeasurementMasters objKPIMeasurementMasters = _ProcessSrv.GetKPIMeasurementMasterslistByID(Convert.ToInt32(iId));
        objKPIMeasurementMasters.ID = Convert.ToInt32(iId);
        return PartialView("_EditKPIMeasurementMaster", objKPIMeasurementMasters);
    }

    [HttpPost]
    public IActionResult EditKPIMeasurementMaster(KPIMeasurementMasters kPIMeasurementMasters)
    {
        //int? iId = HttpContext.Session.GetInt32("iId");

        bool Success = false;
        try
        {
            kPIMeasurementMasters.ID = Convert.ToInt32(kPIMeasurementMasters.ID);
            kPIMeasurementMasters.OrgId = _UserDetails.OrgID;
            kPIMeasurementMasters.IsActive = 1;
            kPIMeasurementMasters.CreatedDate = DateTime.Now;
            kPIMeasurementMasters.CreatedBy = _UserDetails.UserID;

             Success = _ProcessSrv.KPIMeasurementMasters_Update(kPIMeasurementMasters);
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
        //HttpContext.Session.Remove("iId");
        return RedirectToAction("KPIMeasurementMaster");

    }

    [HttpGet]
    public IActionResult DeleteKPIMeasurementMaster(int iId)
    {
        //HttpContext.Session.SetInt32("iId", Convert.ToInt32(iId));
        KPIMeasurementMasters objKPIMeasurementMasters = _ProcessSrv.GetKPIMeasurementMasterslistByID(Convert.ToInt32(iId));
        objKPIMeasurementMasters.ID = Convert.ToInt32(iId);
        return PartialView("_DeleteKPIMeasurementMaster", objKPIMeasurementMasters);
    }

    [HttpPost]
    public IActionResult DeleteKPIMeasurementMaster(KPIMeasurementMasters kPIMeasurementMasters)
    {
        //int? iId = HttpContext.Session.GetInt32("iId");

        //bool Success = false;
        try
        {
            bool Success = _ProcessSrv.KPIMeasurementMastersDeleteById(Convert.ToInt32(kPIMeasurementMasters.ID), Convert.ToInt32(_UserDetails.UserID));
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("KPIMeasurementMaster");

    }
}

