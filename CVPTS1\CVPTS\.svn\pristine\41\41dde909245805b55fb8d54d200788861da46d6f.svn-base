﻿@model IEnumerable<BCM.BusinessClasses.DepartmentInfo>

@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int index = 1;
    var selectedOrgID = ViewBag.selectedOrgID;
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers


<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Department </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <div class="dropdown">
            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                <i class="cv-filter align-middle" title="View Filter"></i>
            </button>
            <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                <div class="mb-3">
                    <label>Organizations</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                        <select id="orglist" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example">
                            <option selected value="0">-- All Organizations --</option>
                            @foreach (var organization in ViewBag.OrgInfo)
                            {
                                <!option value="@organization.Value" @(organization.Value == selectedOrgID.ToString() ? "selected=\"selected\"" : "")>@organization.Text</!option>
                            }
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Units</label>
                    <div class="input-group w-30">
                        <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                        <select id="unitlist" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example">
                            <option selected value="0">-- All Units --</option>
                            @foreach (var objUnit in ViewBag.OrgUnit)
                            {
                                <option value="@objUnit.Value">@objUnit.Text</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="mb-3" style="display:none">
                    <label>Departments</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-department"></i></span>
                        <select id="departmentlist" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example">
                            <option selected value="0">-- All Departments --</option>
                            @foreach (var objDepartment in ViewBag.DepartmentInfo)
                            {
                                <option value="@objDepartment.Value">@objDepartment.Text</option>
                            }
                        </select>
                    </div>
                </div>
            </form>
        </div>
        <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="Modal"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant card border-0" id="tblContent">
    <table id="example" class="table table-hover" style="width:100%">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Department Name</th>
                <th>Unit</th>
                
                <th>Department Head</th>
                <th>Alt Department Head</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="tblBody">
            @await Html.PartialAsync("_FilterDepartment")
        </tbody>
    </table>
</div>

<div class="modal fade" id="Modal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle"></h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-data">
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div> *@

<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div>
<!-- Delete Modal -->
@section Scripts {
    <script>
        $(document).ready(function () {
            $(document).on("click", ".Closebtn", function(){
              location.reload();
            });

            $('#btnCreate').click(function () {
                $.get('@Url.Action("AddDepartment", "Department")', function (data) {
                    $('.modal-data').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Department Configuration');
                });
            });

            $(document).on('click', '.btnEdit', function () {
                var iId = $(this).data('id');
                $.get('@Url.Action("EditDepartment", "Department")', { iId: iId }, function (data) {
                    $('.modal-data').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Update Department');
                });
            })

            $(document).on('click', '.btnDelete', function () {
                var iId = $(this).data('id');
                $.get('@Url.Action("DeleteDepartment", "Department")', { iId: iId }, function (data) {
                    $('#deleteBody').html(data);
                    $('#DeleteModal').modal('show');
                    $('#modaltitle').text('Delete Department');
                });
            })

            // $('body').on('submit', 'form', function (e) {
            //     e.preventDefault();
            //     var form = $(this);
            //     $.ajax({
            //         type: form.attr('method'),
            //         url: form.attr('action'),
            //         data: form.serialize(),
            //         success: function (data) {
            //             $('#Modal').modal('hide');
            //             location.reload();
            //         },
            //         error: function (xhr, status, error) {
            //             console.log(error);
            //             console.error(xhr.status);
            //             console.error(xhr.responseText);
            //         }
            //     });
            // });

            $('#departmentlist').change(function () {
                var iDepartmentId = $(this).val();
                $.get('@Url.Action("GetDepartmentByID", "Department")', { iDepartmentId: iDepartmentId }, function (data) {
                    var tblBody = $('#tblBody');
                    tblBody.empty();
                    $('#example tbody').html(data);
                });
            });

            $('#unitlist').change(function () {
                var iUnitId = $(this).val();
                var ddlUnitVal = $('#unitlist').val();
                            var ddlOrganizationVal = $('#orglist').val();
                             // var ddlDepartmentVal = $('#departmentlist').val();
                             // var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                        if (iUnitId)
                        {
                            // $.ajax({
                            //      url: '@Url.Action("GetDepartmentsByUnitID", "Department")',
                            //      type: 'GET',
                            //      data: {iOrgID:ddlOrganizationVal, iUnitID: ddlUnitVal },
                            //      success: function (data) {
                            //          console.log(data);
                            //          var ddlDepartment = $('#departmentlist');
                            //          ddlDepartment.empty();
                            //          ddlDepartment.append('<option value="0">-- All Departments --</option>');
                            //          $.each(data, function (index, item) {
                            //              ddlDepartment.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                            //          });
                            //      }
                            // })
                            $.get('@Url.Action("GetUnitByID", "Department")', { iUnitId: iUnitId }, function (data) {
                                var tblBody = $('#tblBody');
                                tblBody.empty();
                                $('#example tbody').html(data);
                            });
                        }                
            });

            // $('#orglist').change (function () {
            //     var ddlOrganizationVal = $('#orglist').val();
            //     var ddlUnitVal = $('#unitlist').val();
            //     debugger;
            //     var ddlDepartmentVal = $('#departmentlist').val();
            //     var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
            //         if (ddlOrganizationVal)
            //         {
            //             $.ajax({
            //                         url: '@Url.Action("GetAllUnits", "Department")',
            //                         type: 'GET',
            //                         data: { iOrgID: ddlOrganizationVal },
            //                         success: function (data) {
            //                             debugger;
            //                             var ddlUnit = $('#ddlUnit');
            //                             ddlUnit.empty();
            //                             ddlUnit.append('<option value="0">-- All Units --</option>');


            //                             $.each(data, function (index, item) {
            //                                 ddlUnit.append('<option value="' + item.unitID + '">' + item.unitName + '</option>')

            //                             });
            //                         },
            //                         error:function(Error){
            //                             console.log(Error,"Error Block");
            //                         }

            //             })
            //         }
            //         $.get('@Url.Action("GetUnitByID", "Department")', { iUnitId: ddlUnitVal }, function (data) {
            //         var tblBody = $('#tblBody');
            //         tblBody.empty();
            //         $('#example tbody').html(data);
            //     });
            // });


            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        //Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    </script>
}