@{
    ViewData["Title"] = "Training Exam Results";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Scripts
{
    <style>
        .display-1 {
            font-size: 4rem;
        }

        .display-4 {
            font-size: 2.5rem;
            font-weight: 600;
        }

        .progress {
            border-radius: 10px;
            background-color: #e9ecef;
        }

        .progress-bar {
            border-radius: 10px;
            transition: width 1.5s ease-in-out;
        }

        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .btn {
            border-radius: 6px;
            padding: 10px 20px;
        }

        .badge {
            padding: 8px 12px;
            border-radius: 6px;
        }

        @@media print {
            .btn, .no-print {
                display: none !important;
            }

            .card {
                border: 1px solid #dee2e6 !important;
                box-shadow: none !important;
            }
        }
    </style>

    <script>
        $(document).ready(function() {
            // Initialize any charts or animations here
            animateProgressBars();

            $('#btnRetakeExam').on('click', function() {
                if (confirm('Are you sure you want to retake this exam? Your current results will be reset.')) {
                    window.location.href = '@Url.Action("TrainingQuestionPaper", "TrainingandExamination", new { Area = "BCMTraining", TrainingID = ViewBag.TrainingId, watch = 1, PublishID = ViewBag.PublishID })';
                }
            });

            $('#btnBackToTraining').on('click', function() {
                window.location.href = '@Url.Action("TrainingandExamination", "TrainingandExamination", new { Area = "BCMTraining" })';
            });
        });

        function animateProgressBars() {
            $('.progress-bar').each(function() {
                var $this = $(this);
                var width = $this.data('width');
                $this.animate({ width: width + '%' }, 1500);
            });
        }

        function printResults() {
            window.print();
        }
    </script>
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-success text-white">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-trophy me-2"></i>
                    <div>
                        <h5 class="mb-0">Training Exam Results</h5>
                        <small>@ViewBag.TrainingName</small>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-light btn-sm me-2" onclick="printResults()">
                        <i class="fas fa-print me-1"></i>Print
                    </button>
                    @if (ViewBag.IsPassed == true)
                    {
                        <span class="badge bg-success fs-6">
                            <i class="fas fa-check-circle me-1"></i>PASSED
                        </span>
                    }
                    else
                    {
                        <span class="badge bg-danger fs-6">
                            <i class="fas fa-times-circle me-1"></i>FAILED
                        </span>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-primary mb-2">@ViewBag.TotalQuestions</div>
                    <h6 class="text-muted mb-0">Total Questions</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-success mb-2">@ViewBag.CorrectAnswers</div>
                    <h6 class="text-muted mb-0">Correct Answers</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-warning mb-2">@ViewBag.IncorrectAnswers</div>
                    <h6 class="text-muted mb-0">Incorrect Answers</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-4 @(ViewBag.IsPassed == true ? "text-success" : "text-danger") mb-2">
                        @ViewBag.ScorePercentage%
                    </div>
                    <h6 class="text-muted mb-0">Score</h6>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <h6 class="card-title mb-3">Performance Overview</h6>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">Overall Score</label>
                    <div class="progress mb-3" style="height: 25px;">
                        <div class="progress-bar @(ViewBag.IsPassed == true ? "bg-success" : "bg-danger")" 
                             role="progressbar" 
                             data-width="@ViewBag.ScorePercentage"
                             style="width: 0%;">
                            @ViewBag.ScorePercentage%
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Passing Score: @(ViewBag.PassingScore ?? 70)%</label>
                    <div class="progress mb-3" style="height: 25px;">
                        <div class="progress-bar bg-info" 
                             role="progressbar" 
                             data-width="@(ViewBag.PassingScore ?? 70)"
                             style="width: 0%;">
                            @(ViewBag.PassingScore ?? 70)%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Exam Details -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <h6 class="card-title mb-3">Exam Details</h6>
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Training Name:</strong></td>
                            <td>@ViewBag.TrainingName</td>
                        </tr>
                        <tr>
                            <td><strong>Exam Date:</strong></td>
                            <td>@(ViewBag.ExamDate ?? DateTime.Now.ToString("dd/MM/yyyy"))</td>
                        </tr>
                        <tr>
                            <td><strong>Time Taken:</strong></td>
                            <td>@(ViewBag.TimeTaken ?? "00:00:00")</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Total Questions:</strong></td>
                            <td>@ViewBag.TotalQuestions</td>
                        </tr>
                        <tr>
                            <td><strong>Correct Answers:</strong></td>
                            <td class="text-success">@ViewBag.CorrectAnswers</td>
                        </tr>
                        <tr>
                            <td><strong>Incorrect Answers:</strong></td>
                            <td class="text-danger">@ViewBag.IncorrectAnswers</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Result Message -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body text-center">
            @if (ViewBag.IsPassed == true)
            {
                <div class="text-success mb-3">
                    <i class="fas fa-check-circle display-1"></i>
                </div>
                <h4 class="text-success mb-2">Congratulations!</h4>
                <p class="text-muted mb-0">You have successfully passed the training exam with a score of @ViewBag.ScorePercentage%.</p>
                <p class="text-muted">You are now certified for this training module.</p>
            }
            else
            {
                <div class="text-danger mb-3">
                    <i class="fas fa-times-circle display-1"></i>
                </div>
                <h4 class="text-danger mb-2">Exam Not Passed</h4>
                <p class="text-muted mb-0">You scored @ViewBag.ScorePercentage%, but the passing score is @(ViewBag.PassingScore ?? 70)%.</p>
                <p class="text-muted">Don't worry! You can retake the exam to improve your score.</p>
            }
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="card border-0 shadow-sm">
        <div class="card-body text-center">
            <div class="d-flex justify-content-center gap-3">
                <button type="button" class="btn btn-outline-primary" id="btnBackToTraining">
                    <i class="fas fa-arrow-left me-1"></i>Back to Training List
                </button>
                
                @if (ViewBag.IsPassed != true)
                {
                    <button type="button" class="btn btn-warning" id="btnRetakeExam">
                        <i class="fas fa-redo me-1"></i>Retake Exam
                    </button>
                }
                
                <button type="button" class="btn btn-success" onclick="printResults()">
                    <i class="fas fa-download me-1"></i>Download Certificate
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden Fields -->
<input type="hidden" id="trainingId" value="@ViewBag.TrainingId" />
<input type="hidden" id="publishId" value="@ViewBag.PublishID" />
