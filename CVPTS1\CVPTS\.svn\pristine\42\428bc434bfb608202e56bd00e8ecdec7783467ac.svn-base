﻿@model IEnumerable<BCM.BusinessClasses.BusinessProcessInfo>
@using BCM.Shared

@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";

}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">BCM Entity</h6>
        <div class="d-flex gap-3 justify-content-end align-items-end">
           @*  <div class="form-check">
                <input class="form-check-input Change" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="-1" checked>
                <label class="form-check-label" for="inlineRadio1">All Other BCM Entities</label>
            </div>
            <div class="form-check">
                <input class="form-check-input Change" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="1">
                <label class="form-check-label" for="inlineRadio2">Entities Under BCM Scope</label>
            </div>
            <div class="form-check">
                <input class="form-check-input Change" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="0">
                <label class="form-check-label" for="inlineRadio3">Entities Not Under BCM Scope</label>
            </div> *@
            <div class="vendor-section my-0">
                <div class="d-flex align-items-center gap-2 fs-14 filterButton" role="button">
                    <span class="vendor-circle primary"><i class="cv-all-facilities fs-6"></i></span><span>All BCM Entities</span><span class="count-primary">@ViewBag.TotalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14 filterButton" role="button" id="btnUnderBCM">
                    <span class="vendor-circle bg-white"><i class="cv-all-facilities fs-6 text-primary"></i></span><span>Entities Under BCM Scope</span><span class="text-primary">@ViewBag.UnderBCMCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14 filterButton" role="button" id="btnCtitcalProcess">
                    <span class="vendor-circle warning"><i class="cv-warning fs-6"></i></span><span>Critical Entities</span><span class="count-warning">@ViewBag.CriticalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14 filterButton" role="button" id="btnNonCtitcalProcess">
                    <span class="vendor-circle success"><i class="cv-success1 fs-6"></i></span><span>Non Critical Entities</span><span class="count-success">@ViewBag.NonCriticalCount</span>
                </div>
            </div>
            <div class="input-group Search-Input">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control" placeholder="Search">
            </div>
            <div class="dropdown">
                <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                    <i class="cv-filter align-middle" title="View Filter"></i>
                </button>
                <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                    <div class="mb-3">
                        <label>Organizations</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                            <select id="orglist" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example">
                                <option selected value="0">-- All Organizations --</option>
                                @foreach (var objOrg in ViewBag.OrgInfo)
                                {
                                    <option value="@objOrg.Value">@objOrg.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Units</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                            <select id="unitlist" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example">
                                <option selected value="0">-- All Units --</option>
                                @foreach (var objUnit in ViewBag.OrgUnit)
                                {
                                    <option value="@objUnit.Value">@objUnit.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-department"></i></span>
                            <select id="departmentlist" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example">
                                <option selected value="0">-- All Departments --</option>
                                @foreach (var objDepartment in ViewBag.DepartmentInfo)
                                {
                                    <option value="@objDepartment.Value">@objDepartment.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Sub Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
                            <select id="subdepartmentlist" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example">
                                <option selected value="0">-- All Sub Departments --</option>
                                @foreach (var objSubDepartment in ViewBag.SubFunction)
                                {
                                    <option value="@objSubDepartment.Value">@objSubDepartment.Text</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="text-end">
                        @* <button type="submit" id="btnSearch" class="btn btn-sm btn-primary">Search</button> *@
                    </div>
                </form>
            </div>
            @* <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#NormalModal"><i class="cv-Plus" title="Create New"></i>Create</button>*@
            @* <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="Modal"><i class="cv-Plus" title="Create New"></i>Create</button> *@
            <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#NormalModal"><i class="cv-Plus" title="Create New"></i>Create</button>
        </div>

    </div>
</div>

<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Entity&nbsp;Name</th>
                <th>Owner&nbsp;Details</th>
                @* <th>Org&nbsp;Level</th> *@
                <th>Unit</th>
                <th>Department</th>
                <th>SubDepartment</th>
                @* <th>RTO&nbsp;/&nbsp;MAO&nbsp;/&nbsp;RPO</th> *@
                <th>RTO</th>
                <th>IsCritical</th>
                <th>Status</th>               
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="tblBody">
            @await Html.PartialAsync("_FilterBCMEntities")
        </tbody>
    </table>
</div>

<!-- Configuration Modal -->
<div class="modal fade" id="NormalModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle"></h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0" >
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!-- Delete Modal -->
@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
            </div>
        </div>
    </div>
</div> *@

<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->
@section Scripts {
    <script>
        $(document).ready(function () {

            $(document).on("click", ".Closebtn", function(){
              location.reload();
            });

            $('#btnCreate').click(function () {
               // debugger;
                //$.get('/BCMEntities/ManageBCMEntities/AddBCMEntities', function (data) {
                $.get('@Url.Action("AddBCMEntities", "ManageBCMEntities")', function (data) {
                    $('.modal-body').html(data);
                    $('#NormalModal').modal('show');
                    $('#modaltitle').text('BCM Entities Configuration');
                })
            });

            $(document).on('click','.btnEdit', function () {
                //debugger;
                var iEntityTypeId = $(this).data('entity-id');
                var iRecordId = $(this).data('record-id');
                //$.get('/BCMEntities/ManageBCMEntities/EditBcmEntity', { iEntityTypeId: iEntityTypeId , iRecordId:iRecordId }, function (data) {
                $.get('@Url.Action("EditBcmEntity", "ManageBCMEntities")', { iEntityTypeId: iEntityTypeId , iRecordId:iRecordId }, function (data) {
                    debugger;
                    $('.modal-body').html(data);

                    // Check if this is a BCM Resource form (People entity type = 4) and initialize steps with validation
                    if (iEntityTypeId == 4) {
                        console.log("BCM Resource form detected, initializing validation and steps");

                        // Wait for content to be fully loaded, then initialize validation and wizard
                        setTimeout(function() {
                            console.log("🔵 BCM ENTITY PAGE: Edit setTimeout callback executing");
                            console.log("🔵 BCM ENTITY PAGE: Form exists in edit setTimeout:", !!document.getElementById('editResourceForm'));

                            // Initialize validation for the loaded edit form
                            if (typeof window.initializeEditBCMResourceValidation === 'function') {
                                console.log("🔵 BCM ENTITY PAGE: Calling initializeEditBCMResourceValidation");
                                window.initializeEditBCMResourceValidation();
                            } else {
                                console.error("❌ BCM ENTITY PAGE: initializeEditBCMResourceValidation function not found");
                            }

                            // Initialize wizard with validation for edit form
                            var form = $(".example-form");
                            form.steps({
                                headerTag: "h6",
                                bodyTag: "section",
                                transitionEffect: "fade",
                                titleTemplate: '<span class="step">#index#</span> #title#',
                                onStepChanging: function (event, currentIndex, newIndex) {
                                    // Allow going back
                                    if (currentIndex > newIndex) {
                                        return true;
                                    }

                                    // Validate current step before proceeding using BCM validation
                                    var isValid = true;
                                    var currentSection = form.find('section').eq(currentIndex);
                                    var formElement = document.getElementById('editResourceForm');

                                    if (window.BCMValidation && formElement) {
                                        // Show validation messages for current step
                                        window.BCMValidation.showAllValidationMessages(formElement);

                                        // Find all required fields in current step
                                        var requiredFields = currentSection.find('input[required], select[required]');

                                        requiredFields.each(function() {
                                            if (!window.BCMValidation.validateInput(this, true)) {
                                                isValid = false;
                                            }
                                        });

                                        // Special validation for role selection in step 4 (index 3)
                                        if (currentIndex === 3) {
                                            var roleSelected = $('input[name="role"]:checked').length > 0;
                                            if (!roleSelected) {
                                                // Find the role radio button group and show validation
                                                var roleGroup = currentSection.find('input[name="role"]').first();
                                                if (roleGroup.length > 0) {
                                                    var formGroup = roleGroup.closest('section');
                                                    var feedbackElement = formGroup.find('.invalid-feedback').last();
                                                    if (feedbackElement.length > 0) {
                                                        feedbackElement.show();
                                                        feedbackElement.text('Please select a Role');
                                                    }
                                                }
                                                isValid = false;
                                            }
                                        }
                                    } else {
                                        // Fallback validation
                                        var requiredFields = currentSection.find('input[required], select[required]');
                                        requiredFields.each(function() {
                                            if (this.hasAttribute('required') && !this.value.trim()) {
                                                isValid = false;
                                            }
                                        });
                                    }

                                    if (!isValid) {
                                        alert('Please fill in all required fields before proceeding to the next step.');
                                    }

                                    return isValid;
                                },
                                onFinishing: function (event, currentIndex) {
                                    // Final validation before submission using BCM validation
                                    var formElement = document.getElementById('editResourceForm');
                                    var isValid = true;

                                    if (window.BCMValidation && formElement) {
                                        // Show all validation messages
                                        window.BCMValidation.showAllValidationMessages(formElement);

                                        // Validate the entire form
                                        isValid = window.BCMValidation.validateForm(formElement);

                                        // Additional check for role selection
                                        var roleSelected = $('input[name="role"]:checked').length > 0;
                                        if (!roleSelected) {
                                            var roleGroup = $('input[name="role"]').first();
                                            if (roleGroup.length > 0) {
                                                var formGroup = roleGroup.closest('section');
                                                var feedbackElement = formGroup.find('.invalid-feedback').last();
                                                if (feedbackElement.length > 0) {
                                                    feedbackElement.show();
                                                    feedbackElement.text('Please select a Role');
                                                }
                                            }
                                            isValid = false;
                                        }
                                    } else {
                                        // Fallback validation
                                        var form = $(this);
                                        form.find('input[required], select[required]').each(function() {
                                            if (this.hasAttribute('required') && !this.value.trim()) {
                                                isValid = false;
                                            }
                                        });
                                    }

                                    if (!isValid) {
                                        alert('Please fill in all required fields before submitting the form.');
                                    }

                                    return isValid;
                                }
                            });
                        }, 200);
                    }
                    $('#NormalModal').modal('show');
                }).fail(function(xhr, status, error) {
                    console.error("Failed to load edit form:", error);
                });
            });

            $(document).on('click', '.ViewBIA', function () {
                // debugger;
                var iId = $(this).data('id');

                $.get('@Url.Action("ViewBIA", "ManageBusinessProcesses")',{ iId: iId }, function (data) {
                });
            });

            $(document).on('click', '.btnDelete', function () {
                debugger;
                var id = $(this).data('id');
                var iEntityTypeId = $(this).data('entity-id');
                //var iRecordId = $(this).data('record-id');
                //$.get('/BCMEntities/ManageBCMEntities/DeleteBcmEntity', { id: id }, function (data) {
                $.get('@Url.Action("DeleteBcmEntity", "ManageBCMEntities")', { id: id ,iEntityTypeId: iEntityTypeId}, function (data) {
                    $('.modal-body').html(data);
                    $('#DeleteModal').modal('show');
                    $('#modelTitle').text('Delete Business Process');
                });
            });

            $('#btnSearch').click(function () {
                var IsUnderBCMScope = -1;

                if ($("#inlineRadio3").is(':checked')) {
                    IsUnderBCMScope = $("#inlineRadio3").val();
                }

                if ($("#inlineRadio2").is(':checked')) {
                    IsUnderBCMScope = $("#inlineRadio2").val();
                }

                var ddlOrgSeletedValue = $('#orglist').val();
                var ddlDepartmentValue = $('#departmentlist').val();
                var ddlUnitValue = $('#unitlist').val();
                var ddlSubDepartmentValue = $('#subdepartmentlist').val();


                //$.get('/BCMEntities/ManageBCMEntities/GetFileredProcess', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
                $.get('@Url.Action("GetFileredProcess", "ManageBCMEntities")', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: IsUnderBCMScope }, function (data) {
                    $('#tblBody').html(data);
                });
            });

            $('#orglist,#unitlist,#departmentlist,#subdepartmentlist,#inlineRadio1,#inlineRadio2,#inlineRadio3').change(function () {
                var iIsUnderBCM;
                if ($("#inlineRadio1").prop("checked")) {

                    iIsUnderBCM = $('#inlineRadio1').val();
                }
                else if ($("#inlineRadio2").prop("checked")) {

                    iIsUnderBCM = $('#inlineRadio2').val();
                }
                else if ($("#inlineRadio3").prop("checked")) {

                    iIsUnderBCM = $('#inlineRadio3').val();
                }
                var ddlOrganizationVal = $('#orglist').val();
                var ddlUnitnVal = $('#unitlist').val();
                var ddlDepartmentVal = $('#departmentlist').val();
                var ddlSubDepartmentVal = $('#subdepartmentlist').val();

                // alert(Radio + " " + ddlOrganizationVal + " " + ddlUnitnVal + " " + ddlDepartmentVal + " " + ddlSubDepartmentVal);
                //$.get('/BCMEntities/ManageBCMEntities/GetFileredProcess', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM, }, function (data) {
                $.get('@Url.Action("GetFileredProcess", "ManageBCMEntities")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM, }, function (data) {
                    $('#tblBody').html(data);
                });
            });

            $('#unitlist').on('change', function () {
                var iOrgID = $('#orglist').val();
                var iUnitID = $('#unitlist').val();
                var iDepartmentID = $('#departmentlist').val();
                var iSubDepartmentID = $('#subdepartmentlist').val();
                $.ajax({
                    url:'@Url.Action("GetAllDepartments", "ManageBCMEntities", new { area = "BCMEntities" })',
                    type: 'GET',
                    data: { iUnitID: iUnitID },
                    success: function (response) {
                        let selectizeInstance = $('#departmentlist')[0].selectize;
                        selectizeInstance.clear();
                        selectizeInstance.clearOptions();
                        selectizeInstance.addOption({ value: "0", text: "-- All Departments --" });
                        selectizeInstance.addItem("0");

                        response && response.forEach(({ departmentID, departmentName }) => {
                             if (departmentID && departmentName) {
                                 selectizeInstance.addOption({ value: departmentID, text: departmentName });
                             }
                        });
                       
                    },                   
                    error: function (xhr, status, error) {
                        console.log("Error in binding dropdown list");
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            })

            $('#departmentlist').on('change', function () {
                var iOrgID = $('#orglist').val();
                var iUnitID = $('#unitlist').val();
                var iDepartmentID = $('#departmentlist').val();
                var iSubDepartmentID = $('#subdepartmentlist').val();

                $.ajax({
                    url:'@Url.Action("GetAllSubDepartments", "ManageBCMEntities", new { area = "BCMEntities" })',
                    type: 'GET',
                    data: { iDepartmentID: iDepartmentID },
                    success: function (response) {
                        let selectizeInstance = $('#subdepartmentlist')[0].selectize;
                        selectizeInstance.clear();
                        selectizeInstance.clearOptions();
                        selectizeInstance.addOption({ value: "0", text: "-- All SubDepartments --" });
                        selectizeInstance.addItem("0");

                        response && response.forEach(({ subFunctionID, subFunctionName }) => {
                             if (subFunctionID && subFunctionName) {
                                 selectizeInstance.addOption({ value: subFunctionID, text: subFunctionName });
                             }
                        });                       
                    },                    
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            })

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    </script>
}
