﻿namespace BCM.UI.Areas.BCMReports.ReportModels.BIAReport;

public class BIAReportModel
{
    public List<ProcessIdentificationAndCriticalityAssessment> ProcessIdentificationAndCriticalityAssessments { get; set; } = new();
    public List<VitalRecordsAndInformation> VitalRecordsAndInformations { get; set; } = new();
    public List<BuildingAndWorkspaceRequirements> BuildingAndWorkspaceRequirements { get; set; } = new();
    public List<HumanResourcesRequirements> HumanResourcesRequirements { get; set; } = new();
    public List<ITRequirements> ITRequirements { get; set; } = new();
    public List<WorkAreaRecoverySupplies> WorkAreaRecoverySupplies { get; set; } = new();
    public List<ThirdParties> ThirdParties { get; set; } = new();
}
public class ProcessIdentificationAndCriticalityAssessment
{
    public string? ProcessName { get; set; }
    public string? ProcessOwner { get; set; }
    public string? CriticalityAssessment { get; set; }
    public string? HSSE { get; set; }
    public string? Reputation { get; set; }
    public string? ProgramDelivery { get; set; }
    public string? PriorityActivityList { get; set; }
}
public class VitalRecordsAndInformation
{
    public string? ProcessName { get; set; }
    public string? VitalRecordsName { get; set; }
    public string? BriefDescription { get; set; }
    public string? Type { get; set; }
    public string? MediaNature { get; set; }
    public string? PrimaryStorageLocation { get; set; }
    public string? SecondaryStorageLocation { get; set; }
    public string? AlternateSource { get; set; }
}
public class BuildingAndWorkspaceRequirements
{
    public string? ProcessName { get; set; }
    public string? PrimaryLocation { get; set; }
    public string? WorkTransferPossible { get; set; }
    public string? RemoteAccessDetails { get; set; }
    public string? OptionPrimaryLocation { get; set; }
}
public class HumanResourcesRequirements
{
    public string? ProcessName { get; set; }
    public string? CurrentActualManPower { get; set; }
    public string? Day1 { get; set; }
    public string? Day3 { get; set; }
    public string? Day7 { get; set; }
    public string? Day14 { get; set; }
    public string? Day30 { get; set; }
    public string? Beyond { get; set; }
    public string? Primary { get; set; }
    public string? AssignedBackup { get; set; }
    public string? Activity { get; set; }
    public string? PotentialTeam { get; set; }
}
public class ITRequirements
{
    public string? ProcessName { get; set; }
    public string? ITApplication { get; set; }
    public string? KeyUsage { get; set; }
    public string? ITApplicationRTO { get; set; }
    public string? ITApplicationDataLoss { get; set; }
    public string? ManualWorkArounds { get; set; }
}
public class WorkAreaRecoverySupplies
{
    public string? ProcessName { get; set; }
    public string? EquipmentSupply { get; set; }
    public string? Comments { get; set; }
    public string? Day1 { get; set; }
    public string? Day3 { get; set; }
    public string? Day7 { get; set; }
    public string? Day14 { get; set; }
    public string? Day30 { get; set; }
    public string? Beyond { get; set; }
}
public class ThirdParties
{
    public string? ProcessName { get; set; }
    public string? ThirdPartName { get; set; }
    public string? NatureofService { get; set; }
    public string? CriticalityCategory { get; set; }
    public string? ContractualSupportArrangements { get; set; }
    public string? Contingencies { get; set; }
    public string? PotentialAlternateSources { get; set; }
}