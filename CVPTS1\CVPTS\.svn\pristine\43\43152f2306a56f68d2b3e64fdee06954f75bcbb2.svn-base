@font-face {
  font-family: 'cv-icon';
  src:  url('fonts/cv-icon.eot?a0oue7');
  src:  url('fonts/cv-icon.eot?a0oue7#iefix') format('embedded-opentype'),
    url('fonts/cv-icon.ttf?a0oue7') format('truetype'),
    url('fonts/cv-icon.woff?a0oue7') format('woff'),
    url('fonts/cv-icon.svg?a0oue7#cv-icon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="cv-"], [class*=" cv-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'cv-icon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    vertical-align: middle;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.cv-stroke-circle:before {
  content: "\eac0";
}
.cv-condition-left:before {
  content: "\eaad";
}
.cv-condition-right:before {
  content: "\eabe";
}
.cv-condition-top:before {
  content: "\eabf";
}
.cv-validation:before {
  content: "\eac1";
}
.cv-actions1:before {
  content: "\eac2";
}
.cv-actual-threat-false-alarm:before {
  content: "\eac3";
}
.cv-communication-CMT-BCM-CG:before {
  content: "\eac4";
}
.cv-condition-bottom:before {
  content: "\eac5";
}
.cv-workflow-flow:before {
  content: "\ea7b";
}
.cv-initiated-2:before {
  content: "\ea8e";
}
.cv-kpi-status:before {
  content: "\ea8f";
}
.cv-latest-incident:before {
  content: "\ea90";
}
.cv-review:before {
  content: "\ea91";
}
.cv-waiting-2:before {
  content: "\ea92";
}
.cv-business-process-2:before {
  content: "\ea93";
}
.cv-business-unit:before {
  content: "\ea94";
}
.cv-facilites:before {
  content: "\ea95";
}
.cv-manage-application:before {
  content: "\ea96";
}
.cv-manage-bcm-entities:before {
  content: "\ea97";
}
.cv-manage-bia-result:before {
  content: "\ea98";
}
.cv-manage-other-bcm:before {
  content: "\ea99";
}
.cv-people:before {
  content: "\ea9a";
}
.cv-third-parties:before {
  content: "\ea9b";
}
.cv-type-master:before {
  content: "\ea9c";
}
.cv-view-applications:before {
  content: "\ea9d";
}
.cv-application-rto:before {
  content: "\ea9e";
}
.cv-hr-report:before {
  content: "\ea9f";
}
.cv-primary-resource-report:before {
  content: "\eaa0";
}
.cv-process-compliant-report:before {
  content: "\eaa1";
}
.cv-risk-assessment-report:before {
  content: "\eaa2";
}
.cv-bia-report:before {
  content: "\eaa3";
}
.cv-business-impact-result:before {
  content: "\eaa4";
}
.cv-pci-report:before {
  content: "\eaa5";
}
.cv-process-recovery-report:before {
  content: "\eaa6";
}
.cv-bia-profile1:before {
  content: "\eaa7";
}
.cv-bia-survey-section:before {
  content: "\eaa8";
}
.cv-configure-bia-profile:before {
  content: "\eaa9";
}
.cv-configure-menu:before {
  content: "\eaaa";
}
.cv-parameter-profile:before {
  content: "\eaab";
}
.cv-process-compliance:before {
  content: "\eaac";
}
.cv-survey-questions:before {
  content: "\eaae";
}
.cv-survey-section:before {
  content: "\eaaf";
}
.cv-vault:before {
  content: "\eab0";
}
.cv-audit-log:before {
  content: "\eab1";
}
.cv-audit-policy:before {
  content: "\eab2";
}
.cv-bcm-document:before {
  content: "\eab3";
}
.cv-bcm-facility:before {
  content: "\eab4";
}
.cv-bcm-policy:before {
  content: "\eab5";
}
.cv-bcm-teams:before {
  content: "\eab6";
}
.cv-bcm-user-login:before {
  content: "\eab7";
}
.cv-manage-bcm-risk:before {
  content: "\eab8";
}
.cv-manage-location:before {
  content: "\eab9";
}
.cv-resources-2:before {
  content: "\eaba";
}
.cv-user-activity:before {
  content: "\eabb";
}
.cv-vendor-configure:before {
  content: "\eabc";
}
.cv-dot:before {
  content: "\eabd";
}
.cv-home-2:before {
  content: "\ea7a";
}
.cv-KPI:before {
  content: "\ea7c";
}
.cv-manage:before {
  content: "\ea7d";
}
.cv-notification:before {
  content: "\ea7e";
}
.cv-notification-main:before {
  content: "\ea7f";
}
.cv-notification-number:before {
  content: "\ea80";
}
.cv-report:before {
  content: "\ea81";
}
.cv-review1:before {
  content: "\ea82";
}
.cv-settings:before {
  content: "\ea83";
}
.cv-subject:before {
  content: "\ea84";
}
.cv-timeline1:before {
  content: "\ea85";
}
.cv-action:before {
  content: "\ea86";
}
.cv-approved:before {
  content: "\ea87";
}
.cv-automation:before {
  content: "\ea88";
}
.cv-BIA:before {
  content: "\ea89";
}
.cv-business-center:before {
  content: "\ea8a";
}
.cv-clock-waiting:before {
  content: "\ea8b";
}
.cv-config:before {
  content: "\ea8c";
}
.cv-criticality-summary:before {
  content: "\ea8d";
}
.cv-workflow-dashline:before {
  content: "\ea72";
}
.cv-workflow-dot-line:before {
  content: "\ea73";
}
.cv-workflow-line:before {
  content: "\ea79";
}
.cv-time:before {
  content: "\ea71";
}
.cv-new:before {
  content: "\ea67";
}
.cv-Report:before {
  content: "\ea68";
}
.cv-save:before {
  content: "\ea69";
}
.cv-saveas:before {
  content: "\ea6a";
}
.cv-start:before {
  content: "\ea6b";
}
.cv-step:before {
  content: "\ea6c";
}
.cv-tag:before {
  content: "\ea6d";
}
.cv-wf-condition:before {
  content: "\ea6e";
}
.cv-wf-edit:before {
  content: "\ea6f";
}
.cv-workflow-dependent:before {
  content: "\ea70";
}
.cv-condition:before {
  content: "\ea74";
}
.cv-export:before {
  content: "\ea75";
}
.cv-folder:before {
  content: "\ea76";
}
.cv-import:before {
  content: "\ea77";
}
.cv-info1:before {
  content: "\ea78";
}
.cv-answer:before {
  content: "\ea50";
}
.cv-approval-status:before {
  content: "\ea51";
}
.cv-audit-1:before {
  content: "\ea52";
}
.cv-auditor:before {
  content: "\ea53";
}
.cv-backup:before {
  content: "\ea54";
}
.cv-bcm-scope:before {
  content: "\ea55";
}
.cv-contingencies:before {
  content: "\ea56";
}
.cv-critical-category:before {
  content: "\ea57";
}
.cv-data-loss:before {
  content: "\ea58";
}
.cv-data-source:before {
  content: "\ea59";
}
.cv-date-of-close:before {
  content: "\ea5a";
}
.cv-feed-notify:before {
  content: "\ea5b";
}
.cv-focus:before {
  content: "\ea5c";
}
.cv-landline-phone:before {
  content: "\ea5d";
}
.cv-manpower:before {
  content: "\ea5e";
}
.cv-observer:before {
  content: "\ea5f";
}
.cv-primary:before {
  content: "\ea60";
}
.cv-process:before {
  content: "\ea61";
}
.cv-source:before {
  content: "\ea62";
}
.cv-storage:before {
  content: "\ea63";
}
.cv-storage-location:before {
  content: "\ea64";
}
.cv-supply:before {
  content: "\ea65";
}
.cv-view-feed:before {
  content: "\ea66";
}
.cv-na:before {
  content: "\ea4c";
}
.cv-no-change:before {
  content: "\ea4d";
}
.cv-upward:before {
  content: "\ea4e";
}
.cv-downward:before {
  content: "\ea4f";
}
.cv-risk-assessment:before {
  content: "\ea47";
}
.cv-expiry_team:before {
  content: "\ea42";
}
.cv-start_reminder:before {
  content: "\ea37";
}
.cv-task-acknowledged:before {
  content: "\ea3a";
}
.cv-task-assigned:before {
  content: "\ea3b";
}
.cv-task-in-progress:before {
  content: "\ea3c";
}
.cv-accidental-hazards:before {
  content: "\ea3d";
}
.cv-bia-profile:before {
  content: "\ea3e";
}
.cv-call_back:before {
  content: "\ea3f";
}
.cv-category:before {
  content: "\ea40";
}
.cv-continue_reminder:before {
  content: "\ea41";
}
.cv-filter:before {
  content: "\ea43";
}
.cv-inbox_id:before {
  content: "\ea44";
}
.cv-reminder:before {
  content: "\ea45";
}
.cv-reply_number:before {
  content: "\ea46";
}
.cv-sms_url:before {
  content: "\ea48";
}
.cv-sms_user:before {
  content: "\ea49";
}
.cv-sms-password:before {
  content: "\ea4a";
}
.cv-smtp_password:before {
  content: "\ea4b";
}
.cv-delibrate-hazards:before {
  content: "\ea35";
}
.cv-natural-disaster:before {
  content: "\ea36";
}
.cv-system-failure:before {
  content: "\ea38";
}
.cv-unavailable:before {
  content: "\ea39";
}
.cv-domain-name:before {
  content: "\ea31";
}
.cv-login-name:before {
  content: "\ea32";
}
.cv-module:before {
  content: "\ea33";
}
.cv-actions:before {
  content: "\ea34";
}
.cv-application:before {
  content: "\ea17";
}
.cv-circle-minus:before {
  content: "\ea18";
}
.cv-circle-plus:before {
  content: "\ea19";
}
.cv-dollar:before {
  content: "\ea1a";
}
.cv-fill-check .path1:before {
  content: "\ea1b";
  color: rgb(0, 0, 0);
}
.cv-fill-check .path2:before {
  content: "\ea1c";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cv-fill-error .path1:before {
  content: "\ea1d";
  color: rgb(0, 0, 0);
}
.cv-fill-error .path2:before {
  content: "\ea1e";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cv-fill-info .path1:before {
  content: "\ea1f";
  color: rgb(0, 0, 0);
}
.cv-fill-info .path2:before {
  content: "\ea20";
  margin-left: -1.0419921875em;
  color: rgb(255, 255, 255);
}
.cv-fill-info .path3:before {
  content: "\ea21";
  margin-left: -1.0419921875em;
  color: rgb(255, 255, 255);
}
.cv-fill-note .path1:before {
  content: "\ea22";
  color: rgb(0, 0, 0);
}
.cv-fill-note .path2:before {
  content: "\ea23";
  margin-left: -1.0419921875em;
  color: rgb(255, 255, 255);
}
.cv-fill-question-mark .path1:before {
  content: "\ea24";
  color: rgb(0, 0, 0);
}
.cv-fill-question-mark .path2:before {
  content: "\ea25";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cv-fill-question-mark .path3:before {
  content: "\ea26";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cv-fill-success .path1:before {
  content: "\ea27";
  color: rgb(0, 0, 0);
}
.cv-fill-success .path2:before {
  content: "\ea28";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cv-fill-warning .path1:before {
  content: "\ea29";
  color: rgb(0, 0, 0);
}
.cv-fill-warning .path2:before {
  content: "\ea2a";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cv-fill-warning .path3:before {
  content: "\ea2b";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cv-records:before {
  content: "\ea2c";
}
.cv-service:before {
  content: "\ea2d";
}
.cv-square-minus:before {
  content: "\ea2e";
}
.cv-square-plus:before {
  content: "\ea2f";
}
.cv-third-party:before {
  content: "\ea30";
}
.cv-uncheck:before {
  content: "\ea0b";
}
.cv-bcm-audit:before {
  content: "\ea0c";
}
.cv-bcm-strategy:before {
  content: "\ea0d";
}
.cv-bia-section:before {
  content: "\ea0e";
}
.cv-check:before {
  content: "\ea0f";
}
.cv-minus:before {
  content: "\ea10";
}
.cv-other-bcm-entities:before {
  content: "\ea11";
}
.cv-process-bia:before {
  content: "\ea12";
}
.cv-radio:before {
  content: "\ea13";
}
.cv-recovery-plan:before {
  content: "\ea14";
}
.cv-risk-profile:before {
  content: "\ea15";
}
.cv-test-recovery-plan:before {
  content: "\ea16";
}
.cv-reject:before {
  content: "\ea05";
}
.cv-success:before {
  content: "\ea06";
}
.cv-waiting:before {
  content: "\ea07";
}
.cv-captcha:before {
  content: "\ea08";
}
.cv-initiated:before {
  content: "\ea09";
}
.cv-mtpod:before {
  content: "\ea0a";
}
.cv-unit-head:before {
  content: "\e900";
}
.cv-up:before {
  content: "\e901";
}
.cv-up-arrow:before {
  content: "\e902";
}
.cv-upload:before {
  content: "\e903";
}
.cv-url:before {
  content: "\e904";
}
.cv-user:before {
  content: "\e905";
}
.cv-user-profile:before {
  content: "\e906";
}
.cv-value:before {
  content: "\e907";
}
.cv-values:before {
  content: "\e908";
}
.cv-version:before {
  content: "\e909";
}
.cv-vertical-dots:before {
  content: "\e90a";
}
.cv-view:before {
  content: "\e90b";
}
.cv-view_report:before {
  content: "\e90c";
}
.cv-vulnerability:before {
  content: "\e90d";
}
.cv-warning:before {
  content: "\e90e";
}
.cv-wating-approver:before {
  content: "\e90f";
}
.cv-weightage:before {
  content: "\e910";
}
.cv-zoom-in:before {
  content: "\e911";
}
.cv-zoom-out:before {
  content: "\e912";
}
.cv-fa:before {
  content: "\e913";
}
.cv-activity:before {
  content: "\e914";
}
.cv-activity-details:before {
  content: "\e915";
}
.cv-activity-name:before {
  content: "\e916";
}
.cv-activity-type:before {
  content: "\e917";
}
.cv-address:before {
  content: "\e918";
}
.cv-affected-services:before {
  content: "\e919";
}
.cv-all-facilities:before {
  content: "\e91a";
}
.cv-analyze:before {
  content: "\e91b";
}
.cv-application-directory:before {
  content: "\e91c";
}
.cv-Application-name:before {
  content: "\e91d";
}
.cv-Application-owner:before {
  content: "\e91e";
}
.cv-approvals-pending:before {
  content: "\e91f";
}
.cv-approver:before {
  content: "\e920";
}
.cv-assess:before {
  content: "\e921";
}
.cv-attach:before {
  content: "\e922";
}
.cv-attachment-folder:before {
  content: "\e923";
}
.cv-audit:before {
  content: "\e924";
}
.cv-audit_activities:before {
  content: "\e925";
}
.cv-audit_approach:before {
  content: "\e926";
}
.cv-audit_code:before {
  content: "\e927";
}
.cv-audit_description:before {
  content: "\e928";
}
.cv-audit_name:before {
  content: "\e929";
}
.cv-audit_objective:before {
  content: "\e92a";
}
.cv-audit_scope:before {
  content: "\e92b";
}
.cv-audit_team:before {
  content: "\e92c";
}
.cv-audit_type:before {
  content: "\e92d";
}
.cv-BCM-calendar:before {
  content: "\e92e";
}
.cv-BCM-entites:before {
  content: "\e92f";
}
.cv-bcm-entities-compliant:before {
  content: "\e930";
}
.cv-bcm-entities-not-compliant:before {
  content: "\e931";
}
.cv-BCM-workbench:before {
  content: "\e932";
}
.cv-BCP-Head:before {
  content: "\e933";
}
.cv-BCP-coordinator:before {
  content: "\e934";
}
.cv-business-continuity-summary:before {
  content: "\e935";
}
.cv-business-process:before {
  content: "\e936";
}
.cv-calculated:before {
  content: "\e937";
}
.cv-calendar:before {
  content: "\e938";
}
.cv-callback:before {
  content: "\e939";
}
.cv-checklist:before {
  content: "\e93a";
}
.cv-circle-down-linearrow:before {
  content: "\e93b";
}
.cv-circle-left-linearrow:before {
  content: "\e93c";
}
.cv-circle-right-linearrow:before {
  content: "\e93d";
}
.cv-circle-up-linearrow:before {
  content: "\e93e";
}
.cv-clock:before {
  content: "\e93f";
}
.cv-close:before {
  content: "\e940";
}
.cv-comment:before {
  content: "\e941";
}
.cv-company:before {
  content: "\e942";
}
.cv-configuration:before {
  content: "\e943";
}
.cv-configure-process-criticality:before {
  content: "\e944";
}
.cv-corporate-address:before {
  content: "\e945";
}
.cv-corrective-action:before {
  content: "\e946";
}
.cv-create_report:before {
  content: "\e947";
}
.cv-criteria:before {
  content: "\e948";
}
.cv-Critical:before {
  content: "\e949";
}
.cv-critical-BCM-entities:before {
  content: "\e94a";
}
.cv-critical-entities:before {
  content: "\e94b";
}
.cv-culture:before {
  content: "\e94c";
}
.cv-current-BCM-entites:before {
  content: "\e94d";
}
.cv-customer-id:before {
  content: "\e94e";
}
.cv-deactivate-accounts:before {
  content: "\e94f";
}
.cv-deallocate:before {
  content: "\e950";
}
.cv-default-mail:before {
  content: "\e951";
}
.cv-delete:before {
  content: "\e952";
}
.cv-department:before {
  content: "\e953";
}
.cv-department-name:before {
  content: "\e954";
}
.cv-description:before {
  content: "\e955";
}
.cv-develop-Plan:before {
  content: "\e956";
}
.cv-development-mode:before {
  content: "\e957";
}
.cv-disable:before {
  content: "\e958";
}
.cv-do-not-allow:before {
  content: "\e959";
}
.cv-donut-chart:before {
  content: "\e95a";
}
.cv-down:before {
  content: "\e95b";
}
.cv-down-arrow:before {
  content: "\e95c";
}
.cv-download:before {
  content: "\e95d";
}
.cv-drsite:before {
  content: "\e95e";
}
.cv-edit:before {
  content: "\e95f";
}
.cv-effectiveness:before {
  content: "\e960";
}
.cv-entity:before {
  content: "\e961";
}
.cv-entity-description:before {
  content: "\e962";
}
.cv-entity-name:before {
  content: "\e963";
}
.cv-entity-type:before {
  content: "\e964";
}
.cv-error:before {
  content: "\e965";
}
.cv-escalation:before {
  content: "\e966";
}
.cv-escalation-matrix:before {
  content: "\e967";
}
.cv-execute:before {
  content: "\e968";
}
.cv-existing-control:before {
  content: "\e969";
}
.cv-extensions:before {
  content: "\e96a";
}
.cv-facilities-compliant:before {
  content: "\e96b";
}
.cv-facility-address:before {
  content: "\e96c";
}
.cv-facility-name:before {
  content: "\e96d";
}
.cv-fax:before {
  content: "\e96e";
}
.cv-file-size:before {
  content: "\e96f";
}
.cv-function:before {
  content: "\e970";
}
.cv-High:before {
  content: "\e971";
}
.cv-holiday-date:before {
  content: "\e972";
}
.cv-home:before {
  content: "\e973";
}
.cv-horizontal-dots:before {
  content: "\e974";
}
.cv-host-name:before {
  content: "\e975";
}
.cv-human-resource:before {
  content: "\e976";
}
.cv-id:before {
  content: "\e977";
}
.cv-impact:before {
  content: "\e978";
}
.cv-impact_category:before {
  content: "\e979";
}
.cv-impact_detail:before {
  content: "\e97a";
}
.cv-impact_name:before {
  content: "\e97b";
}
.cv-Impact-rating:before {
  content: "\e97c";
}
.cv-improvement-status:before {
  content: "\e97d";
}
.cv-inbox-id:before {
  content: "\e97e";
}
.cv-incident:before {
  content: "\e97f";
}
.cv-incident-detail:before {
  content: "\e980";
}
.cv-incident-summary:before {
  content: "\e981";
}
.cv-incident-time:before {
  content: "\e982";
}
.cv-incident-type:before {
  content: "\e983";
}
.cv-info:before {
  content: "\e984";
}
.cv-insignificant:before {
  content: "\e985";
}
.cv-ip-address:before {
  content: "\e986";
}
.cv-key:before {
  content: "\e987";
}
.cv-kpi:before {
  content: "\e988";
}
.cv-language:before {
  content: "\e989";
}
.cv-language-code:before {
  content: "\e98a";
}
.cv-latitude:before {
  content: "\e98b";
}
.cv-left-arrow:before {
  content: "\e98c";
}
.cv-left-double-arrow:before {
  content: "\e98d";
}
.cv-Legal-Entity:before {
  content: "\e98e";
}
.cv-lock:before {
  content: "\e98f";
}
.cv-lock-accounts:before {
  content: "\e990";
}
.cv-login-code:before {
  content: "\e991";
}
.cv-longitude:before {
  content: "\e992";
}
.cv-low:before {
  content: "\e993";
}
.cv-mail:before {
  content: "\e994";
}
.cv-measure:before {
  content: "\e995";
}
.cv-medium:before {
  content: "\e996";
}
.cv-menu:before {
  content: "\e997";
}
.cv-menu-detail:before {
  content: "\e998";
}
.cv-Mobile:before {
  content: "\e999";
}
.cv-my-escalations:before {
  content: "\e99a";
}
.cv-my-task-list:before {
  content: "\e99b";
}
.cv-name:before {
  content: "\e99c";
}
.cv-not-approver:before {
  content: "\e99d";
}
.cv-note:before {
  content: "\e99e";
}
.cv-notfication:before {
  content: "\e99f";
}
.cv-notify-incident:before {
  content: "\e9a0";
}
.cv-notify-team:before {
  content: "\e9a1";
}
.cv-objectives1:before {
  content: "\e9a2";
}
.cv-organisation-summary:before {
  content: "\e9a3";
}
.cv-organization:before {
  content: "\e9a4";
}
.cv-organization-group-name:before {
  content: "\e9a5";
}
.cv-organizer:before {
  content: "\e9a6";
}
.cv-org-level:before {
  content: "\e9a7";
}
.cv-orgnization-structure:before {
  content: "\e9a8";
}
.cv-org-revel-escalations:before {
  content: "\e9a9";
}
.cv-outgoing-mail:before {
  content: "\e9aa";
}
.cv-overall-rating:before {
  content: "\e9ab";
}
.cv-owner:before {
  content: "\e9ac";
}
.cv-page-name:before {
  content: "\e9ad";
}
.cv-parameter:before {
  content: "\e9ae";
}
.cv-password:before {
  content: "\e9af";
}
.cv-password-expiration:before {
  content: "\e9b0";
}
.cv-password-hide:before {
  content: "\e9b1";
}
.cv-password-visible:before {
  content: "\e9b2";
}
.cv-pdf-files:before {
  content: "\e9b3";
}
.cv-phone:before {
  content: "\e9b4";
}
.cv-platform-location:before {
  content: "\e9b5";
}
.cv-Plus:before {
  content: "\e9b6";
}
.cv-pn-number:before {
  content: "\e9b7";
}
.cv-primary-facility:before {
  content: "\e9b8";
}
.cv-prioritized-activity:before {
  content: "\e9b9";
}
.cv-privilege:before {
  content: "\e9ba";
}
.cv-probability-rating:before {
  content: "\e9bb";
}
.cv-probability-ratings:before {
  content: "\e9bc";
}
.cv-probable-risk:before {
  content: "\e9bd";
}
.cv-process-code:before {
  content: "\e9be";
}
.cv-process-description:before {
  content: "\e9bf";
}
.cv-process-name:before {
  content: "\e9c0";
}
.cv-profile:before {
  content: "\e9c1";
}
.cv-profile-details:before {
  content: "\e9c2";
}
.cv-question-mark:before {
  content: "\e9c3";
}
.cv-questions:before {
  content: "\e9c4";
}
.cv-rating:before {
  content: "\e9c5";
}
.cv-refresh:before {
  content: "\e9c6";
}
.cv-reload:before {
  content: "\e9c7";
}
.cv-remarks:before {
  content: "\e9c8";
}
.cv-reminder1:before {
  content: "\e9c9";
}
.cv-reply-number:before {
  content: "\e9ca";
}
.cv-reports:before {
  content: "\e9cb";
}
.cv-resources:before {
  content: "\e9cc";
}
.cv-response:before {
  content: "\e9cd";
}
.cv-responsible:before {
  content: "\e9ce";
}
.cv-review-date:before {
  content: "\e9cf";
}
.cv-reviews:before {
  content: "\e9d0";
}
.cv-review-type:before {
  content: "\e9d1";
}
.cv-revision:before {
  content: "\e9d2";
}
.cv-right-arrow:before {
  content: "\e9d3";
}
.cv-right-double-arrow:before {
  content: "\e9d4";
}
.cv-risk-champion:before {
  content: "\e9d5";
}
.cv-risk-description:before {
  content: "\e9d6";
}
.cv-risk-item-category:before {
  content: "\e9d7";
}
.cv-risk-option:before {
  content: "\e9d8";
}
.cv-risk-owner:before {
  content: "\e9d9";
}
.cv-risk-rating:before {
  content: "\e9da";
}
.cv-risks:before {
  content: "\e9db";
}
.cv-risk-status:before {
  content: "\e9dc";
}
.cv-risk-value:before {
  content: "\e9dd";
}
.cv-role:before {
  content: "\e9de";
}
.cv-role-text:before {
  content: "\e9df";
}
.cv-rpo:before {
  content: "\e9e0";
}
.cv-RTO:before {
  content: "\e9e1";
}
.cv-schedule-time:before {
  content: "\e9e2";
}
.cv-score:before {
  content: "\e9e3";
}
.cv-search:before {
  content: "\e9e4";
}
.cv-secondary-facility:before {
  content: "\e9e5";
}
.cv-section:before {
  content: "\e9e6";
}
.cv-sender-id:before {
  content: "\e9e7";
}
.cv-sequence:before {
  content: "\e9e8";
}
.cv-service-criticality:before {
  content: "\e9e9";
}
.cv-site-incharge:before {
  content: "\e9ea";
}
.cv-smtp-server:before {
  content: "\e9eb";
}
.cv-ssl:before {
  content: "\e9ec";
}
.cv-status:before {
  content: "\e9ed";
}
.cv-strategy-options:before {
  content: "\e9ee";
}
.cv-subdepartment:before {
  content: "\e9ef";
}
.cv-subdepartment-name:before {
  content: "\e9f0";
}
.cv-submenu:before {
  content: "\e9f1";
}
.cv-success1:before {
  content: "\e9f2";
}
.cv-support:before {
  content: "\e9f3";
}
.cv-target:before {
  content: "\e9f4";
}
.cv-targeted-date:before {
  content: "\e9f5";
}
.cv-team-distribution:before {
  content: "\e9f6";
}
.cv-team-size:before {
  content: "\e9f7";
}
.cv-threat:before {
  content: "\e9f8";
}
.cv-threat-category:before {
  content: "\e9f9";
}
.cv-threats:before {
  content: "\e9fa";
}
.cv-timeline:before {
  content: "\e9fb";
}
.cv-timeout:before {
  content: "\e9fc";
}
.cv-to-do-list:before {
  content: "\e9fd";
}
.cv-Total-Licenses-Purchased:before {
  content: "\e9fe";
}
.cv-Total-Licenses-UnUsed:before {
  content: "\e9ff";
}
.cv-Total-Licenses-Used:before {
  content: "\ea00";
}
.cv-transaction-volume:before {
  content: "\ea01";
}
.cv-trend:before {
  content: "\ea02";
}
.cv-type:before {
  content: "\ea03";
}
.cv-unit:before {
  content: "\ea04";
}
