﻿@model IEnumerable<BCM.BusinessClasses.PerformanceEvaluation>
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int iIndex = 0;
}
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Performance Evaluation List</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal"
        data-bs-target="#staticBackdrop">
        <i class="cv-Plus" title="Create New"></i>Create
        </button> *@
        <button type="button" class="btn icon-btn btn-primary btn-sm btnCreate" data-bs-toggle="modal" data-bs-target="#CreateModal"> <i class="cv-Plus" title="Create New"></i>Create</button>

    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th>Sr. No. </th>
                <th>IT&nbsp;DR&nbsp;Objective </th>
                <th>Objective</th>
                <th>MeasureKPI </th>
                <th>When&nbsp;to&nbsp;be&nbsp;Measured</th>
                <th>Responsible&nbsp;Parties</th>
                <th>Effectiveness&nbsp;Rating</th>
                <th>Effectiveness</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var PerformanceEvaItems in Model)
            {
                iIndex++;
                <tr>
                    <td>
                        @iIndex
                    </td>
                    <td> @PerformanceEvaItems.ITDRName </td>
                    <td> @PerformanceEvaItems.Objective</td>
                    <td> @PerformanceEvaItems.MeasureKPI </td>
                    <td> @PerformanceEvaItems.FrequencyName </td>
                    <td> @PerformanceEvaItems.ResponsibleParties </td>
                    <td> @PerformanceEvaItems.Rating</td>
                    <td> @PerformanceEvaItems.Effectiveness </td>
                    <td>
                        <span class="btn-action btnEdit" type="button" data-id="@PerformanceEvaItems.ID"><i class="cv-edit" title="Edit"></i></span>
                        <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal" data-id="@PerformanceEvaItems.ID"><i class="cv-delete text-danger" title="Delete"></i></span>
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>


<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Performance Evaluation Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="configurationBody"></div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!--Notify Configuration Modal -->
<div class="modal fade" id="NotifyModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Notify Teams Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">ITDR Objectives</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-organization"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Perpetuuiti</option>
                                    <option value="value">Perpetuuiti</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Notification Type</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notfication"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Select</option>

                                </select>
                            </div>
                        </div>

                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Unit Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-unit"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Admin Groups</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Incident to Notify</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notify-incident"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Select</option>
                                </select>
                            </div>
                        </div>

                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Team Notification Subject</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notfication"></i></span>
                                <textarea class="form-control" placeholder="Team Notification Subject" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Team Notification Message</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notify-team"></i></span>
                                <textarea class="form-control" placeholder="Team Notification Message" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-group d-flex align-items-center">
                                <span class="form-label mb-0"><i class="cv-user me-1"></i>User Response Required</span>

                                <input type="checkbox" class="form-check  ms-2" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Accept User Response for</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-user"></i></span>
                                <input type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group">
                            @* <label class="form-label">Accept User Response for</label> *@
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-file-size"></i></span>
                                <input type="file" class="form-control" />
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary btn-sm me-1">Filter</button>
                    <button type="submit" class="btn btn-primary btn-sm">Add Team</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Notify Configuration Modal -->
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->
@section Scripts {
    <script>
        $(document).ready(function () {

            $(document).on("click", ".Closebtn", function(){
              location.reload();
            });

            $(document).on('click', '.btnCreate', function () {
                debugger;
                $.get('@Url.Action("AddPerformanceEvaluation", "ManagePerformanceEvaluationSC")', function (data) {

                    $('#configurationBody').html(data);
                    $('#CreateModal').modal('show');
                });
            });



            $(document).on('click', '.btnEdit', function () {
                debugger;
                //var iId = $('.btnEdit').val();
                var iId = $(this).data('id');


                $.get('@Url.Action("EditPerformanceEvaluation", "ManagePerformanceEvaluationSC")', { iId: iId }, function (data) {

                    $('#configurationBody').html(data);
                    $('#CreateModal').modal('show');
                });
            });


            $(document).on('click', '.btnDelete', function () {
                debugger;
                //var iId = $('.btnEdit').val();
                var iId = $(this).data('id');
                $.get('@Url.Action("DeletePerformanceEvaluation", "ManagePerformanceEvaluationSC")', { iId: iId }, function (data) {
                    $('#deleteBody').html(data);
                    $('#DeleteModal').modal('show');
                });
            });


            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        //Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

        });

    </script>
}
