﻿@using BCM.Shared
@model IEnumerable<BCM.BusinessClasses.MenuRights>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers


@{
    ViewData["Title"] = "Sub Menu Master";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var sequences = ViewBag.Sequences as System.Data.DataTable;
    int Index = 1;
}
<style>
    .dataTables_scrollBody {
        max-height: calc(100vh - 450px);
        height: calc(100vh - 450px);
    }
</style>
@* <div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Configure SubMenuMaster</h6>

 </div>*@
<div class="Page-Condant card border-0">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        <h6 style="padding-left:1%" class="Page-Title mb-2">Configure SubMenuMaster</h6>
    </div> 
    <div class="row">
        <div class="col d-grid">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="Sub-Title mb-0">SubMenuMaster Structure</h6>
                    </div>                    
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <form asp-action="SaveUpdateSubMenuMaster" method="post">
                                <div class="row">
                                    <div class="form-group col-6">
                                        <input type="hidden" name="SubMenuID" id="SubMenuID" value="" />
                                        <label class="form-label">Sub Menu Name</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-submenu"></i></span>
                                            <input class="form-control" type="text" placeholder="Enter Sub Menu Name" name="SubMenuName" id="SubMenuName" required />
                                        </div>
                                    </div>  
                                    <div class="form-group col-6">
                                        <label class="form-label">Sequence</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-sequence"></i></span>
                                            <select class="form-select" name="Sequence" id="Sequence" required>
                                                <option value="" disabled selected>Select Sequence</option>
                                                @if (sequences != null)
                                                {
                                                    foreach (System.Data.DataRow row in sequences.Rows)
                                                    {
                                                        <option value="@row["SequenceID"]">@row["SequenceName"]</option>
                                                    }
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group col-12">
                                        <label class="form-label">Description</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-description"></i></span>
                                            <input class="form-control" type="text" placeholder="Enter Description" name="SubMenuDetails" id="SubMenuDetails" required />
                                        </div>
                                    </div>
                                </div>
                            
                                <div class="col-12">
                                    <div class="text-end me-4 pb-3">
                                        <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit">Save</button>
                                        <button class="btn btn-sm btn-secondary" id="btnCancel" formnovalidate>Cancel</button>
                                    </div>
                                </div>
                            </form>                            
                        </div>
                        
                        <div class="col-12">
                            <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
                                <thead>
                                    <tr>
                                        <th>Sl. No</th>
                                        <th>Sub Menu Name</th>
                                        <th>Sub MenuDetails</th>
                                        <th>Sequence</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if(@Model != null)
                                    {
                                        @foreach (var item in Model)
                                        {
                                            <tr>
                                                <td>@Index</td>
                                                <td>@item.SubMenuName</td>
                                                <td>@item.SubMenuDetails</td>
                                                <td>@item.Sequence</td>
                                                <td>
                                                    <span class="btn-action btnEdit" type="button" data-id="@item.SubMenuID" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                                    <span class="btn-action btnDelete" type="button" data-id="@item.SubMenuID" data-bs-toggle="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                                                </td>
                                            </tr>
                                            Index++;
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>                  
                </div>
            </div>
        </div>
    </div>
</div>

@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
            </div>
        </div>
    </div>
</div> *@
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>


@section Scripts
{
    <script>
        $(document).ready(function () {
            $(document).on('click', '.btnEdit', function () {
                  var iSubMenuId = $(this).data('id');

                //$.get('/BCMProcessBIAForms/BIAVitalRecords/GetVitalRecordByID/', { id: id })
                $.get('@Url.Action("GetSubMenuById", "SubMenusMaster")', { iSubMenuId: iSubMenuId })
                    .done(function (data) {
                        if (data) {
                            debugger
                            $('#SubMenuID').val(data.subMenuID);
                            $('input[name="SubMenuName"]').val(data.subMenuName);
                            $('input[name="SubMenuDetails"]').val(data.subMenuDetails);
                            $('#Sequence').val(data.sequence);                           
                            UpdateButtonLabel();
                        }
                    })
                    .fail(function () {
                        console.error('Failed to fetch data.');
                    });

             });

             $(document).on('click', '.btnDelete', function () {
                 var iSubMenuId = $(this).data('id');
                  $.get('@Url.Action("DeleteSubMenuById", "SubMenusMaster")', { iSubMenuId: iSubMenuId })
                    .done(function (data) {
                        $('.modal-body').html(data);
                        $('#DeleteModal').modal('show');
                        $('#modelTitle').text('Delete MenuMaster');
                    })
                    .fail(function () {
                        console.error('Failed to fetch delete confirmation.');
                    });
             });

             $(document).on('click', '#btnCancel', function (event) {
                event.preventDefault();
                location.reload();
             });


             function UpdateButtonLabel() {


                var id = $('#SubMenuID').val();
                if (id && id > 0) {
                    $('#btnSubmit').text('Update');
                } else {
                    $('#btnSubmit').text('Save');
                }
            }
        });
    </script>
}
