﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model BCM.BusinessClasses.IncidentType

<form asp-action="DeleteIncidentTypeDetails" method="post" asp-controller="IncidentTypeMaster">
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    <div class="modal-body d-grid px">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="OrgGroupSpan">@Model.IncidentTypeName</span>" ?</span>
        <input type="hidden" id="lblIncidentID1" name="IncidentTypeID" asp-for="IncidentTypeID" />
    </div>
    <div class="modal-footer justify-content-center p-0">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>
