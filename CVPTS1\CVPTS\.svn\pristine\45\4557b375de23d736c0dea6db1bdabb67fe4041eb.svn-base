﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model BCM.BusinessClasses.CompanyMasterInfo
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@

@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}
<form id="editBCMVendorForm" asp-action="EditBCMVendor" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="modal-body pt-0">
    <div class="row">
        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group">
                <div>
                    <input type="hidden" asp-for="CompanyID" id="Id" />
                    <input type="hidden" asp-for="EntityTypeID" id="EntityTypeID" />
                    <input type="hidden" asp-for="IsButtonVisible" id="IsVisible" />

                </div>
                <label class="form-label">Company Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-company"></i></span>
                    <input type="text" asp-for="CompanyName" class="form-control" required pattern="^[a-zA-Z0-9_ ]{0,50}$" title="Only Alphanumeric with Underscore and space is allowed (Max Limit 50)" />
                </div>
                    <div class="invalid-feedback">Enter Company Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Unit</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-unit"></i></span>
                        <select class="form-select form-control selectized ddlUnit" autocomplete="off" id="ddlUnit" aria-label="Default select example" required asp-for="UnitID" asp-items="@(new SelectList(ViewBag.Unit,"UnitID","UnitName"))">
                        <option selected value="0">-- Select Units --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Unit</div>
            </div>
            <div class="form-group">
                <label class="form-label">SubDepartment</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                        <select class="form-select form-control selectized ddlSubDepartment" autocomplete="off" id="ddlSubDepartment" aria-label="Default select example" asp-for="DevisionID" asp-items="@(new SelectList(ViewBag.Subdepartment,"SubFunctionID","SubFunctionName"))">
                        <option selected value="0">-- Select SubDepartments --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select SubDepartment</div>
            </div>


            <div class="form-group">
                <label class="form-label">Contract Start Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-holiday-date"></i></span>
                        <input type="date" id="ContractStartDate" name="ContractStartDate"  class="form-control" asp-for="ContractStartDate" required />
                </div>
                    <div class="invalid-feedback" id="ContractStartDate-feedback">Enter Contract Start Date</div>
            </div>
        </div>
        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group">
                <label class="form-label">Organization</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select class="form-select form-control selectized ddlOrganization" autocomplete="off" asp-for="OrgID" id="ddlOrganization" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.OrgInfo,"Id","OrganizationName"))" required>
                        <option selected value="0">-- Select Organizations --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Organization</div>
            </div>
            <div class="form-group">
                <label class="form-label">Department</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-department"></i></span>
                        <select class="form-select form-control selectized ddlDepartment" autocomplete="off" asp-for="DepartmentID" id="ddlDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Department,"DepartmentID","DepartmentName"))" required>
                        <option selected value="0">-- Select Departments --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Department</div>
            </div>
            <div class="form-group">
                <label class="form-label">Company Address</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <textarea class="form-control" style="height:0px" asp-for="CompanyAddress" required></textarea>
                </div>
                <div class="invalid-feedback">Enter Company Address</div>
            </div>
            <div class="form-group">
                <label class="form-label">Contract End Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-holiday-date"></i></span>
                        <input type="date" id="ContractEndDate" name="ContractEndDate"  class="form-control" asp-for="ContractEndDate" required />
                </div>
                    <div class="invalid-feedback" id="ContractEndDate-feedback">Enter Contract End Date</div>
            </div>

        </div>
        <div class="col-12">
            <div class="form-group">
                <label class="form-label">Company Description</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <textarea class="form-control" style="height:0px" asp-for="CompanyDetails" ></textarea>
                </div>
                <div class="invalid-feedback">Enter Company Description</div>
            </div>
        </div>
    </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Update</button>
            <button type="button" class="btn btn-primary btn-sm" id="btnBcm">View BCM Details</button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function () {
        // Mark fields as 'dirty' on change
        $('#ContractStartDate, #ContractEndDate').on('change', function () {
            $(this).data('dirty', true);
            validateContractDates(); // real-time validation
        });

        // Run once on load if both fields are filled (e.g., in edit mode)
        const startFilled = $('#ContractStartDate').val();
        const endFilled = $('#ContractEndDate').val();
        if (startFilled || endFilled) {
            validateContractDates(); // No 'force' = gentle check
        }

        const isVisible = $("#IsVisible").val();
        if (isVisible == 0) {
            $("#btnBcm").hide();
        } else if (isVisible == 1) {
            $("#btnBcm").show();
        }


         $('#btnBcm').click(function () {
            debugger;
            var iId = $("#Id").val();  // Capture the Id field value
            var iEntityTypeId =$("#EntityTypeID").val();
            // Check if the ID is present before proceeding
            if (iId) {
                $.ajax({
                    url: '@Url.Action("GetProcessId", "ManageBCMEntities", new { area = "BCMEntities" })',
                    type: 'POST',
                    data: { iId: iId , iEntityTypeId: iEntityTypeId  },  // Send the id as POST data
                    success: function (response) {
                        if (response.redirectUrl) {
                    window.location.href = response.redirectUrl;  // Redirect to the URL from the response
                }
                    },
                    error: function () {
                        alert("Error occurred");
                    }
                });
            } else {
                alert("ID not found");
            }
        });

        console.log("EditVendor form ready");

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for editBCMVendorForm form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('editBCMVendorForm');
                if (!form) {
                    console.error("Form not found with ID: editBCMVendorForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                const feedbackElements = {};

                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        const message = element.textContent.trim();

                        // Primary key: Use input ID
                        if (input.id) {
                            customMessages[input.id] = message;
                            feedbackElements[input.id] = element;
                            console.log("Stored custom message for ID", input.id, ":", message);
                        }

                        // Fallback: Use asp-for attribute
                        const aspFor = input.getAttribute('asp-for');
                        if (aspFor) {
                            customMessages[aspFor] = message;
                            feedbackElements[aspFor] = element;
                            console.log("Stored custom message for asp-for", aspFor, ":", message);
                        }

                        // Store original message on element for direct access
                        element.originalMessage = message;
                    }
                });

                // Helper function to restore custom message for an input
                function restoreCustomMessage(input) {
                    let message = null;
                    let feedbackElement = null;

                    // Priority 1: Use input ID
                    if (input.id && customMessages[input.id]) {
                        message = customMessages[input.id];
                        feedbackElement = feedbackElements[input.id];
                        console.log("Found custom message by ID", input.id, ":", message);
                    }
                    // Priority 2: Use asp-for attribute
                    else if (input.getAttribute('asp-for') && customMessages[input.getAttribute('asp-for')]) {
                        const aspFor = input.getAttribute('asp-for');
                        message = customMessages[aspFor];
                        feedbackElement = feedbackElements[aspFor];
                        console.log("Found custom message by asp-for", aspFor, ":", message);
                    }
                    // Priority 3: Find feedback element in the same form group
                    else {
                        const formGroup = input.closest('.form-group');
                        feedbackElement = formGroup?.querySelector('.invalid-feedback');
                        if (feedbackElement && feedbackElement.originalMessage) {
                            message = feedbackElement.originalMessage;
                            console.log("Found custom message by form group for", input.id || input.getAttribute('asp-for'), ":", message);
                        }
                    }

                    if (message && feedbackElement) {
                        feedbackElement.textContent = message;
                        // Don't use direct style manipulation - let CSS classes handle display
                        console.log("✅ Restored custom message:", message, "for input:", input.id || input.getAttribute('asp-for'));
                        return true;
                    } else {
                        console.warn("❌ Could not restore custom message for input:", input.id || input.getAttribute('asp-for'));
                        return false;
                    }
                }

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // For select fields, only validate if forced or if they have value "0"
                    if (input.tagName === 'SELECT' && !forceValidation) {
                        const value = input.value;
                        if (value && value !== "0") {
                            // Has valid selection, clear any validation errors
                            input.classList.remove('is-invalid');
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                feedbackElement.hide();
                            }
                            return true;
                        }
                    }

                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        restoreCustomMessage(input);
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        restoreCustomMessage(input);
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);

                    // Validate dates (force validation on submit)
                    const datesValid = validateContractDates(true);

                    console.log("Form validation result:", isValid);
                    console.log("Date validation result:", datesValid);

                    if (!isValid || !datesValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });

            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        // Add event listeners for real-time validation on select fields
        $('#ddlOrganization, #ddlUnit, #ddlDepartment, #ddlSubDepartment').on('change', function() {
            var $this = $(this);

            // Clear validation state when user makes a selection
            if ($this.val() && $this.val() !== "0") {
                $this.removeClass('is-invalid');
                var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                if (feedbackElement.length > 0) {
                    feedbackElement.hide();
                }
            }
        });

        // Add date validation for Contract Start Date and End Date
        function validateContractDates(force = false) {
            var startDateStr = $('#ContractStartDate').val();
            var endDateStr = $('#ContractEndDate').val();
            var currentDate = new Date().toISOString().split('T')[0]; // Get current date in YYYY-MM-DD format
            var isEditMode = $('#Id').val() !== "" && $('#Id').val() !== "0"; // Check if Id exists
            var isValid = true;

            // Clear previous date validation errors
            if (force) {
                clearDateValidationErrors();
            }

            // Validate Contract Start Date - in edit mode, allow past dates
            if (startDateStr && !isEditMode && startDateStr < currentDate) {
                showDateValidationError('#ContractStartDate', 'Contract Start Date cannot be earlier than today');
                isValid = false;
            }

            // Validate Contract End Date - cannot be less than Start Date
            if (startDateStr && endDateStr && endDateStr < startDateStr) {
                showDateValidationError('#ContractEndDate', 'Contract End Date cannot be earlier than Start Date');
                isValid = false;
            }

            return isValid;
        }

        // Function to show date validation error
        function showDateValidationError(selector, message) {
            var element = $(selector);
            element.addClass('is-invalid').removeClass('is-valid');

            var feedbackSelector = selector + '-feedback';
            var feedbackElement = $(feedbackSelector);
            if (feedbackElement.length > 0) {
                feedbackElement.text(message);
                // Use CSS classes and show method for proper display
                feedbackElement.addClass('custom-validation show').show();
            }
        }

        // Function to clear date validation errors
        function clearDateValidationErrors() {
            var dateSelectors = ['#ContractStartDate', '#ContractEndDate'];

            dateSelectors.forEach(function(selector) {
                var element = $(selector);
                var feedbackSelector = selector + '-feedback';
                var feedbackElement = $(feedbackSelector);

                // Only clear if the current message is a date validation error
                if (feedbackElement.length > 0 && (feedbackElement.text().includes('cannot be earlier') || feedbackElement.text().includes('Contract Start Date') || feedbackElement.text().includes('Contract End Date'))) {
                    element.removeClass('is-invalid');
                    // Use CSS classes for proper hiding
                    feedbackElement.removeClass('custom-validation show').hide();

                    // Restore original message
                    var originalMessages = {
                        '#ContractStartDate': 'Enter Contract Start Date',
                        '#ContractEndDate': 'Enter Contract End Date'
                    };

                    if (originalMessages[selector]) {
                        feedbackElement.text(originalMessages[selector]);
                    }
                }
            });
        }

        // Add event listeners for date validation
        $('#ContractStartDate, #ContractEndDate').on('change blur', function() {
            var $this = $(this);

            // Mark as dirty for future validations
            $this.data('dirty', true);

            // Clear validation state when user makes a selection
            if ($this.val()) {
                $this.removeClass('is-invalid');
                var feedbackSelector = '#' + $this.attr('id') + '-feedback';
                var feedbackElement = $(feedbackSelector);
                if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be earlier')) {
                    feedbackElement.hide();
                }
            }

            // Small delay to allow the change to complete, then validate dates
            setTimeout(function() {
                validateContractDates();
            }, 100);
        });

        // Prevent validation messages from showing on page load
        $(window).on('load', function() {
            // Hide all validation messages that might be showing incorrectly
            $('.invalid-feedback').each(function() {
                var $feedback = $(this);
                var $input = $feedback.closest('.form-group').find('input, select');

                // Only hide if the input is not actually invalid
                if ($input.length > 0 && !$input.hasClass('is-invalid')) {
                    $feedback.hide();
                }
            });
        });

        // Organization dropdown change event
        $('#ddlOrganization').change(function () {
            var ddlOrganizationVal = $(this).val();

            // Clear dependent dropdowns
            clearDropdown('.ddlUnit');
            clearDropdown('.ddlDepartment');
            clearDropdown('.ddlSubDepartment');

            if (ddlOrganizationVal && ddlOrganizationVal !== "0") {
                $.ajax({
                    url: '@Url.Action("GetAllUnits", "ManageVendor")',
                    type: 'GET',
                    data: { iOrgID: ddlOrganizationVal },
                    success: function (response) {
                        populateDropdown('.ddlUnit', response, 'unitID', 'unitName', '-- Select Units --');
                    },
                    error: function(error) {
                        console.error('Error loading units:', error);
                    }
                });
            }
        });

        // Unit dropdown change event
        $('.ddlUnit').change(function () {
            var iUnitID = $(this).val();

            // Clear dependent dropdowns
            clearDropdown('.ddlDepartment');
            clearDropdown('.ddlSubDepartment');

            if (iUnitID && iUnitID !== "0") {
                $.ajax({
                    url: '@Url.Action("GetAllDepartments", "ManageVendor")',
                    type: 'GET',
                    data: { iUnitID: iUnitID },
                    success: function (response) {
                        populateDropdown('.ddlDepartment', response, 'departmentID', 'departmentName', '-- Select Departments --');
                    },
                    error: function(error) {
                        console.error('Error loading departments:', error);
                    }
                });
            }
        });

        // Department dropdown change event
        $('.ddlDepartment').change(function () {
            var iDepartmentID = $(this).val();

            // Clear dependent dropdown
            clearDropdown('.ddlSubDepartment');

            if (iDepartmentID && iDepartmentID !== "0") {
                $.ajax({
                    url: '@Url.Action("GetAllSubDepartments", "ManageVendor")',
                    type: 'GET',
                    data: { iDepartmentID: iDepartmentID },
                    success: function (response) {
                        populateDropdown('.ddlSubDepartment', response, 'subFunctionID', 'subFunctionName', '-- Select SubDepartments --');
                    },
                    error: function(error) {
                        console.error('Error loading sub departments:', error);
                    }
                });
            }
        });

        // Helper function to clear dropdown
        function clearDropdown(selector) {
            if ($(selector)[0] && $(selector)[0].selectize) {
                let selectizeInstance = $(selector)[0].selectize;
                selectizeInstance.clear();
                selectizeInstance.clearOptions();
                selectizeInstance.addOption({ value: "0", text: $(selector).find('option:first').text() });
                selectizeInstance.addItem("0");
            }
        }

        // Helper function to populate dropdown
        function populateDropdown(selector, data, valueField, textField, defaultText) {
            if ($(selector)[0] && $(selector)[0].selectize) {
                let selectizeInstance = $(selector)[0].selectize;
                selectizeInstance.clear();
                selectizeInstance.clearOptions();
                selectizeInstance.addOption({ value: "0", text: defaultText });
                selectizeInstance.addItem("0");

                if (data && data.length > 0) {
                    data.forEach(function(item) {
                        if (item[valueField] && item[textField]) {
                            selectizeInstance.addOption({ value: item[valueField], text: item[textField] });
                        }
                    });
                }
            }
        }
    });
</script>





