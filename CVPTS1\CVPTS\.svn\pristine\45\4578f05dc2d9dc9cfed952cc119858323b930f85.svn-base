﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using static BCM.Shared.BCPEnum;
using System.Security.Cryptography;

using System;
using System.Data;
using System.Linq;
using System.Collections.Generic;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMProcessBIA.Controllers;
[Area("BCMProcessBIA")]
public class ManageBusinessProcessesController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly CVLogger _CVLogger;
    readonly Utilities _Utilities;
    readonly int iEntityTypeID = Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.BusinessProcess);

    public ManageBusinessProcessesController(ProcessSrv iProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    public IActionResult Index()
    {
        return View();
    }
    public IActionResult ManageBusinessProcess(int IsDepartmentBIA = 0,int UnitID = 0,int iDepartmentID = 0)
    {
        List<BusinessProcessInfo> lstBusinessProcess = new List<BusinessProcessInfo>();

        try
        {
            PopulateDropdowns();

            ViewBag.UserOrgID = _UserDetails.OrgID.ToString();
            lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
            lstBusinessProcess = GetBusinessProcess(lstBusinessProcess);

            
                if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
                {
                    if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                    {
                        lstBusinessProcess = _Utilities.FilterListByOrgGroupID(lstBusinessProcess, _UserDetails.OrgGroupID);

                    }
                    else
                    {

                        lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                        lstBusinessProcess = _Utilities.FilterListByRoleID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    }
                }
            if (IsDepartmentBIA != 0)
            {
                lstBusinessProcess = lstBusinessProcess.Where(x=> x.DepartmentID == iDepartmentID).ToList();
            }


                ViewBag.TotalCount = lstBusinessProcess.Count;
            ViewBag.UnderBCMCount = lstBusinessProcess.Where(x => x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;
            ViewBag.CriticalCount = lstBusinessProcess.Where(x => x.IsCritical == 1 && x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;
            ViewBag.NonCriticalCount = lstBusinessProcess.Where(x => x.IsCritical == 0 && x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;

            // Remove the ProcessCode grouping as it's causing display issues
            // ViewBag.ProcessCode = lstBusinessProcess
            //                   .GroupBy(p => new { p.ProcessCode,p.ProcessName}).Select(g => g.First())
            //                   .ToList();
            ViewBag.lstBusinessProcess = lstBusinessProcess;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            //return NotFound("No any record");
        }
        return View();
    }

    public List<BusinessProcessInfo> GetBusinessProcess(List<BusinessProcessInfo> lstBusinessProcess)
    {
        try
        {
            // Don't reload data - use the passed list
            if (_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole))
            {
                // For admin users, apply basic filtering without role restrictions
                lstBusinessProcess = _Utilities.GetBusinessProcess(lstBusinessProcess, 0, 0, 0, 0, -1);
            }
            else
            {
                // For non-admin users, apply role-based filtering only for initial load
                lstBusinessProcess = _Utilities.FilterListForOwner(lstBusinessProcess, 0, 0, 0, 0, Convert.ToInt32(BCPEnum.EntityType.BusinessProcess), -1);
            }

        }
        catch (Exception)
        {

            throw;
        }
        return lstBusinessProcess;
    }

    [HttpGet]
    public IActionResult AddBusinessProcess()
    {
        
        BusinessProcessInfo lstBusinessProcess = new BusinessProcessInfo();
        try
        {
            lstBusinessProcess.OrgID = _UserDetails.OrgID;
            PopulateDropdowns();
           // lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddBusinessProcess", lstBusinessProcess);
    }



    [HttpPost]
    public IActionResult AddBusinessProcess(BusinessProcessInfo businessProcessInfo)
    {
        bool bSuccess = false;
        string errorMessage = "";

        try
        {
            // Server-side validation: Check if process name already exists
            if (!string.IsNullOrEmpty(businessProcessInfo.ProcessName))
            {
                bool processNameExists = IsExistProcessName(businessProcessInfo.ProcessName, businessProcessInfo.ProcessID);
                if (processNameExists)
                {
                    errorMessage = "This Process Name already exists. Please choose another.";
                    ModelState.AddModelError("ProcessName", errorMessage);
                }
            }

            // If validation fails, return to the form
            if (!ModelState.IsValid)
            {
                PopulateDropdowns();
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = errorMessage });
                }
                return PartialView("_AddBusinessProcess", businessProcessInfo);
            }

            PopulateDropdowns();
            businessProcessInfo = BuildData("", businessProcessInfo);
            int iProcessID = _ProcessSrv.BusinessProcessMasterSave(businessProcessInfo);
            bSuccess = iProcessID > 0 ? true : false;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            errorMessage = "An error occurred while saving the business process.";
        }

        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? businessProcessInfo.ProcessName + " Added Successfully" : (string.IsNullOrEmpty(errorMessage) ? "Failed To Add Business Process." : errorMessage) });
        }
        return RedirectToAction("ManageBusinessProcess");
    }

    
   

    private BusinessProcessInfo BuildData(string strProcessID, BusinessProcessInfo objBusinesProcess)
    {

        try
        {
            string cronstringF = string.Empty;
            string cronstringT = string.Empty;
            objBusinesProcess.ProcessID = 0;
            objBusinesProcess.IsActive = 1;
            objBusinesProcess.ChangedBy = _UserDetails.UserID;
            objBusinesProcess.CreatedBy = _UserDetails.UserID;
            objBusinesProcess.TimeOperationF = "0 0/  * * ? ";
            objBusinesProcess.TimeOperationT = "0 0/  * * ? ";
            objBusinesProcess.PeakTranVolume = string.Empty;
            objBusinesProcess.PeakPeriod = "-- Select --";
            objBusinesProcess.SPOF = string.Empty;
            objBusinesProcess.PossibleSol = string.Empty;
            objBusinesProcess.Field1 = string.Empty;
            objBusinesProcess.Field2 = string.Empty;
            objBusinesProcess.Field3 = string.Empty;
            objBusinesProcess.Field4 = string.Empty;

            objBusinesProcess.WorkType = 0;
            objBusinesProcess.HandOffs = 0;
            objBusinesProcess.TeamSize = 0;


            objBusinesProcess.EntityTypeID = (int)BCPEnum.EntityType.BusinessProcess;
            objBusinesProcess.RecordID = 0;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return objBusinesProcess;
    }

    public IActionResult GetFileredProcess(int OrgID = 0, int UnitID = 0, int DepartmentID = 0, int SubDepartmentID = 0, int IsUnderBCM = -1)
    {
        try
        {
            PopulateDropDown(_UserDetails.OrgGroupID, OrgID, UnitID, DepartmentID, SubDepartmentID);
            List<BusinessProcessInfo> lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);

            lstBusinessProcess = GetBusinessProcess(lstBusinessProcess);

            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    lstBusinessProcess = _Utilities.FilterListByOrgGroupID(lstBusinessProcess, _UserDetails.OrgGroupID);
                }
                else
                {
                    lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstBusinessProcess = _Utilities.FilterListByRoleID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                }
            }

            int TotalBusinessProcess = lstBusinessProcess.Count;
            int CriticalCount = lstBusinessProcess.Where(x => x.IsCritical == 1).ToList().Count;
            int NonCriticalCount = lstBusinessProcess.Where(x => x.IsCritical == 0).ToList().Count;

            // Debug: Log the filter parameters
            _CVLogger.LogInfo($"GetFileredProcess called with OrgID: {OrgID}, UnitID: {UnitID}, DepartmentID: {DepartmentID}, SubDepartmentID: {SubDepartmentID}, IsUnderBCM: {IsUnderBCM}");

            string strFilter = "x => 1==1";
            if (IsUnderBCM == -1)
            {
                if (OrgID > 0)
                {
                    strFilter = " && x => x.OrgID == " + OrgID;
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID == OrgID).ToList();
                }
                if (UnitID > 0)
                {
                    strFilter = " && x => x.UnitID == " + UnitID;
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.UnitID == UnitID).ToList();
                }
                if (DepartmentID > 0)
                {
                    strFilter = " && x => x.DepartmentID == " + DepartmentID;
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.DepartmentID == DepartmentID).ToList();
                }
                if (SubDepartmentID > 0)
                {
                    strFilter = " && x => x.SubfunctionID == " + SubDepartmentID;
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.SubfunctionID == SubDepartmentID).ToList();
                }
            }
            else
            {
                if (OrgID > 0)
                {
                    strFilter = " && x => x.OrgID == " + OrgID;
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID == OrgID).ToList();
                }
                if (UnitID > 0)
                {
                    strFilter = " && x => x.UnitID == " + UnitID;
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.UnitID == UnitID).ToList();
                }
                if (DepartmentID > 0)
                {
                    strFilter = " && x => x.DepartmentID == " + DepartmentID;
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.DepartmentID == DepartmentID).ToList();
                }
                if (SubDepartmentID > 0)
                {
                    strFilter = " && x => x.SubfunctionID == " + SubDepartmentID;
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.SubfunctionID == SubDepartmentID).ToList();
                }
                if (IsUnderBCM == 1)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList();
                }
                if (IsUnderBCM == 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.ProcessID == 0 || string.IsNullOrEmpty(x.ProcessCode)).ToList();
                }
            }

            ViewBag.lstBusinessProcess = lstBusinessProcess;
            ViewBag.TotalCount = TotalBusinessProcess;
            ViewBag.UnderBCMCount = lstBusinessProcess.Where(x => x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;
            ViewBag.CriticalCount = CriticalCount;
            ViewBag.NonCriticalCount = NonCriticalCount;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_FilteredBusinessProcessTable");
    }

    public IActionResult GetSearchProcess(string textSearch = "", int OrgID = 0, int UnitID = 0, int DepartmentID = 0, int SubDepartmentID = 0, int IsUnderBCM = -1, int iIsCritical = -1)
    {

        List<BusinessProcessInfo> lstBusinessProcess = new List<BusinessProcessInfo>();
        try
        {
            try
            {

                lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
                lstBusinessProcess = GetBusinessProcess(lstBusinessProcess);

                // Apply organization-level filtering first
                if (OrgID > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID == OrgID).ToList();
                }
                if (UnitID > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.UnitID == UnitID).ToList();
                }
                if (DepartmentID > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.DepartmentID == DepartmentID).ToList();
                }
                if (SubDepartmentID > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.SubfunctionID == SubDepartmentID).ToList();
                }

                // Apply role-based filtering only if user is not admin and no specific org filter is applied
                if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole)))
                {
                    if (OrgID == 0 && UnitID == 0 && DepartmentID == 0 && SubDepartmentID == 0)
                    {
                        // Only apply role-based filtering when no specific organizational filter is selected
                        lstBusinessProcess = _Utilities.FilterListByRoleID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    }
                }
                lstBusinessProcess = lstBusinessProcess.Where(x=> x.ProcessName.Contains(textSearch) || x.UnitName.Contains(textSearch) || x.OrgName.Contains(textSearch)
                                      || x.OrgName.Contains(textSearch) || x.DepartmentName.Contains(textSearch) || x.SubFunctionName.Contains(textSearch)
                                      || x.ProcessCode.Contains(textSearch)).ToList();
                if (iIsCritical != -1)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.IsCritical == iIsCritical && x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList();
                }

                if (IsUnderBCM == 1)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList();
                }
                else if (IsUnderBCM == 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.ProcessID == 0 || string.IsNullOrEmpty(x.ProcessCode)).ToList();
                }


                ViewBag.TotalCount = lstBusinessProcess.Count;
                ViewBag.UnderBCMCount = lstBusinessProcess.Where(x => x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;
                ViewBag.CriticalCount = lstBusinessProcess.Where(x => x.IsCritical.Equals(1) && x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;
                ViewBag.NonCriticalCount = lstBusinessProcess.Where(x => x.IsCritical == 0 && x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);

            }
            // Remove the ProcessCode grouping as it's causing display issues
            // ViewBag.ProcessCode = lstBusinessProcess
            //                   .GroupBy(p => new { p.ProcessCode, p.ProcessName }).Select(g => g.First())
            //                   .ToList();
            ViewBag.lstBusinessProcess = lstBusinessProcess;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_FilteredBusinessProcessTable");

    }

    [HttpGet]
    public IActionResult EditBusinessProcess(string RecordID,string ProcessID)
    {

        BusinessProcessInfo objBusinessProcess = new BusinessProcessInfo();
        try
        {
            objBusinessProcess = _ProcessSrv.GetBusinessProcessMasterByProcessID(Convert.ToInt32(RecordID));
            //PopulateDropdowns();
            //if (Convert.ToInt32(ProcessID) == 0)
            //{
            //    objBusinessProcess = _ProcessSrv.GetBusinessProcessByRecordID(Convert.ToInt32(RecordID.ToString() == "" ? "0" : RecordID), (int)EntityType.BusinessProcess);

            //}else if (Convert.ToInt32(ProcessID) > 0)
            //{
            //    objBusinessProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(ProcessID), 1);
            //}
            ViewBag.lstDepartment = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), objBusinessProcess.UnitID.ToString());
            ViewBag.lstSubDepartment = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(), objBusinessProcess.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), objBusinessProcess.DepartmentID.ToString());
            ViewBag.lstOrg = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());
            ViewBag.lstUnit = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString());
            //objBusinessProcess = _ProcessSrv.GetBusinessProcessMasterByProcessID(Convert.ToInt32(id));
            bool Button1 = false;
            bool btnSavePlan = false;
            //Dictionary<string, bool> NewList = _Utilities.ShowButtonsByAccess((objBusinessProcess.Status).ToString(), objBusinessProcess.ApproverID.ToString(), objBusinessProcess.ProcessOwnerID.ToString(),
            //                                            objBusinessProcess.AltProcessOwnerID.ToString(), Button1, Button1, Button1, Button1, _UserDetails.UserID.ToString(), btnSavePlan, Button1,
            //                                            Convert.ToInt32(BCPEnum.PrivilegeID.Modify), Button1, Button1, Button1);

            //btnSavePlan = NewList.FirstOrDefault(x => x.Key == "btnUpdate").Value;
            //if (btnSavePlan)
            //{
            //    objBusinessProcess.btnSave = "Visible";
            //}
            //else
            //{
            //    objBusinessProcess.btnSave = "Hidden";
            //}
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

        }
        return PartialView("_EditBusinessProcess", objBusinessProcess);
    }



    [HttpPost]
    public IActionResult EditBusinessProcess(BusinessProcessInfo objBusinessProcessInfo)
    {
        bool bSuccess = false;
        try
        {
            PopulateDropdowns();
            //objBusinessProcessInfo = BuildData("", objBusinessProcessInfo);
            objBusinessProcessInfo.TimeOperationF = "0 0/  * * ? ";
            objBusinessProcessInfo.TimeOperationT = "0 0/  * * ? ";
            objBusinessProcessInfo.PeakTranVolume = string.Empty;
            objBusinessProcessInfo.PeakPeriod = "-- Select --";
            objBusinessProcessInfo.SPOF = string.Empty;
            objBusinessProcessInfo.PossibleSol = string.Empty;
            objBusinessProcessInfo.Field1 = string.Empty;
            objBusinessProcessInfo.Field2 = string.Empty;
            objBusinessProcessInfo.Field3 = string.Empty;
            objBusinessProcessInfo.Field4 = string.Empty;

            objBusinessProcessInfo.WorkType = 0;
            objBusinessProcessInfo.HandOffs = 0;
            objBusinessProcessInfo.TeamSize = 0;

            objBusinessProcessInfo.EntityTypeID = (int)BCPEnum.EntityType.BusinessProcess;
            bSuccess = _ProcessSrv.BusinessProcessMasterUpdate(objBusinessProcessInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objBusinessProcessInfo.ProcessName + " Updated Successfully" : "Failed To Update Business Process." });
        }
        return RedirectToAction("ManageBusinessProcess");
    }

    [HttpGet]
    public IActionResult DeleteBusinessProcess(int id)
    {
        BusinessProcessInfo objBusinessProcess = new BusinessProcessInfo();
        try
        {
            PopulateDropdowns();
            objBusinessProcess = _ProcessSrv.GetBusinessProcessMasterByProcessID(id);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_DeleteBusinessProcess", objBusinessProcess);
    }

    [HttpPost]
    public IActionResult DeleteBusinessProcess(BusinessProcessInfo objBusinessProcess)
    {
        bool bSuccess = false;
        try
        {
            PopulateDropdowns();
            bSuccess = _ProcessSrv.BusinessProcessMaster_DeleteByProcessID(objBusinessProcess.ProcessID, Convert.ToInt32(BCPEnum.EntityType.BusinessProcess));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objBusinessProcess.ProcessName + " Deleted Successfully" : "Failed To Delete Business Process." });
        }
        return RedirectToAction("ManageBusinessProcess");
    }

    /// <summary>
    /// Check if business process name already exists via AJAX
    /// </summary>
    [HttpGet]
    public IActionResult CheckProcessNameExists(string processName, int processId = 0)
    {
        try
        {
            // Skip validation if it's the same process (for edit mode)
            if (processId > 0)
            {
                var currentProcess = _ProcessSrv.GetBusinessProcessByProcessId(processId, (int)BCPEnum.EntityType.BusinessProcess);
                if (currentProcess != null && currentProcess.ProcessName.Equals(processName, StringComparison.OrdinalIgnoreCase))
                {
                    return Json(new { exists = false });
                }
            }

            // Check if process name exists using the existing method
            bool exists = IsExistProcessName(processName, processId);
            return Json(new { exists = exists });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { exists = false, error = ex.Message });
        }
    }

    /// <summary>
    /// Check if business process name already exists
    /// </summary>
    private bool IsExistProcessName(string processName, int processId = 0)
    {
        try
        {
            if (string.IsNullOrEmpty(processName))
                return false;

            // Get all business processes for the current organization
            var existingProcesses = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);

            // Check if any process has the same name (case-insensitive) but different ID
            var duplicateProcess = existingProcesses.FirstOrDefault(p =>
                p.ProcessName.Equals(processName, StringComparison.OrdinalIgnoreCase)
                && p.ProcessID != processId);

            return duplicateProcess != null;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return false;
        }
    }

    public void GetBusinessProcess(int iOrgID = 0)
    {


    }

    public void PopulateDropDown(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());

            ViewBag.Unit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);

            ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

            ViewBag.ResourceList = _Utilities.GetAllResourceList();

            ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();

            ViewBag.lstResource = _ProcessSrv.GetAllResourcesList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public void PopulateDropdowns()
    {
        try
        {
            ViewBag.lstDepartment = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString());
            ViewBag.lstSubDepartment = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.DepartmentID.ToString());
            ViewBag.lstOrg = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());
            ViewBag.ResourcesInfo = _Utilities.GetAllResourceList();
            ViewBag.lstUnit = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString());
            ViewBag.lstorgGroupInfoList = _Utilities.GetOrgGroupList();

            ViewBag.SelectedUnit = "0";
            ViewBag.SelectedDepartment = "0";

            List<UserRoleMasterInfo> RoleMasterList = _Utilities.GetUserRoleMasterByOrgID(_UserDetails.OrgID);
            ViewBag.lstRoleMasterList = RoleMasterList;

            List<TypeMasterInfo> TypeList = _Utilities.GetTypeInfoByEntityID(Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.ResourcePlan));
            ViewBag.lstType = TypeList;

            ViewBag.lstCompany = _Utilities.GetCompanyMasterInfoList(_UserDetails.OrgID);
            ViewBag.lstResourceDesignation = _Utilities.GetAllResourceDesignationList();
            ViewBag.lstResource = _Utilities.GetResources(_UserDetails.OrgID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    [HttpGet]
    public JsonResult GetAllUnits(int iOrgID)
    {
        try
        {
            ViewBag.SelectedOrgID = iOrgID.ToString();
            var objDepartmentList = _Utilities.BindUnit(iOrgID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }   
        return Json(null);
    }


    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID)
    {
        try
        {
            ViewBag.SelectedUnit = iUnitID.ToString();
            var objDepartmentList = _Utilities.BindFunction(iUnitID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllSubDepartments(int iDepartmentID)
    {
        try
        {
            ViewBag.SelectedDepartment = iDepartmentID.ToString();
            var objSubDepartmentList = _Utilities.BindSubFunction(iDepartmentID);
            return Json(objSubDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult DebugBusinessProcessData(int OrgID = 0)
    {
        try
        {
            var allData = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
            var debugInfo = new
            {
                TotalCount = allData.Count,
                OrganizationCounts = allData.GroupBy(x => new { x.OrgID, x.OrgName })
                                          .Select(g => new {
                                              OrgID = g.Key.OrgID,
                                              OrgName = g.Key.OrgName,
                                              Count = g.Count()
                                          }).ToList(),
                FilteredCount = OrgID > 0 ? allData.Where(x => x.OrgID == OrgID).Count() : 0,
                UserDetails = new
                {
                    UserOrgID = _UserDetails.OrgID,
                    IsProductAdmin = _Utilities.IsProductAdmin(_UserDetails.UserRole),
                    IsSuperAdmin = _Utilities.IsSuperAdmin(_UserDetails.UserRole)
                }
            };
            return Json(debugInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { Error = ex.Message });
        }
    }
}

