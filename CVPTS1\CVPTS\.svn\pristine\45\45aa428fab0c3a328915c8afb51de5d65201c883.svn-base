﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model BCM.BusinessClasses.RecoveryPlan
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@

<script src="~/lib/jquery/jquery.min.js"></script>
<script src="~/lib/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.min.js"></script>
<script src="~/lib/datatables/js/jquery.datatables.min.js"></script>
<script src="~/lib/datatables/js/datatables.bootstrap5.min.js"></script>
<script src="~/lib/selectize/selectize.min.js"></script>
<script src="~/js/managerecoveryplan.js"></script>
<script src="~/js/password_toggle.js"></script>

<style>
/* Force hide Previous button on first step */
.wizard .current:first-child ~ .actions a[href="#previous"],
.wizard .current:first-child + .actions a[href="#previous"],
.wizard .steps li:first-child.current ~ .actions a[href="#previous"] {
    display: none !important;
}

/* Additional CSS to ensure Previous button is hidden on first step */
.wizard .actions a[href="#previous"] {
    display: none;
}

/* Show Previous button when not on first step */
.wizard .steps li:not(:first-child).current ~ .actions a[href="#previous"] {
    display: inline-block !important;
}
</style>

<div class="wizard-content">
    <form asp-action="EditRecoveryPlan" method="post" class="tab-wizard wizard-circle wizard clearfix example-form">
        <h6>
            <span class="step"><i class="cv-recovery-plan"></i></span>
            <span class="step_title">Recovery Plan Cofiguration</span>
        </h6>
        <section>
            <div class="row row-cols-2">
                <div class="col">
                    <div class="form-group">
                        <div>
                            <input type="hidden" id="txtPlanIds" asp-for="ID" />
                        </div>
                        <label class="form-label">Plan Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-name"></i></span>
                            <input type="text" id="txtPlanName" asp-for="PlanName" class="form-control" placeholder="Enter Plan Name" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Organization Code</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <select class="form-select form-control" asp-for="OrgID" autocomplete="off" id="ddlOrganizations" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.OrgInfo,"Id","OrganizationName"))">
                                <option selected value="0">-- Select Organizations --</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Department</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-department"></i></span>
                            <select class="form-select form-control" asp-for="DepartmentID" autocomplete="off" id="ddlDepartments" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Department,"DepartmentID","DepartmentName"))">
                                <option selected value="0">-- select Departments --</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Plan Owner</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-owner"></i></span>
                            <select class="form-select form-control" asp-for="PlanOwnerID" autocomplete="off" id="ddlOwners" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ResourceList,"ResourceId","ResourceName"))">
                                <option selected value="0">-- select Owner --</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Estimated Time</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-calendar"></i></span>
                            <input type="time" id="txtEstimatedTime" asp-for="EstimatedRecoveryTime" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label class="form-label">Plan Code</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-name"></i></span>
                            <input type="text" id="txtPlanCode" asp-for="PlanCode" class="form-control" placeholder="Enter Plan Code" disabled />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Unit</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-unit"></i></span>
                            <select class="form-select form-control" asp-for="UnitID" autocomplete="off" id="ddlUnits" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Unit,"UnitID","UnitName"))">
                                <option selected value="0">-- Select Units --</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Sub Department</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                            <select class="form-select form-control" asp-for="SubfunctionID" autocomplete="off" id="ddlSubDepartments" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.SubDepartment,"SubFunctionID","SubFunctionName"))">
                                <option selected value="0">-- Select SubDepartments --</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Plan Approver</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-approver"></i></span>
                            <select class="form-select form-control" asp-for="PlanApproverID" autocomplete="off" id="ddlApprovers" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ResourceList,"ResourceId","ResourceName"))">
                                <option selected value="0">-- select Approver --</option>
                            </select>
                        </div>
                    </div>


                    <div class="form-group">
                        <label class="form-label">Comments</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-comment"></i></span>
                            <input type="text" id="txtComment" asp-for="Comments" class="form-control" placeholder="Enter Comments" />
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <h6>
            <span class="step"><i class="cv-reviews"></i></span>
            <span class="step_title">Review Section</span>
        </h6>
        <section>
            <div class="row row-cols-2">
                <div class="col">
                    <div class="form-group">
                        <label class="form-label">Last Review Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-calendar"></i></span>
                            <input type="date" id="txtLastReviewDate" asp-for="LastReviewDate" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label class="form-label">Next Review Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-calendar"></i></span>
                            <input type="date" id="txtNextReviewDate" asp-for="PlanReviewDate" class="form-control" />
                        </div>
                    </div>
                </div>
            </div> 
        </section>        
    </form>
</div>

<script>
    $(document).ready(function () {
        // Additional control for Previous button visibility
        function controlPreviousButtonVisibility() {
            // Hide Previous button initially and on first step
            $('a[href="#previous"]').hide();

            // Monitor step changes
            $(document).on('click', 'a[href="#next"]', function() {
                setTimeout(function() {
                    var currentStep = $('.wizard .steps .current').index();
                    if (currentStep === 0) {
                        $('a[href="#previous"]').hide();
                    } else {
                        $('a[href="#previous"]').show();
                    }
                }, 100);
            });

            $(document).on('click', 'a[href="#previous"]', function() {
                setTimeout(function() {
                    var currentStep = $('.wizard .steps .current').index();
                    if (currentStep === 0) {
                        $('a[href="#previous"]').hide();
                    }
                }, 100);
            });
        }

        // Call the function immediately and with delays
        controlPreviousButtonVisibility();
        setTimeout(controlPreviousButtonVisibility, 500);
        setTimeout(controlPreviousButtonVisibility, 1000);

        $('a').click(function (arrayOfValues) {
            var href = $(this).attr('href');
            if (href == "#finish") {
                var objdata =
                {
                    ID: $('#txtPlanIds').val(),
                    OrgID: $('#ddlOrganizations').val(),
                    UnitID: $('#ddlUnits').val(),
                    DepartmentID: $('#ddlDepartments').val(),
                    SubFunctionID: $('#ddlSubDepartments').val(),
                    PlanOwnerID: $('#ddlOwners').val(),
                    PlanApproverID: $('#ddlApprovers').val(),
                    PlanName: $('#txtPlanName').val(),
                    EstimatedRecoveryTime: $('#txtEstimatedTime').val() || "00:00",
                    PlanCode: $('#txtPlanCode').val(),
                    Comments: $('#txtComment').val(),
                    LastReviewDate: $('#txtLastReviewDate').val(),
                    PlanReviewDate: $('#txtNextReviewDate').val() || null,
                }

                // $.ajax({
                //     type: "POST",
                //     url: '/BCMFunctionRecoveryPlan/ManageRecoveryPlans/EditRecoveryPlan',
                //     data: JSON.stringify(objdata),
                //     contentType: 'application/json',
                //     dataType: 'JSON',
                //     success: function (response) {
                //         //$('#CreateModal').modal('hide');
                //         location.reload();
                //     },
                //     error: function (data) {
                //         //console.log('error is invoked');
                //         alert('failure');
                //     }
                // });
                   $.ajax({
                        type: "POST",
                        url: '@Url.Action("EditRecoveryPlan", "ManageRecoveryPlans", new { area = "BCMFunctionRecoveryPlan" })',
                        data: JSON.stringify(objdata),
                        contentType: 'application/json',
                        dataType: 'json',  // 'json' should be lowercase
                        success: function (response) {
                            if (response && response > 0) {
                                // Close the modal
                                $('#CreateModal').modal('hide');

                                // Show success message
                                alert('Recovery plan updated successfully!');

                                // Use a controlled reload with delay to prevent multiple DataTable initializations
                                setTimeout(function() {
                                    window.location.reload(true);
                                }, 500);
                            } else {
                                alert('Failed to update recovery plan. Please try again.');
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Error details: ', status, error);  // Log error details
                            alert('Request failed');
                        }
                    });

            }
        });
    });
</script>