﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;

namespace BCM.UI.Areas.BCMTraining.Controllers;

[Area("BCMTraining")]
public class ManageBCMTrainingFormController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public ManageBCMTrainingFormController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult ManageBCMTrainingForm()
    {
        var lstBCMTrainingMaster = new List<BCMTrainingMaster>();

        try
        {
            PopulateDropdown();
            lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstBCMTrainingMaster);
    }

    [HttpGet]
    public IActionResult AddBCMTrainingMaster()
    {
        try
        {
            PopulateDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_AddBCMTraining", new BCMTrainingMaster());
    }

    [HttpPost]
    public IActionResult AddBCMTrainingMaster(BCMTrainingMaster objBCMTrainingMaster)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.SaveTrainingMaster(objBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }


    [HttpGet]
    public IActionResult EditBCMTrainingMaster(int iId)
    {
        BCMTrainingMaster objBCMTrainingMaster = new BCMTrainingMaster();

        try
        {
            PopulateDropdown();

            objBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getByID(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_EditBCMTraining", objBCMTrainingMaster);
    }

    [HttpPost]
    public IActionResult EditBCMTrainingMaster(BCMTrainingMaster objBCMTrainingMaster)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.SaveTrainingMaster(objBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }


    [HttpGet]
    public IActionResult DeleteBCMTrainingMaster(string iId)
    {
        BCMTrainingMaster objBCMTrainingMaster = new BCMTrainingMaster();

        try
        {
            // Convert string ID to int for the service call
            if (int.TryParse(iId, out int trainingId))
            {
                objBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getByID(trainingId);

                // Ensure both ID properties are set correctly
                objBCMTrainingMaster.ID = iId;
                objBCMTrainingMaster.TrainingMasterID = trainingId;

                //   _CVLogger.LogInfoApp($"Retrieved training for delete: ID={iId}, TrainingMasterID={trainingId}, Name={objBCMTrainingMaster.TrainingName}");
            }
            else
            {
                //     _CVLogger.LogErrorApp($"Invalid training ID format: {iId}");
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteBCMTraining", objBCMTrainingMaster);
    }

    [HttpPost]
    public IActionResult DeleteBCMTrainingMaster(string ID, int TrainingMasterID, string TrainingName, int Status, int OwnerID)
    {
        bool TrainingMasterDelete = false;
        bool bQuestionsDelete = false;
        bool bDeleteOptions = false;
        string message = "";

        try
        {
            // Log received data for debugging
            //_CVLogger.LogInfoApp($"DELETE POST - Received: ID={ID}, TrainingMasterID={TrainingMasterID}, TrainingName={TrainingName}, Status={Status}, OwnerID={OwnerID}");

            int TrainingID = TrainingMasterID;

            // Validate that we have a valid TrainingID
            if (TrainingID <= 0)
            {
                // Try to use ID if TrainingMasterID is not set
                if (int.TryParse(ID, out int parsedId))
                {
                    TrainingID = parsedId;
                    //    _CVLogger.LogInfoApp($"Using parsed ID as TrainingID: {TrainingID}");
                }
                else
                {
                    //   _CVLogger.LogErrorApp($"Invalid TrainingID: TrainingMasterID={TrainingMasterID}, ID={ID}");
                    message = "Invalid training ID. Cannot delete training.";
                    TempData["DeleteMessage"] = message;
                    TempData["DeleteSuccess"] = false;
                    return RedirectToAction("ManageBCMTrainingForm");
                }
            }

            //_CVLogger.LogInfoApp($"Attempting to delete BCM Training with ID: {TrainingID}, Name: {TrainingName}");

            // Delete the training master using the specified method
            TrainingMasterDelete = _ProcessSrv.BCMTrainingMaster_DeleteByID(TrainingID);

            if (TrainingMasterDelete)
            {
                // Delete related questions and options
                List<BCMTrainingMaster> objQuestiondetails = _ProcessSrv.QuestionsDetailsGetByTrainingMasterID(TrainingID);
                bQuestionsDelete = _ProcessSrv.BCMTrainingQuestion_DeleteByTrainingID(TrainingID);

                foreach (BCMTrainingMaster item in objQuestiondetails)
                {
                    bDeleteOptions = _ProcessSrv.BCMTrainingOption_DeleteByQuestionID(Convert.ToInt32(item.ID));
                }

                //_CVLogger.LogInfoApp($"Successfully deleted BCM Training: {TrainingName} (ID: {TrainingID})");
                message = $"Training '{TrainingName}' has been deleted successfully.";
            }
            else
            {
                // _CVLogger.LogErrorApp($"Failed to delete BCM Training: {TrainingName} (ID: {TrainingID})");
                message = $"Failed to delete training '{TrainingName}'. Please try again.";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            message = $"An error occurred while deleting training '{TrainingName}': {ex.Message}";
        }

        // Store message in TempData to show on the redirected page
        TempData["DeleteMessage"] = message;
        TempData["DeleteSuccess"] = TrainingMasterDelete;

        return RedirectToAction("ManageBCMTrainingForm");
    }

    public IActionResult GetDepartmentByID(int iDepartmentId)
    {
        try
        {
            List<BCMTrainingMaster> lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();
            if (iDepartmentId > 0)
            {
                lstBCMTrainingMaster = lstBCMTrainingMaster.Where(x => x.DepartmentID == iDepartmentId).ToList();
                if (lstBCMTrainingMaster == null || !lstBCMTrainingMaster.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterBCMTraining", lstBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }


    public IActionResult GetUnitByID(int iUnitId)
    {
        try
        {
            List<BCMTrainingMaster> lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();
            if (iUnitId > 0)
            {
                lstBCMTrainingMaster = lstBCMTrainingMaster.Where(x => x.UnitID == iUnitId).ToList();
                if (lstBCMTrainingMaster == null || !lstBCMTrainingMaster.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterBCMTraining", lstBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMTrainingForm");
    }


    public void PopulateDropdown()
    {
        try
        {
            ViewBag.DepartmentInfo = new SelectList(_Utilities.GetDepartmentAllListForDropdown(), "DepartmentID", "DepartmentName");
            ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString()), "Id", "OrganizationName");
            ViewBag.OrgUnit = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails.OrgID), "UnitID", "UnitName");
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
}

