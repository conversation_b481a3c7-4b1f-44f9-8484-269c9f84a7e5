﻿// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

// Create chart instance - ensure the element exists first
var departmentBI<PERSON>hart;

// Make sure the chart is created only if the element exists
if (document.getElementById("DepartmentBIA_Chart")) {
    departmentBIAChart = am4core.create("DepartmentBIA_Chart", am4charts.PieChart);
    departmentBIAChart.className = "pie-chart-improved";
    if (departmentBIAChart.logo) {
        departmentBIAChart.logo.disabled = true;
    }
} else {
    console.error("Element DepartmentBIA_Chart not found in the DOM");
}

// Make chart responsive - only if chart was created


if (departmentBIAChart) {
    departmentBIAChart.responsive.enabled = true;
    departmentBIAChart.responsive.rules.push({
        relevant: function () {
            // Apply to all screen sizes
            return true;
        },
        state: function (target, stateId) {
            if (target instanceof am4charts.PieSeries) {
                var state = target.states.create(stateId);
                // Adjust inner radius based on screen size
                if (target.chart && target.chart.pixelWidth && target.chart.pixelWidth <= 400) {
                    state.properties.innerRadius = am4core.percent(40);
                }
                return state;
            }
            if (target instanceof am4charts.Legend) {
                var state = target.states.create(stateId);
                // Position legend based on screen size
                if (target.chart && target.chart.pixelWidth) {
                    if (target.chart.pixelWidth <= 600) {
                        state.properties.position = "bottom";
                        state.properties.width = undefined;
                        if (target.chart.pixelWidth <= 400) {
                           // state.properties.maxHeight = 80;
                        }
                    } else {
                        state.properties.position = "bottom";
                        //state.properties.width = 120;
                    }
                } else {
                    // Default position if pixelWidth is not available
                    state.properties.position = "bottom";
                    //state.properties.width = 300;
                }
                return state;
            }
            if (target instanceof am4charts.Chart) {
                var state = target.states.create(stateId);
                // Adjust padding based on screen size
                if (target.pixelWidth && target.pixelWidth <= 400) {
                    state.properties.paddingTop = 0;
                    state.properties.paddingRight = 0;
                    state.properties.paddingBottom = 0;
                    state.properties.paddingLeft = 0;
                } else {
                    state.properties.paddingTop = 10;
                    state.properties.paddingRight = 15;
                    state.properties.paddingBottom = 10;
                    state.properties.paddingLeft = 15;
                }
                return state;
            }
            return null;
        }
    });
}

// Continue only if chart was created
if (departmentBIAChart) {
    // Add data
    departmentBIAChart.data = [{
        "category": "Completed",
        "value": 35,
        "percentage": 35,
    }, {
        "category": "In Progress",
        "value": 40,
        "percentage": 40,
    }, {
        "category": "Not Started",
        "value": 25,
        "percentage": 25,
    }];

    // Add and configure Series
    var pieSeries = departmentBIAChart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "value";
    pieSeries.dataFields.category = "category";
    pieSeries.slices.template.propertyFields.fill = "color";
    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 1; 
    pieSeries.slices.template.strokeOpacity = 1;

    // Default hover effect
    pieSeries.slices.template.states.getKey("hover").properties.scale = 1.05;

    // Make slices bigger
    pieSeries.slices.template.tooltipText = "{category}: {value}";
    pieSeries.slices.template.cornerRadius = 5;

    // Let's cut a hole in our Pie chart the size of 30% the radius
    departmentBIAChart.innerRadius = am4core.percent(70);

    // Set chart padding to ensure labels fit - matched with other charts
    departmentBIAChart.paddingTop = 10;
    departmentBIAChart.paddingBottom = 10;
    departmentBIAChart.paddingLeft = 15;
    departmentBIAChart.paddingRight = 15;
    departmentBIAChart.radius = am4core.percent(90); 

    // Reduce background area
    departmentBIAChart.background.fill = am4core.color("#ffffff");
    departmentBIAChart.background.fillOpacity = 0;

    // Add a legend
    departmentBIAChart.legend = new am4charts.Legend();
    departmentBIAChart.legend.markers.template.width = 12;
    departmentBIAChart.legend.markers.template.height = 12;
    //departmentBIAChart.legend.fontSize = 12;
    //departmentBIAChart.legend.contentAlign = "left";

    // Disable labels on pie slices
    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;
    pieSeries.alignLabels = false;

    // Add actual count to legend
    pieSeries.legendSettings.itemValueText = "{value}";
    pieSeries.legendSettings.valueText = "{value}";

    // Make legend more visible
    departmentBIAChart.legend.valueLabels.template.fontSize = 12;
    departmentBIAChart.legend.valueLabels.template.fontWeight = "bold";
    departmentBIAChart.legend.labels.template.fontSize = 12;
    departmentBIAChart.legend.useDefaultMarker = true;
    // Position and width are handled by responsive rules
}
