﻿@{
    ViewData["Title"] = "KPIReport";
}
@using DevExpress.AspNetCore;
@using BCM.UI.Areas.BCMReports.ReportTemplate;

@section Styles
{
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
}
@section HeaderScripts
{
    <script src="~/js/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/viewer.part.bundle.js" asp-append-version="true"></script>
}
<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @{
                @Html.DevExpress().WebDocumentViewer("kpireportdocumentviewer").Height("850px").Bind(new KPIReport(ViewData["kpireportdata"].ToString()));
            }
        </div>
    </div>
</div>