﻿@using BCM.Shared
@using BCM.BusinessClasses
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@* @{
    List<BusinessProcessInfo> businessProcessInfos = @ViewBag.lstBusinessProcess as List<BusinessProcessInfo>;

                          int iIndex = 0;


}
 *@
@{
    List<BusinessProcessInfo> businessProcessInfos = (List<BusinessProcessInfo>)ViewBag.lstBusinessProcess;
    int iIndex = 0;
    int secondIndex = 0;
}


<div class="list-group list-group-horizontal fw-semibold bg-white">

    <div class="border-0 flex-fill p-2" style="width:50px"></div>
    <div class="border-0 flex-fill p-2" style="width:50px">#</div>
    <div class="border-0 w-25 flex-fill p-2">Process Details</div>
    <div class="border-0 w-25 flex-fill p-2">Owner Details</div>
    <div class="border-0 w-25 flex-fill p-2">Org Level</div>
    <div class="border-0 w-25 flex-fill p-2">RTO / MAO / RPO</div>
    <div class="border-0 w-25 flex-fill p-2">IsCritical</div>
    <div class="border-0 w-25 flex-fill p-2">Status</div>
    <div class="border-0 w-25 flex-fill p-2">View BIA</div>
    <div class="border-0 rounded-0 p-2 text-center" style="width: 7%;">Action</div>
</div>



@*  @foreach (var ReviewTypeMasterItems in Model.ReviewTypeMaster.GroupBy(x => new { x.ReportCode }).Select(y => y.First()))
        { *@
@if (ViewBag.ProcessCode != null)
{
    @foreach (var Codes in ViewBag.ProcessCode)
    {

        @foreach (var objBsunessProcess in @ViewBag.lstBusinessProcess)
        {
            if (@Codes.ProcessCode == @objBsunessProcess.ProcessCode && @Codes.ProcessName == @objBsunessProcess.ProcessName)
            {
                iIndex++;
                if (iIndex == 1)
                {
                    secondIndex++;
                    <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal bg-white">
                        <div class="flex-fill p-2 border-0" style="width:50px">
                            @if (businessProcessInfos.Where(x => x.ProcessCode == @Codes.ProcessCode).ToList().Count > 1 && @Codes.ProcessCode != string.Empty)
                            {
                                <span class="toggle-password d-block" title="Expand Versions" role="button" data-bs-toggle="collapse" data-bs-target="#@Codes.ProcessCode" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                            }

                        </div>

                        <div class="flex-fill p-2 border-0" style="width:50px">
                            <span class="fw-semibold text-primary"> @secondIndex</span><br />
                        </div>



                        <div class="w-25 flex-fill p-2 border-0 d-grid">

                            <span class="text-secondary">Process Code :</span><span class="fw-semibold text-warning" title="Process Code"> @objBsunessProcess.ProcessCode</span>
                            <span>@objBsunessProcess.ProcessName</span>
                            <span><span class="text-secondary" title="Process Version">Version :</span><span> @objBsunessProcess.Version</span></span>
                            <span>
                                <span class="text-secondary">Is Under BCM Scope :</span>
                                @if (@objBsunessProcess.ProcessCode != "")
                                {
                                    <span class="text-success fw-semibold">
                                        Yes
                                    </span>
                                }
                                else
                                {
                                    <span class="text-danger fw-semibold">
                                        No
                                    </span>
                                }
                            </span>
                        </div>
                        <div class="w-25 flex-fill p-2 border-0 ">

                            <table>
                                <tbody>
                                    <tr title="Process Owner">
                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                        <td> : </td>
                                        <td>@objBsunessProcess.ProcessOwner</td>
                                    </tr>
                                    @* <tr>
                                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                                        <td>:</td>
                                        <td>@objBsunessProcess.OwnerEmail</td>
                                    </tr> *@
                                    <tr>
                                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                                        <td>:</td>
                                        <td>@objBsunessProcess.ProcessOwnerMobile</td>
                                    </tr>
                                </tbody>
                            </table>

                        </div>
                        <div class="w-25 flex-fill p-2 border-0 ">
                            <div class="d-flex">
                                @* <div class="User-icon">
                                        <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                                    </div> *@
                                <div>
                                    <table>
                                        <tbody>
                                            <tr title="Unit">
                                                <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                                <td>:</td>
                                                <td>@objBsunessProcess.UnitName</td>
                                            </tr>
                                            <tr title="Department">
                                                <td class="fw-semibold"><i class="cv-department"></i> </td>
                                                <td>:</td>
                                                <td>@objBsunessProcess.DepartmentName</td>
                                            </tr>
                                            <tr title="Sub Department">
                                                <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                                <td>:</td>
                                                <td>@objBsunessProcess.SubFunctionName</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="w-25 flex-fill p-2 border-0 ">
                            <table>
                                <tbody>
                                    <tr title="RTO" style="display:none">
                                        <td>Owner RTO </td>
                                        <td> : </td>
                                        <td>
                                            @Utilities.GetFormattedRTO(Convert.ToInt32(@objBsunessProcess.OwnerRTO))
                                        </td>
                                    </tr>
                                    <tr title="MAO" style="display:none">
                                        <td>Owner MTR </td>
                                        <td>:</td>
                                        <td> @Utilities.GetFormattedRTO(Convert.ToInt32(@objBsunessProcess.OwnerMTR))</td>
                                    </tr>
                                    <tr title="Calculated RTO">
                                        <td>RTO </td>
                                        <td> : </td>
                                        <td>
                                            @Utilities.GetFormattedRTO(Convert.ToInt32(@<EMAIL>))
                                        </td>
                                    </tr>
                                    <tr title="Calculated MAO">
                                        <td>MAO </td>
                                        <td>:</td>
                                        <td>@Utilities.GetFormattedRTO(Convert.ToInt32(@objBsunessProcess.MTR))</td>
                                    </tr>
                                    <tr title="RPO">
                                        <td>RPO </td>
                                        <td>:</td>
                                        @* <td>@Utilities.GetFormattedRPO(Convert.ToInt32(@objBsunessProcess.RPO))</td> *@
                                        @if (@objBsunessProcess.RPOUnit == "1")
                                        {
                                            <td>
                                                @objBsunessProcess.RPO Minutes(s)
                                            </td>
                                        }
                                        @if (@objBsunessProcess.RPOUnit == "2")
                                        {
                                            <td>
                                                @objBsunessProcess.RPO Hour(s)
                                            </td>
                                        }
                                        @if (@objBsunessProcess.RPOUnit == "3")
                                        {
                                            <td>
                                                @objBsunessProcess.RPO Day(s)
                                            </td>
                                        }
                                        @if (@objBsunessProcess.RPOUnit == "")
                                        {
                                            <td>
                                                @objBsunessProcess.RPO Month(s)
                                            </td>
                                        }
                                        

                                       
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="w-25 flex-fill p-2 border-0">
                            @if (@objBsunessProcess.IsCritical == 1)
                            {

                                <span class="text-danger fw-semibold">
                                    Yes
                                </span>

                            }
                            else
                            {

                                <span class="text-success fw-semibold">
                                    No
                                </span>

                            }


                        </div>
                        <div class="w-25 flex-fill p-2 border-0 ">
                            @if (Convert.ToString(@objBsunessProcess.Status) == "0")
                            {
                                <span class="badge bg-info-subtle text-info py-1 fs-14 px-2 fw-normal">
                                    <i class="cv-initiated me-1"></i>
                                    Initiated
                                </span>
                            }
                            @if (Convert.ToString(@objBsunessProcess.Status) == "1")
                            {

                                <span class="badge bg-warning-subtle text-warning py-1 fs-14 px-2 fw-normal">
                                    <i class="cv-waiting me-1"></i>
                                    WaitingForApprove
                                </span>

                            }
                            @if (Convert.ToString(@objBsunessProcess.Status) == "2")
                            {

                                <span class="badge success-light-bg py-1 fs-14 px-2 fw-normal">
                                    <i class="cv-success me-1 align-middle"></i>
                                    Approved
                                </span>


                            }
                            @if (Convert.ToString(@objBsunessProcess.Status) == "3")
                            {

                                <span class="badge danger-light-bg py-1 fs-14 px-2 fw-normal">
                                    <i class="cv-error me-1"></i>
                                    DisApproved
                                </span>

                            }

                        </div>
                        <div class="w-25 flex-fill p-2 border-0 ">
                            @if (@objBsunessProcess.ProcessCode == string.Empty)
                            {
                                <a class="btn btn-sm btn-primary accept-policy px-2" asp-action="BusinessProcessForm" asp-controller="BusinessProcessForm" asp-route-strEntityTypeID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBsunessProcess.EntityTypeID.ToString())" asp-route-strRecordID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBsunessProcess.RecordID.ToString())" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBsunessProcess.ProcessID.ToString())">
                                    <span class="cv-view align-middle ViewBIA" type="button"></span>
                                </a>
                            }
                            else
                            {
                                <a class="btn btn-sm btn-primary px-2" asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBsunessProcess.ProcessID.ToString())">
                                    <span class="cv-view align-middle ViewBIA" type="button"></span>
                                </a>
                            }
                        </div>
                        <div class="flex-fill p-2 border-0" style="width:7%">
                            <span class="btn-action btnEdit" type="button" data-process-id="@objBsunessProcess.ProcessID" data-record-id="@objBsunessProcess.RecordID" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>


                            <span class="btn-action btnDelete" type="button" data-id="@objBsunessProcess.RecordID" data-bs-toggle="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                        </div>

                    </div>
                }
                else
                {
                    <div class="ps-3 collapse" id="@Codes.ProcessCode">
                        <div class="list-group list-group-horizontal fw-semibold bg-white">

                            <div class="border-0 flex-fill p-2" style="width:50px">#</div>
                            <div class="border-0 flex-fill p-2" style="width:50px"></div>
                            <div class="border-0 w-25 flex-fill p-2">Process Details</div>
                            <div class="border-0 w-25 flex-fill p-2">Owner Details</div>
                            <div class="border-0 w-25 flex-fill p-2">Org Level</div>
                            <div class="border-0 w-25 flex-fill p-2">RTO / MAO / RPO</div>
                            <div class="border-0 w-25 flex-fill p-2">IsCritical</div>
                            <div class="border-0 w-25 flex-fill p-2">Status</div>
                            <div class="border-0 w-25 flex-fill p-2">View BIA</div>
                            <div class="border-0 rounded-0 p-2 text-center" style="width: 7%;">Action</div>
                        </div>
                        <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal bg-white">
                            <div class="flex-fill p-2 border-0" is="index" style="width:50px">
                                <span class="fw-semibold text-primary"> @iIndex</span><br />
                            </div>


                            <div class="w-25 flex-fill p-2 border-0 ">
                                <span class="fw-semibold text-warning"> @objBsunessProcess.ProcessCode</span><br />
                                Process Name:
                                <a href="#">
                                    <span class="fw-semibold text-primary"> @objBsunessProcess.ProcessName</span><br />

                                </a>
                                <span>Version: @objBsunessProcess.Version</span><br />
                                <span>
                                    Is Under BCM Scope:
                                    @if (@objBsunessProcess.ProcessCode != "")
                                    {
                                        <span class="text-success fw-semibold">
                                            Yes
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="text-danger fw-semibold">
                                            No
                                        </span>
                                    }
                                </span>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">

                                <table>
                                    <tbody>
                                        <tr>
                                            <td class="fw-semibold"><i class="cv-user"></i></td>
                                            <td> : </td>
                                            <td>@objBsunessProcess.ProcessOwner</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                                            <td>:</td>
                                            <td><a class="text-primary" href="#">@objBsunessProcess.OwnerEmail</a></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                                            <td>:</td>
                                            <td>@objBsunessProcess.ProcessOwnerMobile</td>
                                        </tr>
                                    </tbody>
                                </table>

                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                <div class="d-flex">
                                    @* <div class="User-icon">
                                            <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                                        </div> *@
                                    <div>
                                        <table>
                                            <tbody>
                                                <tr title="Unit">
                                                    <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                                    <td>:</td>
                                                    <td>@objBsunessProcess.UnitName</td>
                                                </tr>
                                                <tr title="Department">
                                                    <td class="fw-semibold"><i class="cv-department"></i> </td>
                                                    <td>:</td>
                                                    <td>@objBsunessProcess.DepartmentName</td>
                                                </tr>
                                                <tr title="Sub Department">
                                                    <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                                    <td>:</td>
                                                    <td>@objBsunessProcess.SubFunctionName</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                <table>
                                    <tbody>
                                        <tr title="RTO">
                                            <td>Owner RTO </td>
                                            <td> : </td>
                                            <td>
                                                @Utilities.GetFormattedRTO(Convert.ToInt32(@objBsunessProcess.OwnerRTO))
                                            </td>
                                        </tr>
                                        <tr title="MAO">
                                            <td>Owner MTR </td>
                                            <td>:</td>
                                            <td> @Utilities.GetFormattedRTO(Convert.ToInt32(@objBsunessProcess.OwnerMTR))</td>
                                        </tr>
                                        <tr title="Calculated RTO">
                                            <td>RTO </td>
                                            <td> : </td>
                                            <td>
                                                @Utilities.GetFormattedRTO(Convert.ToInt32(@objBsunessProcess.RTO))
                                            </td>
                                        </tr>
                                        <tr title="Calculated MAO">
                                            <td>MAO </td>
                                            <td>:</td>
                                            <td>@Utilities.GetFormattedRTO(Convert.ToInt32(@objBsunessProcess.MTR))</td>
                                        </tr>
                                        <tr title="RPO">
                                            <td>RPO </td>
                                            <td>:</td>
                                            <td>@Utilities.GetFormattedRPO(Convert.ToInt32(@objBsunessProcess.RPO))</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="w-25 flex-fill p-2 border-0">
                                @if (@objBsunessProcess.IsCritical == 1)
                                {

                                    <span class="text-danger fw-semibold">
                                        Yes
                                    </span>

                                }
                                else
                                {

                                    <span class="text-success fw-semibold">
                                        No
                                    </span>

                                }


                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                @if (Convert.ToString(@objBsunessProcess.Status) == "0")
                                {
                                    <span class="badge bg-info-subtle text-info py-1 fs-14 px-2 fw-normal">
                                        <i class="cv-initiated me-1"></i>
                                        Initiated
                                    </span>
                                }
                                @if (Convert.ToString(@objBsunessProcess.Status) == "1")
                                {

                                    <span class="badge bg-warning-subtle text-warning py-1 fs-14 px-2 fw-normal">
                                        <i class="cv-waiting me-1"></i>
                                        WaitingForApprove
                                    </span>

                                }
                                @if (Convert.ToString(@objBsunessProcess.Status) == "2")
                                {

                                    <span class="badge success-light-bg py-1 fs-14 px-2 fw-normal">
                                        <i class="cv-success me-1 align-middle"></i>
                                        Approved
                                    </span>


                                }
                                @if (Convert.ToString(@objBsunessProcess.Status) == "3")
                                {

                                    <span class="badge danger-light-bg py-1 fs-14 px-2 fw-normal">
                                        <i class="cv-error me-1"></i>
                                        DisApproved
                                    </span>

                                }

                            </div>
                            <div class="w-25 flex-fill p-2 border-0 ">
                                @if (@objBsunessProcess.ProcessCode == string.Empty)
                                {
                                    <a class="btn btn-sm btn-primary accept-policy px-2" asp-action="BusinessProcessForm" asp-controller="BusinessProcessForm" asp-route-strEntityTypeID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBsunessProcess.EntityTypeID.ToString())" asp-route-strRecordID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBsunessProcess.RecordID.ToString())" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBsunessProcess.ProcessID.ToString())">
                                        <span class="cv-view align-middle ViewBIA" type="button"></span>
                                    </a>
                                }
                                else
                                {
                                    <a class="btn btn-sm btn-primary px-2" asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBsunessProcess.ProcessID.ToString())">
                                        <span class="cv-view align-middle ViewBIA" type="button"></span>
                                    </a>
                                }
                            </div>
                            <div class="flex-fill p-2 border-0" style="width:7%">
                                <span class="btn-action btnEdit" type="button" data-process-id="@objBsunessProcess.ProcessID" data-record-id="@objBsunessProcess.RecordID" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>

                                <span class="btn-action btnDelete" type="button" data-id="@objBsunessProcess.RecordID" data-bs-toggle="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                            </div>
                        </div>
                    </div>
                }
            }
        }
        iIndex = 0;
    }
}
else
{
    <tr>
        <td colspan="10" class="text-center py-4">
            <div class="d-flex flex-column align-items-center">
                <i class="cv-no-data text-muted mb-2" style="font-size: 3rem;"></i>
                <h5 class="text-muted mb-1">No Records Found</h5>
                <p class="text-muted mb-0">No Application data available to display.</p>
            </div>
        </td>
    </tr>
}



