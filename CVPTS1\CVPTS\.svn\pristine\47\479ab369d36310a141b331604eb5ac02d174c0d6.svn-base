﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Security.Helper;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.Web.CodeGenerators.Mvc.Templates.BlazorIdentity.Pages.Manage;
using System.Text.RegularExpressions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace BCM.UI.Areas.BCMAdministration.Controllers;

[Area("BCMAdministration")]
public class ResetPasswordController : Controller
{
    private ManageUsersDetails _UserDetails = new ManageUsersDetails();
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    private readonly BCMMail _BCMMail;
    string UserName;
    public ResetPasswordController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _UserDetails = _Utilities.LoginUserDetails();
        _CVLogger = CVLogger;

        if (_UserDetails == null)
        {
            RedirectToAction("Login", "Login");
        }
    }
    public IActionResult ResetPasswordNew(string Email, string User)
    {
        ForgetPassword forgetPassword = new ForgetPassword();
        forgetPassword.EmailID = Email;
        forgetPassword.UserName = User;
        //string strEmail = CryptographyHelper.Decrypt(Email);
        //string strUserID = CryptographyHelper.Decrypt(User);
        return View(forgetPassword);
    }

    [HttpPost]
    public IActionResult SavePassword(ForgetPassword forgetPassword)
    {
        bool IsPasswordChanged = false;
        string ErrorMessege = string.Empty;
        try
        {
            var GetPwd = _ProcessSrv.GetPWDBYEmailID(forgetPassword.EmailID.Trim());
            var OrgIDByEmailID = GetPwd.OrgID;
            var objVaultSetting = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt16(OrgIDByEmailID));
            
            var oDetails = _ProcessSrv.GetOldPassword(GetPwd.Password, Convert.ToInt32(GetPwd.UserID));
            UserName = oDetails.LoginName;
            if (forgetPassword.Password != "" && forgetPassword.NewPassword != "")
            {
                if (forgetPassword.Password == forgetPassword.NewPassword)
                {

                    bool ValidatedPwd = IsValid_Pwd(Convert.ToString(forgetPassword.Password));
                    string NewPassword = forgetPassword.NewPassword;
                    if (forgetPassword.Password.Trim().Length >= 7)
                    {
                        if (ValidatedPwd)
                        {
                            string countVal = "0";
                            if (objVaultSetting != null)
                            {
                                countVal = objVaultSetting.PasswordHistory;
                            }
                            var passwordHistoryList = _ProcessSrv.GetPasswordHistoryListByUserIDAndOrgID(Convert.ToInt32(GetPwd.OrgID.ToString()), Convert.ToInt32(GetPwd.UserID), Convert.ToInt32(countVal));

                            foreach (PasswordHistory passwordHistory in passwordHistoryList)
                            {
                                bool bSuccess = false;
                                if (Convert.ToInt32(countVal) > 0)
                                {
                                    if (passwordHistory.Password == forgetPassword.NewPassword)
                                    {
                                        IsPasswordChanged = false;
                                        ErrorMessege = "New password can not be same as your last" + countVal + "Password(s).";

                                        //if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                                        //{
                                        //    ErrorMessege = "New password can not be same as your last" + countVal + "Password(s).";
                                        //   // return Json(new { success = bSuccess, message = bSuccess ? "Pasword Reset SuccessFully" : "New password can not be same as your last" + countVal + "Password(s)." });
                                        //}
                                    }
                                }
                            }
                            bool isUpdate = false;
                            if (!forgetPassword.UserName.Contains(UserName.ToString()))
                            {
                                isUpdate = _ProcessSrv.ManageUserResetPasswordByOLDPassword(GetPwd.Password, NewPassword, GetPwd.UserID.ToString(), DateTime.UtcNow);
                            }
                            else
                            {
                                IsPasswordChanged = false;
                                ErrorMessege = "User Name and Password can not be similar";
                                //if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                                //{
                                //    return Json(new { success = isUpdate, message = isUpdate ? "Pasword Reset SuccessFully" : "User Name and Password can not be similar" });
                                //}
                            }
                            if (isUpdate)
                            {
                                PasswordHistorySave(GetPwd.UserID.ToString(), GetPwd.OrgID.ToString(), forgetPassword);

                                HttpContext.Session.Clear();

                                IsPasswordChanged = true ;
                                //ErrorMessege = "Password Change failed.";
                                //if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                                //{
                                //    return Json(new { success = isUpdate, message = isUpdate ? "Pasword Changed SuccessFully" : "Password Change failed." });
                                //}
                            }
                        }
                        else
                        {
                            IsPasswordChanged = false;
                            ErrorMessege = "Password must contain: Minimum 8 and Maximum 12 characters atleast 1 UpperCase Alphabet, 1 LowerCase Alphabet, 1 Number and 1 Special Character in $@!%*?&";
                            //if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                            //{
                            //    return Json(new { success = false, message = true ? "Pasword Changed SuccessFully" : "Password must contain: Minimum 8 and Maximum 12 characters atleast 1 UpperCase Alphabet, 1 LowerCase Alphabet, 1 Number and 1 Special Character in $@!%*?&" });
                            //}
                        }
                    }
                    else
                    {
                        IsPasswordChanged = false;
                        ErrorMessege = "Password Length should be Greater than 7 Characters!!!";
                        //if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                        //{
                        //    return Json(new { success = false, message = true ? "Pasword Changed SuccessFully" : "Password Length should be Greater than 7 Characters!!!" });
                        //}
                    }
                }
                else
                {
                    IsPasswordChanged = false;
                    ErrorMessege = "New Password and Confirm Password Does Not Match !!!";
                    //if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    //{
                    //    return Json(new { success = false, message = true ? "Pasword Changed SuccessFully" : "New Password and Confirm Password Does Not Match !!!" });
                    //}
                }
            }
            else
            {
                IsPasswordChanged = false;
                ErrorMessege = "Enter Password !!!";

            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
                return Json(new { success = IsPasswordChanged, message = IsPasswordChanged ? "Pasword Changed SuccessFully" : ErrorMessege });
            
        }
        return RedirectToAction("Login", "Login", new { area = "" });
    }

    public void PasswordHistorySave(string userID, string orgID,ForgetPassword ForgetPassword)
    {
        try
        {
            PasswordHistory objPasswordHistory = new PasswordHistory();

            objPasswordHistory.UserID = userID;
            objPasswordHistory.Password = ForgetPassword.Password;
            objPasswordHistory.OrgID = orgID;

            var result = _ProcessSrv.PasswordHistorySave(objPasswordHistory);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
    public bool IsValid_Pwd(string ConfPwd)
    {
        try
        {
            //string reg="^[a-zA-Z0-9'@&#.\s]{7,10}$";
            //string reg="^(?=.*\d)(?=.*[a-z])[0-9a-zA-Z]{7,}$";
            //string reg1 ="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]{8,10}";
            //string reg2 = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]{8,}";
            //string reg4="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$";
            //string reg5="^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{8,}$";
            //string reg6="^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$";

            //string reg= "(?!^[0-9]*$)(?!^[a-zA-Z]*$)^([a-zA-Z0-9]{7,10})$";

            string reg = "(?!^[0-9]*$)(?!^[a-zA-Z]*$)^([a-zA-Z0-9@*_]{8,})$";
            if (Regex.IsMatch(ConfPwd, reg))
            {
                //Console.WriteLine("Email.");
                return true;
            }
            else
            {
                //Console.WriteLine("Not email.");
                return false;
            }

            //if (Regex.IsMatch(ConfPwd, "(?!^[0-9]*$)(?!^[a-zA-Z]*$)^([a-zA-Z0-9]{8,10})$"))
            //{
            //    return true;
            //}
            //else
            //{ return false; }
        }
        catch (FormatException)
        {
            return false;
        }
    }
}

