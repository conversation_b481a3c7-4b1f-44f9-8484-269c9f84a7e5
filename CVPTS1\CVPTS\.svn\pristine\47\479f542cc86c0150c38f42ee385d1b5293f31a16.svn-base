﻿@{
    ViewData["Title"] = "Widget List";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<link href="~/css/widgetlibrary.css" rel="stylesheet" />
<style>
    .card-design-none{
        min-height:224px;
    }
</style>
<div class="Page-Header header">
    <div class="d-flex align-items-center gap-3">
        <h6 class="Page-Title">Widget Library</h6>
        <div class="tab-design">
            <ul class="nav nav-tabs gap-3" id="pills-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active fw-normal" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Pre-Built</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link fw-normal" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Custom</button>
                </li>
            </ul>
        </div>
    </div>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="offcanvas" href="#offcanvasExample" aria-controls="offcanvasExample"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant">
    <div class="tab-content" id="pills-tabContent">
        <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0">
            <div class="row g-3">
                <div class="col-12 col-lg-4 col-xl-3 col-xxl-2">
                    <div class="card card-design-none">
                        <div class="card-header">
                            <h6>Business Processes BIA</h6>
                        </div>
                        <div class="card-body">
                            <div id="DepartmentBIA-PreBuilt"></div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-3 col-xl-2 col-xxl-1">
                    <div class="card card-design-none">
                        <div class="card-header">
                            <h6>OverAll KPI Status</h6>
                        </div>
                        <div class="card-body pt-0">
                            <div class="d-grid align-items-center justify-content-center">
                                <div class="donut-chart" style="--progress: 55;">
                                    <svg width="120" height="120" viewBox="0 0 120 120">
                                        <circle class="circle-bg" cx="60" cy="60" r="50" />
                                        <circle class="circle-progress " cx="60" cy="60" r="50" style="stroke:#d51ba1;" />
                                    </svg>
                                    <div class="percentage">60%</div>
                                </div>
                                <div class="d-grid text-center">
                                    <span class="fw-normal">
                                        <span class="fs-4">60%</span>
                                    </span>
                                    <span class="text-secondary">InProgress</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-3 col-xl-3 col-xxl-2">
                    <div class="card card-design-none">
                        <div class="card-header">
                            <h6>Review Progress BIA</h6>
                        </div>
                        <div class="card-body pt-0">
                            <ul class="list-group list-group-flush d-grid h-100">
                                <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                    <div class="d-flex gap-2">
                                        <i class="cv-dot fs-5" style="color:#2315a3;"></i>
                                        <div class="text-secondary">Business Progress</div>
                                    </div>
                                    <div class="d-flex flex-fill align-items-center gap-3">
                                        <div class="fw-semibold">5 / 10</div>
                                        <div class="flex-fill">
                                            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 20%; background-color:#2315a3;"></div>
                                            </div>
                                        </div>
                                        <div class="fw-normal">20%</div>
                                    </div>
                                </li>
                                <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                    <div class="d-flex gap-2">
                                        <i class="cv-dot fs-5" style="color:#cd086a;"></i>
                                        <div class="text-secondary">Recovery Plan</div>
                                    </div>
                                    <div class="d-flex flex-fill align-items-center gap-3">
                                        <div class="fw-semibold">5 / 10</div>
                                        <div class="flex-fill">
                                            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 50%; background-color:#cd086a;"></div>
                                            </div>
                                        </div>
                                        <div class="fw-normal">50%</div>
                                    </div>
                                </li>
                                <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                    <div class="d-flex gap-2">
                                        <i class="cv-dot fs-5" style="color:#880ba7;"></i>
                                        <div class="text-secondary">Risk Assessment</div>
                                    </div>
                                    <div class="d-flex flex-fill align-items-center gap-3">
                                        <div class="fw-semibold">8 / 10</div>
                                        <div class="flex-fill">
                                            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 80%; background-color:#880ba7;"></div>
                                            </div>
                                        </div>
                                        <div class="fw-normal">80%</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-4 col-xl-4 col-xxl-3">
                    <div class="card card-design-none">
                        <div class="card-header header border-0">
                            <form class="d-flex tab-design w-100">
                                <ul class="nav nav-tabs nav-justified w-100" id="myTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="incidentNotificationTab" data-bs-toggle="tab" type="button" role="tab" aria-selected="true">
                                            <h6 class="page_title d-flex align-items-center mb-0">
                                                <span>Incident Summary</span>
                                            </h6>
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="teamNotificationTab" data-bs-toggle="tab" type="button" role="tab" aria-selected="false" tabindex="-1">
                                            <h6 class="page_title d-flex align-items-center mb-0">
                                                <span>Team Notification</span>
                                            </h6>
                                        </button>
                                    </li>
                                </ul>
                            </form>
                        </div>
                        <div class="card-body pt-0 " id="notificationBlock">
                            <div class="d-flex align-items-center justify-content-between mt-3">
                                <div class="d-flex align-items-center gap-2">
                                    <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                        <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                    </div>
                                    <div class="d-grid">
                                        <span class="fw-medium">fdgdg</span>
                                        <small class="text-secondary">Notified by David Brown on 27-07-2025 16:47:00</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center gap-3">
                                    <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="47">
                                        <i class="cv-timeline1 fs-4 text-primary"></i>
                                    </span>
                                    <span class="btnReport" data-incident-id="47" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center justify-content-between mt-3">
                                <div class="d-flex align-items-center gap-2">
                                    <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                        <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                    </div>
                                    <div class="d-grid">
                                        <span class="fw-medium">fdgdg</span>
                                        <small class="text-secondary">Notified by David Brown on 27-07-2025 14:59:00</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center gap-3">
                                    <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="49">
                                        <i class="cv-timeline1 fs-4 text-primary"></i>
                                    </span>
                                    <span class="btnReport" data-incident-id="49" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                </div>
                            </div>

                            <div class="d-flex align-items-center justify-content-between mt-3">
                                <div class="d-flex align-items-center gap-2">
                                    <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                        <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                    </div>
                                    <div class="d-grid">
                                        <span class="fw-medium">Plan flood for testing</span>
                                        <small class="text-secondary">Notified by David Brown on 25-07-2025 14:57:00</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center gap-3">
                                    <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="48">
                                        <i class="cv-timeline1 fs-4 text-primary"></i>
                                    </span>
                                    <span class="btnReport" data-incident-id="48" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-7 col-xl-6 col-xxl-4">
                    <div class="card card-design-none">
                        <div class="card-header header">
                            <h6>Business Entities Summary</h6>
                        </div>
                        <div class="card-body p-0 align-content-center">
                            <ul class="list-group list-group-horizontal">
                                <li class="list-group-item bg-white flex-fill border-0 border-end">
                                    <div class="d-grid">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <span class="fs-6">Risk Assessment</span>
                                            <span class="shadow-sm rounded-2 icon-circle" style="background-color:#2315a3;">
                                                <i class="cv-initiated-2 text-white fs-4" style="line-height: 1.2;"></i>
                                            </span>
                                        </div>
                                        <span class="fw-normal my-1">
                                            <span class="fs-4">
                                                16
                                            </span>
                                        </span>
                                        <span class="text-secondary">
                                            <a href="/BCMRiskAssessment/ManageRisk/ManageRisk?upcoming=1" style="text-decoration: underline; color: inherit;">
                                                <span class="fw-semibold text-primary">0</span>
                                                BCM Risk(s) has been
                                                <span class="text-secondary">coming for review within next 7 days</span>
                                            </a>
                                        </span>
                                        <span class="text-secondary">
                                            <a href="/BCMRiskAssessment/ManageRisk/ManageRisk?past=1" style="text-decoration: underline; color: inherit;">
                                                <span class="fw-semibold text-primary">14</span>
                                                BCM Risk
                                                <span class="text-secondary">item(s) past review date</span>
                                            </a>
                                        </span>
                                    </div>
                                </li>
                                <li class="list-group-item bg-white flex-fill border-0">
                                    <div class="d-grid">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <span class="fs-6">Recovery Plan</span>
                                            <span class="shadow-sm rounded-2 icon-circle" style="background-color:#880ba7;">
                                                <i class="cv-waiting-2 text-white fs-4" style="line-height: 1.2;"></i>
                                            </span>
                                        </div>
                                        <span class="fw-normal my-1">
                                            <span class="fs-4">
                                                19
                                            </span>
                                        </span>
                                        <a href="/BCMFunctionRecoveryPlan/ManageRecoveryPlans/ManageRecoveryPlans?upcoming=1" style="text-decoration: underline; color: inherit;">
                                            <span class="text-secondary"><span class="fw-semibold text-primary">1</span> Recovery Plan(s) has been</span>
                                            <span class="text-secondary">coming for review within next 7 days</span>
                                        </a>

                                        <a href="/BCMFunctionRecoveryPlan/ManageRecoveryPlans/ManageRecoveryPlans?past=1" style="text-decoration: underline; color: inherit;">
                                            <span class="text-secondary"><span class="fw-semibold text-primary">13</span> Recovery Plan(s)</span>
                                            <span class="text-secondary">past review date</span>
                                        </a>
                                    </div>
                                </li>
                                <li class="list-group-item bg-white flex-fill border-0 border-start">
                                    <div class="d-grid">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <span class="fs-6">Vendor</span>
                                            <span class="shadow-sm rounded-2 icon-circle" style="background-color:#cd086a;">
                                                <i class="cv-approved text-white fs-4" style="line-height: 1.2;"></i>
                                            </span>
                                        </div>
                                        <span class="fw-normal my-1">
                                            <span class="fs-4">
                                                21
                                            </span>
                                        </span>
                                        <span class="text-secondary">
                                            <a href="/BCMThirdParty/ManageVendor/ManageVendor?upcoming=1" style="text-decoration: underline; color: inherit;">
                                                <span class="fw-semibold text-primary">0</span>
                                                Vendor(s) contract

                                                <span class="text-secondary">has been expiring in next 7 days</span>
                                            </a>
                                        </span>
                                        <span class="text-secondary">
                                            <a href="/BCMThirdParty/ManageVendor/ManageVendor?past=1" style="text-decoration: underline; color: inherit;">
                                                <span class="fw-semibold text-primary">6</span>
                                                Vendor(s) contract

                                                <span class="text-secondary">is expired in past days</span>
                                            </a>
                                        </span>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-4 col-xl-3 col-xxl-2">
                    <div class="card card-design-none">
                        <div class="card-header header">
                            <h6 class="page_title d-flex align-items-center ">
                                <span>My Task List</span>
                            </h6>
                        </div>
                        <div class="card-body pt-0" style="max-height: 188px; overflow-y:auto;">
                            <div class="mb-3">
                                <div class="d-flex align-items-start justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#2315a3;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold" style="font-size:large;">0 </span>
                                                <span style="padding-left: 8px !important;">
                                                    Processes Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="TakeActionModal" role="button">
                                        <i class="cv-action fs-4 text-primary"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#880ba7;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold" style="font-size:large;">1</span>
                                                <span style="padding-left: 8px !important;">
                                                    Recovery Plan Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span role="button" class="TakeActionModal" data-id="57" data-status="1">
                                        <i class="cv-action fs-4 text-primary"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#cd086a;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold" style="font-size:large;">1</span> <span style="padding-left: 8px !important;">
                                                    Risk Assessments Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span role="button" class="TakeActionModal"><i class="cv-action fs-4 text-primary"></i></span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#cd086a;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold" style="font-size:large;">1</span> <span style="padding-left: 8px !important;">
                                                    Risk Assessments Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span role="button" class="TakeActionModal"><i class="cv-action fs-4 text-primary"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-5 col-xl-3 col-xxl-3">
                    <div class="card card-design-none">
                        <div class="card-header header">
                            <h6 class="page_title d-flex align-items-center mb-0">
                                <span>To Do List</span>
                            </h6>
                            <div class="d-none gap-2">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button">
                                        Action with selected
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="actionWithSelected">
                                        <li>
                                            <a class="dropdown-item todo-action" data-action="done" href="#">Done</a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item todo-action" data-action="archive" href="#">
                                                Add
                                                to archive
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item todo-action" data-action="remove" href="#">Remove</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary active" data-filter="all">
                                        All
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-filter="done">
                                        Done
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-filter="archived">
                                        Archived
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="todo-list-container">
                                <ul class="list-group list-group-flush todo-list-container" id="todoListItems">
                                    <li class="list-group-item d-flex align-items-center border-0 bg-transparent">
                                        <img class="me-2" src="/img/Profile-img/Usericon.svg" height="30">
                                        <div>
                                            <div class="fw-semibold">this is another TODO list for archive</div>
                                            <div class="text-primary">Created on  <small class="text-black-50">11-06-2025 16:21:34</small></div>
                                        </div>
                                        <span class="ms-auto edit-todo-btn d-none" role="button" data-id="10" data-description="this is another TODO list for archive">
                                            <i class="cv-edit fs-4 text-primary"></i>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex align-items-center border-0 bg-transparent">
                                        <img class="me-2" src="/img/Profile-img/Usericon.svg" height="30">
                                        <div>
                                            <div class="fw-semibold">this is another to list for done 101</div>
                                            <div class="text-primary">Created on  <small class="text-black-50">11-06-2025 16:21:06</small></div>
                                        </div>
                                        <span class="ms-auto edit-todo-btn d-none" role="button" data-id="9" data-description="this is another to list for done 101">
                                            <i class="cv-edit fs-4 text-primary"></i>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex align-items-center border-0 bg-transparent">
                                        <img class="me-2" src="/img/Profile-img/Usericon.svg" height="30">
                                        <div>
                                            <div class="fw-semibold">this to list for archive</div>
                                            <div class="text-primary">Created on  <small class="text-black-50">11-06-2025 15:51:08</small></div>
                                        </div>
                                        <span class="ms-auto edit-todo-btn d-none" role="button" data-id="7" data-description="this to list for archive">
                                            <i class="cv-edit fs-4 text-primary"></i>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent p-2 d-none">
                            <div class="d-flex justify-content-end">
                                <form id="todoForm" class="d-flex gap-2 mb-3">
                                    <input type="text" class="form-control" id="todoInput" placeholder="Add new task...">
                                    <button type="submit" class="btn btn-primary" id="addTodoBtn">Add</button>
                                    <button type="button" class="btn btn-secondary" id="cancelEditBtn" style="display: none;">
                                        Cancel
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-7 col-xl-6 col-xxl-3">
                    <div class="card card-design-none">
                        <div class="card-header">
                            <h6>Organization Summary</h6>
                        </div>
                        <div class="card-body pt-0">
                            <div class="row g-3">
                                <div class="col-auto col-xxl-3 border-end">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#f7e9fa">
                                        <i class="cv-unit align-middle fs-4" style="color:#ae1fcd;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Business&nbsp;Unit</div>
                                    <h4 class="fw-normal mb-0">9</h4><span class="text-success"></span>
                                </div>
                                <div class="col col-xxl-3 border-end">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#f5f4fd">
                                        <i class="cv-department align-middle fs-4" style="color:#6645e9;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Department</div>
                                    <h4 class="fw-normal mb-0">14</h4><span class="text-success"></span>
                                </div>
                                <div class="col col-xxl-3 border-end">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#efe7f6">
                                        <i class="cv-subdepartment align-middle fs-4" style="color:#640aa8;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Sub&nbsp;Department</div>
                                    <h4 class="fw-normal mb-0">8</h4><span class="text-success"></span>
                                </div>
                                <div class="col col-xxl-3">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#f9e9f4">
                                        <i class="cv-business-process fs-4 align-middle" style="color:#c8238e;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Business&nbsp;Process</div>
                                    <h4 class="fw-normal mb-0">29</h4><span class="text-success"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-4 col-xl-3 col-xxl-2">
                    <div class="card card-design-none">
                        <div class="card-header">
                            <h6>Critical &amp; Non-Critical Business</h6>
                        </div>
                        <div class="card-body pt-0">
                            <div class="card-group gap-3">
                                <div class="card w-50 text-center rounded-2 bg-light border-light-subtle" style="box-shadow:none !important;">
                                    <div class="card-header bg-transparent border-0 pt-3">
                                        <span class="shadow-sm rounded-2 icon-circle mx-auto" style="background-color:#2315a3;">
                                            <i class="cv-success1 text-white fs-4" style="line-height: 1.2;"></i>
                                        </span>
                                    </div>
                                    <div class="card-body pt-1">
                                        <div class="fs-4">26</div>
                                        <span class="Sub-Title">Non Critical</span>
                                    </div>
                                </div>
                                <div class="card w-50 text-center rounded-2 bg-light border-light-subtle" style="box-shadow:none !important;">
                                    <div class="card-header bg-transparent border-0 pt-3">
                                        <span class="shadow-sm rounded-2 icon-circle mx-auto" style="background-color:#cd086a;">
                                            <i class="cv-critical-entities text-white fs-4" style="line-height: 1.2;"></i>
                                        </span>
                                    </div>
                                    <div class="card-body pt-1">
                                        <div class="fs-4">3</div>
                                        <span class="Sub-Title">Critical</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-4 col-xl-3 col-xxl-2">
                    <div class="card card-design-none">
                        <div class="card-header border-0 pb-0">
                            <h6 class="page_title d-flex align-items-center ">
                                <span>Approvals Pending</span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush ">
                                <li role="button" data-bs-target="#TakeActionModal" data-id="1" data-status="1" class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                    <div class="d-flex fw-semibold align-items-center">
                                        <i class="cv-business-process fs-6 icon-circle bg-warning-subtle text-warning"></i><span class="ms-2 fw-normal">Business Process</span>
                                    </div>
                                    <span class="fs-5">2</span>
                                </li>
                                <li role="button" data-bs-target="#TakeActionModal" data-id="57" data-status="1" class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                    <div class="d-flex fw-semibold align-items-center">
                                        <i class="cv-recovery-plan fs-6 icon-circle bg-info-subtle text-info"></i><span class="ms-2 fw-normal">Recovery Plan</span>
                                    </div>
                                    <span class="fs-5">1</span>
                                </li>
                                <li role="button" data-bs-target="#TakeActionModal" data-id="77" data-status="1" class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                    <span class="d-flex fw-semibold  align-items-center">
                                        <i class="cv-other-bcm-entities  icon-circle bg-danger-subtle text-danger-emphasis fs-6"></i>
                                        <span class="ms-2 fw-normal">BCM Risk</span>
                                    </span>
                                    <span class="fs-5">1</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 row-cols-xxl-4 g-3">
                <div class="col">
                    <div class="card card-design-none">
                        <div class="card-header header">
                            <h6>Pie Chart</h6>
                            <div class="btn-group btn-group-sm" role="group">
                                <button onclick="toggleLegend()" id="Pielegend" type="button" class="btn py-0 border-0">
                                    <i class="cv-sequence fs-6"></i>
                                </button>
                                <button onclick="toggleLabels()" id="Pielabels" type="button" class="btn py-0 border-0">
                                    <i class="cv-edit fs-6"></i>
                                </button>
                                <button onclick="toggleTicks()" id="Pieticks" type="button" class="btn py-0 border-0">
                                    <i class="cv-delete text-danger fs-6"></i>
                                </button>
                            </div>
                            <div class="chart-controls">
                                <button onclick="updateColor(0, '#ff0000')">Red for Lithuania</button>
                                <button onclick="updateColor(1, '#00ff00')">Green for Czechia</button>
                                <button onclick="updateColor(2, '#0000ff')">Blue for Ireland</button>
                                <button onclick="updateColor(3, '#ffff00')">Yellow for Germany</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="PieChart"></div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-design-none">
                        <div class="card-header header">
                            <h6>Donut chart</h6>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn py-0 border-0" onclick="toggleLegend()" id="legendBtn"><i class="cv-sequence fs-6"></i></button>
                                <button class="btn py-0 border-0" onclick="toggleLabels()" id="labelsBtn"><i class="cv-edit fs-6"></i></button>
                                <button class="btn py-0 border-0" onclick="toggleTicks()" id="ticksBtn"><i class="cv-delete text-danger fs-6"></i></button>
                            </div>
                            <div class="chart-controls">
                                <input type="color" id="color0" value="#09b96d" oninput="updateColor(0, this.value)">

                                <input type="color" id="color1" value="#ffcf27" oninput="updateColor(1, this.value)">

                                <input type="color" id="color2" value="#ff812e" oninput="updateColor(2, this.value)">
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="DonutChart"></div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-design-none">
                        <div class="card-header header">
                            <h6>Organisation Summary</h6>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn py-0 border-0">
                                    <i class="cv-sequence fs-6"></i>
                                </button>
                                <button type="button" class="btn py-0 border-0">
                                    <i class="cv-edit fs-6"></i>
                                </button>
                                <button type="button" class="btn py-0 border-0">
                                    <i class="cv-delete text-danger fs-6"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-design-none">
                        <div class="card-header header">
                            <h6>BCM Calendar</h6>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn py-0 border-0">
                                    <i class="cv-sequence fs-6"></i>
                                </button>
                                <button type="button" class="btn py-0 border-0">
                                    <i class="cv-edit fs-6"></i>
                                </button>
                                <button type="button" class="btn py-0 border-0">
                                    <i class="cv-delete text-danger fs-6"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-design-none">
                        <div class="card-header header">
                            <h6>Overall KPI Status</h6>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn py-0 border-0">
                                    <i class="cv-sequence fs-6"></i>
                                </button>
                                <button type="button" class="btn py-0 border-0">
                                    <i class="cv-edit fs-6"></i>
                                </button>
                                <button type="button" class="btn py-0 border-0">
                                    <i class="cv-delete text-danger fs-6"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasExample" aria-labelledby="offcanvasExampleLabel">
    <div class="offcanvas-header">
        <h6 class="Page-Title">Configure Widget</h6>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body pt-0">
        <div class="form-group field-touched">
            <label>Widget Name</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-name"></i></span>
                <input type="text" class="form-control" placeholder="Enter Widget Name">
            </div>
        </div>
        <div class="form-group field-touched">
            <label>Widget Type</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-calculated"></i></span>
                <select class="form-select form-control selectized">
                    <option selected>Select Widget Type</option>
                    <option>Chart</option>
                    <option>Table</option>
                </select>
            </div>
        </div>
        <div class="form-group field-touched">
            <label>Chart Type</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-calculated"></i></span>
                <select class="form-select form-control selectized">
                    <option selected>Select Chart</option>
                    <option>Donet Chart</option>
                    <option>Pie Chatrt</option>
                    <option>Line Chatrt</option>
                </select>
            </div>
        </div>
        <div class="form-group field-touched">
            <label>Widget Description</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-type"></i></span>
                <input type="text" class="form-control" placeholder="Enter Widget Type">
            </div>
        </div>
        <div class="form-group field-touched">
            <label>Dataset</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-calculated"></i></span>
                <select class="form-select form-control selectized">
                    <option selected>Select Dataset</option>
                    <option>Donet Chart</option>
                    <option>Pie Chatrt</option>
                    <option>Line Chatrt</option>
                </select>
            </div>
        </div>
        <div class="form-group d-grid">
            <label class="form-label">Chart Preview</label>
            <i class="cv-donut-chart"></i>
        </div>
        <div class="row">
            <div class="col-6">
                <div class="form-group field-touched">
                    <label>Field 1</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Value">
                        <span class="input-group-text py-0"><input type="color"/></span>
                    </div>
                </div>
                <div class="form-group field-touched">
                    <label>Field 2</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Value">
                        <span class="input-group-text py-0"><input type="color" /></span>
                    </div>
                </div>
                <div class="form-group field-touched">
                    <label>Field 3</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Value">
                        <span class="input-group-text py-0"><input type="color" /></span>
                    </div>
                </div>
                <div class="form-group field-touched">
                    <label>Field 4</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Value">
                        <span class="input-group-text py-0"><input type="color" /></span>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="form-group field-touched">
                    <label>Data</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Data">
                    </div>
                </div>
                <div class="form-group field-touched">
                    <label>Data</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Data">
                    </div>
                </div>
                <div class="form-group field-touched">
                    <label>Data</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Data">
                    </div>
                </div>
                <div class="form-group field-touched">
                    <label>Data</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Data">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script src="~/lib/jquery/jquery.min.js"></script>

@* Chart Library *@
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>

@* All PreBuild Charts & Js *@
<script src="~/js/widgetlibrarycharts/pre-built/departmentbia-prebuilt.js"></script>
<script src="~/js/widgetlibrarycharts/pre-built/PreBuiltCalendar.js"></script>

@* All Custom Charts *@
<script src="~/js/widgetlibrarycharts/custom-built/piechart.js"></script>
<script src="~/js/widgetlibrarycharts/custom-built/donutchart.js"></script>

