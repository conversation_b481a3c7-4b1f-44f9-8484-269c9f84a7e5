﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using BCM.Shared
@model IEnumerable<BCM.BusinessClasses.Facility>
@{
    ViewBag.Title = "BCM Facility";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .home-section {
        height: auto !important;
    }
</style>

<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">BCM Facility</h6>
        <div class="d-flex gap-3 justify-content-end align-items-end">
            <div class="vendor-section my-0">
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle primary"><i class="cv-all-facilities fs-6"></i></span><span>Total Facilitites</span><span class="count-primary">@ViewBag.TotalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle bg-white"><i class="cv-all-facilities fs-6 text-primary"></i></span><span>Facilitites Under BCM Scope</span><span class="text-primary">@ViewBag.UnderBCMCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle warning"><i class="cv-warning fs-6"></i></span><span>Critical Facilitites</span><span class="count-warning">@ViewBag.CriticalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle success"><i class="cv-success1 fs-6"></i></span><span>Non Critical Facilitites</span><span class="count-success">@ViewBag.NonCriticalCount</span>
                </div>
            </div>
            <div class="dropdown">
                <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                    <i class="cv-filter align-middle"></i>
                </button>
                <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                    <div class="mb-3">
                        <label>Organizations</label>
                        <div class="input-group">
                            @{
                                string SelectedOrgID = Convert.ToString(ViewBag.SelectedOrgID);
                                string SelectedUnitID = Convert.ToString(ViewBag.SelectedUnitID);
                                string SelectedDepartmentID = Convert.ToString(ViewBag.SelectedDepartmentID);
                                string SelectedSubDepartmentID = Convert.ToString(ViewBag.SelectedSubDepartmentID);
                            }
                            <span class="input-group-text py-1"><i class="cv-organization-group-name"></i></span>
                            <select class="form-select form-control selectized ddlOrganization" autocomplete="off" asp-for="@SelectedOrgID" id="ddlOrganization" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.OrgInfo,"Id","OrganizationName"))">
                                <option selected value="0">-- Select Organizations --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Units</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                            <select class="form-select form-control selectized ddlUnit" autocomplete="off" id="ddlUnit" asp-for="@SelectedUnitID" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Unit,"UnitID","UnitName"))">
                                <option selected value="0">-- Select Units --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-department"></i></span>
                            <select class="form-select form-control selectized ddlDepartment" autocomplete="off" id="ddlDepartment" asp-for="@SelectedDepartmentID" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Department,"DepartmentID","DepartmentName"))">
                                <option selected value="0">-- select Departments --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>SubDepartments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-subdepartment-name"></i></span>
                            <select class="form-select form-control selectized ddlSubDepartment" autocomplete="off" id="ddlSubDepartment" asp-for="@SelectedSubDepartmentID" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.SubDepartment,"SubFunctionID","SubFunctionName"))">
                                <option selected value="0">-- Select SubDepartments --</option>
                            </select>
                        </div>
                    </div>
                    @* <div class="text-end">
                        <button type="submit" class="btn btn-sm btn-primary">Search</button>
                    </div> *@
                </form>
            </div>
            <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal" aria-controls="offcanvasExample"> <i class="cv-Plus"></i>Create</button>
        </div>
    </div>
</div>

<div class="Page-Condant card border-0" id="FacilityList">
    <div class="card">
        <div class="card-body py-0">
            <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Facility Name</th>
                        <th>Owner Details</th>
                        <th>Unit</th>
                        <th>Department</th>
                        <th>Sub Department</th>
                        <th style="display:none">Org Level</th>
                        @* <th>RTO / MAO / RPO</th> *@
                        <th>RTO</th>
                        <th>IsCritical</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="tblBody">
                    @await Html.PartialAsync("_FilteredFacility")
                </tbody>
            </table>
        </div>
    </div>
</div>

@* Configure Facility Modal Start *@
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title"> Facility Configuration</h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-data">
            </div>
        </div>
    </div>
</div>
@* Configure Facility Modal End *@

<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>

@* BCM Entity Summary Modal Start *@
<div class="modal fade" id="SummaryModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">BCM Entity Summary Site Cerebrum IT Park</h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="tab-design">
                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane" type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">
                                BCM Entity Configuration
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane" type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false" tabindex="-1">
                                BCM Entity BIA Overview
                            </button>
                        </li>

                    </ul>
                </div>
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade  show active" id="home-tab-pane" aria-labelledby="home-tab" role="tabpanel" tabindex="0">
                        <div class="d-flex justify-content-between gap-5 mb-2 p-3">
                            <div class="w-100">
                                <div>
                                    <table class="table table-borderless">
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <i class="cv-entity-name me-1"></i>Entity Name
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    Cerebrum IT Park ( PRC 2022 160 )
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-department-name me-1"></i>Department Name
                                                </td>
                                                <td>:</td>
                                                <td>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-approver me-1"></i>Approver
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    Anwar Chalamannil

                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-status me-1"></i>Status
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    Initiated
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-user me-1"></i>User MTPOD
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    Default BIA Templates
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-review-date me-1"></i>Review Date
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    05/01/2024
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="vr"></div>
                            <div class="w-100">
                                <div>
                                    <table class="table table-borderless">
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <i class="cv-organization me-1"></i>Organization
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    Perpetuuiti
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-subdepartment-name me-1"></i>Sub Department
                                                </td>
                                                <td>:</td>
                                                <td>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-rpo me-1"></i>RPO
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    10 Min(s)
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-RTO me-1"></i>Calculated RTO
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    NA
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-critical-entities me-1"></i>Is Critical
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    No
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-version me-1"></i>Version
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    1.0
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="vr"></div>
                            <div class="w-100">
                                <div>
                                    <table class="table table-borderless">
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <i class="cv-unit me-1"></i>Unit Name
                                                </td>
                                                <td>:</td>
                                                <td>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-owner me-1"></i>Owner
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    Arun Patil
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-calculated me-1"></i>Calculated MTPOD
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    NA
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-RTO me-1"></i>User RTO
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    NA
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <i class="cv-profile me-1"></i>Profile Name
                                                </td>
                                                <td>:</td>
                                                <td class="text-primary">
                                                    Default BIA Templates
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="profile-tab-pane" aria-labelledby="profile-tab" role="tabpanel" tabindex="0">
                        <div class="p-3">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <td>Sr.No</td>
                                        <td>BIA Section</td>
                                        <td>Version</td>
                                        <td>Version Change Description</td>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="4">
                                            <div class="text-center">
                                                <img src="/img/Isomatric/no_records_to_display.svg" class=" img-fluid" />
                                            </div>
                                        </td>


                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
@* BCM Entity Summary Modal End *@

@* Escalation Matrix for Review Modal Start *@
<div class="modal fade" id="EscalationModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Escalation Matrix for Review</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="tab-design">
                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="EscalationDetails-tab" data-bs-toggle="tab" data-bs-target="#EscalationDetails-tab-pane" type="button" role="tab" aria-controls="EscalationDetails-tab-pane" aria-selected="true">
                                Add Escalation Details
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="EscalationFYI-tab" data-bs-toggle="tab" data-bs-target="#EscalationFYI-tab-pane" type="button" role="tab" aria-controls="EscalationFYI-tab-pane" aria-selected="false" tabindex="-1">
                                Add Resources for Escalation FYI Mail
                            </button>
                        </li>

                    </ul>
                </div>
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade  show active" id="EscalationDetails-tab-pane" aria-labelledby="EscalationDetails-tab" role="tabpanel" tabindex="0">
                        <div class="d-flex justify-content-between gap-5 mb-2 p-3">

                            <div class="w-100">
                                <table class="table table-borderless">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <i class="cv-escalation me-1"></i>Escalation Matrix Name
                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option>Select Escalation Matrix</option>
                                                            <option>Cerebrum IT Park</option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Escalation Matrix Name</div>
                                                </div>
                                            </td>
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                            <div class="w-100">
                                <table class="table table-borderless">
                                    <tbody>

                                        <tr>
                                            <td>
                                                <i class="cv-holiday-date me-1"></i>Escalation Date
                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <input type="datetime-local" class="form-control" />
                                                    </div>
                                                    <div class="invalid-feedback">Escalation Date</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>


                    </div>
                    <div class="tab-pane fade" id="EscalationFYI-tab-pane" aria-labelledby="EscalationFYI-tab" role="tabpanel" tabindex="0">
                        <div class="row">
                            <h6 class="Sub-Title my-2">Add Users & Teams for FYI :</h6>
                            <div class="tab-design">
                                <ul class="nav nav-tabs" id="myuser" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="Members-tab" data-bs-toggle="tab" data-bs-target="#Members-tab-pane" type="button" role="tab" aria-controls="Members-tab-pane" aria-selected="true">
                                            Members
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="Teams-tab" data-bs-toggle="tab" data-bs-target="#Teams-tab-pane" type="button" role="tab" aria-controls="Teams-tab-pane" aria-selected="false" tabindex="-1">
                                            Teams
                                        </button>
                                    </li>

                                </ul>
                            </div>
                            <div class="tab-content" id="myuserContent">
                                <div class="tab-pane fade  show active" id="Members-tab-pane" aria-labelledby="Members-tab" role="tabpanel" tabindex="0">
                                    <div class="card">
                                        <div class="card-header border-0 d-flex align-items-center justify-content-between">
                                            <h6 class="Sub-Title">User List for FYI</h6>
                                            <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

                                                <div class="input-group Search-Input">
                                                    <span class="input-group-text py-1"><i class="cv-search"></i></span>
                                                    <input id="search-inp" type="text" class="form-control" placeholder="Search">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
                                                <thead>
                                                    <tr>
                                                        <td>Sr.No</td>
                                                        <td>Select</td>
                                                        <td>Resource Name</td>
                                                        <td>Org Level </td>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>01</td>
                                                        <td><input type="checkbox" class="form-check" /></td>
                                                        <td>
                                                            <div class="d-flex">
                                                                <div class="User-icon">
                                                                    <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                                                                </div>
                                                                <div>
                                                                    <ul class="ps-0 mb-0">
                                                                        <li class="list-group-item fw-semibold">Arun Patil</li>
                                                                        <li class="list-group-item"><a class="text-primary" href="#"><EMAIL></a></li>
                                                                        <li class="list-group-item">9988337711</li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <ul class="ps-0 mb-0">
                                                                <li class="list-group-item"><i class="cv-organization me-1"></i>Perpetuuiti</li>
                                                                <li class="list-group-item"><i class="cv-unit me-1"></i></li>
                                                                <li class="list-group-item"><i class="cv-subdepartment-name me-1"></i></li>


                                                            </ul>
                                                        </td>


                                                    </tr>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="Teams-tab-pane" aria-labelledby="Teams-tab" role="tabpanel" tabindex="0">
                                    <div class="card">
                                        <div class="card-header border-0 d-flex align-items-center justify-content-between">
                                            <h6 class="Sub-Title">Team List for FY</h6>
                                            <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

                                                <div class="input-group Search-Input">
                                                    <span class="input-group-text py-1"><i class="cv-search"></i></span>
                                                    <input id="search-inp" type="text" class="form-control" placeholder="Search">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
                                                <thead>
                                                    <tr>
                                                        <td>Sr.No</td>
                                                        <td>Select</td>
                                                        <td>Group Name</td>
                                                        <td>Belongs To</td>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>01</td>
                                                        <td><input type="checkbox" class="form-check" /></td>
                                                        <td>
                                                            <div class="d-flex">
                                                                <div class="User-icon">
                                                                    <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                                                                </div>
                                                                <div>
                                                                    <ul class="ps-0 mb-0">
                                                                        <li class="list-group-item fw-semibold">Arun Patil</li>
                                                                        <li class="list-group-item"><a class="text-primary" href="#"><EMAIL></a></li>

                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <ul class="ps-0 mb-0">
                                                                <li class="list-group-item"><i class="cv-unit me-1"></i>Finance affairs</li>
                                                                <li class="list-group-item"><i class="cv-subdepartment-name me-1"></i>Product Development</li>
                                                            </ul>
                                                        </td>


                                                    </tr>

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Attach Matrix Profile</button>
                </div>
            </div>
        </div>
    </div>
</div>
@* Escalation Matrix for Review Modal End *@

@* BIA Profile Modal Start *@
<div class="modal fade" id="BiaProfileModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">BIA Profile</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between gap-5 mb-2 p-3">
                    <div class="w-100">
                        <div>
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td>
                                            <i class="cv-organization me-1"></i>Organization
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            New_BIA  Cerebrum IT Park ( PRC 2022 160 )
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <i class="cv-approver me-1"></i>Approver
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            Anwar Chalamannil

                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="vr"></div>
                    <div class="w-100">
                        <div>
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td>
                                            <i class="cv-organization me-1"></i>Organization
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            Perpetuuiti
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <i class="cv-owner me-1"></i>Owner
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            Irene Solomon

                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
                <div class="p-3">
                    <table class="table">
                        <thead>
                            <tr>
                                <td>Sr.No</td>
                                <td>Section</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="2">
                                    <div class="text-center">
                                        <img src="/img/Isomatric/no_records_to_display.svg" class=" img-fluid" />
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="my-2">
                        <div class="d-flex align-items-center gap-3 my-2">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#ImpactTypeName" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                            <h6 class="mb-0">ImpactTypeName: Financial Impact</h6>
                        </div>
                        <div class="collapse" id="ImpactTypeName">
                            <p class="text-primary">Competitive advantage</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Finish</button>
                </div>
            </div>
        </div>
    </div>
</div>
@* BIA Profile  Modal End *@

@* Change RTO Modal Start *@
<div class="modal fade" id="ChangeRTOModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">
                    Change RTO
                </h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between gap-5 mb-2 p-3">
                    <div class="w-100">
                        <div>
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td>
                                            <i class="cv-organization me-1"></i>Process Name
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            Cerebrum IT Park
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <i class="cv-approver me-1"></i>Calculated RTO
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            NA
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-approver me-1"></i>Calculated MAO
                                        </td>
                                        <td>:</td>
                                        <td class="text-primary">
                                            NA
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="w-100">
                        <div>
                            <label class="form-label">Owner Defined RTO</label>
                            <div class="d-flex gap-2 align-items-center">
                                <div class="form-group">

                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-owner"></i></span>
                                        <input type="text" class="form-control" placeholder="0">
                                    </div>
                                    <div class="invalid-feedback">Owner Defined RTO</div>
                                </div>
                                <div class="form-group w-50">
                                    <div class="input-group">
                                        <select class="form-select form-select-sm">
                                            <option value="value">10</option>
                                            <option value="value">20</option>
                                            <option value="value">30</option>
                                            <option value="value">40</option>
                                        </select>
                                    </div>
                                    <div class="invalid-feedback">MINS</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="form-label">Owner Defined MAO</label>
                            <div class="d-flex gap-2 align-items-center">

                                <div class="form-group">

                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-owner"></i></span>
                                        <input type="text" class="form-control" placeholder="0">
                                    </div>
                                    <div class="invalid-feedback">Owner Defined MAO</div>
                                </div>
                                <div class="form-group w-50">
                                    <div class="input-group">
                                        <select class="form-select form-select-sm">
                                            <option value="value">10</option>
                                            <option value="value">20</option>
                                            <option value="value">30</option>
                                            <option value="value">40</option>
                                        </select>
                                    </div>
                                    <div class="invalid-feedback">MINS</div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
                <div class="p-3">
                    <table class="table">
                        <thead>
                            <tr>
                                <td>Sr.No</td>
                                <td>Section</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="2">
                                    <div class="text-center">
                                        <img src="/img/Isomatric/delete.svg" class=" img-fluid" />
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="my-2">
                        <div class="d-flex align-items-center gap-3 my-2">
                            <button class="btn btn-sm btn-primary rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#ImpactTypeName" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></button>
                            <h6 class="mb-0">ImpactTypeName: Financial Impact</h6>
                        </div>
                        <div class="collapse" id="ImpactTypeName">
                            <p class="text-primary">Competitive advantage</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Finish</button>
                </div>
            </div>
        </div>
    </div>
</div>
@* Change RTO Modal End *@
<!-- Configuration offcanvas -->
<div class="offcanvas offcanvas-end w-100" tabindex="-1" id="offcanvasExample" aria-labelledby="offcanvasExampleLabel">
    <div class="offcanvas-header">
        <h6 class="mb-0">Configure BCM Entities (Cerebrum IT Park)</h6>
        <button type="button" class="btn-close Closebtn" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <div class="card mt-2">
            <div class="card-body">
                <div class="d-flex justify-content-between gap-5 mb-2 p-3">
                    <div class="w-75">
                        <div>
                            <table class="w-100">
                                <tbody>
                                    <tr>
                                        <td>
                                            <i class="cv-version me-1"></i>Version
                                        </td>
                                        <td>:</td>
                                        <td>
                                            1.0
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-organization me-1"></i>Organization
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-select-sm">
                                                        <option>Perpetuuiti</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">Select Organization</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-department me-1"></i>Department
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-select-sm">
                                                        <option>Perpetuuiti</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">Select Department</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-entity-type me-1"></i>BCM Entity Type
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-select-sm">
                                                        <option>Perpetuuiti</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">Select BCM Entity Type</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-approver me-1"></i>Select Approver
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <div class="form-group w-100">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option>Anwar Chalamannil</option>
                                                        </select>

                                                    </div>
                                                    <div class="invalid-feedback">Select Approver</div>
                                                </div>
                                                <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#EscalationModal"><i class="cv-escalation"></i></button>
                                            </div>
                                        </td>

                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-user-profile me-1"></i>Select BIA Profile
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <div class="form-group w-100">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option>New_BIA</option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Select BIA Profile</div>
                                                </div>
                                                <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#BiaProfileModal"><i class="cv-profile-details"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="vr"></div>
                    <div class="w-75">
                        <div>


                            <table class="w-100">
                                <tbody>
                                    <tr>
                                        <td>
                                            <i class="cv-process-code me-1"></i>Process Code
                                        </td>
                                        <td>:</td>
                                        <td>
                                            PRC 2022 160
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-unit me-1"></i>Unit
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-select-sm">
                                                        <option>All Units</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">All Units</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-subdepartment me-1"></i>SubDepartment
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-select-sm">
                                                        <option>All SubDepartment</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">Select SubDepartment</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-business-process me-1"></i>Business Process
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-select-sm">
                                                        <option>Cerebrum IT Park</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">Select Business Process</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <i class="cv-rpo me-1"></i>RPO
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-select-sm">
                                                        <option>Anwar Chalamannil</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">Select RPO</div>
                                            </div>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Entity Description</label>
                    <div class="input-group">
                        <textarea class="form-control" placeholder="NA"></textarea>

                    </div>
                    <div class="invalid-feedback">Entity Description</div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-lg-6 col-sm-12">
                        <div>
                            <table class="w-100">
                                <tbody>

                                    <tr>
                                        <td style="width:42%">
                                            <i class="cv-transaction-volume me-1"></i>Total Transaction Volume
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-select-sm">
                                                        <option>Cerebrum IT Park</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">Select Site</div>
                                            </div>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="col-md-6 col-lg-6 col-sm-12">
                        <div>


                            <table class="w-100">
                                <tbody>

                                    <tr>
                                        <td style="width:42%">
                                            <i class="cv-team-size me-1"></i>Team Size
                                        </td>
                                        <td>:</td>
                                        <td>
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <select class="form-select form-select-sm">
                                                        <option>Cerebrum IT Park</option>
                                                    </select>
                                                </div>
                                                <div class="invalid-feedback">Select Team Size</div>
                                            </div>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="d-flex align-items-center gap-1 mb-2">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                            <h6 class="mb-0">Owner Details</h6>
                        </div>
                        <div class="row collapse p-3" id="collapseExample">
                            <div class="col-md-6 col-lg-6 col-sm-12 ">
                                <table class="w-100">
                                    <tbody>
                                        <tr>
                                            <td style="width:42%">
                                                <i class="cv-owner me-1"></i>Select Owner

                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option>Arun Patil</option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Select Owner</div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width:42%">
                                                <i class="cv-mail me-1"></i>Select Email

                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option><EMAIL></option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Select Email</div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width:42%">
                                                <i class="cv-Mobile me-1"></i>Select Mobile

                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option>Mobile</option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Select Mobile</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-md-6 col-lg-6 col-sm-12">
                                <table class="w-100">
                                    <tbody>
                                        <tr>
                                            <td style="width:42%">
                                                <i class="cv-owner me-1"></i>Select Alternate Owne

                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option>Arun Patil</option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Select Alternate Owne</div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width:42%">
                                                <i class="cv-mail me-1"></i>Select Email

                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option><EMAIL></option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Select Email</div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width:42%">
                                                <i class="cv-Mobile me-1"></i>Select Mobile

                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option>Mobile</option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Select Mobile</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-1 my-2">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#profile-details" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                            <h6 class="mb-0">Business Parameter Profile Details </h6>
                        </div>
                        <div class="row collapse p-3" id="profile-details">
                            <div class="col-md-6 col-lg-6 col-sm-12">
                                <table class="w-100">
                                    <tbody>
                                        <tr>
                                            <td style="width:42%">
                                                <i class="cv-profile me-1"></i>Business Parameter Profile

                                            </td>
                                            <td>:</td>
                                            <td>
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <select class="form-select form-select-sm">
                                                            <option>Arun Patil</option>
                                                        </select>
                                                    </div>
                                                    <div class="invalid-feedback">Business Parameter Profile</div>
                                                </div>
                                            </td>
                                        </tr>

                                    </tbody>
                                </table>

                            </div>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <td>Sr.No</td>
                                        <td>Parameter Name</td>
                                        <td>Parameter Type</td>
                                        <td>Monitoring Enabled</td>
                                        <td>Active</td>
                                        <td>Schedule Time</td>
                                        <td>Edit/View</td>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>

                                        <td>01</td>
                                        <td>
                                            Move resource to other EGS location
                                        </td>
                                        <td>
                                            Object
                                        </td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <a href="#"><i class="cv-edit"></i></a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>02</td>
                                        <td>
                                            Move resource to other EGS location
                                        </td>
                                        <td>
                                            Sensitivity
                                        </td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <a href="#"><i class="cv-edit"></i></a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>03</td>
                                        <td>
                                            People WFH
                                        </td>
                                        <td>
                                            Sensitivity
                                        </td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <a href="#"><i class="cv-edit"></i></a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>04</td>
                                        <td>
                                            Increasing shift time at DR Site
                                        </td>
                                        <td>
                                            Object
                                        </td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <a href="#"><i class="cv-edit"></i></a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex align-items-center gap-1 my-2">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#Process-Review" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                            <h6 class="mb-0">Process  Review Section </h6>
                        </div>
                        <div class="row collapse p-3" id="Process-Review">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <span><i class="cv-calendar me-1"></i>Last Review Date :</span><span class="ms-1 text-primary">10/05/2024</span>
                                </div> <div class="d-flex align-items-center">
                                    <span><i class="cv-calendar me-1"></i>Next Review Date :</span><span class="ms-1">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <input type="date" class="form-control" />
                                            </div>
                                            <div class="invalid-feedback">Business Parameter Profile</div>
                                        </div>
                                    </span>
                                </div>
                            </div>

                            <table class="table table-striped">
                                <thead>
                                    <tr>

                                        <td>Cycle</td>
                                        <td>Start Date</td>
                                        <td>End Date</td>
                                        <td>Status</td>
                                        <td>Next Review Date</td>
                                        <td>Reviewer Name</td>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>

                                        <td>01</td>
                                        <td>
                                            10/05/2024
                                        </td>
                                        <td>
                                            -
                                        </td>
                                        <td>In Progress</td>
                                        <td>18/08/2022</td>
                                        <td>
                                            <div class="d-flex">
                                                <div class="User-icon">
                                                    <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">

                                                    <div class="d-flex align-items-center gap-1 my-2">
                                                        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#profile-details" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                                                        <h6 class="mb-0">Business Parameter Profile Details </h6>
                                                    </div>
                                                    <div class="row collapse p-3" id="profile-details">
                                                        <div class="col-md-6 col-lg-6 col-sm-12">
                                                            <table class="w-100">
                                                                <tbody>
                                                                    <tr>
                                                                        <td style="width:42%">
                                                                            <i class="cv-profile me-1"></i>Business Parameter Profile

                                                                        </td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            <div class="form-group">
                                                                                <div class="input-group">
                                                                                    <select class="form-select form-select-sm">
                                                                                        <option>Arun Patil</option>
                                                                                    </select>
                                                                                </div>
                                                                                <div class="invalid-feedback">Business Parameter Profile</div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>

                                                                </tbody>
                                                            </table>

                                                        </div>
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <td>Sr.No</td>
                                                                    <td>Parameter Name</td>
                                                                    <td>Parameter Type</td>
                                                                    <td>Monitoring Enabled</td>
                                                                    <td>Active</td>
                                                                    <td>Schedule Time</td>
                                                                    <td>Edit/View</td>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>

                                                                    <td>01</td>
                                                                    <td>
                                                                        Move resource to other EGS location
                                                                    </td>
                                                                    <td>
                                                                        Object
                                                                    </td>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td>
                                                                        <a href="#"><i class="cv-edit"></i></a>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>02</td>
                                                                    <td>
                                                                        Move resource to other EGS location
                                                                    </td>
                                                                    <td>
                                                                        Sensitivity
                                                                    </td>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td>
                                                                        <a href="#"><i class="cv-edit"></i></a>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>03</td>
                                                                    <td>
                                                                        People WFH
                                                                    </td>
                                                                    <td>
                                                                        Sensitivity
                                                                    </td>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td>
                                                                        <a href="#"><i class="cv-edit"></i></a>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>04</td>
                                                                    <td>
                                                                        Increasing shift time at DR Site
                                                                    </td>
                                                                    <td>
                                                                        Object
                                                                    </td>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td></td>
                                                                    <td>
                                                                        <a href="#"><i class="cv-edit"></i></a>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <div class="d-flex align-items-center gap-1 my-2">
                                                        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#Process-Review" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                                                        <h6 class="mb-0">Process  Review Section </h6>
                                                    </div>
                                                    <div class="row collapse p-3" id="Process-Review">


                                                        <div class="d-flex align-items-center justify-content-between">
                                                            <div>
                                                                <span><i class="cv-calendar me-1"></i>Last Review Date :</span><span class="ms-1 text-primary">10/05/2024</span>
                                                            </div>
                                                            <div class="d-flex align-items-center">
                                                                <span><i class="cv-calendar me-1"></i>Next Review Date :</span><span class="ms-1"></span>
                                                                <div class="form-group">
                                                                    <div class="input-group">
                                                                        <input type="date" class="form-control" />


                                                                        <div class="d-flex align-items-center gap-1 my-2">
                                                                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#Process-Review" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                                                                            <h6 class="mb-0">Process  Review Section </h6>
                                                                        </div>
                                                                        <div class="row collapse p-3" id="Process-Review">


                                                                            <div class="d-flex align-items-center justify-content-between">
                                                                                <div>
                                                                                    <span><i class="cv-calendar me-1"></i>Last Review Date :</span><span class="ms-1 text-primary">10/05/2024</span>
                                                                                </div>
                                                                                <div class="d-flex align-items-center">
                                                                                    <span><i class="cv-calendar me-1"></i>Next Review Date :</span><span class="ms-1">
                                                                                        <div class="form-group">
                                                                                            <div class="input-group">
                                                                                                <input type="date" class="form-control" />

                                                                                            </div>
                                                                                            <div>
                                                                                                <ul class="ps-0 mb-0">
                                                                                                    <li class="list-group-item fw-semibold">Arun Patil</li>
                                                                                                    <li class="list-group-item"><a class="text-primary" href="#"><EMAIL></a></li>
                                                                                                    <li class="list-group-item">9988337711</li>
                                                                                                </ul>
                                                                                            </div>
                                                                                        </div>
                                                                                </div>
                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>

                                    </tr>

                                </tbody>
                            </table>
                            <div class="d-flex align-items-center justify-content-end gap-2">
                                <button class="btn btn-sm btn-primary">End Review</button>
                                <button class="btn btn-sm btn-primary">Show History</button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="card-footer d-flex gap-2 align-items-center justify-content-between my-3">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="submit" class="btn btn-primary btn-sm">View BIA/Dependencies</button>
                    <button type="button" class="btn btn-secondary btn-sm me-1">Cancel</button>
                    <button type="submit" class="btn btn-primary btn-sm">Update</button>

                </div>
            </div>
        </div>
    </div>
</div>
<!--End Configuration offcanvas -->
@section Scripts {
    <script>
        $(document).ready(function () {
            $(document).on("click", ".Closebtn", function(){
               
              location.reload();
            });

            $('#btnCreate').click(function () {
                $.get('@Url.Action("AddBCMFacility", "ManageFacility", new { Area = "BCMFacility" })', function (data) {
                    $('.modal-data').html(data);
                    $('#CreateModal').modal('show');
                })
            });





                $(document).on('click', '.btnEdit', function () {
                    var id = $(this).data('id');
                    $.get('@Url.Action("EditBCMFacility", "ManageFacility", new { Area = "BCMFacility" })',{ id: id } , function (data) {
                        $('.modal-data').html(data);
                        $('#CreateModal').modal('show');
                    });
                });

            $(document).on('click', '.btnDelete', function () {
                var id = $(this).data('id');
                var EntityTypeID = $(this).data('EntityTypeID');
                var Text = $(this).data('Text');
                $.get('@Url.Action("DeleteBCMFacility", "ManageFacility", new { Area = "BCMFacility" })' ,{ id: id }, function (data) {
                    // $.get('/Dashboard/_Delete/' + id, function (data) {
                        $('#DeleteBody').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

            $('#inlineRadio1').click(function () {

                var Searchtxt = $('#txtSearch').val();
                var iIsUnderBCM = ($(this).val());
                var ddlOrgSeletedValue = $('#ddlOrganization').val();
                var ddlDepartmentValue = $('#ddlDepartment').val();
                var ddlUnitValue = $('#ddlUnit').val();
                var ddlSubDepartmentValue = $('#ddlSubDepartment').val();
                $.get('@Url.Action("GetFileredFacility", "ManageFacility", new { Area = "BCMFacility" })', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: "" }, function (data) {
                        var tableData = $('#tblBody');
                            var tableData = $('#tblBody');
                        tableData.empty();
                            $('#tblBody').html(data);
                });
            });

            $('#inlineRadio2').click(function () {
                var Searchtxt = $('#txtSearch').val();
                var iIsUnderBCM = ($(this).val());
                var ddlOrgSeletedValue = $('#ddlOrganization').val();
                var ddlDepartmentValue = $('#ddlDepartment').val();
                var ddlUnitValue = $('#ddlUnit').val();
                var ddlSubDepartmentValue = $('#ddlSubDepartment').val();
                $.get('@Url.Action("GetFileredFacility", "ManageFacility", new { Area = "BCMFacility" })', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: "" }, function (data) {
                        var tableData = $('#tblBody');
                            var tableData = $('#tblBody');
                        tableData.empty();
                            $('#tblBody').html(data);
                });
            });

            $('#inlineRadio3').click(function () {
                var Searchtxt = $('#txtSearch').val();
                var iIsUnderBCM = ($(this).val());
                var ddlOrgSeletedValue = $('#ddlOrganization').val();
                var ddlDepartmentValue = $('#ddlDepartment').val();
                var ddlUnitValue = $('#ddlUnit').val();
                var ddlSubDepartmentValue = $('#ddlSubDepartment').val();
                $.get('@Url.Action("GetFileredFacility", "ManageFacility", new { Area = "BCMFacility" })', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: "" }, function (data) {
                        var tableData = $('#tblBody');
                            var tableData = $('#tblBody');
                        tableData.empty();
                            $('#tblBody').html(data);
                });
            });

            $('#txtSearch').change(function () {
                var Searchtxt = ($(this).val());
                var Searchtxt = $('#txtSearch').val();
                var iIsUnderBCM = -1;
                var ddlOrgSeletedValue = $('#ddlOrganization').val();
                var ddlDepartmentValue = $('#ddlDepartment').val();
                var ddlUnitValue = $('#ddlUnit').val();
                var ddlSubDepartmentValue = $('#ddlSubDepartment').val();
                $.get('@Url.Action("GetFileredFacility", "ManageFacility", new { Area = "BCMFacility" })', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, IsUnderBCM: iIsUnderBCM, SearchText: Searchtxt }, function (data) {
                        var tableData = $('#tblBody');
                            var tableData = $('#tblBody');
                        tableData.empty();
                            $('#tblBody').html(data);
                });
            });



                $('#ddlOrganization').change(function () {
                        debugger;
                var ddlOrgSeletedValue = $(this).val();
                var ddlUnitValue = $('#ddlUnit').val();
                var Searchtxt = $('#txtSearch').val();
                var ddlDepartmentValue = $('#ddlDepartment').val();
                var ddlSubDepartmentValue = $('#ddlSubDepartment').val();

                    if (ddlOrgSeletedValue)
                       {

                           $.ajax({
                                      url: '@Url.Action("GetAllUnits", "ManageFacility", new { Area = "BCMFacility" })',
                                      type: 'GET',
                                      data: { iOrgID: ddlOrgSeletedValue },
                                      success: function (response) {
                                            let selectizeInstance = $('#ddlUnit')[0].selectize;
                                            selectizeInstance.clear();
                                            selectizeInstance.clearOptions();
                                            selectizeInstance.addOption({ value: "0", text: "-- All Units --" });
                                            selectizeInstance.addItem("0");

                                                 response && response.forEach(({ unitID, unitName }) => {
                                                         if (unitID && unitName) {
                                                             selectizeInstance.addOption({ value: unitID, text: unitName });
                                                 }
                                            });

                                       },
                                      // success: function (data) {
                                      //     var ddlUnit = $('#ddlUnit');
                                      //     ddlUnit.empty();
                                      //     ddlUnit.append('<option value="0">-- All Units --</option>');
                                      //     console.log(data);

                                      //     $.each(data, function (index, item) {
                                      //                 ddlUnit.append('<option value="' + item.unitID + '">' + item.unitName + '</option>')

                                      //     });
                                      // },
                                      error:function(Error){
                                          console.log(Error,"Error Block");
                                      }
                        })
                    }
                    $.get('@Url.Action("GetFileredFacility", "ManageFacility", new { Area = "BCMFacility" })', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, SearchText: "",iIsCritical:-1 }, function (data) {
                       var tableData = $('#tblBody');
                            var tableData = $('#tblBody');
                        tableData.empty();
                            $('#tblBody').html(data);
                });
            });

                $('#ddlUnit').change(function () {

                        var ddlOrgSeletedValue = $('#ddlOrganization').val();
                        var selectedValue = $(this).val();
                        var Searchtxt = $('#txtSearch').val();
                        $('#ddlDepartment').empty();
                        var ddlDepartmentValue = $('#ddlDepartment').val();
                        var ddlSubDepartmentValue = $('#ddlSubDepartment').val();


                            var iUnitID = $(this).val();
                            if (iUnitID)
                            {
                                $.ajax({
                                            url: '@Url.Action("GetAllDepartments", "ManageFacility", new { Area = "BCMFacility" })',
                                            type: 'GET',
                                            data: { iUnitID: iUnitID, iOrgID: ddlOrgSeletedValue},
                                            success: function (response) {
                                                let selectizeInstance = $('#ddlDepartment')[0].selectize;
                                                selectizeInstance.clear();
                                                selectizeInstance.clearOptions();
                                                selectizeInstance.addOption({ value: "0", text: "-- All Departments --" });
                                                selectizeInstance.addItem("0");

                                                     response && response.forEach(({ departmentID, departmentName }) => {
                                                             if (departmentID && departmentName) {
                                                                 selectizeInstance.addOption({ value: departmentID, text: departmentName });
                                                     }
                                                });

                                            }
                                            // success: function (data) {
                                            //     var ddlDepartment = $('#ddlDepartment');
                                            //     ddlDepartment.empty();
                                            //     ddlDepartment.append('<option value="0">-- All Departments --</option>');
                                            //     $.each(data, function (index, item) {
                                            //         ddlDepartment.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                                            //     });
                                            // }
                                })
                            }



                            $.get('@Url.Action("GetFileredFacility", "ManageFacility", new { Area = "BCMFacility" })', { OrgID: ddlOrgSeletedValue, UnitID: selectedValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, SearchText: "",iIsCritical:-1 }, function (data) {
                                var tableData = $('#tblBody');
                                    var tableData = $('#tblBody');
                                tableData.empty();
                                    $('#tblBody').html(data);
                        });
                    });



                $('#ddlDepartment').change(function () {
                var ddlOrgSeletedValue = $('#ddlOrganization').val();
                var ddlDepartmentValue = $(this).val();
                var Searchtxt = $('#txtSearch').val();
                var ddlUnitValue = $('#ddlUnit').val();
                var ddlSubDepartmentValue = $('#ddlSubDepartment').val();


                    var iUnitID = $(this).val();
                            if (iUnitID)
                            {
                                $.ajax({
                                    url: '@Url.Action("GetAllSubDepartments", "ManageFacility", new { Area = "BCMFacility" })',
                                    type: 'GET',
                                    data: {iOrgID:ddlOrgSeletedValue,iDepartmentID : ddlDepartmentValue},
                                    success: function (response) {
                                              let selectizeInstance = $('#ddlSubDepartment')[0].selectize;
                                          selectizeInstance.clear();
                                          selectizeInstance.clearOptions();
                                              selectizeInstance.addOption({ value: "0", text: "-- All SubDepartments --" });
                                          selectizeInstance.addItem("0");

                                                  response && response.forEach(({ subFunctionID, subFunctionName }) => {
                                                      if (subFunctionID && subFunctionName) {
                                                           selectizeInstance.addOption({ value: subFunctionID, text: subFunctionName });
                                              }
                                          });

                                    }
                                    // success: function (data) {
                                    //     var ddlSubDepartment = $('#ddlSubDepartment');
                                    //     ddlSubDepartment.empty();
                                    //     ddlSubDepartment.append('<option value="0">-- All SubDepartments --</option>');
                                    //     $.each(data, function (index, item) {
                                    //         ddlSubDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>')
                                    //     });
                                    //     }
                                    // }
                                });
                            }



                    $.get('@Url.Action("GetFileredFacility", "ManageFacility", new { Area = "BCMFacility" })', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, SearchText: "",iIsCritical:-1 }, function (data) {
                       var tableData = $('#tblBody');
                            var tableData = $('#tblBody');
                        tableData.empty();
                            $('#tblBody').html(data);
                });
            });

            $('#ddlSubDepartment').change(function () {
                var ddlOrgSeletedValue = $('#ddlOrganization').val();
                var ddlSubDepartmentValue = $(this).val();
                var Searchtxt = $('#txtSearch').val();
                var ddlUnitValue = $('#ddlUnit').val();
                var ddlDepartmentValue = $('#ddlDepartment').val();

                    var iUnitID = $(this).val();
                            if (iUnitID)
                            {
                                $.ajax({
                                            url: '@Url.Action("GetAllSubDepartments", "ManageBusinessProcesses")',
                                            type: 'GET',
                                                data: { iDepartmentID: iUnitID,iOrgID : ddlOrgSeletedValue},
                                            success: function (data) {
                                                var ddlSubDepartment = $('#ddlSubDepartment');
                                                ddlSubDepartment.empty();
                                                ddlSubDepartment.append('<option value="0">-- All SubDepartments --</option>');
                                                $.each(data, function (index, item) {
                                                    ddlSubDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>')
                                                });
                                            }
                                })
                            }

                    $.get('@Url.Action("GetFileredFacility", "ManageFacility", new { Area = "BCMFacility" })', { OrgID: ddlOrgSeletedValue, UnitID: ddlUnitValue, DepartmentID: ddlDepartmentValue, SubDepartmentID: ddlSubDepartmentValue, SearchText: "",iIsCritical:-1 }, function (data) {
                       var tableData = $('#tblBody');
                            var tableData = $('#tblBody');
                        tableData.empty();
                            $('#tblBody').html(data);
                });
            });




            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#CreateModal').modal('hide');
                        location.reload();
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

            $(document).on('click', '.filterButton', function () {
                 debugger;

                 var btn = $(this).attr('id')
                var TextSearch = $('#search-inp').val();
                 var iIsUnderBCM = -1;
                 var iIsCritical;
                 var ddlDepartmentVal = $('#ddlDepartment').val();
                    var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                    var ddlUnitnVal = $('#ddlUnit').val();
                    var ddlOrganizationVal = $('#ddlOrganization').val();


                    if (btn == 'btnUnderBCM') {
                        iIsUnderBCM = 1;
                    }

                    if (btn == 'btnNonCtitcalProcess') {
                        iIsCritical = 0;
                    }
                    if (btn == 'btnCtitcalProcess') {
                        iIsCritical = 1;
                    }



                $.get('@Url.Action("GetFileredFacility", "ManageFacility", new { Area = "BCMFacility" })', {SearchText : TextSearch,OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM, iIsCritical:iIsCritical }, function (data) {
                             var tableData = $('#tblBody');
                                var tableData = $('#tblBody');
                            tableData.empty();
                                $('#tblBody').html(data);
                    });
            });
        });
    </script>

}