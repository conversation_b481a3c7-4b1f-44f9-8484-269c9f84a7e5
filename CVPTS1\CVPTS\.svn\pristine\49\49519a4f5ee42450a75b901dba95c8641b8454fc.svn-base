.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 260px;
  z-index: 100;
  transition: all 0.5s ease;
  background-color: var(--bs-white);
}

.sidebar.close {
  width: 60px;
}

.sidebar .logo-details {
  height: 60px;
  width: 100%;
  display: flex;
  align-items: center;
}

.sidebar .logo-details img {
  height: 50px;
  min-width: 60px;
  text-align: center;
  line-height: 50px;
}

.sidebar .logo-details .logo_name {
  transition: 0s ease;
  transition-delay: 0.3s;
}

.sidebar.close .logo-details .logo_name {
  transition-delay: 0s;
  opacity: 0;
  pointer-events: none;
}

.sidebar .nav-links {
  padding: 0 0 150px 0;
  overflow-y: auto;
  height: calc(100vh - 92px);
}

.sidebar.close .nav-links {
  overflow: visible;
}

.sidebar .nav-links::-webkit-scrollbar {
  display: none;
}

.sidebar .nav-links li {
  position: relative;
  list-style: none;
  transition: all 0.4s ease;
  margin: 10px;
  border-radius: 23px;
}

.sidebar .nav-links li:hover {
  background: rgb(130, 16, 65);
  background: linear-gradient(50deg, #E63875 4%, #320284 100%);
  border-radius: 23px;
  margin: 10px;
}

.sidebar .showMenu .sub-menu{
border-radius: 23px;
}

.sidebar .nav-links .showMenu {
  border:1px solid rgb(130, 16, 65);
}

.sidebar .nav-links .showMenu {
  border:1px solid rgb(130, 16, 65);
}

.sidebar .nav-links li .icon-link {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
}

.sidebar.close .nav-links li .icon-link {
  display: block;
}

.sidebar .nav-links li i {
  height: 40px;
  min-width: 40px;
  text-align: center;
  line-height: 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--bs-nav-icon-font-size);
}

.sidebar .nav-links li .arrow {
  font-size: var(--bs-body-font-size);
}

.sidebar .nav-links li i:hover {
  color: var(--bs-white);
}

    .sidebar .nav-links li:hover > a{
        color: var(--bs-white);
    }

.sidebar .nav-links li.showMenu i.arrow {
  transform: rotate(-180deg);
}

.sidebar.close .nav-links i.arrow {
  display: none;
}

.sidebar .nav-links li a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--bs-secondary);
  width: 100%;
}

.sidebar .nav-links li a:hover{
  color: var(--bs-white);
}


.sidebar .nav-links li a .link_name {
  font-size: var(--bs-nav-link-font-size);
  font-weight: 400;
  transition: all 0.4s ease;
}

.sidebar.close .nav-links li a .link_name {
  opacity: 0;
  pointer-events: none;
}

.sidebar .profile-details {
  position: fixed !important;
  bottom: 0;
  width: 240px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.5s ease;
}

.sidebar.close .profile-details {
  background: none;
}

.sidebar.close .profile-details {
  width: 40px;
}

.home-section {
  position: relative;
  /*height: 100vh;*/
  /*left: 260px;*/
  /*width: calc(100% - 260px);*/
  transition: all 0.5s ease;
}

.sidebar.close~.home-section {
  /*left: 60px;*/
  /*width: calc(100% - 60px);*/
}

.bx-menu {
  position: absolute;
  left: -10px;
  bottom: -10px;
  z-index: 999;
  border-radius: 50%;
  background-color: var(--bs-red);
  color: var(--bs-white);
  height: 18px;
  width: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.Profile-img {
  width: 30px;
  border-radius: 50%;
}

.Menu-Header {
    background-color: var(--bs-white) !important;
    background: #fff !important;
    top: 0px;
    position: sticky !important;
    border-radius: 0 !important;
    z-index: 1;
}

    .Menu-Header .link_name {
        font-weight: 500;
        color: var(--bs-primary) !important;
        background-color: #f1f1f1;
    }

/* Sidebar Sub Menu Style */

.sidebar .nav-links li .sub-menu {
    padding: 0px 6px 0px 15px;
    margin-top: -10px;
    background: var(--bs-white);
    display: none;
}

.sidebar .nav-links li.showMenu .sub-menu {
  display: block;
}

.sidebar .nav-links li .sub-menu a {
  padding: 3px 12px;
  white-space: nowrap;
  /* transition: all 0.3s ease; */
}

.sidebar .nav-links li .sub-menu a:hover {
  opacity: 1;
}

.sidebar.close .nav-links li .sub-menu {
    position: absolute;
    left: 100%;
    top: -10px;
    margin-top: 0;
    padding: 0px;
    opacity: 0;
    display: block;
    pointer-events: none;
    transition: 0s;
    box-shadow: var(--bs-box-shadow) !important;
    width: auto;
    min-width: 214px !important;
    height: auto;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
}

.sidebar.close .nav-links li:hover .sub-menu {
  top: 0;
  opacity: 1;
  pointer-events: auto;
  transition: all 0.4s ease;
}

.sidebar.close .nav-links li .sub-menu li{
  padding: 0px;
  margin: 4px;
}

.sidebar .nav-links li .sub-menu .link_name {
  display: none;
}

.sidebar.close .nav-links li .sub-menu .link_name {
  font-size: var(--bs-nav-link-font-size);
  opacity: 1;
  display: block;
}

.sidebar .nav-links li .sub-menu.blank {
  opacity: 1;
  pointer-events: auto;
  /* padding: 3px 20px 6px 16px; */
  opacity: 0;
  pointer-events: none;
}

.sidebar .nav-links li:hover .sub-menu.blank {
  top: 50%;
  transform: translateY(-50%);
}

@media (max-width: 420px) {
  .sidebar.close .nav-links li .sub-menu {
    display: none;
  }
}

/* End Sidebar Sub Menu Style */

.sidebar.close .nav-links li:hover .home {
    top: -20px;
    max-height: calc(100vh - 80px);
}

.sidebar.close .nav-links li:hover .analyze {
    top: -70px;
    max-height: calc(100vh - 80px);
}

.sidebar.close .nav-links li:hover .configuration {
    top: -223px;
    max-height: calc(100vh - 80px);
}

/*.sidebar.close .nav-links li:hover .reports {
    top: -170px;
}
*/

