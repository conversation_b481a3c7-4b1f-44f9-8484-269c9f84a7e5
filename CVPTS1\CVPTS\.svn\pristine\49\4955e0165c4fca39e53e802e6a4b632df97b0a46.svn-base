using CVEncryptionLibrary;

namespace BCM.UI.Services
{
    public interface ISimpleEncryptionService
    {
        string Encrypt(string text);
        string Decrypt(string encryptedText);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hash);
    }

    public class SimpleEncryptionService : ISimpleEncryptionService
    {
        private readonly string _defaultKey;

        public SimpleEncryptionService(IConfiguration configuration)
        {
            _defaultKey = configuration["Encryption:DefaultKey"] ?? 
                         "DefaultKey123456789012345678901234";
        }

        public string Encrypt(string text)
        {
            if (string.IsNullOrEmpty(text))
                throw new ArgumentException("Text cannot be null or empty");

            try
            {
                string encrypted = EncryptionHelper.EncryptAES256(text, _defaultKey);

                if (string.IsNullOrEmpty(encrypted))
                    throw new InvalidOperationException("Encryption returned null or empty result");

                return encrypted;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"AES256 Encryption failed: {ex.Message}", ex);
            }
        }

        public string Decrypt(string encryptedText)
        {
            if (string.IsNullOrEmpty(encryptedText))
                throw new ArgumentException("Encrypted text cannot be null or empty");

            try
            {
                // Validate Base64 format before attempting decryption
                if (!IsValidBase64String(encryptedText))
                    throw new FormatException("The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.");

                string decrypted = EncryptionHelper.DecryptAES256(encryptedText, _defaultKey);

                if (string.IsNullOrEmpty(decrypted))
                    throw new InvalidOperationException("Decryption returned null or empty result");

                return decrypted;
            }
            catch (FormatException)
            {
                // Re-throw FormatException as-is for Base64 issues
                throw;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"AES256 Decryption failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Validates if a string is a proper Base64 format
        /// </summary>
        private bool IsValidBase64String(string base64String)
        {
            try
            {
                if (string.IsNullOrEmpty(base64String))
                    return false;

                // Remove any whitespace
                base64String = base64String.Trim();

                // Check if string length is multiple of 4 (Base64 requirement)
                if (base64String.Length % 4 != 0)
                    return false;

                // Check for valid Base64 characters only
                if (!System.Text.RegularExpressions.Regex.IsMatch(base64String, @"^[A-Za-z0-9+/]*={0,2}$"))
                    return false;

                // Try to convert from Base64 to validate format
                Convert.FromBase64String(base64String);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("Password cannot be null or empty");
            
            var result = EncryptionHelper.HashPassword(password);
            return result.ToString();
        }

        public bool VerifyPassword(string password, string hash)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hash))
                return false;
            
            try
            {
                var storedHash = PasswordHashResult.FromString(hash);
                return EncryptionHelper.VerifyPassword(password, storedHash);
            }
            catch
            {
                return false;
            }
        }
    }
}
