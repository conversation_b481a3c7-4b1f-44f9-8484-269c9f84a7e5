﻿/* Reset and Base Styles */

/* App Layout */
.app-container {
    height: calc(100vh - 110px);
    /* display: flex; */
    /* flex-direction: column; */
    /* overflow: auto; */
}

/* Header */
/*.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 100;
}

    .header h1 {
        font-size: 22px;
        font-weight: 700;
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

.header-actions {
    display: flex;
    gap: 12px;
}*/




.btn-icon {
    padding: 8px 10px;
    min-width: 36px;
    justify-content: center;
}

/* Main Content Layout */
.main-content {
    /* flex: 1; */
    /* display: flex; */
    /* overflow: hidden; */
    height: 100%;
}


.paletteNode {
    /* display: flex; */
    /* align-items: center; */
    /* gap: 18px; */
    /* padding: 8px; */
    /* margin-bottom: 18px; */
    /* background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); */
    /* border: 0px solid #e2e8f0; */
    /* border-radius: 9px; */
    /* box-shadow: 0 2px 4px rgba(0,0,0,0.05); */
    cursor: grab;
}

    .paletteNode:hover {
        /* background: radial-gradient(circle at 100% 100%, #ffffff 0, #ffffff 5px, transparent 5px) 0% 0%/7px 7px no-repeat, radial-gradient(circle at 0 100%, #ffffff 0, #ffffff 5px, transparent 5px) 100% 0%/7px 7px no-repeat, radial-gradient(circle at 100% 0, #ffffff 0, #ffffff 5px, transparent 5px) 0% 100%/7px 7px no-repeat, radial-gradient(circle at 0 0, #ffffff 0, #ffffff 5px, transparent 5px) 100% 100%/7px 7px no-repeat, linear-gradient(#ffffff, #ffffff) 50% 50%/calc(100% - 4px) calc(100% - 14px) no-repeat, linear-gradient(#ffffff, #ffffff) 50% 50%/calc(100% - 14px) calc(100% - 4px) no-repeat, radial-gradient(at 100% 100%, transparent 22%, #e63875 84%, #e63875 100%), #350383; */
        /* border-radius: 10px; */
        /*padding: 2px;*/
        /* box-sizing: border-box; */
        /*background: linear-gradient(114deg, rgba(230,56,117,1) 4%, rgba(50,2,132,1) 100%);*/
        /*border-color: #667eea;*/
        /* transform: translateY(-2px); */
        /*box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);*/
        /* color: #000; */
    }

        .paletteNode:hover .node-icon,
        .paletteNode:hover span {
            color: #000;
        }

    .paletteNode:active {
        cursor: grabbing;
        transform: translateY(0);
    }




.node-icon {
    font-size: 18px;
    width: 28px;
    text-align: center;
    transition: color 0.3s ease;
}

.paletteNode span {
    /* font-size: 14px; */
    /* font-weight: 600; */
    /* color: #374151; */
    /* transition: color 0.3s ease; */
}

/* Custom palette nodes */
.custom-palette-node {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #cbd5e0;
    border-radius: 12px;
    cursor: grab;
    transition: all 0.3s ease;
    user-select: none;
}


    .custom-palette-node:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        color: white;
    }

        .custom-palette-node:hover .node-info,
        .custom-palette-node:hover .node-name,
        .custom-palette-node:hover .node-desc {
            color: white;
        }

    .custom-palette-node .node-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .custom-palette-node .node-name {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        transition: color 0.3s ease;
    }

    .custom-palette-node .node-desc {
        font-size: 12px;
        color: #6b7280;
        transition: color 0.3s ease;
    }

    .custom-palette-node .delete-custom-node {
        opacity: 0;
        transition: opacity 0.3s ease;
        padding: 4px 6px;
        font-size: 10px;
    }

    .custom-palette-node:hover .delete-custom-node {
        opacity: 1;
    }

    .custom-palette-node .delete-custom-node:hover {
        background-color: rgba(239, 68, 68, 0.2) !important;
        border-color: #ef4444 !important;
        color: #ef4444 !important;
    }

.selected {
    position: relative;
    background: linear-gradient(white, white) padding-box, linear-gradient(to right, #d33276, #4c0a82) border-box !important;
    border: 1px solid transparent !important;
}



/* Form styling */
.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

.property-row {
    background: white;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

/* Enhanced sidebar styling */
.sidebar h4 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    font-weight: 700;
    text-transform: uppercase;
    color: #4a5568;
    margin-bottom: 16px;
    letter-spacing: 1px;
    padding-left: 8px;
    border-left: 3px solid #667eea;
}

#custom-nodes-section h4 {
    justify-content: space-between;
    align-items: center;
}

#custom-nodes-section .btn {
    font-size: 10px;
    padding: 2px 6px;
}

/* Double-click hint for custom nodes */
.custom-palette-node {
    position: relative;
}

    .custom-palette-node::after {
        content: "Double-click to edit";
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 10px;
        color: #6b7280;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        white-space: nowrap;
    }

    .custom-palette-node:hover::after {
        opacity: 1;
    }



/* Canvas Container */
.canvas-container {
    /* flex: 1; */
    /* display: flex; */
    height: 100%;
    /* flex-direction: column; */
    /* background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); */
    overflow-x: auto;
    overflow-y: hidden;
}

.canvas-toolbar {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 2px solid #e2e8f0;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    background: rgba(255,255,255,0.8);
    padding: 8px 16px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

    .zoom-controls #zoom-level {
        font-size: 14px;
        font-weight: 600;
        min-width: 60px;
        text-align: center;
        color: #4a5568;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }

        .zoom-controls #zoom-level:hover {
            background-color: rgba(102, 126, 234, 0.1);
        }

.canvas-info {
    display: flex;
    gap: 24px;
    font-size: 14px;
    font-weight: 600;
    color: #4a5568;
}

    .canvas-info span {
        background: rgba(255,255,255,0.8);
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
    }

.canvas-wrapper {
    flex: 1;
    position: relative;
    overflow-x: auto;
    overflow-y: hidden;
    /* Hide scrollbars */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    scroll-behavior: smooth;
}

    .canvas-wrapper::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }

.canvas {
    /* width: 5000px; Wide horizontal canvas */
    height: 100%;
    position: relative;
    background-image: radial-gradient(circle, #cbd5e0 1px, transparent 1px);
    background-size: 24px 24px;
    background-position: 0 0;
}

/* Auto-scroll visual indicators */
.canvas-wrapper::before,
.canvas-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100px;
    pointer-events: none;
    z-index: 100;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.canvas-wrapper::before {
    left: 0;
    background: linear-gradient(to right, rgba(102, 126, 234, 0.2), transparent);
}

.canvas-wrapper::after {
    right: 0;
    background: linear-gradient(to left, rgba(102, 126, 234, 0.2), transparent);
}

.canvas-wrapper.auto-scroll-left::before,
.canvas-wrapper.auto-scroll-right::after {
    opacity: 1;
    animation: scrollPulse 1s ease-in-out infinite alternate;
}

@keyframes scrollPulse {
    0% {
        opacity: 0.8;
    }

    100% {
        opacity: 1;
    }
}

.canvas.connecting-mode {
    cursor: crosshair;
}

    .canvas.connecting-mode .port.input {
        border-color: #10b981;
        background: rgba(16, 185, 129, 0.1);
        transform: translateY(-50%) scale(1.2);
    }

        .canvas.connecting-mode .port.input:hover {
            background: #10b981;
            transform: translateY(-50%) scale(1.4);
        }

/* SVG Connections Layer */
.connections-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 5000px;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

    .connections-layer path {
        pointer-events: stroke;
        stroke-width: 2;
        fill: none;
        cursor: pointer;
    }

.connection {
    stroke: #6b7280;
    stroke-width: 2;
    transition: all 0.2s ease;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

    .connection:hover {
        stroke: #667eea;
        stroke-width: 3;
        filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
    }

    .connection.selected {
        stroke: #ef4444;
        stroke-width: 3;
        filter: drop-shadow(0 2px 4px rgba(239, 68, 68, 0.3));
    }

    /* Enhanced connection feedback during node drag */
    .connection.updating {
        stroke: #667eea;
        stroke-width: 2.5;
        opacity: 0.8;
        transition: none; /* Disable transitions during updates for performance */
    }

    /* Connection animation when finalizing position */
    .connection.finalizing {
        animation: connectionFinalize 0.3s ease-out;
    }

@keyframes connectionFinalize {
    0% {
        stroke-width: 3;
        opacity: 0.7;
    }

    100% {
        stroke-width: 2;
        opacity: 1;
    }
}

/* Workflow Nodes */
.workflow-node {
    position: absolute;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 3px solid #e2e8f0;
    border-radius: 16px;
    padding: 20px;
    min-width: 180px;
    cursor: grab;
    user-select: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    z-index: 10;
}

    .workflow-node:active {
        cursor: grabbing;
    }

    .workflow-node:hover {
        /*border-color: #667eea;*/
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
        transform: translateY(-2px);
    }

    .workflow-node.selected {
        /*border-color: #e63875;*/
        background: radial-gradient(circle at 100% 100%, #ffffff 0, #ffffff 5px, transparent 5px) 0% 0%/7px 7px no-repeat, radial-gradient(circle at 0 100%, #ffffff 0, #ffffff 5px, transparent 5px) 100% 0%/7px 7px no-repeat, radial-gradient(circle at 100% 0, #ffffff 0, #ffffff 5px, transparent 5px) 0% 100%/7px 7px no-repeat, radial-gradient(circle at 0 0, #ffffff 0, #ffffff 5px, transparent 5px) 100% 100%/7px 7px no-repeat, linear-gradient(#ffffff, #ffffff) 50% 50%/calc(100% - 4px) calc(100% - 14px) no-repeat, linear-gradient(#ffffff, #ffffff) 50% 50%/calc(100% - 14px) calc(100% - 4px) no-repeat, radial-gradient(at 100% 100%, transparent 22%, #e63875 84%, #e63875 100%), #350383;
        border-radius: 10px;
        /*padding: 2px;*/
        box-sizing: border-box;
        /*  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);*/
        /*background: linear-gradient(114deg, rgba(230,56,117,1) 4%, rgba(50,2,132,1) 100%); color: white;*/
    }

        .workflow-node.selected .node-title,
        .workflow-node.selected .node-subtitle {
            color: #000;
        }

    .workflow-node.dragging {
        transform: rotate(2deg) scale(1.03);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        z-index: 1000;
        transition: none; /* Disable transitions during drag for performance */
    }

        .workflow-node.dragging .port {
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
            transform: translateY(-50%) scale(1.1);
        }

.node-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.node-icon {
    font-size: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.workflow-node.selected .node-icon {
    background: rgba(255,255,255,0.2);
}

.node-title {
    font-size: 16px;
    font-weight: 700;
    color: #1a202c;
    transition: color 0.3s ease;
}

.node-subtitle {
    font-size: 13px;
    color: #4a5568;
    font-weight: 500;
    transition: color 0.3s ease;
}

/* Node Ports */
.node-ports {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    position: relative;
}

/* Enhanced port system with edge-based positioning */
.port {
    width: 16px;
    height: 16px;
    border: 2px solid #667eea;
    border-radius: 50%;
    background: #ffffff;
    cursor: crosshair;
    transition: all 0.2s ease;
    position: absolute;
    z-index: 20;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

    .port:hover {
        border-color: #667eea;
        background: #667eea;
        transform: scale(1.3);
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.6);
    }

    .port.input {
        left: -8px;
        top: 50%;
        transform: translateY(-50%);
        border-color: #10b981;
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    }

        .port.input:hover {
            border-color: #10b981;
            background: #10b981;
            transform: translateY(-50%) scale(1.3);
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.6);
        }

    .port.output {
        right: -8px;
        border-color: #f59e0b;
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
    }

        .port.output:hover {
            border-color: #f59e0b;
            background: #f59e0b;
            box-shadow: 0 4px 16px rgba(245, 158, 11, 0.6);
        }

/* Edge highlighting for connection zones */
.workflow-node::before,
.workflow-node::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 60%;
    top: 20%;
    background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.workflow-node::before {
    left: -2px;
    background: linear-gradient(to bottom, transparent, rgba(16, 185, 129, 0.3), transparent);
}

.workflow-node::after {
    right: -2px;
    background: linear-gradient(to bottom, transparent, rgba(245, 158, 11, 0.3), transparent);
}

.workflow-node:hover::before,
.workflow-node:hover::after {
    opacity: 1;
}

.workflow-node.connecting::before,
.workflow-node.connecting::after {
    opacity: 1;
    animation: edgePulse 1.5s ease-in-out infinite;
}

@keyframes edgePulse {
    0%, 100% {
        opacity: 0.3;
    }

    50% {
        opacity: 0.8;
    }
}

/* Multi-output node styling with edge-based positioning */
.node-ports.multi-output {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    height: 60px;
    padding: 0;
    position: relative;
}

    .node-ports.multi-output .port.input {
        left: -8px;
        top: 50%;
        transform: translateY(-50%);
    }

.output-ports {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 18px;
    position: absolute;
    right: 0;
    height: 100%;
}

.output-port-group {
    display: flex;
    align-items: center;
    gap: 6px;
    position: relative;
}

    .output-port-group .port {
        position: relative;
        right: -8px;
        transform: none;
    }

.port-label {
    font-size: 9px;
    font-weight: 700;
    color: #4a5568;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    user-select: none;
    pointer-events: none;
    margin-right: 4px;
}

/* True/False port specific styling */
.output-port-group[data-port-type="true"] .port-label {
    color: #059669;
}

.output-port-group[data-port-type="false"] .port-label {
    color: #dc2626;
}

.output-port-group .port[data-port-type="true"] {
    border-color: #059669;
    box-shadow: 0 2px 8px rgba(5, 150, 105, 0.4);
}

    .output-port-group .port[data-port-type="true"]:hover {
        border-color: #059669;
        background: #059669;
        transform: scale(1.3);
        box-shadow: 0 4px 16px rgba(5, 150, 105, 0.6);
    }

.output-port-group .port[data-port-type="false"] {
    border-color: #dc2626;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4);
}

    .output-port-group .port[data-port-type="false"]:hover {
        border-color: #dc2626;
        background: #dc2626;
        transform: scale(1.3);
        box-shadow: 0 4px 16px rgba(220, 38, 38, 0.6);
    }

/* Multi-output node adjustments */
.workflow-node[data-node-type="if"] {
    min-height: 120px;
    position: relative;
}

    .workflow-node[data-node-type="if"] .node-ports.multi-output {
        margin-top: 8px;
    }

    /* Enhanced edge indicators for multi-output nodes */
    .workflow-node[data-node-type="if"]::after {
        height: 80%;
        top: 10%;
        background: linear-gradient(to bottom, transparent, rgba(5, 150, 105, 0.2), rgba(245, 158, 11, 0.2), rgba(220, 38, 38, 0.2), transparent);
    }

/* Connection styling for different port types */
.connection-true {
    stroke: #059669;
    stroke-width: 2.5;
}

.connection-false {
    stroke: #dc2626;
    stroke-width: 2.5;
}

.connection-true:hover {
    stroke: #047857;
    stroke-width: 3.5;
    filter: drop-shadow(0 2px 4px rgba(5, 150, 105, 0.4));
}

.connection-false:hover {
    stroke: #b91c1c;
    stroke-width: 3.5;
    filter: drop-shadow(0 2px 4px rgba(220, 38, 38, 0.4));
}

/*.port.connecting {
    border-color: #667eea;
    background: #667eea;
    animation: pulse 1s infinite;
    box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.3);
}

@keyframes pulse {
    0%, 100% {
        transform: translateY(-50%) scale(1.2);
        box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.3);
    }

    50% {
        transform: translateY(-50%) scale(1.5);
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0.1);
    }
}*/

/* Enhanced connection states and temporary connections */
.temp-connection {
    stroke-dasharray: 8,4;
    animation: dashMove 1s linear infinite;
    opacity: 0.8;
}

@keyframes dashMove {
    0% {
        stroke-dashoffset: 0;
    }

    100% {
        stroke-dashoffset: 12;
    }
}

/* Enhanced hover states during connection mode */
.workflow-node.connecting .port.input:hover {
    border-color: #10b981;
    background: #10b981;
    transform: translateY(-50%) scale(1.4);
    box-shadow: 0 0 16px rgba(16, 185, 129, 0.8);
}

.workflow-node.connecting .port.output:hover {
    border-color: #f59e0b;
    background: #f59e0b;
    transform: scale(1.4);
    box-shadow: 0 0 16px rgba(245, 158, 11, 0.8);
}

/* Multi-output port hover states during connection */
.workflow-node.connecting .output-port-group .port[data-port-type="true"]:hover {
    border-color: #059669;
    background: #059669;
    transform: scale(1.4);
    box-shadow: 0 0 16px rgba(5, 150, 105, 0.8);
}

.workflow-node.connecting .output-port-group .port[data-port-type="false"]:hover {
    border-color: #dc2626;
    background: #dc2626;
    transform: scale(1.4);
    box-shadow: 0 0 16px rgba(220, 38, 38, 0.8);
}

/* Context Menu */
.context-menu {
    position: absolute;
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 150px;
}

    .context-menu ul {
        list-style: none;
        margin: 4px 0;
    }

    .context-menu li {
        padding: 8px 16px;
        font-size: 14px;
        cursor: pointer;
        transition: background 0.2s ease;
    }

        .context-menu li:hover {
            background: #f3f4f6;
        }

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 240px;
    }

    .header h1 {
        font-size: 18px;
    }

    .workflow-node {
        min-width: 140px;
        padding: 12px;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.no-select {
    user-select: none;
}

.pointer-events-none {
    pointer-events: none;
}

.diagram-area {
    background-image: radial-gradient(#dfdfdf 1px, #ffffff00 1px) !important;
    background-size: 15px 15px;
    background-color: #fff !important;
    height: calc(100vh - 125px);
}

.offcanvas-footer button.btn {
    min-width: 85px !important;
}

.offcanvas {
    border-radius: 20px 0px 0px 20px !important;
}

.hoverable-card .card-header {
    background: rgb(230, 56, 117);
    background: linear-gradient(242deg, rgba(230, 56, 117, 1) 0%, rgba(50, 2, 132, 1) 100%);
    border: none;
    color: #fff;
}

.offcanvas-header {
    border-radius: 20px 0px 0px 0px !important;
}

#workflowSteps {
    position: relative;
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 20px;
}

.workflowSteps {
    background-color: var(--bs-white);
    border: 1px solid var(--bs-gray-300);
    padding: 3px 4px;
    width: 19rem;
    align-items: center;
    height: 30px;
    text-align: left;
    display: flex;
    border-radius: 3rem;
    box-shadow: 0 .125rem .5rem rgba(0,0,0,.075) !important;
    font-size: var(--bs-body-font-size-small);
}

.workflowTableTooltip {
    position: absolute;
    width: 19rem;
    word-wrap: break-word !important;
    word-break: break-word !important;
}

.highlighted {
    border: 1px dashed var(--bs-secondary) !important;
    box-shadow: 0 .125rem 0.5rem var(--bs-focus-ring-color) !important;
    margin-left: -1px;
}

.conditionalDot {
    height: 4px;
    width: 4px;
    border-radius: 50%;
}

.contextMenu {
    display: none;
    position: absolute;
}

.UlContextBtn {
    margin: 0;
    padding: 0;
}

.contextBtn {
    list-style: none;
    font-size: 14px;
}

    .contextBtn:hover {
        background: #f4f4f4;
    }

.workflowCanvas {
    position: absolute;
    pointer-events: none;
    height: 100%;
    width: 100%;
    left: 0;
}

#workflowSteps svg {
    pointer-events: none;
}

.ui-sortable-handle {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.nodeClass {
    padding: 10px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 3px 10px 0 rgb(0 0 0 / 10%);
    border: 0px solid #c1c1c1;
    /*    height: 62px;*/
    min-width: 130px;
    padding-bottom: 8px;
}

.parentConditionCont {
    position: relative;
    top: -6px;
    height: fit-content
}


/* UI New Style*/
.Start-btn {
    display: flex;
    align-self: center;
    height: fit-content;
    gap: 10px;
    align-items: center;
    font-size: 16px;
    border: 1px solid #ebebeb;
    padding: 15px 8px 15px 15px;
    border-radius: 14px 30px 30px 14px;
    background-color: #ffffff;
    box-shadow: 0 3px 10px 0 rgb(0 0 0 / 10%);
}

/*.ui-sortable svg path {
    stroke: #8c8c8c;
}
*/
.Workflow-Title-Icon {
    display: flex;
    height: 30px;
    width: 30px;
    border-radius: 50%;
    color: #fff;
    font-size: 18px;
    box-shadow: 0 4px 8px rgb(0 0 0 / 15%);
}

    .Workflow-Title-Icon:before {
        margin: auto;
    }

.Action-Title-Icon {
    display: flex;
    height: 25px;
    width: 25px;
    border-radius: 50%;
    color: #fff;
    font-size: 14px;
    box-shadow: 0 4px 8px rgb(0 0 0 / 15%);
    margin-right: 5px;
}

    .Action-Title-Icon:before {
        margin: auto;
    }

.link-dot .cv-stroke-circle {
    font-size: 10px;
    border-radius: 50%;
    vertical-align: bottom;
    font-weight: 600;
}
/*End UI New Style*/
