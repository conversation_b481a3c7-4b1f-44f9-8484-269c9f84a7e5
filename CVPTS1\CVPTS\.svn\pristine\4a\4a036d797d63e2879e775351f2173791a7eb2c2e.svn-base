﻿@model BCM.BusinessClasses.BIASection
@*@model IEnumerable<BCM.BusinessClasses.BIASection>
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<form asp-action="AddBIASurveySection" method="post" id="addBIASurveySection" class="needs-validation progressive-validation" novalidate>
    <div class="row">
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">Section Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-section"></i></span>
                    <input type="text" class="form-control" asp-for="SectionName" placeholder="Enter Selection Name" required title="Name must start with character" />
                </div>
                <div class="invalid-feedback">Enter Section Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Sequence No</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-sequence"></i></span>
                    <input type="text" class="form-control" asp-for="SequenceNo" placeholder="Enter Sequence No" required />
                </div>
                <div class="invalid-feedback">Enter Sequence No</div>
            </div>


        </div>
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">Section Weightage</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-weightage"></i></span>
                    <input class="form-control" type="text" asp-for="SectionWeightage" placeholder="Enter Section Weightage" required title="Weightage allow Numbers only" />
                </div>
                <div class="invalid-feedback">Enter Section Weightage</div>
            </div>
            <div class="form-group">
                <label class="form-label">Page</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-url"></i></span>
                    <select class="form-select form-control" autocomplete="off" asp-for="PageID" aria-label="Default select example" required>
                        <option selected disabled value="">-- Select Page --</option>
                        @foreach (var item in ViewBag.LstPageMaster)
                        {
                            <option value="@item.PageID">@item.PageName</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Page</div>
            </div>

        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label"><i class="cv-description me-1"></i>Description</label>
                <div class="accordion accordion-flush" id="accordionFlushExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button border" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                Worksheet #1: Process Identification and Criticality Assessment
                            </button>
                        </h2>
                        <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                            <div class="accordion-body">
                                <textarea id="editor" asp-for="SectionDescription"></textarea>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>

<script src="~/lib/ckeditor/js/ckeditor.js"></script>
<script>

    ClassicEditor.create(document.querySelector("#editor"));
    $(document).ready(function() {
     // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for addBIASurveySection form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('addBIASurveySection');
                if (!form) {
                    console.error("Form not found with ID: addBIASurveySection");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateEmail function similarly
                const originalValidateEmail = window.BCMValidation.validateEmail;
                window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateEmail(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validatePatternInput function similarly
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidatePatternInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    if (!isValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }
     });
    // document.querySelector("form").addEventListener("submit", (e) => {
    //     e.preventDefault();
    //     console.log(document.getElementById("editor").value);
    // });

</script>
