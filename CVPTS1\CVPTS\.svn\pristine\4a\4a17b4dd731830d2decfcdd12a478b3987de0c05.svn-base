﻿$(function () {
    const datasetURL = {
        getPagination: "/BCMAdministration/Dataset/GetAll",
        getTableNames: "/BCMAdministration/Dataset/GetTableAccessData",
        getSchemaNames: "/BCMAdministration/Dataset/GetSchemaNamesByTableName",
        getTableColumns: "/BCMAdministration/Dataset/GetTableColumns",
        nameExistUrl: "/BCMAdministration/Dataset/DataSetNameExist",
        runQuery: "/BCMAdministration/Dataset/RunQuery",
        datasetCreateOrUpdate: "/BCMAdministration/Dataset/CreateOrUpdate",
        datasetDelete: "/BCMAdministration/Dataset/Delete",
        getById: "/BCMAdministration/Dataset/GetById"
    };
    let dbValue = '';
    let queryResult = '';
    $(document).ready(function () {

        const dataTable = $('#datasetTable').DataTable({
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            processing: true,
            serverSide: false,
            filter: true,
            order: [],
            ajax: {
                type: "GET",
                url: datasetURL.getPagination,
                dataType: "json",
                dataSrc: function (json) {
                    return json?.data || [];
                }
            },
            columns: [
                {
                    data: null,
                    name: "Sr. No.",
                    orderable: false,
                    render: function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    }
                },
                {
                    data: "dataSetName",
                    name: "Name",
                    render: (data, type) =>
                        type === 'display' ? (data || 'NA') : data
                },
                {
                    data: "description",
                    name: "Description",
                    render: (data, type) =>
                        type === 'display'
                            ? `<span title="${data || 'NA'}" class="text-truncate" style="max-width:450px; display:inline-block">${data || 'NA'}</span>`
                            : data
                },
                {
                    data: "storedQuery",
                    name: "Stored Query",
                    render: (data, type) =>
                        type === 'display'
                            ? `<span title="${data || 'NA'}" class="text-truncate" style="max-width:600px; display:inline-block">${data || 'NA'}</span>`
                            : data
                },
                {
                    data: null,
                    orderable: false,
                    render: function (data, type, row) {
                        return `
                    <div class="d-flex align-items-center gap-2">
                        <span class="btn-action btnEdit" role="button" data-id="${row.id}" data-bs-target="#Modal">
                            <i class="cv-edit" title="Edit"></i>
                        </span>
                        <span class="btn-action btnDelete" role="button" data-id="${row.id}" data-bs-target="#Modal">
                            <i class="cv-delete text-danger" title="Delete"></i>
                        </span>
                    </div>`;
                    }
                }
            ],
            initComplete: function () {
                setPaginationButtonTitles();
            }
        });
        dataTable.on('draw.dt', function () {
            setPaginationButtonTitles();
        });
        function setPaginationButtonTitles() {
            $('li.paginate_button.previous > a').attr('title', 'Previous');
            $('li.paginate_button.next > a').attr('title', 'Next');
        }

        fetchDatabaseType();
        function fetchDatabaseType() {
            return $.ajax({
                url: '/BCMAdministration/Dataset/GetDatabaseType',
                method: 'GET',
                success: function (res) {
                    if (res?.success && res?.dbType) {
                        dbValue = res.dbType.toLowerCase();
                        console.log("Detected DB Type:", dbValue);
                    } else {
                        dbValue = 'unknown';
                    }
                },
                error: function () {
                    dbValue = 'unknown';
                    console.error("Failed to fetch DB type.");
                }
            });
        }
        //function updateQuery() {
        //    const $schema = $('#schemaName');
        //    const $table = $('#tableName');
        //    const $queryBox = $('#datasetStoredQuery');
        //    const $leftList = $('#leftList');
        //    const $rightList = $('#rightList');
        //    if ($leftList.prop('options').length === 0 && $rightList.prop('options').length > 0) {
        //        return;
        //    }

        //    let schema = $schema.val();
        //    let table = $table.val();
        //    let columns = queryResult || '*'; 
        //    let query = '';

        //    if (!schema || !table) {
        //        $queryBox.val('');
        //        return;
        //    }

        //    switch ((dbValue || '').toLowerCase()) {
        //        case 'mysql':
        //            query = `SELECT ${columns} FROM \`${schema}\`.\`${table}\``;
        //            break;
        //        case 'oracle':
        //            const oracleColumns = columns !== '*'
        //                ? columns.split(',').map(col => `"${col.trim()}"`).join(', ')
        //                : '*';
        //            query = `SELECT ${oracleColumns} FROM "${schema}"."${table}"`;
        //            break;
        //        default: 
        //            query = `SELECT ${columns} FROM [${schema}].[dbo].[${table}]`;
        //            break;
        //    }

        //    $queryBox.val(query);
        //    $('#StoredQuery-error').text('').removeClass('field-validation-error');
        //}

        function checkColumnData() {
            setTimeout(function () {
                $('#datasetLoader').addClass('d-none').hide();
                if ($("#datasetColumnName_to").children().length && $("#rightList").children().length) {
                    $("#btnRunQuery").show();
                } else {

                }
            }, 100);
        }
        function getAllQuery() {
            const schema = $('#schemaName').val() || $('#schemaName').text();
            const db = dbValue?.toLowerCase();
            const tableNames = $('#tableName option:selected').map(function () {
                return $(this).text().trim();
            }).get();

            if (tableNames.length === 0) return;

            const queries = tableNames.map(table => {
                if (db === 'mysql') {
                    return `SELECT * FROM \`${schema}\`.\`${table}\``;
                } else if (db === 'oracle') {
                    return `SELECT * FROM "${schema}"."${table}"`;
                } else {
                    return `SELECT * FROM [${schema}].[dbo].[${table}]`;
                }
            });

            $("#datasetStoredQuery").val(queries.join('; '));
        }

        function updateQuery() {
            const schema = $('#schemaName').val()?.trim();
            const tableNames = $('#tableName option:selected').map(function () {
                return $(this).text().trim();
            }).get();
            const db = dbValue?.toLowerCase();

            if (!schema || tableNames.length === 0) return;

            const columns = $('#rightList li').map(function () {
                return $(this).text().trim();
            }).get();

            const formattedColumns = columns.length
                ? (db === 'oracle'
                    ? columns.map(c => `"${c}"`).join(', ')
                    : columns.join(', '))
                : '*';

            const queries = tableNames.map(table => {
                if (db === 'mysql') {
                    return `SELECT ${formattedColumns} FROM \`${schema}\`.\`${table}\``;
                } else if (db === 'oracle') {
                    return `SELECT ${formattedColumns} FROM "${schema}"."${table}"`;
                } else {
                    return `SELECT ${formattedColumns} FROM [${schema}].[dbo].[${table}]`;
                }
            });

            $('#datasetStoredQuery').val(queries.join('; '));
            $('#StoredQuery-error').text('').removeClass('field-validation-error');
        }

        function loadTablesBySchema1(schemaName) {
            return new Promise((resolve, reject) => {
                if (!schemaName) return resolve([]);

                const tableSelect = $('#tableName')[0]?.selectize;
                if (!tableSelect) return resolve([]);

                $.ajax({
                    url: datasetURL.getTableNames,
                    type: "GET",
                    dataType: "json",
                    success: function (result) {
                        tableSelect.clearOptions();
                        const matchedTables = [];

                        if (result?.success && Array.isArray(result.data)) {
                            const added = new Set();

                            result.data.forEach(table => {
                                const tableSchema = table.schemaName?.trim();
                                const tableName = table.tableName?.trim();

                                if (tableSchema === schemaName && tableName && !added.has(tableName)) {
                                    added.add(tableName);
                                    matchedTables.push(tableName); // ✅ Collect for resolve
                                    tableSelect.addOption({ value: tableName, text: tableName });
                                }
                            });

                            tableSelect.refreshOptions(false);
                            resolve(matchedTables); // ✅ Resolve with matched tables
                        } else {
                            resolve([]); // 🔁 Graceful fallback
                        }
                    },
                    error: function () {
                        reject("Failed to load table list.");
                    }
                });
            });
        }

        function populateModalFields(datasetData) {
            const $name = $('#datasetName');
            const $desc = $('#datasetDesc');
            const $query = $('#datasetStoredQuery');
            const $id = $('#id');
            const $leftList = $('#leftList');
            const $rightList = $('#rightList');

            // Reset form
            $name.add($desc).add($query).add($id).val('');
            $leftList.add($rightList).empty();

            const schemaSelect = $('#schemaName')[0]?.selectize;
            const tableSelect = $('#tableName')[0]?.selectize;
            schemaSelect?.clearOptions(); schemaSelect?.clear();
            tableSelect?.clearOptions(); tableSelect?.clear();

            $name.val(datasetData?.dataSetName || '');
            $desc.val(datasetData?.description || '');
            $id.val(datasetData?.id || '');

            const storedQuery = datasetData?.storedQuery || '';
            const dbType = dbValue?.toLowerCase();
            let schemaData = '', tableData = '', selectedCols = new Set();

            // Extract schema and tables
            const patterns = {
                mysql: /from\s+`([^`]+)`\.`([^`]+)`/gi,
                oracle: /from\s+"([^"]+)"\."([^"]+)"/gi,
                default: /\[([^\]]+)\]\.\[dbo\]\.\[([^\]]+)\]/gi
            };
            const regex = patterns[dbType] || patterns.default;
            const matches = [...storedQuery.matchAll(regex)];

            if (matches.length) {
                schemaData = matches[0][1];
                tableData = matches.map(m => m[2]).join(',');
            }

            schemaSelect?.addOption({ value: schemaData, text: schemaData });
            schemaSelect?.setValue(schemaData);

            const tableList = tableData.split(',').map(t => t.trim()).filter(Boolean);

            loadTablesBySchema1(schemaData).then(allTables => {
                if (!tableSelect) return;
                tableSelect.clearOptions(); tableSelect.clear();

                allTables.forEach(table =>
                    tableSelect.addOption({ value: table, text: table })
                );

                tableSelect.refreshOptions(false);
                tableSelect.setValue(tableList.filter(t => allTables.includes(t)));
            });

            let isSelectAll = /select\s+\*/i.test(storedQuery);
            if (!isSelectAll) {
                const selectMatch = storedQuery.match(/select\s+(.+?)\s+from/i);
                if (selectMatch?.[1]) {
                    selectMatch[1]
                        .split(',')
                        .map(c => c.trim().replace(/["[\]]/g, '').split(/\s+as\s+/i).pop().toLowerCase())
                        .forEach(col => selectedCols.add(col));
                }
            }

            // Fetch column list and fill UI
            const fetchColumns = () => new Promise((resolve, reject) => {
                if (!schemaData || !tableList.length) {
                    reject('Invalid schema or table name(s)');
                    return;
                }

                const seen = new Set(), leftItems = [], rightItems = [];

                const ajaxCalls = tableList.map(table =>
                    $.ajax({
                        url: datasetURL.getTableColumns,
                        type: "POST",
                        contentType: "application/json",
                        dataType: "json",
                        data: JSON.stringify({ schemaName: schemaData, tableName: table })
                    }).then(res => {
                        if (res?.success && Array.isArray(res.data)) {
                            res.data.forEach(rawCol => {
                                const col = (rawCol || '').trim(), colLower = col.toLowerCase();
                                if (!col || seen.has(colLower)) return;

                                seen.add(colLower);
                                const li = `<li draggable="true">${col}</li>`;
                                (isSelectAll || selectedCols.has(colLower))
                                    ? rightItems.push(li)
                                    : leftItems.push({ colLower, html: li });
                            });
                        }
                    })
                );

                Promise.all(ajaxCalls)
                    .then(() => {
                        setTimeout(() => {
                            try {
                                $leftList.empty();
                                $rightList.empty();

                                const rightSet = new Set(
                                    rightItems.map(item => item.match(/>(.*?)<\/li>/)?.[1]?.trim().toLowerCase())
                                );

                                const filteredLeft = leftItems
                                    .filter(({ colLower }) => !rightSet.has(colLower))
                                    .map(({ html }) => html);

                                $leftList.append(filteredLeft.join(''));
                                $rightList.append(rightItems.join(''));
                                resolve();
                            } catch (e) {
                                reject(e);
                            }
                        }, 400);
                    })
                    .catch(() => reject('Error loading columns for one or more tables'));
            });

            // Set query and trigger fetch
            setTimeout(() => $query.val(storedQuery), 1000);

            fetchColumns()
                .then(() => {
                    updateQuery();
                    checkColumnData();
                })
                .catch(err => {
                    console.error("Error in column binding:", err);
                    showNotification('error', typeof err === 'string' ? err : 'Column load error');
                });
        }


        function validateQuery(query) {
            let queryPattern;
            switch (dbValue?.toLowerCase()) {
                case 'oracle':
                    queryPattern = /^SELECT\s+.+\s+FROM\s+"[^"]+"\."[^"]+"(?:\s+WHERE\s+.+)?$/i;
                    break;
                case 'mysql':
                    queryPattern = /^SELECT\s+.+\s+FROM\s+`[^`]+`\.`[^`]+`(?:\s+WHERE\s+.+)?$/i;
                    break;
                case 'mssql':
                    queryPattern = /^SELECT\s+.+\s+FROM\s+(\[[^\]]+\]\.)?\[dbo\]\.\[[^\]]+\](\s+\w+)?(,\s*(\[[^\]]+\]\.)?\[dbo\]\.\[[^\]]+\](\s+\w+)?)*\s*$/i;
                    break;

                default:
                    $('#StoredQuery-error').text('Unsupported database type').addClass('field-validation-error');
                    return false;
            }

            if (!query?.trim()) {
                $('#StoredQuery-error').text('Enter query').addClass('field-validation-error');
                return false;
            }

            // Split multi-query string (on semicolon or newline)
            const statements = query.trim().split(/;\s*|\n+/).filter(Boolean);

            const isValid = statements.every(statement => queryPattern.test(statement.trim()));

            if (!isValid) {
                $('#StoredQuery-error').text('Enter valid query').addClass('field-validation-error');
                return false;
            }

            $('#StoredQuery-error').text('').removeClass('field-validation-error');
            return true;
        }

        async function RunQuery(query) {
            //validateQuery

            return new Promise((resolve, reject) => {
                $.ajax({
                    method: "GET",
                    url: datasetURL.runQuery,
                    data: { runQuery: query },
                    dataType: "json",
                    success: function (response) {
                        $("#datasetWrapper").empty();

                        if (response?.success && response?.data) {
                            let tableList = typeof response.data.tableValue === "string"
                                ? JSON.parse(response.data.tableValue)
                                : response.data.tableValue;

                            if (!tableList || tableList.length === 0) {
                                const imageHtml = `<img src="../../img/isomatric/no_data_found.svg" style="width: 385px;padding-top: 81px;" class="Card_NoData_Img">`;
                                $("#datasetWrapper").css('text-align', 'center').html(imageHtml).show();
                                return resolve([]);
                            }

                            tableList.forEach((table, index) => {
                                if (!table || table.length === 0) return;

                                let columns = Object.keys(table[0]);

                                let tableHtml = `
                            <div class="card mb-4 shadow-sm p-3 bg-white rounded">
                                <h6 class="fw-bold">Result Table ${index + 1}</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead class="table-light">
                                            <tr>${columns.map(col => `<th>${col}</th>`).join("")}</tr>
                                        </thead>
                                        <tbody>
                                            ${table.map(row =>
                                    `<tr>${columns.map(col => `<td>${row[col] ?? ""}</td>`).join("")}</tr>`
                                ).join("")}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        `;

                                $("#datasetWrapper").append(tableHtml);
                            });

                            resolve(tableList);
                        } else {
                            $('#StoredQuery-error').text('Enter valid query').addClass('field-validation-error');
                            resolve([]);
                        }
                    },
                    error: function (xhr) {
                        $('#StoredQuery-error').text('An error occurred while running query').addClass('field-validation-error');
                        console.error("RunQuery error:", xhr);
                        reject([]);
                    }
                });
            });
        }


        $('#datasetColumnName_rightSelected').on('click', function () {
            $('#leftList li.selected').each(function () {
                $(this).removeClass('selected').appendTo('#rightList');
            });
            updateQuery();
            checkColumnData();
        });

        $('#datasetColumnName_leftSelected').on('click', function () {
            $('#rightList li.selected').each(function () {
                $(this).removeClass('selected').appendTo('#leftList');
            });
            setTimeout(function () {
                if ($('#rightList').children().length === 0) {
                    $('#datasetStoredQuery').val('');
                } else {
                    if ($('#leftList').children().length || $('#rightList').children().length) {
                        updateQuery();
                    }
                }
            }, 100);

            checkColumnData();
        });

        $('#datasetColumnName_rightAll').on('click', function () {
            getAllQuery();
            let rightDDL = $("#datasetColumnName option");
            let newQueryResult = rightDDL.map((_, opt) => opt.text).get().join(',');
            queryResult = queryResult ? `${queryResult},${newQueryResult}` : newQueryResult;
            checkColumnData();
        });

        $('#datasetColumnName_leftAll').on('click', function () {
            queryResult = '';
            $("#datasetStoredQuery").val("");
            checkColumnData();
        });

        // Bind Create Dataset button click
        $('#createDataset').on('click', () => {
            ['#datasetName', '#datasetDesc', '#datasetStoredQuery', '#searchLeft', '#searchRight'].forEach(id => {
                $(id).val('');
            });
            $('#leftList, #rightList').empty();
            ['#schemaName', '#tableName'].forEach(selector => {
                const selectize = $(selector)[0]?.selectize;
                if (selectize) {
                    selectize.clearOptions();
                    selectize.clear();
                } else {
                    $(selector).empty();
                }
            });
            loadSchemas();
        });

        $('.loadcls').on("click", function () {
            $('#AddModal').modal('show');
        });
        $('#modelcls').on("click", function () {
            location.reload();
        });

        $('#btnDatasetSave').on("click", function () {
            const id = datasetId; // assuming this is the dataset Id
            const name = $("#datasetName").val()?.trim();
            const description = $("#datasetDesc").val()?.trim();
            const storedQuery = $("#datasetStoredQuery").val()?.trim();
            //const tableName = $("#tableName option:selected").text()?.trim();
            const tableName = $('#tableName option:selected').map(function () {
                return $(this).text().trim();
            }).get().join(', ');
            const tableId = $("#tableName").val();
            const schemaName = $("#schemaName").val();

            if (!name || !storedQuery || !tableName) {
                showNotification('warning', 'Please fill in required fields.');
                return;
            }

            $.ajax({
                url: datasetURL.datasetCreateOrUpdate,
                type: "POST",
                dataType: "json",
                data: {
                    Id: id,
                    DataSetName: name,
                    Description: description,
                    TableName: tableName,
                    StoredQuery: storedQuery,
                    StoredProcedureName: "",
                    TableAccessId: tableId,
                    IsActive: true
                },
                success: function (result) {
                    if (result?.success) {
                        showNotification('success', 'Dataset saved successfully.');
                        $('#AddModal').modal('hide');
                        location.reload();

                    } else {
                        showNotification('error', result.message || 'Failed to save dataset.');
                    }
                },
                error: function () {
                    showNotification('error', 'An error occurred while saving dataset.');
                }
            });
        });
        $('#tableName').selectize({
            placeholder: 'Select Table Name',
            plugins: ['remove_button'],
            maxItems: null, // allow multiple selections
            persist: false,
            create: false
        });
        let datasetId;
        $('#datasetTable').on('click', '.btnEdit', function () {
            datasetId = $(this).data('id');
            $.ajax({
                url: datasetURL.getById,
                type: "GET",
                dataType: "json",
                data: { id: datasetId },
                success: function (result) {
                    if (result?.success) {
                        populateModalFields(result.data);
                        $('#SaveFunction').text('Update');
                        $('#AddModal').modal('show');
                        checkColumnData();
                    } else {
                        showNotification('error', result.message || 'Failed to load dataset.');
                    }
                },
                error: function () {
                    showNotification('error', 'An error occurred while loading dataset.');
                }
            });
        });
        // Delete dataset entry
        $('#datasetTable').on('click', '.btnDelete', function () {
            const datasetId = $(this).data('id');
            const confirmed = confirm("Are you sure you want to delete this dataset?");
            if (!confirmed || !datasetId) return;
            $.ajax({
                url: datasetURL.datasetDelete,
                type: "POST",
                dataType: "json",
                data: { id: datasetId },
                success: function (result) {
                    if (result?.success) {
                        showToast('success', 'Dataset deleted successfully.');
                        $('#datasetTable').DataTable().ajax.reload();
                    } else {
                        showToast('error', result.message || 'Failed to delete dataset.');
                    }
                },
                error: function () {
                    showToast('error', 'An error occurred while deleting dataset.');
                }
            });
        });

        $('#schemaName').on("change", function () {
            const selectedSchema = $(this).val();
            loadTablesBySchema(selectedSchema);
        });

        $('#tableName').on('change', function () {
            $('#datasetStoredQuery').val('');
            $('#leftList').empty();
            $('#rightList').empty();

            const selectedTables = $('#tableName').val(); // gets array of selected values

            //if (!Array.isArray(selectedTables) || selectedTables.length === 0) {
            //    showNotification('warning', 'Please select at least one table.');
            //    return;
            //}

            selectedTables.forEach(tablename => {
                $.ajax({
                    url: datasetURL.getTableColumns,
                    type: "POST",
                    contentType: "application/json",
                    dataType: "json",
                    data: JSON.stringify({ tableName: tablename }),
                    success: function (result) {
                        if (result?.success && Array.isArray(result.data)) {
                            result.data.forEach(column => {
                                $('#leftList').append(`<li draggable="true">${column}</li>`);
                            });
                        }// else {
                        //    showNotification('warning', `No columns found for table: ${tablename}`);
                        //}
                    },
                    error: function () {
                        showNotification('error', `Failed to load column data for table: ${tablename}`);
                    }
                });
            });
        });


        $('#btnRunQuery').on('click', async () => {
            const query = $('#datasetStoredQuery').val()?.trim();

            if (!query) {
                $('#StoredQuery-error').text('Please provide a stored query.').addClass('field-validation-error');
                return;
            }

            $('#btnRunQuery, #tableName, #schemaName').prop('disabled', true);
            $('#datasetLoader').removeClass('d-none').show();

            $("#tablerow").empty();
            $("#tableData").empty();

            $("#datasetTable").hide();
            $("#datasetWrapper").show();

            try {
                const result = await RunQuery(query);

                if (result && result.length > 0) {
                    // Show modal
                    $('#AddModal').modal('hide');
                    $('#QueryListModal').modal('show');

                    // Scroll reset
                    $('#QueryListModal').off('shown.bs.modal').on('shown.bs.modal', function () {
                        $(this).find('.modal-body > div[style*="overflow:auto"]').scrollTop(0);
                    });

                    // Build headers from first row keys
                    const headers = Object.keys(result[0]);
                    headers.forEach(h => {
                        $('#tablerow').append(`<th>${h}</th>`);
                    });

                    // Build rows
                    result.forEach(row => {
                        const rowHtml = headers.map(h => `<td>${row[h]}</td>`).join('');
                        $('#tableData').append(`<tr>${rowHtml}</tr>`);
                    });
                } else {
                    showToast('No records returned from the query.', 'info');
                }
            } catch (error) {
                console.error(error);
                showToast('Error running query.', 'error');
            } finally {
                $('#datasetLoader').addClass('d-none').hide();
                $('#btnRunQuery, #tableName, #schemaName').prop('disabled', false);
            }
        });
        function loadSchemas() {
            const $schemaEl = $('#schemaName');

            // Ensure selectize is initialized
            if (!$schemaEl[0].selectize) {
                $schemaEl.selectize();
            }

            const schemaSelect = $schemaEl[0].selectize;

            $.ajax({
                url: datasetURL.getTableNames,
                type: "GET",
                dataType: "json",
                success: function (result) {
                    if (result?.success && Array.isArray(result.data)) {
                        schemaSelect.clearOptions();
                        schemaSelect.addOption({ value: "", text: "Select schema" });

                        const addedSchemas = new Set();

                        result.data.forEach(table => {
                            const schemaName = table.schemaName?.trim();
                            if (schemaName && !addedSchemas.has(schemaName)) {
                                addedSchemas.add(schemaName);
                                schemaSelect.addOption({ value: schemaName, text: schemaName });
                            }
                        });
                    }
                },
                error: function () {
                    showNotification('error', 'Failed to load schema names from TableAccess.');
                }
            });
        }

        //function loadTablesBySchema(schemaName) {
        //    if (!schemaName) return;

        //    $.ajax({
        //        url: datasetURL.getTableNames,
        //        type: "GET",
        //        dataType: "json",
        //        success: function (result) {
        //            if (result?.success && Array.isArray(result.data)) {
        //                const tableSelect = $('#tableName')[0].selectize;
        //                tableSelect.clearOptions();

        //                const added = new Set();

        //                result.data.forEach(table => {
        //                    const tableSchema = table.schemaName?.trim();
        //                    const tableName = table.tableName?.trim();
        //                    if (tableSchema === schemaName && tableName && !added.has(tableName)) {
        //                        added.add(tableName);
        //                        tableSelect.addOption({ value: tableName, text: tableName });
        //                    }
        //                });

        //                // Optional: clear previous selection
        //                tableSelect.setValue('');
        //            }
        //        }
        //    });
        //}

        function loadTablesBySchema(schemaName) {
            return new Promise((resolve, reject) => {
                if (!schemaName) return resolve();

                const tableSelect = $('#tableName')[0]?.selectize;
                if (!tableSelect) return resolve(); // no selectize instance

                $.ajax({
                    url: datasetURL.getTableNames,
                    type: "GET",
                    dataType: "json",
                    success: function (result) {
                        tableSelect.clearOptions();

                        if (result?.success && Array.isArray(result.data)) {
                            const added = new Set();

                            result.data.forEach(table => {
                                const tableSchema = table.schemaName?.trim();
                                const tableName = table.tableName?.trim();
                                if (tableSchema === schemaName && tableName && !added.has(tableName)) {
                                    added.add(tableName);
                                    tableSelect.addOption({ value: tableName, text: tableName });
                                }
                            });

                            tableSelect.refreshOptions(false);
                            resolve();
                        } else {
                            reject("No tables found.");
                        }
                    },
                    error: function () {
                        reject("Failed to load table list.");
                    }
                });
            });
        }


        function showNotification(message, type) {
            if (typeof toastr !== 'undefined') {
                if (type === 'success') toastr.success(message);
                else if (type === 'error') toastr.error(message);
                else toastr.info(message);
            } else if (typeof notificationAlert === 'function') {
                notificationAlert(type, message);
            } else {
                alert((type === 'success' ? '✅ ' : type === 'error' ? '❌ ' : '') + message);
            }
        }
    });
});