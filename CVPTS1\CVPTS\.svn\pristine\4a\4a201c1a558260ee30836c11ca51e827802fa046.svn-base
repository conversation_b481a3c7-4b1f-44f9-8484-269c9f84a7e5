﻿@model BCM.BusinessClasses.SubFunction

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}
<form id="addSubDepartmentForm" asp-action="AddSubDepartment" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="modal-body pt-0">
        <div class="row row-cols-2">
            <div class="col">
                <div class="form-group">
                    <label for="validationCustom01" class="form-label">Sub Department Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Department Name" asp-for="SubFunctionName" required>
                    </div>
                    <div class="invalid-feedback">Enter Sub Department Name</div>
                </div>
                <div class="form-group">
                    <label for="validationCustom01" class="form-label">Department Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-department"></i></span>
                        <select class="form-select form-control selectized" id="departmentDDL" autocomplete="off" aria-label="Default select example" asp-for="DepartmentID" required>
                            <option selected value="0">-- All Departments --</option>
                            @foreach (var objDepartment in ViewBag.DepartmentInfo)
                            {
                                <option value="@objDepartment.Value">@objDepartment.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Enter Department Name</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Alt Owner</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" id="altheadlist" autocomplete="off" aria-label="Default select example" asp-for="AlternateOwnerId" required>
                            <option selected value="0">-- All Resources --</option>
                            @foreach (var objResource in ViewBag.ResourcesInfo)
                            {
                                <option value="@objResource.Value">@objResource.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Select Alt Owner</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-mail"></i></span>
                        <input type="email" id="altcompanyEmail" class="form-control" readonly placeholder="Enter Alt Owner Email" asp-for="AltownerEmail">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Mobile</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-phone"></i></span>
                        <input type="text" id="altmobilePhone" class="form-control" readonly placeholder="Enter Alt Owner Mobile" asp-for="AltownerMobile">
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Unit Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-unit"></i></span>
                        <select id="ddlUnit" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example" asp-for="UnitID" required>
                            <option selected value="0">-- All Units --</option>
                            @foreach (var objUnit in ViewBag.OrgUnit)
                            {
                                <option value="@objUnit.Value">@objUnit.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Enter Unit Name</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Owner</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" id="headlist" autocomplete="off" aria-label="Default select example" asp-for="OwnerId" required>
                            <option selected value="0">-- All Resources --</option>
                            @foreach (var objResource in ViewBag.ResourcesInfo)
                            {
                                <option value="@objResource.Value">@objResource.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Select Owner</div>

                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-mail"></i></span>
                        <input type="email" id="companyEmail" class="form-control" readonly placeholder="Enter Owner Email" asp-for="OwnerEmail">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Mobile</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-phone"></i></span>
                        <input type="text" id="mobilePhone" class="form-control" readonly placeholder="Enter Owner Mobile" asp-for="OwnerMobile">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button id="btnsave" type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function () {
        $('#headlist,#altheadlist').change(function () {
            var selectedDDL = $(this).attr('id');
            var iId = $(this).val();
            $.ajax({
                url: '@Url.Action("GetResourceDetails", "SubDepartment")',
                //url: '/BCMOrgStructure/SubDepartment/GetResourceDetails' + '/' + iId,
                type: "GET",
                data: { iId: iId },
                success: function (data) {
                    if (data) {
                        if (selectedDDL == "headlist") {
                            $("#companyEmail").val(data.mail);
                            $("#mobilePhone").val(data.mobile);
                        } else if (selectedDDL == "altheadlist") {
                            $("#altcompanyEmail").val(data.mail);
                            $("#altmobilePhone").val(data.mobile);
                        }
                    }
                    else {
                        console.log("Error While Binding Data.");
                    }
                },
                error: function (data) {
                    console.log('Error Is Invoked');
                }
            });
        });

        $('#ddlUnit').change(function(){
            var iUnitID = $(this).val();
            $.ajax({
                url: '@Url.Action("GetAllDepartments", "SubDepartment")',
                type: 'GET',
                data: { iUnitID: iUnitID },
                success: function (response) {
                    debugger;
                                        let selectizeInstance = $('#departmentDDL')[0].selectize;
                                        selectizeInstance.clear();
                                        selectizeInstance.clearOptions();
                                        selectizeInstance.addOption({ value: "0", text: "-- All Departments --" });
                                        selectizeInstance.addItem("0");

                                        response && response.forEach(({ departmentID, departmentName }) => {
                                             if (departmentID && departmentName) {
                                                 selectizeInstance.addOption({ value: departmentID, text: departmentName });
                                             }
                                        });
                                    }
                // success: function (data) {
                //     var department = $('#departmentDDL');
                //     department.empty();
                //     department.append('<option value="0">-- All Departments --</option>');
                //     $.each(data, function (index, item) {
                //         department.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                //     });
                // }
            });
        });

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for AddSubDepartment form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('addSubDepartmentForm');
                if (!form) {
                    console.error("Form not found with ID: addSubDepartmentForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                const feedbackElements = {};

                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        const message = element.textContent.trim();

                        // Primary key: Use input ID (now that all required fields have IDs)
                        if (input.id) {
                            customMessages[input.id] = message;
                            feedbackElements[input.id] = element;
                            console.log("Stored custom message for ID", input.id, ":", message);
                        }

                        // Fallback: Use asp-for attribute
                        const aspFor = input.getAttribute('asp-for');
                        if (aspFor) {
                            customMessages[aspFor] = message;
                            feedbackElements[aspFor] = element;
                            console.log("Stored custom message for asp-for", aspFor, ":", message);
                        }

                        // Store original message on element for direct access
                        element.originalMessage = message;
                    }
                });

                // Helper function to restore custom message for an input
                function restoreCustomMessage(input) {
                    let message = null;
                    let feedbackElement = null;

                    // Priority 1: Use input ID (most reliable now)
                    if (input.id && customMessages[input.id]) {
                        message = customMessages[input.id];
                        feedbackElement = feedbackElements[input.id];
                        console.log("Found custom message by ID", input.id, ":", message);
                    }
                    // Priority 2: Use asp-for attribute
                    else if (input.getAttribute('asp-for') && customMessages[input.getAttribute('asp-for')]) {
                        const aspFor = input.getAttribute('asp-for');
                        message = customMessages[aspFor];
                        feedbackElement = feedbackElements[aspFor];
                        console.log("Found custom message by asp-for", aspFor, ":", message);
                    }
                    // Priority 3: Find feedback element in the same form group
                    else {
                        const formGroup = input.closest('.form-group');
                        feedbackElement = formGroup?.querySelector('.invalid-feedback');
                        if (feedbackElement && feedbackElement.originalMessage) {
                            message = feedbackElement.originalMessage;
                            console.log("Found custom message by form group for", input.id || input.getAttribute('asp-for'), ":", message);
                        }
                    }

                    if (message && feedbackElement) {
                        feedbackElement.textContent = message;
                        // Only show if the input is actually invalid
                        if (input.classList.contains('is-invalid')) {
                            feedbackElement.style.display = 'block';
                        }
                        console.log("✅ Restored custom message:", message, "for input:", input.id || input.getAttribute('asp-for'));
                        return true;
                    } else {
                        console.warn("❌ Could not restore custom message for input:", input.id || input.getAttribute('asp-for'));
                        return false;
                    }
                }

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Skip email and mobile fields from validation
                    const skipFields = [
                        "companyEmail", "mobilePhone",
                        "altcompanyEmail", "altmobilePhone"
                    ];

                    if (skipFields.includes(input.id)) {
                        console.log("Skipping validation for", input.id);
                        return true; // Always return true for skipped fields
                    }

                    // For select fields, only validate if forced or if they have value "0"
                    if (input.tagName === 'SELECT' && !forceValidation) {
                        const value = input.value;
                        if (value && value !== "0") {
                            // Has valid selection, clear any validation errors
                            input.classList.remove('is-invalid');
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement && !feedbackElement.textContent.includes('cannot be the same person')) {
                                feedbackElement.style.display = 'none';
                            }
                            return true;
                        }
                    }

                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        restoreCustomMessage(input);
                    }

                    return result;
                };

                // Override the validateEmail function similarly
                const originalValidateEmail = window.BCMValidation.validateEmail;
                window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                    // Skip email fields from validation
                    const skipFields = [
                        "companyEmail",
                        "altcompanyEmail"
                    ];

                    if (skipFields.includes(input.id)) {
                        console.log("Skipping email validation for", input.id);
                        return true; // Always return true for skipped fields
                    }

                    // Get the result from the original function
                    const result = originalValidateEmail(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        restoreCustomMessage(input);
                    }

                    return result;
                };

                // Override the validatePatternInput function similarly
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                    // Skip mobile fields from validation
                    const skipFields = [
                        "mobilePhone",
                        "altmobilePhone"
                    ];

                    if (skipFields.includes(input.id)) {
                        console.log("Skipping pattern validation for", input.id);
                        return true; // Always return true for skipped fields
                    }

                    // Get the result from the original function
                    const result = originalValidatePatternInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        restoreCustomMessage(input);
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        restoreCustomMessage(input);
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);

                    // Validate that roles are not assigned to the same person
                    const rolesValid = validateRolesDifferent();

                    console.log("Form validation result:", isValid);
                    console.log("Roles validation result:", rolesValid);

                    if (!isValid || !rolesValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });

            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        // Function to validate that roles are assigned to different people
        function validateRolesDifferent() {
            var ownerID = $('#headlist').val();
            var altOwnerID = $('#altheadlist').val();

            var isValid = true;

            // Clear previous error states for role validation
            clearRoleValidationErrors();

            // Validate Owner vs Alt Owner
            if (ownerID && altOwnerID && ownerID !== "" && ownerID !== "0" && altOwnerID !== "" && altOwnerID !== "0" && ownerID === altOwnerID) {
                showRoleValidationError('#headlist', 'Owner and Alternate Owner cannot be the same person');
                showRoleValidationError('#altheadlist', 'Owner and Alternate Owner cannot be the same person');
                isValid = false;
            }

            return isValid;
        }

        // Function to show role validation error
        function showRoleValidationError(selector, message) {
            var element = $(selector);
            element.addClass('is-invalid').removeClass('is-valid');

            var formGroup = element.closest('.form-group');
            var feedbackElement = formGroup.find('.invalid-feedback');
            if (feedbackElement.length > 0) {
                feedbackElement.text(message);
                // Use CSS classes instead of forcing display
                feedbackElement.addClass('custom-validation show').show();
            }
        }

        // Function to clear role validation errors
        function clearRoleValidationErrors() {
            var roleSelectors = ['#headlist', '#altheadlist'];

            roleSelectors.forEach(function(selector) {
                var element = $(selector);
                var formGroup = element.closest('.form-group');
                var feedbackElement = formGroup.find('.invalid-feedback');

                // Only clear if the current message is a role validation error
                if (feedbackElement.length > 0 && feedbackElement.text().includes('cannot be the same person')) {
                    element.removeClass('is-invalid');
                    // Use CSS classes for proper hiding
                    feedbackElement.removeClass('custom-validation show').hide();

                    // Restore original message
                    var originalMessages = {
                        '#headlist': 'Select Owner',
                        '#altheadlist': 'Select Alt Owner'
                    };

                    if (originalMessages[selector]) {
                        feedbackElement.text(originalMessages[selector]);
                    }
                }
            });
        }

        // Add event listeners for real-time validation
        $('#headlist, #altheadlist').on('change', function() {
            var $this = $(this);

            // Clear validation state when user makes a selection
            if ($this.val() && $this.val() !== "") {
                $this.removeClass('is-invalid');
                var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be the same person')) {
                    feedbackElement.removeClass('custom-validation show').hide();
                }
            }

            // Small delay to allow the change to complete, then validate roles
            setTimeout(function() {
                validateRolesDifferent();
            }, 100);
        });

        // Prevent validation messages from showing on page load
        $(window).on('load', function() {
            // Hide all validation messages that might be showing incorrectly
            $('.invalid-feedback').each(function() {
                var $feedback = $(this);
                var $input = $feedback.closest('.form-group').find('input, select');

                // Only hide if the input is not actually invalid
                if ($input.length > 0 && !$input.hasClass('is-invalid')) {
                    $feedback.hide();
                }
            });
        });
    });
</script>