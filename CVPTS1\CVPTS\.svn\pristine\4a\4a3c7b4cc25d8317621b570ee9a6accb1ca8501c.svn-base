﻿// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

console.log('CriticalProcesses_Chart.js loaded - v3');

// Create chart instance
var criticalProcessesChart = am4core.create("CriticalProcesses_Chart", am4charts.XYChart);
if (criticalProcessesChart.logo) {
    criticalProcessesChart.logo.disabled = true;
}


criticalProcessesChart.colors.list = [
    am4core.color("#845EC2"),
    am4core.color("#D65DB1"),
    am4core.color("#FF6F91")
];

// Make chart responsive
criticalProcessesChart.responsive.enabled = true;
criticalProcessesChart.responsive.rules.push({
    relevant: function (target) {
        if (target.pixelWidth <= 400) {
            return true;
        }
        return false;
    },
    state: function (target, stateId) {
        if (target instanceof am4charts.Chart) {
            var state = target.states.create(stateId);
            state.properties.paddingTop = 0;
            state.properties.paddingRight = 15;
            state.properties.paddingBottom = 5;
            state.properties.paddingLeft = 15;
            return state;
        }
        if (target instanceof am4charts.AxisRendererY) {
            var state = target.states.create(stateId);
            state.properties.inside = true;
            state.properties.maxLabelPosition = 0.99;
            return state;
        }
        return null;
    }
});

// Add data - only two columns: Critical and Non-Critical
criticalProcessesChart.data = [{
    "category": "Critical",
    "value": 65,
}, {
    "category": "Non-Critical",
    "value": 35,
    }];


// Set chart padding to ensure labels fit - reduced padding
criticalProcessesChart.paddingTop = 0;
criticalProcessesChart.paddingBottom = 0;
criticalProcessesChart.paddingLeft = 0;
criticalProcessesChart.paddingRight = 0;

// Reduce background area
criticalProcessesChart.background.fill = am4core.color("#ffffff");
criticalProcessesChart.background.fillOpacity = 0;

// Create axes - horizontal chart like Review in Meetings
var categoryAxis = criticalProcessesChart.xAxes.push(new am4charts.CategoryAxis());
categoryAxis.dataFields.category = "category";
categoryAxis.renderer.grid.template.location = 0;
categoryAxis.renderer.labels.template.fontSize = 12;
categoryAxis.renderer.minGridDistance = 30;
categoryAxis.renderer.grid.template.disabled = true;

var valueAxis = criticalProcessesChart.yAxes.push(new am4charts.ValueAxis());
valueAxis.min = 0;
valueAxis.max = 100; // Range from 0 to 100
valueAxis.strictMinMax = true;
valueAxis.renderer.labels.template.fontSize = 12;
valueAxis.renderer.grid.template.strokeOpacity = 0.1; // Light grid lines for better visibility
valueAxis.renderer.minGridDistance = 50;
valueAxis.renderer.grid.template.disabled = true;
// Set axis intervals to 20
valueAxis.renderer.labels.template.adapter.add("text", function (text) {
    return text;
});
// Set grid intervals
valueAxis.step = 20;

// Make sure the chart renders properly
criticalProcessesChart.events.on("ready", function () {
    console.log("Critical Processes Chart is ready");
});

// Create series
var series = criticalProcessesChart.series.push(new am4charts.ColumnSeries());
series.dataFields.valueY = "value";
series.dataFields.categoryX = "category";
series.columns.template.tooltipText = "{categoryX}: {valueY}";
series.columns.template.propertyFields.fill = "color";
series.columns.template.strokeWidth = 0;
series.columns.template.column.cornerRadiusTopLeft = 25;
series.columns.template.column.cornerRadiusTopRight = 25;

// Make columns wider for better visibility
series.columns.template.width = am4core.percent(20);

// Ensure columns are visible
series.columns.template.adapter.add("fill", function (_, target) {
    return target.dataItem.dataContext.color;
});

// Add column strokes for better visibility
series.columns.template.stroke = am4core.color("#ffffff");
series.columns.template.strokeWidth = 1;

// Add labels on top of columns
var labelBullet = series.bullets.push(new am4charts.LabelBullet());
labelBullet.label.text = "{valueY}";
labelBullet.label.fill = am4core.color("#000000");
labelBullet.label.fontSize = 12;
labelBullet.label.fontWeight = "bold";
labelBullet.label.dy = -5;
labelBullet.locationY = 1;

// Add background to labels
labelBullet.label.background = new am4core.RoundedRectangle();
labelBullet.label.background.fill = am4core.color("#ffffff");
labelBullet.label.background.fillOpacity = 0.7;
//labelBullet.label.background.cornerRadius(3, 3, 3, 3);
labelBullet.label.padding(5, 5, 5, 5);
