﻿@model BCM.BusinessClasses.ReviewReportMomItem
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
<form asp-action="EditMOM" method="post">
    <div class="row">

        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group">
                <label class="form-label">Review Type</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-review-type"></i></span>
                    <select class="form-select form-select-sm" asp-for="ReviewTypeID" asp-items="@(new SelectList(ViewBag.ReviewType,"ReviewTypeID","ReviewType"))">
                        <option selected value="0">-- Select ReviewType --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Owner</div>
            </div>
            <div class="form-group">
                <label class="form-label">Mom Item</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-name"></i></span>
                    <input type="text" class="form-control" asp-for="MomItem" />
                </div>
                <div class="invalid-feedback">Company Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Review Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-calendar"></i></span>
                    <input type="date" class="form-control" asp-for="ReviewDate" />
                </div>
                <div class="invalid-feedback">Review Date</div>
            </div>

        </div>
        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group">
                <label class="form-label">Owner</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-owner"></i></span>
                    @*   <select class="form-select form-select-sm">
                        <option></option>
                        <option>Cerebrum IT Park</option>
                    </select> *@
                    <select class="form-select form-control" autocomplete="off" id="ddlResource" asp-for="OwnerID" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ResourceList,"ResourceId","ResourceName"))">
                        <option selected value="0">-- Select Owner --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Owner</div>
            </div>
            <div class="form-group">
                <label class="form-label">Description</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <textarea class="form-control" style="height:0px" asp-for="Description"></textarea>
                </div>
                <div class="invalid-feedback">Description</div>
            </div>
            <div class="form-group">
                <label class="form-label">Plan Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-calendar"></i></span>
                    <input type="date" class="form-control" asp-for="PlanStartDate" />
                </div>
                <div class="invalid-feedback">Plan Date</div>
            </div>



        </div>
        <div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" value="0" id="flexCheckDefault">
                <label class="form-check-label" for="flexCheckDefault">
                    Is Closed
                </label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault1" >
                <label class="form-check-label" for="flexCheckDefault1">
                    Is Action Item
                </label>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm" id="btnSave">Save</button>
        </div>
    </div>
</form>


<script>
    // $(document).ready(function () {
    //   $('#btnCreate').click(function () {
    //       debugger
    //         $.get('/BCMCompliance/ManagementReview/AddManagementReview', function (data) {
    //             $('.modal-body').html(data);
    //             $('#CreateModal').modal('show');
    //         });
    //     });
    // });

    // $(document).ready(function () {
    //   $('#btnSave').click(function () {
    //       debugger
    //         $.get('/BCMCompliance/ManagementReview/AddManagementReview', function (data) {
    //             $('.modal-body').html(data);
    //             $('#CreateModal').modal('show');
    //         });
    //     });
    // });

</script>