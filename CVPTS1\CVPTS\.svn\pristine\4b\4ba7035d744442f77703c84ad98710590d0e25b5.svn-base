﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BCM.UI.Areas.BCMTeams.Controllers;
[Area("BCMTeams")]
public class BCMGroupmembersController : BaseController
{
    #region Global Declaration

    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;

    #endregion

    public BCMGroupmembersController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    #region BCMGroupmembers 

    [HttpGet]
    public IActionResult BCMGroupmembers(int iGrpMapID)
    {         
        try
        {
            if (iGrpMapID != 0)
            {
                HttpContext.Session.SetString("objGrpMapID", iGrpMapID.ToString());
                ViewData["grpID"] = iGrpMapID;
            }
            List<BCMGroupResources> lstBCMGroupResources = _ProcessSrv.GetBCMGroupMemberResourcesList(0, 0, iGrpMapID, 0, 0, 0, "");

            if (lstBCMGroupResources != null)
            {
                var objUserList = lstBCMGroupResources.Where(x => x.ResourceId != 0).ToList();
                ViewBag.UnitName = lstBCMGroupResources.FirstOrDefault()?.UnitName == "" ? "NA" : lstBCMGroupResources.FirstOrDefault()?.UnitName;
                ViewBag.DepartmentName = lstBCMGroupResources.FirstOrDefault()?.DepartmentName == "" ? "NA" : lstBCMGroupResources.FirstOrDefault()?.DepartmentName;
                ViewBag.SideName = lstBCMGroupResources.FirstOrDefault()?.SiteName == "" ? "NA" : lstBCMGroupResources.FirstOrDefault()?.SiteName;
                ViewBag.GroupName = lstBCMGroupResources.FirstOrDefault()?.GroupName == "" ? "NA" : lstBCMGroupResources.FirstOrDefault()?.GroupName;

                return View(objUserList);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
    }

    #endregion

    #region DeleteBCMGrpMember

    [HttpGet]
    public IActionResult DeleteBCMGrpMember([FromQuery] int iGrpMemId, [FromQuery] string strResourceName)
    {
        int iGrpMapId = Convert.ToInt32(HttpContext.Session.GetString("objGrpMapID"));
        try
        {
            BCMGroupResources objBCMGroupResources = new BCMGroupResources();
            objBCMGroupResources.ResourceId = iGrpMemId;
            objBCMGroupResources.ResourceName = strResourceName;
            return PartialView("_DeleteBCMGroupMember", objBCMGroupResources);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BCMGroupmembers", new { iGrpMapID = iGrpMapId });
    }

    [HttpPost]
    public IActionResult DeleteBCMGrpMember(BCMGroupResources objBCMGroupResources)
    {
        int iGrpMapId = Convert.ToInt32(HttpContext.Session.GetString("objGrpMapID"));
        try
        {            
            if (iGrpMapId > 0 && objBCMGroupResources.ResourceId > 0)
            {
                int iDelete = _ProcessSrv.BCMGroupMembersDelete(_UserDetails.UserID, iGrpMapId, objBCMGroupResources.ResourceId);
            }            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BCMGroupmembers", new { iGrpMapID = iGrpMapId });

    }

    #endregion
}