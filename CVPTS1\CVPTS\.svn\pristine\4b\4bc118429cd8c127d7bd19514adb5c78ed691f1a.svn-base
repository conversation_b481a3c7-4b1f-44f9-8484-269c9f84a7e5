﻿@model BCM.BusinessClasses.SubFunction

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}
<form id="addSubDepartmentForm" asp-action="AddSubDepartment" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="modal-body pt-0">
        <div class="row row-cols-2">
            <div class="col">
                <div class="form-group">
                    <label for="validationCustom01" class="form-label">Sub Department Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Sub Department Name" asp-for="SubFunctionName" id="SubFunctionName" required>
                    </div>
                    <div class="invalid-feedback">Enter Sub Department Name</div>
                </div>
                <div class="form-group">
                    <label for="validationCustom01" class="form-label">Department Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-department"></i></span>
                        <select class="form-select form-control selectized" id="departmentDDL" autocomplete="off" aria-label="Default select example" asp-for="DepartmentID" required>
                            <option selected value="0">-- All Departments --</option>
                            @foreach (var objDepartment in ViewBag.DepartmentInfo)
                            {
                                <option value="@objDepartment.Value">@objDepartment.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Enter Department Name</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Alt Owner</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" id="altheadlist" autocomplete="off" aria-label="Default select example" asp-for="AlternateOwnerId" required>
                            <option selected value="0">-- All Resources --</option>
                            @foreach (var objResource in ViewBag.ResourcesInfo)
                            {
                                <option value="@objResource.Value">@objResource.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Select Alt Owner</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-mail"></i></span>
                        <input type="email" id="altcompanyEmail" class="form-control" readonly placeholder="Enter Alt Owner Email" asp-for="AltownerEmail">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Mobile</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-phone"></i></span>
                        <input type="text" id="altmobilePhone" class="form-control" readonly placeholder="Enter Alt Owner Mobile" asp-for="AltownerMobile">
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Unit Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-unit"></i></span>
                        <select id="ddlUnit" class="form-select form-control selectized" autocomplete="off" aria-label="Default select example" asp-for="UnitID" required>
                            <option selected value="0">-- All Units --</option>
                            @foreach (var objUnit in ViewBag.OrgUnit)
                            {
                                <option value="@objUnit.Value">@objUnit.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Enter Unit Name</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Owner</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" id="headlist" autocomplete="off" aria-label="Default select example" asp-for="OwnerId" required>
                            <option selected value="0">-- All Resources --</option>
                            @foreach (var objResource in ViewBag.ResourcesInfo)
                            {
                                <option value="@objResource.Value">@objResource.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Select Owner</div>

                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-mail"></i></span>
                        <input type="email" id="companyEmail" class="form-control" readonly placeholder="Enter Owner Email" asp-for="OwnerEmail">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Mobile</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-phone"></i></span>
                        <input type="text" id="mobilePhone" class="form-control" readonly placeholder="Enter Owner Mobile" asp-for="OwnerMobile">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button id="btnsave" type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function () {
        console.log("=== SUB DEPARTMENT FORM INITIALIZING ===");

        // Sub Department Form Validation - Clean Implementation
        window.SubDepartmentForm = {
            init: function() {
                console.log("Initializing Sub Department Form...");
                this.setupFormValidation();
                this.setupDropdownHandlers();
                this.setupSubDepartmentNameValidation();
                this.setupDepartmentChangeHandler();
                this.setupUnitChangeHandler();
                console.log("Sub Department Form initialized successfully");
            },

            // Check if subdepartment name exists in selected department
            checkSubDepartmentName: function(subDepartmentName, departmentId, subDepartmentId, callback) {
                console.log("=== CHECKING SUB DEPARTMENT NAME ===");
                console.log("Sub Department Name:", subDepartmentName);
                console.log("Department ID:", departmentId);
                console.log("Sub Department ID:", subDepartmentId);

                var checkUrl = '@Url.Action("CheckSubDepartmentNameExists", "SubDepartment", new { area = "BCMOrgStructure" })';
                console.log("AJAX URL:", checkUrl);

                $.ajax({
                    url: checkUrl,
                    type: 'GET',
                    data: {
                        subDepartmentName: subDepartmentName,
                        departmentId: departmentId,
                        subDepartmentId: subDepartmentId
                    },
                    timeout: 10000,
                    success: function(response) {
                        console.log("=== AJAX SUCCESS ===");
                        console.log("Response:", response);
                        callback(response && response.exists === true);
                    },
                    error: function(xhr, status, error) {
                        console.log("=== AJAX ERROR ===");
                        console.error("Status:", status);
                        console.error("Error:", error);
                        console.error("Response Text:", xhr.responseText);
                        alert("Error checking sub department name. Please try again.");
                        callback(false); // Assume unique on error
                    }
                });
            },

            // Setup form validation and submission
            setupFormValidation: function() {
                var self = this;
                var form = document.getElementById('addSubDepartmentForm');

                // Initialize BCM Validation if available
                if (typeof window.BCMValidation !== 'undefined') {
                    try {
                        window.BCMValidation.init();
                        window.BCMValidation.addRequiredFieldIndicators(form);
                        window.BCMValidation.addFormatIndicators(form);
                    } catch (e) {
                        console.error("BCM Validation error:", e);
                    }
                }

                // Form submission handler
                $('#addSubDepartmentForm').on('submit', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var subDepartmentName = $('#SubFunctionName').val().trim();
                    var departmentId = self.getDepartmentId();
                    var subDepartmentId = 0; // For add mode

                    console.log("Form Submit - Sub Department Name:", subDepartmentName);
                    console.log("Form Submit - Department ID:", departmentId);

                    // Check subdepartment name if both name and department are provided
                    if (subDepartmentName && subDepartmentName.length >= 2 && departmentId && departmentId !== "0") {
                        self.checkSubDepartmentName(subDepartmentName, departmentId, subDepartmentId, function(exists) {
                            if (exists) {
                                // Show invalid feedback message instead of alert
                                var input = $('#SubFunctionName');
                                var feedback = input.closest('.form-group').find('.invalid-feedback');
                                input.addClass('is-invalid');
                                input.closest('.input-group').addClass('is-invalid');
                                feedback.text('This Sub Department Name already exists in the selected department. Please choose another.').show();
                                input.focus();
                            } else {
                                self.validateAndSubmit(form);
                            }
                        });
                    } else {
                        self.validateAndSubmit(form);
                    }
                });
            },

            // Validate and submit form
            validateAndSubmit: function(form) {
                var self = this;

                // Use BCM validation if available
                if (typeof window.BCMValidation !== 'undefined') {
                    try {
                        window.BCMValidation.showAllValidationMessages(form);
                        var isValid = window.BCMValidation.validateForm(form);

                        // Also validate that head and alt head are different
                        var rolesValid = self.validateRolesDifferent();

                        if (!isValid || !rolesValid) {
                            var firstInvalid = form.querySelector('.is-invalid');
                            if (firstInvalid) {
                                firstInvalid.focus();
                            }
                            return;
                        }
                    } catch (e) {
                        console.error("BCM Validation error:", e);
                        // Fall back to HTML5 validation
                        if (!form.checkValidity()) {
                            form.reportValidity();
                            return;
                        }
                    }
                } else {
                    // Fallback to HTML5 validation
                    if (!form.checkValidity()) {
                        form.reportValidity();
                        return;
                    }

                    // Also validate roles for HTML5 fallback
                    if (!self.validateRolesDifferent()) {
                        return;
                    }
                }

                // All validation passed - submit form via AJAX to handle success/error toasts
                self.submitFormWithToast(form);
            },

            // Submit form with AJAX to show success/error toasts
            submitFormWithToast: function(form) {
                var formData = new FormData(form);

                $.ajax({
                    url: form.action,
                    type: form.method,
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // Show success toast
                        if (window.ToastManager) {
                            window.ToastManager.showSuccess('Sub Department added successfully!');
                        }

                        // Close modal if it exists
                        var modal = $('#addSubDepartmentForm').closest('.modal');
                        if (modal.length > 0) {
                            modal.modal('hide');
                        }

                        // Refresh the page or reload data table if needed
                        setTimeout(function() {
                            if (typeof window.location !== 'undefined') {
                                window.location.reload();
                            }
                        }, 1500);
                    },
                    error: function(xhr, status, error) {
                        // Show error toast
                        if (window.ToastManager) {
                            var errorMessage = 'Failed to add sub department. Please try again.';

                            // Try to get specific error message from response
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            } else if (xhr.responseText) {
                                try {
                                    var errorData = JSON.parse(xhr.responseText);
                                    if (errorData.message) {
                                        errorMessage = errorData.message;
                                    }
                                } catch (e) {
                                    // Use default error message
                                }
                            }

                            window.ToastManager.showError(errorMessage);
                        }
                    }
                });
            },

            // Setup dropdown handlers for head, alt head, and cascading dropdowns
            setupDropdownHandlers: function() {
                var self = this;

                // Head and Alt Head dropdown handlers
                $('#headlist,#altheadlist').change(function () {
                    var selectedDDL = $(this).attr('id');
                    var iId = $(this).val();

                    // Load resource details
                    $.ajax({
                        url: '@Url.Action("GetResourceDetails", "SubDepartment")',
                        type: "GET",
                        data: { iId: iId },
                        success: function (data) {
                            if (data) {
                                if (selectedDDL == "headlist") {
                                    $("#companyEmail").val(data.mail);
                                    $("#mobilePhone").val(data.mobile);
                                } else if (selectedDDL == "altheadlist") {
                                    $("#altcompanyEmail").val(data.mail);
                                    $("#altmobilePhone").val(data.mobile);
                                }
                            }
                        },
                        error: function (data) {
                            console.log('Error loading resource details');
                        }
                    });

                    // Clear validation state when user makes a selection
                    var $this = $(this);
                    if ($this.val() && $this.val() !== "" && $this.val() !== "0") {
                        $this.removeClass('is-invalid');
                        var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                        if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be the same person')) {
                            feedbackElement.removeClass('custom-validation show').hide();
                        }
                    }

                    // Small delay to allow the change to complete, then validate roles
                    setTimeout(function() {
                        self.validateRolesDifferent();
                    }, 100);
                });

                // Handle selectized dropdown changes for role validation
                setTimeout(function() {
                    if ($('#headlist')[0] && $('#headlist')[0].selectize) {
                        $('#headlist')[0].selectize.on('change', function(value) {
                            setTimeout(function() {
                                self.validateRolesDifferent();
                            }, 100);
                        });
                    }

                    if ($('#altheadlist')[0] && $('#altheadlist')[0].selectize) {
                        $('#altheadlist')[0].selectize.on('change', function(value) {
                            setTimeout(function() {
                                self.validateRolesDifferent();
                            }, 100);
                        });
                    }
                }, 500);
            },

            // Setup unit change handler for cascading dropdown
            setupUnitChangeHandler: function() {
                var self = this;

                $('#ddlUnit').change(function(){
                    var iUnitID = $(this).val();

                    // Clear subdepartment name validation when unit changes
                    var input = $('#SubFunctionName');
                    var feedback = input.closest('.form-group').find('.invalid-feedback');
                    input.removeClass('is-invalid');
                    input.closest('.input-group').removeClass('is-invalid');
                    feedback.hide();

                    $.ajax({
                        url: '@Url.Action("GetAllDepartments", "SubDepartment")',
                        type: 'GET',
                        data: { iUnitID: iUnitID },
                        success: function (response) {
                            let selectizeInstance = $('#departmentDDL')[0].selectize;
                            selectizeInstance.clear();
                            selectizeInstance.clearOptions();
                            selectizeInstance.addOption({ value: "0", text: "-- Select Department --" });
                            selectizeInstance.addItem("0");

                            response && response.forEach(({ departmentID, departmentName }) => {
                                 if (departmentID && departmentName) {
                                     selectizeInstance.addOption({ value: departmentID, text: departmentName });
                                 }
                            });
                        }
                    });
                });
            },

            // Helper function to get the actual department ID value
            getDepartmentId: function() {
                // Try to get from the original select first
                var departmentId = $('#departmentDDL').val();

                // If selectized is being used and original is 0, try to get from selectized
                if ((!departmentId || departmentId === "0") && $('#departmentDDL')[0] && $('#departmentDDL')[0].selectize) {
                    departmentId = $('#departmentDDL')[0].selectize.getValue();
                }

                console.log("Getting Department ID - Original:", $('#departmentDDL').val(), "Selectized:", departmentId);
                return departmentId;
            },

            // Setup real-time subdepartment name validation
            setupSubDepartmentNameValidation: function() {
                console.log("Setting up sub department name validation...");
                var self = this;
                var subDepartmentNameTimeout;

                $('#SubFunctionName').on('input keyup paste', function() {
                    console.log("=== SUB DEPARTMENT NAME INPUT EVENT ===");
                    var subDepartmentName = $(this).val().trim();
                    var departmentId = self.getDepartmentId();
                    var subDepartmentId = 0; // For add mode

                    console.log("Input - Sub Department Name:", subDepartmentName);
                    console.log("Input - Department ID:", departmentId);

                    clearTimeout(subDepartmentNameTimeout);

                    // Clear existing validation state
                    var input = $(this);
                    var feedback = input.closest('.form-group').find('.invalid-feedback');
                    input.removeClass('is-invalid');
                    input.closest('.input-group').removeClass('is-invalid');
                    feedback.hide();

                    // Validate if both name and department are provided
                    if (subDepartmentName.length >= 2 && departmentId && departmentId !== "0") {
                        console.log("Triggering validation timeout...");
                        subDepartmentNameTimeout = setTimeout(function() {
                            console.log("Timeout triggered - calling checkSubDepartmentName");
                            self.checkSubDepartmentName(subDepartmentName, departmentId, subDepartmentId, function(exists) {
                                console.log("Validation callback - exists:", exists);
                                if (exists) {
                                    // Ensure the error message is displayed properly
                                    input.addClass('is-invalid');
                                    input.closest('.input-group').addClass('is-invalid');

                                    // Make sure the feedback element is visible with proper styling
                                    feedback.text('This Sub Department Name already exists in the selected department. Please choose another.');
                                    feedback.css('display', 'block');
                                    feedback.addClass('d-block');

                                    console.log("Real-time validation - Showing error message:", feedback.text());
                                    console.log("Real-time validation - Feedback element display:", feedback.css('display'));
                                    console.log("Real-time validation - Input has invalid class:", input.hasClass('is-invalid'));
                                } else {
                                    // Clear error if no other validation issues
                                    if (input[0].checkValidity()) {
                                        input.removeClass('is-invalid');
                                        input.closest('.input-group').removeClass('is-invalid');
                                        feedback.hide();
                                        feedback.removeClass('d-block');
                                        console.log("Real-time validation - Cleared error message");
                                    }
                                }
                            });
                        }, 500);
                    } else {
                        console.log("Validation conditions not met - Name length:", subDepartmentName.length, "Department ID:", departmentId);
                    }
                });
            },

            // Setup department change handler to trigger subdepartment name validation
            setupDepartmentChangeHandler: function() {
                console.log("Setting up department change handler...");
                var self = this;

                // Handle both regular select and selectized dropdown
                $('#departmentDDL').on('change', function() {
                    console.log("=== DEPARTMENT DROPDOWN CHANGED ===");
                    var subDepartmentName = $('#SubFunctionName').val().trim();
                    var departmentId = self.getDepartmentId();

                    console.log("Department Change - Sub Department Name:", subDepartmentName);
                    console.log("Department Change - Department ID:", departmentId);

                    // Clear existing validation state
                    var input = $('#SubFunctionName');
                    var feedback = input.closest('.form-group').find('.invalid-feedback');
                    input.removeClass('is-invalid');
                    input.closest('.input-group').removeClass('is-invalid');
                    feedback.hide();

                    // Validate if both name and department are provided
                    if (subDepartmentName.length >= 2 && departmentId && departmentId !== "0") {
                        console.log("Department change validation triggered");
                        setTimeout(function() {
                            self.checkSubDepartmentName(subDepartmentName, departmentId, 0, function(exists) {
                                console.log("Department change validation result:", exists);
                                if (exists) {
                                    // Ensure the error message is displayed properly
                                    input.addClass('is-invalid');
                                    input.closest('.input-group').addClass('is-invalid');

                                    // Make sure the feedback element is visible with proper styling
                                    feedback.text('This Sub Department Name already exists in the selected department. Please choose another.');
                                    feedback.css('display', 'block');
                                    feedback.addClass('d-block');

                                    console.log("Department change - Showing error message:", feedback.text());
                                    console.log("Department change - Feedback element display:", feedback.css('display'));
                                    console.log("Department change - Input has invalid class:", input.hasClass('is-invalid'));

                                    // Force focus to draw attention to the error
                                    setTimeout(function() {
                                        input.focus();
                                    }, 100);
                                }
                            });
                        }, 100);
                    } else {
                        console.log("Department change validation conditions not met");
                    }
                });

                // Handle selectized dropdown changes
                setTimeout(function() {
                    if ($('#departmentDDL')[0] && $('#departmentDDL')[0].selectize) {
                        $('#departmentDDL')[0].selectize.on('change', function(value) {
                            console.log("=== SELECTIZED DEPARTMENT CHANGED ===", value);
                            $('#departmentDDL').trigger('change');
                        });
                    }
                }, 500);
            },

            // Function to validate that head and alt head are different people
            validateRolesDifferent: function() {
                var headID = this.getSelectValue('#headlist');
                var altHeadID = this.getSelectValue('#altheadlist');

                var isValid = true;

                // Clear previous error states for role validation
                this.clearRoleValidationErrors();

                // Validate Head vs Alt Head
                if (headID && altHeadID && headID !== "" && headID !== "0" && altHeadID !== "" && altHeadID !== "0" && headID === altHeadID) {
                    this.showRoleValidationError('#headlist', 'Head and Alternate Head cannot be the same person');
                    this.showRoleValidationError('#altheadlist', 'Head and Alternate Head cannot be the same person');
                    isValid = false;
                }

                return isValid;
            },

            // Helper function to get select value (handles both regular and selectized)
            getSelectValue: function(selector) {
                var element = $(selector);
                var value = element.val();

                // If selectized is being used and original is 0, try to get from selectized
                if ((!value || value === "0") && element[0] && element[0].selectize) {
                    value = element[0].selectize.getValue();
                }

                return value;
            },

            // Function to show role validation error
            showRoleValidationError: function(selector, message) {
                var element = $(selector);
                element.addClass('is-invalid').removeClass('is-valid');

                var formGroup = element.closest('.form-group');
                var feedbackElement = formGroup.find('.invalid-feedback');
                if (feedbackElement.length > 0) {
                    feedbackElement.text(message);
                    feedbackElement.addClass('custom-validation show').show();
                }
            },

            // Function to clear role validation errors
            clearRoleValidationErrors: function() {
                var roleSelectors = ['#headlist', '#altheadlist'];

                roleSelectors.forEach(function(selector) {
                    var element = $(selector);
                    var formGroup = element.closest('.form-group');
                    var feedbackElement = formGroup.find('.invalid-feedback');

                    // Only clear if the current message is a role validation error
                    if (feedbackElement.length > 0 && feedbackElement.text().includes('cannot be the same person')) {
                        element.removeClass('is-invalid');
                        feedbackElement.removeClass('custom-validation show').hide();

                        // Restore original message
                        var originalMessages = {
                            '#headlist': 'Select Head',
                            '#altheadlist': 'Select Alt Head'
                        };

                        if (originalMessages[selector]) {
                            feedbackElement.text(originalMessages[selector]);
                        }
                    }
                });
            }
        };
        // Toast Implementation using existing #liveToast from layout
        window.ToastManager = {
            // Show success toast
            showSuccess: function(message) {
                this.showToast(message, 'success');
            },

            // Show error toast
            showError: function(message) {
                this.showToast(message, 'danger');
            },

            // Show warning toast
            showWarning: function(message) {
                this.showToast(message, 'warning');
            },

            // Show info toast
            showInfo: function(message) {
                this.showToast(message, 'info');
            },

            // Generic toast show function using existing #liveToast
            showToast: function(message, type) {
                console.log("Showing toast:", message, "Type:", type);

                // Update toast message using the existing structure
                $('#liveToast .toast-body .d-flex span:last-child').text(message);

                // Update toast color
                const toastElement = $('#liveToast');
                toastElement.removeClass('bg-success bg-warning bg-danger bg-info');
                toastElement.addClass('bg-' + type);

                // Show the toast using existing #liveToast
                const toastLiveExample = document.getElementById('liveToast');
                if (toastLiveExample) {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                    toastBootstrap.show();
                } else {
                    console.error("liveToast element not found in layout");
                }
            },

            // Test function to verify toasts are working
            test: function() {
                console.log("Testing toasts...");
                this.showSuccess('Test success message');
                setTimeout(() => this.showError('Test error message'), 1500);
                setTimeout(() => this.showWarning('Test warning message'), 3000);
                setTimeout(() => this.showInfo('Test info message'), 4500);
            }
        };

        // Wait for DOM and other scripts to load
        setTimeout(function() {
            console.log("Initializing Sub Department Form after delay...");
            window.SubDepartmentForm.init();
        }, 100);
    });
</script>