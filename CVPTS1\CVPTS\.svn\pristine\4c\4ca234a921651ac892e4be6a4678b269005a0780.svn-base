﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Organization Structure Weightage</h6>
</div>
<div class="Page-Condant card border-0">
    <div class="card-body" style="height:calc(100vh - 114px);overflow:auto">
        <div class="row">
            <div class="col-7">
                <h6 class="Sub-Title">Organization Structure</h6>
                <div style="height:calc(100vh - 180px);overflow:auto">
                    <div class="tree-menu">
                        <ul class="tree">
                            <li>
                                <span role="button">Perpetuuiti( Weightage : 100 % )</span>
                                <ul>
                                    <li>
                                        <span role="button">Admin group( Weightage : 0 % )</span>
                                        <ul class="sub-parent">
                                            <li>
                                                <span role="button">Corporate affairs ( Weightage : 50 % )</span>
                                            </li>
                                            <li>
                                                <span>Department( Weightage : 0 % )</span>
                                                <ul class="sub-parent">
                                                    <li>
                                                        <span>Operation and Logistics Department ( Weightage : 0 % )</span>
                                                    </li>
                                                    <li>
                                                        <span>Logistic SubDepartment( Weightage : 0 % )</span>
                                                    </li>
                                                </ul>
                                            </li>

                                        </ul>
                                    </li>
                                    <li>
                                        <span role="button">Finance affairs( Weightage : 0 % )</span>
                                        <ul class="sub-parent">
                                            <li>
                                                <span role="button">Corporate affairs ( Weightage : 50 % )</span>
                                            </li>
                                            <li>
                                                <span>Department( Weightage : 0 % )</span>
                                                <ul class="sub-parent">
                                                    <li>
                                                        <span>Operation and Logistics Department ( Weightage : 0 % )</span>
                                                    </li>
                                                    <li>
                                                        <span>Logistic SubDepartment( Weightage : 0 % )</span>
                                                    </li>
                                                </ul>
                                            </li>

                                        </ul>
                                    </li>
                                </ul>

                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col">
                <h6 class="Sub-Title">Configure Entity Weightage</h6>
                <div>
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td>Entity 75</td>
                                <td>:</td>
                                <td>BusinessUnit</td>
                            </tr>
                            <tr>
                                <td>Name</td>
                                <td>:</td>
                                <td>
                                    Secretary General Group
                                </td>
                            </tr>
                            <tr>
                                <td>Entity</td>
                                <td>:</td>
                                <td>
                                  <div class="form-group">
                                      <div class="input-group">
                                          <input class="form-control" type="number"/>
                                      <span class="input-group-text">%</span>
                                      </div>

                                  </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="text-end">
                    <button class="btn btn-sm btn-primary">Save</button>
                    </div>
                </div>
            </div>
        </div>
      
    </div>
</div>
