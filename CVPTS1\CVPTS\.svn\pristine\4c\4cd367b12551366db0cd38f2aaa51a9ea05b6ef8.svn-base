﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Net.Mail;
using System.Numerics;
using System.Reflection;
using System.Runtime.Serialization;
using System.Diagnostics.Contracts;


namespace BCM.BusinessClasses
{
    #region VaultAlerts
    public class VaultAlerts
    {
        public int ID { get; set; }
        public int UnitID { get; set; }
        public string? Severity { get; set; }
        public int EntityID { get; set; }
        public string? AlertType { get; set; }
        public string? Message { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }
    #endregion

    #region resourcemaster
    public class Resourcelist
    {
        public int ResourceId { get; set; }
        public string? ResourceName { get; set; }
        public string? RoleName { get; set; }
        public string? Designation { get; set; }
        public string? CompanyEmail { get; set; }
        public string? MobilePhone { get; set; }
    }

    public class Resourcemaster
    {
        public long ResourceId { get; set; }

        public long? EntityId { get; set; }

        public string? ResourceName { get; set; }

        public long? StatusId { get; set; }

        public string? ResourceTitle { get; set; }

        public long? ResourceTypeId { get; set; }

        public string? BusinessPhone { get; set; }

        public string? HomePhone { get; set; }

        public string? MobilePhone { get; set; }

        public string? Fax { get; set; }

        public string? OtherPhone { get; set; }

        public string? CompanyEmail { get; set; }

        public string? OtherEmail { get; set; }

        public string? Address { get; set; }

        public string? City { get; set; }

        public string? State { get; set; }

        public string? PinCode { get; set; }

        public long? DepartmentId { get; set; }

        public string? BackupResourceId { get; set; }

        public string? RoleDetails { get; set; }

        public string? UserRole { get; set; }

        public string? Comments { get; set; }

        public int? IsActive { get; set; }

        public DateTime? CreateDate { get; set; }

        public DateTime? UpdateDate { get; set; }

        public long? ChangedBy { get; set; }

        public long IsActiveMobile { get; set; }

        public long IsActiveEmail { get; set; }

        public DateTime? ActiveDate { get; set; }

        public long? Orgid { get; set; }

        public string? Fname { get; set; }

        public string? Mname { get; set; }

        public string? Lname { get; set; }

        public long? VendorId { get; set; }

        public long? LocationId { get; set; }

        public string? Mime { get; set; }

        public string? Guid { get; set; }

        public DateTime? LastEmailResponseDate { get; set; }

        public DateTime? LastSmsResponseDate { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? DefaultRole { get; set; }

        public int? EntityTypeId { get; set; }

        public string? Field1 { get; set; }

        public string? Field2 { get; set; }

        public string? Field3 { get; set; }

        public string? Field4 { get; set; }

        public int? OrgGroupId { get; set; }

        public int? SubDeptId { get; set; }

        public int? DesignationId { get; set; }

        public string? Culture { get; set; }

        public Guid SsmaRowid { get; set; }
    }
    #endregion

    #region AddCalenderActivity
    public class AddCalendarActivity : UserAction
    {
        public int Id { get; set; }
        public int? OrgID { get; set; }
        public int? UnitID { get; set; }
        public int? DepartmentID { get; set; }
        public string? ActivityName { get; set; }
        public string? ActivityDetails { get; set; }
        public DateTime ScheduledStartDate { get; set; }
        public DateTime ScheduledENDDate { get; set; }
        public DateTime ActualStartDate { get; set; }
        public DateTime ActualEndDate { get; set; }
        public string? Remarks { get; set; }
        public string? Status { get; set; }
        public string? Responsibility { get; set; }
        public string? ResponsibleUserName { get; set; }
        public int IsActive { get; set; }
        public int ChangedBy { get; set; }
        public int ChangesAt { get; set; }
        public int TypeID { get; set; }
        public string? ActivityType { get; set; }
        public string? Reminder { get; set; }
        public string? RecurrenceRule { get; set; }
        public int AttachmentID { get; set; }
        public string? AttachmentName { get; set; }
        public int IsCal { get; set; }

        public string? title { get; set; }

        public string? start { get; set; }

        public string? end { get; set; }

        public IFormFile? File { get; set; }

    }
    #endregion

    #region CalenderDetails
    public class BCMCalenderDetailss
    {
        public int Id { get; set; }
        public int TeamId { get; set; }
        public int UserId { get; set; }
        public string? UserName { get; set; }
        public string? NotificationTypeId { get; set; }
        public int CalenderId { get; set; }
        public string? TeamName { get; set; }
    }
    #endregion

    #region Application
    public class Applications : UserActivityStatus
    {
        public int ApplicationId { get; set; }
        public int ApplicationId_Encrypted { get; set; }
        public int ApplicationID { get; set; }
        public string? ApplicationName { get; set; }
        public string? ApplicationCode { get; set; }
        public string? ApplicationDetails { get; set; }
        public string? CalculatedRTO { get; set; }
        public string? ImplementedRTO { get; set; }
        public string? InflowApplications { get; set; }
        public string? MaxRTOofInflowApplications { get; set; }
        public string? BIARevalidation { get; set; }
        public string? AdditionalCostRTO { get; set; }
        public string? RevenueRTO { get; set; }
        public string? FinalRTO { get; set; }
        public int ApproverID { get; set; }
        public string? ApproverName { get; set; }
        public string? ApproverMobile { get; set; }
        public string? ApproverEmail { get; set; }
        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerMobile { get; set; }
        public string? OwnerEmail { get; set; }
        public int UnitID { get; set; }
        public string? UnitName { get; set; }
        public int DepartmentID { get; set; }
        public int SubfunctionId { get; set; }
        public string? SubfunctionName { get; set; }
        public string? DepartmentName { get; set; }
        public int OrgID { get; set; }
        public string? OrganizationName { get; set; }
        public string? Status { get; set; }
        public string? Comments { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int IsCritical { get; set; }
        public string? MAO { get; set; }
        public int OrgHeadId { get; set; }
        public int UnitHeadId { get; set; }
        public int AltUnitHeadId { get; set; }
        public int UnitBCPCorId { get; set; }
        public int AltBCPCorId { get; set; }
        public int DeptHeadId { get; set; }
        public int AltDeptHeadId { get; set; }
        public int SubFunOwnerId { get; set; }
        public int AltSubFunOwnerId { get; set; }
        public int EntityTypeID { get; set; }
        public string? Field1 { get; set; }
        public string? Field2 { get; set; }
        public string? Field3 { get; set; }
        public string? Field4 { get; set; }
        public int OrgGroupID { get; set; }
        public int IsEffective { get; set; }
        public int RTOID { get; set; }
        public string? ApplicationRTOText { get; set; }
        public int RPOID { get; set; }
        public string? ApplicationRPOText { get; set; }
        public string? RTOInHours { get; set; }
        public int IsButtonVisible { get; set; }
    }
    #endregion

    #region ApplicationBIA
    public class ApplicationRelation
    {
        public int ID { get; set; }
        public int ParentAppID { get; set; }
        public int ChildAppID { get; set; }
        public int DirectionID { get; set; }
        public int RelationTypeID { get; set; }
        public int ChildRelTypeID { get; set; }
        public int AppDetailsID { get; set; }
        public string? CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public string? ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }

    public class ApplicationBIA
    {
        public string? ID { get; set; }
        public string? ApplicationID { get; set; }
        public string? BIAOwnerID { get; set; }
        public string? BIAApproverID { get; set; }
        public string? BIADescription { get; set; }
        public string? RTO { get; set; }
        public string? CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? ChangedAt { get; set; }
        public string? ChangedBy { get; set; }
    }

    public class ApplicationActivity
    {
        public int ID { get; set; }
        public int ApplicationID { get; set; }
        public string? ActivityDetails { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }

    public class ApplicationDependency
    {
        public int ID { get; set; }
        public int ApplicationID { get; set; }
        public string? ApplicationName { get; set; }
        public int BIAID { get; set; }
        public int InBondAppID { get; set; }
        public int OutBondAppID { get; set; }
        public int RelationID { get; set; }
        public int InBound { get; set; }
        public string? DataSource { get; set; }
        public int IsRealTime { get; set; }
        public string? AreaofFocus { get; set; }
        public string? CriticalForBCM { get; set; }
        public string? BCMScope { get; set; }
        public string? Supported { get; set; }
        public string? RTO { get; set; }
        public int IsCritical { get; set; }
        public int IsDRRequired { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int QuestionID { get; set; }
        public int RelTypeID { get; set; }
        public int ChildRelTypeID { get; set; }
        public int ChildImpactSevID { get; set; }
        public int IsComplete { get; set; }
    }

    public class ImpactRatings     // added by shubham b
    {
        public int ImpactRatingID { get; set; }

        public string? ImpactRatingName { get; set; }
    }

    public class ApplicationBIADetailsVoda
    {
        public int BIADetailsID { get; set; }
        public string? BIAID { get; set; }
        public string? ImpactType { get; set; }
        public string? ImpactTypeName { get; set; }
        public int Impact { get; set; }
        public string? ImpactName { get; set; }
        public string? Upto2Hours { get; set; }
        public string? Upto4Hours { get; set; }
        public string? Upto8Hours { get; set; }
        public string? Upto12Hours { get; set; }
        public string? Upto24Hours { get; set; }
        public string? Upto48Hours { get; set; }
        public string? Upto72Hours { get; set; }
        public string? Upto1Week { get; set; }
        public string? Upto2Weeks { get; set; }
        public string? Upto1Month { get; set; }
        public string? Comment { get; set; }
        public string? CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? ChangedAt { get; set; }
        public string? ChangedBy { get; set; }
    }

    public class ApplicationBIAVoda
    {
        public string? ID { get; set; }
        public string? ApplicationID { get; set; }
        public string? ImpSeverityID { get; set; }
        public string? BIAID { get; set; }
    }

    public class ApplicationBIASection
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public int QuestionID { get; set; }
        public string? BIAQuestion { get; set; }
        public string? SectionWeightage { get; set; }
        public string? SectionDescription { get; set; }
        public int OrganizationId { get; set; }
        public string? BIAPageURL { get; set; }
        public string? Version { get; set; }
        public int IsActive { get; set; }
        public int IsEffective { get; set; }
        public string? ApprovalStatus { get; set; }
        public string? VersionChangeDescription { get; set; }
        public int ApplicationID { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }

    #endregion

    #region ApplicationBIAPeople
    public class ApplicationBIAPeopleInfo : UserActivityStatus
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int ResourceID { get; set; }
        public int IsCritical { get; set; }
        public int SectionID { get; set; }
        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public int OwnerMobile { get; set; }
        public string? OwnerEmail { get; set; }
        public int AltResourceID { get; set; }
        public string? AltOwnerName { get; set; }
        public int AltOwnerMobile { get; set; }
        public string? AltOwnerEmail { get; set; }
        public int QuestionID { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public string? BIAFindings { get; set; }
    }

    #endregion

    #region ApplicationBIAThirdParty
    public class ApplicationBIAThirdPartyInfo : UserActivityStatus
    {
        public int ID { get; set; }
        public int ThirdPartyID { get; set; }
        public string? ThirdPartyName { get; set; }
        public int SPOCID { get; set; }
        public string? SPOCName { get; set; }
        public int SPOCMobile { get; set; }
        public string? SPOCEmail { get; set; }
        public int AltSPOCID { get; set; }
        public string? AltSPOCName { get; set; }
        public int AltSPOCMobile { get; set; }
        public string? AltSPOCEmail { get; set; }
        public int IsCritical { get; set; }
        public int BIAID { get; set; }
        public int QuestionID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? BIAFindings { get; set; }
        public int SectionID { get; set; }

    }

    #endregion

    #region ApplicationBIAFacility
    public class ApplicationBIAFacilityInfo
    {
        public int ID { get; set; }
        public int FacilityID { get; set; }
        public string? FacilityName { get; set; }
        public string? FacilityAddress { get; set; }
        public string? FacilityManagerName { get; set; }
        public string? FacilityType { get; set; }
        public int BIAID { get; set; }
        public int QuestionID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? iBIAFindings { get; set; }
        public int SectionID { get; set; }
    }

    #endregion

    #region Applicationbialegalandregulatory    
    public class Applicationbialegalandregulatory
    {
        public int ID { get; set; }
        public string? LegalAndRegAuth { get; set; }
        public string? DependencyReport { get; set; }
        public string? Frequency { get; set; }
        public int BIAID { get; set; }
        public int QuestionID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? BIAFindings { get; set; }
        public int SectionID { get; set; }
    }

    #endregion

    #region ApplicationBIAImpactMatrix
    public class ApplicationBIAImpactMatrix
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int ImpactSeverityID { get; set; }
        public int ImpactTypeID { get; set; }
        public string? ImpactTypeName { get; set; }
        public int ImpactID { get; set; }
        public string? ImpactName { get; set; }
        public string? Upto2Hours { get; set; }
        public string? Upto4Hours { get; set; }
        public string? Upto8Hours { get; set; }
        public string? Upto12Hours { get; set; }
        public string? Upto24Hours { get; set; }
        public string? Upto48Hours { get; set; }
        public string? Upto72Hours { get; set; }
        public string? Upto1Week { get; set; }
        public string? Upto2Weeks { get; set; }
        public string? Upto1Month { get; set; }
        public int IsQualitative { get; set; }
        public string? Comment { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int QuestionID { get; set; }
        public int SectionID { get; set; }
    }


    #endregion

    #region ApplicationBIAQuestion
    public class ApplicationBIAQuestion
    {
        public int ID { get; set; }
        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public string? QuestionDetails { get; set; }
        public int OrgID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }
    #endregion

    #region ApplicationBIASurveyQ1
    public class ApplicationBIAActivityInfo
    {
        public int ActivityID { get; set; }
        public string? ActivityDetails { get; set; }
        public int BIAID { get; set; }
        public int ApplicationID { get; set; }
        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public int QuestionID { get; set; }
        public string? QuestionDetails { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public string? BIAfinding { get; set; }
    }
    #endregion

    #region ApplicationSurveyDetails
    public class ApplicationSurveyDetail
    {
        public int ApplicationSurveyID { get; set; }
        public int ImpactTypeID { get; set; }
        public int ImpactType { get; set; }
        public int ImpactID { get; set; }
        public string? ImpactName { get; set; }
        public int ApplicationID { get; set; }
        public string? ApplicationName { get; set; }
        public string? Upto2Hrs { get; set; }
        public string? Upto4Hrs { get; set; }
        public string? Upto8Hrs { get; set; }
        public string? Upto12Hrs { get; set; }
        public string? Upto24Hrs { get; set; }
        public string? Upto48Hrs { get; set; }
        public string? Upto72Hrs { get; set; }
        public string? Upto1Week { get; set; }
        public string? Upto2Weeks { get; set; }
        public string? Upto1Month { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }
    #endregion

    #region AuditLogs
    public class AuditLogsInfo
    {
        public int ID { get; set; }
        public int DetailsID { get; set; }
        public string? ChangeDescription { get; set; }
        public string? PreviousValue { get; set; }
        public string? NewValue { get; set; }
        public int ModuleID { get; set; }
        public int RecordID { get; set; }
        public string? RecordName { get; set; }
        public int ActionID { get; set; }
        public string? CreatedOn { get; set; }
        public int UserID { get; set; }
        public string? UserName { get; set; }
        public string? Module { get; set; }
        public string? EntityName { get; set; }
        public string? EntityType { get; set; }
        public string? Action { get; set; }
        public int ProcessID { get; set; }
        public string? Version { get; set; }
        public int ApplicationId { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public string? Guid { get; set; }
        public string? Mime { get; set; }
    }


    #endregion

    #region Auditlog_Process
    public class Auditlog_Process
    {
        public int ID { get; set; }
        public int ModuleID { get; set; }
        public int RecordID { get; set; }
        public string? ComponantType { get; set; }
        public int CompID { get; set; }
        public int ActionID { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime CreatedBy { get; set; }
        public int DetailsID { get; set; }
        public string? RecordName { get; set; }
        public string? IPAddress { get; set; }
    }
    #endregion

    #region Auditlog_Process_Details
    public class Auditlog_Process_Details
    {
        public int AutoID { get; set; }
        public int DetailsID { get; set; }
        public string? ChangeDescription { get; set; }
        public string? PreviousValue { get; set; }
        public string? NewValue { get; set; }
        public string? getByName { get; set; }
        public int ModuleID { get; set; }
        public int RecordID { get; set; }
        public string? RecordName { get; set; }
        public string ActionID { get; set; }
        public int CreatedBy { get; set; }
        public string? IPAddress { get; set; }
        public string? EntityName { get; set; }
        public string? UserName { get; set; }
        public DateTime CreatedOn { get; set; }
        public string? UserRole { get; set; }
        public int UserID { get; set; }
        public string? UserRoleName { get; set; }
        public string? SessionID { get; set; }
        public int IDValue { get; set; }
    }
    #endregion

    #region Attachments

    public class AttachmentsAndReviewHistory
    {
        public Attachments Attachments { get; set; }
        public List<EntityReview> EntityReview { get; set; }
        public ButtonAcces ButtonAcces { get; set; }

    }
    public class Attachments
    {
        public int AttachmentId { get; set; }
        public int EntityId { get; set; }
        public int RecordId { get; set; }
        public int UserId { get; set; }
        public string? username { get; set; }
        //public BigInteger Usermobile { get; set; }
        public long Usermobile { get; set; }
        public string? usermail { get; set; }
        // public BigInteger App_Usermobile { get; set; }
        public long App_Usermobile { get; set; }
        public string? App_usermail { get; set; }
        public int ApproverID { get; set; }
        public string? ApproverName { get; set; }
        public string? AttchmentName { get; set; }
        public string? Description { get; set; }
        public int FileSize { get; set; }
        public int CreatedBy { get; set; }
        public string? CreaterName { get; set; }
        public int UpdatedBy { get; set; }
        public string? UpdaterName { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifyDate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public string? Attachment { get; set; } //BLOB Field        
        public Object? AttachmentObj { get; set; } //BLOB Field
        public IFormFile? AttachementFile { get; set; }
        public string? MimeType { get; set; }
        public string? GUID { get; set; }
        public string? Status { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime ReviewDate { get; set; }
        public DateTime? LastReviewDate { get; set; }
        public string? AttachmentType { get; set; }
        public string? version { get; set; }
        public string? SaveAsType { get; set; }
        public long OrginalFile { get; set; }
        public byte[]? Blob { get; set; }
        public int OrgID { get; set; }
        public string? OrgName { get; set; }
        public int UnitID { get; set; }
        public string? UnitName { get; set; }
        public int DeptID { get; set; }
        public string? DepartmentName { get; set; }
        public string? SubFunction { get; set; }
        public string? SubFunctionName { get; set; }
        public string? DocCode { get; set; }
        public int IsActive { get; set; }
        public string? ActivationModeEmail { get; set; }
        public string? EmailVerifiedTooltip { get; set; }
        public string? ActivationModeMobile { get; set; }
        public string? MobileVerifiedTooltip { get; set; }
        public string? ActivationModeEmailAlt { get; set; }
        public string? EmailVerifiedTooltipAlt { get; set; }
        public string? ActivationModeMobileAlt { get; set; }
        public string? MobileVerifiedTooltipAlt { get; set; }
        public string? flag { get; set; }
        public int CVattchmentID { get; set; }
        public int OrgGroupID { get; set; }
        public int OwnerID { get; set; }
        public int AltOwnerID { get; set; }

        public string? CreatedByName { get; set; }
        public string? UpdatedByName { get; set; }

        public DateTime? CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
    }
    #endregion

    #region Approver
    public class Approver
    {
        public int ApproverId { get; set; }
        public int EntityId { get; set; }
        public int RecordId { get; set; }
        public int UserId { get; set; }
    }
    #endregion

    #region Approval   
    public class Approval
    {
        public int ApprovalId { get; set; }
        public int UserId { get; set; }
        public int EntityId { get; set; }
        public int RecordId { get; set; }
        public string? ApprovalType { get; set; }
    }
    #endregion

    #region BPBIAMatrix
    public class BPBIAMatrix
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int BusinessProcessID { get; set; }
        public int ImpactSeverityID { get; set; }
        public int ImpactTypeID { get; set; }
        public int ImpactID { get; set; }
        public int IsQuantitative { get; set; }
        public string? Comment { get; set; }
        public int CreatorId { get; set; }
        public DateTime CreateDate { get; set; }
        public int UpdatorId { get; set; }
        public DateTime UpdateDate { get; set; }
    }
    #endregion

    #region BPBIAMatrixDetails
    public class BPBIAMatrixDetails
    {
        public int ID { get; set; }
        public int BPMatrixID { get; set; }
        public int TimeIntervalID { get; set; }
        public decimal Cost { get; set; }
    }
    #endregion

    #region BIAApproval 
    public class BIAApprovalAndButtonAccess
    {
        public BIAApproval BIAApproval { get; set; }

        public ButtonAcces ButtonAcces { get; set; }
    }
    public class BIAApproval
    {
        public int EntityTypeID { get; set; }
        public int RecordID { get; set; }
        public int StatusID { get; set; }
        public string? RecordName { get; set; }
        public string? EntityCode { get; set; }
        public string? Version { get; set; }
        public string? RTO { get; set; }
        public string? EntityType { get; set; }
        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerEmail { get; set; }
        public string? OwnerPhone { get; set; }
        public string? ApproverEmail { get; set; }
        public string? ApproverPhone { get; set; }
        public int ApprovalID { get; set; }
        public int ApproverID { get; set; }
        public string? ApprovarName { get; set; }
        public int ProcessID { get; set; }
        public int BCMEntityID { get; set; }
        public int IsApproved { get; set; }
        public int ChangedBy { get; set; }
        public string? ChangedAt { get; set; }
        public int UserRole { get; set; }
    }
    #endregion

    #region BIAAssesment
    public class BIAAssesment
    {
        public int BiaAssesmentID { get; set; }
        public int BiaID { get; set; }
        public string? BiaAssesmentStepName { get; set; }
        public string? BiaAssesmentBusinessOwner { get; set; }
        public string? BiaAssesmentAltenativeName { get; set; }
        public string? BiaAssesmentSupportTeam { get; set; }
        public string? BiaAssesmentApplicationFunction { get; set; }
        public string? BiaAssesmentHeavyLoadTimes { get; set; }
        public string? BiaAssesmentCriticalTimes { get; set; }
        public string? BiaAssesmentBusinessRepresentatives { get; set; }
        public string? BiaAssesmentLocationOfBusiness { get; set; }
        public string? BiaAssesmentBriefOverviewOfImpact { get; set; }
        public string? BiaAssesmentMethodsofProcessing { get; set; }
        public string? BiaAssesmentAvailability { get; set; }
        public string? BiaAssesmentMaximumDownTime { get; set; }
        public string? BiaAssesmentBriefDescription { get; set; }
        public string? BiaAssesmentCheckboxcsv { get; set; }
        public string? BiaAssesmentProfitlossReduction { get; set; }
        public string? BiaAssesmentReductionHours { get; set; }
        public string? BiaAssesmentRegulatoryImpact { get; set; }
        public string? BiaAssesmentRegulatoryImpactHours { get; set; }
        public string? BiaAssesmentCustomerImpactRetention { get; set; }
        public string? BiaAssesmentCustomerImpactHours { get; set; }
        public string? BiaAssesmentBrandNameImpactReputation { get; set; }
        public string? BiaAssesmentBrandNameImpactHours { get; set; }
        public string? BiaAssesmentOperationalImpactEffect { get; set; }
        public string? BiaAssesmentOperationalImpactHours { get; set; }
        public string? BiaAssesmentManagementControl { get; set; }
        public string? BiaAssesmentManagementControlHours { get; set; }
        public string? BiaAssesmentComplianceImpactEffect { get; set; }
        public string? BiaAssesmentComplianceImpactHours { get; set; }
        public string? BiaAssesmentComplianceImpactLicence { get; set; }
        public string? BiaAssesmentComplianceImpactLisenceHours { get; set; }
        public string? BiaAssesmentComplianceImpactCreditFraud { get; set; }
        public string? BiaAssesmentComplianceImpactCreditFraudHours { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }



    #endregion

    #region BIAImpactMatrix
    public class BIAImpactMatrix
    {
        public int Id { get; set; }
        public string? ImpactRanking { get; set; }
        public string? ImpactName { get; set; }
        public string? Description { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangesAt { get; set; }
    }
    #endregion

    #region BIASurvey
    public class BIASurvey
    {
        public int BIASurveyID { get; set; }
        public string? BIALineofBusiness { get; set; }
        public int FunctionID { get; set; }
        public string? FunctionName { get; set; }
        public int ProcessID { get; set; }
        public string? ProcessName { get; set; }
        public int FunctionalPlannerID { get; set; }
        public string? FunctionalPlanner { get; set; }
        public int FunctionalOwnerID { get; set; }
        public string? FunctionalOwner { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public string? Reason { get; set; }
        public int IsActive { get; set; }
    }
    #endregion

    #region BIA Process Approver
    public class BIAProcessApprover
    {
        public int ProcessID { get; set; }
        public string? ProcessName { get; set; }
        public int OwnerID { get; set; }
        public int AltOwnerID { get; set; }
        public int ApproverID { get; set; }
        public string? ApproverName { get; set; }
        public int ApproverEmailID { get; set; }
        public string? ApproverMobileNo { get; set; }
        public string? Status { get; set; }
    }
    #endregion

    #region BIAProfileImpactTypes
    public class BIAProfileImpactTypes
    {
        public int ID { get; set; }
        public int ProfileID { get; set; }
        public int ImpactID { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int CreatorID { get; set; }
        public int UpdatorID { get; set; }
        public int ImpactCategoryID { get; set; }
    }
    #endregion

    #region BIAProfileMaster
    public class BIAProfileMaster
    {
        public int ID { get; set; }
        public string? ProfileCode { get; set; }
        public string? ProfileName { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public int CreatorID { get; set; }
        public DateTime UpdateDate { get; set; }
        public int UpdatorID { get; set; }
        public string? UserName { get; set; }
        public string? UserMobile { get; set; }
        public string? UserEmail { get; set; }
        public int TimeIntervalID { get; set; }
        public string? TimeIntervalText { get; set; }
        public int ImpactID { get; set; }
        public string? ImpactName { get; set; }
        public int ImpactCategoryID { get; set; }
        public string? ImpactTypeName { get; set; }
        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public int ImpactSeverityID { get; set; }
        public string? Cost { get; set; }
        public int OrgID { get; set; }
        public int ApproverID { get; set; }
        public string? Status { get; set; }
        public int OwnerID { get; set; }
        public string? OrgName { get; set; }
        public string? ApproverName { get; set; }
        public string? OwnerName { get; set; }
        public int OrgGroupID { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public int SubfunctionID { get; set; }              
        public string? ProcessName { get; set; }

    }
    #endregion

    #region BIAProfileTimeInterval
    public class BIAProfileTimeInterval
    {
        public int ID { get; set; }
        public int ProfileID { get; set; }
        public int TimeIntervalID { get; set; }
        public int CreatorID { get; set; }
        public int UpdatorID { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }
    #endregion

    #region BIAProfileSection
    public class BIAProfileSection
    {
        public int ID { get; set; }
        public int ProfileID { get; set; }
        public int SectionID { get; set; }
        public DateTime CreateDate { get; set; }
        public int CreatorID { get; set; }
        public DateTime UpdateDate { get; set; }
        public int UpdatorID { get; set; }
        public string? SectionName { get; set; }
    }

    public class ManageBIAProfileSectionMain
    {
        public int ID { get; set; }
        public int ProfileID { get; set; }
        public int SectionID { get; set; }
        public DateTime CreateDate { get; set; }
        public int CreatorID { get; set; }
        public DateTime UpdateDate { get; set; }
        public int UpdatorID { get; set; }
        public string? SectionName { get; set; }
        public int ImpactID { get; set; }
        public int ImpactCategoryID { get; set; }
        public string? ProfileCode { get; set; }
        public string? ProfileName { get; set; }
        public int IsActive { get; set; }
        public string? UserName { get; set; }
        public string? UserMobile { get; set; }
        public string? UserEmail { get; set; }
        public int TimeIntervalID { get; set; }
        public string? TimeIntervalText { get; set; }

        // Time Interval properties for Add New Time Interval functionality
        public string? MinInterval { get; set; }
        public int MinIntervalUnit { get; set; }
        public string? MaxInterval { get; set; }
        public int MaxIntervalUnit { get; set; }
        public string? ImpactName { get; set; }
        public string? ImpactTypeName { get; set; }
        public int ImpactSeverityID { get; set; }
        public string? Cost { get; set; }
        public int OrgID { get; set; }
        public int ApproverID { get; set; }
        public string? Status { get; set; }
        public int OwnerID { get; set; }
        public string? OrgName { get; set; }
        public string? ApproverName { get; set; }
        public string? OwnerName { get; set; }
        public int OrgGroupID { get; set; }

        //public List<int>? ArrBIASectionID { get; set; }


        public List<int> ArrBIASectionID { get; set; }
        public List<int> ArrTimeIntervalID { get; set; }
        public List<int> ArrImpactID { get; set; }
        public List<int> ArrImpactCategoryID { get; set; }


        public List<int> ArrBIASectionIDEdit { get; set; }
        public List<int> ArrTimeIntervalIDEdit { get; set; }
        public List<int> ArrImpactIDEdit { get; set; }
        public List<int> ArrImpactCategoryIDEdit { get; set; }

        // New properties to receive all items with their checked status
        public List<ItemWithStatus> AllBIASectionsWithStatus { get; set; }
        public List<ItemWithStatus> AllTimeIntervalsWithStatus { get; set; }
        public List<ItemWithStatus> AllImpactsWithStatus { get; set; }
        public List<ItemWithStatus> AllImpactCategoriesWithStatus { get; set; }

        // Constructor to initialize arrays (following _PageAccess.cshtml pattern)
        public ManageBIAProfileSectionMain()
        {
            ArrBIASectionID = new List<int>();
            ArrTimeIntervalID = new List<int>();
            ArrImpactID = new List<int>();
            ArrImpactCategoryID = new List<int>();
            ArrBIASectionIDEdit = new List<int>();
            ArrTimeIntervalIDEdit = new List<int>();
            ArrImpactIDEdit = new List<int>();
            ArrImpactCategoryIDEdit = new List<int>();
            AllBIASectionsWithStatus = new List<ItemWithStatus>();
            AllTimeIntervalsWithStatus = new List<ItemWithStatus>();
            AllImpactsWithStatus = new List<ItemWithStatus>();
            AllImpactCategoriesWithStatus = new List<ItemWithStatus>();
        }
    }

    // Helper class to represent items with their checked status
    public class ItemWithStatus
    {
        public int Id { get; set; }
        public bool Checked { get; set; }
    }
    #endregion

    #region BIASurveyQuestion



    public class BIAVitalRecordsViewModel
    {
        public List<BIASurveyQuestion>? Questions { get; set; }
        public List<BIAVitalRecord>? VitalRecords { get; set; }
        public BIASection? BIASectionVersion { get; set; }
        public List<VitalRecordOwnerName>? RecordOwnerNames { get; set; }
    }

    public class BIASurveyQuestion
    {
        public int ID { get; set; }
        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public string? QuestionDetails { get; set; }
        public int OrgID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int SequenceNo { get; set; }
    }

    public class BIASection
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public int QuestionID { get; set; }
        public string? BIAQuestion { get; set; }
        public string? SectionWeightage { get; set; }
        public string? SectionDescription { get; set; }
        public int OrganizationId { get; set; }
        public string? BIAPageURL { get; set; }
        public string? Version { get; set; }
        public int IsActive { get; set; }
        public int IsEffective { get; set; }
        public string? ApprovalStatus { get; set; }
        public string? VersionChangeDescription { get; set; }
        public int ProcessID { get; set; }
        public int CreatedBy { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int IsBCMEntity { get; set; }
        public string? BIADONE { get; set; }
        public string? CreatedByName { get; set; }
        public string? ChangedByName { get; set; }
        public int SequenceNo { get; set; }
        public int CompletionStatus { get; set; }

        public int PageID { get; set; }
        public string? PageName { get; set; }
        public string? AreaName { get; set; }
        public string? ControllerName { get; set; }
        public string? ActionName { get; set; }
        public bool IsSelected { get; set; }
    }

    public class BIASurveySection_Mapping
    {
        public int ID { get; set; }
        public int OrgID { get; set; }
        public int SectionID { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreateDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    public class VitalRecordOwnerName
    {
        public int ResourceID { get; set; }
        public string? ResourceName { get; set; }
    }

    #endregion

    #region BIASurveyDatails
    public class BIASurveyDetail
    {
        public int ID { get; set; }
        public int ImpactTypeID { get; set; }
        public string? ImpactType { get; set; }
        public int ImpactID { get; set; }
        public string? ImpactName { get; set; }
        public int BIASurveyID { get; set; }
        public string? BIASurveyName { get; set; }
        public int ProcessID { get; set; }
        public string? ProcessName { get; set; }
        public string? Upto2Hrs { get; set; }
        public string? Upto4Hrs { get; set; }
        public string? Upto8Hrs { get; set; }
        public string? Upto12Hrs { get; set; }
        public string? Upto24Hrs { get; set; }
        public string? Upto48Hrs { get; set; }
        public string? Upto72Hrs { get; set; }
        public string? Upto1Week { get; set; }
        public string? Upto2Weeks { get; set; }
        public string? Upto1Month { get; set; }
        public string? MoreThan1Month { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }


    public class BIAImpactDetail
    {
        public string? ID { get; set; }
        public string? ImTypeID { get; set; }
        public string? ImpactID { get; set; }
        public string? ScoreID { get; set; }
        public string? RangeID { get; set; }
        public string? Cost { get; set; }
        public string? Description { get; set; }
        public string? ImpactTypeName { get; set; }
        public string? ImpactName { get; set; }
        public string? ChangedBy { get; set; }
        public string? OrgID { get; set; }
        public string? UnitID { get; set; }
        public string? ProcessID { get; set; }
        public string? Unit { get; set; }
        public string? Process { get; set; }

    }
    #endregion

    #region BIASurveySCG

    public class BIASurveySCG
    {
        public int SCGApprovalID { get; set; }
        public int ApplicationID { get; set; }
        public string? ApplicationName { get; set; }
        public int UnitID { get; set; }
        public string? UnitName { get; set; }
        public int ProcessID { get; set; }
        public string? ProcessName { get; set; }
        public string? RTO { get; set; }
        public int IsDRInPlace { get; set; }
        public int IsSubmittedToSCG { get; set; }
        public string? RTODefinedByIT { get; set; }
        public string? GAP { get; set; }
        public string? SentItForITImplementation { get; set; }
        public string? DRTestConducted { get; set; }
        public DateTime DRTestDate { get; set; }
        public string? DRTestRTO { get; set; }
        public string? ITStatus { get; set; }
        public string? TimePeriod { get; set; }
        public string? Observation { get; set; }
        public string? Comment { get; set; }
        public int IsNotified { get; set; }
        public int ChangedBy { get; set; }
        public int IsCritical { get; set; }
    }

    #endregion

    #region BIASurveyQ1
    public class BIAActivityInfo
    {
        public int ActivityID { get; set; }
        public string? ActivityDetails { get; set; }
        public int BIAID { get; set; }
        public int ProcessID { get; set; }
        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public int QuestionID { get; set; }
        public string? QuestionDetails { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public string? BIAfinding { get; set; }
        public string? RPO { get; set; }
        public string? RPOUnit { get; set; }
        public string? RTO { get; set; }
        public string? RTOUnit { get; set; }
        public string? MAO { get; set; }
        public string? MAOUnit { get; set; }
        public int Impact { get; set; }
        public int IsComplete { get; set; }
    }
    #endregion

    #region BIADefaultSection
    public class BIADefaultSection
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int AnswerField { get; set; }
        public string? Field1 { get; set; }
        public string? Field2 { get; set; }
        public string? Field3 { get; set; }
        public string? Field4 { get; set; }
        public int QuestionID { get; set; }
        public string? QuestionDetails { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public string? BIAfinding { get; set; }
        public int SectionID { get; set; }
        public int IsComplete { get; set; }

    }


    #endregion

    #region ProcessBIADepOrgStructure
    public class ProcessBIADepOrgStructure
    {
        public int ID { get; set; }
        public string? Description { get; set; }
        public int BIAID { get; set; }
        public int ProcessID { get; set; }
        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public int QuestionID { get; set; }
        public int QuestionDetails { get; set; }
        public int OrgGroupID { get; set; }
        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DeptID { get; set; }
        public int SubDeptID { get; set; }
        public string? OrgName { get; set; }
        public string? UnitName { get; set; }
        public string? DeptName { get; set; }
        public string? SubDeptName { get; set; }
        public int Impact { get; set; }
        public int CreatedBy { get; set; }
    }


    #endregion

    #region BIASurveyQ2


    public class BIASurveyQ2Info
    {
        public int ID { get; set; }
        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public int OwnerMobile { get; set; }
        public string? OwnerEmail { get; set; }
        public int AltOwnerID { get; set; }
        public string? AltOwnerName { get; set; }
        public int AltOwnerMobile { get; set; }
        public string? AltOwnerEmail { get; set; }
        public int IsOwnerWFH { get; set; }
        public int IsAltOwnerWFH { get; set; }
        public int ProcessID { get; set; }
        public int QuestionID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }

    #endregion

    #region BIASurveyQ3

    public class BIASurveyQ3Info
    {
        public int IsUsedByThirdParty { get; set; }
        public int ThirdPartyID { get; set; }
        public int AppID { get; set; }
        public int ApplicationID { get; set; }
        public string? ApplicationName { get; set; }
        public string? ApplicationDetails { get; set; }
        public string? ApplicationOwnerName { get; set; }
        public string? ApproverName { get; set; }
        public string? CalculatedRTO { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ProcessID { get; set; }
        public string? ProcessRTO { get; set; }
        public string? ProcessName { get; set; }
        public int QuestionID { get; set; }
        public int IsCritical { get; set; }
        public int IsDrImplemented { get; set; }
    }

    #endregion

    #region BIASurveyQ4
    public class BIASurveyQ4Info
    {
        public int ID { get; set; }
        public int ThirdPartyID { get; set; }
        public string? ThirdPartyName { get; set; }
        public int SPOCID { get; set; }
        public string? SPOCName { get; set; }
        public int SPOCMobile { get; set; }
        public string? SPOCEmail { get; set; }
        public int AltSPOCID { get; set; }
        public string? AltSPOCName { get; set; }
        public int AltSPOCMobile { get; set; }
        public string? AltSPOCEmail { get; set; }
        public int IsCritical { get; set; }
        public int ProcessID { get; set; }
        public int QuestionID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }
    #endregion

    #region BIASurveyQ5
    public class BIASurveyQ5Info
    {
        public int ID { get; set; }
        public int FacilityID { get; set; }
        public string? FacilityName { get; set; }
        public string? FacilityAddress { get; set; }
        public string? FacilityManagerName { get; set; }
        public string? FacilityType { get; set; }
        public int ProcessID { get; set; }
        public int QuestionID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }

    #endregion

    #region BIASurveyQ6
    public class BIASurveyQ6Info
    {
        public int ID { get; set; }
        public string? LegalAndRegAuth { get; set; }
        public string? DependencyReport { get; set; }
        public string? Frequency { get; set; }
        public int ProcessID { get; set; }
        public int QuestionID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }
    #endregion

    #region BIASurveyQ7
    public class BIASurveyQ7CriticalProc
    {
        public int ID { get; set; }
        public int ProcessID { get; set; }
        public string? Upto2Hrs { get; set; }
        public string? Upto4Hrs { get; set; }
        public string? Upto8Hrs { get; set; }
        public string? Upto12Hrs { get; set; }
        public string? Upto2Days { get; set; }
        public string? Upto3Days { get; set; }
        public string? Upto7Days { get; set; }
        public string? Upto2Weeks { get; set; }
        public string? Upto1Month { get; set; }
        public int CriticalQuestionID { get; set; }
        public string? Comments { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int IsCritical { get; set; }
        public int ApplicationID { get; set; }
    }
    #endregion

    #region BIASurveyVitalRecord


    public class BIAVitalRecord
    {
        public int VitalRecordId { get; set; }
        public string? VitalRecordName { get; set; }
        public int BIAID { get; set; }
        public int ProcessID { get; set; }
        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public string? TypeOfRecord { get; set; }
        public string? Description { get; set; }
        public int RecordOwnerID { get; set; }
        public string? RecordOwnerName { get; set; }
        public string? NeedBy { get; set; }
        public string? Site { get; set; }
        public string? Facility { get; set; }
        public string? IsBackUp { get; set; }
        public int QuestionID { get; set; }
        public string? QuestionDetails { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public string? ResourceName { get; set; }
        public string? Impact { get; set; }
        public string? SecondaryStorageLocation { get; set; }
        public string? ProcessName { get; set; }
        public int IsComplete { get; set; }

        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DeptID { get; set; }
        public int SubDept { get; set; }

        public string? Version { get; set; }
    }


    #endregion

    #region BIASurveyQDefault


    public class BIASurveyQDefault
    {
        public int ID { get; set; }
        public int ProcessID { get; set; }
        public int QuestionID { get; set; }
        public string? Description { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }

    }


    #endregion

    #region BIASurveyQ7_CriticalProc_Owner


    public class BIASurveyQ7_CriticalProc_Owner
    {
        public int ID { get; set; }
        public int ProcessID { get; set; }
        public string? Upto2Hrs { get; set; }
        public string? Upto4Hrs { get; set; }
        public string? Upto8Hrs { get; set; }
        public string? Upto12Hrs { get; set; }
        public string? Upto2Days { get; set; }
        public string? Upto3Days { get; set; }
        public string? Upto7Days { get; set; }
        public string? Upto2Weeks { get; set; }
        public string? Upto1Month { get; set; }
        public int CriticalQuestionID { get; set; }
        public string? Comments { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }


    #endregion

    #region BIATimeInterval
    public class BIATimeInterval
    {
        public int ID { get; set; }
        public string? MinInterval { get; set; }
        public string? MinIntervalUnit { get; set; }
        public string? MaxInterval { get; set; }
        public string? MaxIntervalUnit { get; set; }
        public string? TimeIntervalText { get; set; }
        public int CreatorID { get; set; }
        public int UpdatorID { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int OrgID { get; set; }
    }
    #endregion

    #region BCMStrategyOptionMaster
    public class BCMStrategyOptionMaster
    {
        public int OptionID { get; set; }
        public string? OptionName { get; set; }
        public string? OptionDescription { get; set; }
        public int EntityID { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int IsActive { get; set; }
    }
    #endregion BCMStrategyOptionMaster

    #region BCMStrategyRatingMaster
    public class BCMStrategyRatingMaster
    {
        public int ID { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Weight { get; set; }
        public string? CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int IsActive { get; set; }
    }
    #endregion BCMStrategyRatingMaster

    #region BulkUploadTransaction
    public class BulkUploadTransaction
    {
        public int TranID { get; set; }
        public string? ForTable { get; set; }
        public string? TranName { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public int OrgGroupID { get; set; }
        public int OrgID { get; set; }

        public string? OrgGroupName { get; set; }
        public string? OrgName { get; set; }
        public string? CreatedByName { get; set; }

        public int TotalRecords { get; set; }
        public int Processed { get; set; }
        public int Unprocessed { get; set; }

        public int Created { get; set; }
        public int Updated { get; set; }
        public int FailedToCreate { get; set; }
        public int FailedToUpdate { get; set; }

    }


    #endregion BulkUploadTransaction

    #region BCMStrategy
    public class BCMStrategy : UserActivityStatus
    {
        public int StratID { get; set; }
        public string? StratCode { get; set; }
        public string? StrategyName { get; set; }
        public string? Description { get; set; }
        public string? OwnerID { get; set; }
        public string? ApproverID { get; set; }
        public string? EntityTypeID { get; set; }
        public string? RecordID { get; set; }
        public string? Status { get; set; }
        public DateTime LastReviewDate { get; set; }
        public DateTime NextReviewDate { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }

        public string? EntityName { get; set; }

        public int OrgID { get; set; }
        public string? OrganizationName { get; set; }

        public int UnitID { get; set; }
        public string? UnitName { get; set; }

        public int DepartmentID { get; set; }
        public string? DepartmentName { get; set; }

        public int SubfunctionId { get; set; }
        public string? SubfunctionName { get; set; }

        public string? OwnerName { get; set; }
        public string? OwnerMobile { get; set; }
        public string? OwnerEmail { get; set; }

        public string? ApproverName { get; set; }
        public string? ApproverMobile { get; set; }
        public string? ApproverEmail { get; set; }

        public string? StratEditorHtmlText { get; set; }
        public string? StratEditorPlainText { get; set; }

        public int OrgHeadId { get; set; }
        public int UnitHeadID { get; set; }
        public int AltUnitHeadID { get; set; }
        public int UnitBCPCorID { get; set; }
        public int AltBCPCorID { get; set; }
        public int DepartmentHeadID { get; set; }
        public int AltDepartmentHeadID { get; set; }
        public int SubFunOwnerId { get; set; }
        public int AltSubFunOwnerId { get; set; }

        public int OrgGroupID { get; set; }
        public string? EntityType { get; set; }

        public string? MobilePhone { get; set; }
        public string? CompanyEmail { get; set; }
        public int IsTemplate { get; set; }

    }
    #endregion BCMStrategy

    #region Facility_Holiday
    public class Facility_Holiday
    {
        public int ID { get; set; }
        public int SrID { get; set; }
        public int FacilityID { get; set; }
        public DateTime HolidayDate { get; set; }
        public string? HolidayReason { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
    }


    #endregion Facility_Holiday

    #region Shift_Timings
    public class Shift_Timings
    {
        public int ID { get; set; }
        public int SrID { get; set; }
        public int ProcessID { get; set; }
        public int BIAFacilityID { get; set; }
        public string? From_Shift { get; set; }
        public string? To_Shift { get; set; }
        public int ResourceCount { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
    #endregion Shift_Timings

    #region BCMStrategyOptions
    public class BCMStrategyOptions
    {
        public int SOptionID { get; set; }
        public int StratID { get; set; }
        public int OptionID { get; set; }
        public string? RangeFrom { get; set; }
        public string? RangeFromUnit { get; set; }
        public string? RangeTo { get; set; }
        public string? RangeToUnit { get; set; }
        public string? OptionScore { get; set; }
        public int IsPreferred { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
    }


    #endregion BCMStrategyOptions

    #region BCMStrategyRatings
    public class BCMStrategyRatings
    {
        public int SRID { get; set; }
        public int SOptionID { get; set; }
        public int OptionID { get; set; }
        public int RatingID { get; set; }
        public string? RatingValue { get; set; }
    }


    #endregion BCMStrategyOptions

    #region BCMEntitiesTypeMaster
    public class BCMEntitiesTypeMaster
    {

        public int BCMEntityID { get; set; }

        public string? BCMEntityName { get; set; }

        public string? Description { get; set; }

        public int IsActive { get; set; }

        public DateTime CreatedDate { get; set; }

        public int CreatedBy { get; set; }

        public DateTime UpdatedDate { get; set; }

        public int UpdatedBy { get; set; }

        public string? Field1 { get; set; }

        public string? Field2 { get; set; }

        public string? Field3 { get; set; }

        public string? Field4 { get; set; }
    }



    #endregion

    #region BCMSubEntitiesMaster
    public class BCMSubEntitiesMaster
    {
        public int BCMSubEntityID { get; set; }
        public string? BCMSubEntityName { get; set; }
        public int BCMEntityTypeID { get; set; }
        public string? Description { get; set; }
        public int IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public string? BCMEntityType { get; set; }
    }



    #endregion

    #region CVDetailMaster    
    public class CVDetailMaster
    {
        public int DetailID { get; set; }
        public string? Status { get; set; }
        public Object? DetailFile { get; set; }
        public int DetailCount { get; set; }
        public string? DetailType { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public int OrgGroupID { get; set; }
        public int guid { get; set; }
        public int OrgID { get; set; }
        public string? DetailKey { get; set; }
        public string? DetailCategory { get; set; }
        public int RequestID { get; set; }
        public string? RequestCode { get; set; }
        public string? DetailCode { get; set; }
        public string? LicenceEntity { get; set; }

    }

    #endregion

    #region CVMain
    public class CVMain
    {
        public int ID { get; set; }
        public int Number { get; set; }
        public int OrgID { get; set; }
        public string? PKEY { get; set; }
        public string? TrialHours { get; set; }
        public int DetailID { get; set; }
    }


    #endregion

    #region CVDetailsApplication    
    public class CVDetailsApplication
    {
        public int ID { get; set; }
        public string? DetailType { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int OrgID { get; set; }
        public int OrgGroupID { get; set; }
        public int CreatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public string? DetailCategory { get; set; }
        public int DetailCount { get; set; }
        public string? Description { get; set; }
        public string? EmailSentTo { get; set; }
        public string? EmailCCTo { get; set; }
        public string? EmailBCCTo { get; set; }
        public string? SentStatus { get; set; }
        public string? RequestCode { get; set; }
        public string? EmailSubject { get; set; }
        public string? EmailMessage { get; set; }
        public int IsEffective { get; set; }
        public int DetailID { get; set; }
        public DateTime CreatedDate { get; set; }
        public string? ResourceName { get; set; }
        public string? OrganizationName { get; set; }
        public string? OrganizationGroupName { get; set; }
        public string? LicenceEntity { get; set; }
    }
    #endregion

    #region CVDetailEmails
    public class CVDetailEmails
    {
        public int ID { get; set; }
        public string? EmailSentTo { get; set; }
        public string? EmailCCTo { get; set; }
        public string? EmailBCCTo { get; set; }
        public int IsActive { get; set; }
        public string? RequestCode { get; set; }
        public string? EmailSubject { get; set; }
        public string? EmailMessage { get; set; }
        public int RecordID { get; set; }

    }
    #endregion

    #region CVDetailAttachments

    public class CVDetailAttachments
    {
        public int ID { get; set; }
        public int RequestID { get; set; }
        public Object? Attachment { get; set; }
        public string? FileExt { get; set; }
        public string? FileName { get; set; }
        public int IsActive { get; set; }


    }
    #endregion

    #region BCMSubEntitityPrivileges

    public class BCMSubEntityPrivileges
    {
        public int ID { get; set; }
        public int BCMEntityID { get; set; }
        public int BCMSubEntityID { get; set; }
        public int PrivilegeID { get; set; }
    }


    #endregion

    #region MenuPrivilegeMaster

    public class MenuPrivilegeMaster
    {
        public int ID { get; set; }
        public string? PrivilegeName { get; set; }
        public string? PrivilegeDetails { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
    }

    #endregion

    #region LangaugeMaster
    public class LangaugeMaster
    {
        public int ID { get; set; }
        public string? LanguageCode { get; set; }
        public string? LanguageName { get; set; }
        public string? Culture { get; set; }
        public string? UICulture { get; set; }
        public string? Description { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }

    }



    #endregion

    #region BusinessFunction

    public class BusinessFunction
    {

        public int FunctionID { get; set; }
        public string? FunctionName { get; set; }
        public string? TypeOfFunction { get; set; }
        public int FunctionIsActive { get; set; }
        public string? FunctionDetails { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }

    #endregion

    #region BusinessImpactAnalysis

    public class BusinessImpactAnalysis
    {
        public int BusinessImpactAnalysisID { get; set; }
        public string? BusinessImpactAnalysisStatus { get; set; }
        public string? BusinessImpactAnalysisName { get; set; }
        public string? BusinessImpactAnalysisTopic { get; set; }
        public string? BusinessImpactAnalysisPlannedStart { get; set; }
        public DateTime BusinessImpactAnalysisStartDate { get; set; }
        public string? BusinessImpactAnalysisPlannedCompletion { get; set; }
        public DateTime BusinessImpactAnalysisCompletionDate { get; set; }
        public int BusinessImpactAnalysisOwner { get; set; }
        public int BusinessImpactAnalysisApprovedBy { get; set; }
        public int BusinessImpactAnalysisSection { get; set; }
        public string? BusinessImpactAnalysisComments { get; set; }
        public int BusinessImpactAnalysisIsActive { get; set; }
        public DateTime BusinessImpactAnalysisCreateDate { get; set; }
        public DateTime BusinessImpactAnalysisUpdateDate { get; set; }
    }
    #endregion

    #region ContinuityManagement

    public class ContinuityManagement
    {
        //Define and asscoiate the Develop Strategies -read/write only
        public int ContinuityManagementID { get; set; }
        public int ContinuityManagementEntityID { get; set; }
        public string? ContinuityManagementStatus { get; set; }
        public string? ContinuityManagementName { get; set; }
        public string? ContinuityManagementStage { get; set; }
        public string? ContinuityManagementPercentageCompleted { get; set; }
        public DateTime ContinuityManagementPlannedStart { get; set; }
        public DateTime ContinuityManagementStartDate { get; set; }
        public string? ContinuityManagementPlannedCompletion { get; set; }
        public DateTime ContinuityManagementCompletionDate { get; set; }
        public string? ContinuityManagementPrimaryResource { get; set; }
        public string? ContinuityManagementDescription { get; set; }
        public string? ContinuityManagementComments { get; set; }
        public int ContinuityManagementIsActive { get; set; }
        public DateTime ContinuityManagementCreateDate { get; set; }
        public DateTime ContinuityManagementUpdateDate { get; set; }
    }
    #endregion

    #region DevelopStrategies
    public class DevelopStrategiesInfo
    {
        public int StrategiesID { get; set; }
        public string? StrategiesName { get; set; }
        public int StatusID { get; set; }
        public string? TimeToImplement { get; set; }
        public DateTime PlanStartDate { get; set; }
        public DateTime PlanCompletionDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime CompletionDate { get; set; }
        public string? StrategyCost { get; set; }
        public string? MonthlyCost { get; set; }
        public int StrategyOwnerID { get; set; }
        public int ApprovedByID { get; set; }
        public string? StrategyDescription { get; set; }
        public string? Comments { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region DamageAssessmentTeam
    public class DamageAssessmentTeam
    {
        public int ID { get; set; }
        public int UnitID { get; set; }
        public string? UnitName { get; set; }
        public int DepartmentID { get; set; }
        public string? DepartmentName { get; set; }
        public int ResourceID { get; set; }
        public string? ResourceName { get; set; }
        public string? ResourceMail { get; set; }
        public int ResourceMobile { get; set; }
        public int FacilityID { get; set; }
        public string? FacilityName { get; set; }
        public int FacilityManagerID { get; set; }
        public string? FacilityManager { get; set; }
        public string? FacilityAddress { get; set; }
        public string? Comment { get; set; }
        public int IsActive { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }

    #endregion

    #region DecalreDisaster
    public class DisasterManagement : UserActivityStatus
    {

        public int DisasterID { get; set; }

        public string? DisasterName { get; set; }

        public string? Status { get; set; }

        public int UnitID { get; set; }

        public string? UnitName { get; set; }

        public int ApproverID { get; set; }

        public int OwnerID { get; set; }

        public string? ApproverName { get; set; }

        public string? Description { get; set; }

        public string? Comment { get; set; }

        public int IsActive { get; set; }

        public DateTime ChangedAt { get; set; }

        public int ChangedBy { get; set; }

        public int IncidentTypeID { get; set; }

        public int OrgId { get; set; }

        public string? IncidentCode { get; set; }

        public int ApproverMobile { get; set; }

        public int ApproverEmail { get; set; }

        public string? OwnerName { get; set; }

        public string? OrganizationName { get; set; }

        public int DepartmentID { get; set; }

        public string? DepartmentName { get; set; }

        public int SubfunctionID { get; set; }

        public string? SubfunctionName { get; set; }

        public string? CompanyEmail { get; set; }

        public string? MobilePhone { get; set; }

        public string? Version { get; set; }

        public int IsEffective { get; set; }

        public int RiskID { get; set; }

        public DateTime ReviewDate { get; set; }

        public string? NotifiedAsDrillOrLive { get; set; }

        public int PlanID { get; set; }

        public int OrgHeadId { get; set; }

        public int UnitHeadID { get; set; }

        public int AltUnitHeadID { get; set; }

        public int UnitBCPCorID { get; set; }

        public int AltBCPCorID { get; set; }

        public int DepartmentHeadID { get; set; }

        public int AltDepartmentHeadID { get; set; }

        public int SubFunOwnerId { get; set; }

        public int AltSubFunOwnerId { get; set; }

        public DateTime LastReviewDate { get; set; }

        public int Overdueby { get; set; }

        public string? RiskCode { get; set; }

        public int OrgGroupID { get; set; }

        public string? NotifiedAsDrill { get; set; }

        public string? NotifiedAsLive { get; set; }

        public string? RiskTo { get; set; }

        public string? RiskToItem { get; set; }

        public string? RiskToProcessCode { get; set; }

        public int CreatedBy { get; set; }
    }

    public class Disaster
    {
        public int ID { get; set; }
        public int DisasterID { get; set; }
        public int DisasterTypeID { get; set; }
        public string? DisasterName { get; set; }
        public string? DisasterTypeName { get; set; }
        public string? DisasterDescription { get; set; }
        public string? DisasterTypeDescription { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public string? AVG { get; set; }
        public int Counts { get; set; }
        public int FacilityID { get; set; }
        public string? FacilityName { get; set; }
        public DateTime ThreatsenarioID { get; set; }
        public string? ThreatsenarioName { get; set; }
        public DateTime Value { get; set; }
    }

    public class SiteTaThreatAssessment
    {
        public int ID { get; set; }
        public int UnitID { get; set; }
        public string? Unit { get; set; }
        public int DisasterID { get; set; }
        public string? Disaster { get; set; }
        public int DisasterTypeID { get; set; }
        public string? DisasterType { get; set; }
        public int DepartmentID { get; set; }
        public string? Department { get; set; }
        public int UserID { get; set; }
        public string? User { get; set; }
        public string? City { get; set; }
        public int FacilityID { get; set; }
        public string? Facility { get; set; }
        public int SeverityID { get; set; }
        public string? Severity { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int ThreatSenarioID { get; set; }
        public string? value { get; set; }
    }

    #endregion

    #region DisasterPlanning
    public class DisasterPlanning
    {
        public int ID { get; set; }
        public string? PlaningElements { get; set; }
        public int FullyAddressed { get; set; }
        public string? PartiallyAddressed { get; set; }
        public string? NotAddressedYet { get; set; }
        public string? AdditionalComments { get; set; }
        // 01-10-2013 Surendra
        public DateTime AvailabilityDate { get; set; }
        public string? Responsibility { get; set; }
        public string? Status { get; set; }
        public string? Remarks { get; set; }
        // 01-10-2013
        public int PlanReferenceID { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }


    #endregion

    #region Attachments

    public class DocumentManagementInfo : UserActivityStatus
    {
        public int EntityID { get; set; }
        public int DocId { get; set; }
        public int DocVerId { get; set; }
        public int AttachmentId { get; set; }
        public int AttachmentIdBlob { get; set; }

        public int OrgID { get; set; }
        public string? OrgName { get; set; }
        public int UnitID { get; set; }
        public string? UnitName { get; set; }
        public int DeptID { get; set; }
        public string? DepartmentName { get; set; }
        public string? SubFunction { get; set; }
        public string? SubFunctionName { get; set; }

        public string? DocCode { get; set; }
        public string? DocName { get; set; }
        public string? DocDescription { get; set; }
        public int IsActive { get; set; }
        public int IsEffective { get; set; }
        public string? DocStatus { get; set; }
        public string? version { get; set; }


        public int OwnerId { get; set; }
        public string? OwnerName { get; set; }
        public int? Ownermobile { get; set; }
        public string? OwnerMail { get; set; }

        public int AltOwnerId { get; set; }
        public string? AltOwnerName { get; set; }
        public int AltOwnermobile { get; set; }
        public string? AltOwnerMail { get; set; }

        public int ApproverID { get; set; }
        public string? ApproverName { get; set; }
        public int ApproverMobile { get; set; }
        public string? ApproverMail { get; set; }

        public int CreatedBy { get; set; }
        public string? CreatedByName { get; set; }
        public DateTime CreatedDate { get; set; }

        public int UpdatedBy { get; set; }
        public string? UpdatedByName { get; set; }
        public DateTime UpdatedDate { get; set; }

        public string? Attachment { get; set; } //BLOB Field
        public Object AttachmentObj { get; set; } //BLOB Field

        public int FileSize { get; set; }
        public string? MimeType { get; set; }
        public string? GUID { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime EffectiveDateVerLev { get; set; }
        public DateTime ReviewDate { get; set; }
        public DateTime LastReviewDate { get; set; }
        public string? SaveAsType { get; set; }
        public long OrginalFile { get; set; }
        public byte[] Blob { get; set; }
        public int flag { get; set; }
        public int CVattchmentID { get; set; }
        public int OrgGroupID { get; set; }
    }


    #endregion

    #region Documentviewers


    public class DocumentviewersInfo
    {
        public int DocViewId { get; set; }
        public int DocID { get; set; }
        public int IsTeam { get; set; }
        public int ResourceID { get; set; }
        public int AccessID { get; set; }


    }


    #endregion Documentviewers

    #region DocumentViewersAccess


    public class DocumentViewersAccessInfo
    {
        public int DocViewId { get; set; }
        public int DocID { get; set; }
        public int IsTeam { get; set; }
        public int ResourceID { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ChangedAt { get; set; }
        public DateTime ChangedDate { get; set; }
    }



    #endregion DocumentViewersAccess

    #region EnablerPerformed

    public class EnablerPerformed
    {
        public int EnablerPerformedId { get; set; }
        public int ProcessEnablerId { get; set; }
        public string? Type { get; set; }
        public string? PerformedLocally { get; set; }
        public string? TextFiled1 { get; set; }
        public string? TextFiled2 { get; set; }
        public string? TextFiled3 { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }
    #endregion

    #region EntityReviewHistory

    public class EntityReview : UserActivityStatus
    {
        public int ID { get; set; }
        public int EntityID { get; set; }
        public string? EntityName { get; set; }
        public int RecordID { get; set; }
        public string? RecordName { get; set; }
        public DateTime NextReviewDate { get; set; }
        public int ReviewerID { get; set; }
        public string? ReviewerName { get; set; }

        public string? ReviewerMobile { get; set; }
        public string? ReviewerEmail { get; set; }

        public DateTime ReviewedAt { get; set; }
        public DateTime ReviewStartDate { get; set; }
        public DateTime ReviewEndDate { get; set; }
        public string? Status { get; set; }
        public string? Remarks { get; set; }

        public string? EscalationExist { get; set; }
        public int EscalationMatrixID { get; set; }
        public int EscalationMapID { get; set; }
        public string? EscalationTime { get; set; }
        public string? EscalationTimeUnit { get; set; }

        public int ChangedBy { get; set; }
    }

    #endregion

    #region ExercisePlan


    public class ExercisePlan
    {
        public int ExerciseId { get; set; }
        public string? PlanName { get; set; }
        public string? ExerciseDue { get; set; }
        public string? ExerciseCompleted { get; set; }
        public string? ExerciseCoordinator { get; set; }
        public string? Status { get; set; }
        public string? Type { get; set; }
        public string? ExerciseStart { get; set; }
        public string? ActionsImplemented { get; set; }
        public string? Objective { get; set; }
        public string? Log { get; set; }
        public string? Scenario { get; set; }
        public string? RecommendedActions { get; set; }
        public string? ActionImplemented { get; set; }
        public string? Comment { get; set; }
    }


    #endregion

    #region "JobConfigMaster"


    public class JobConfigMasterInfo
    {
        public int JobConfigID { get; set; }
        public int OrgID { get; set; }
        public int JobID { get; set; }
        public string? CronExpression { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? JobNameText { get; set; }
        public string? JobNameValue { get; set; }
        public string? ExecutionSetting { get; set; }

        public string? EveryMinute { get; set; }
        public string? EveryHour { get; set; }
        public string? EveryDay { get; set; }

        public string? EveryHourlyMinute { get; set; }

        public string? EveryDaily { get; set; }


        public string? MonthTimeType { get; set; }
        public string? SelectedMonth { get; set; }
        public string? WeekdayHours { get; set; }
        public string? WeekdayMinutes { get; set; }
        public string? SelectedWeekdays { get; set; }
        public string? DayOfMonth { get; set; }
        public string? DayOfMonthHours { get; set; }
        public string? DayOfMonthMinutes { get; set; }

    }


    #endregion "JobConfigMaster"

    #region JobMaster

    public class JobMasterInfo
    {
        public int JobID { get; set; }
        public string? JobNameText { get; set; }
        public string? JobNameValue { get; set; }
    }

    #endregion

    #region EscalationMatrix



    public class EscalationMatrix
    {

        public int EscMatID { get; set; }


        public string? EscMatName { get; set; }


        public string? EscMatType { get; set; }


        public string? EscMatStatus { get; set; }



        public int IsActive { get; set; }

        public int EscMapID { get; set; }



        public string? BCMEntityType { get; set; }


        public int BCMEntityID { get; set; }

        public int CreatedBy { get; set; }


        public DateTime CreatedDate { get; set; }


        public int UpdatedBy { get; set; }


        public DateTime UpdatedDate { get; set; }

        public DateTime EscStartDate { get; set; }


        public string? CreatedByName { get; set; }

        public string? UpdatedByName { get; set; }


        //public string? EscMatApprovedDate { get; set; }


        public string? EscMatCode { get; set; }


        public int MatrixCount { get; set; }


        public string? EscMatDesc { get; set; }


        public int EscMatApproverID { get; set; }


        public int EscMatOwnerID { get; set; }


        public string? EscMatApprovedDate { get; set; }


        public string? EscMatApproverName { get; set; }


        public int OrgID { get; set; }

        public int UnitID { get; set; }

        public int count { get; set; }


        public string? OrgName { get; set; }


        public string? EscMatOwnerName { get; set; }


        public string? EscTime { get; set; }


        public string? EscTimeUnit { get; set; }


        public string? EscFromUser { get; set; }


        public int UserRoleID { get; set; }


        public int OrgGroupID { get; set; }
    }



    #endregion

    #region EscalationMatrixFyi


    public class EscalationMatrixFyi
    {
        public int ID { get; set; }
        public int EscMapID { get; set; }
        public int ResEntityTypeID { get; set; }
        public int ResourceID { get; set; }
        //public string? BCMEntityType { get; set; }
        //public string? BCMEntityID { get; set; }

        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }


    }


    #endregion

    #region EscalationLevelConfig



    public class EscalationLevelConfig
    {

        public int LevConID { get; set; }

        public int EscMatMapID { get; set; }

        public int EscMatID { get; set; }

        public string? LevName { get; set; }

        public string? LevDetails { get; set; }

        public string? EscLevel { get; set; }

        public string? CanApprove { get; set; }

        public string? AnyOrAll { get; set; }


        public string? EscTime { get; set; }

        public string? TimeUnit { get; set; }

        public string? EscLevStatus { get; set; }

        public string? LevEscDate { get; set; }


        public string? Levsequence { get; set; }

        public int LevConResID { get; set; }

        public int EscLevConfigID { get; set; }

        public string? ResEntityType { get; set; }

        public int ResourceID { get; set; }

        public int CreatedBy { get; set; }

        public int UpdatedBy { get; set; }

        public int LevResID { get; set; }

        public int FromTeamID { get; set; }

        public int IsApproved { get; set; }

        public string? ResourceName { get; set; }


        public string? EscMatStatus { get; set; }

        public int IncidentStepID { get; set; }

        public int IsLevelNotified { get; set; }


        public int LevelCount { get; set; }
    }


    #endregion

    #region EscalationLevel



    public class EscalationLevel
    {

        public int EscLevID { get; set; }

        public int EscLevConfigID { get; set; }

        public int EscMatID { get; set; }

        public string? EscLevel { get; set; }

        public int EntityID { get; set; }

        public string? ResEntityType { get; set; }

        public string? ResourceID { get; set; }

        public string? FromTeamID { get; set; }


        public int IsApproved { get; set; }
        //public string? AnyOrAll { get; set; }


        public int CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; }

        public int UpdatedBy { get; set; }

        public DateTime UpdatedDate { get; set; }
    }


    #endregion

    #region Facilities

    public class FacilityAndHoliday
    {
        public Facility Facility { get; set; }
        public Facility_Holiday Facility_Holiday { get; set; }
    }
    public class Facility : UserActivityStatus
    {
        public int FacilityID { get; set; }
        public string? FacilityCode { get; set; }

        public bool btnSave { get; set; }

        [Required(ErrorMessage = "FacilityName is required.")]
        public string? FacilityName { get; set; }
        public string? FacilitySecondryName { get; set; }
        public string? FacilityStatus { get; set; }
        public string? FacilityType { get; set; }
        public string? FacilityAddress { get; set; }
        public string? FacilityManager { get; set; }
        public string? FacilityManagerName { get; set; }
        public string? FacilityNumberOfResources { get; set; }
        public string? FacilityAccomadatedResoures { get; set; }
        public string? FacilityDetails { get; set; }
        public string? FacilityComments { get; set; }
        public int FacilityIsActive { get; set; }

        [Required]
        public int FacilityUnitID { get; set; }

        public int FacilityDepartmentID { get; set; }

        public int FacilitySubDepartmentID { get; set; }
        public string? FacilityUnitName { get; set; }
        public DateTime FacilityCreateDate { get; set; }
        public DateTime FacilityUpdateDate { get; set; }
        public string? ProcessName { get; set; }
        public string? MinimumResource { get; set; }
        public string? RTO { get; set; }
        public string? Department { get; set; }
        public string? Unit { get; set; }
        public int ChangedBy { get; set; }
        public string? Location { get; set; }
        public string? Latitude { get; set; }
        public string? Longitude { get; set; }
        public string? LocationName { get; set; }
        public int OrgID { get; set; }
        public string? OrgName { get; set; }

        public int UnitHeadId { get; set; }
        public int AltUnitHeadId { get; set; }
        public int UnitBCPCorId { get; set; }
        public int AltBCPCorId { get; set; }
        public string? CompanyEmail { get; set; }
        public string? MobilePhone { get; set; }
        public int EntityTypeID { get; set; }
        public string? Field1 { get; set; }
        public string? Field2 { get; set; }
        public string? Field3 { get; set; }
        public string? Field4 { get; set; }
        public int OrgGroupID { get; set; }
        public int OwnerID { get; set; }
        public int AltOwnerID { get; set; }
        public int ApproverID { get; set; }
        public int IsButtonVisible { get; set; }
    }

    public class FacilityJson
    {
        public int Id { get; set; }
        public string? city { get; set; }
        public string? country { get; set; }
        public string? location { get; set; }
        public string? address { get; set; }

        public string? address2 { get; set; }
        public string? phone { get; set; }
        public string? fax { get; set; }
        public string? email { get; set; }
    }

    #endregion

    #region FMEA

    public class ITFMEAInfo
    {
        public int Id { get; set; }
        public int UnitId { get; set; }
        public int DepartmentId { get; set; }
        public int ResourceId { get; set; }
        public string? RiskItem { get; set; }
        public string? PotentialFailureMode { get; set; }
        public string? PotentialEffectFailure { get; set; }
        public string? Severity { get; set; }
        public string? PotentialCauseFailure { get; set; }
        public string? Occurrence { get; set; }
        public string? DetectionofFailureMode { get; set; }
        public string? CurrentPrevention { get; set; }
        public string? CurrentContinuity { get; set; }
        public string? CurrentRecovery { get; set; }
        public string? EffectiveCurrentControl { get; set; }
        public string? RPN { get; set; }
        public string? RecommendedDetective { get; set; }
        public string? RecommendedPrevention { get; set; }
        public string? RecommendedContinuity { get; set; }
        public string? RecommendedRecovery { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int ChangedBy { get; set; }
    }

    #endregion

    #region FMEASeverity

    public class FMEASeverity
    {
        public int ID { get; set; }
        public string? Ranking { get; set; }
        public string? SeverityEffect { get; set; }
        public string? SeverityofEffect { get; set; }
        public string? OccurrenceProbabilityofFailure { get; set; }
        public string? OccurrenceFailureRates { get; set; }
        public string? OccurrenceEquivDPMO { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }

    #endregion

    #region FMEAEffectiveControls


    public class FMEAEffectiveControls
    {
        public int ID { get; set; }
        public string? DetRating { get; set; }
        public string? DetDescription { get; set; }

        public string? PrevRating { get; set; }
        public string? PrevDescription { get; set; }

        public string? ConRating { get; set; }
        public string? ConDescription { get; set; }

        public string? RecRating { get; set; }
        public string? RecDescription { get; set; }

        public string? EffectiveRating { get; set; }
        public int IsActive { get; set; }
        public int ChangedBy { get; set; }
        public string? ChangedAt { get; set; }
    }

    #endregion

    #region IncidentManagement
    public class IncidentManagement : UserActivityStatus
    {
        public int Id { get; set; }
        public string? EventName { get; set; }
        public int DisasterId { get; set; }
        public string? DisasterName { get; set; }
        public string? DisasterTime { get; set; }
        public string? NotificationTime { get; set; }
        public string? EstimatedRecoveryTime { get; set; }
        public string? ActualRecoveryTime { get; set; }
        public int AuthorizedByID { get; set; }
        public int IsActive { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public DateTime UpdateDate { get; set; }
        public int NotifiedBy { get; set; }
        public string? NotifierName { get; set; }
        public int IsNotified { get; set; }
        public string? Description { get; set; }
        public string? Status { get; set; }
        public string? DisasterCount { get; set; }
        public string? DisaterOccured { get; set; }
        public string? DrillCount { get; set; }
        public string? DrillTime { get; set; }
        public string? NotifiedAs { get; set; }
        public string? TemplateText { get; set; }
        public string? RecordsCount { get; set; }
        public string? CostExpenditure { get; set; }
        public string? IncidentCode { get; set; }
        public int OrgID { get; set; }
        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerEmail { get; set; }
        public BigInteger OwnerMobile { get; set; }
        public string? OwnersMobile { get; set; }
        public int AltOwnerID { get; set; }
        public string? AltOwnerName { get; set; }
        public string? AltOwnerEmail { get; set; }
        public int AltOwnerMobile { get; set; }
        //public string? MobileVerifiedTooltip{ get; set; }
        //public string? EmailVerifiedTooltip{ get; set; }
        public int TotalCount { get; set; }
        public int PartialCount { get; set; }
        public int ActiveCount { get; set; }
        public int InActiveCount { get; set; }
        public int ProcessID { get; set; }
        //public string? IsActive_Mobile{ get; set; }
        //public string? IsActive_Email{ get; set; }
        //public string? LastEmailResponse{ get; set; }
        //public string? LastMobileResponse{ get; set; }
        public int UnitID { get; set; }
        public string? UnitName { get; set; }
        public int DepartmentID { get; set; }
        public string? DepartmentName { get; set; }
        //  public string? OrgID{ get; set; }
        public string? OrganizationName { get; set; }
        public int SubFunctionID { get; set; }
        public string? SubFunctionName { get; set; }
        public int OrgGroupID { get; set; }
        public int BCMEntityID { get; set; }
        public int BCMEntityTypeID { get; set; }
        public int RiskID { get; set; }
        public string? RiskType { get; set; }
        public string? ClientIncidentCode { get; set; }
        public string? ClientCode { get; set; }
        public string? ActiveStatusImg { get; set; }
        public int PlanTypeID { get; set; }

        public List<int>? IncidentTeams { get; set; }
        public string? Password { get; set; }
        public string? ButtonType { get; set; }





        //public string? IsActive_Mobile{ get; set; }
        //public string? IsActive_Email{ get; set; }

        //public string? LastEmailResponse{ get; set; }
        //public string? LastMobileResponse{ get; set; }







        public int StepId { get; set; }

        public string? StepName { get; set; }

        public string? StepDescription { get; set; }

        public int TimeTaken { get; set; }

        public int TimeUnit { get; set; }

        public int IncidentStepID { get; set; }
        public int IncidentID { get; set; }

        public int DisasterID { get; set; }
        public string? IncNotificationTime { get; set; }
        public string? ActualTime { get; set; }

        public string? ExecutedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public string? StepOwnerName { get; set; }
        public int StepOwnerMobileNo { get; set; }
        public string? StepOwnerEmail { get; set; }
        public int AltStepOwnerMobileNo { get; set; }
        public string? AltStepOwnerEmail { get; set; }
        public string? TaskName { get; set; }
        public int TaskOwnerID { get; set; }
        public string? TaskOwnerName { get; set; }
        public int TaskOwnerMobile { get; set; }
        public string? TaskOwnerEmail { get; set; }

        public int PlanID { get; set; }

        public string? PlanName { get; set; }

        public string? Remarks { get; set; }

        public string? NotifiedByMobile { get; set; }
        public string? NotifiedByEmail { get; set; }
        public string? NotifiedToName { get; set; }
        public string? NotifiedToMobile { get; set; }

        public string? NotifiedToEmail { get; set; }
        public int CommunicationID { get; set; }

        public string? MailSubject { get; set; }
        public string? Body { get; set; }
        public string? NotificationDate { get; set; }
        public int NotifiedTo { get; set; }
        public string? CreatedAt { get; set; }
        public int TeamID { get; set; }
        public int inbound { get; set; }
        public int StepExecutionStatus { get; set; }
    }




    #endregion

    #region NotifyIncident

    public class NotifyIncident
    {
        public int IncidentID { get; set; }
        public int OrgID { get; set; }
        public int UserID { get; set; }
        public int PlanExecutionType { get; set; }
    }

    //public class NotifyIncident
    //{
    //    public int IncidentID { get; set; }
    //    public int PlanExecutionType { get; set; }
    //    public int NotificationType { get; set; }
    //    public string? DisasterName { get; set; }
    //    public int DisasterID { get; set; }
    //    public string? IncidentDate { get; set; }
    //    public string? NotificationDate { get; set; }
    //    public List<int>? IncidentTeams { get; set; }
    //    public string? Password { get; set; }
    //    public string? TemplateMarkup { get; set; }
    //    public string? ButtonType { get; set; }
    //}

    #endregion

    #region IncidentNotification


    public class IncidentNotificationHistory
    {

        public int ID { get; set; }

        public int IncidentID { get; set; }

        public int IncidentStepID { get; set; }

        public int StepID { get; set; }

        public int CommunicationID { get; set; }

        public string? Subject { get; set; }

        public string? Status { get; set; }

        public string? Body { get; set; }

        public int TeamID { get; set; }

        public int IsInbound { get; set; }

        public DateTime NotificationDate { get; set; }

        public int NotifiedBy { get; set; }

        public string? NotifiedTo { get; set; }

        public DateTime CreateddAt { get; set; }

        public string? StepExecutionStatus { get; set; }
    }


    #endregion

    #region IncidentNotificationTeam


    public class IncidentNotificationTeam
    {

        public int ID { get; set; }

        public int IncidentID { get; set; }

        public int TeamID { get; set; }

        public string? IncidentName { get; set; }

        public string? TeamName { get; set; }

        public int ResourceId { get; set; }

        public string? ResourceMail { get; set; }

        public string? ResourceMobilePhone { get; set; }

        public DateTime CreatedAt { get; set; }

        public int CreatedBy { get; set; }

        public DateTime ChangedAt { get; set; }

        public int ChangedBy { get; set; }
    }

    #endregion

    #region Imapct

    public class Impact
    {
        public int ImpactID { get; set; }
        public string? ImpactName { get; set; }
        public int ImpactTypeID { get; set; }
        public string? ImpactTypeName { get; set; }
        public string? ImpactDetails { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        //Added by rishikesh
        public int MatrixId { get; set; }
        public int Sequence { get; set; }

    }


    #endregion

    #region ImapctType

    public class ImpactType
    {
        public int ImpactTypeID { get; set; }
        public string? ImpactTypeName { get; set; }
        public string? ImpactTypeDetails { get; set; }
        public string? ImpactName { get; set; }
        public int ImpactID { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }


    #endregion

    #region ImpactSeverity

    public class ImpactSeverity
    {
        public int ImpSeverityID { get; set; }
        public string? ImpSeverityValue { get; set; }
        public string? Description { get; set; }
    }



    #endregion

    #region Insurance

    public class Insurance
    {
        //Define and asscoiate the Insurance -read/write only
        public int InsuranceID { get; set; }
        public int EntityID { get; set; }
        public string? InsuranceName { get; set; }
        public int StatusID { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string? Premium { get; set; }
        public int PolicyID { get; set; }
        public string? Insurer { get; set; }
        public int InsurerContact { get; set; }
        public int InsuranceAdminID { get; set; }
        public string? Description { get; set; }
        public string? Comments { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }
    #endregion

    #region InternalAuditProgram

    public class InternalAuditProgramInfo
    {
        public int ID { get; set; }
        public int UnitID { get; set; }
        public string? SBU { get; set; }
        public string? Entity { get; set; }
        public string? Description { get; set; }
        public string? AuditorName { get; set; }
        public string? AuditName { get; set; }
        public string? FacilityName { get; set; }
        public string? AuditorBusinessFunction { get; set; }
        public string? LocationORFacility { get; set; }
        public string? AuditeeName { get; set; }
        public string? AuditeeBusinessFunction { get; set; }
        public DateTime IA1AuditStartDate { get; set; }
        public string? IA1AuditClosingMeeting { get; set; }
        public DateTime IA2AuditStartDate { get; set; }
        public string? IA2AuditClosingMeeting { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int ChangedBy { get; set; }

        public DateTime PlanStartDate { get; set; }
        public DateTime PlanEndDate { get; set; }
        public DateTime ActualStartDate { get; set; }
        public DateTime ActualEndDate { get; set; }


        public string? AuditorMobile { get; set; }
        public string? AuditorEmail { get; set; }
        public string? AuditeeMobile { get; set; }
        public string? AuditeeEmail { get; set; }

    }

    #endregion

    #region IncidentType


    public class IncidentType
    {

        public int IncidentTypeID { get; set; }

        public string? IncidentTypeName { get; set; }

        public string? IncidentDetails { get; set; }

        public DateTime ChangedAt { get; set; }

        public string? ChangedBy { get; set; }

        public int DisasterID { get; set; }

        public string? DisasterName { get; set; }
    }
    #endregion

    #region Locations

    public class LocationMaster
    {
        public int Id { get; set; }
        public string? Locationcode { get; set; }
        public string? LocationName { get; set; }
        public int OrgGroupID { get; set; }
        public string? OrgGroupName { get; set; }
        public string? OrgName { get; set; }
        public string? UnitName { get; set; }
        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public string? Latitude { get; set; }
        public string? Longitude { get; set; }
        public int IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime UpdatedAt { get; set; }
        public int UpdatedBy { get; set; }
        public int EntityTypeID { get; set; }
        public string? Field1 { get; set; }
        public string? Field2 { get; set; }
        public string? Field3 { get; set; }
        public string? Field4 { get; set; }
        public int IsButtonVisible { get; set; }
    }


    #endregion

    #region SiteFMEA

    public class SiteFMEAInfo
    {
        public int Id { get; set; }
        public int UnitId { get; set; }
        public int DepartmentId { get; set; }
        public int ResourceId { get; set; }
        public int FacilityId { get; set; }
        public string? RiskItem { get; set; }
        public string? PotentialFailureMode { get; set; }
        public string? PotentialEffectFailure { get; set; }
        public string? Severity { get; set; }
        public string? PotentialCauseFailure { get; set; }
        public string? Occurrence { get; set; }
        public string? DetectionofFailureMode { get; set; }
        public string? CurrentPrevention { get; set; }
        public string? CurrentContinuity { get; set; }
        public string? CurrentRecovery { get; set; }
        public string? EffectiveCurrentControl { get; set; }
        public string? RPN { get; set; }
        public string? RecommendedDetective { get; set; }
        public string? RecommendedPrevention { get; set; }
        public string? RecommendedContinuity { get; set; }
        public string? RecommendedRecovery { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int ChangedBy { get; set; }
    }

    #endregion

    #region NCCATracker

    public class NCCATracker
    {
        public int ID { get; set; }
        public int UnitID { get; set; }
        public int UnitHeadID { get; set; }
        public int UnitBCPCoOrdinatorID { get; set; }
        public string? Observation { get; set; }
        public string? Source { get; set; }
        public string? SourceType { get; set; }
        public DateTime DateReported { get; set; }
        public string? RootCause { get; set; }
        public string? Correction { get; set; }
        public string? CorrectiveAction { get; set; }
        public string? Priority { get; set; }
        public string? Responsibility { get; set; }
        public DateTime TargetDate { get; set; }
        public DateTime CompletionDate { get; set; }
        public string? Status { get; set; }
        public int ReviewedBy { get; set; }
        public DateTime ReviewDate { get; set; }
        public string? Remarks { get; set; }
        public int IsActive { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public string? NCACode { get; set; }
        public int IsEffective { get; set; }
        public int DepartmentID { get; set; }
        public int SubfunctionID { get; set; }
    }


    #endregion

    #region RecoveryPlan
    public class RecoveryPlan
    {
        //Define and asscoiate the Develop Strategies -read/write only

        public int ID { get; set; }
        public int ID_Encrypted { get; set; }

        public string? RPlanCode { get; set; }

        public string? PlanName { get; set; }

        public int PlanOwnerID { get; set; }

        public string? PlanOwnerName { get; set; }

        public int PlanApproverID { get; set; }

        public string? PlanApprovarName { get; set; }

        public int PlanStageID { get; set; }

        public int OrgID { get; set; }

        public int UnitID { get; set; }

        public int DepartmentID { get; set; }

        public int SubfunctionID { get; set; }

        public string? OrgName { get; set; }

        public string? UnitName { get; set; }

        public string? DepartmentName { get; set; }

        public string? SubfunctionName { get; set; }

        public DateTime PlanCreateDate { get; set; }

        public DateTime LastRevisionDate { get; set; }

        public string? Version { get; set; }

        public int IsEffective { get; set; }

        public string? IncidentType { get; set; }

        public string? Comments { get; set; }

        public int IsActive { get; set; }

        public DateTime CreatedAt { get; set; }

        public int CreatedBy { get; set; }

        public DateTime ChangedAt { get; set; }

        public int ChangedBy { get; set; }

        public string? MobilePhone { get; set; }

        public string? CompanyEmail { get; set; }

        public int IsTemplate { get; set; }

        public string? ApproverEmail { get; set; }

        public string? ApproverMobile { get; set; }

        public DateTime PlanReviewDate { get; set; }

        public string? Drill { get; set; }

        public string? Live { get; set; }

        public string? EstiTime { get; set; }

        public string? Overdue { get; set; }

        public string? Reminder { get; set; }

        public string? RecurrenceRule { get; set; }

        public int OrgHeadId { get; set; }

        public int UnitHeadID { get; set; }

        public int AltUnitHeadID { get; set; }

        public int UnitBCPCorID { get; set; }

        public int AltBCPCorID { get; set; }

        public int DepartmentHeadID { get; set; }

        public int AltDepartmentHeadID { get; set; }

        public int SubFunOwnerId { get; set; }

        public int AltSubFunOwnerId { get; set; }

        public int DisasterID { get; set; }


        public string? XML { get; set; }

        public string? SyncXML { get; set; }

        public object? SyncXMLDiagram { get; set; }


        public string? RiskProfileName { get; set; }


        public string? RiskProfileVersion { get; set; }

        public string? RiskProfileStatus { get; set; }

        public int OrgGroupID { get; set; }

        public string? EntityType { get; set; }

        public string? Status { get; set; }

        public string? IncidentCode { get; set; }

        public string? DisasterName { get; set; }

        public DateTime LastReviewDate { get; set; }

        public string? NotifiedAs { get; set; }

        public DateTime PlanRevisionDate { get; set; }

        public int NotifiedBy { get; set; }

        public int IsNotified { get; set; }

        public string? DisasterTime { get; set; }

        public string? NotificationTime { get; set; }

        public string? EstimatedRecoveryTime { get; set; }

        public string? ActualRecoveryTime { get; set; }

        public string? EstimatedRecoveryTimeUnit { get; set; }
        //added by pushkar

        public string? PlanCode { get; set; }

        public int PlanID { get; set; }

        public int ProcessID { get; set; }

        public string? ProcessCode { get; set; }

        public string? RiskCode { get; set; }

        public string? ActivationModeMobile { get; set; }
        public string? MobileVerified { get; set; }
        public string? MobileVerifiedTooltip { get; set; }
        public string? ActivationModeEmail { get; set; }

        public string? EmailVerified { get; set; }
        public string? EmailVerifiedTooltip { get; set; }
        public string? ActivationModeMobileAltr { get; set; }

        public string? MobileVerifiedAltr { get; set; }
        public string? ActivationModeEmailAltr { get; set; }
        public string? MobileVerifiedTooltipAltr { get; set; }
        public string? EmailVerifiedAltr { get; set; }
        public string? EmailVerifiedTooltipAltr { get; set; }
        public string? PlanDescription { get; set; }

    }

    public class RecoveryPlanReminders
    {
        public int PlanID { get; set; }
        public string? PlanName { get; set; }
        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? MobilePhone { get; set; }
        public string? CompanyEmail { get; set; }
        public DateTime PlanRevisionDate { get; set; }
        public DateTime CommunicationDate { get; set; }
        public int CommunicationMode { get; set; }
    }

    #endregion

    #region RecoveryPlanEntityDetails

    public class RecoveryPlanEntityDetails
    {
        //Define and asscoiate the Develop Strategies -read/write only
        public int ID { get; set; }
        public int PlanID { get; set; }
        public string? PlanName { get; set; }
        public int EntityID { get; set; }
        public int RecordID { get; set; }
        public string? ProcessName { get; set; }
        public string? FacilityName { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }

    #endregion

    #region ResourceTraining

    public class ResourceTraining
    {
        //Define and asscoiate the Develop Strategies -read/write only
        public int ResourceTrainingID { get; set; }
        public string? ResourceTrainingStatus { get; set; }
        public string? ResourceTrainingFirstName { get; set; }
        public string? ResourceTrainingSurName { get; set; }
        public string? ResourceTrainingDepartment { get; set; }
        public string? ResourceTrainingTitle { get; set; }
        public string? ResourceTrainingDesignatedBackup { get; set; }
        public string? ResourceTrainingType { get; set; }
        public string? ResourceTrainingRole { get; set; }
        public string? ResourceTrainingPrimarySkills { get; set; }
        public string? Training { get; set; }
    }
    #endregion

    #region Task Group

    public class TaskGroup
    {
        public int TaskGroupId { get; set; }
        public string? TaskGroupName { get; set; }
        public int TaskId { get; set; }
        public string? Purpose { get; set; }
        public string? Comments { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region ResourcesAvailability

    public class ResourcesAvailability
    {
        public int ID { get; set; }
        public int ResourceID { get; set; }
        public string? ResourceName { get; set; }
        public string? ResourceTitle { get; set; }
        public int DepartmentID { get; set; }
        public string? DepartmentName { get; set; }
        public int StatusID { get; set; }
        public string? Role { get; set; }
        public DateTime AvailabilityDate { get; set; }
        public string? Comments { get; set; }
        public int IsActive { get; set; }
        public int CreatorID { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region DisaterPlan

    public class DisasterPaln
    {
        public int ID { get; set; }
        public int DisasterID { get; set; }
        public int PlanID { get; set; }
        public string? PlanCode { get; set; }
        public string? Status { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int UserID { get; set; }
        public int TaskID { get; set; }
        public string? StepOwnerName { get; set; }
        public string? StepOwnerEmail { get; set; }
        public string? StepOwnerOtherEmail { get; set; }
        public string? StepOwnerMphone { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerEmail { get; set; }
        public string? OwnerOtherEmail { get; set; }
        public string? OwnerMphone { get; set; }
        public string? PrincipalOwner { get; set; }
        public string? PrincipalEmail { get; set; }
        public string? PrincipalOtherEmail { get; set; }
        public string? PricipalMphone { get; set; }
        public string? IncidentName { get; set; }
        public string? planName { get; set; }
        public string? TaskName { get; set; }
        public string? stepName { get; set; }
        public string? Description { get; set; }
        public string? Timetaken { get; set; }
        public string? Interdependency { get; set; }
        public string? IncidentCode { get; set; }

    }


    #endregion

    #region UserRole

    public class UserRoleInfo
    {
        public int UserRoleID { get; set; }
        public string? UserRoleName { get; set; }
        public string? UserRoleDetails { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int CretedBy { get; set; }
        public int UpdatedBy { get; set; }
        public int OrgGroupID { get; set; }
        public int OrgID { get; set; }
        public string? OrganizationGroupName { get; set; }
        public string? OrganizationName { get; set; }
        public string? SessionTimeOut { get; set; }
    }
    #endregion

    #region User Role Master

    public class UserRoleMasterInfo
    {
        public int UserRoleID { get; set; }
        public string? UserRoleName { get; set; }
        public string? UserRoleDetails { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int CretedBy { get; set; }
        public int UpdatedBy { get; set; }
        public int OrgGroupID { get; set; }
        public int OrgID { get; set; }
        public string? OrgGroupName { get; set; }
        public string? OrgName { get; set; }
    }
    #endregion

    #region BCMGroup

    public class BCMGroupInfo
    {
        public int GroupID { get; set; }

        public string? GroupName { get; set; }

        public string? Description { get; set; }

        public string? GroupEmailID { get; set; }

        public int GroupMapID { get; set; }

        public int OwnerID { get; set; }

        public int AltOwnerID { get; set; }

        public int SiteId { get; set; }

        public int OrgID { get; set; }

        public int UnitId { get; set; }

        public int DepartmentId { get; set; }
        public int ProcessId { get; set; }

        public int TotalCount { get; set; }

        public int IsActive { get; set; }

        public int ActiveCount { get; set; }
        public int InActiveCount { get; set; }

        public int PartialCount { get; set; }

        public DateTime CreateDate { get; set; }

        public DateTime UpdateDate { get; set; }

        public int CreatedBy { get; set; }

        public int UpdatedBy { get; set; }

        public string? SiteName { get; set; }

        public string? UnitName { get; set; }

        public string? DepartmentName { get; set; }

        public string? ProcessName { get; set; }

        public string? CreatedByName { get; set; }

        public string? UpdatedByName { get; set; }


        public string? OwnerName { get; set; }

        public string? OwnerEmail { get; set; }

        public BigInteger OwnerMobile { get; set; }

        public string? OwnersMobile { get; set; }

        public string? AltOwnerName { get; set; }

        public string? AltOwnerEmail { get; set; }

        public BigInteger AltOwnerMobile { get; set; }

        public int OrgGroupID { get; set; }


        public string? OrganizationName { get; set; }

        public int is_Active { get; set; }
        //public string? MobileVerifiedTooltip{ get; set; }
        //public string? EmailVerifiedTooltip{ get; set; }

        // public string? IsActive_Mobile{ get; set; }
        // public string? IsActive_Email{ get; set; }

        //public string? LastEmailResponse{ get; set; }
        //public string? LastMobileResponse{ get; set; }

        public int ActiveStatusImg { get; set; }

        //public string? ActivationModeMobile
        //{
        //    get{ get; set; }
        //    set{ get; set; }
        //}

        //public string? ActivationModeEmail
        //{
        //    get{ get; set; }
        //    set{ get; set; }
        //}

        //public string? MobileVerified { get; set; }

        //public string? EmailVerified { get; set; }
    }

    public class SubResourceModel
    {
        public List<ResourcesInfo>? MainResources { get; set; }
        public List<int>? SubBCMResourcesID { get; set; }
    }



    public class BCMTeamsAndResources
    {
        public List<ResourcesInfo>? ResourceList { get; set; }

        public List<BCMGroupInfo>? BCMGroupList { get; set; }
    }

    public class BCMGroupResources : ResourcesInfo
    {
        public int GroupMapID { get; set; }

        public string? GroupName { get; set; }

        public int ResourceId { get; set; }

        //public string? ResourceName { get; set; }

        //public BigInteger MobilePhone { get; set; }

        //public string? CompanyEmail { get; set; }

        //public DateTime CreateDate { get; set; }

        //public DateTime UpdateDate { get; set; }

        public int CreatedBy { get; set; }

        public int UpdatedBy { get; set; }

        public string? SiteName { get; set; }

        //public string? UnitName { get; set; }

        //public string? DepartmentName { get; set; }

        public string? ProcessName { get; set; }

        public string? SentSuccess { get; set; }

        public string? Mode { get; set; }

        public string? Response { get; set; }

        public DateTime ResponseDate { get; set; }

        public string? NotificationAs { get; set; }

        //public string? Address { get; set; }
    }

    public class NotificationRequest : UserAction
    {
        public string? TxtMsg { get; set; }
        public string? TxtSubject { get; set; }
        public string? DdlNotification { get; set; }
        public string? DdlTimeOut { get; set; }
        public string? TxtTimeTaken { get; set; }
        public string? TxtPassword { get; set; }
        public int IChkResponse { get; set; }
        public IFormFile? File { get; set; }
    }

    public class UserAction
    {
        public List<int> UsersFYA { get; set; } = new List<int>();
        public List<int> UsersFYI { get; set; } = new List<int>();
        public List<int> TeamsFYA { get; set; } = new List<int>();
        public List<int> TeamsFYI { get; set; } = new List<int>();
    }

    public class BCMGroupNotification
    {

        public int ID { get; set; }

        public string? GroupName { get; set; }

        public int NotificationID { get; set; }

        public int GroupID { get; set; }

        public int groupMapId { get; set; }

        public string? Subject { get; set; }

        public string? MailBody { get; set; }

        public int IncidentID { get; set; }

        public int MgtGroupMapId { get; set; }

        public int NotifiedBy { get; set; }

        public string? ResourceName { get; set; }

        public int resourceID { get; set; }

        public int CommunicationMode { get; set; }

        public string? IncidentName { get; set; }

        public DateTime NotiDate { get; set; }

        public string? NotiType { get; set; }

        public string? SentSuccess { get; set; }

        public string? NotifiedFYIA { get; set; }

        public string? NeedResponse { get; set; }

        public string? GUIDAttachment { get; set; }

        public DateTime? EndTime { get; set; }

        public string? TimeUnit { get; set; }

        public int UnitId { get; set; }

        public int OrgId { get; set; }

        public int NotificationAs { get; set; }


        public int TotalCount { get; set; }

        public int ResponseCount { get; set; }
    }

    #endregion

    #region ProcessEnabler

    public class ProcessEnabler
    {
        public int ProcessEnablerId { get; set; }
        public string? LineofBusiness { get; set; }
        public string? FunctionName { get; set; }
        public string? FunctionalPlanner { get; set; }
        public string? ProcessChampion { get; set; }
        public string? MegaProcess { get; set; }
        public string? ProcessBucket { get; set; }
        public string? ActivitiesPerformed { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }
    #endregion

    #region ProcessRA

    public class ProcessRA
    {
        public int CorrespondenceId { get; set; }
        public int ProcessId { get; set; }
        public int EnablerId { get; set; }
        public string? Enabler { get; set; }
        public string? Threat { get; set; }
        public string? ThreatScenario { get; set; }
        public string? Vulnerabilities { get; set; }
        public string? Impact { get; set; }
        public string? Probability { get; set; }
        public int RiskValue { get; set; }
        public int RiskRating { get; set; }
        public string? Preventive { get; set; }
        public string? Rating1 { get; set; }
        public string? Detective { get; set; }
        public string? Rating2 { get; set; }
        public string? Recovery { get; set; }
        public string? Rating3 { get; set; }
        public string? CurrentValue { get; set; }
        public string? CurrentRating { get; set; }
        public string? RiskExposure { get; set; }
        public string? RiskStatus { get; set; }
        public string? ManagementDecision { get; set; }
        public int? IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ReviewDate { get; set; }
    }

    #endregion

    #region PacaTracker

    public class PacaTrackerInfo
    {
        public int Id { get; set; }
        public int Id_Encrypted { get; set; }
        public int UnitID { get; set; }
        public int RiskID { get; set; }
        public string? CircleHub { get; set; }
        public string? Observation { get; set; }
        public string? Source { get; set; }
        public string? SourceType { get; set; }
        public string? DateReported { get; set; }
        public string? RootCause { get; set; }
        public string? Action { get; set; }
        public int IsCorrective { get; set; }
        public string? Priority { get; set; }
        public string? Responsibility { get; set; }
        public string? Responsible_Name { get; set; }
        public DateTime TargetDate { get; set; }
        public DateTime CompletionDate { get; set; }
        public string? PACATracker { get; set; }
        public string? Remarks { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int ChangedBy { get; set; }
        public string? PCACode { get; set; }
        public string? Status { get; set; }
        public int IsEffective { get; set; }
        public int OrgID { get; set; }
        public int DepartmentID { get; set; }
        public int SubfunctionID { get; set; }
        public int isPCFlag { get; set; }
        public string? PACAType { get; set; }
        public string? MobilePhone { get; set; }
        public string? CompanyEmail { get; set; }
        public string? Reminder { get; set; }
        public string? RecurrenceRule { get; set; }
        //public string? IsActive_Mobile{ get; set; }
        //public string? IsActive_Email{ get; set; }
        //public string? MobileVerifiedTooltip{ get; set; }
        //public string? EmailVerifiedTooltip{ get; set; }
        public int ResourceID { get; set; }

        public int PACAID { get; set; }
        public string? PACADescription { get; set; }
        public DateTime NextReviewDate { get; set; }
        public DateTime LastReviewDate { get; set; }
        public int CreatedBy { get; set; }
        public int OrgGroupID { get; set; }

        public int PercentageCompletion { get; set; }

        public string? ActiveStatusImg { get; set; }

        public string? ActivationModeMobile { get; set; }
        public string? MobileVerified { get; set; }

        public string? MobileVerifiedTooltip { get; set; }
        public string? ActivationModeEmail { get; set; }

        public string? EmailVerified { get; set; }
        public string? EmailVerifiedTooltip { get; set; }
        public string? IsActive_Email { get; set; }
        public string? IsActive_Mobile { get; set; }

    }

    #endregion

    #region ProcessToSite

    public class ProcessToSite
    {
        public int ProcessToSiteId { get; set; }
        public string? LineofBusiness { get; set; }
        public string? FunctionName { get; set; }
        public string? FunctionalPlanner { get; set; }
        public string? ProcessChampion { get; set; }
        public string? ProcessName { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
    }
    #endregion

    #region SiteName

    public class SiteName
    {
        public string? SiteNameId { get; set; }
        public string? ProcessSiteId { get; set; }
        public string? TextFiled1 { get; set; }
        public string? Type { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
    }

    #endregion

    #region OrganizationStructure

    public class OrganizationStructure
    {
        public string? StructureId { get; set; }
        public string? Name { get; set; }
        public string? Details { get; set; }
    }
    #endregion

    #region SubFunction


    public class SubFunction : UserActivityStatus
    {

        public string? SubFunctionID { get; set; }
        // public string? SubFunID_Encrypted{ get; set; }

        public string? FunctionId { get; set; }

        public string? FunctionName { get; set; }

        public string? UnitID { get; set; }

        public string? UnitName { get; set; }

        public string? SubFunctionName { get; set; }

        public string? OwnerId { get; set; }

        public string? AlternateOwnerId { get; set; }

        public string? IsActive { get; set; }

        public string? ChangedBy { get; set; }

        public string? ChangedAt { get; set; }

        public string? DepartmentID { get; set; }

        public string? OrgID { get; set; }

        public string? OrganizationName { get; set; }


        public string? OwnerName { get; set; }

        public string? OwnerEmail { get; set; }

        public string? OwnerMobile { get; set; }

        public string? AltownerName { get; set; }

        public string? AltownerEmail { get; set; }

        public string? AltownerMobile { get; set; }

        public string? RoleID { get; set; }
        public string? DepartmentName { get; set; }

    }
    #endregion

    #region OrganizationSubFunctionDetails

    public class OrgSubDetails
    {
        public string? Id { get; set; }
        public string? StructureId { get; set; }
        public string? SubfunctionId { get; set; }
    }
    #endregion

    #region UnitSubFunctionDetails

    public class UnitSubDetails
    {
        public string? Id { get; set; }
        public string? UnitId { get; set; }
        public string? SubfunctionId { get; set; }
    }
    #endregion

    #region OrganizationUnits


    public class OrgUnit : ResourcesInfo
    {

        public string? UnitHeadID { get; set; }

        public string? UnitBCPCorID { get; set; }

        public string? AltUnitHeadID { get; set; }

        public string? AltBCPCorID { get; set; }

        public string? CreatedBy { get; set; }

        public string? RoleID { get; set; }

        public string? UnitHeadName { get; set; }

        public string? IsActive_Email3 { get; set; }

        public string? IsActive_Mobile3 { get; set; }

        public string? LastEmailResponse3 { get; set; }

        public string? LastMobileResponse3 { get; set; }

        public string? ActivationModeMobile3 { get; set; }

        public string? MobileVerified3 { get; set; }

        public string? ActivationModeEmail3 { get; set; }

        public string? EmailVerified3 { get; set; }

        public string? MobileVerifiedTooltip3 { get; set; }

        public string? EmailVerifiedTooltip3 { get; set; }
    }

    #endregion

    #region Organization

    #region OrganizationMaster

    public class OrganizationMaster
    {
        public int OrgGroupID { get; set; }
        public string? OrganizationGroupCode { get; set; }
    }

    public class OrgInfoAndAttachments
    {
        public Attachments Attachments { get; set; }

        public OrgInfo OrgInfo { get; set; }
    }

    public class OrgInfo
    {

        public string? Id { get; set; }

        //public string? _ID
        //{
        //    get
        //    {
        //        return Id{ get; set; }
        //    }
        //    set
        //    {
        //        Id = value{ get; set; }
        //    }
        //}


        // public string? Id_Encrypted{ get; set; }

        public string? OrganizationName { get; set; }

        //public string? _OrganizationName
        //{
        //    get
        //    {
        //        return OrganizationName{ get; set; }
        //    }
        //    set
        //    {
        //        OrganizationName = value{ get; set; }
        //    }
        //}

        public string? OrganizationCode { get; set; }

        public string? LegalEntity { get; set; }

        public string? CompanyAddress { get; set; }

        public string? Mobile { get; set; }

        public string? Fax { get; set; }

        public string? SPOCName { get; set; }

        public string? SPOCEmail { get; set; }

        public string? SPOCMobile { get; set; }

        public string? CreateDate { get; set; }

        public string? UpdateDate { get; set; }

        public string? ChangedBy { get; set; }

        public string? OrgGroupID { get; set; }

        public string? OrgHeadID { get; set; }

        public string? OrgHeadName { get; set; }

        public string? OrgHeadEmail { get; set; }

        public string? OrgHeadPhone { get; set; }

        public string? LoginCode { get; set; }

        public object OrgLogo { get; set; }

        public string? RoleID { get; set; }

        public string? Field1 { get; set; }

        public string? Field2 { get; set; }

        public string? Field3 { get; set; }

        public string? Field4 { get; set; }

        public string? OrgGroupName { get; set; }



        public string? ProductKey { get; set; }

        public string? ProductKeyAppliedDate { get; set; }

        public string? ProductKeyEntity { get; set; }


        public string? LicenseType { get; set; }


        public string? ActivationModeMobile { get; set; }

        public string? MobileVerified { get; set; }

        public string? MobileVerifiedTooltip { get; set; }

        public string? ActivationModeEmail { get; set; }

        public string? EmailVerified { get; set; }

        public string? EmailVerifiedTooltip { get; set; }

        public string? IsActive_Email { get; set; }
        public string? IsActive_Mobile { get; set; }
    }


    #endregion

    #region Orgnization

    public class OrganizeInfo
    {
        public int OrgGroupID { get; set; }
        public string? OrganizationName { get; set; }
        public string? OrganizationGroupName { get; set; }
        public string? OrganizationCode { get; set; }
        public string? OrganizationGroupCode { get; set; }
        public string? LegalEntity { get; set; }
        public string? CompanyAddress { get; set; }
        public string? Mobile { get; set; }
        public string? Fax { get; set; }
        public string? SPOCName { get; set; }
        public string? SPOCEmail { get; set; }
        public string? SPOCMobile { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
        public string? ChangedBy { get; set; }

    }


    #endregion

    #region OrganizationGroup

    public class OrgGroup : OrganizeInfo
    {
        //public int OrgGroupID{ get; set; }
        //// public string? OrgGroupID_Encrypted{ get; set; }
        //public string? OrganizationGroupName{ get; set; }
        //public string? OrganizationGroupCode{ get; set; }
        //public string? LegalEntity{ get; set; }
        //public string? CompanyAddress{ get; set; }
        //public string? Mobile{ get; set; }
        //public string? Fax{ get; set; }
        //public string? SPOCName{ get; set; }
        //public string? SPOCEmail{ get; set; }
        //public string? SPOCMobile{ get; set; }
        //public string? CreateDate{ get; set; }
        //public string? UpdateDate{ get; set; }
        //public string? ChangedBy{ get; set; }
    }


    #endregion

    #endregion

    #region ConfigurationSettings

    public class ConfigSettings : OrganizeInfo
    {
        public string? Id { get; set; }
        public string? configurationsectionname { get; set; }
        public string? DateFormat { get; set; }
        public string? DateTimeFormat { get; set; }

    }


    #endregion

    #region MasterpageEntrySettings

    public class MasterpageEntrySettings //: OrganizeInfo
    {
        public string? Id { get; set; }
        public string? MasterEntryTypeName { get; set; }

        public string? SubEntityId { get; set; }

        public string? OrgID { get; set; }

        public string? OrgGroupID { get; set; }

        public string? SubEntityName { get; set; }

        public string? PrevillageId { get; set; }
        public string? PrevillageName { get; set; }

        public string? SubEntityPrivilegeId { get; set; }
        public string? SubEntityPrivilegeName { get; set; }


        public string? IsSelected { get; set; }



    }


    #endregion

    #region OtherBCMEntities


    public class OtherBCMEntities
    {
        public string? ID { get; set; }
        public string? EntityCode { get; set; }
        public string? EntityName { get; set; }
        public string? EntityDescription { get; set; }
        public string? OrgID { get; set; }
        public string? UnitID { get; set; }
        public string? FunctionID { get; set; }
        public string? SubfunctionID { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? CreatorID { get; set; }
        public string? UpdateDate { get; set; }
        public string? UpdatorID { get; set; }

        public string? OrgName { get; set; }
        public string? UnitName { get; set; }
        public string? FunctionName { get; set; }
        public string? SubfunctionName { get; set; }
        public string? ProcessCode { get; set; }
        public string? OtherEntityType { get; set; }
        public int EntityTypeID { get; set; }
        public string? Field1 { get; set; }
        public string? Field2 { get; set; }
        public string? Field3 { get; set; }
        public string? Field4 { get; set; }
        public string? OrgGroupID { get; set; }
        public string? OwnerID { get; set; }
        public string? AltOwnerID { get; set; }
        public string? ApproverID { get; set; }
        public string? StatusID { get; set; }
        public int IsButtonVisible { get; set; }


    }



    #endregion

    public class BcmEntities
    {
        public int? ID { get; set; }
        public string? EntityCode { get; set; }
        public string? EntityName { get; set; }
        public string? EntityDescription { get; set; }
        public int? OrgID { get; set; }
        public int? UnitID { get; set; }
        public int? FunctionID { get; set; }
        public int? SubfunctionID { get; set; }
        public int? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public int? CreatorID { get; set; }
        public string? UpdateDate { get; set; }
        public int? UpdatorID { get; set; }

        public string? OrgName { get; set; }
        public string? UnitName { get; set; }
        public string? FunctionName { get; set; }
        public string? SubfunctionName { get; set; }
        public string? ProcessCode { get; set; }
        public string? OtherEntityType { get; set; }
        public int EntityTypeID { get; set; }
        public string? Field1 { get; set; }
        public string? Field2 { get; set; }
        public string? Field3 { get; set; }
        public string? Field4 { get; set; }
        public int? OrgGroupID { get; set; }
        public int? OwnerID { get; set; }
        public int? AltOwnerID { get; set; }
        public int? ApproverID { get; set; }
        public int? StatusID { get; set; }



    }

    #region DashBoard UintProcess

    public class UintProcess
    {
        public string? Count { get; set; }
        //public string? Name{ get; set; }
    }


    #endregion

    #region CriticalPartners

    public class CriticalPartners
    {
        public string? PartnerId { get; set; }
        public string? ProcessId { get; set; }
        public string? ProcessRto { get; set; }
        public string? ApplicationUsedId { get; set; }
        public string? PartnerSpocId { get; set; }
        public string? AltSpocId { get; set; }
        public string? PartnerSpoc { get; set; }
        public string? AltPartnerSpoc { get; set; }
    }


    #endregion

    #region PartnerReadiness

    public class PartnerReadiness
    {
        public string? ID { get; set; }
        public string? ReadinessId { get; set; }
        public string? PartnerId { get; set; }
        public string? ProcessID { get; set; }
        public string? ApplicationID { get; set; }
        public string? ControlDetails { get; set; }
        public string? ComplianceStatus { get; set; }
        public string? Remarks { get; set; }
        public string? AttachmentName { get; set; }
        public string? ActionType { get; set; }
    }

    #endregion

    #region RoleMenu

    public class RoleMenu
    {
        public string? MenuId { get; set; }
        public string? UserRole { get; set; }
        public string? MenuName { get; set; }
        public string? MenuLink { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
    }


    #endregion

    #region RTOHistory
    public class RTOHistory
    {
        public int ID { get; set; }
        public int ProcessID { get; set; }
        public string? ProcessName { get; set; }
        public int OldRTO { get; set; }
        public int NewRTO { get; set; }

        public int OldMTR { get; set; }
        public int NewMTR { get; set; }

        public int ResourceID { get; set; }
        public string? ResourceName { get; set; }
        public string? ResourceDesignation { get; set; }
        public string? Comments { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }


    #endregion

    #region SupportResources

    public class SupportResourcesInfo
    {
        public string? Id { get; set; }
        public string? UnitID { get; set; }
        public string? ProcessId { get; set; }
        public string? Resource { get; set; }
        public string? Threat { get; set; }
        public string? CausesofFailure { get; set; }
        public string? CurrentControls { get; set; }
        public string? RecommendedActions { get; set; }
        public string? ManagementDecision { get; set; }
        public string? Responsibility { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
        public string? ChangedBy { get; set; }
    }

    #endregion

    #region ThreatAssessment


    public class ThreatAssessment
    {
        public string? ID { get; set; }
        public string? UnitId { get; set; }
        public string? FunctionID { get; set; }
        public string? ResourceID { get; set; }
        public string? City { get; set; }
        public string? FacilityID { get; set; }
        public string? EDHurricanecyclone { get; set; }
        public string? EDFlood { get; set; }
        public string? EDDrought { get; set; }
        public string? EDEarthquake { get; set; }
        public string? EDLightning { get; set; }
        public string? EDFire { get; set; }
        public string? EDSubsidenceandLandslides { get; set; }
        public string? EDCEHazards { get; set; }
        public string? EDEpidemic { get; set; }
        public string? EDPandemeic { get; set; }
        public string? DDActofTerrorism { get; set; }
        public string? DDActofSabotage { get; set; }
        public string? DDActofwar { get; set; }
        public string? DDTheft { get; set; }
        public string? DDArson { get; set; }
        public string? DDLabourDisputes { get; set; }
        public string? USElectricalPowerFailure { get; set; }
        public string? USLossofWater { get; set; }
        public string? USPetroleumandOil { get; set; }
        public string? USCommunicationsservices { get; set; }
        public string? USLossofdrainage { get; set; }
        public string? ESFInternalPowerFailure { get; set; }
        public string? ESFAirConditioningFailure { get; set; }
        public string? ESFEquipmentFailure { get; set; }
        public string? SISICybercrime { get; set; }
        public string? SISILossofRecords { get; set; }
        public string? SISIDisclosure { get; set; }
        public string? SISIITSystemFailure { get; set; }
        public string? SISINetworkIntrusion { get; set; }
        public string? SISINetworkServicesFailure { get; set; }
        public string? OESWorkplaceViolence { get; set; }
        public string? OESPublicTransportation { get; set; }
        public string? OESNeighbourhoodHazard { get; set; }
        public string? OESHealthHazards { get; set; }
        public string? OESEmployeeMorale { get; set; }
        public string? OESMergersandAcquisitions { get; set; }
        public string? OESNegativePublicity { get; set; }
        public string? OESLegalProblems { get; set; }
        public string? OESGovernmentalActions { get; set; }
        public string? OESIdentityRelatedIntrusions { get; set; }
        public string? OESMassAbsence { get; set; }
        public string? IsActive { get; set; }
        public string? ChangedAt { get; set; }
        public string? ChangedBy { get; set; }
    }


    #endregion

    #region ToDoList

    public class ToDoList
    {
        public int ID { get; set; }
        public string? TaskDescription { get; set; }
        public int Status { get; set; }
        public string? ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int OrgID { get; set; }

    }

    #endregion

    #region RiskAssessment

    public class RiskAssessmentInfo
    {
        public string? RiskID { get; set; }
        public string? RiskName { get; set; }
        public string? RiskStatus { get; set; }
        public string? RiskLevel { get; set; }
        public string? RiskIdentified { get; set; }
        public string? RiskType { get; set; }
        public string? Likelihod { get; set; }
        public string? Consequences { get; set; }
        public string? ControlType { get; set; }
        public string? ControlImplemented { get; set; }
        public string? ControlCost { get; set; }
        public string? ControlValue { get; set; }
        public string? MonthlyCost { get; set; }
        public string? MonthlyValue { get; set; }
        public string? ControlAdequency { get; set; }
        public string? ControlImplementation { get; set; }
        public string? ResidualConsequences { get; set; }
        public string? ResidualLikeliHood { get; set; }
        public string? TreatmentStartDate { get; set; }
        public string? TreatmentCompletedDate { get; set; }
        public string? TreatmentDueDate { get; set; }
        public string? RiskPriority { get; set; }
        public string? RiskOwner { get; set; }
        public string? UnitId { get; set; }
        public string? FacilityId { get; set; }
        public string? RiskDescription { get; set; }
        public string? ControlDescription { get; set; }
        public string? TreatmentDescription { get; set; }
        public string? Comment { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
        public int? ProcessID { get; set; }
        public string? ProcessName { get; set; }
    }

    #endregion

    #region RiskAssessment

    public class RiskTrendInfo : UserActivityStatus
    {
        public int RiskTrendID { get; set; }
        public int RiskID { get; set; }
        public string? RiskName { get; set; }
        public int RiskTrendStatus { get; set; }
        public string? RiskLevel { get; set; }
        public int ImprovementRequired { get; set; }
        public int ImprovementStatus { get; set; }
        public string? ReasonforUpdate { get; set; }
        public string? ResidualRiskSeverity { get; set; }
        public int ResiRiskSeverity { get; set; } //added by Shubham B.
        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public int SubDepartmentID { get; set; }
        public string? RiskCode { get; set; }
        public int IsEffective { get; set; }
        public string? RiskDescription { get; set; }

        public int IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
        public int RiskStatus { get; set; }
        public int RiskRating { get; set; }

        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerMobile { get; set; }
        public string? OwnerEmail { get; set; }

        public string? OrgName { get; set; }
        public string? RiskVersion { get; set; }

        public int RiskSubRegID { get; set; }
        public string? NextReviewDate { get; set; }
        public string? OverdueBy { get; set; }

        public string? ResidualRiskSeverityValue; //added by Shubham B.

        public string? ImprovementRequiredText; //added by Shubham B.

        public string? ImprovementStatusText; //added by Shubham B.

        public string? TreatmentPlan; //added by Shubham B.

        //public string? RiskType{ get; set; }
        //public string? Likelihod{ get; set; }
        //public string? Consequences{ get; set; }
        //public string? ControlType{ get; set; }
        //public string? ControlImplemented{ get; set; }
        //public string? ControlCost{ get; set; }
        //public string? ControlValue{ get; set; }
        //public string? MonthlyCost{ get; set; }
        //public string? MonthlyValue{ get; set; }
        //public string? ControlAdequency{ get; set; }
        //public string? ControlImplementation{ get; set; }
        //public string? ResidualConsequences{ get; set; }
        //public string? ResidualLikeliHood{ get; set; }
        //public string? TreatmentStartDate{ get; set; }
        //public string? TreatmentCompletedDate{ get; set; }
        //public string? TreatmentDueDate{ get; set; }
        //public string? RiskPriority{ get; set; }
        //public string? RiskOwner{ get; set; }
        //public string? UnitId{ get; set; }
        //public string? FacilityId{ get; set; }

        //public string? ControlDescription{ get; set; }
        //public string? TreatmentDescription{ get; set; }
        //public string? Comment{ get; set; }
    }

    #endregion

    #region RiskTreatmentPlan

    public class RiskTreatmentPlan : EntityReview
    {
        public int Id { get; set; }
        public int RiskID { get; set; }
        public string? PlanName { get; set; }
        public int Owner { get; set; }
        public string? RiskLevel { get; set; }
        public string? IdentifiedDate { get; set; }
        public string? TargetStartDate { get; set; }
        public string? TargetEndDate { get; set; }
        public string? ActualStartDate { get; set; }
        public string? ActualEndDate { get; set; }
        public int PercentageCompletion { get; set; }
        public int risksubId { get; set; }
        public int CreateDate { get; set; }
        public string? UpdateDate { get; set; }
        public int CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }

        public int IsActive { get; set; }
        public string? IsEffective { get; set; }
        public int? TreatmentStatus { get; set; }
        public string? TreatmentCode { get; set; }
        public string? Remarks { get; set; }
        public string? ReasonForUpdate { get; set; }
        public DateTime RevStartDate { get; set; }
        public DateTime RevEndDate { get; set; }
        public string? OwnerName { get; set; }
        public string? Remark { get; set; }
        public string? IsEffectivePlan { get; set; }
        public int ApprovalStatus { get; set; }
    }


    #endregion

    #region RiskTreatmentPlanHistory

    public class RiskTreatmentPlanHistory
    {//Id, PlanId, RevStartDate, RevEndDate, ReasonForUpdate, CreateDate, UpdateDate, CreatedBy, UpdatedBy, IsActive
        public int Id { get; set; }
        public int PlanId { get; set; }
        public string? RevStartDate { get; set; }
        public string? RevEndDate { get; set; }
        public string? ReasonForUpdate { get; set; }
        public string? UpdateDate { get; set; }
        public int CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public string? IsActive { get; set; }
    }

    #endregion

    #region RecoveryTask

    public class RecoveryTaskInfo
    {
        public string? TaskID { get; set; }
        public string? BusinessProcessID { get; set; }
        public string? TaskName { get; set; }
        public string? StatusID { get; set; }
        public string? Version { get; set; }
        public string? TaskTypeID { get; set; }
        public string? Duration { get; set; }
        public string? PlannedStart { get; set; }
        public string? PlannedCompleted { get; set; }
        public string? ActualCompleted { get; set; }
        public string? ActualStart { get; set; }
        public string? TaskOwnerID { get; set; }
        public string? TaskOwnerName { get; set; }
        public string? TaskOwnerEmail { get; set; }
        public string? TaskOwnerMobile { get; set; }
        public string? Purpose { get; set; }
        public string? Procedures { get; set; }
        public string? Comments { get; set; }
        public string? PlanID { get; set; }
        public string? PlanName { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
        public string? ChangedBy { get; set; }
    }

    #endregion

    #region RecoveryTaskStep



    public class RecoveryTaskStepInfo
    {

        public string? StepID { get; set; }

        public string? TaskID { get; set; }

        public string? TaskName { get; set; }

        public string? TaskOwnerID { get; set; }

        public string? TaskOwnerName { get; set; }

        public string? TaskOwnerMobile { get; set; }

        public string? TaskOwnerEmail { get; set; }

        public string? StepName { get; set; }

        public string? StepDescription { get; set; }

        public string? ProcessID { get; set; }

        public string? DisasterID { get; set; }

        public string? DisasterName { get; set; }

        public string? PlanID { get; set; }

        public string? PlanName { get; set; }

        public string? IncidentID { get; set; }

        public string? IncidentName { get; set; }

        public string? IncidentCode { get; set; }

        public string? IncidentStepID { get; set; }

        public string? FailureScenario { get; set; }

        public string? StepAction { get; set; }

        public string? StepOwnerID { get; set; }


        public string? AltStepOwnerID { get; set; }

        public string? StepOwnerName { get; set; }

        public string? StepOwnerEmail { get; set; }

        public string? StepOwnerMobileNo { get; set; }


        public string? MobilePhone { get; set; }

        public string? CompanyEmail { get; set; }


        public string? MobilePhone_Alt { get; set; }

        public string? CompanyEmail_Alt { get; set; }


        public string? ActivationModeMobile { get; set; } // i.e. Active image path

        public string? MobileVerified { get; set; }

        public string? MobileVerifiedTooltip { get; set; }


        public string? ActivationModeEmail { get; set; }

        public string? EmailVerified { get; set; }

        public string? EmailVerifiedTooltip { get; set; }


        public string? ActivationModeMobile_Alt { get; set; } // i.e. Active image path

        public string? MobileVerified_Alt { get; set; }

        public string? MobileVerifiedTooltip_Alt { get; set; }


        public string? ActivationModeEmail_Alt { get; set; }

        public string? EmailVerified_Alt { get; set; }

        public string? EmailVerifiedTooltip_Alt { get; set; }


        public string? AltStepOwnerName { get; set; }

        public string? AltStepOwnerEmail { get; set; }

        public string? AltStepOwnerMobileNo { get; set; }


        public string? EstTime { get; set; }

        public string? TimeUnit { get; set; }

        public string? UnitID { get; set; }

        public string? Interdependency { get; set; }

        public string? DependentOn { get; set; }

        public string? StepStatus { get; set; }

        public string? IncidentStatus { get; set; }

        public string? CompletionTime { get; set; }

        public string? NotificationSentTime { get; set; }

        public string? ExecutedBy { get; set; }

        public string? UpdatedBy { get; set; }

        public string? Remarks { get; set; }

        public string? EmailTemplateText { get; set; }

        public string? ChangedAt { get; set; }

        public string? ChangedBy { get; set; }


        public string? IsCondition { get; set; }

        public string? SuccessStepID { get; set; }

        public string? FailureStepID { get; set; }

        public string? Sequence { get; set; }


        public string? StepOwnerguid { get; set; }

        public string? StepOwnermime { get; set; }


        public string? AltStepOwnerguid { get; set; }

        public string? AltStepOwnermime { get; set; }


        public string? TimeTaken { get; set; }

        public int OrgID { get; set; }

        public string? Message { get; set; }


        public string? ReassignedTo { get; set; }


        public string? ReassignedOwnerName { get; set; }

        public string? ReassignedOwnerEmail { get; set; }

        public string? ReassignedOwnerMobileNo { get; set; }

        public string? ReassignedOwnerguid { get; set; }

        public string? ReassignedOwnermime { get; set; }


        public string? ActivationModeEmail_Reassigned { get; set; }

        public string? EmailVerified_Reassigned { get; set; }

        public string? EmailVerifiedTooltip_Reassigned { get; set; }



        public string? ActivationModeMobile_Reassigned { get; set; } // i.e. Active image path

        public string? MobileVerified_Reassigned { get; set; }

        public string? MobileVerifiedTooltip_Reassigned { get; set; }


        public string? OwnerReceivedEmailStatus { get; set; }

        public string? OwnerReceivedSMSStatus { get; set; }

        public string? AltOwnerReceivedEmailStatus { get; set; }

        public string? AltOwnerReceivedSMSStatus { get; set; }

        public string? MessageSender { get; set; }


        public string? ReassignedOwnerReceivedEmailStatus { get; set; }

        public string? ReassignedOwnerReceivedSMSStatus { get; set; }

        public string? RemarksHtml { get; set; }

        public string? IsUsedinWorkflow { get; set; }


        public string? GoToStep { get; set; }

        public string? CPProfileID { get; set; }

        public string? CPParallelDROperationId { get; set; }
        public string? ActualCompletionTime { get; set; }
    }
    #endregion

    #region BusinessProcess

    public class BusinessProcessInfoAndReviewHistory
    {
        public BusinessProcessInfo BusinessProcessInfo { get; set; }
        public List<EntityReview> EntityReview { get; set; }
        public ButtonAcces ButtonAcces { get; set; }

    }

    public class BusinessProcessInfoAndReviewHistory_Test
    {
        public List<EntityReview> EntityReview { get; set; }
    }



    public class BusinessProcessInfo : UserActivityStatus
    {

        public int ID { get; set; }

        public int OrgID { get; set; }

        public string? OrgName { get; set; }

        public string? UnitName { get; set; }

        public int UnitHeadID { get; set; }

        public int UnitBCPCorID { get; set; }

        public int AltBCPCorID { get; set; }

        public string? DepartmentName { get; set; }

        public int DepartmentHeadID { get; set; }

        public int AltDepartmentHeadID { get; set; }

        public int SubfunctionID { get; set; }

        public string? SubFunctionName { get; set; }

        public int ProcessID { get; set; }

        public int ProcessID_Encrypted { get; set; }

        public string? ProcessName { get; set; }

        public int ProcessOwnerID { get; set; }

        public string? ProcessOwner { get; set; }

        public string? ProcessOwnerMobile { get; set; }

        public string? OwnerEmail { get; set; }

        public int AltProcessOwnerID { get; set; }

        public string? AltProcessOwner { get; set; }

        public string? AltProcessOwnerMobile { get; set; }

        public string? AltProcessOwnerEmail { get; set; }

        public DateTime? LastReviewDate { get; set; }

        public DateTime? ReviewDate { get; set; }

        public DateTime? ContractEndDate { get; set; }

        public string? Reminder { get; set; }

        public string? RecurrenceRule { get; set; }

        public int RecurrenceParentID { get; set; }


        public int PrimaryResourceID { get; set; }

        public string? PrimaryResource { get; set; }

        //public int SubFunctionID { get; set; }

        public int DepartmentID { get; set; }

        public int UnitID { get; set; }

        public int Status { get; set; }

        public string? Priority { get; set; }

        public string? ProcessType { get; set; }

        public string? LossPerDay { get; set; }

        public string? AttachedFile { get; set; }

        public string? ApprovalCode { get; set; }

        public string? MyAssigned { get; set; }

        public string? Comments { get; set; }

        public string? MinimumResource { get; set; }

        public string? ProcessDescription { get; set; }

        public int IsCritical { get; set; }

        public int IsActive { get; set; }

        public DateTime? CreateDate { get; set; }

        public DateTime? UpdateDate { get; set; }

        public string? OwnerRTO { get; set; }

        public string? MTR { get; set; }

        public string? OwnerMTR { get; set; }

        public int ApproverID { get; set; }

        public string? OverAllStatus { get; set; }

        public int ChangedBy { get; set; }

        public string? ProcessCode { get; set; }

        public string? Version { get; set; }

        public int IsEffective { get; set; }

        public string? RTO { get; set; }

        public string? RPO { get; set; }

        public string? RPOUnit { get; set; }

        public string? Comment { get; set; }

        public int ProcessIsActive { get; set; }

        public string? ProcessVersion { get; set; }

        public string? ApproverName { get; set; }

        public string? ApproverEmail { get; set; }

        public string? ApproverMobile { get; set; }

        public int CreatedBy { get; set; }

        public int OrgGroupID { get; set; }

        public int DetailID { get; set; }

        public int SunFunOwnerId { get; set; }

        public int AltSubFunOwnerId { get; set; }

        public int OrgHeadId { get; set; }


        public int AltUnitHeadId { get; set; }


        public DateTime? ProcessReviewDate { get; set; }


        public int IncidentPlanID { get; set; }

        public string? IncidentPlanName { get; set; }

        public DateTime? IncidentPlanReviewDate { get; set; }

        public string? IncidentPlanStatus { get; set; }

        public string? DrillStatus { get; set; }

        public DateTime RiskReviewDate { get; set; }


        public int RiskID { get; set; }

        public string? RiskCode { get; set; }

        public string? ProcessPCIScore { get; set; }


        public int ProfileID { get; set; }

        public int EntityTypeID { get; set; }//rohini

        public int RecordID { get; set; }

        public string? ESCMatType { get; set; }

        public string? BCMEntityType { get; set; }

        public string? PageName { get; set; }

        public string? TimeOperationT { get; set; }//shweta

        public string? TimeOperationF { get; set; }


        public string? PeakTranVolume { get; set; }

        public string? PeakPeriod { get; set; }

        public string? SPOF { get; set; }

        public string? PossibleSol { get; set; }

        public string? ProfileName { get; set; }

        public string? Field1 { get; set; }

        public string? Field2 { get; set; }

        public string? Field3 { get; set; }

        public string? Field4 { get; set; }

        public int BPProfileID { get; set; }


        public int WorkType { get; set; }

        public int HandOffs { get; set; }

        public int TeamSize { get; set; }

        public string? RTOText { get; set; }

        public string? CategoryRange { get; set; }

        public int MinRange { get; set; }

        public int MaxRange { get; set; }

        public int IsAddedToBCM { get; set; }

        public string? ApprovalStatus { get; set; }

        public int SectionID { get; set; }

        public int SectionName { get; set; }

        public int BIAID { get; set; }

        public int SubFunOwnerId { get; set; }

        public string? BIAPageURL { get; set; }

        public int IsBCMEntity { get; set; }

        public string? ProcessOwnerName { get; set; }

        public string? LocationName { get; set; }

        public string? Latitude { get; set; }

        public string? Longitude { get; set; }
        public int IsButtonVisible { get; set; }
    }

    public class ButtonAcces
    {
        public string? btnSave { get; set; }
        public string? btnUpdate { get; set; }
        public string? btnCreateVersion { get; set; }
        public string? btnDelete { get; set; }
        public string? btnApprove { get; set; }
        public string? btnSendForApproval { get; set; }
        public string? btnReSendApprove { get; set; }
        public string? btnDisApprove { get; set; }
        public string? btnImgDelete { get; set; }
        public string? btnImgApprove { get; set; }
        public string? btnImgDisApprove { get; set; }
    }
    public class BusinessProcessMap
    {
        public int ID { get; set; }
        public int ProcessId { get; set; }
        public int EntityID { get; set; }
        public int RecordID { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region BCMEntity

    public class BCMEntityInfo : UserActivityStatus
    {
        public int ID { get; set; }
        public int OrgID { get; set; }
        public string? OrgName { get; set; }
        public string? UnitName { get; set; }
        public int UnitHeadID { get; set; }
        public int UnitBCPCorID { get; set; }
        public int AltBCPCorID { get; set; }
        public string? DepartmentName { get; set; }
        public int DepartmentHeadID { get; set; }
        public int AltDepartmentHeadID { get; set; }
        public int SubfunctionID { get; set; }
        public string? SubFunctionName { get; set; }
        public int BCMEntityID { get; set; }
        public int BCMEntityID_Encrypted { get; set; }
        public string? BCMEntityName { get; set; }
        public int BCMEntityOwnerID { get; set; }
        public string? BCMEntityOwner { get; set; }
        public string? AltProcessOwner { get; set; }
        public int BCMEntityOwnerMobile { get; set; }
        public string? BCMEntityOwnerEmail { get; set; }
        public int AltBCMEntityOwnerID { get; set; }
        public string? AltBCMEntityOwner { get; set; }
        public int AltBCMEntityOwnerMobile { get; set; }
        public string? AltBCMEntitysOwnerEmail { get; set; }
        public DateTime LastReviewDate { get; set; }
        public DateTime ReviewDate { get; set; }
        public string? Reminder { get; set; }
        public string? RecurrenceRule { get; set; }
        public int PrimaryResourceID { get; set; }
        public string? PrimaryResource { get; set; }
        //public string? SubFunctionID{ get; set; }
        public int DepartmentID { get; set; }
        public int UnitID { get; set; }
        public int Status { get; set; }
        public string? Priority { get; set; }
        public string? BCMEntityType { get; set; }
        public int RecordID { get; set; }
        public string? BCMEntityTypeName { get; set; }
        public string? LossPerDay { get; set; }
        public string? AttachedFile { get; set; }
        public string? ApprovalCode { get; set; }
        public string? MyAssigned { get; set; }
        public string? Comments { get; set; }
        public string? MinimumResource { get; set; }
        public string? BCMEntityDescription { get; set; }
        public int IsCritical { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public string? UpdateDate { get; set; }
        public string? OwnerRTO { get; set; }
        public string? MTR { get; set; }
        public string? OwnerMTR { get; set; }
        public string? ApproverID { get; set; }
        public string? OverAllStatus { get; set; }
        public string? UpdatedBy { get; set; }
        public string? BCMEntityCode { get; set; }
        public string? Version { get; set; }
        public string? IsEffective { get; set; }
        public string? RTO { get; set; }
        public string? Comment { get; set; }
        public string? BCMEntityIsActive { get; set; }
        public string? BCMEntityVersion { get; set; }
        public string? ApproverName { get; set; }
        public string? ApproverEmail { get; set; }
        public string? ApproverMobile { get; set; }
        public string? CreatedBy { get; set; }
        //public int IsActive_Mobile{ get; set; }
        //public int IsActive_Email{ get; set; }
        public string? SunFunOwnerId { get; set; }
        public string? AltSubFunOwnerId { get; set; }
        public string? OrgHeadId { get; set; }

        public string? AltUnitHeadId { get; set; }

        public string? BCMEntityReviewDate { get; set; }

        public string? IncidentPlanID { get; set; }
        public string? IncidentPlanName { get; set; }
        public string? IncidentPlanReviewDate { get; set; }
        public string? IncidentPlanStatus { get; set; }
        public string? DrillStatus { get; set; }
        public string? RiskReviewDate { get; set; }

        public string? RiskID { get; set; }
        public string? RiskCode { get; set; }
        public string? BCMEntityPCIScore { get; set; }
        public string? ProfileID { get; set; }

        //public string? ActivationModeMobile
        //{
        //    get{ get; set; }
        //    set{ get; set; }
        //}

        //public string? ActivationModeEmail
        //{
        //    get{ get; set; }
        //    set{ get; set; }
        //}

        //public string? MobileVerified { get; set; }

        //public string? EmailVerified { get; set; }
        //public Approver Approver = new Approver(){ get; set; }


        public string? SubFunctionID { get; set; }
    }



    #endregion

    #region  BCMDirector

    public class BCMDirectorInfo : UserActivityStatus
    {

        public string? ID { get; set; }
        public string? BCMSectionID { get; set; }
        public string? BCMSubSectionID { get; set; }
        public string? SectionSeq { get; set; }
        public string? SubSectionSeq { get; set; }
        public string? OrgStructureTypeID { get; set; }
        public string? CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? ChangedAt { get; set; }
        public string? ChangedBy { get; set; }
        public string? url { get; set; }

        public string? SectionName { get; set; }
        public string? SectionDescription { get; set; }
        public string? sectionCreatedAt { get; set; }
        public string? sectionCreatedBy { get; set; }
        public string? sectionChangedAt { get; set; }
        public string? sectionChangedBy { get; set; }

        public string? SubSectionName { get; set; }
        public string? SubSectionDescription { get; set; }
        public string? SubsectionCreatedAt { get; set; }
        public string? SubsectionCreatedBy { get; set; }
        public string? SubsectionChangedAt { get; set; }
        public string? SubsectionChangedBy { get; set; }

        public string? MenuID { get; set; }
        public string? MenuName { get; set; }
        public string? SubMenuID { get; set; }
        public string? SubMenuName { get; set; }
        public string? pageurl { get; set; }
        public string? PageName { get; set; }
        public string? PageID { get; set; }
        public string? entityID { get; set; }
        public string? RecordID { get; set; }
        public string? Ischecked { get; set; }
        public string? IsActive { get; set; }
        // public string? PageURL{ get; set; }


        //public Approver Approver = new Approver(){ get; set; }

    }

    #endregion

    #region BCMDirectorMapping

    public class BCMDirectorMappingInfo : UserActivityStatus
    {

        public string? ID { get; set; }
        public string? SectionName { get; set; }
        public string? SectionDescription { get; set; }
        public string? sectionCreatedAt { get; set; }
        public string? sectionCreatedBy { get; set; }
        public string? sectionChangedAt { get; set; }
        public string? sectionChangedBy { get; set; }
        public string? subsectionID { get; set; }
        public string? SubSectionName { get; set; }
        public string? SubSectionDescription { get; set; }
        public string? SubsectionCreatedAt { get; set; }
        public string? SubsectionCreatedBy { get; set; }
        public string? SubsectionChangedAt { get; set; }
        public string? SubsectionChangedBy { get; set; }

        public string? MenuID { get; set; }
        public string? MenuName { get; set; }
        public string? SubMenuID { get; set; }
        public string? SubMenuName { get; set; }
        public string? pageurl { get; set; }
        public string? PageName { get; set; }
        public string? PageID { get; set; }
        public string? entityID { get; set; }
        public string? RecordID { get; set; }
        //public Approver Approver = new Approver(){ get; set; }
    }

    #endregion

    #region  Report

    public class ReportInfo : UserActivityStatus
    {

        public string? MenuID { get; set; }
        public string? MenuName { get; set; }
        public string? SubMenuID { get; set; }
        public string? SubMenuName { get; set; }
        public string? pageurl { get; set; }
        public string? PageName { get; set; }
        public string? PageID { get; set; }
        public string? entityID { get; set; }
        public string? RecordID { get; set; }
        public string? RoleID { get; set; }
        public string? CronExpression { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public string? CreatedDate { get; set; }
        public string? UpdatedDate { get; set; }
        public string? ResourceID { get; set; }
        public string? ScheduleReportID { get; set; }
        public string? IsActive { get; set; }
        public string? CanAutomate { get; set; }

        //public Approver Approver = new Approver(){ get; set; }

    }


    #endregion

    #region  BCMSection

    public class BCMsectionInfo : UserActivityStatus
    {

        public string? ID { get; set; }
        public string? SectionName { get; set; }
        public string? Description { get; set; }
        public string? CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? ChangedAt { get; set; }
        public string? ChangedBy { get; set; }


        // public Approver Approver = new Approver(){ get; set; }

    }


    #endregion

    /*************************/

    #region BusinessProcessPCI

    public class BusinessProcessPCI
    {
        public string? ID { get; set; }
        public string? ProcessID { get; set; }
        public string? PCIScoreID { get; set; }
        public string? IsActive { get; set; }
        public string? CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? ChangedAt { get; set; }
        public string? ChangedBy { get; set; }
        public string? Sequence { get; set; }
        public string? ScoreName { get; set; }
        public string? Weightage { get; set; }
        public string? Description { get; set; }
        public string? RecordID { get; set; }
        public string? AssignedWeightage { get; set; }
        public string? ActualWeightage { get; set; }
    }

    #endregion

    #region BCMEntityPCI

    public class BCMEntityPCIScore
    {
        public string? ID { get; set; }
        public string? EntityID { get; set; }
        public string? RecordID { get; set; }
        public string? PCIScoreID { get; set; }
        public string? IsActive { get; set; }
        public string? CreatedBy { get; set; }
        public string? CreatedAt { get; set; }
        public string? ChangedBy { get; set; }
        public string? ChangedAt { get; set; }
    }
    #endregion

    #region MediaRelations

    public class MediaRelationsInfo
    {
        // public string? ID{ get; set; }       
        public string? MediaId { get; set; }
        public string? MediaName { get; set; }
        public string? MediaType { get; set; }
        public string? MediaStatus { get; set; }
        public string? MediaTopic { get; set; }
        public string? FeedBack { get; set; }
        public string? PlannedRelease { get; set; }
        public string? ReleaseDate { get; set; }
        public string? Owner { get; set; }
        public string? MyAssigned { get; set; }
        public string? ApprovedBy { get; set; }
        public string? DisasterDate { get; set; }
        public string? Description { get; set; }
        public string? Comments { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
    }

    #endregion

    #region PlannedSchedule

    public class PlannedScheduleInfo
    {
        public string? Id { get; set; }
        public string? PlannedValue { get; set; }
        public string? PlannedInterval { get; set; }
        public string? PlannedStartDate { get; set; }
        public string? PlannedCompleteDate { get; set; }
        public string? ActualValue { get; set; }
        public string? ActualInterval { get; set; }
        public string? ActualStartDate { get; set; }
        public string? ActualCompleteDate { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
    }

    #endregion

    #region Potentioal Impact

    public class PotentialImpactDetails
    {
        public string? ID { get; set; }
        public string? ThreatSenarioID { get; set; }
        public string? Hurricanecyclone { get; set; }
        public string? Flood { get; set; }
        public string? Drought { get; set; }
        public string? Earthquake { get; set; }
        public string? Lightning { get; set; }
        public string? Fire { get; set; }
        public string? SubsidenceandLandslides { get; set; }
        public string? CEHazards { get; set; }
        public string? Epidemic { get; set; }
        public string? Pandemeic { get; set; }
        public string? ActofTerrorism { get; set; }
        public string? ActofSabotage { get; set; }
        public string? Actofwar { get; set; }
        public string? Theft { get; set; }
        public string? Arson { get; set; }
        public string? LabourDisputes { get; set; }
        public string? ElectricalPowerFailure { get; set; }
        public string? LossofWater { get; set; }
        public string? PetroleumandOil { get; set; }
        public string? Communicationsservices { get; set; }
        public string? Lossofdrainage { get; set; }
        public string? InternalPowerFailure { get; set; }
        public string? AirConditioningFailure { get; set; }
        public string? EquipmentFailure { get; set; }
        public string? Cybercrime { get; set; }
        public string? LossofRecords { get; set; }
        public string? Disclosure { get; set; }
        public string? ITSystemFailure { get; set; }
        public string? NetworkIntrusion { get; set; }
        public string? NetworkServicesFailure { get; set; }
        public string? WorkplaceViolence { get; set; }
        public string? PublicTransportation { get; set; }
        public string? NeighbourhoodHazard { get; set; }
        public string? HealthHazards { get; set; }
        public string? EmployeeMorale { get; set; }
        public string? MergersandAcquisitions { get; set; }
        public string? NegativePublicity { get; set; }
        public string? LegalProblems { get; set; }
        public string? GovernmentalActions { get; set; }
        public string? IdentityRelatedIntrusions { get; set; }
        public string? MassAbsence { get; set; }
        public string? IsActive { get; set; }
        public string? ChangedAt { get; set; }
        public string? ChangedBy { get; set; }
    }


    #endregion

    #region Resoures
    public class ResourcesInfo : UserActivityStatus
    {
        public int Id { get; set; }
        public int ResourceId { get; set; }

        public int EntityId { get; set; }

        public string? ResourceName { get; set; }

        public int UnitID { get; set; }

        public string? UnitName { get; set; }

        public string? LoginName { get; set; }

        public int StatusID { get; set; }

        public string? ResourceTitle { get; set; }

        public int ResourceTypeID { get; set; }

        public string? BusinessPhone { get; set; }

        public string? HomePhone { get; set; }

        public string? MobilePhone { get; set; }

        public string? Fax { get; set; }

        public string? OtherPhone { get; set; }

        public string? CompanyEmail { get; set; }

        public string? OtherEmail { get; set; }

        public string? Address { get; set; }

        public string? City { get; set; }

        public string? State { get; set; }

        public string? PinCode { get; set; }

        public int DepartmentID { get; set; }

        public string? DepartmentName { get; set; }

        public int BackupResourceID { get; set; }

        public string? RoleDetails { get; set; }

        public string? UserRole { get; set; }
        public string? UserRoleName { get; set; }

        public string? Comments { get; set; }

        public int IsActive { get; set; }

        public int LoginIsActive { get; set; }

        public DateTime CreateDate { get; set; }

        public DateTime UpdateDate { get; set; }

        public int ChangedBy { get; set; }

        public string? UnitCode { get; set; }

        public int OwnerID { get; set; }

        public string? PlanOwner { get; set; }

        public string? TaskOwner { get; set; }

        public string? StepOwner { get; set; }

        public int PlanID { get; set; }

        public string? PlanName { get; set; }

        public int TaskID { get; set; }

        public string? TaskName { get; set; }

        public int StepID { get; set; }

        public string? StepName { get; set; }

        public int PricipalResourceID { get; set; }

        public string? Responsible { get; set; }

        public string? ActiveDate { get; set; }

        public int OrgID { get; set; }

        public string? FName { get; set; }

        public string? MName { get; set; }

        public string? LName { get; set; }

        public int VendorID { get; set; }

        public string? VendorName { get; set; }

        public string? mime { get; set; }

        public string? guid { get; set; }

        public string? OrganizationName { get; set; }

        public string? ADLoginName { get; set; }

        public string? DomainName { get; set; }

        public int TypeID { get; set; }

        public string? TypeName { get; set; }

        public DateTime EffectiveStartDate { get; set; }

        public DateTime EffectiveEndDate { get; set; }

        public string? DefaultRole { get; set; }

        public string? RoleList { get; set; }

        public int EntityTypeID { get; set; }

        public string? AltUHeadResourceName { get; set; }

        public string? AltUHeadMobilePhone { get; set; }

        public string? AltUHeadCompanyEmail { get; set; }

        public string? AltUHeadUserRole { get; set; }

        public int AltUHeadIsActive_Mobile { get; set; }

        public string? AltUHeadIsActive_Email { get; set; }

        public DateTime AltUHeadActiveDate { get; set; }

        public string? AltUHeadActivationModeMobile { get; set; }

        public string? AltUHeadMobileVerified { get; set; }

        public string? AltUHeadMobileVerifiedTooltip { get; set; }

        public string? AltUHeadActivationModeEmail { get; set; }

        public string? AltUHeadEmailVerified { get; set; }

        public string? AltUHeadEmailVerifiedTooltip { get; set; }


        public string? BCPCoordinatorName { get; set; }

        public string? BCPCoordinatorMail { get; set; }

        public string? BCPCoordinatorMobile { get; set; }

        public string? AltUBCPCoordinatorName { get; set; }

        public string? AltUBCPCoordinatorMail { get; set; }

        public string? AltUBCPCoordinatorMobile { get; set; }

        public string? Field1 { get; set; }

        public string? Field2 { get; set; }

        public string? Field3 { get; set; }

        public string? Field4 { get; set; }

        public int OrgGroupID { get; set; }

        public string? OrgGroupName { get; set; }

        public int SubFunctionID { get; set; }

        public string? SubFunctionName { get; set; }

        public string? Count { get; set; }

        public string? LastLoginTime { get; set; }


        public string? TranID { get; set; }

        public int IsAlredyExist { get; set; }

        public int TempResID { get; set; }

        public string? ActiveStatusImg { get; set; }

        public int DesignationId { get; set; }

        public int UserID { get; set; }

        public string? Designation { get; set; }

        public string? Culture { get; set; }

        public int CompanyID { get; set; }
        public string? CompanyName { get; set; }

        public string? Description { get; set; }

        public int TitleId { get; set; }

        public string? UserRoles { get; set; }

        public bool IsSelected { get; set; }

        //public List<BCMUserRoles?> Filters { get; set; }

        public string? Status { get; set; }

        public BigInteger FileSize { get; set; }

        public string? MimeType { get; set; }

        public string? version { get; set; }
        public List<int>? role { get; set; }
        public int IsButtonVisible { get; set; }
    }

    public class BCMUserRoles
    {
        public int UserRole { get; set; }
        public string? UserRoleName { get; set; }

        public bool Selected { get; set; }
    }


    #endregion

    //#region UserRole
    //
    //public class UserRoleInfo : UserActivityStatus
    //{
    //    public string? UserID{ get; set; }
    //    public string?  RoleID{ get; set; }
    //}


    //
    //public class UserRoleInfoColl : CollectionBase
    //{
    //    public void New()
    //    {

    //    }

    //    public void Add(UserRoleInfo resource)
    //    {
    //        base.InnerList.Add(resource){ get; set; }
    //    }

    //    public UserRoleInfo this[int index]
    //    {
    //        get
    //        {
    //            return (UserRoleInfo)base.InnerList[index]{ get; set; }
    //        }
    //        set
    //        {
    //            base.InnerList[index] = value{ get; set; }
    //        }
    //    }
    //}

    //#endregion

    #region ResourceVerification

    public class ResourcesReminder
    {
        public int ResourceID { get; set; }
        public int CommunicationMode { get; set; }
        public int InOut_bound { get; set; }
        //  public string? CommunicationDate{ get; set; }
        public int SenderID { get; set; }
        public string? ResourceName { get; set; }
        public string? LoginName { get; set; }
        public string? MobilePhone { get; set; }
        public string? CompanyEmail { get; set; }
        public string? UserRole { get; set; }
        public string? IsActive_Mobile { get; set; }
        public string? IsActive_Email { get; set; }
        public DateTime lastSMSSentOn { get; set; }
        public DateTime lastEmailSentOn { get; set; }
        //public string? lastResponseSMSReceivedOn{ get; set; }
        //public string? lastEmailReceivedOn{ get; set; }
        public DateTime ActiveDate { get; set; }
        public DateTime lastSMSReceivedOn { get; set; }
        public DateTime lastEmailReceivedOn { get; set; }

        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public int OrgGroupID { get; set; }


    }


    #endregion

    #region Resoure Team

    public class ResourceTeamInfo
    {
        public int ResourceTeamID { get; set; }
        public string? ResourceTeamName { get; set; }
        public string? ResourceTeamLead { get; set; }
        public string? TeamRole { get; set; }
        public string? Comments { get; set; }
        public int? IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public string? TeamName { get; set; }
    }


    #endregion

    #region RecoverySupportTeam



    public class RecoverySupportTeamRST
    {
        public int ID { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public int OwnerID { get; set; }
        public int AltOwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerPhone { get; set; }
        public string? OwnerEmail { get; set; }
        public string? AltOwnerName { get; set; }
        public string? AltOwnerPhone { get; set; }
        public string? AltOwnerEmail { get; set; }
        public int? IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public int ChangedBy { get; set; }


    }


    #endregion

    #region ThirdpartyMaster

    public class ThirdpartyMasterInfo
    {
        public int CompanyID { get; set; }
        public string? CompanyName { get; set; }
        public string? CompanyDetails { get; set; }
        public int PrimaryContactID { get; set; }
        public int PrimaryContactEmailID { get; set; }
        public int AltContactID { get; set; }
        public int AltContactEmailID { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }

    }

    #endregion

    #region Assets

    public class AssetsInfo
    {
        public int AssetID { get; set; }
        public int EntityID { get; set; }
        public string? AssetName { get; set; }
        public int AssetInvId { get; set; }
        public int StatusID { get; set; }
        public int AssetTypeID { get; set; }
        public string? Location { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? ManufacturerName { get; set; }
        public string? SerialNumber { get; set; }
        public string? ModalNumber { get; set; }
        public string? ReplacementValue { get; set; }
        public string? Description { get; set; }
        public string? Comments { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region AssetInventory

    public class AssetInventory
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? IPAddress { get; set; }
        public string? MacAddress { get; set; }
        public string? MachineName { get; set; }
        public string? DomainName { get; set; }
        public string? AssetCategoty { get; set; }
        public string? OsName { get; set; }
        public string? DiskSpace { get; set; }
        public string? ProcessorSpeed { get; set; }
        public string? Manufacturer { get; set; }
        public string? ProcessorName { get; set; }
        public string? SerialNumber { get; set; }
        public string? AssetTag { get; set; }
        public string? BarCode { get; set; }
        public string? Vendor { get; set; }
        public string? AssetCost { get; set; }
        public string? AcquisitionDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public int PartNo { get; set; }
        public string? CurrentlyIn { get; set; }
        public string? Location { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region AssetsGroup

    public class AssetGroupInfo
    {
        public int AssetGroupId { get; set; }
        public string? AssetGroupName { get; set; }
        public int AssetId { get; set; }
        public string? GroupPurpose { get; set; }
        public string? Comments { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }


    #endregion

    #region DeliveryType

    public class DeliveryTypeInfo
    {
        public int DeliveryTypeId { get; set; }
        public string? BusinessTelephone { get; set; }
        public string? HomeTelephone { get; set; }
        public string? MobileTelephone { get; set; }
        public string? OtherTelephone { get; set; }
        public string? FaxNumber { get; set; }
        public string? CompanyEmail { get; set; }
        public string? OtherEmail { get; set; }
    }


    #endregion

    #region Notify

    public class Notify
    {
        public int NotifyId { get; set; }
        public int EntityID { get; set; }
        public int RecordID { get; set; }
        public string? Title { get; set; }
        public string? BusinessPhone { get; set; }
        public string? HomePhone { get; set; }
        public string? MobilePhone { get; set; }
        public string? Fax { get; set; }
        public string? OtherPhone { get; set; }
        public string? CompanyEmail { get; set; }
        public string? OtherEmail { get; set; }
        public string? SenderName { get; set; }
        public string? Subject { get; set; }
        public string? Message { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime DeliveryDate { get; set; }
    }

    #endregion

    #region TypeMaster

    public class TypeMasterInfo
    {
        public int TypeID { get; set; }
        public int EntityID { get; set; }
        public string? TypeName { get; set; }
        public string? TypeDetails { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region StatusMaster

    public class StatusMasterInfo
    {
        public int StatusID { get; set; }
        public int EntityID { get; set; }
        public string? StatusName { get; set; }
        public string? StatusDetails { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region EntityMaster

    public class EntityMasterInfo
    {
        public int EntityID { get; set; }
        public string? EntityName { get; set; }
        public string? EntityDetails { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }

    }

    #endregion

    #region EntityMasterDetails

    public class EntityMasterDetailsInfo
    {
        public int EntityDetailsId { get; set; }
        public string? Keyword { get; set; }
        public string? Displaytext { get; set; }
        public string? Description { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public int Changedby { get; set; }
        public string? HelpText { get; set; }
        public int EntityID { get; set; }
    }




    #endregion

    #region Department


    public class DepartmentInfo
    {
        [Required]
        public int DepartmentID { get; set; }
        // public string? DeptID_Encrypted{ get; set; }

        public int CompanyID { get; set; }

        public string? DepartmentName { get; set; }

        public string? DepartmentDetails { get; set; }

        public int IsActive { get; set; }

        public DateTime CreateDate { get; set; }

        public DateTime UpdateDate { get; set; }

        [Required]
        public int DeptHeadID { get; set; }

        [Required]
        public int DeptAltHeadID { get; set; }

        [Required]
        public int UnitID { get; set; }

        public string? UnitName { get; set; }

        public int ChangedBy { get; set; }

        public int OrgID { get; set; }

        public string? OrganiZationName { get; set; }

        public string? DepartmentHead { get; set; }

        public string? AltDepartmentHead { get; set; }


        public string? HeadEmail { get; set; }

        public string? HeadMobilePhone { get; set; }

        public string? AltHeadEmail { get; set; }

        public string? AltHeadMobilePhone { get; set; }

        public int RoleID { get; set; }


        public int SubfunctionID { get; set; }

        public string? SubfunctionName { get; set; }

        public int OrgGroupID { get; set; }

        public string? ActivationModeMobile { get; set; }
        public string? MobileVerified { get; set; }
        public string? MobileVerifiedTooltip { get; set; }
        public string? ActivationModeEmail { get; set; }
        public string? EmailVerified { get; set; }
        public string? EmailVerifiedTooltip { get; set; }

        public string? ActivationModeMobileAltr { get; set; }
        public string? MobileVerifiedAltr { get; set; }
        public string? MobileVerifiedTooltipAltr { get; set; }
        public string? ActivationModeEmailAltr { get; set; }
        public string? EmailVerifiedAltr { get; set; }
        public string? EmailVerifiedTooltipAltr { get; set; }


        public int TotalProcessCount { get; set; }  // Added by Shubham b.
        public int NonCriticalProcessCount { get; set; }    // Added by Shubham b.
        public int CriticallProcessCount { get; set; }  // Added by Shubham b.
        public int ApprovedProcessCount { get; set; }   // Added by Shubham b.
        public int DisApprovedlProcessCount { get; set; }   // Added by Shubham b.
        public int WaitingForApproveProcessCount { get; set; }  // Added by Shubham b.
    }


    #endregion

    #region DepartmentBIA
    public class DepartmentBIAEntities : UserActivityStatus
    {
        public int ID { get; set; }
        public string? OrgName { get; set; }
        public string? UnitName { get; set; }
        public int DepartmentBiaID { get; set; }
        public string? CustodianID { get; set; }
        public string? DepartmentCode { get; set; }
        public string? SubDepartmentCode { get; set; }
        public string? Author { get; set; }
        public string? Description { get; set; }
        public string? PrimarySiteID { get; set; }
        public string? AlternateSiteID { get; set; }
        public string? StaffLocation { get; set; }
        public string? DepartmentID { get; set; }
        public int RecordID { get; set; }
        public int EntityTypeID { get; set; }
        public string? SubDepartmentID { get; set; }
        public int BCMCoOrdinatorID { get; set; }
        public int ApproverID { get; set; }
        public string? BIACode { get; set; }
        public string? ReviewDate { get; set; }
        public string? LastReviewDate { get; set; }

        public int BCMAltCoOrdinatorID { get; set; }
        public string? ImpactToOrganisation { get; set; }
        public string? MaximumTimePeriod { get; set; }
        public string? LocationID { get; set; }
        public string? LocationName { get; set; }
        public string? HowsItHandle { get; set; }
        public string? RevenueLoss { get; set; }
        public string? Remarks { get; set; }
        public string? ReviewerID { get; set; }
        public int FRPID { get; set; }
        public int Status { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
        public string? CreatedAt { get; set; }
        public int DetailID { get; set; }
        public string? Issue { get; set; }
        public string? ReviewerName { get; set; }
        public string? AuthorName { get; set; }

        public string? AuthorEmail { get; set; }
        public string? DepartmentName { get; set; }
        public string? BCMCoOrdinatorName { get; set; }
        public string? BCMAltCoOrdinatorName { get; set; }
        public string? CustodianNames { get; set; }
        public string? PrimarySiteName { get; set; }
        public string? AltSiteName { get; set; }
        public string? SubDepartmentName { get; set; }
        public string? ReasonForVersionChange { get; set; }
        public int ReviewStatus { get; set; }
        public int IsEffective { get; set; }
        public int RetensionPeriodID { get; set; }
        public string? DocumentID { get; set; }
        public string? GeographicalApp { get; set; }
        public string? CompanyID { get; set; }
        public string? DepartmentDetails { get; set; }
        public string? CompanyName { get; set; }
        public string? IsActive { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdateDate { get; set; }
        public string? UnitID { get; set; }

        public string? OrgID { get; set; }
        public string? OrganiZationName { get; set; }
        public string? DepartmentHeadName { get; set; }
        public string? DeptHeadID { get; set; }
        public string? DeptAltHeadID { get; set; }
        public string? DepartmentHead { get; set; }
        public string? AltDepartmentHeadName { get; set; }
        public string? AltDepartmentHead { get; set; }
        public string? HeadEmail { get; set; }
        public string? HeadMobilePhone { get; set; }
        public string? AltHeadEmail { get; set; }
        public string? AltHeadMobilePhone { get; set; }
        public string? AuthHeadMobilePhone { get; set; }
        public string? ApproverName { get; set; }
        public string? ApproverPhone { get; set; }
        public string? ApproverEmail { get; set; }
        public string? ApproverMobilePhone { get; set; }
        public string? Version { get; set; }
        public string? NameOfEmployeeID { get; set; }
        public string? NameOfEmployee { get; set; }
        public string? PeopleToInformID { get; set; }
        public string? PeopleToInformName { get; set; }
        public string? DepartmentDescription { get; set; }
        public string? Designation { get; set; }
        public string? ApprovedOn { get; set; }
        public int BCMtamStatus { get; set; }
        public int DirectorStatus { get; set; }
        public int SeniorDirectorStatus { get; set; }
        public int COOStatus { get; set; }
        public int PresidentStatus { get; set; }
        public int UnitHeadID { get; set; }
        public int AltUnitHeadID { get; set; }
        public int OrgHeadID { get; set; }
        public int SubFunctionOwnerID { get; set; }
        public int SubFunctionAltOwnerID { get; set; }
        public int DirectorID { get; set; }
        public int SeniorDirectorID { get; set; }
        public string? DirectorName { get; set; }
        public string? SeniorDirectorName { get; set; }
        public string? PresidentID { get; set; }
        public string? LastReminderDate { get; set; }
    }
    #endregion

    #region CompanyMaster

    public class CompanyMasterInfo
    {
        public int CompanyID { get; set; }
        public string? CompanyName { get; set; }
        public string? CompanyDetails { get; set; }
        public string? CompanyAddress { get; set; }
        public DateTime ContractStartDate { get; set; }
        public DateTime ContractEndDate { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public string? CompanyCode { get; set; }
        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public string? OrganizationName { get; set; }
        public string? DepartmentName { get; set; }
        public string? UnitName { get; set; }
        public int EntityTypeID { get; set; }
        public string? Field1 { get; set; }
        public string? Field2 { get; set; }
        public string? Field3 { get; set; }
        public string? Field4 { get; set; }
        public int OrgGroupID { get; set; }
        public int DevisionID { get; set; }
        public int IsButtonVisible { get; set; }

    }


    #endregion

    #region ExtensionMaster

    public class ExtensionMasterInfo     // Created By Prashant
    {
        public int ExtensionID { get; set; }
        public string? Extension { get; set; }

    }

    #endregion

    #region DetailsHistory

    public class DetailsHistory
    {
        public int ID { get; set; }
        public int DetailID { get; set; }
        public int OrgGroupID { get; set; }
        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DeptID { get; set; }
        public int SubDeptID { get; set; }
        public int EntityID { get; set; }
        public int RecordID { get; set; }
        public string? version { get; set; }
        public string? RecordCode { get; set; }
        public string? RecordName { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
        public int StatusID { get; set; }
        public string? StatusDesc { get; set; }
    }


    #endregion

    #region DetailsDistribution

    public class DetailsDistribution
    {
        public int ID { get; set; }
        public int DetailID { get; set; }

        public int OrgGroupID { get; set; }
        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DeptID { get; set; }
        public int SubDeptID { get; set; }
        public int EntityID { get; set; }

        //public string? SubEntityID{ get; set; }
        //public string? PrivilegeID{ get; set; }
        public int IsActive { get; set; }
        public int RecordID { get; set; }
        public string? version { get; set; }
        public string? RecordCode { get; set; }
        public string? RecordName { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public Object? DeallocationFile { get; set; }
        public int OldIsActive { get; set; }

        public int ProcessID { get; set; }
        public string? ProcessName { get; set; }
        public string? ProcessCode { get; set; }
        public int Active { get; set; }
        public string? EntityName { get; set; }

        public string? DetailType { get; set; }

    }


    #endregion

    #region MenuRightsRegion

    public class MenuRights
    {
        public int ID { get; set; }
        public int MenuID { get; set; }
        public string? MenuName { get; set; }
        public string? MenuDetails { get; set; }
        public int PageID { get; set; }
        public string? PageName { get; set; }

        public string? PageDetails { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int UserID { get; set; }
        public string? IconClass { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
        public int PrivilegeID { get; set; }
        public string? PrivilegeName { get; set; }
        public int RoleID { get; set; }
        public int SubMenuID { get; set; }
        public string? SubMenuName { get; set; }
        public string? SubMenuDetails { get; set; }
        public string? MenuIcon { get; set; }
        public string? PageURL { get; set; }
        public string? Sequence { get; set; }
        public string? MenuSequence { get; set; }
        public string? SubMenuSequence { get; set; }
        public string? PageSequence { get; set; }

        //added by saurabh
        public string? ControllerName { get; set; }
        public string? AreaName { get; set; }
        public string? ActionName { get; set; }
        public bool? IsSelected { get; set; }
    }



    public class MenuRoleRights
    {
        public int ID { get; set; }
        public int RoleID { get; set; }
        public string? RoleName { get; set; }
        public int MenuID { get; set; }
        public int PageID { get; set; }
        public int PrivilageID { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
        public int SubmenuID { get; set; }
        public string? SubmenuName { get; set; }
        public string? SubmenuDetails { get; set; }
        public int UserID { get; set; }
    }



    public class OrgRoleRights
    {
        public int ID { get; set; }
        public int RoleID { get; set; }

        public int OrgGroupID { get; set; }
        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DeptID { get; set; }
        public int SubDeptID { get; set; }
        public int EntityID { get; set; }

        public int EntityTypeID { get; set; }
        public int RecordID { get; set; }

        public int ProcessID { get; set; }
        public string? ProcessCode { get; set; }
        public string? version { get; set; }
        public int Level { get; set; }
        public int RoleRightsSubEntityID { get; set; }
        public int MasterEntryTypeID { get; set; }
        public int SubEntityID { get; set; }
        public int ParentPrivilegeID { get; set; }
        public int PrivilegeID { get; set; }
        public int UserID { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }

        public string? OrganizationGroupName { get; set; }
        public string? OrgName { get; set; }
        public string? UnitName { get; set; }
        public string? DeptName { get; set; }
        public string? SubDeptName { get; set; }
        public string? BcmEntityName { get; set; }
        public string? FacilityName { get; set; }
        public string? Version { get; set; }
        public int IsActive { get; set; }
        public string? ProcessName { get; set; }
        public string? OtherBcmEntityName { get; set; }
        public string? ResourceName { get; set; }
        public string? locationName { get; set; }

    }



    #endregion

    #region Contact

    public class Contact
    {
        public int ContactId { get; set; }
        public int EntityID { get; set; }
        public int ContactGroupID { get; set; }
        public string? ContactName { get; set; }
        public int StatusID { get; set; }
        public string? ContactTitle { get; set; }

        public int ContactTypeID { get; set; }
        public string? BusinessPhone { get; set; }
        public string? HomePhone { get; set; }
        public string? MobilePhone { get; set; }

        public string? Fax { get; set; }
        public int OtherPhone { get; set; }
        public string? CompanyEmail { get; set; }
        public string? OtherEmail { get; set; }

        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public int PinCode { get; set; }

        public int DepartmentID { get; set; }
        public int AlternateContactID { get; set; }
        public string? Comments { get; set; }

        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region ContactGroup

    public class ContactGroup
    {
        public int ContactGroupID { get; set; }
        public string? ContactGroupName { get; set; }
        public int PrincipalContact { get; set; }
        public string? GroupPurpose { get; set; }
        public string? Comments { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region Schedule

    public class ScheduledInfo
    {
        public int EntityID { get; set; }
        public int StatusID { get; set; }
        public int Duration { get; set; }
        public int ResourceID { get; set; }
        public DateTime PlannedStart { get; set; }
        public DateTime PlannedCompleted { get; set; }
        public DateTime ActualStart { get; set; }
        public DateTime ActualCompleted { get; set; }

    }

    #endregion

    #region SearchPrefrenceDetails

    public class SearchPrefrenceDetails
    {
        public int ID { get; set; }
        public int ColumnID { get; set; }
        public int UserID { get; set; }
        public int UnitID { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
    }

    #endregion

    #region Manage Users

    public class ManageUsersDetails
    {

        public string? OrgGroupCode { get; set; }
        public int UserID { get; set; }

        public string? UserName { get; set; }


        public string? UserRole { get; set; }

        public bool IsAdLogin { get; set; }
        public int UserRoleID { get; set; }


        public int OrgID { get; set; }


        public string? OrgName { get; set; }


        public int UnitID { get; set; }


        public string? UnitName { get; set; }


        public string? UnitCode { get; set; }


        public int DepartmentID { get; set; }


        public string? DepartmentName { get; set; }


        public string? LoginName { get; set; }

        public string? Password { get; set; }


        public DateTime LastLoginTime { get; set; }


        public int AllowView { get; set; }


        public int AllowEdit { get; set; }

        public int AllowDelete { get; set; }


        public DateTime CreateDate { get; set; }

        public DateTime UpdateDate { get; set; }


        public string? OrgGroupName { get; set; }


        public int OrgGroupID { get; set; }


        public string? Mime { get; set; }


        public string? Guid { get; set; }


        public string? TwoFactIsActive { get; set; }


        public string? TwoFactBySMS { get; set; }


        public string? TwoFactbyMail { get; set; }


        public string? TwoFactCodeExpiration { get; set; }



        public string? MobilePhone { get; set; }


        public string? CompanyEmail { get; set; }



        public string? TwoFactorCode { get; set; }


        public string? ExpirationTime { get; set; }


        public string? InValidAttempts { get; set; }



        public string? SessionTimeOut { get; set; }


        public string? EnableCAPACHA { get; set; }


        public DateTime PasswordChgDate { get; set; }


        public string? Culture { get; set; }


        public string? ADLoginName { get; set; }


        public string? DomainName { get; set; }



        public int SubDepartmentID { get; set; }


        public string? Designation { get; set; }


        public string? SubDepartmentName { get; set; }

        public string? OrganizationCode { get; set; }


        public string? OrganizationGroupCode { get; set; }


        public int UserStatus { get; set; }


        public int IsActive { get; set; }


        public int ManageUserID { get; set; }


        public string? IsLicensed { get; set; }

        public int ChangedBy { get; set; }
        public int LoginIsActive { get; set; }

        public DateTime DateOfActivation { get; set; }
        public DateTime DateOfDeactivation { get; set; }


    }

    [DataContract]
    [Serializable()]
    public class ManageUsersDetailsLogin
    {

        public int UserID { get; set; }

        public string? LoginName { get; set; }

        public string? LoginCode { get; set; }

        public string? UserType { get; set; }

        public string? UserName { get; set; }

        public string? UserRole { get; set; }

        public int UserRoleID { get; set; }

        public int OrgID { get; set; }


        public string? OrgName { get; set; }


        public int UnitID { get; set; }


        public string? UnitName { get; set; }


        public string? UnitCode { get; set; }


        public int DepartmentID { get; set; }


        public string? DepartmentName { get; set; }

        public string? Password { get; set; }


        public DateTime LastLoginTime { get; set; }


        public int AllowView { get; set; }


        public int AllowEdit { get; set; }

        public int AllowDelete { get; set; }


        public DateTime CreateDate { get; set; }

        public DateTime UpdateDate { get; set; }


        public string? OrgGroupName { get; set; }


        public int OrgGroupID { get; set; }


        public string? Mime { get; set; }


        public string? Guid { get; set; }


        public string? TwoFactIsActive { get; set; }


        public string? TwoFactBySMS { get; set; }


        public string? TwoFactbyMail { get; set; }


        public string? TwoFactCodeExpiration { get; set; }



        public string? MobilePhone { get; set; }


        public string? CompanyEmail { get; set; }



        public string? TwoFactorCode { get; set; }


        public string? ExpirationTime { get; set; }


        public string? InValidAttempts { get; set; }



        public string? SessionTimeOut { get; set; }


        public string? EnableCAPACHA { get; set; }


        public DateTime PasswordChgDate { get; set; }


        public string? Culture { get; set; }


        public string? ADLoginName { get; set; }


        public string? DomainName { get; set; }



        public int SubDepartmentID { get; set; }


        public string? Designation { get; set; }


        public string? SubDepartmentName { get; set; }


        public string? OrganizationCode { get; set; }


        public string? OrganizationGroupCode { get; set; }


        public int UserStatus { get; set; }


        public int IsActive { get; set; }


        public int ManageUserID { get; set; }


        public string? IsLicensed { get; set; }

        public int ChangedBy { get; set; }

    }

    #endregion

    #region Org Structure Type Master

    public class OrgStructureTypeMasterInfo
    {
        public int UserID { get; set; }
        public string? OrgStructureName { get; set; }
        public string? OrgDescription { get; set; }
        public int CreatedBy { get; set; }
        public int OrgStructureID { get; set; }



    }

    #endregion

    #region BCM Sub Section Master

    public class BCMSubSectionMasterInfo
    {
        public int UserID { get; set; }

        public int BCMSectionID { get; set; }

        public int SectionID { get; set; }
        public string? SectionName { get; set; }
        public int SubSectionID { get; set; }
        public string? SubSectionName { get; set; }
        public string? SubSectionDesc { get; set; }
        public string? subsectionURL { get; set; }
        public int CreatedBy { get; set; }
    }

    #endregion

    #region RiskAssessment

    public class RiskAssessmentFacility
    {
        public string? NonCompliant { get; set; }
        public string? Compliant { get; set; }

        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region RTOMTR

    public class RtoMtrDetails
    {
        public int Id { get; set; }
        public string? Name { get; set; }
    }


    public class RTOMTRConfigurations
    {
        public int ID { get; set; }
        public string? ConfiguredRTO { get; set; }
        public string? ConfiguredMTR { get; set; }
        public int RTORating { get; set; }
        public int MTRRating { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int OrgID { get; set; }

        public string? OrganizationName { get; set; }
    }


    #endregion

    #region RecoveryPriority

    public class RecoveryPriorityInfo
    {
        public int RecoveryPriorityID { get; set; }
        public int ProcessID { get; set; }
        public int RTOID { get; set; }
        public int MTRID { get; set; }
        public int Upto2hrs { get; set; }
        public int Upto4hrs { get; set; }
        public int Upto8hrs { get; set; }
        public int Upto12hrs { get; set; }
        public int Upto24hrs { get; set; }
        public int Upto48hrs { get; set; }
        public int Upto72hrs { get; set; }
        public int Upto1week { get; set; }
        public int Upto2weeks { get; set; }
        public int Upto30Days { get; set; }
        public int Morethan1Month { get; set; }

    }

    #endregion

    #region VersionHistory

    public class VersionHistoryInfo
    {
        public int Id { get; set; }
        public string? TemplateTitle { get; set; }
        public string? TemplateCode { get; set; }
        public DateTime TempDateofRelease { get; set; }
        public string? TempVersionNo { get; set; }
        public string? TemplateOwner { get; set; }
        public string? TemplateAuthor { get; set; }
        public string? DocumentTitle { get; set; }
        public DateTime DocDateofRelease { get; set; }
        public string? DocumentOwner { get; set; }
        public string? DocumentAuthor { get; set; }
        public int DocVersionNo { get; set; }
        public int VersionNo { get; set; }
        public DateTime RevisionDate { get; set; }
        public string? NatureofChange { get; set; }
        public DateTime DateApproved { get; set; }
    }

    #endregion

    #region VaultNotification

    public class VaultNotifications
    {

        public int ID { get; set; }
        public int UserID { get; set; }
        public int EntityID { get; set; }
        public int RecordID { get; set; }
        public int Medium { get; set; }
        public int Status { get; set; }
        public string? Purpose { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int UnitID { get; set; }

    }

    #endregion

    #region NotificationMaster  //added by Shubham B.

    public class NotificationMasters
    {
        public int ID;
        public int NotificationMode;
        public string? Body;
        public string? Subject;
        public int NotifiedBy;
        public int NotificationType;
        public int FYAI;
        public string? GUIDAttachment;
        public DateTime NotificationTime;
        public int IsSuccess;
        public int successmsg;
        public string? NotificationTo;
        public string? NotificationCC;
        public string? NotificationBCC;
        public string? Attachment;
        public int OrgID;
        public string? NotifiedByName;
        public string? AttachmentID;
        public string? AttachmentName;
        public string? SMNotifiedToID;
        public string? SMNotifiedCcID;
        public string? SMNotifiedBccID;
        public string? SMSMobileNo;
    }

    #endregion

    #region VaultSettings
    public class VaultSettings
    {


        public int ID { get; set; }

        public string? LogoPath { get; set; }
        public string? SMTPServer { get; set; }

        public string? SMTPUserName { get; set; }

        public string? SMTPPassword { get; set; }

        public string? OutGoingMailPort { get; set; }

        public string? MailFrom { get; set; }

        public string? DefaultMailID { get; set; }

        public string? SMSUserName { get; set; }

        public string? SMSUserPassword { get; set; }

        public string? SenderID { get; set; }

        public string? DefaultSMSNo { get; set; }

        public string? SMSUrl { get; set; }

        public string? CallBackURL { get; set; }

        public string? InboxID { get; set; }

        public string? ReplyNumber { get; set; }

        public string? ApplicationDirectory { get; set; }

        public string? DevMode { get; set; }

        public string? AttachmentFolder { get; set; }

        public string? PDFFilesPath { get; set; }

        public int OrgGrouID { get; set; }

        public int OrgID { get; set; }

        public DateTime ChangedAt { get; set; }

        public int ChangedBy { get; set; }

        public string? VerificationInterval { get; set; }

        public string? VerificationDaysPrior { get; set; }

        public string? VerificationStopDays { get; set; }

        public string? PlanRevisionDaysBefore { get; set; }

        public string? PlanRevisionDaysAfter { get; set; }

        public string? AuditPlanRevisionDaysBefore { get; set; }


        public int TwoFactorIsActive { get; set; }

        public string? TwoFactorBySMS { get; set; }

        public string? TwoFactorByMail { get; set; }

        public string? TwoFactorExpirationTime { get; set; }


        public string? SessionTimeOut { get; set; }

        public string? EnableCAPACHA { get; set; }

        public string? FileSize { get; set; }

        public string? AllowedExtension { get; set; }

        public string? Culture { get; set; }

        public string? DateFormat { get; set; }

        public string? DateTimeFormat { get; set; }

        public string? PasswordExpiration { get; set; }

        public string? DormantAccountDeactivationDays { get; set; }

        public string? CustomerID { get; set; }


        public string? EnableSSL { get; set; }

        public string? AllowAnyNumber { get; set; }


        public string? WrongLoginAttempts { get; set; }


        public string? PasswordHistory { get; set; }


    }

    #endregion

    #region CriticalApplications


    public class CriticalApplicationsInfo
    {
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public int ProcessID { get; set; }
        public string? IsDRinPlace { get; set; }
        public string? IsSubmittedToSCG { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int IsCritical { get; set; }
        public int IsDrImplemented { get; set; }
    }

    #endregion

    #region RiskAssessmentFacilityDetails

    public class RiskassessFacilitiesinDetailsInfo
    {
        public int ID { get; set; }
        public int UnitID { get; set; }
        public int FacilityID { get; set; }
        public string? FacilityName { get; set; }
        public string? PolicySegment { get; set; }
        public string? Heading { get; set; }
        public string? KeyStatement { get; set; }
        public string? RelevantCriteria { get; set; }
        public string? RiskCategory { get; set; }
        public string? Compliance { get; set; }
        public int Partial { get; set; }
        public string? PreFillRemarks { get; set; }
        public string? Remarks { get; set; }
        public string? Responsibility { get; set; }
        public string? Accountability { get; set; }
        public string? ActionStep { get; set; }
        public string? Budget { get; set; }
        public string? DateSODexpiring { get; set; }
        public string? ClosureDate { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region RiskassessThirdParty

    public class RiskassessThirdPartyInfo
    {
        public int ID { get; set; }
        public int UnitID { get; set; }
        public int ThirdPartyID { get; set; }
        public string? ThirdPartyName { get; set; }
        public string? PolicySegment { get; set; }
        public string? Heading { get; set; }
        public string? KeyStatement { get; set; }
        public string? RelevantCriteria { get; set; }
        public string? RiskCategory { get; set; }
        public string? Compliance { get; set; }
        public int Partial { get; set; }
        public string? PreFillRemarks { get; set; }
        public string? Remarks { get; set; }
        public string? Responsibility { get; set; }
        public string? Accountability { get; set; }
        public string? ActionStep { get; set; }
        public string? Budget { get; set; }
        public string? DateSODexpiring { get; set; }
        public DateTime ClosureDate { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }
    }

    #endregion

    #region RiskAssessmentFacility

    public class RiskAssessmentFacilityDetails
    {
        public int ID { get; set; }
        public int FacilityID { get; set; }
        public int ProcessID { get; set; }
        public string? Compliance { get; set; }
        public string? BMS { get; set; }
        public string? ElectMechSys { get; set; }
        public string? EmergencyMgmt { get; set; }
        public string? FireSafety { get; set; }
        public string? NBC { get; set; }
        public string? PhysicalSecurity { get; set; }
        public string? SiteAdminMgmt { get; set; }
        public string? SiteSelectionDesign { get; set; }
        public string? Spec { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public string? ProcessName { get; set; }
        public string? CriticalProcesses { get; set; }
        public int UnitId { get; set; }
        public string? FacilitySecondryName { get; set; }


        public string? FacilityName { get; set; }
        public string? FacilityAddress { get; set; }
        public string? MinimumResource { get; set; }
        public string? RTO { get; set; }

    }


    #endregion

    #region ORTDetails

    public class ORTDetailInfo
    {
        public int ProcessID { get; set; }
        public int ResourceID { get; set; }
        public string? ResourceName { get; set; }
        public string? MobilePhone { get; set; }
        public string? AltResourceName { get; set; }
        public string? AltPhoneNo { get; set; }
        public int ApplicationId { get; set; }
        public string? ApplicationName { get; set; }
        public int FacilityId { get; set; }
        public string? FacilityName { get; set; }
        public string? AltFacilityName { get; set; }
        public int PartnerId { get; set; }
        public string? PartnerName { get; set; }
        public string? PartnerPhoneNumber { get; set; }
        public string? EmailAddress { get; set; }
    }

    #endregion

    #region ResourceTraining

    public class TrainingAndAttachemnt
    {
        ResourceTrainingInfo ResourceTrainingInfo { get; set; }
        Attachment Attachment { get; set; }
    }

    public class ResourceTrainingInfo
    {
        public int ResourceTrainingID { get; set; }
        public int? ResourceID { get; set; }
        public string? Role { get; set; }
        public string? PrimarySkills { get; set; }
        public string? Training { get; set; }
        public int ChangedBy { get; set; }
        public string? ResourceName { get; set; }
    }
    #endregion

    #region EntityDependancy

    public class EntityDependancy
    {
        public int DependancyID { get; set; }
        public string? LineofBusiness { get; set; }
        public string? FunctionName { get; set; }
        public string? FunctionalPlanner { get; set; }
        public string? ProcessChampion { get; set; }
        public string? BusinessFunction { get; set; }
        public string? BusinessProcess { get; set; }
        public string? Dependancy { get; set; }
        public string? RegulatoryAuthority { get; set; }
        public string? Frequency { get; set; }
        public int IsActive { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime UpdateDate { get; set; }

    }

    #endregion

    #region CategoryMasters

    public class CategoryMasters
    {
        public int Id { get; set; }
        public string? CategoryName { get; set; }
        public string? Description { get; set; }
        public string? CategoryRange { get; set; }
        public int MinRange { get; set; }
        public int MaxRange { get; set; }
    }

    #endregion

    #region EntityBusinessFunction

    public class EntityBusinessFunction
    {
        public int FunctionID { get; set; }
        public string? FunctionName { get; set; }
    }
    #endregion

    #region VaultChat and VaultChatUser Details

    /// <summary>
    /// VaultChatUserAndMessage class holds user and message details
    /// </summary>


    public class VaultChatUserAndMessage
    {

        public int ChatMessageId { get; set; }

        public string? ChatMessage { get; set; }

        public int ChatUserDetailsId { get; set; }

        public int SenderUserId { get; set; }

        public int ReceiverUserId { get; set; }

        public DateTime ChangedAt { get; set; }

        public int ChangedBy { get; set; }

        public int IsActive { get; set; }

        public string? SenderName { get; set; }

        public string? ReceiverName { get; set; }

        public int SenderGuid { get; set; }

        public string? SenderMime { get; set; }

        public int ReceiverGuid { get; set; }

        public string? ReceiverMime { get; set; }
    }


    #endregion

    #region EntityApprovalRecords

    public class EntityApprovalRecords
    {
        public int ID { get; set; }
        public int EntityID { get; set; }
        public int RecordID { get; set; }
        public string? Remarks { get; set; }
        public int SenderID { get; set; }
        public int ReceiverID { get; set; }
        public int Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
    }


    #endregion

    #region BCMAuditCheck List
    public class BCMAuditEscalation
    {
        public int AuditID { get; set; }
        public int groupMapId { get; set; }
        public string? Subject { get; set; }
        public string? MailBody { get; set; }
        public string? AuditType { get; set; }
        public int MgtGroupMapId { get; set; }
        public int EscalatedBy { get; set; }
        public int EscalateID { get; set; }
        public int resourceID { get; set; }
        public string? CommunicationMode { get; set; }

    }

    public class CheckList
    {
        public int CheckListId { get; set; }
        public string? CheckListName { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public string? UpdatedDate { get; set; }
        public int OrgID { get; set; }

    }


    public class CheckListItemsInfo
    {
        public int CheckListItemId { get; set; }
        public int ChecklListId { get; set; }
        public string? CheckListName { get; set; }
        public string? CheckListItemName { get; set; }
        public int IsActive { get; set; }
        public int RAGStatus { get; set; }
        public string? Remarks { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int CLMapID { get; set; }
    }



    public class SMSKeyword
    {
        public int KeywordId { get; set; }
        public string? KeywordName { get; set; }
        public int OrgID { get; set; }
        public string? ApplicationName { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
    }



    public class SMSResponse
    {
        public int MobileNo { get; set; }
        public int APPCode { get; set; }
        public int ModuleId { get; set; }
        public string? Response { get; set; }
        public string? TemeStamp { get; set; }
        public int RecordId { get; set; }
        public string? SMSText { get; set; }
    }




    public class BCMAuditMaster
    {
        public int AuditTypeId { get; set; }
        public int AuditType { get; set; }
        public int AuditMasterTypeId { get; set; }
        public string? AuditMasterTypeName { get; set; }
        public int MasterAuditListId { get; set; }
        public string? MasterAuditListName { get; set; }

        public int AuditOwnerID { get; set; }
        public string? AuditOwnerName { get; set; }
        public string? AuditOwnerEmail { get; set; }
        public int AuditOwnerMobile { get; set; }

        public int OrgID { get; set; }
        public string? OrganizationName { get; set; }
        public int UnitId { get; set; }
        public string? UnitName { get; set; }
        public int SiteId { get; set; }
        public string? SiteName { get; set; }
        public int DepartmentId { get; set; }
        public string? DepartmentName { get; set; }
        public int SubFunctionId { get; set; }
        public string? SubFunctionName { get; set; }

        public int ProcessId { get; set; }
        public int EntityTypeID { get; set; }
        public string? EntityTypeName { get; set; }
        public string? ProcessName { get; set; }
        public int ProcessOwnerID { get; set; }
        public int AltProcessOwnerID { get; set; }

        public int AuditTeamId { get; set; }
        public string? AuditTeamName { get; set; }

        public int CheckListId { get; set; }
        public string? CheckListname { get; set; }
        public int CheckListItemId { get; set; }
        public string? CheckListItemName { get; set; }

        public DateTime plannedStartDt { get; set; }
        public DateTime plannedEndDt { get; set; }
        public DateTime ActualStartDt { get; set; }
        public DateTime ActualEndDt { get; set; }
        public int Status { get; set; }
        public string? Auditor { get; set; }

        public string? AuditorName { get; set; }
        public string? AuditorEmail { get; set; }
        public string? AuditorMobile { get; set; }

        public string? Observer { get; set; }
        public string? ObserverName { get; set; }
        public string? ObserverEmail { get; set; }
        public int ObserverMobile { get; set; }

        public int AuditeeId { get; set; }
        public string? AuditeeName { get; set; }
        public string? AuditeeEmail { get; set; }
        public int AuditeeMobile { get; set; }

        public int ApproverID { get; set; }
        public string? ApproverName { get; set; }
        public string? ApproverEmail { get; set; }
        public int ApproverMobile { get; set; }
        public int ApprvlStatus { get; set; }
        public int AuditID { get; set; }
        public int AuditPlanID { get; set; }
        public string? Result { get; set; }
        public string? Description { get; set; }
        public string? NonConformityType { get; set; }

        public string? Objective { get; set; }
        public string? Activities { get; set; }
        public string? Approach { get; set; }
        public string? Scope { get; set; }

        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }

        public string? AuditScore { get; set; }
        public string? AuditDescription { get; set; }
        public string? AuditMasterListDescription { get; set; }
        public string? Version { get; set; }
        public int IsEffective { get; set; }
        public string? AuditPlanDesc { get; set; }
        public string? AuditAlertDays { get; set; }

        public int OrgHeadId { get; set; }
        public int UnitHeadID { get; set; }
        public int AltUnitHeadID { get; set; }
        public int UnitBCPCorID { get; set; }
        public int AltBCPCorID { get; set; }
        public int DepartmentHeadID { get; set; }
        public int AltDepartmentHeadID { get; set; }
        public int SubFunOwnerId { get; set; }
        public int AltSubFunOwnerId { get; set; }

        public int OrgGroupID { get; set; }

        public int ActivationModeMobileAltr { get; set; }
        public string? MobileVerifiedAltr { get; set; }

        public string? MobileVerifiedTooltipAltr { get; set; }

        public int ActivationModeEmailAltr { get; set; }

        public int EmailVerifiedAltr { get; set; }

        public string? EmailVerifiedTooltipAltr { get; set; }


        public int ActivationModeMobile { get; set; }
        public string? MobileVerified { get; set; }

        public string? MobileVerifiedTooltip { get; set; }

        public int ActivationModeEmail { get; set; }
        public int EmailVerified { get; set; }

        public string? EmailVerifiedTooltip { get; set; }

        public int IsActive_Email { get; set; }
        public int IsActive_Mobile { get; set; }


        //   public string? Approver { get; set; }

        // public string? ApprovalStatus { get; set; }
    }


    public class BCMAuditChart
    {
        public int AuditTypeId { get; set; }
        public string? AuditType { get; set; }
        public string? totalCount { get; set; }
        public string? Month { get; set; }
    }



    public class AuditCheckList
    {
        public int Id { get; set; }
        public int AuditTypeId { get; set; }
        public int AuditID { get; set; }
        public int CheckListId { get; set; }
        public string? CheckListName { get; set; }
        public int CheckListItemId { get; set; }
        public string? CheckListItemName { get; set; }
        public int IsCheck { get; set; }
        public int IsActive { get; set; }
        public int RAGStatus { get; set; }
        public string? Remarks { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
    }




    public class BCMAuditResults
    {
        public int AuditID { get; set; }
        public int AuditorId { get; set; }
        public string? AuditorName { get; set; }
        public string? AuditorEmail { get; set; }
        public int AuditorMobile { get; set; }
        public DateTime PlanStartDate { get; set; }

        public int ResponsibleId { get; set; }
        public string? ResposibleName { get; set; }
        public string? ResponsibleEmail { get; set; }
        public string? ResponsibleMobilePhone { get; set; }
        public string? InOutBound { get; set; }

        public string? ActionItem { get; set; }
        public string? Description { get; set; }
        public DateTime CommunicationDate { get; set; }
        public string? CommunicationMode { get; set; }
    }



    public class BCMAuditTimeLines
    {
        public int IssueId { get; set; }
        public int AuditID { get; set; }

        public int AuditorId { get; set; }
        public string? AuditorName { get; set; }
        public string? AuditorEmail { get; set; }
        public int AuditorMobile { get; set; }
        public string? TimeLine { get; set; }
        public int ResponsibleId { get; set; }
        public string? ResposibleName { get; set; }
        public string? ResponsibleEmail { get; set; }
        public string? ResponsibleMobilePhone { get; set; }

        public string? InOutBound { get; set; }

        public string? ActionItem { get; set; }
        public string? Description { get; set; }
        public DateTime CommunicationDate { get; set; }
        public string? CommunicationMode { get; set; }
    }




    public class BCMAuditResultResponse
    {
        public int AuditID { get; set; }
        public int AuditorId { get; set; }
        public string? AuditorName { get; set; }
        public string? AuditorEmail { get; set; }
        public int AuditorMobile { get; set; }
        public string? InOutBound { get; set; }
        public DateTime CommunicationDate { get; set; }
        public string? CommunicationMode { get; set; }
    }



    public class BCMAuditTimelineResponse
    {
        public int AuditID { get; set; }
        public int ResponsibleId { get; set; }
        public string? ResposibleName { get; set; }
        public string? ResponsibleEmail { get; set; }
        public string? ResponsibleMobilePhone { get; set; }
        public string? InOutBound { get; set; }
        public DateTime CommunicationDate { get; set; }
        public string? CommunicationMode { get; set; }
    }


    #region AuditNotification

    public class BCMAuditNotification
    {
        public int AuditId { get; set; }
        public string? AuditTypeName { get; set; }
        public int AuditorId { get; set; }
        public int ApproverId { get; set; }
        public string? AuditorName { get; set; }
        public int NotificationID { get; set; }
        public string? Mode { get; set; }
        public string? Subject { get; set; }
        public string? MailBody { get; set; }
        public int NotifiedBy { get; set; }
        public string? CommunicationMode { get; set; }
        public DateTime NotiDate { get; set; }
        public string? NotiType { get; set; }
        public string? SentSuccess { get; set; }
        public string? NeedResponse { get; set; }
    }


    public class BCMAuditNotificationResource
    {
        public int ApproverId { get; set; }
        public string? AuditorName { get; set; }
        public int NotificationID { get; set; }
        public string? Mode { get; set; }
        public string? Subject { get; set; }
        public string? MailBody { get; set; }
        public int NotifiedBy { get; set; }
        public string? CommunicationMode { get; set; }
        public DateTime NotiDate { get; set; }
        public string? NotiType { get; set; }
        public string? SentSuccess { get; set; }
        public string? Response { get; set; }
        public int AuditId { get; set; }
        public int ResponseDate { get; set; }
        public int ApprvlStatus { get; set; }
    }



    #endregion



    #region RecommendationItem
    public class BCMAuditRecommendationItem
    {
        public int RecommendId { get; set; }
        public int AuditId { get; set; }
        public string? Description { get; set; }
        public string? Responsible { get; set; }
        public string? ActionItem { get; set; }
        public int Status { get; set; }
        public string? Timeline { get; set; }
        public int Is_Active { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    #endregion

    #region KeyIssues

    public class BCMAuditKeyIssues
    {
        public int ID { get; set; }
        public int KeyId { get; set; }
        public int AuditId { get; set; }
        public int AuditTypeID { get; set; }
        public string? Description { get; set; }
        public string? AssignedTo { get; set; }
        public string? RiskCategory { get; set; }
        public string? RiskCategoryName { get; set; }
        public string? RiskRating { get; set; }
        public string? ActionItem { get; set; }
        public int Status { get; set; }
        public string? Timeline { get; set; }

        public int Is_Active { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? Comments { get; set; }
        public string? Evidence { get; set; }
        public string? AttachmentFileName { get; set; }
        public int AttachmentID { get; set; }
        public string? IssueType { get; set; }

        public string? AuditType { get; set; }
        public string? AuditTypeName { get; set; }
        public string? AuditResult { get; set; }
        public string? Responsible { get; set; }
        public string? NonConfirmityType { get; set; }
        public string? AssignedToName { get; set; }
        public string? ResponsibleName { get; set; }
        public string? feedback { get; set; }
        public string? Metric { get; set; }
        public string? Version { get; set; }
        public int CLMapID { get; set; }
        public int ChecklListId { get; set; }
        public string? CheckListName { get; set; }
        public string? CheckListItemName { get; set; }
        public int CheckListItemId { get; set; }

    }

    #endregion

    #region ObservationAndFeedBack

    public class BCMAuditObservationFeedback
    {
        public int ObsFedId { get; set; }
        public int AuditId { get; set; }
        public string? AuditEntityType { get; set; }
        public string? Description { get; set; }
        public int Is_Active { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
    #endregion


    #region CVaultEntities

    public class CVaultEntities
    {
        public int EntityID { get; set; }
        public string? EntityName { get; set; }
        public int IsActive { get; set; }
        public int ParentID { get; set; }
    }
    #endregion

    #region Plan Approver Reminder

    public class PlanApprover
    {
        public int PlanId { get; set; }
        public string? PlanName { get; set; }
        public int ApproverID { get; set; }
        public string? ApproverName { get; set; }
        public string? ApproverEmail { get; set; }
        public string? ApproverMobile { get; set; }
        public int OwnerID { get; set; }
        public int StageID { get; set; }
        public string? StepName { get; set; }
        public string? TaskName { get; set; }
    }
    #endregion

    #region Risk


    public class RiskTreatment
    {
        RiskManagement RiskManagement { get; set; }

        RiskTreatment RiskTreatments { get; set; }
    }

    public class RiskManagement : RiskTreatmentPlan
    {
        public int ID { get; set; }
        public int RiskID { get; set; }
        public int IsActive { get; set; }
        public string? RiskName { get; set; }
        public string? RiskCategory { get; set; }
        public string? RiskType { get; set; }
        public string? RiskItemCategory { get; set; }
        public string? RiskItemSubCategory { get; set; }
        public string? RiskToItem { get; set; }
        public string? ProcessCode { get; set; }
        public string? RiskTo { get; set; }
        public int OrgID { get; set; }
        public string? OrgName { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public DateTime CloseDate { get; set; }
        public DateTime LastAccessDate { get; set; }
        public DateTime NextReviewDate { get; set; }
        public string? RiskDesCription { get; set; }
        public string? RiskCode { get; set; }
        public string? Impact { get; set; }
        public string? Mitigation { get; set; }
        public string? CotigencyStep { get; set; }
        public string? CotigencyPlan { get; set; }
        public string? LikliHood { get; set; }
        public string? RiskValue { get; set; }
        public string? RiskRating { get; set; }
        public DateTime LastReviewDate { get; set; }
        public string? PreventiveAction { get; set; }
        public DateTime RiskReviewDate { get; set; }
        public string? Reminder { get; set; }
        public string? RecurrenceRule { get; set; }

        public string? RiskOwner { get; set; }
        public string? RiskChampion { get; set; }
        public string? RiskOwnerName { get; set; }
        public int RiskOwnerID { get; set; }
        public int RiskOwnerMobile { get; set; }
        public string? RiskOwnerEmail { get; set; }

        public string? MgmtDecision { get; set; }
        public string? RiskHandleStatus { get; set; }
        public int CreatedBy { get; set; }
        public int IsEffective { get; set; }
        public DateTime ChangedOn { get; set; }
        public string? UnitName { get; set; }
        public int SubDepartmentID { get; set; }
        public string? CateName { get; set; }
        public string? SubCateName { get; set; }
        public string? PacaCode { get; set; }
        public int PCAID { get; set; }
        public string? DeptName { get; set; }
        public string? SubDeptName { get; set; }
        public int ProcessID { get; set; }
        public string? ProcessName { get; set; }

        public int UnitHeadId { get; set; }
        public int AltUnitHeadId { get; set; }
        public int UnitBCPCorId { get; set; }
        public int AltBCPCorId { get; set; }
        public int DeptHeadId { get; set; }
        public int AltDeptHeadId { get; set; }
        public int SubFunOwnerId { get; set; }
        public int AltSubFunOwnerId { get; set; }
        public string? RiskTypeName { get; set; }
        public string? RiskategoryName { get; set; }

        public int IncidentID { get; set; }
        public string? IncidentCode { get; set; }
        public string? IncidentName { get; set; }
        public string? IncidentStatus { get; set; }

        public int FacilityID { get; set; }
        public int LocationID { get; set; }
        public int OverdueBy { get; set; }

        public string? Drill { get; set; }
        public string? Live { get; set; }
        public DateTime IncidentLastReviewDate { get; set; }
        public DateTime IncidentReviewDate { get; set; }

        public string? PACAType { get; set; }
        public int ResourceID { get; set; }
        public DateTime CreateDate { get; set; }
        public string? MobilePhone { get; set; }
        public string? CompanyEmail { get; set; }
        public int IsActive_Email { get; set; }
        public int IsActive_Mobile { get; set; }
        public string? Field1 { get; set; }
        public string? Field2 { get; set; }
        public string? Field3 { get; set; }
        public string? Field4 { get; set; }

        public int? BCMEntityID { get; set; }
        public string? BCMEntityName { get; set; }
        public DateTime RiskStartDate { get; set; }
        public DateTime RiskEndDate { get; set; }
        public int EntityID { get; set; }
        public string? ApproverName { get; set; }
        public int ApproverID { get; set; }

        public string? ResidualImpact { get; set; }
        public string? ResidualLikeliHood { get; set; }
        public string? ResidualRiskRating { get; set; }

        public int ResidualRiskRatings { get; set; }

        public string? TimeLine { get; set; }
        public string? Version { get; set; }

        public int ParentID { get; set; }

        public int RiskTypeID { get; set; }
        public string? RiskTreeStructure { get; set; }
        public string? UpdateReason { get; set; }

        public int SubRiskRegID { get; set; }
        public int RiskEntityID { get; set; }

        public string? Vulnerability { get; set; }
        public string? PACAStatus { get; set; }

        public int PACAOwnerID { get; set; }
        public string? PACAOwnerName { get; set; }

        public string? RiskSubRegTrend { get; set; }
        public int RiskSubRegID { get; set; }

        public DateTime UpdatedDate { get; set; }
        public int UpdatedByID { get; set; }
        public string? UpdatedByName { get; set; }

        public DateTime TargetDate { get; set; }

        public string? RiskCategoryNamewithversion { get; set; }
        public string? RiskSubCategoryNamewithversion { get; set; }

        public int RiskTrendStatus { get; set; }
        public int PercentageCompletion { get; set; }
        public string? RiskTypeMapping { get; set; }
        public int RiskCloseStatus { get; set; }
        public string? TreatmentCost { get; set; }
        public DateTime RiskTargetDate { get; set; }
        public string? MDecisionComment { get; set; }

        public DateTime RiskEntryDate { get; set; }

        public string? ProbableRiskScenarios { get; set; }
        public string? ServiceCriticallyValue { get; set; }

        public string? IncidentTypeName { get; set; }
        public int IncidentTypeID { get; set; }
        public int Status { get; set; }
        public int RiskChampionID { get; set; }
        public string? RiskChampionName { get; set; }
        public int PlanID { get; set; }
        public string? IncidentDisplayName { get; set; }

        public int ActivationModeMobile { get; set; }
        public string? MobileVerified { get; set; }
        public string? MobileVerifiedTooltip { get; set; }
        public int ActivationModeEmail { get; set; }
        public int EmailVerified { get; set; }
        public string? EmailVerifiedTooltip { get; set; }

        public int DisasterID { get; set; }
        public int DisasterName { get; set; }
        public int DisasterTypeID { get; set; }

        public int ImpactId { get; set; }
        public string? ImpactName { get; set; }

        public int PlanId { get; set; }

        public int ApprovalStatus { get; set; }

        public string? LatestVersion { get; set; }
        public int IsBCMEntity { get; set; }

        public string? Remark { get; set; }

        public DateTime NxtDate { get; set; }

    }
    public class RiskAssesmentResult
    {
        public string? RiskCode { get; set; }
        public string? RiskName { get; set; }
        public string? Threat { get; set; }
        public string? Vulnerability { get; set; }
        public string? AffectedServices { get; set; }
        public string? RiskDescription { get; set; }
        public string? ExistingControl { get; set; }
        public string? ProbabilityRating { get; set; }
        public string? ImpactRating { get; set; }
        public string? OverAllRiskRating { get; set; }
    }

    #endregion


    #region RiskStatus


    public class RiskStatus
    {
        public int RiskStatusID { get; set; }
        public int EntityID { get; set; }

        public int RecordID { get; set; }
        public int Status { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime ReviewDate { get; set; }
        public int IsEffective { get; set; }
        public string? Description { get; set; }

        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
    }



    #endregion

    #region DashboardCountEntity
    [DataContract]
    [Serializable()]
    public class DashboardCount : UserActivityStatus
    {
        [DataMember]
        public string ID;
        [DataMember]
        public string OrgID;
        [DataMember]
        public string FacilitiesCount;
        [DataMember]
        public string LocationCount;
        [DataMember]
        public string CalenderCount;
        [DataMember]
        public string IncidentCount;
        [DataMember]
        public string RiskCount;
        [DataMember]
        public string DepartmentBIACount;
        [DataMember]
        public string MumbaiLoactionCount;
        [DataMember]
        public string businessprocessCount;
        [DataMember]
        public string CriticalbusinessprocessCount;
        [DataMember]
        public string NonCriticalbusinessprocessCount;
        [DataMember]
        public string frpdocumentcontrolcount;
        [DataMember]
        public string erpdocumentcontrolcount;
        [DataMember]
        public string RiskAssessmentCount;
        [DataMember]
        public string DrillCount;
        [DataMember]
        public string DepartmentCount;

        [DataMember]
        public string RiskAssessmentPendingCount;

        [DataMember]
        public string DepartmentBIAPendingCount;
        [DataMember]
        public string applicationBIAPendingCount;
        [DataMember]
        public string frpdocumentcontrolpendingcount;
        [DataMember]
        public string ERPPendingCount;
        [DataMember]
        public string recoveryplanPendingCount;
        [DataMember]
        public string biaprofilePendingCount;
        [DataMember]
        public string bcmauditPendingCount;
        [DataMember]
        public string ApplicationCriticalCount;


        [DataMember]
        public string RiskAssessmentCountReview;

        [DataMember]
        public string frpdocumentcontrolcountReview;


        [DataMember]
        public string erpdocumentcontrolcountReview;

    }

    #endregion

    #region customDashboard

    public class DashboardApprovalPendingCount
    {
        public int RecoveryPlan { get; set; }
        public int? OtherBCMEntities { get; set; }
        public int BCMStategies { get; set; }
        public int BCMAudit { get; set; }
        public int BIAProfile { get; set; }
        public int RiskAssessment { get; set; }
        public int BusinessProcess { get; set; }
    }
    public class DashboardWidgets
    {
        public int WidgetId { get; set; }
        public string? WidgetName { get; set; }
        public int UserID { get; set; }
        public int OrgID { get; set; }
        public int isActive { get; set; }
        public int CreatedBy { get; set; }
        public int TempID { get; set; }
        public string? TempName { get; set; }
        public string? TempDesciption { get; set; }
        public int RoleID { get; set; }


        public int OrganisationSummary { get; set; }
        public int MyTaskList { get; set; }
        public int ToDo { get; set; }
        public int BCMCalender { get; set; }
        public int PendingApprovals { get; set; }
        public int IncidentSummary { get; set; }
        public int ActivityStream { get; set; }
        public int ProcessRisk { get; set; }
        public int ResourceRisk { get; set; }
        public int OtherBCMRisk { get; set; }
        public int PlanRisk { get; set; }
        public int BIASummaryRisk { get; set; }
        public int latestIncidentStatus { get; set; }
        public int Messaging { get; set; }
        public int OpenRisk { get; set; }
        public int UrgentAttension { get; set; }
        public int TeamNotificationSummary { get; set; }
        public int VendorRisk { get; set; }
        public int ViewCuurentExcalations { get; set; }
        public int ViewEscalatedEscalations { get; set; }
        public int AllRiskStatus { get; set; }
        public int LatestIncident { get; set; }
        public int BCMAreaReview { get; set; }
    }

    public class DashboardTemplateEntity
    {
        public int TempId { get; set; }
        public string? TempDescription { get; set; }
        public int UserID { get; set; }
        public string? UserName { get; set; }
        public int OrgID { get; set; }
        public int isActive { get; set; }
        public int roleID { get; set; }
        public string? RoleName { get; set; }

        public string? RoleDiscription { get; set; }

        public int CreatedBy { get; set; }

        public string? TempName { get; set; }
    }


    #endregion

    #region processbiapeopleactivity



    public class ProcessBIAPeopleActivity
    {
        public int Id { get; set; }
        public int BIAID { get; set; }
        public int QuestionID { get; set; }
        public string? QuestionDetails { get; set; }
        public string? Activity { get; set; }
        public string? PotentialTeam { get; set; }

        public int CreatedBy { get; set; }
        public int IsComplete { get; set; }

        public string? Manpower { get; set; }
    }
    #endregion

    #region ProcessBIAPeople

    public class ProcessBIA
    {
        public List<ProcessBIAPeopleInfo>? ProcessBIAPeopleInfo { get; set; }
        public List<ProcessBIAPeopleActivity>? ProcessBIAPeopleActivity { get; set; }
    }


    public class ProcessBIAPeopleInfo : ProcessBIAThirdParty
    {
        //public int ID { get; set; }
        //public int BIAID { get; set; }
        public int ResourceID { get; set; }
        public string? ResourceName { get; set; }
        //public int IsCritical { get; set; }
        //public int SectionID { get; set; }
        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? PrimaryResource { get; set; }
        //public int OwnerMobile { get; set; }
        public string? OwnerEmail { get; set; }
        public int AltResourceID { get; set; }
        public string? AltResourceName { get; set; }
        public string? AltOwnerName { get; set; }
        public string? AssignedBackup { get; set; }
        public int AltOwnerMobile { get; set; }
        public string? AltOwnerEmail { get; set; }


        public string? AlterOwnerMobile { get; set; }

        public string? AlternateOwnerMobile { get; set; }


        //public int QuestionID { get; set; }
        //public string? QuestionDetails { get; set; }
        //public DateTime CreatedAt { get; set; }
        //public int CreatedBy { get; set; }
        //public int ChangedBy { get; set; }
        //public DateTime ChangedAt { get; set; }
        //public string? BIAFindings { get; set; }
        //public int OrgID { get; set; }
        //public int UnitID { get; set; }
        //public int DepartmentID { get; set; }
        //public int SubfunctionID { get; set; }
        //public string? ProcessName { get; set; }
        public int ProcessOwnerID { get; set; }
        public int AltProcessOwnerID { get; set; }
        //public string? ProcessOwnerName { get; set; }
        public string? AltProcessOwnerName { get; set; }
        //public string? RTO { get; set; }
        //public int Impact { get; set; }
        public string? PotentialTeam { get; set; }
        public string? Activity { get; set; }
        public int Day1 { get; set; }
        public int Day3 { get; set; }
        public int Day7 { get; set; }
        public int Day14 { get; set; }
        public int Day30 { get; set; }
        public int Beyond { get; set; }
        public int UnitHeadID { get; set; }
        public int AltUnitHeadId { get; set; }
        public int UnitBCPCorID { get; set; }
        public int AltBCPCorID { get; set; }
        public int DepartmentHeadID { get; set; }
        public int AltDepartmentHeadID { get; set; }
        public int ApproverID { get; set; }
        public int OrgHeadId { get; set; }
        public int AltSubFunOwnerId { get; set; }
        public int SunFunOwnerId { get; set; }
        public int IsComplete { get; set; }
        public int actualcurrentmanpower { get; set; }

        public string? Time { get; set; }
        public int Value { get; set; }
        //public string? OrgHeadId{ get; set; }
    }

    #endregion

    #region ProcessBIAApplication

    public class ProcessBIAApplicationInfo : ProcessBIAThirdParty
    {
        //public int ID { get; set; }
        //public int BIAID { get; set; }
        //public int SectionID { get; set; }
        public int IsUsedByThirdParty { get; set; }
        //public int ThirdPartyID { get; set; }
        //public string? ThirdPartyName { get; set; }
        public int ApplicationID { get; set; }
        public string? ApplicationName { get; set; }
        public string? ApplicationDetails { get; set; }
        public string? IsApplicationDown { get; set; }
        public string? ApplicationOwnerName { get; set; }
        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerPhone { get; set; }
        public string? OwnerEmail { get; set; }
        //public string? IsActive_Email{ get; set; }
        //public string? IsActive_Mobile{ get; set; }
        //public string? ActivationModeMobile{ get; set; }
        //public string? MobileVerifiedTooltip{ get; set; }
        //public string? ActivationModeEmail{ get; set; }
        //public string? MobileVerified{ get; set; }
        //public string? EmailVerified{ get; set; } 
        //public string? EmailVerifiedTooltip{ get; set; }
        public string? CalculatedRTO { get; set; }
        //public int ChangedBy { get; set; }
        //public DateTime ChangedAt { get; set; }
        public string? ProcessRTO { get; set; }
        //public string? ProcessName { get; set; }
        //public int QuestionID { get; set; }
        //public string? QuestionDetails { get; set; }
        //public int IsCritical { get; set; }
        //public int IsDrImplemented { get; set; }
        //public int CreatedBy { get; set; }
        //public DateTime CreatedAt { get; set; }
        public int IsThirdParty { get; set; }
        //public string? BIAFindings { get; set; }
        //public int OrgID { get; set; }
        //public int UnitID { get; set; }
        //public int DepartmentID { get; set; }
        //public int SubfunctionID { get; set; }
        public string? BP_RTO { get; set; }
        //public int Impact { get; set; }
        public int IsComplete { get; set; }
        //added by priyanka
        public string? KeyUsage { get; set; }
        public int RecoveryTime { get; set; }
        public int DataLoss { get; set; }
        public int RTOText { get; set; }
        public string? ManualWorkarounds { get; set; }
        public string? Unit { get; set; }
        public string? DatalossUnit { get; set; }

        public string? ApplicationRTOText { get; set; }
        public string? ApplicationRPOText { get; set; }

        public DateTime UpdateDate { get; set; }

        //public int ProcessID { get; set; }

        //public string? ProcessCode { get; set; }

        //public string? LastEmailResponse{ get; set; }
        //public string? LastMobileResponse{ get; set; }
    }

    #endregion

    #region ProcessBIAThirdParty


    public class ProcessBIAThirdParty : UserActivityStatus
    {
        public int ID { get; set; }
        public int ThirdPartyID { get; set; }
        public string? ThirdPartyName { get; set; }
        public int SPOCID { get; set; }
        public string? SPOCName { get; set; }
        public string? SPOCMobile { get; set; }
        public string? SPOCEmail { get; set; }
        public int AltSPOCID { get; set; }
        public string? AltSPOCName { get; set; }
        public string? AltSPOCMobile { get; set; }
        public string? AltSPOCEmail { get; set; }
        public int IsCritical { get; set; }
        public int BIAID { get; set; }
        public int QuestionID { get; set; }
        public string? QuestionDetails { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? BIAFindings { get; set; }
        public int SectionID { get; set; }
        public string? ProcessName { get; set; }
        public string? ProcessCode { get; set; }
        public string? RTO { get; set; }
        public string? RPO { get; set; }
        public string? OwnerRTO { get; set; }
        public string? OwnerMTR { get; set; }
        public int StatusID { get; set; }
        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public int SubfunctionID { get; set; }
        public string? ProcessOwnerName { get; set; }
        public string? OwnerMobile { get; set; }
        public string? OwnerMail { get; set; }
        public string? UnitName { get; set; }
        public string? DepartmentName { get; set; }
        public string? SubFunctionName { get; set; }
        public string? OrganizationName { get; set; }
        public int OrgGroupID { get; set; }
        public int ProcessID { get; set; }
        public int Impact { get; set; }
        public string? NatureOfService { get; set; }
        public int CriticalityCategory { get; set; }
        public string? Contingencies { get; set; }
        public string? PotentialAlternateSources { get; set; }
        public int IsComplete { get; set; }
        public string? Version { get; set; }
        public int MinimumRTO { get; set; }
        public int MinimumRPO { get; set; }
    }

    #endregion

    #region ProcessBIAFacility


    public class ProcessBIAFacility : ProcessBIAThirdParty
    {
        //public int ID { get; set; }
        public int FacilityID { get; set; }
        public string? FacilityName { get; set; }
        public string? FacilityAddress { get; set; }
        public string? FacilityManagerName { get; set; }
        public string? FacilityType { get; set; }
        //public int BIAID { get; set; }
        //public int QuestionID { get; set; }
        //public string? QuestionDetails { get; set; }
        //public int ChangedBy { get; set; }
        //public DateTime ChangedAt { get; set; }
        //public int CreatedBy { get; set; }
        //public DateTime CreatedAt { get; set; }
        public string? iBIAFindings { get; set; }
        //public int SectionID { get; set; }
        public int FacilityManagerId { get; set; }
        //public string? ProcessName { get; set; }
        //public string? RTO { get; set; }
        //public int IsCritical { get; set; }
        public string? ResourceName { get; set; }
        public int ResourceMobile { get; set; }
        public string? ResourceEmail { get; set; }
        public string? LocationName { get; set; }
        //public int OrgID { get; set; }
        //public int UnitID { get; set; }
        //public int DepartmentID { get; set; }
        //public int SubfunctionID { get; set; }


        public string? TotalPlatformByHCLoacation { get; set; }
        public string? TeamSize { get; set; }
        public string? TeamDistribution { get; set; }
        public string? CurrentProcessSLA { get; set; }
        public string? MemberConnectFromHome { get; set; }
        public string? ResourceMovedToDRSite { get; set; }
        public string? ResourceMovedToAnotherLocation { get; set; }
        public string? ResourceMovedToEGSLocations { get; set; }
        public string? IncreasingShiftTiming { get; set; }
        //public int Impact { get; set; }
        public string? RemoteAccessDetails { get; set; }
        public string? SecondaryLocation { get; set; }
        public string? RemoteAccess { get; set; }
        //public int IsComplete { get; set; }


    }

    #endregion

    #region biaupstreamprocesses


    public class biaupstreamprocesses
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int ProcessID { get; set; }
        public string? Description { get; set; }
        public int QuestionID { get; set; }
        public string? QuestionDetails { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdateDate { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }


        public string? UpStreamProcessName { get; set; }
        public string? RTO { get; set; }
        public int IsCritical { get; set; }
        public string? ResourceName { get; set; }
        public int ResourceMobile { get; set; }
        public string? ResourceEmail { get; set; }
        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public int SubfunctionID { get; set; }


    }

    #endregion

    #region processbialegalandregulatory


    public class ProcessBIALegalAndRegulatory
    {
        public int ID { get; set; }
        public string? LegalAndRegAuth { get; set; }
        public string? DependencyReport { get; set; }
        public string? Frequency { get; set; }
        public int BIAID { get; set; }
        public int QuestionID { get; set; }
        public string? QuestionDetails { get; set; }
        public int ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? BIAFindings { get; set; }
        public int SectionID { get; set; }
        public int IsComplete { get; set; }
    }

    #endregion

    #region ProcessBIAImpactMatrix


    public class ProcessBIAImpactMatrix
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int ImpactSeverityID { get; set; }
        public int ImpactTypeID { get; set; }
        public string? ImpactTypeName { get; set; }
        public int ImpactID { get; set; }
        public string? ImpactName { get; set; }
        public int Upto2Hours { get; set; }
        public int Upto4Hours { get; set; }
        public int Upto8Hours { get; set; }
        public int Upto12Hours { get; set; }
        public int Upto24Hours { get; set; }
        public int Upto48Hours { get; set; }
        public int Upto72Hours { get; set; }
        public int Upto1Week { get; set; }
        public int Upto2Weeks { get; set; }
        public int Upto1Month { get; set; }
        public int IsQualitative { get; set; }
        public string? Comment { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int QuestionID { get; set; }
        public int SectionID { get; set; }
    }

    #endregion

    #region Terminology
    public class Terminology
    {
        public string? unit { get; set; }
        public string? department { get; set; }
        public string? subdepartment { get; set; }
        public string? process { get; set; }
        public string? resource { get; set; }
        public string? owner { get; set; }
        public string? mobile { get; set; }
        public string? email { get; set; }
        public string? description { get; set; }
        public string? organisation { get; set; }
        public string? risk { get; set; }
        public string? riskcategory { get; set; }
        public string? risktype { get; set; }
        public string? planname { get; set; }
        public string? planowner { get; set; }
        public string? planapprover { get; set; }
        public string? alternateowner { get; set; }
        public string? revisionDate { get; set; }
        public string? contigency { get; set; }
        public string? liklihood { get; set; }
        public string? mitigation { get; set; }

    }
    public class TerminologyHelpText
    {
        public string? unit { get; set; }
        public string? department { get; set; }
        public string? subdepartment { get; set; }
        public string? process { get; set; }
        public string? resource { get; set; }
        public string? owner { get; set; }
        public string? mobile { get; set; }
        public string? email { get; set; }
        public string? description { get; set; }
        public string? organisation { get; set; }
        public string? risk { get; set; }
        public string? riskcategory { get; set; }
        public string? risktype { get; set; }
        public string? planname { get; set; }
        public string? planowner { get; set; }
        public string? planapprover { get; set; }
        public string? alternateowner { get; set; }
        public string? revisionDate { get; set; }
        public string? contigency { get; set; }
        public string? liklihood { get; set; }
        public string? mitigation { get; set; }

    }

    public class TerminologyID
    {
        public string? unit { get; set; }
        public string? department { get; set; }
        public string? subdepartment { get; set; }
        public string? process { get; set; }
        public string? resource { get; set; }
        public string? owner { get; set; }
        public string? mobile { get; set; }
        public string? email { get; set; }
        public string? description { get; set; }
        public string? organisation { get; set; }
        public string? risk { get; set; }
        public string? riskcategory { get; set; }
        public string? risktype { get; set; }
        public string? planname { get; set; }
        public string? planowner { get; set; }
        public string? planapprover { get; set; }
        public string? alternateowner { get; set; }
        public string? revisionDate { get; set; }
        public string? contigency { get; set; }
        public string? liklihood { get; set; }
        public string? mitigation { get; set; }
    }
    #endregion

    #region ReportTemplate

    public class ReportTemplateInfo
    {
        // reporttemplate table
        public string? TempId { get; set; }
        public string? TempName { get; set; }
        public string? TempDescription { get; set; }
        public string? CreatedBy { get; set; }
        public string? CreateDate { get; set; }
        public string? IsSharable { get; set; }
        public string? OrgGroupId { get; set; }
        public string? OrgId { get; set; }
        public string? UnitId { get; set; }
        public string? DepartmentId { get; set; }
        public string? SubfunctionId { get; set; }

        //reportcriteria table
        public string? CriteriaId { get; set; }
        public string? CriteriaName { get; set; }

        //reportfields table
        public string? FieldId { get; set; }
        public string? FieldName { get; set; }

        //reporttemplateentity table
        public string? EntityMapId { get; set; }
        public string? RptTempId { get; set; }
        public string? RptEntityId { get; set; }
        public string? Type { get; set; }
        public string? CriteriaValue { get; set; }

        public string? IsActive { get; set; }
    }

    #endregion

    #region Citymasters

    public class Citymaster
    {
        public string? CityId { get; set; }
        public string? StateId { get; set; }
        public string? Latitude { get; set; }
        public string? Longitude { get; set; }
        public string? STDCode { get; set; }
        public string? IsActive { get; set; }
        public string? CreatorId { get; set; }
        public string? CreateDate { get; set; }
        public string? UpdatorId { get; set; }
        public string? UpdateDate { get; set; }
        public string? CityName { get; set; }
    }

    #endregion

    #region UserActivityStatus

    public class UserActivityStatus
    {
        public int IsActive_Email { get; set; }
        public BigInteger IsActive_Mobile { get; set; }
        public string? LastEmailResponse { get; set; }
        public string? LastMobileResponse { get; set; }
        public string? ActivationModeMobile { get; set; }
        public string? MobileVerified { get; set; }
        public string? ActivationModeEmail { get; set; }
        public string? EmailVerified { get; set; }
        public string? MobileVerifiedTooltip { get; set; }
        public string? EmailVerifiedTooltip { get; set; }
        public int IsActive_EmailAltr { get; set; }

        public BigInteger IsActive_MobileAltr { get; set; }
        public string? LastEmailResponseAltr { get; set; }
        public string? LastMobileResponseAltr { get; set; }
        public string? ActivationModeMobileAltr { get; set; }
        public string? MobileVerifiedAltr { get; set; }
        public string? ActivationModeEmailAltr { get; set; }
        public string? EmailVerifiedAltr { get; set; }
        public string? MobileVerifiedTooltipAltr { get; set; }
        public string? EmailVerifiedTooltipAltr { get; set; }
        public string? IsActiveImageUrl { get; set; }
        public string? IsActiveImageUrlAltr { get; set; }
        // used in Incident Timeline for Reassigning Step
        public int IsActive_Email_Reassigned { get; set; }
        public int IsActive_Mobile_Reassigned { get; set; }
        public string? LastEmailResponse_Reassigned { get; set; }
        public string? LastMobileResponse_Reassigned { get; set; }
        public string? ActivationModeMobile_Reassigned { get; set; }
        public string? MobileVerified_Reassigned { get; set; }
        public string? ActivationModeEmail_Reassigned { get; set; }
        public string? EmailVerified_Reassigned { get; set; }
        public string? MobileVerifiedTooltip_Reassigned { get; set; }
        public string? EmailVerifiedTooltip_Reassigned { get; set; }
        public string? IsActiveImageUrl_Reassigned { get; set; }


        public string? IsActive_EmailAuth { get; set; }
        public string? IsActive_MobileAuth { get; set; }
        public string? ActivationModeMobileAuth { get; set; }
        public string? MobileVerifiedTooltipAuth { get; set; }
        public string? MobileVerifiedAuth { get; set; }
        public string? ActivationModeEmailAuth { get; set; }
        public string? EmailVerifiedAuth { get; set; }
        public string? EmailVerifiedTooltipAuth { get; set; }
        public string? IsActive_EmailApvr { get; set; }
        public string? IsActive_MobileApvr { get; set; }
        public string? ActivationModeMobileApvr { get; set; }
        public string? MobileVerifiedApvr { get; set; }
        public string? ActivationModeEmailApvr { get; set; }
        public string? EmailVerifiedApvr { get; set; }
        public string? EmailVerifiedTooltipApvr { get; set; }
        public string? MobileVerifiedTooltipApvr { get; set; }
    }

    #endregion

    public class TimeUnit
    {
        public string? ID { get; set; }
        public string? Name { get; set; }
    }

    public class RTOEnums
    {
        public int RTOID { get; set; }
        public string? RTOName { get; set; }
    }

    public class CalendarStatusEnums
    {
        public int StatusID { get; set; }
        public string? StatusName { get; set; }
    }

    public class NotificationAsEnums
    {
        public int NotificationAsID { get; set; }
        public string? NotificationAsName { get; set; }
    }

    public class StepStatusEnums
    {
        public int StatusId { get; set; }
        public string? StatusName { get; set; }
    }

    public class MonthsEnums
    {
        public int MonthId { get; set; }
        public string? MonthName { get; set; }
    }

    public class ReviewReportEnums
    {
        public int StatusId { get; set; }
        public string? StatusName { get; set; }
    }

    #region WorkflowAction

    public class WorkflowActionSaveUpdate
    {
        public WorkflowActionInfo? objWorkflowActionInfo { get; set; }
        public List<int>? objDependedOnCheckList { get; set; }
    }

    public class WorkflowActionInfo
    {
        public string? ID { get; set; }
        public string? Name { get; set; }
        public string? RTO { get; set; }
        public string? Description { get; set; }
        public string? Stepownerid { get; set; }
        public string? Aleternatespetownerid { get; set; }
        public string? Estimationtime { get; set; }
        public string? TimeUnit { get; set; }
        public string? PlanID { get; set; }
        public string? InterDependancy { get; set; }
        public string? DependancyOn { get; set; }
        public string? IsUsedinWorkflow { get; set; }
        public string? ChangedBy { get; set; }
        public string? GoToStep { get; set; }
        public string? CPProfileID { get; set; }
        public string[] objDependedOnCheckListNew { get; set; }
    }


    public class Workflow
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? WorkflowXoml { get; set; }
        public object WorkflowXmlDiagram { get; set; }
        public string? HiddenWorkflowId { get; set; }
        public string? ChangedBy { get; set; }

    }

    public class DashboardInfo
    {
        public int? Id { get; set; }
        public string? ReferenceId { get; set; }
        public string? Name { get; set; }
        public string? Properties { get; set; }
        public bool? IsLock { get; set; }
        public bool? IsPublish { get; set; }
        public bool? IsActive { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? LastModifiedBy { get; set; }
        public DateTime? LastModifiedDate { get; set; }


    }


    #endregion


    #region RecoveryStepsDependentMapping


    public class RecoveryStepsDependentMapping
    {

        public string? Id { get; set; }

        public string? StepID { get; set; }

        public string? DependentStepID { get; set; }
    }


    #endregion


    #region WorkFlowExecutionPlan



    public class WorkFlowExecutionPlanInfo
    {
        public string? PlanID { get; set; }
        public string? StepID { get; set; }
        public string? TaskID { get; set; }
        public string? IncidentID { get; set; }
        public string? ChangedBy { get; set; }

        public string? IsCondition { get; set; }
        public string? SuccessStepID { get; set; }
        public string? FailureStepID { get; set; }
        public string? Sequence { get; set; }
    }



    #endregion

    #region UserActivities


    public class UserActivitiesInfo
    {
        public int ID { get; set; }
        public int UserID { get; set; }
        public string? HostName { get; set; }
        public string? IPAddress { get; set; }
        public string? SessionID { get; set; }
        public DateTime CreatedDate { get; set; }
        public int SessionIsActive { get; set; }
        public string? SessionEndReason { get; set; }
        public string? ResourceName { get; set; }
        public string? OrgName { get; set; }
        public int OrgID { get; set; }
        public string? LastLoginTime { get; set; }
        public UserActivitiesInfo() { }

    }


    #endregion

    #region UserActivitiesLoginStat


    public class UserActivitiesLoginStatInfo
    {
        public string? ID { get; set; }
        public string? UserID { get; set; }
        public string? NoOfLogin { get; set; }
        public string? NoOfFailureAttempt { get; set; }
        public string? CreatedDate { get; set; }
        public string? UpdatedDate { get; set; }
    }

    #endregion

    #region "BIADependentServices"


    public class BIADependentServicesInfo
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int ProcessID { get; set; }
        public string? Description { get; set; }
        public string? IscriticalForProcess { get; set; }
        public string? BIAFindings { get; set; }

        public string? ProcessName { get; set; }
        public string? ProcessCode { get; set; }
        public string? Version { get; set; }
        public string? OrgName { get; set; }
        public string? UnitName { get; set; }
        public string? DepartmentName { get; set; }
        public string? SubFunctionName { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerEmail { get; set; }
        public int OwnerMobile { get; set; }
        public string? RTO { get; set; }
        public int IsCritical { get; set; }
        public int StatusID { get; set; }

        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int? ChangedBy { get; set; }
        public int? QuestionID { get; set; }
        public string? Question { get; set; }
        public int SectionID { get; set; }
        public int Impact { get; set; }
        public int IsComplete { get; set; }
    }


    #endregion "BIADependentServices"


    #region "BIADependentEntities"


    public class BIADependentEntitiesInfo
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public int EntityID { get; set; }
        public string? Description { get; set; }
        public string? IscriticalForProcess { get; set; }
        public string? BIAFinding { get; set; }
        public string? EntityName { get; set; }
        public string? ProcessCode { get; set; }
        public string? Version { get; set; }
        public string? OrgName { get; set; }
        public string? UnitName { get; set; }
        public string? DepartmentName { get; set; }
        public string? SubFunctionName { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerEmail { get; set; }
        public string? OwnerMobile { get; set; }
        public string? RTO { get; set; }
        public int IsCritical { get; set; }
        public int StatusID { get; set; }

        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int QuestionID { get; set; }
        public string? Question { get; set; }
        public int SectionID { get; set; }
        public int Impact { get; set; }
        public int IsComplete { get; set; }
    }


    #endregion "BIADependentEntities"


    #region SMSResponse_Ooredoo

    public class SMSResponse_Ooredoo
    {
        public string? ID { get; set; }
        public string? Keyword { get; set; }
        public string? StepID { get; set; }
        public string? Timestamp { get; set; }
        public string? Response { get; set; }
        public string? Message { get; set; }
        public string? From { get; set; }
        public string? MessageType { get; set; }
        public string? IsRead { get; set; }
        public string? ResourceName { get; set; }
        public string? OrgID { get; set; }

    }

    #endregion


    #region Authentication Key Mapping For External Client


    public class AuthKeyMappingForExtClient
    {
        public string? ID { get; set; }
        public string? AuthKey { get; set; }
        public string? ClientName { get; set; }
        public string? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }


    }

    #endregion


    #region SchedulerProfile

    public class SchedulerProfile
    {
        public string? ProfileID { get; set; }
        public string? ProfileName { get; set; }
        public string? OrgID { get; set; }
        public string? OwnerID { get; set; }
        public string? AltOwnerID { get; set; }
        public string? ScheduleTime { get; set; }
        public string? IsScheduled { get; set; }
        public string? IsSendAsLink { get; set; }//ProfileID, ProfileName, OwnerID, AltOwnerID, ScheduleTime, IsScheduled, IsSendAsLink, IsSendAsAttachment, CreatedAt, CreatedBy, UpdatedAt, UpdatedBy, IsActive
        public string? IsSendAsAttachment { get; set; }
        public string? CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
        public string? IsActive { get; set; }
    }


    #endregion

    #region SchedulerProfileReceiver

    public class SchedulerProfileReceiver
    {
        public string? ProfileID { get; set; }
        public string? ProfileName { get; set; }
        public string? ReportID { get; set; }
        public string? ReportName { get; set; }
        public string? UserID { get; set; }
        public string? UserMobilePhone { get; set; }
        public string? UserCompanyEmail { get; set; }
        public string? TeamID { get; set; }
        public string? OrgID { get; set; }
        public string? OwnerID { get; set; }
        public string? AltOwnerID { get; set; }
        public string? ScheduleTime { get; set; }
        public string? IsScheduled { get; set; }
        public string? IsSendAsLink { get; set; }//ProfileID, ProfileName, OwnerID, AltOwnerID, ScheduleTime, IsScheduled, IsSendAsLink, IsSendAsAttachment, CreatedAt, CreatedBy, UpdatedAt, UpdatedBy, IsActive
        public string? IsSendAsAttachment { get; set; }
        public string? CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
    }

    #endregion


    #region SchedulerProfileReports

    public class SchedulerProfileReports
    {
        public string? ID { get; set; }
        public string? ProfileID { get; set; }
        public string? ReportID { get; set; }

    }

    #endregion
    #region MetricMeasurement

    public class ProfilePerformance
    {
        public string? MetricID { get; set; }
        public string? MetricName { get; set; }
        public string? MetricDetail { get; set; }
        public string? MeasurementID { get; set; }
        public string? MeasurementName { get; set; }
        public string? MeasurementDetail { get; set; }
        public string? ProfileID { get; set; }
        public string? ProfileName { get; set; }
        public string? ProfileDetail { get; set; }
        public string? ProfileOwnerID { get; set; }
        public string? FrequencyID { get; set; }
        public string? ModeID { get; set; }
        public string? AnalysisID { get; set; }
        public string? ScheduleTime { get; set; }
        public string? IsScheduled { get; set; }
        public string? IsSendAsLink { get; set; }
        public string? IsSendAsAttachment { get; set; }
        public string? CreatedBy { get; set; }
        public string? OrgID { get; set; }
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }
        public string? UserMobile { get; set; }
        public string? UserID { get; set; }
        // public string? UserMobilePhone{ get; set; }
        // public string? UserCompanyEmail{ get; set; }
        public string? TeamID { get; set; }
        public string? RecordID { get; set; }

    }

    #endregion

    #region ManagementReview

    #region ReviewReportMaster
    public class ReviewReportMaster
    {
        public string? ReportID { get; set; }
        public string? ReportName { get; set; }
        public string? ImpLinks { get; set; }
        public string? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? IsActive { get; set; }
        public string? OrgID { get; set; }
        public DateTime? PlanDate { get; set; }
        public string? Description { get; set; }
        public string? OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerEmail { get; set; }
        public string? OwnerMobile { get; set; }
        public string? ReportCode { get; set; }
        public string? Status { get; set; }
        public string? OrganizationName { get; set; }

    }

    #endregion


    #region ReviewReportMomItem


    public class ReviewReportMomItem
    {
        public string? ID { get; set; }
        public string? ReportID { get; set; }
        public string? ReviewTypeID { get; set; }
        public string? ReportName { get; set; }
        public string? ReviewType { get; set; }
        public string? ImpLinks { get; set; }
        public string? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? IsActive { get; set; }
        public string? ClosedDate { get; set; }
        public string? ReviewDate { get; set; }
        public string? IsClosed { get; set; }
        public string? OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerMobile { get; set; }
        public string? OwnerEmail { get; set; }
        public string? Description { get; set; }
        public string? MomItem { get; set; }
        public string? PlanStartDate { get; set; }
        public string? PlanEndDate { get; set; }
        public string? ActualStartDate { get; set; }
        public string? ActualEndDate { get; set; }
        public string? IsActionItem { get; set; }
        public string? IsImprovement { get; set; }
        public string? BCMEntityType { get; set; }
        public string? BCMEntityID { get; set; }
        public string? ActionOwnerID { get; set; }
        public string? Status { get; set; }

        public string? Improvement { get; set; }
        public string? ImprovementID { get; set; }
        public string? ImpPlanStartDate { get; set; }
        public string? ImpPlanEndDate { get; set; }
        public string? ImpActualStartDate { get; set; }
        public string? ImpActualEndDate { get; set; }
        public string? ImpStatus { get; set; }
        public string? ImpDescription { get; set; }
        public string? ImpOwnerID { get; set; }


        public string? ActionItem { get; set; }
        public string? ActionOwnerName { get; set; }
        public string? ImpOwnerName { get; set; }
        public string? BCMEntityName { get; set; }

    }

    #endregion


    #region ReviewTypeMaster

    public class ReviewType
    {
        public List<ReviewTypeMaster> ReviewTypeMaster { get; set; }
        public List<MomTotal> MomTotal { get; set; }

        public List<ReviewReportMomItem> ReviewReportMomItem { get; set; }
    }

    public class MomTotal
    {
        public int? TotalCount { get; set; }
        public int? ClosedCount { get; set; }
        public int? OpenCount { get; set; }
        public int? TotalCount_Review { get; set; }
        public int? ClosedCount_Review { get; set; }
        public int? OpenCount_Review { get; set; }
        public int? PreviousCount { get; set; }
        public string? ReviewTypeID { get; set; }
        public string? ReviewType { get; set; }
        public string? OrgID { get; set; }
        public int ReportID { get; set; }
    }

    public class ReportItems
    {
        public int ReportID { get; set; }
        public int ReviewTypeID { get; set; }
        public int MomID { get; set; }
    }

    public class ReviewTypeMaster
    {
        public string? ReportID { get; set; }
        public string? ReportName { get; set; }
        public string? ImpLinks { get; set; }
        public string? Comment { get; set; }
        public string? ReportCode { get; set; }
        public string? PlanDate { get; set; }
        public string? Status { get; set; }
        public string? OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerEmail { get; set; }
        public string? OwnerMobile { get; set; }
        public string? IsActive_Email { get; set; }
        public string? IsActive_Mobile { get; set; }
        public string? ReviewTypeID { get; set; }
        public string? ReviewType { get; set; }
        public string? OrgID { get; set; }
        public string? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? Description { get; set; }
        public string? IsActive { get; set; }
        public string? UpdatedDate { get; set; }
        public string? UpdatedBy { get; set; }
        public string? TotalCount { get; set; }
        public string? ClosedCount { get; set; }
        public string? OpenCount { get; set; }
        public string? PreviousCount { get; set; }
        public string? TotalCount_Review { get; set; }
        public string? ClosedCount_Review { get; set; }
        public string? OpenCount_Review { get; set; }
        public string? CloseDate { get; set; }
        public string? IsClosed { get; set; }
        public string? IsActionItem { get; set; }
        public string? IsImprovement { get; set; }
        public string? MomItem { get; set; }
        public int MomID { get; set; }
        public string? MomOwnerID { get; set; }
        public string? MomOwnerName { get; set; }
        public string? MomOwnerEmail { get; set; }
        public string? MomOwnerMobile { get; set; }
    }

    #endregion

    #region MomImprovements


    public class MomImprovements
    {
        public string? ImprovementID { get; set; }
        public string? ReportID { get; set; }
        public string? ReviewTypeID { get; set; }
        public string? ReportName { get; set; }
        public string? ReviewType { get; set; }
        public string? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? IsActive { get; set; }
        public string? IsClosed { get; set; }
        public string? ImpOwnerID { get; set; }
        public string? ImpOwnerName { get; set; }
        public string? Description { get; set; }
        public string? MomItem { get; set; }
        public string? PlanStartDate { get; set; }
        public string? PlanEndDate { get; set; }
        public string? ActualStartDate { get; set; }
        public string? ActualEndDate { get; set; }
        public string? IsActionItem { get; set; }
        public string? IsImprovement { get; set; }
        public string? BCMEntityType { get; set; }
        public string? BCMEntityID { get; set; }
        //public string? ImpOwnerID{ get; set; }
        public string? Status { get; set; }
        public string? Improvement { get; set; }

        public string? OutCome { get; set; }
    }
    #endregion
    #endregion

    #region PerformanceEvaluation

    public class PerformanceEvaluation
    {
        public int ID { get; set; }
        public int OrgID { get; set; }
        public string? EvaluationName { get; set; }
        public int StatusID { get; set; }
        public int MetricID { get; set; }
        public string? MetricName { get; set; }
        public int MeasurementID { get; set; }
        public int ModeID { get; set; }
        public int AnalysisID { get; set; }
        public int FrequencyID { get; set; }
        public string? FrequencyName { get; set; }
        public DateTime EvaluationDate { get; set; }
        public DateTime CloseDate { get; set; }
        public int MeasuredBy { get; set; }
        public string? CurIndicator { get; set; }
        public string? PrevIndicator { get; set; }
        public string? Trend { get; set; }
        public string? TrendHtmlText { get; set; }
        public string? Reason { get; set; }
        public string? ReasonHtmlText { get; set; }
        public string? RequiredAction { get; set; }
        public string? ActionHtmlText { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public DateTime EvFromDate { get; set; }
        public DateTime EvToDate { get; set; }
        public int IsEffective { get; set; }

        public string? ResourcesReq { get; set; }
        public string? RespParties { get; set; }
        public string? RespPartiesName { get; set; }
        public string? EffectiveCriteria { get; set; }
        public string? RemarkEffLevel { get; set; }
        public int EffectiveRating { get; set; }
        public double EffectiveRatingRpt { get; set; }
        public string Effectiveness { get; set; }
        public int OverallKPI { get; set; }
        public string? Target { get; set; }
        public DateTime TargetedDate { get; set; }
        public string? CorrectiveAction { get; set; }
        public string? CurrentRisk { get; set; }
        public string? Remarks { get; set; }

        public string? ITDRName { get; set; }
        public string? Objective { get; set; }
        public string? Effectiveness1 { get; set; }
        public string? MeasureKPI { get; set; }
        public string? ResponsibleParties { get; set; }
        public string? Rating { get; set; }
        public DateTime Targeteddate { get; set; }

        public string? MeasuredByName { get; set; }
    }


    #endregion


    #region KPIMeasurementMaster


    public class KPIMeasurementMasters
    {
        public int ID { get; set; }
        public string? Objective { get; set; }
        public string? Effectiveness { get; set; }
        public string? Target { get; set; }
        public string? EffectivenessRating { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime ChangedDate { get; set; }
        public int OrgId { get; set; }
        public int IsActive { get; set; }
    }


    #endregion


    #region RiskHeatmap


    public class RiskImpactMaster
    {


        public string? Id { get; set; }
        public string? ImpactName { get; set; }
        public string? ImpactDescription { get; set; }
        // public string? RiskImpactMastercol{ get; set; }
        public int ImpactWeightage { get; set; }
        public string? RiskSeverityDescription { get; set; }
        public string? ColorCode { get; set; }
        public int FromImpactRange { get; set; }
        public int ToImpactRange { get; set; }
        public int ImpactSequence { get; set; }


        public int riskprobabilitymasterid { get; set; }
        public string? ProbabilityName { get; set; }
        public string? ProbabilityDescription { get; set; }
        public int ProbabilityWeightage { get; set; }
        public int ProbabilitySequence { get; set; }

        public int Profileid { get; set; }
        public string? ProfileName { get; set; }
        public string? ProfileDescription { get; set; }
        public int OrgID { get; set; }
        public int Owner { get; set; }
        public int Approver { get; set; }


        public int CreatedBy { get; set; }
        public string? CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public string? UpdatedDate { get; set; }

        public string? RiskSeverityName { get; set; }

        //public int FromImpactRange{ get; set; }
        //public int ToImpactRange{ get; set; }

        public int RiskRating { get; set; }
        public string? ImpactRating { get; set; }
        public int ProbabilityID { get; set; }
        //public string? ProbabilityName { get; set; }
        public int ImpactID { get; set; }
        //public string? ImpactName { get; set; }

        public int ResidualLikeliHoodID { get; set; }
        public int ResidualImpactID { get; set; }

        public int ResidualRiskRatings { get; set; }
        public int Impact { get; set; }
        public string? ResidualImpact { get; set; }
    }


    #endregion

    #region RiskProfileMaster

    public class RiskProfileMaster
    {
        public int ProfileID { get; set; }
        public string? ProfileName { get; set; }

        public string? ProfileDescription { get; set; }

        public string? ImpactDescription { get; set; }

        public string? ProbabilityDescription { get; set; }

        public string? RiskSeverityDescription { get; set; }

        public int ImpactScale { get; set; }

        public int ProbabilityScale { get; set; }

        public string? CreatedDate { get; set; }

        public int CreatedBy { get; set; }

        public int Owner { get; set; }

        public int Approver { get; set; }

        public int OrgID { get; set; }

        public int IsDefault { get; set; }

        public int IsActive { get; set; }
    }


    #endregion


    #region CVDetailKeys

    public class CVDetailKeys
    {
        public int ID { get; set; }

        public string? DetailKey { get; set; }

        public int DetailID { get; set; }

        public int IsUsed { get; set; }

        public string? EntityCode { get; set; }

        public int EntityTypeID { get; set; }
    }


    #endregion


    #region BusinessParameter


    public class BusinessParameter
    {  //ID, ParameterName, OrgID, IsActive, CreatedBy, CreateDate, UpdatedBy, UpdateDate
        //NumberValueType   RangeFromOperator RangeToOperator IsNumberListIn NumberList string?ValueID
        public int ID { get; set; }
        public string? ParameterName { get; set; }
        //ID, ParameterName, ParameterCode, Description, Category, ValueType, Measure, Unit, Descrete, RangeFrom, RangeTo, BinaryValue1,
        //BinaryValue2, string?Value, OrgID, IsActive, CreatedBy, CreateDate, UpdatedBy, UpdateDate

        public string? ParameterCode { get; set; }
        public string? Description { get; set; }
        public int Category { get; set; }
        public int ValueType { get; set; }
        public int Measure { get; set; }
        public int Unit { get; set; }
        public int Descrete { get; set; }
        public int RangeFrom { get; set; }
        public int RangeTo { get; set; }

        public string? BinaryValue1 { get; set; }
        public string? BinaryValue2 { get; set; }
        public int DescreteOperator { get; set; }

        public string? stringValue { get; set; }

        public int OrgID { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public string? CreateDate { get; set; }

        public int UpdatedBy { get; set; }
        public string? UpdateDate { get; set; }

        public int NumberValueType { get; set; }
        public int RangeFromOperator { get; set; }
        public int RangeToOperator { get; set; }
        public int IsNumberListIn { get; set; }

        public string? NumberList { get; set; }
        public int stringValueID { get; set; }

        public int CostAsInput { get; set; }
        public int TimeAsInput { get; set; }
    }

    #endregion


    #region BusinessParameterProfile


    public class BusinessParameterProfiles
    {  //ID, ProfileName, Description, OrgID, IsActive, CreatedBy, CreateDate, UpdatedBy, UpdateDate
        public int ID { get; set; }
        public string? ProfileName { get; set; }
        public string? Description { get; set; }
        public int OrgID { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public string? CreateDate { get; set; }

        public int UpdatedBy { get; set; }
        public string? UpdateDate { get; set; }
    }


    #endregion

    #region BusinessProfileParameter


    public class BusinessProfileParameter
    {  //ID, ProfileID, ParameterID
        public int ID { get; set; }
        public int ProfileID { get; set; }
        public int ParameterID { get; set; }
        public int ProcessID { get; set; }


        public int UpdatedBy { get; set; }
    }


    #endregion

    #region OrgStructureWeightage


    public class OrgStructureWeightages
    {
        public int ID { get; set; }
        public int EntityID { get; set; }
        public string? RecordID { get; set; }
        public int OrgID { get; set; }
        public double Weightage { get; set; }
    }


    #endregion





    #region BusinessParameterValue


    public class BusinessParameterValue
    {  //ID, ParameterName, OrgID, IsActive, CreatedBy, CreateDate, UpdatedBy, UpdateDate
        //NumberValueType   RangeFromOperator RangeToOperator IsNumberListIn NumberList string?ValueID
        public int ID { get; set; }
        public string? ParameterName { get; set; }
        //ID, ParameterName, ParameterCode, Description, Category, ValueType, Measure, Unit, Descrete, RangeFrom, RangeTo, BinaryValue1,
        //BinaryValue2, string?Value, OrgID, IsActive, CreatedBy, CreateDate, UpdatedBy, UpdateDate
        public int ProcessID { get; set; }
        public int ParameterID { get; set; }
        public int ProfileID { get; set; }
        public string? CronExpression { get; set; }
        public int IsMonitoringEnabled { get; set; }

        // public string? ParameterCode{ get; set; }
        // public string? Description{ get; set; }
        // public int Category{ get; set; }
        public int ValueType { get; set; }
        public int Measure { get; set; }
        public int Unit { get; set; }
        public int Descrete { get; set; }
        public int RangeFrom { get; set; }
        public int RangeTo { get; set; }

        public string? BinaryValue1 { get; set; }
        public string? BinaryValue2 { get; set; }
        public int DescreteOperator { get; set; }

        public string? stringValue { get; set; }

        public int OrgID { get; set; }
        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public string? CreateDate { get; set; }

        public int UpdatedBy { get; set; }
        public string? UpdateDate { get; set; }

        public int NumberValueType { get; set; }
        public int RangeFromOperator { get; set; }
        public int RangeToOperator { get; set; }
        public int IsNumberListIn { get; set; }

        public string? NumberList { get; set; }
        public int stringValueID { get; set; }





    }


    #endregion

    #region TimeIntervalMaster

    public class TimeIntervalMaster
    {
        //ID, Value, Time, Sequence
        public int ID { get; set; }
        public int Value { get; set; }
        public string? Time { get; set; }
        public int Sequence { get; set; }
        public int Qantity { get; set; }
    }


    #endregion

    #region TimeIntervalDataForReport

    public class TimeIntervalDataForReport
    {
        public string? ASGName { get; set; }
        public string? DepartmentName { get; set; }
        public string? SubDepartmentName { get; set; }
        public string? DivisionName { get; set; }
        public string? ProcessName { get; set; }
        public string? Time { get; set; }
        public int Value { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public int SubFunctionID { get; set; }
        public int OrgID { get; set; }
        public int ProcessID { get; set; }

    }


    #endregion



    #region BIAHRRequirementsReport

    public class BIAHRRequirementsReport
    {
        public string? ProcessName { get; set; }
        public string? CurrentActualManPower { get; set; }
        public string? Time { get; set; }
        public string? Value { get; set; }
        public string? Activity { get; set; }
        public string? PotentialTeam { get; set; }
        public string? BIAID { get; set; }
    }


    #endregion



    #region PeopleTimeDetails

    public class PeopleTimeDetailsViewModel
    {
        public int BIAID { get; set; }
        public int IsComplete { get; set; }
        public int ActualCurrentManPowerID { get; set; }
        public int ActualCurrentManPower { get; set; }
        public int ColumnCount { get; set; }
        public List<TimeIntervalMaster> TimeIntervalMasterList { get; set; }
        public List<PeopleTimeDetails> PeopleTimeDetailsList { get; set; }
        public List<GridRowModel> GridRows { get; set; }

        public List<TimeIntervalMaster> TimeIntervalMaster { get; set; }
        // Add other properties as needed
    }

    public class GridRowModel
    {
        public Dictionary<string, int> Values { get; set; }
    }

    public class PeopleTimeDetailsFormModel
    {
        public int ActualCurrentManpower { get; set; }

        // Dynamic properties for time intervals - these will be populated based on TimeIntervalMaster data
        // Common time intervals (these can be extended based on actual data)
        public int Day1 { get; set; }
        public int Day3 { get; set; }
        public int Day7 { get; set; }
        public int Day14 { get; set; }
        public int Day30 { get; set; }
        public int Beyond { get; set; }
        public int Week1 { get; set; }
        public int Week2 { get; set; }
        public int Week3 { get; set; }
        public int Week4 { get; set; }
        public int Month1 { get; set; }
        public int Month2 { get; set; }
        public int Month3 { get; set; }
        public int Month6 { get; set; }
        public int Year1 { get; set; }

        // Method to get all time interval values as a dictionary
        public Dictionary<string, int> GetTimeIntervalValues()
        {
            var values = new Dictionary<string, int>();
            var properties = this.GetType().GetProperties()
                .Where(p => p.PropertyType == typeof(int) && p.Name != "ActualCurrentManpower");

            foreach (var prop in properties)
            {
                values[prop.Name] = (int)prop.GetValue(this);
            }

            return values;
        }
    }

    public class PeopleTimeDetails
    {
        //ID, Value, TimeIntervalMasterId, ProcessBiaPeopleID
        public int ID { get; set; }
        public int Value { get; set; }
        public int TimeIntervalMasterId { get; set; }
        public int ProcessBiaPeopleID { get; set; }
        public string? Time { get; set; }

        public int CreatedBy { get; set; }
    }

    public class BIAActualCurrentManPower
    {
        public int ID { get; set; }
        public int BIAID { get; set; }
        public string? actualcurrentmanpower { get; set; }
        public int CreatedBy { get; set; }
        public int IsComplete { get; set; }
        public int ResourceID { get; set; }
        public string? ResourceName { get; set; }
        public int AltResourceID { get; set; }
        public string? AltResourceName { get; set; }
    }

    #endregion

    #region equipments


    public class Equipments : UserActivityStatus
    {
        //ID,EquipmentName,Description,IsActive,CreatorId,CreateDate,UpdatorId,UpdateDate
        public int ID { get; set; }
        public string? EquipmentName { get; set; }
        public string? Description { get; set; }
        public int IsActive { get; set; }
        public int CreatorId { get; set; }
        public DateTime CreateDate { get; set; }
        public int UpdatorId { get; set; }
        public string? UpdateDate { get; set; }


    }

    #endregion

    #region EquipmentSupply


    public class EquipmentSupply : UserActivityStatus
    {
        //ID,EquipmentId,Description,IsActive,CreatorId,CreateDate,UpdatorId,UpdateDate
        public int ID { get; set; }
        public int EquipmentId { get; set; }
        public string? Description { get; set; }
        public string? EquipmentName { get; set; }
        public int IsActive { get; set; }
        public int CreatorId { get; set; }
        public DateTime CreateDate { get; set; }
        public int UpdatorId { get; set; }
        public List<equipmentsupply_quantity> equipmentsupply_quantitylist { get; set; }
        public string? UpdateDate { get; set; }
        public string? QuestionID { get; set; }
        public string? QuestionDetails { get; set; }
        public int BIAID { get; set; }
        public int Day1 { get; set; }
        public int Day3 { get; set; }
        public int Day7 { get; set; }
        public int Day14 { get; set; }
        public int Day30 { get; set; }
        public int Beyond { get; set; }
        public string? ProcessName { get; set; }
        public int IsComplete { get; set; }

        public int Version { get; set; }

        public int OrgID { get; set; }
        public int UnitID { get; set; }
        public int DepartmentID { get; set; }
        public int SubFunctionID { get; set; }
    }

    #endregion

    #region equipmentsupply_quantity


    public class equipmentsupply_quantity : UserActivityStatus
    {
        //Id,EquipmentSupplyId,TimeIntervalId,Quantity,IsActive,CreatorId,CreateDate,UpdatorId,UpdateDate
        public int Id { get; set; }
        public int EquipmentSupplyId { get; set; }
        public int TimeIntervalId { get; set; }
        public string? ProcessBiaPeopleID { get; set; }
        public int Quantity { get; set; }
        public string? Time { get; set; }
        public int IsActive { get; set; }
        public int CreatorId { get; set; }
        public DateTime CreateDate { get; set; }
        public int UpdatorId { get; set; }
        public DateTime UpdateDate { get; set; }

    }

    #endregion

    #region ITServiceMaster

    public class ITServiceMaster
    {
        //ID, Value, TimeIntervalMasterId, ProcessBiaPeopleID
        public int ID { get; set; }
        public int ITServiceID { get; set; }
        public string? ITServiceName { get; set; }
        public string? ITServiceDescription { get; set; }
        public int OrgId { get; set; }
        public int OwnerID { get; set; }
        public int EntityTypeID { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
        public string? BIAID { get; set; }
        public string? QuestionID { get; set; }
        public string? Question { get; set; }
        public string? ProcessCode { get; set; }
        public string? Version { get; set; }
        public int IsComplete { get; set; }
    }


    #endregion

    #region Resourcedesignation

    public class ResourceDesignation
    {

        public int ID { get; set; }
        public string? Designation { get; set; }
        public int OrgId { get; set; }

    }


    #endregion


    #region OverallCount

    public class OverallCount
    {
        public int TotalOrg { get; set; }
        public int TotalUnit { get; set; }
        public int TotalDept { get; set; }
        public int TotalDivision { get; set; }

    }
    #endregion

    #region PasswordHistory

    public class PasswordHistory
    {
        public string? ID { get; set; }
        public string? UserID { get; set; }
        public string? Password { get; set; }
        public string? CreateDate { get; set; }
        public string? OrgID { get; set; }
    }

    #endregion

    #region Statistics

    public class RiskStatistics
    {
        public int UpComingRisksForReviews { get; set; }
        public int TotalRiskCount { get; set; }
        public int TotalInitiatedRiskCount { get; set; }
        public int PastReviewedDateRiskCount { get; set; }
    }

    public class RecoveryPlanStatistics
    {
        public int UpComingRecoveryPlanForReviews { get; set; }
        public int PastRecoveryPlanForReviews { get; set; }
        public int TotalRecoveryPlanCount { get; set; }
        public int TotalInitiatedRecoveryPlanCount { get; set; }
        public int TotalWaitingForApproval { get; set; }
        public int TotalRecoveryPlanPercentage { get; set; }
    }

    public class AuditStatistics
    {
        public int UpComingAuditForReviews { get; set; }
        public int TotalAuditCount { get; set; }
        public int TotalInitiatedAuditCount { get; set; }
    }

    public class VendorStatistics
    {
        public int PastVendorForReviews { get; set; }
        public int UpComingVendorForReviews { get; set; }
        public int TotalVendorCount { get; set; }
        //public int TotalInitiatedAuditCount { get; set; }
    }

    #endregion


    public class UserRoleDetails
    {

        public int RoleID { get; set; }

        public int UserID { get; set; }

        public int OrgGroupID { get; set; }

        public int OrgID { get; set; }

        public int RoleRightsSubEntityID { get; set; }

        public int masterentrytypeID { get; set; }

        public int ParentPrivilegeID { get; set; }

        public int PrivilegeID { get; set; }

        public int CreatedBy { get; set; }

        public string? CreatedOn { get; set; }
    }



    #region parallel_profile

    public class parallel_profile
    {
        public int Id { get; set; }
        public string? ProfileName { get; set; }


        public string? Status { get; set; }


        public string? Password { get; set; }
        public string? Description;
    }

    #endregion


    #region ParallelDROperation

    public class ParallelDROperation
    {
        public int Id { get; set; }


        public DateTime StartTime { get; set; }


        public DateTime EndTime { get; set; }


        public string? Status { get; set; }


        public string? Description { get; set; }



        public string? ActionMode { get; set; }


        public int ProfileId { get; set; }


        public string? ProfileName { get; set; }





        public int CreatorId { get; set; }



    }

    #endregion




    #region ParallelWorkflowProfile

    public class ParallelWorkflowProfile
    {
        public int Id { get; set; }
        public int InfraobjectId { get; set; }


        public int WorkflowType { get; set; }


        public string? InfraobjectName { get; set; }


        public string? GroupState { get; set; }


        public int ProfileId { get; set; }


        public int WorkflowId { get; set; }


        public string? WorkflowName { get; set; }


        public int CurrentActionId { get; set; }


        public string? CurrentActionName { get; set; }


        public string? Status { get; set; }


        public string? Message { get; set; }


        public string? ProgressStatus { get; set; }


        public int ConditionalOperation { get; set; }


        public int CheckedStatus { get; set; }


        public string? NameofTable { get; set; }



        public string? ProfileName { get; set; }


        public string? BusinessServiceName { get; set; }


        public string? BusinessFunctionName { get; set; }


        public string? DRCo_Status { get; set; }


        public string? DRCoStrWorkflowIds { get; set; }


        public int DRCoordinationId { get; set; }



        public string? WorkflowVersion { get; set; }
        public int CreatorId { get; set; }



    }

    #endregion


    #region ParallelGroupWorkflow

    public class ParallelGroupWorkflow
    {


        public int Id { get; set; }
        public int InfraObjectId { get; set; }


        public string? InfraObjectName { get; set; }


        public string? GroupState { get; set; }


        public int WorkflowId { get; set; }


        public string? WorkflowName { get; set; }


        public int CurrentActionId { get; set; }


        public string? CurrentActionName { get; set; }


        public string? Status { get; set; }


        public string? Message { get; set; }


        public int ParallelDROperationId { get; set; }


        public int ConditionalOperation { get; set; }


        public string? ProgressStatus { get; set; }


        public string? NameofTable { get; set; }


        public string? WorkflowVersion { get; set; }


        public int CreatorId { get; set; }


    }

    #endregion

    public class ProcessUpDownbiadetails
    {
        public int ID { get; set; }
        public int ProcessID { get; set; }
        public string? ProcessName { get; set; }
        public int BIAID { get; set; }
        public int InBondAppID { get; set; }
        public int OutBondAppID { get; set; }
        public int RelationID { get; set; }
        public string? InBound { get; set; }
        public string? DataSource { get; set; }
        public int IsRealTime { get; set; }
        public string? AreaofFocus { get; set; }
        public string? CriticalForBCM { get; set; }
        public string? BCMScope { get; set; }
        public string? Supported { get; set; }
        public string? RTO { get; set; }
        public int IsCritical { get; set; }
        public int IsDRRequired { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public int ChangedBy { get; set; }
        public int QuestionID { get; set; }
        public int RelTypeID { get; set; }
        public int ChildRelTypeID { get; set; }
        public int ChildImpactSevID { get; set; }
        public string? Question { get; set; }
        public int IsComplete { get; set; }

        //Added by Shubham B
        public int ImpactSev1 { get; set; }
        public int ImpactSev2 { get; set; }
        public int ImpactSev3 { get; set; }

    }


    public class ProcessRelation
    {
        public string? ID { get; set; }
        public string? ParentAppID { get; set; }
        public string? ChildAppID { get; set; }
        public string? DirectionID { get; set; }
        public string? RelationTypeID { get; set; }
        public string? ChildRelTypeID { get; set; }
        public string? AppDetailsID { get; set; }
        public string? CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? ChangedAt { get; set; }
        public string? ChangedBy { get; set; }
    }

    #region SearchFields
    public class SearchFields : BusinessProcessInfo
    {
        public string? rbtnId { get; set; }
        public string? searchstring { get; set; }
        public string? IsBack { get; set; }
        public string? Type { get; set; }
        public string? ResourceRole { get; set; }
        public string? Threat { get; set; }
        public string? RiskTreatmentOption { get; set; }
    }

    #endregion

    #region ViewBCMComplianceColl


    public class ViewBCMCompliances : UserActivityStatus
    {
        public int RTO { get; set; }


        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public int ApproverID { get; set; }
        public string? OwnerMobile { get; set; }
        public string? ApproverName { get; set; }
        public string? Iscritical { get; set; }
        public string? OwnerEmail { get; set; }
        public string? ApproverMobile { get; set; }
        public string? ApproverEmail { get; set; }
        public string? BCMEntityname { get; set; }
        public string? ProcessName { get; set; }
        public int ProcessID { get; set; }
        public string? orgName { get; set; }
        public string? UnitName { get; set; }
        public string? DeptName { get; set; }
        public string? PCIscore { get; set; }


    }



    #endregion
    #region rssfeedsettings


    public class RSSFeedSettings : UserActivityStatus
    {
        public int ID { get; set; }


        public int OrgID { get; set; }
        public string? OrgName { get; set; }
        public string? SearchKey { get; set; }

        public string? MailTo { get; set; }
        public string? MailToTeams { get; set; }

        public string? UnitName { get; set; }

        public int IsActive { get; set; }
        public int CreatedBy { get; set; }
        public string? CreatedAt { get; set; }

        public string? ChangedAt { get; set; }
        public string? ChangedBy { get; set; }
    }



    #endregion rssFeedSettings

    #region rssfeedmaster

    public class rssfeedmaster
    {
        public string? OrgID { get; set; }
        public string? Title { get; set; }
        public string? Feedurl { get; set; }
        public int IsActive { get; set; }
        public int ID { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }


    #endregion

    #region rssTransatcion


    public class Rsstransaction : UserActivityStatus
    {
        public int ID { get; set; }

        public string? Title { get; set; }
        public string? Summary { get; set; }
        public string? Authers { get; set; }

        public string? PublishDate { get; set; }
        public string? CreateDate { get; set; }

        public string? SearchKeys { get; set; }

        public string? SearchKeysFound { get; set; }
        public string? MailSentTo { get; set; }
        public int FeedID { get; set; }

        public string? FailedMailIDs { get; set; }
        public string? FeedItemID { get; set; }
    }


    #endregion rssTransatcion

    public class ViewAllFacilitiesComplaint : UserActivityStatus
    {
        public int RTO { get; set; }


        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public int ApproverID { get; set; }
        public string? OwnerMobile { get; set; }
        public string? ApproverName { get; set; }
        public string? Iscritical { get; set; }
        public string? OwnerEmail { get; set; }
        public string? ApproverMobile { get; set; }
        public string? ApproverEmail { get; set; }
        public string? BCMEntityname { get; set; }
        public string? ProcessName { get; set; }
        public int ProcessID { get; set; }
        public string? orgName { get; set; }
        public string? UnitName { get; set; }
        public string? DeptName { get; set; }
        public string? PCIscore { get; set; }


    }

    #region BCM.CVWebAPI
    public class ManageUserLoginDetails
    {
        public string? Username { get; set; }
        public string? Password { get; set; }
        public string? OrgCode { get; set; }
    }

    public class ResetNewPassword
    {
        public string? EmailID { get; set; }
        public string? NewPassword { get; set; }
        public string? ConfirmNewPassword { get; set; }
    }

    public class ResetPassword
    {
        public string? OldPassword { get; set; }
        public string? NewPassword { get; set; }
        public string? ConfirmNewPassword { get; set; }
        public int UserID { get; set; }
        public int OrgID { get; set; }
    }

    public class ForgetPassword
    {
        public string? EmailID { get; set; }
        public string? UserName { get; set; }

        public string? Password { get; set; }
        public string? NewPassword
        {
            get; set;
        }
    }
    public class IncidentTimeline
    {
        public int IncidentID { get; set; }
        public int disasterId { get; set; }
    }
    #endregion


    #region BCMTraining

    public class BCMTrainingMaster
    {
        public string? ID;
        public int TrainingMasterID;
        public string? TrainingName;
        public int OrgID;
        public int IsCorrect;
        public int UnitID;
        public int DepartmentID;
        public int OwnerID;
        public int ApproverID;
        public string? AttachmentName;
        public string? ValidityDate;
        public string? RevieweDate;
        public string? AttachmentID;
        public int Status;
        public int CreatedBy;
        public int CreatedAt;
        public int UpdatedBy;
        public int UpdatedAt;
        public int NotifiedByName;
        public string? QuestionText;
        public int IsAnswer;
        public int UserID;
        public int TrainingID;
        public int TrainingCompleted;
        public int Attempt;
        public int IsPassed;
        public string? AllocatedQuestionID;
        public int IsActive;
        public int QuestionmasterID;
        public string? OptionText;
        public string? OwnerName;
        public string? OwnerEmail;
        public string? ApproverName;
        public string? ApproverEmail;
        public string? ApproverNumber;
        public string? OwnerNumber;
        public string? OrgName;
        public string? UnitName;
        public string? DepartmentName;
        public string? EntityType;
        public string? TrainingDuration;
        public string? Version;
        public string? TrainingCode;
        public string? Marks;
        public string? TotalQuestions;
        public int iTotalQuestions;
        public string? AttemptedQuestions;
        public string? CorrectAttempted;
        public string? Result;
        public int iResult;
        public int IsEffective;
        public int TrainingScore;
        public DateTime ExamStartTime;
        public DateTime ExamEndTime;
        public string? SuccessFullAttempt;
        public string? FailedAttempt;
        public string? UserExamMasterID;
        public string? QuestionID;
        public string? OptionID;
        public string? IsSelected;
        public string? TotalTime;
        public string? TotalQuestionCount;
        public string? FailedQstnAttempted;
        public string? AttemptedQuestionCount;
        public string? CorrectAttemptedQstnCount;
        public string? TStatus;

        public string? Purpose;
        public int Frequency;
        public int Priority;
        public string? ActivationModeMobile;
        public string? MobileVerified;
        public string? MobileVerifiedTooltip;
        public string? ActivationModeEmail;
        public string? EmailVerified;
        public string? EmailVerifiedTooltip;
        public int ReleaseToVendore;
        public int PublishID;
        public DateTime PublishDate;

        public string? AttachemntName;
    }


    public class BCMTrainingMasterColl : CollectionBase //, System.Collections.Generic.IBCPEnumerable<Compliance.BusinessClasses.TaskGroup >
    {
        public void New()
        {
        }
        public void Add(BCMTrainingMaster notificationMaster)
        {
            base.InnerList.Add(notificationMaster);
        }
        public OrganizationMaster this[int index]
        {
            get
            {
                return (OrganizationMaster)base.InnerList[index];
            }
            set
            {
                base.InnerList[index] = value;
            }
        }
    }

    #endregion

    #region RISK Assessment
    public class RiskItem
    {
        public string? Item { get; set; }

        public string? ID { get; set; }
        public string? ParentID { get; set; }
        public string? ChangedOn { get; set; }
        public string? Responsibility { get; set; }
        public string? NxtRevDate { get; set; }
        public string? Unit { get; set; }
        public string? Depart { get; set; }
        public string? SubDept { get; set; }
        public string? Org { get; set; }
        public string? RiskTo { get; set; }
        public string? RiskRating { get; set; }
        public string? RiskDescription { get; set; }
        public string? RiskCategory { get; set; }
        public string? RiskType { get; set; }
        public string? IsEffective { get; set; }
        public string? Impact { get; set; }
        public string? Likelihood { get; set; }
        public string? IRiskID { get; set; }
        public string? ImageURL { get; set; }
        public string? OrgId { get; set; }
        public string? UnitID { get; set; }
        public string? DepartmentId { get; set; }
        public string? SubFunctionID { get; set; }
        public string? AltUnitHeadId { get; set; }
        public string? UnitBCPCorId { get; set; }
        public string? AltBCPCorId { get; set; }
        public string? DeptHeadId { get; set; }
        public string? AltDeptHeadId { get; set; }
        public string? SubFunOwnerId { get; set; }
        public string? AltSubFunOwnerId { get; set; }
        public string? RiskOwner { get; set; }
        public string? RiskItemCategory { get; set; }
        public string? ProcessID { get; set; }
        public string? RiskName { get; set; }
        public string? ResidualImpact { get; set; }
        public string? ResidualLikeliHood { get; set; }
        public string? ResidualRiskRating { get; set; }
        public string? ResidualRiskRatingName { get; set; }
        public string? RiskTreatmentStrategyAction { get; set; }
        public string? Version { get; set; }
        public string? RiskEntryDate { get; set; }
        public string? IncidentTypeName { get; set; }
        public string? LastRevDate { get; set; }
        public string? RiskCloseStatus { get; set; }
        public string? Status { get; set; }
        public string? StatusID { get; set; }
        public string? EntityTypeID { get; set; }

        public string? SubRiskRegID { get; set; }
        public string? DisasterName { get; set; }

        public string? ApproverName { get; set; }

        public string? ApproverEmail { get; set; }

        public string? ApproverMobile { get; set; }

        public string? RiskOwnerMobile { get; set; }

        public string? RiskOwnerEmail { get; set; }
    }

    #endregion

    #region MomItemCount

    #endregion

    #region Frequency & Years

    public class FrequencyEnums
    {
        public int ID { get; set; }
        public string FrequencyName { get; set; }
    }
    #endregion

    #endregion

    #region API Configuration
    public class APIConfiguration
    {
        // API Basic Details
        public string ID { get; set; }
        public string APIName { get; set; }
        public string APIType { get; set; }
        public string BaseURL { get; set; }
        public string EndpointPath { get; set; }
        public string FullURL { get; set; }
        public bool IsActive { get; set; }

        // Authentication Details
        public string AuthType { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string APIKeyName { get; set; }
        public string APIKeyValue { get; set; }
        public string TokenURL { get; set; }
        public string ClientID { get; set; }
        public string ClientSecret { get; set; }
        public string BearerToken { get; set; }

        // Request Settings
        public List<KeyValuePair<string, string>> Headers { get; set; }
        public List<KeyValuePair<string, string>> QueryParameters { get; set; }
        public List<KeyValuePair<string, string>> PathParameters { get; set; }
        public string ContentType { get; set; }
        public string RequestBody { get; set; }

        // Response Handling
        public string SuccessKeyPath { get; set; }
        public string ExpectedStatusCodes { get; set; }
        public string ErrorKeyPath { get; set; }
        public string ResponseFormat { get; set; }
        public int ResponseTimeout { get; set; }
        public int RetryAttempts { get; set; }
        public string CreatedBy { get; set; }
        public List<ResponseFieldMapping> ResponseFieldMappings { get; set; }
    }

    public class ResponseFieldMapping
    {
        public string SourceField { get; set; }
        public string TargetField { get; set; }
    }
    #endregion


    #region CP Workflow Service Status Response Models

    /// <summary>
    /// Response model for SusanAI workflow service status API
    /// </summary>
    public class WorkflowServiceStatusResponse
    {
        public bool Success { get; set; }
        public List<string> ActiveNodes { get; set; } = new List<string>();
        public List<string> InActiveNodes { get; set; } = new List<string>();
        public string? Message { get; set; }
    }

    /// <summary>
    /// Model representing a CP Profile option for dropdown selection
    /// </summary>
    public class CPProfileOption
    {
        public string Value { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
    }

    /// <summary>
    /// Model representing a workflow profile from SusanAI service
    /// </summary>
    public class WorkflowProfile
    {
        public string? Id { get; set; }
        public string profileName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Configuration settings for SusanAI workflow service integration
    /// </summary>
    public class SusanAIWorkflowConfig
    {
        public const string ServerHost = "************";
        public const int Port = 8000;
        public const string Endpoint = "api/SusanAI/check-workflow-service-status";
        public const string ApiKey = "pgH7QzFHJx4w46fI~5Uzi4RvtTwlEXp";
        public const int TimeoutSeconds = 30;

        public static string FullUrl => $"https://{ServerHost}:{Port}/{Endpoint}";

        /// <summary>
        /// Default CP Profile options when service is unavailable
        /// </summary>
        public static readonly List<CPProfileOption> DefaultOptions = new()
        {
            new CPProfileOption { Value = "0", Text = "-- Select --" },
            new CPProfileOption { Value = "1", Text = "CP1 (Offline)" },
            new CPProfileOption { Value = "2", Text = "CP2 (Offline)" }
        };
    }

    #endregion Workflow Service Status Response Models


}





