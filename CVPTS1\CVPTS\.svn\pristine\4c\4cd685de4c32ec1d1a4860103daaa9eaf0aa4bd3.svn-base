using BCM.BusinessClasses;
using BCM.Security.Helper;
using BCM.UI.Models;
using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text;

namespace CVBCM.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILoggerFactory _loggerFactory;
        private readonly ProcessSrv _processSrv;

        public HomeController(ILoggerFactory loggerFactory, ProcessSrv processSrv)
        {
            _loggerFactory = loggerFactory;
            _processSrv = processSrv;
        }

        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        [HttpPost]
        public IActionResult Index(string LoginName, string LoginCode, string UserType)
        {
            try
            {
                if(LoginName == null || UserType == null) 
                {
                    //CVLogger.CVCore_LogsInfo("Please enter credentails for login");
                }
                else 
                {
                    var loginDetails = _processSrv.GetManageUserByLoginNameAndOrgID(LoginName, LoginCode, UserType);

                    if(loginDetails == null)
                    {
                       // CVLogger.CVCore_LogsInfo("Please enter correct credentials for login");
                    }
                    else
                    {
                        //CVLogger.CVCore_LogsInfo("You have successfully login on CV App");
                    }
                }
            }
            catch (System.Exception)
            {
               
            }
            return View();
        }
    }
}