﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc;
using System.Xml.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using static System.Runtime.InteropServices.JavaScript.JSType;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMAdministration.Controllers;

[Area("BCMAdministration")]
public class OrglevelDepDiagramController : BaseController
{
    private readonly Utilities _Utilities;
    ManageUsersDetails? _UserDetails = new ManageUsersDetails();
    private ProcessSrv _ProcessSrv;
    readonly CVLogger _CvLogger;

    public OrglevelDepDiagramController(Utilities Utilities, ProcessSrv iProcessSrv, CVLogger cVLogger, BCMMail BCMMail) : base(Utilities)
    {
        _ProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _CvLogger = cVLogger;
    }
    [HttpGet]
    public IActionResult OrglevelDepDiagram()
    {
        try
        {
            // Get the data and pass it to the view
            var chartData = OrglevelDepDiagramData().Value;
            ViewBag.ChartData = chartData;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            // Create a simple fallback data structure in case of error
            ViewBag.ChartData = new List<object>
            {
                new
                {
                    name = "Error loading data",
                    fill = "#1B75BD",
                    error = true,
                    level = "red",
                    children = new List<object>()
                }
            };
        }

        return View();
    }


    // Helper class for chart node data
    private class ChartNode
    {
        public string name { get; set; }
        public string fill { get; set; }
        public bool error { get; set; }
        public string level { get; set; }
        public List<ChartNode> children { get; set; } = new List<ChartNode>();

        // Optional property for business processes
        public string subname { get; set; }
    }

    // Method to get business processes for a sub-department
    private List<ChartNode> GetBusinessProcesses(string subDepartmentId)
    {
        var businessProcesses = new List<ChartNode>();

        try
        {
            // Try to get real business processes from the database
            List<BusinessProcessInfo> realBusinessProcesses = GetRealBusinessProcesses(subDepartmentId);

            if (realBusinessProcesses != null && realBusinessProcesses.Any())
            {
                // Use real data if available
                realBusinessProcesses = realBusinessProcesses.Where(x => x.SubfunctionID == Convert.ToInt32(subDepartmentId)).ToList();
                foreach (BusinessProcessInfo bp in realBusinessProcesses)
                {

                    if (bp.SubfunctionID.ToString() == subDepartmentId)
                    {
                        businessProcesses.Add(new ChartNode
                        {
                            name = bp.ProcessName,
                            subname = "",
                            fill = bp.IsActive.ToString() == "1" ? "#008000" : "#FF8C00", // Green for active, orange for inactive
                            error = false,
                            level = "green",
                            children = new List<ChartNode>()
                        });
                    }
                    
                }
            }
            else
            {
                // Fallback to simulated data if no real data is available
                // Simulate 1-3 business processes per sub-department
                int count = new Random().Next(1, 4);
                for (int i = 1; i <= count; i++)
                {
                    businessProcesses.Add(new ChartNode
                    {
                        name = $"Business Process {i}",
                        subname = " ",
                        fill = "#008000", // Green for business processes
                        error = false,
                        level = "green",
                        children = new List<ChartNode>()
                    });
                }
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        return businessProcesses;
    }

    // Helper class for business process data
    private class BusinessProcess
    {
        public string Name { get; set; }
        public string Code { get; set; }
        public string Status { get; set; }
    }

    // Method to get real business processes from the database
    private List<BusinessProcessInfo> GetRealBusinessProcesses(string subDepartmentId)
    {
        try
        {

            //List<BCM.BusinessClasses.BusinessProcessInfo> lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);

            List<BusinessProcessInfo> lstBusinessProcess = _ProcessSrv.GetBusinessProcessMasterList(_UserDetails.OrgID);
            //lstBusinessProcess = GetBusinessProcess(lstBusinessProcess);
            //if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            //{
            //    if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
            //    {
            //        lstBusinessProcess = _Utilities.FilterListByOrgGroupID(lstBusinessProcess, _UserDetails.OrgGroupID);

            //    }
            //    else
            //    {

            //        lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
            //        lstBusinessProcess = _Utilities.FilterListByRoleID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
            //    }
            //}
            // This is where you would make a call to your database or service
            // to get the actual business processes for the given sub-department

            // For now, we'll return null to use the simulated data
            // In a real implementation, you would replace this with actual data retrieval

            // Example of how you might retrieve data:
            // return _ProcessSrv.GetBusinessProcessesBySubDepartmentId(subDepartmentId);

            return lstBusinessProcess;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return null;
        }
    }

    public List<BusinessProcessInfo> GetBusinessProcess(List<BusinessProcessInfo> lstBusinessProcess)
    {
        try
        {
            lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
            if (_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole))
            {
                lstBusinessProcess = _Utilities.GetBusinessProcess(lstBusinessProcess, 0, 0, 0, 0, -1);
            }
            else
            {
                lstBusinessProcess = _Utilities.FilterListForOwner(lstBusinessProcess, 0, 0, 0, 0, Convert.ToInt32(BCPEnum.EntityType.BusinessProcess), -1);
            }

        }
        catch (Exception)
        {

            throw;
        }
        return lstBusinessProcess;
    }

    // Main method to generate the dependency matrix data
    public JsonResult OrglevelDepDiagramData()
    {
        var events = new List<object>();
        try
        {
            // Get organization data
            List<OrgInfo> lstOrgInfo = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());

            // Get units data
            //List<OrgUnit> lstOrgUnits = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(),_UserDetails.OrgID.ToString(),_UserDetails.UserRoleID.ToString());
            List<OrgUnit> lstOrgUnits = _ProcessSrv.GetOrganizationUnitList_New(_UserDetails.OrgID);
            // Get departments data
            //List<DepartmentInfo> lstDepartmentInfo = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString());
            
            List<DepartmentInfo> lstDepartmentInfo = _ProcessSrv.GetDepartmentMasterAll();
            // Get sub-departments data
            //List<SubFunction> lstSubFunction = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString());
            
            List<SubFunction> lstSubFunction = _ProcessSrv.GetSubFunctionList_ByOrgGroupID(_UserDetails.OrgGroupID);
            // Build the hierarchical data structure for the dependency matrix
            foreach (OrgInfo item in lstOrgInfo)
            {
                var unitsForOrg = new List<ChartNode>();
                if (item.Id == _UserDetails.OrgID.ToString())
                {
                    // Get units for this organization
                    foreach (OrgUnit orgUnit in lstOrgUnits.Where(u => u.OrgID.ToString() == item.Id))
                    {
                        var departmentsForUnit = new List<ChartNode>();

                        // Get departments for this unit
                        foreach (DepartmentInfo department in lstDepartmentInfo.Where(d => d.UnitID == orgUnit.UnitID))
                        {
                            var subDepartmentsForDept = new List<ChartNode>();

                            // Get sub-departments for this department
                            foreach (SubFunction subDept in lstSubFunction.Where(s => s.FunctionId == department.DepartmentID.ToString()))
                            {
                                // Get business processes for this sub-department
                                var businessProcesses = GetBusinessProcesses(subDept.SubFunctionID);

                                // Add sub-department to the list
                                subDepartmentsForDept.Add(new ChartNode
                                {
                                    name = subDept.SubFunctionName,
                                    fill = "#FF0000", // Red for sub-departments
                                    error = false,
                                    level = "red",
                                    children = businessProcesses
                                });
                            }

                            // Add department to the list
                            departmentsForUnit.Add(new ChartNode
                            {
                                name = department.DepartmentName,
                                fill = "#FB5A0C", // Orange for departments
                                error = false,
                                level = "green",
                                children = subDepartmentsForDept
                            });
                        }

                        // Add unit to the list
                        unitsForOrg.Add(new ChartNode
                        {
                            name = orgUnit.UnitName,
                            fill = "#FFB406", // Yellow for units
                            error = false,
                            level = "green",
                            children = departmentsForUnit
                        });
                    }

                    // Add organization to the main list
                    events.Add(new ChartNode
                    {
                        name = item.OrganizationName,
                        fill = "#1B75BD", // Blue for organizations
                        error = false,
                        level = "green",
                        children = unitsForOrg
                    });
                }
                
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);

            // Return a default structure if an error occurs
            events.Add(new ChartNode
            {
                name = "Error loading data",
                fill = "#1B75BD",
                error = true,
                level = "red"
            });
        }

        return Json(events);
    }
}

