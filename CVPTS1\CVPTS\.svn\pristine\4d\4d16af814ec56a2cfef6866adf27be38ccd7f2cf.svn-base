﻿@{
    ViewData["Title"] = "Data Set";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .dataset-editor {
        height: 130px;
        overflow: auto;
        border: 1px solid #C3C3C3;
        border-radius: 10px;
    }
</style>

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Dataset </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal">
            <i class="cv-Plus" title="Create New"></i>Data Set
        </button>
    </div>
</div>

<div class="Page-Condant card border-0 ">
    <table id="example" class="table table-hover" style="width:100%">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Name</th>
                <th>Description</th>
                <th style="max-width:60%;width:60%">Stored Query</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="log-table-body">
        </tbody>
    </table>

</div>



<div class="modal fade" id="AddModal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Dataset Configuration</h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-name"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Dataset Name" name="database" value="">
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Description" name="Description" value="">
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Schema Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-workflow-dashline"></i></span>
                                <select class="form-select form-control selectized">
                                    <option></option>
                                    <option>Name 1</option>
                                    <option>Name 2</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Table Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-calculated"></i></span>
                                <select class="form-select form-control selectized">
                                    <option></option>
                                    <option>Table 1</option>
                                    <option>Table 2</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <label class="form-label">Column Name</label>
                    <div class="col-5">
                        <div class="dataset-editor">
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="d-flex align-items-center justify-content-center flex-column gap-2 w-100">
                            <button class="btn btn-sm btn-outline-secondary w-50"><i class="cv-right-double-arrow fs-5"></i></button>
                            <button class="btn btn-sm btn-outline-secondary w-50"><i class="cv-right-arrow fs-5"></i></button>
                            <button class="btn btn-sm btn-outline-secondary w-50"><i class="cv-left-double-arrow fs-5"></i></button>
                            <button class="btn btn-sm btn-outline-secondary w-50"><i class="cv-left-arrow fs-5"></i></button>
                        </div>
                    </div>
                    <div class="col-5">
                        <div class="dataset-editor">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group field-touched">
                            <label class="form-label">Stored Query</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <textarea type="text" class="form-control" placeholder="New application virus name corona virus _covid" name="" value="" style="height:0px;max-height:100px;overflow:auto"></textarea>
                                <span class="input-group-text"><button class="btn btn-sm btn-primary ms-2">Run</button></span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary">Cancel</button>
                <button class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>
</div>







<script>
    document.addEventListener("DOMContentLoaded", function () {
      const tbody = document.getElementById("log-table-body");

      for (let i = 1; i <= 10; i++) {
        const tr = document.createElement("tr");

        tr.innerHTML = `
          <td>${i}</td>
          <td>Oracle Log Datase ${i}</td>
          <td>Oracle Log Dataset ${i}</td>
          <td title="Select * from [WAPT].[dbo].[oracle_monitor_logs_${i}]"><span class="d-inline-block text-truncate" style="max-width:60%;width:60%">Select * from [WAPT].[dbo].[oracle_monitor_logs_${i}] </span></td>
          <td>
            <div class="d-flex align-items-center gap-2">
              <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="#Modal" data-id="${i}">
                <i class="cv-edit" title="Edit"></i>
              </span>
              <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#Modal" data-id="${i}">
                <i class="cv-delete text-danger" title="Delete"></i>
              </span>
            </div>
          </td>
        `;

        tbody.appendChild(tr);
      }
    });
</script>