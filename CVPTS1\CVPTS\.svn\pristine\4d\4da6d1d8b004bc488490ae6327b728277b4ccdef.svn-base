﻿using BCM.UI.Areas.BCMReports.Controllers;
using BCM.UI.Areas.BCMReports.ReportModels.PCIReportModels;
using BCM.UI.Areas.BCMReports.ReportModels.ProcessRecovery_Report;
using DevExpress.ReportServer.ServiceModel.DataContracts;
using DevExpress.Web;
using DevExpress.XtraCharts;
using DevExpress.XtraReports.UI;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;

namespace BCM.UI.Areas.BCMReports.ReportTemplate
{
    public partial class ProcessRecoveryPriorityReport : DevExpress.XtraReports.UI.XtraReport
    {
        public List<ProcessRecoveryPriorityReportList> Reportdata = new();
        public List<ProcessRecoveryPriorityReportList> Category1 = new();
        public List<ProcessRecoveryPriorityReportList> Category2 = new();
        public List<ProcessRecoveryPriorityReportList> Category3 = new();
        public List<ProcessRecoveryPriorityReportList> Category4 = new();
        public List<CategoryMasterlist> CategoryMasterlistdata = new();
        public ProcessRecoveryPriorityReportVm reportJsonData = new();
        public ProcessRecoveryPriorityReport(string data)
        {
            InitializeComponent();
            reportJsonData = JsonConvert.DeserializeObject<ProcessRecoveryPriorityReportVm>(data);
            CategoryMasterlistdata = reportJsonData.CategoryMastersList;
            Reportdata = reportJsonData.ProcessRecoveryPriorityReportList;
            lblUserName.Text = reportJsonData?.LoginName?.ToString();
            xrLabel6.Text = CategoryMasterlistdata[0]?.Description?.ToString();
            xrLabel7.Text = CategoryMasterlistdata[1]?.Description?.ToString();
            xrLabel12.Text = CategoryMasterlistdata[2]?.Description?.ToString();
            xrLabel13.Text = CategoryMasterlistdata[3]?.Description?.ToString();
            xrLabel14.Text = CategoryMasterlistdata[0]?.CategoryRange?.ToString();
            xrLabel15.Text = CategoryMasterlistdata[1]?.CategoryRange?.ToString();
            xrLabel16.Text = CategoryMasterlistdata[2]?.CategoryRange?.ToString();
            xrLabel17.Text = CategoryMasterlistdata[3]?.CategoryRange?.ToString();
            var processnamesc1= new List<string>();
            var processnamesc2 = new List<string>();
            var processnamesc3 = new List<string>();
            var processnamesc4 = new List<string>();
            foreach (var report in Reportdata)
            {

                if (report.StatusID == 0)
                {
                    report.CategoryStatus = "Initiated";
                }
                else if (report.StatusID == 1)
                {
                    report.CategoryStatus = "Waiting For Approval";
                }
                else if (report.StatusID == 2)
                {
                    report.CategoryStatus = "Approved";
                }
                else
                {
                    report.CategoryStatus = "Rejected";
                }
                if (report.RTO == "1")
                {
                    xrPictureBox56.Visible = false;
                    processnamesc1.Add(report.ProcessName);
                    report.Category = "Category-I";
                    report.RTOText = "0 to 24 Hrs";
                    Category1.Add(report);
                }
                else if (report.RTO == "2")
                {
                    xrPictureBox59.Visible = false;
                    processnamesc2.Add(report.ProcessName);
                    report.Category = "Category-II";
                    report.RTOText = "1 to 5 Days";
                    Category2.Add(report);
                }
                else if (report.RTO == "3")
                {
                    xrPictureBox58.Visible = false;
                    processnamesc3.Add(report.ProcessName);
                    report.Category = "Category-III";
                    report.RTOText = "1 to 2 Weeks";
                    Category3.Add(report);
                }
                else
                {
                    xrPictureBox57.Visible = false;
                    processnamesc4.Add(report.ProcessName);
                    report.Category = "Category-IV";
                    report.RTOText = "2 Weeks Upto 1 Month";
                    Category4.Add(report);
                }

            }
            xrLabel22.Text = (processnamesc1 != null && processnamesc1.Any())? string.Join(", ", processnamesc1):"NA";
            xrLabel23.Text = (processnamesc2 != null && processnamesc2.Any()) ? string.Join(", ", processnamesc2) : "NA";
            xrLabel24.Text = (processnamesc3 != null && processnamesc3.Any()) ? string.Join(", ", processnamesc3) : "NA";
            xrLabel110.Text = (processnamesc4 != null && processnamesc4.Any()) ? string.Join(", ", processnamesc4) : "NA";
            this.DetailReport.DataSource = Category1; 
            this.DetailReport1.DataSource = Category2;
            this.DetailReport2.DataSource = Category3;
            this.DetailReport3.DataSource = Category4;
            if (Category1.Count == 0)
            {
                DetailReport.Visible= false;
            }
            if (Category2.Count == 0) 
            {
                DetailReport1.Visible= false;
            }
            if (Category3.Count == 0)
            {
                DetailReport2.Visible = false;
            }
            if (Category4.Count == 0)
            {
                DetailReport3.Visible = false;
            }

            this.DisplayName = "ProcessRecoveryPointReport_" + lblUserName.Text.ToString() + "_" + DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss tt");

        }
        private void BarChartBeforePrintEvent(object sender, CancelEventArgs e)
        {
            CategoryMasterlistdata = reportJsonData.CategoryMastersList;
            var category1 = Category1.Count;
            var category2= Category2.Count;
            var category3 = Category3.Count;
            var category4 = Category4.Count;

            Series series = new Series("Series", ViewType.Bar);
            xrChart1.Series.Add(series);
            xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            series.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            series.DataSource = CreateBarChartData(category1, category2, category3, category4);
            series.ArgumentScaleType = ScaleType.Auto;
            series.ArgumentDataMember = "Argument";
            series.ValueScaleType = ScaleType.Numerical;
            series.ValueDataMembers.AddRange(new string[] { "ValueUp" });
            //series.Label.TextPattern = "{V}";
            //series.Label.TextAlignment = StringAlignment.Near;

            SideBySideBarSeriesView curv = series.View as SideBySideBarSeriesView;
            curv.BarWidth = 0.4;

            BarSeriesView view = (BarSeriesView)series.View;
            view.BarWidth = 0.2;

            //chart bar color
            xrChart1.CustomDrawSeriesPoint += (s, e) =>
            {
                string arg = e.SeriesPoint.Argument;

                if (arg == CategoryMasterlistdata[0]?.Description?.ToString())
                    e.SeriesDrawOptions.Color = ColorTranslator.FromHtml("#2F86EB");
                else if (arg == CategoryMasterlistdata[1]?.Description?.ToString())
                    e.SeriesDrawOptions.Color = ColorTranslator.FromHtml("#9359A9");
                else if (arg == CategoryMasterlistdata[2]?.Description?.ToString())
                    e.SeriesDrawOptions.Color = ColorTranslator.FromHtml("#47BB49");
                else if (arg == CategoryMasterlistdata[3]?.Description?.ToString())
                    e.SeriesDrawOptions.Color = ColorTranslator.FromHtml("#FAE634");
            };

            //xrChart2.Series.AddRange(new Series[] { series });
        }
        private DataTable CreateBarChartData(int Category1, int Category2, int Category3, int Category4)
        {
            CategoryMasterlistdata = reportJsonData.CategoryMastersList;
            DataTable table = new DataTable("Table1");
            try
            {
                string format = @"[^0-9\\./]";
                table.Columns.Add("Argument", typeof(string));
                table.Columns.Add("ValueUp", typeof(float));
                Random rnd = new Random();
                if (Category1 > 0)
                {
                    table.Rows.Add(CategoryMasterlistdata[0]?.Description?.ToString(), Category1);
                }
                if (Category2 > 0)
                {
                    table.Rows.Add(CategoryMasterlistdata[1]?.Description?.ToString(), Category2);
                }
                if (Category3 > 0)
                {
                    table.Rows.Add(CategoryMasterlistdata[2]?.Description?.ToString(), Category3);
                }
                if (Category4 > 0)
                {
                    table.Rows.Add(CategoryMasterlistdata[3]?.Description?.ToString(), Category4);
                }

                
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex.Message.ToString());
            }
            return table;
        }
    }
}
