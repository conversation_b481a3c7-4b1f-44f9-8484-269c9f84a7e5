﻿@model IEnumerable<BCM.BusinessClasses.RiskTrendInfo>
@using BCM.Shared
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Condant card border-0" id="tblData">
    <table class="table table-hover data-table" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Risk&nbsp;Code</th>
                <th>Risk&nbsp;Title</th>
                <th>Risk&nbsp;Owner</th>
                <th>Residual&nbsp;Risk&nbsp;Severity</th>
                <th>Trend</th>
                <th>Reason&nbsp;for&nbsp;Update</th>
                <th>Improvement&nbsp;Required</th>
                <th>Improvement&nbsp;Status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="tblBody">
            @if (Model != null)
            {
                int iIndex = 0;
                foreach (var item in Model)
                {
                    item.ReasonforUpdate = string.IsNullOrEmpty(item.ReasonforUpdate) ? "NA" : item.ReasonforUpdate;
                    iIndex++;
                    <tr>
                        <td>@iIndex</td>
                        <td>
                            <span>
                                <label>@item.RiskCode</label>                                
                            </span>
                        </td>
                        <td>
                            <span style="display:none">
                                <label>@item.RiskCode</label>
                                &nbsp; (V : @item.RiskVersion)
                            </span>
                            <br />
                            <label> @item.RiskName (V : @item.RiskVersion)</label>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                        <td> : </td>
                                        <td>@item.OwnerName</td>
                                    </tr>
                                    <tr style="display:none">
                                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                                        <td>:</td>
                                        <td>@item.OwnerEmail</td>
                                    </tr>
                                    <tr style="display:none">
                                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                                        <td>:</td>
                                        <td>@item.OwnerMobile</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        @if (@item.ResidualRiskSeverity.Contains("High"))
                        {
                            <td class="text-danger"><i class="cv-High mx-1 fw-bold"></i> @item.ResidualRiskSeverity</td>
                        }
                        else @if (@item.ResidualRiskSeverity.Contains("Low"))
                        {
                            <td class="text-success"><i class="cv-low mx-1 fw-bold"></i> @item.ResidualRiskSeverity</td>
                        }
                        else @if (@item.ResidualRiskSeverity.Contains("Medium"))
                        {
                            <td class="text-warning"><i class="cv-medium mx-1 fw-bold"></i> @item.ResidualRiskSeverity</td>
                        }
                        else @if (@item.ResidualRiskSeverity.Contains("NA"))
                        {
                            <td class="text-info"><i class="cv-na mx-1 fw-bold"></i> @item.ResidualRiskSeverity</td>
                        }
                        else
                        {
                            <td></td>
                        }

                        @if (@item.RiskTrendStatus == 0)
                        {
                            <td>
                                <i class="cv-no-change text-bg-light" title="No Change"></i>
                            </td>
                        }
                        else if (@item.RiskTrendStatus == 1)
                        {
                            <td>
                                <i class="cv-upward text-danger" title="Upward"></i>
                            </td>
                        }
                        else if (@item.RiskTrendStatus == 2)
                        {
                            <td>
                                <i class="cv-downward text-success" title="Downward"></i>
                            </td>
                        }
                        <td>@item.ReasonforUpdate</td>
                        <td>@item.ImprovementRequiredText</td>
                        <td>@item.ImprovementStatusText</td>
                        <td>
                            @* data-bs-target="#CreateModal" *@
                            @* <a asp-action="AttachActionsToRisk" asp-controller="AttachActionsToRisk" asp-area="BCMCorrectivePreventiveActions">
                                </a> *@
                            @if (item.TreatmentPlan == "1")
                            {
                                <span class="btn-action" type="button">
                                    <i class="cv-attach me-1 btnAttach" data-bs-toggle="modal"
                                       data-risk-sub-id="@item.RiskSubRegID" data-id="@item.RiskID" data-trend="1" data-risk-entity-id="@Convert.ToInt32(BCPEnum.RiskEntityID.RiskTrend)"
                                       data-risk-name="@item.RiskName" data-risk-code="@item.RiskCode" data-version="@item.RiskVersion"></i>
                                </span>
                            }
                            else
                            {
                                <span><i class="cv-na"></i></span>
                            }
                            <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                        </td>
                    </tr>
                }
            }
        </tbody>
    </table>
</div>