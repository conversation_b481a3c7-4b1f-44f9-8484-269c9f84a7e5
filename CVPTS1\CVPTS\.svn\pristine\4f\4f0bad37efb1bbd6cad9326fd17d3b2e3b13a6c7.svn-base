﻿@model BCM.BusinessClasses.ManageUsersDetails

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<form asp-action="ActiveManageUsers" method="post">  
        <div class="row row-cols-2">
             <div class="form-group" hidden>
                <label for="validationCustom01" class="form-label">UserID</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-login-code"></i></span>
                    <input type="hidden" class="form-control" asp-for="UserID">
                </div>               
            </div>
            <div class="form-group">
                <label class="form-label">User Name : <span class="fw-bold">@Model.UserName</span></label>
            </div>
            <div class="form-group">
                <label class="form-label">Current Status of User</label>
                <div class="input-group gap-3 align-items-center">
                    <span class="input-group-text"><i class="cv-questions"></i></span>
                    <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="LoginIsActive" id="activerdo" value="1" asp-for="LoginIsActive" checked="checked">
                        <label class="form-check-label" for="inlineRadio1">Active</label>
                    </div>
                    <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="LoginIsActive" id="inactiverdo" value="0" asp-for="LoginIsActive">
                        <label class="form-check-label" for="inlineRadio2">InActive</label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Date Of Activation of User</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-holiday-date"></i></span>
                    <input type="date" class="form-control" asp-for="DateOfActivation" />
                </div>
            </div>
             <div class="form-group">
                <label class="form-label">Date of Deactivation of User</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-holiday-date"></i></span>
                    <input type="date" class="form-control" asp-for="DateOfDeactivation" />
                </div>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
            <div>
                <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>    
</form>

@* @section Scripts {
    <script>
        $(document).ready(function () {


            $(document).ready(function () {
                $('input:radio[name=loginstatus]:checked').change(function () {
                    if ($("input[name='loginstatus']:checked").val() == 'activerdo') {   
                        alert("Active");
                    }
                    if ($("input[name='loginstatus']:checked").val() == 'inactiverdo') {
                        alert("InActive");
                    }
                });
            });
        });
    </script>
} *@