﻿// Main Dashboard Application

$(document).ready(function () {
    // Initialize the dashboard
    initializeDashboard();

    // Event Listeners
    setupEventListeners();

    // Load saved dashboard or default layout
    loadDashboard();

    // Initialize amCharts after everything is loaded
    setTimeout(() => {
        window.WidgetManager.initializeAllAmCharts();
    }, 2000);
});




function initializeDashboard() {
    console.log('Initializing Dashboard Builder...');

    // Show loading overlay
    $('#loadingOverlay').removeClass('d-none');

    // Initialize Gridstack
    window.WidgetManager.initializeGrid();

    // Initialize components
    window.DashboardStorage.updateUndoRedoButtons();

    // Hide loading overlay after initialization
    setTimeout(() => {
        $('#loadingOverlay').addClass('d-none');
    }, 1000);
}

function setupDropdownToggle() {
    console.log('Setting up SIMPLE dropdown functionality...');

    // Remove all existing dropdown handlers
    $(document).off('click.dropdown');
    $('.dropdown-toggle').off('click');

    // Create a completely custom dropdown system
    $('.dropdown-toggle').on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('SIMPLE DROPDOWN CLICKED!');
        const buttonText = $(this).text().trim();
        console.log('Button:', buttonText);

        // Close all dropdowns first
        $('.custom-dropdown-menu').remove();

        // Create dropdown menu dynamically
        let menuItems = '';

        if (buttonText.includes('Add Widget')) {
            menuItems = `
              <div class="dropdown-section" id="CustomWidget">
                    <div class="dropdown-header">Custom Widgets</div>
                    <a href="#" class="dropdown-item" data-widget="amchart-line">📈 Line Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-column">📊 Column Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-pie">🥧 Pie Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-radar">🎯 Radar Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-area">📈 Area Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-scatter">⚪ Scatter Chart</a>
                </div>
                <div class="dropdown-section">
                    <div class="dropdown-header">amCharts Widgets</div>
                    <a href="#" class="dropdown-item" data-widget="amchart-line">📈 Line Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-column">📊 Column Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-pie">🥧 Pie Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-radar">🎯 Radar Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-area">📈 Area Chart</a>
                    <a href="#" class="dropdown-item" data-widget="amchart-scatter">⚪ Scatter Chart</a>
                </div>
                <div class="dropdown-divider"></div>
                <div class="dropdown-section">
                    <div class="dropdown-header">Basic Widgets</div>
                    <a href="#" class="dropdown-item" data-widget="kpi">📊 KPI Card</a>
                    <a href="#" class="dropdown-item" data-widget="text">📝 Text Block</a>
                    <a href="#" class="dropdown-item" data-widget="progress">📊 Progress Bar</a>
                    <a href="#" class="dropdown-item" data-widget="table">📋 Data Table</a>
                </div>
                <div class="dropdown-divider"></div>
                <div class="dropdown-section">
                    <div class="dropdown-header">Advanced Widgets</div>
                    <a href="#" class="dropdown-item" data-widget="weather">🌤️ Weather Widget</a>
                    <a href="#" class="dropdown-item" data-widget="calendar">📅 Calendar</a>
                    <a href="#" class="dropdown-item" data-widget="clock">🕐 Digital Clock</a>
                    <a href="#" class="dropdown-item" data-widget="gauge">⚡ Gauge Chart</a>
                    <a href="#" class="dropdown-item" data-widget="map">🗺️ Map Widget</a>
                    <a href="#" class="dropdown-item" data-widget="news">📰 News Feed</a>
                    <a href="#" class="dropdown-item" data-widget="social">📱 Social Media</a>
                </div>
            `;
        } else if (buttonText.includes('Export')) {
            menuItems = `
                <a href="#" class="dropdown-item" onclick="exportDashboard()">💾 Export Dashboard</a>
                <a href="#" class="dropdown-item" onclick="importDashboard()">📁 Import Dashboard</a>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item" onclick="window.WidgetManager.clearDashboard()">🗑️ Clear Dashboard</a>
            `;
        }

        if (menuItems) {
            // Get button position
            const buttonOffset = $(this).offset();
            const buttonHeight = $(this).outerHeight();

            // Create dropdown menu
            const $menu = $(`
                <div class="custom-dropdown-menu" style="
                    position: fixed;
                    top: ${buttonOffset.top + buttonHeight + 5}px;
                    left: ${buttonOffset.left}px;
                    z-index: 9999;
                    background: white;
                    border: 2px solid #007bff;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    min-width: 250px;
                    max-height: 400px;
                    overflow-y: auto;
                    padding: 8px 0;
                ">
                    ${menuItems}
                </div>
            `);

            // Add to body
            $('body').append($menu);

            console.log('Custom dropdown created and added to body');

            // Handle menu item clicks
            $menu.find('.dropdown-item').on('click', function (e) {
                e.preventDefault();
                const widgetType = $(this).data('widget');
                const widgetTitle = $(this).text().trim();

                console.log('Menu item clicked:', widgetType, widgetTitle);

                if (widgetType) {
                    // Create widget
                    try {
                        window.WidgetManager.createWidget(widgetType, widgetTitle);
                        console.log('Widget created successfully');
                    } catch (error) {
                        //console.error('Error creating widget:', error);
                    }
                }

                // Close dropdown
                $('.custom-dropdown-menu').remove();
            });
        }
    });

    // Close dropdown when clicking outside
    $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown-toggle, .custom-dropdown-menu').length) {
            $('.custom-dropdown-menu').remove();
        }
    });

    console.log('Simple dropdown setup complete');
}

function setupEventListeners() {
    console.log('Setting up event listeners...');

    // Initialize dropdown functionality
    setupDropdownToggle();

    // Add test functions to global scope for debugging
    window.testDropdown = function () {
        console.log('=== DROPDOWN TEST ===');
        console.log('Dropdown toggles found:', $('.dropdown-toggle').length);
        $('.dropdown-toggle').each(function (i, el) {
            console.log(`Toggle ${i}:`, $(el).text().trim());
        });
        console.log('Dropdown menus found:', $('.dropdown-menu').length);
        $('.dropdown-menu').each(function (i, el) {
            console.log(`Menu ${i}:`, $(el).find('.dropdown-item').length, 'items');
        });
    };

    window.forceShowDropdown = function () {
        console.log('=== FORCE SHOW DROPDOWN ===');
        const $menu = $('.dropdown-menu').first();
        if ($menu.length > 0) {
            $menu.addClass('show').css({
                'display': 'block !important',
                'position': 'absolute',
                'top': '100%',
                'left': '0',
                'z-index': '9999',
                'background-color': 'white',
                'border': '2px solid #007bff',
                'min-width': '250px',
                'visibility': 'visible',
                'opacity': '1'
            });
            console.log('Dropdown forced to show');
        } else {
            console.log('No dropdown menu found');
        }
    };

    console.log('Type testDropdown() or forceShowDropdown() in console to debug');

    // Close dropdown when clicking outside
    $(document).off('click.dropdown').on('click.dropdown', function (e) {
        if (!$(e.target).closest('.dropdown').length) {
            $('.dropdown-menu').removeClass('show').removeAttr('style');
            $('.dropdown').removeClass('show');
            console.log('Dropdown closed by outside click');
        }
    });

    // Close dropdown when clicking on menu items
    $('.dropdown-menu').on('click', 'a', function (e) {
        console.log('Dropdown menu item clicked');
        $('.dropdown-menu').removeClass('show').removeAttr('style');
        $('.dropdown').removeClass('show');
    });

    // Add Widget Menu
    $('.dropdown-menu a[data-widget]').on('click', function (e) {
        e.preventDefault();
        console.log('Add Widget clicked!');

        const widgetType = $(this).data('widget');
        const widgetTitle = $(this).text().trim();

        console.log('Widget Type:', widgetType);
        console.log('Widget Title:', widgetTitle);
        console.log('WidgetManager available:', typeof window.WidgetManager);

        if (!window.WidgetManager) {
          //  console.error('WidgetManager not available!');
           // alert('WidgetManager not initialized. Please refresh the page.');
            return;
        }

        if (!widgetType) {
          //  console.error('Widget type is empty!');
          //  alert('Invalid widget type selected.');
            return;
        }

        try {
            // Create widget with auto-positioning
            console.log('Creating widget...');
            const result = window.WidgetManager.createWidget(widgetType, widgetTitle);
          //  console.log('Widget creation result:', result);

            if (result) {
               // console.log('Widget created successfully');
                // Close dropdown after successful creation
                $('.dropdown-menu').removeClass('show');
            } else {
              //  console.error('Widget creation failed');
               // alert('Failed to create widget. Please try again.');
            }
        } catch (error) {
           // console.error('Error creating widget:', error);
           // alert('Error creating widget: ' + error.message);
        }
    });

    // Undo/Redo buttons
    $('#undoBtn').on('click', () => window.DashboardStorage.undo());
    $('#redoBtn').on('click', () => window.DashboardStorage.redo());

    // Copy/Paste buttons
    $('#copyBtn').on('click', function () {
        if (window.WidgetManager.selectedWidget) {
            window.DashboardStorage.copyWidget(window.WidgetManager.selectedWidget);
        } else {
            window.DashboardStorage.showNotification('Please select a widget to copy', 'info');
        }
    });

    $('#pasteBtn').on('click', () => window.DashboardStorage.pasteWidget());

    // Save/Load buttons
    $('#saveBtn').on('click', () => {

        SaveDashboardBuilder()

    });
    $('#loadBtn').on('click', loadDashboard);

    // Preview Mode Toggle
    $('#previewMode').on('change', function () {
        const isPreview = $(this).is(':checked');
        togglePreviewMode(isPreview);
    });

    // Click outside to deselect widgets
    $('#dashboard-grid').on('click', function (e) {
        if (e.target === this || $(e.target).hasClass('grid-stack')) {
            $('.dashboard-widget').removeClass('selected');
            window.WidgetManager.selectedWidget = null;
        }
    });

    // Keyboard shortcuts
    $(document).on('keydown', function (e) {
        // Ctrl+Z for undo
        if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
            e.preventDefault();
            window.DashboardStorage.undo();
        }

        // Ctrl+Y or Ctrl+Shift+Z for redo
        if (e.ctrlKey && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
            e.preventDefault();
            window.DashboardStorage.redo();
        }

        // Ctrl+C for copy
        if (e.ctrlKey && e.key === 'c' && window.WidgetManager.selectedWidget) {
            e.preventDefault();
            window.DashboardStorage.copyWidget(window.WidgetManager.selectedWidget);
        }

        // Ctrl+V for paste
        if (e.ctrlKey && e.key === 'v') {
            e.preventDefault();
            window.DashboardStorage.pasteWidget();
        }

        // Delete key for delete widget
        if (e.key === 'Delete' && window.WidgetManager.selectedWidget) {
            e.preventDefault();
            window.WidgetManager.deleteWidget(window.WidgetManager.selectedWidget);
        }

        // Escape to deselect
        if (e.key === 'Escape') {
            $('.dashboard-widget').removeClass('selected');
            window.WidgetManager.selectedWidget = null;
        }
    });

    // Window resize handler
    $(window).on('resize', debounce(function () {
        // Refresh all charts on window resize
        if (window.WidgetManager && window.WidgetManager.amCharts) {
            Object.values(window.WidgetManager.amCharts).forEach(chartData => {
                if (chartData && chartData.root && typeof chartData.root.resize === 'function') {
                    chartData.root.resize();
                }
            });
        }
    }, 250));

    // Auto-save every 30 seconds
    setInterval(() => {
        if ($('.dashboard-widget').length > 0) {
            window.DashboardStorage.saveState();
        }
    }, 30000);

    // Add context menu for widgets
    $(document).on('contextmenu', '.dashboard-widget', function (e) {
        e.preventDefault();
        const widgetId = $(this).closest('.grid-stack-item').attr('gs-id');
        showContextMenu(e, widgetId);
    });

    // Hide context menu on click elsewhere
    $(document).on('click', function () {
        $('.context-menu').remove();
    });
}


function SaveDashboardBuilder() {

    let objInfo = {
        Id:19,
        ReferenceId:"343434",
        Name:"Test",
        Properties:"dssds",
        IsLock: true,                      // or false, or null
        IsPublish: false,                 // optional
        IsActive: true,                   // optional
        CreatedBy: 101,                   // or null
        CreatedDate: "2025-07-09T10:00:00", // ISO format (important for DateTime?)
        LastModifiedBy:101,
        LastModifiedDate: "2025-07-09T12:00:00"
    }


    $.ajax({
        url: '/BCMAdministration/DashboardBuilder/SaveDashboardBuilder',
        type: 'POST',
        credentials: "include",
        contentType: "application/json",
        dataType: "json",
        data: JSON.stringify(objInfo),
        success: async function (response) {

        }

    })

}



function loadDashboard() {
    const savedState = window.DashboardStorage.loadState();

    if (savedState && savedState.widgets && savedState.widgets.length > 0) {
        // Clear existing widgets
        window.WidgetManager.grid.removeAll();

        // Load saved widgets
        savedState.widgets.forEach(widgetData => {
            window.WidgetManager.createWidget(
                widgetData.type,
                widgetData.title,
                {
                    x: widgetData.x || 0,
                    y: widgetData.y || 0,
                    w: widgetData.w || 4,
                    h: widgetData.h || 3
                },
                widgetData.data,
                widgetData.id
            );
        });
    } else {
        // Load default dashboard
        window.WidgetManager.loadDefaultDashboard();
    }
}

function togglePreviewMode(isPreview) {
    const $body = $('body');
    const $navbar = $('.navbar');

    if (isPreview) {
        $body.addClass('preview-mode');
        $navbar.slideUp(300);

        // Disable grid editing
        window.WidgetManager.grid.disable();

        window.DashboardStorage.showNotification('Preview mode enabled', 'info');
    } else {
        $body.removeClass('preview-mode');
        $navbar.slideDown(300);

        // Enable grid editing
        window.WidgetManager.grid.enable();

        window.DashboardStorage.showNotification('Edit mode enabled', 'info');
    }
}

function showContextMenu(e, widgetId) {
    $('.context-menu').remove();

    const contextMenu = $(`
        <div class="context-menu position-absolute bg-white border rounded shadow-lg p-2" 
             style="top: ${e.pageY}px; left: ${e.pageX}px; z-index: 9999;">
            <div class="list-group list-group-flush">
                <a href="#" class="list-group-item list-group-item-action py-1 px-2" data-action="duplicate">
                    <i class="fas fa-clone me-2"></i>Duplicate
                </a>
                <a href="#" class="list-group-item list-group-item-action py-1 px-2" data-action="copy">
                    <i class="fas fa-copy me-2"></i>Copy
                </a>
                <a href="#" class="list-group-item list-group-item-action py-1 px-2" data-action="delete">
                    <i class="fas fa-trash me-2 text-danger"></i>Delete
                </a>
            </div>
        </div>
    `);

    $('body').append(contextMenu);

    // Handle context menu actions
    contextMenu.find('a').on('click', function (e) {
        e.preventDefault();
        const action = $(this).data('action');

        switch (action) {
            case 'duplicate':
                window.WidgetManager.duplicateWidget(widgetId);
                break;
            case 'copy':
                window.DashboardStorage.copyWidget(widgetId);
                break;
            case 'delete':
                window.WidgetManager.deleteWidget(widgetId);
                break;
        }

        contextMenu.remove();
    });
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export/Import functionality
function exportDashboard() {
    window.DashboardStorage.exportDashboard();
}

function importDashboard() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function (e) {
        const file = e.target.files[0];
        if (file) {
            window.DashboardStorage.importDashboard(file);
        }
    };
    input.click();
}

// Add export/import buttons to navbar (will be added via DOM manipulation)
$(document).ready(function () {
    // Add export/import dropdown to navbar
    const exportImportDropdown = $(`
        <div class="nav-item dropdown me-3">
            <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-download me-1"></i>Export/Import
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportDashboard()">
                    <i class="fas fa-download me-2"></i>Export Dashboard
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="importDashboard()">
                    <i class="fas fa-upload me-2"></i>Import Dashboard
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" onclick="window.WidgetManager.clearDashboard()">
                    <i class="fas fa-trash me-2 text-danger"></i>Clear Dashboard
                </a></li>
            </ul>
        </div>
    `);

    // Insert before the preview mode toggle
    $('.form-check.form-switch').before(exportImportDropdown);

    // Re-initialize dropdown functionality for dynamically added dropdown
    setTimeout(() => {
        console.log('Re-initializing dropdown functionality for Export/Import...');
        setupDropdownToggle();
    }, 100);
});

// Global functions for window object
window.exportDashboard = exportDashboard;
window.importDashboard = importDashboard;
