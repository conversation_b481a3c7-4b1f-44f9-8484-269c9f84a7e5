﻿@model IEnumerable<BCM.BusinessClasses.MenuRights>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    var checkPagesID = ViewBag.CheckPagesId as List<string>;
    var checkMenuID = ViewBag.checkMenuId as List<int>;
    var checkSubMenuID = ViewBag.CheckSubMenuId as List<string>;
    var name = string.Empty;
}

<script src="~/js/password_toggle.js"></script>

<form method="post" asp-action="UpdatePageAccessClick">
    <div class="tree-menu">
        <ul class="tree">
            @if (Model != null)
            {
                bool objIsCheckedPage = false;
                bool objIsCheckedMenu = false;
                bool objIsCheckedSubMenu = false;

                @foreach (BCM.BusinessClasses.MenuRights item in Model.Where(x => !string.IsNullOrEmpty(x.MenuName)).GroupBy(x => new { x.MenuName }).Select(y => y.First()))
                {
                    if (checkMenuID != null)
                    {
                        objIsCheckedMenu = ((List<int>)checkMenuID).Contains((int)item.MenuID);
                    }

                    <li>
                        <span role="button"><input class="form-check-input me-1 mt-2 menu-checkbox" type="checkbox" name="selectedmenu" value="@item.MenuID" id="@item.MenuID" @(objIsCheckedMenu ? "checked" : "") />@item.MenuName</span>
                        <ul>
                            @foreach (BCM.BusinessClasses.MenuRights item3 in Model.Where(X => X.SubMenuID == 0 && X.MenuID != 0 && X.PageID != 0))
                            {
                                if (@item.MenuID == item3.MenuID)
                                {
                                    if (checkPagesID != null)
                                    {
                                        objIsCheckedPage = ((List<string>)checkPagesID).Contains(item.MenuID.ToString() + "-" + item3.SubMenuID.ToString() + "-" + item3.PageID.ToString());
                                    }

                                    <li>
                                        <span role="button"><input class="form-check-input me-1 mt-2 page-checkbox" type="checkbox" name="selectedPages" value="@<EMAIL><EMAIL>" id="@item3.PageID" @(objIsCheckedPage ? "checked" : "") />@item3.PageName</span>
                                    </li>
                                }
                            }
                            @foreach (BCM.BusinessClasses.MenuRights item1 in Model.Where(x => !string.IsNullOrEmpty(x.SubMenuName) && x.MenuID == @item.MenuID).GroupBy(x => new { x.SubMenuName }).Select(y => y.First()))
                            {
                                if (@item.MenuID == item1.MenuID)
                                {
                                    if (checkSubMenuID != null)
                                    {
                                        objIsCheckedSubMenu = ((List<string>)checkSubMenuID).Contains(item.MenuID.ToString() + "-" + item1.SubMenuID);
                                    }

                                    <li>
                                        <span role="button"><input class="form-check-input me-1 mt-2 submenu-checkbox" type="checkbox" name="selectedsubmenu" value="@<EMAIL>" id="@item1.SubMenuID" @(objIsCheckedSubMenu ? "checked" : "") />@item1.SubMenuName</span>
                                        <ul class="sub-parent">
                                            @foreach (BCM.BusinessClasses.MenuRights item2 in Model.Where(x => !string.IsNullOrEmpty(x.PageName) && x.MenuID == @item.MenuID))
                                            {
                                                if (@item2.SubMenuID == item1.SubMenuID)
                                                {
                                                    if (checkPagesID != null)
                                                    {
                                                        objIsCheckedPage = ((List<string>)checkPagesID).Contains(item.MenuID.ToString() + "-" + item1.SubMenuID.ToString() + "-" + item2.PageID.ToString());
                                                    }
                                                    <li>
                                                        <span role="button"><input class="form-check-input me-1 mt-2 page-checkbox" type="checkbox" name="selectedPages" value="@<EMAIL><EMAIL>" id="@item2.PageID" @(objIsCheckedPage ? "checked" : "") />@item2.PageName</span>
                                                    </li>
                                                }
                                            }
                                        </ul>
                                    </li>
                                }
                            }
                        </ul>
                    </li>
                }
                @foreach (BCM.BusinessClasses.MenuRights item in Model.Where(x => string.IsNullOrEmpty(x.MenuName)).GroupBy(x => new { x.MenuName }).Select(y => y.First()))
                {
                    if (checkMenuID != null)
                    {
                        objIsCheckedMenu = ((List<int>)checkMenuID).Contains((int)item.MenuID);
                    }
                    <li>
                        <span role="button"><input class="form-check-input me-1 mt-2 menu-checkbox" name="selectedmenu" value="0" id="0" type="checkbox" @(objIsCheckedMenu ? "checked" : "") />Not Integrated In Menu</span>
                        <ul>
                            @foreach (BCM.BusinessClasses.MenuRights item1 in Model.Where(x => !string.IsNullOrEmpty(x.PageName)))
                            {
                                if (@item1.MenuID == 0)
                                {
                                    if (checkPagesID != null)
                                    {
                                        objIsCheckedPage = ((List<string>)checkPagesID).Contains(item.MenuID.ToString() + "-" + item.MenuID.ToString() + "-" + item1.PageID.ToString());

                                    }
                                    <li>
                                        <span role="button"><input class="form-check-input me-1 mt-2 page-checkbox" type="checkbox" name="selectedPages" value="@<EMAIL><EMAIL>" id="@item1.PageID" @(objIsCheckedPage ? "checked" : "") />@item1.PageName</span>
                                    </li>
                                }
                            }
                        </ul>
                    </li>
                }
            }
        </ul>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>

<script>

    $(document).ready(function () {
        $('.menu-checkbox').on('change', function () {
            var isChecked = $(this).is(':checked');
            $(this).closest('li').find('.submenu-checkbox').prop('checked', isChecked);
            $(this).closest('li').find('.page-checkbox').prop('checked', isChecked);
        });

        $('.submenu-checkbox').on('change', function () {
            var isChecked = $(this).is(':checked');
            $(this).closest('li').find('.page-checkbox').prop('checked', isChecked);
        });

        $('.page-checkbox').on('change', function () {
            var menuCheckbox = $(this).closest('ul').prev('.menu-checkbox');
            var submenuCheckbox = $(this).closest('ul').prev('.submenu-checkbox');

            if ($(this).is(':checked')) {
                debugger;
                menuCheckbox.prop('checked', true);
                submenuCheckbox.prop('checked', true);
            }

            else {
                debugger;
                var allUnchecked = $(this).closest('ul').find('.page-checkbox:checked').length === 0;
                if (allUnchecked) {
                    menuCheckbox.prop('checked', false);
                    submenuCheckbox.prop('checked', false);
                }
            }
        });
    });

</script>




