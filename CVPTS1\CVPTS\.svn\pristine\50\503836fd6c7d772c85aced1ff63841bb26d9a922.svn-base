﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<BCM.BusinessClasses.IncidentType>
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    max-width: 100%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    }

    .table > :not(caption) > * > * {
    background: transparent
    }

    .accidental_icon_bg {
    background: #F44F4F;
    }

    .accidental_bg {
    background: #FFEDF4;
    }

    .delibrate_icon_bg {
    background: #F4A84F;
    }

    .delibrate_bg {
    background: #FFF5EB;
    }

    .intentional_icon_bg {
    background: #6B6BE8;
    }

    .intentional_bg {
    background: #F3F3FF;
    }

    .natural_icon_bg {
    background: #48D279;
    }

    .natural_bg {
    background: #E7F8EB;
    }

    .system_icon_bg {
    background: #45CFD7;
    }

    .system_bg {
    background: #E3FBFE;
    }

    .unavailibility_icon_bg {
    background: #D9DD17;
    }

    .unavailibility_bg {
    background: #FEF9CE;
    }
</style>
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">
        Threat Type Details

    </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <a class="btn icon-btn btn-primary btn-sm Create" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</a>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>
<div class="Page-Condant  border-0 pe-2" style="height: calc(100vh - 115px);overflow: auto;">
    <div class="card-body">
        <div class="row g-3">
            @if (Model != null)
            {
                foreach (var item in Model)
                {
                    <div class="col-3 Incident-bg">
                        <div class="card shadow-sm h-100">
                            <div class="card-body bg-transparent">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <div class="circle accidental_icon_bg">
                                            <i class="cv-accidental-hazards fs-5 text-white"></i>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-1">
                                        <span class="me-1 btnEdit" role="button" data-bs-toggle="modal" data-bs-target="#CreateModal" data-id="@item.DisasterID" data-disaster-name="@item.DisasterName" data-incident-id="@item.IncidentTypeID" data-incident-name="@item.IncidentTypeName" data-incident-details="@item.IncidentDetails"><i class="cv-edit align-middle"></i></span>
                                        <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger btnDelete" data-incident-id="@item.IncidentTypeID" data-incident-name="@item.IncidentTypeName"></i></span>

                                    </div>
                                </div>
                                <div class="mt-2">
                                    <table class="table table-sm table-borderless mb-0 align-middle">
                                        <tr>
                                            <td class="text-muted">Threat Type </td>
                                            <td>:</td>
                                            <td hidden class="fw-semibold">@item.DisasterID</td>
                                            <td class="fw-semibold">@item.DisasterName</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">Threat Type Name</td>
                                            <td>:</td>
                                            <td hidden class="fw-semibold">@item.IncidentTypeID</td>
                                            <td class="fw-semibold">@item.IncidentTypeName</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
            @*    <div class="col-3">
                <div class="card shadow-sm h-100 delibrate_bg">
                    <div class="card-body bg-transparent">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div class="circle delibrate_icon_bg">
                                    <i class="cv-delibrate-hazards fs-5 text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tr>
                                    <td class="text-muted">Threat Type </td>
                                    <td>:</td>
                                    <td class="fw-semibold">Delibrate Hazards</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Threat Type Name</td>
                                    <td>:</td>
                                    <td class="fw-semibold">WAN link failure</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card shadow-sm h-100 intentional_bg">
                    <div class="card-body bg-transparent">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div class="circle intentional_icon_bg">
                                    <i class="cv-objectives fs-5 text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tr>
                                    <td class="text-muted">Threat Type </td>
                                    <td>:</td>
                                    <td class="fw-semibold">Intentional Acts</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Threat Type Name</td>
                                    <td>:</td>
                                    <td class="fw-semibold">Application Malfunction</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card shadow-sm h-100 natural_bg">
                    <div class="card-body bg-transparent">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div class="circle natural_icon_bg">
                                    <i class="cv-natural-disaster fs-5 text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tr>
                                    <td class="text-muted">Threat Type </td>
                                    <td>:</td>
                                    <td class="fw-semibold">Natural Disaster</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Threat Type Name</td>
                                    <td>:</td>
                                    <td class="fw-semibold">Application Malfunction</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card shadow-sm h-100 system_bg">
                    <div class="card-body bg-transparent">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div class="circle system_icon_bg">
                                    <i class="cv-system-failure fs-5 text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tr>
                                    <td class="text-muted">Threat Type </td>
                                    <td>:</td>
                                    <td class="fw-semibold">System Failure</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Threat Type Name</td>
                                    <td>:</td>
                                    <td class="fw-semibold">Device failure</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card shadow-sm h-100 unavailibility_bg">
                    <div class="card-body bg-transparent">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div class="circle unavailibility_icon_bg">
                                    <i class="cv-unavailable fs-5 text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tr>
                                    <td class="text-muted">Threat Type </td>
                                    <td>:</td>
                                    <td class="fw-semibold">Unavailibility</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Threat Type Name</td>
                                    <td>:</td>
                                    <td class="fw-semibold">Application Malfunction</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card shadow-sm h-100 accidental_bg">
                    <div class="card-body bg-transparent">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div class="circle accidental_icon_bg">
                                    <i class="cv-accidental-hazards fs-5 text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tr>
                                    <td class="text-muted">Threat Type </td>
                                    <td>:</td>
                                    <td class="fw-semibold">Accidental Hazards</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Threat Type Name</td>
                                    <td>:</td>
                                    <td class="fw-semibold">Application malfunction</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card shadow-sm h-100 delibrate_bg">
                    <div class="card-body bg-transparent">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div class="circle delibrate_icon_bg">
                                    <i class="cv-delibrate-hazards fs-5 text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tr>
                                    <td class="text-muted">Threat Type </td>
                                    <td>:</td>
                                    <td class="fw-semibold">Delibrate Hazards</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Threat Type Name</td>
                                    <td>:</td>
                                    <td class="fw-semibold">WAN link failure</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div> *@
        </div>

    </div>
</div>

<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Threat Type Detail Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form asp-action="SaveIncidentTypeMasterDetails" method="post" asp-controller="IncidentTypeMaster">
                <div class="row row-cols-1">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Threat Type Category</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-incident-type"></i></span>
                                <select id="ddlIncidentCatagory" class=" form-select" required>
                                        <option selected disabled value="">--Select--</option>
                                        @foreach (var objDisaster in ViewBag.Disaster)
                                        {
                                            <option value="@objDisaster.Value">@objDisaster.Text</option>
                                        }
                                </select>
                            </div>
                        </div>
                        <div class="form-group w-100">
                            <label class="form-label">Threat Type Detail</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-incident-detail"></i></span>
                                    <textarea class="form-control" id="txtIncidentTypeDetail" name="IncidentDetails" placeholder="Threat Type Detail" style="height:0px" required></textarea>
                                    <input type="hidden" id="lblDisasterID" name="DisasterID" value="" />
                                </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Threat Type Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-incident"></i></span>
                                    <input class="form-control" id="txtIncidentTypeName" name="IncidentTypeName" type="text" placeholder="Threat Type Name" required />
                                    <input type="hidden" id="lblIncidentID" name="IncidentTypeID" value="" />
                                </div>
                        </div>
                    </div>
                </div>
               
                <div class="modal-footer d-flex justify-content-between">
                    <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                        <button id="btnSaveIncidentType" type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
                </form>
            </div>
       
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->
@section Scripts {
    <script>

        $(document).ready(function(){

            $(document).on('click', '.btnEdit', function () {
                debugger;
                var ddlVal = $(this).data('id');
                $('#ddlIncidentCatagory').val(ddlVal).change();
                $('#txtIncidentTypeName').val($(this).data('incident-name'));
                $('#txtIncidentTypeDetail').val($(this).data('incident-details'));
                $('#lblDisasterID').val($(this).data('id'));
                $('#lblIncidentID').val($(this).data('incident-id'));
                $("#btnSaveIncidentType").html("Update");
            });

            $(document).on('click', '.Create', function () {

                 $('#ddlIncidentCatagory').val('');
                $('#txtIncidentTypeName').val('');
                $('#txtIncidentTypeDetail').val('');
                $('#lblDisasterID').val('');
                $('#lblIncidentID').val('');
                $("#btnSaveIncidentType").html("Save");
            });

            $(document).on('click', '.btnDelete', function () {
                var iId = $(this).data('incident-id');
                console.log("Delete button clicked for ID:", iId);
                
                $.get('@Url.Action("DeleteIncidentTypeDetails", "IncidentTypeMaster")', { iId: iId }, function (data) {
                    console.log("Delete partial view loaded");
                    $('#DeleteBody').html(data);
                    $('#DeleteModal').modal('show');
                }).fail(function(error) {
                    console.error("Error loading delete modal:", error);
                });
            })


            var searchTimer;
            $('#search-inp').on('input', function() {
                var searchText = $(this).val().toLowerCase();
                
                // Clear any existing timer to debounce the search
                clearTimeout(searchTimer);
                
                // Set a timer to delay the search until the user stops typing
                searchTimer = setTimeout(function() {
                    // Filter the Threat Type cards based on search text
                    $('.Incident-bg').each(function() {
                        var cardText = $(this).text().toLowerCase();
                        if (cardText.indexOf(searchText) > -1) {
                            $(this).show();
                        } else {
                            $(this).hide();
                        }
                    });
                }, 300); // 300ms delay for debouncing
            });

             document.querySelectorAll('.Incident-bg').forEach((el, idx) => {
          const circle = el.querySelector('.circle');
          if (!circle) return;

          const colors = ['#f96443', '#f4b14f', '#727eff', '#7c38f1','#e63875','#d9dd17'];
          circle.style.backgroundColor = colors[idx % 6];
        });

        })
    </script>
}
