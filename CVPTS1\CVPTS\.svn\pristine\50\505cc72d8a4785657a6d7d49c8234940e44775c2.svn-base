﻿@model IEnumerable<BCM.BusinessClasses.Attachments>
@{
    ViewBag.Title = "AuditPolicy";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Audit Policy</h6>
    <div class="d-flex gap-2 justify-content-end align-items-end" style="width:80%;">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <div class="dropdown">
            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                <i class="cv-filter align-middle" title="View Filter"></i>
            </button>
            <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                <div class="mb-3">
                    <label>Organizations</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                        <select class=" form-control ddlOrganization" autocomplete="off" id="ddlOrganization" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstOrg,"Id","OrganizationName"))">
                            <option selected value="0">-- Select Organizations --</option>
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Units</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                        <select class="form-control ddlDepartment" autocomplete="off" id="ddlUnit" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstUnit,"UnitID","UnitName"))">
                            <option selected value="0">-- Select Units --</option>
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Departments</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-department"></i></span>
                        <select class="form-control ddlDepartment" autocomplete="off" id="ddlDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstDepartment,"DepartmentID","DepartmentName"))">
                            <option selected value="0">-- select Departments --</option>
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Sub Departments</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
                        <select class=" form-control ddlSubDepartment" autocomplete="off" id="ddlSubDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstSubDepartment,"SubFunctionID","SubFunctionName"))">
                            <option selected value="0">-- Select SubDepartments --</option>
                        </select>
                    </div>
                </div>
                @* <div class="text-end">
                        <button type="submit" class="btn btn-sm btn-primary">Search</button>
                    </div> *@
            </form>
        </div>
        <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal"
                data-bs-target="CreateModal" aria-controls="offcanvasExample">
            <i class="cv-Plus" title="Create New"></i>Create
        </button>
    </div>
</div>
<div class="Page-Condant card border-0" id="FacilityList">
    @await Html.PartialAsync("_FilterAuditPolicy")
</div>
@* Configure Manage BCM Audit File
    Modal Start *@
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
     data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Audit Policy Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-data">
            </div>

        </div>
    </div>
</div>

@* Configure Manage BCM Audit File
    Modal End *@

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center deleteBody" id="deleteBody">
            </div>
        </div>
    </div>
</div>

<!-- End Delete Modal -->
@section Scripts {
    <script>
        $('#btnCreate').click(function () {
            //$.get('/BCMDocuments/AuditPolicy/AddAuditPolicy', function (data) {
            $.get('@Url.Action("AddAuditPolicy", "AuditPolicy")', function (data) {
                $('.modal-data').html(data);
                $('#CreateModal').modal('show');
            });
        });

        $(document).on('click', '.btnEdit', function () {
            var id = $(this).data('id');
            //$.get('/BCMDocuments/AuditPolicy/EditAuditPolicy/' + id, function (data) {
            $.get('@Url.Action("EditAuditPolicy", "AuditPolicy")', { id: id }, function (data) {
                $('.modal-data').html(data);
                $('#CreateModal').modal('show');
                $('#modelTitle').text('Audit Policy Update');
            });
        });

        $(document).on('click', '.btnDelete', function () {
            var id = $(this).data('id');
            //$.get('/BCMDocuments/AuditPolicy/DeleteAuditPolicy/', { id: id }, function (data) {
            $.get('@Url.Action("DeleteAuditPolicy", "AuditPolicy")', { id: id }, function (data) {
                $('.deleteBody').html(data);
                $('#DeleteModal').modal('show');

            });
        })

        $('#ddlOrganization').change (function () {
                var ddlOrganizationVal = $('#ddlOrganization').val();
                var ddlUnitnVal = $('#ddlUnit').val();
                var ddlDepartmentVal = $('#ddlDepartment').val();
                var TextSearch = $('#search-inp').text();
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                    if (ddlOrganizationVal)
                    {
                        $.ajax({
                                    url: '@Url.Action("GetAllUnits", "AuditPolicy")',
                                    type: 'GET',
                                    data: { iOrgID: ddlOrganizationVal },
                                    success: function (data) {
                                        debugger;
                                        var ddlUnit = $('#ddlUnit');
                                        ddlUnit.empty();
                                        ddlUnit.append('<option value="0">-- All Units --</option>');


                                        $.each(data, function (index, item) {
                                            ddlUnit.append('<option value="' + item.unitID + '">' + item.unitName + '</option>')

                                        });
                                    },
                                    error:function(Error){
                                        console.log(Error,"Error Block");
                                    }

                        })
                    }
                     $.get('@Url.Action("GetSearchProcess", "AuditPolicy")', {textSearch : TextSearch,OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal}, function (data) {
                         var tableData = $('#FacilityList');
                            var tableData = $('#FacilityList');
                            tableData.empty();
                            $('#FacilityList').html(data);
                        });
            });

            $('#ddlUnit').change (function () {
                            var ddlUnitnV = $('#ddlUnit').val();
                            var ddlUnitnVal = $('#ddlUnit').val();
                            var ddlOrganizationVal = $('#ddlOrganization').val();
                             var ddlDepartmentVal = $('#ddlDepartment').val();
                            var TextSearch = $('#search-inp').text();
                            var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                             debugger;
                        if (ddlUnitnV)
                        {
                            debugger;
                            $.ajax({
                                 url: '@Url.Action("GetDepartmentsByUnitID", "AuditPolicy")',
                                 type: 'GET',
                                 data: {iOrgID:ddlOrganizationVal, iUnitID: ddlUnitnVal },
                                 success: function (data) {
                                     var ddlDepartment = $('#ddlDepartment');
                                     ddlDepartment.empty();
                                     ddlDepartment.append('<option value="0">-- All Departments --</option>');
                                     $.each(data, function (index, item) {
                                         ddlDepartment.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                                     });
                                 }
                            })
                        }
                        $.get('@Url.Action("GetSearchProcess", "AuditPolicy")', {textSearch : TextSearch,OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal}, function (data) {
                         var tableData = $('#FacilityList');
                            var tableData = $('#FacilityList');
                            tableData.empty();
                            $('#FacilityList').html(data);
                        });

            });

            $('#ddlDepartment').change(function ()
                {
                     var ddlUnitnV = $('#ddlUnit').val();
                     var ddlUnitnVal = $('#ddlUnit').val();
                     var ddlOrganizationVal = $('#ddlOrganization').val();
                     var ddlDepartmentVal = $('#ddlDepartment').val();
                     var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                     var TextSearch = $('#search-inp').text();

                    var iUnitID = $(this).val();
                    if (iUnitID)
                    {
                        $.ajax({
                                    url: '@Url.Action("GetAllSubDepartments", "AuditPolicy")',
                                    type: 'GET',
                                    data: {iOrgID : ddlOrganizationVal,iDepartmentID: iUnitID },
                                    success: function (data) {
                                        var ddlSubDepartment = $('#ddlSubDepartment');
                                        ddlSubDepartment.empty();
                                        ddlSubDepartment.append('<option value="0">-- All SubDepartments --</option>');
                                        $.each(data, function (index, item) {
                                            console.log(item);
                                            ddlSubDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>')
                                        });
                                    }
                        })
                    }
                    $.get('@Url.Action("GetSearchProcess", "AuditPolicy")', {textSearch : TextSearch,OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal}, function (data) {
                         var tableData = $('#FacilityList');
                            var tableData = $('#FacilityList');
                            tableData.empty();
                            $('#FacilityList').html(data);
                        });
            })

        // $(document).on('click', '.btnDelete', function () {
        //     var id = $(this).data('id');
        //     $.get('/BCMDocuments/AuditPolicy/DeleteAuditPolicy/' + id, function (data) {
        //         $('#dbody').html(data);
        //         $('#DeleteModal').modal('show');
        //         $('#modelTitle').text('Audit Policy Delete');
        //     });
        // });

    </script>
}
