﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using Newtonsoft.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Reflection;
using Microsoft.AspNetCore.Mvc;
using System.Data;
using Microsoft.AspNetCore.Components.Routing;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMMenuAccess.Controllers;
[Area("BCMMenuAccess")]
public class MenuMasterController : BaseController
{
    private ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;

    public MenuMasterController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    public IActionResult MenuMaster()
    {
        List<MenuRights> lstMenuRights = new List<MenuRights>();
        try
        {
           
            lstMenuRights = _ProcessSrv.GetMenuMasterlist();
            populateData(lstMenuRights);

        }
        catch(Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstMenuRights);
    }

    [HttpGet]
    public JsonResult GetMenuById(int iMenuId)
    {
        try
        {
            MenuRights objMenuRights = _ProcessSrv.GetMenuMasterlistByID(iMenuId);
            if (objMenuRights != null) 
            {
                return Json(objMenuRights);
            }
        }
        catch(Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }



    [HttpPost]
    public IActionResult SaveUpdateMenuMaster(MenuRights objMenuRights)
    {
        bool bSucess = false;
        try
        {
            if(objMenuRights.MenuID == null || objMenuRights.MenuID == 0)
            {
                List<MenuRights> menuData = _ProcessSrv.GetMenuMasterlist();
                var checkDuplicate = menuData.Where(m => m.MenuName == objMenuRights.MenuName.Trim()).ToList();

                if (checkDuplicate.Any())
                {
                    ViewBag.InfoColor = "red";
                    ViewBag.InfoText = "Menu Name already exists! Please enter a different Menu Name.";
                }
                else
                {

                    objMenuRights.CreatedBy = _UserDetails.UserID;                    

                    bSucess = _ProcessSrv.MenuSaveUpdate(objMenuRights);

                    if (bSucess)
                    {
                        ViewBag.InfoColor = "green";
                        ViewBag.InfoText = "Menu Saved Successfully";
                    }
                    else
                    {
                        ViewBag.InfoColor = "red";
                        ViewBag.InfoText = "Error Occurred while Saving record.";
                    }
                }
                
            }
            else
            {
                objMenuRights.UpdatedBy = _UserDetails.UserID;
                bSucess = _ProcessSrv.MenuSaveUpdate(objMenuRights);

                if (bSucess)
                {
                    ViewBag.InfoColor = "green";
                    ViewBag.InfoText = "Menu updated Successfully";
                }
                else
                {
                    ViewBag.InfoColor = "red";
                    ViewBag.InfoText = "Error Occurred while Updating record.";
                }

            }

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            ViewBag.InfoColor = "red";
            ViewBag.InfoText = "An error occurred: " + ex.Message;
        }

       return RedirectToAction("MenuMaster");
    }

    [HttpGet]
    public IActionResult DeleteMenuById(int iMenuId)
    {

        MenuRights objMenuRights = new MenuRights();
        try
        {

            objMenuRights = _ProcessSrv.GetMenuMasterlistByID(iMenuId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteMenuById", objMenuRights);
    }

    [HttpPost]
    public IActionResult DeleteMenuById(MenuRights objMenuRights)
    {
        
        try
        {
            bool bSuccess = _ProcessSrv.DeleteMenuMasterUpadte(objMenuRights.MenuID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("MenuMaster");
    }

    protected void populateData(List<MenuRights> lstMenuRights)
    {
        DataTable dtSequence = new DataTable();
        dtSequence.Columns.Add("SequenceID");
        dtSequence.Columns.Add("SequenceName");
        for (int i = 1; i <= lstMenuRights.Count + 1; i++)
        {
            dtSequence.Rows.Add(i, i);

        }
        ViewBag.Sequences = dtSequence;
    }

}
