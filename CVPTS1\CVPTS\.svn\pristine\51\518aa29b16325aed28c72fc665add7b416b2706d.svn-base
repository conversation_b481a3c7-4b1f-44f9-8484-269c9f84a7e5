﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Areas.BCMTeams.Controllers;
[Area("BCMTeams")]
public class GroupNotificationDetailsController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    int iNotifiedAs = 0;

    public GroupNotificationDetailsController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult GroupNotificationDetails(int iGroupMapID, int iNotificationID, int iNotifiedID)
    {
        List<BCMGroupResources> lstBCMGroupResources = new List<BCMGroupResources>();        
        try
        {
            ViewBag.Condition = iNotifiedID;
            iNotifiedAs = iNotifiedID;
            ViewBag.NotificationID = iNotificationID;
            BindRadioButtons();
            lstBCMGroupResources = GetDataByGroupMapID(iGroupMapID, iNotificationID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_GroupNotificationDetails", lstBCMGroupResources);
    }


    private void BindRadioButtons()
    {
        try
        {
            ViewBag.RadioButtons = _Utilities.PolulateNotificationAs();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }        
    }

    private List<BCMGroupResources> GetDataByGroupMapID(int iGroupMapID, int iNotificationID)
    {
        List<BCMGroupResources> filteredBCMGroupResources = new List<BCMGroupResources>();
        try
        {
            if (iNotificationID > 0)
            {
                List<BCMGroupResources> lstBCMGroupResources = _ProcessSrv.GetBCMGroupNotificationResource_ByNotificationID(iNotificationID);
                if (lstBCMGroupResources.Count > 0)
                {
                    if (iNotifiedAs == (int)BCPEnum.NotificationAs.FYA)
                    {
                        filteredBCMGroupResources = (from item in lstBCMGroupResources
                                                where item.NotificationAs.Equals(((int)BCPEnum.NotificationAs.FYA).ToString())
                                                select item).ToList();

                        DisplayCount(filteredBCMGroupResources);
                    }
                    else if (iNotifiedAs == (int)BCPEnum.NotificationAs.FYI)
                    {
                        filteredBCMGroupResources = (from item in lstBCMGroupResources
                                                where item.NotificationAs.Equals(((int)BCPEnum.NotificationAs.FYI).ToString())
                                                select item).ToList();
                    }
                    ViewBag.GroupNames = filteredBCMGroupResources.GroupBy(item => item.GroupMapID)
                        .Select(group => group.First()).ToList();

                    ViewBag.fyaBCMGroupResources = filteredBCMGroupResources.GroupBy(item => item.ResourceName)
                        .Select(resource => resource.First()).ToList();
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return filteredBCMGroupResources;
    }

    private void DisplayCount(List<BCMGroupResources> lstBCMGroupResources)
    {
        int iTotalCount = 0;
        int iRespondedCount = 0;
        int iNotRespondedCount = 0;
        try
        {
            if (lstBCMGroupResources.Count > 0)
            {
                var totalList = (from iTotal in lstBCMGroupResources
                                 group iTotal by new { Name = iTotal.ResourceName, GroupMapID = iTotal.GroupMapID } into obj
                                 select new { Name = obj.Key.Name, GroupMapID = obj.Key.GroupMapID });

                iTotalCount = totalList.Count();

                var responseList = (from iReponse in lstBCMGroupResources
                                    where string.IsNullOrEmpty(Convert.ToString(iReponse.ResponseDate)) == false && iReponse.ResponseDate != DateTime.MinValue
                                    select iReponse);
                if (responseList.Any())
                {
                    var respondedUserList = (from iReponse in responseList
                                             group iReponse by new { Name = iReponse.ResourceName, GroupMapID = iReponse.GroupMapID } into obj
                                             select new { Name = obj.Key.Name, GroupMapID = obj.Key.GroupMapID });
                    iRespondedCount = respondedUserList.Count();
                }
                iNotRespondedCount = iTotalCount - iRespondedCount;

                ViewBag.TotalCount = iTotalCount;
                ViewBag.RespondedCount = iRespondedCount;
                ViewBag.NotRespondedCount = iNotRespondedCount;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    [HttpGet]
    public IActionResult RadioButtonClick(int iNotifiedID, int iNotificationId)
    {
        List<BCMGroupResources> lstBCMGroupResources = new List<BCMGroupResources>();
        try
        {
            iNotifiedAs = iNotifiedID;
            lstBCMGroupResources = GetDataByGroupMapID(0, iNotificationId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_NotifiedAsUsersAndTeams", lstBCMGroupResources);
    }
}

