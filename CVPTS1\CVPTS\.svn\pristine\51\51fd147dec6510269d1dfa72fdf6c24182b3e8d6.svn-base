﻿@using BCM.BusinessClasses
@* 
@{
    ViewData["Title"] = "Table Access";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>

    .tabel-access-card {
        border-radius: 10px;
        border: 0px;
        background: #FFF9FB;
        padding: 12px 10px;
        box-shadow: 1px 5px 10px #e3dddd;
    }
</style>


<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">Table Access</h6>
        <div class="d-flex gap-3 justify-content-end align-items-end">
            <div class="input-group w-30">
                <input class="form-control" type="text" placeholder="Search" />
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
            </div>
            <div class="form-check form-switch me-0 d-flex align-items-center gap-2 w-50" style="min-height:0px">
                <input class="form-check-input" type="checkbox" role="switch" id="switchCheckChecked" checked>
                <label class="form-check-label" for="switchCheckChecked">Select All</label>
            </div>
            <button class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</div>
<div class="Page-Condant card border-0" style="height: calc(100vh - 108px);">
    <div class="card-body">
        <div class="g-4 row row-cols-xl-4 row-cols-3" id="table-access">
        </div>
    </div>

</div>


<script>
    document.addEventListener("DOMContentLoaded", function () {
      const container = document.getElementById("table-access");

      for (let i = 1; i <= 15; i++) {
        const col = document.createElement("div");
        col.className = "col d-grid";

        col.innerHTML = `
          <div class="d-flex tabel-access-card">
            <div class="custom-control-label w-100 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
              <div class="col-8" style="display: inline-flex;">
                <span class="fs-6">
                  <i class="cv-calculated me-1 fs-5"></i>about_cv_${i}
                </span>
              </div>
              <div class="col-4" style="display:flex;justify-content:end;align-items:center">
                <div class="form-check form-switch" style="min-height:0px">
                  <input class="form-check-input" type="checkbox" checked="checked" role="switch" />
                </div>
              </div>
            </div>
          </div>
        `;

        container.appendChild(col);
      }
    });
</script>

 *@

@model List<BCM.BusinessClasses.TableAccessInfo>

@{
    ViewData["Title"] = "Table Access";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .tabel-access-card {
        border-radius: 10px;
        border: 0px;
        background: #FFF9FB;
        padding: 12px 10px;
        box-shadow: 1px 5px 10px #e3dddd;
    }
    .card_ScrollBody {
        height: calc(100vh - 128px);
        overflow-y: auto;
    }

    input[type="checkbox"]:indeterminate {
        background-color: transparent !important;
        border-color: #ccc !important;
    }
   </style>

<div class="Page-Header">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">Table Access</h6>
        <div class="d-flex gap-3 justify-content-end align-items-end">
            <div class="input-group w-30">
                <input class="form-control" type="text" id="searchInput" placeholder="Search" />
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
            </div>
            <div class="form-check form-switch me-0 d-flex align-items-center gap-2 w-50" style="min-height:0px">
                <input class="form-check-input" type="checkbox" role="switch" id="selectAllSwitch">
                <label class="form-check-label" for="selectAllSwitch">Select All</label>
            </div>
            <button class="btn btn-primary btn-sm" id="tableAccessSaveBtn">Save</button>
        </div>
    </div>
</div>

<div id="noDataMessage" style="display: none; text-align: center; color: #888;">No data found</div>

<div class="Page-Condant card border-0" style="height: calc(100vh - 108px);">
    <div class="card-body card_ScrollBody">
        <div class="g-4 row row-cols-xl-4 row-cols-3" id="table-access">
            @if (Model != null && Model.Any())
            {
                foreach (var item in Model.OrderBy(x => x.TableName))
                {
                    var tableName = $"{item.TableName}";
                    var displayName = tableName.Length > 31 ? tableName.Substring(0, 29) + "..." : tableName;

                    <div class="col d-grid">
                        <div class="d-flex tabel-access-card">
                            <div class="custom-control-label w-100 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                                <div class="col-8 d-inline-flex">
                                    <span class="fs-6 d-flex align-items-center" title="@tableName">
                                        <i class="cv-calculated me-1 fs-5"></i>@displayName
                                    </span>
                                </div>
                                <div class="col-4 d-flex justify-content-end align-items-center">
                                    <div class="form-check form-switch" style="min-height:0px">
                                        <input class="form-check-input table-switch" type="checkbox" role="switch"
                                               data-id="@item.ID" @(item.IsChecked == true ? "checked" : "") />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>

    </div>
</div>
<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1080;">
    <!-- Error Toast -->
    <div id="errorToast" class="toast bg-danger text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center" data-bs-theme="dark">
            <div class="toast-body d-flex align-items-center gap-2">
                <i class="cv-error align-middle fs-5"></i>
                <span id="errorToastMessage">Something went wrong!</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>

    <!-- Success Toast -->
    <div id="successToast" class="toast bg-success text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center" data-bs-theme="dark">
            <div class="toast-body d-flex align-items-center gap-2">
                <i class="cv-success align-middle fs-5"></i>
                <span id="successToastMessage">Action completed successfully!</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>

    <!-- Warning Toast -->
    <div id="warningToast" class="toast bg-warning text-dark border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center" data-bs-theme="dark">
            <div class="toast-body d-flex align-items-center gap-2">
                <i class="cv-warning align-middle fs-5"></i>
                <span id="warningToastMessage">Please check your input!</span>
            </div>
            <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script src="~/js/tableaccess/tableaccess.js"></script>
@* <script>
    document.getElementById("saveBtn").addEventListener("click", function () {
        const tableStates = [];

        document.querySelectorAll(".table-switch").forEach(function (checkbox) {
            tableStates.push({
                ID: checkbox.getAttribute("data-id"),
                IsChecked: checkbox.checked
            });
        });

        console.log(tableStates); // Replace this with your AJAX save call
    });
</script>
 *@


@* <script>
    $(document).ready(function () {
        let tableAccessData = [];
        let originalData = [];

        // Initialize DataTable
        const table = $('#tableAccessTable').DataTable({
            "pageLength": 25,
            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            "order": [[1, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": [3] } // Disable sorting for checkbox column
            ]
        });

        // Load initial data
        loadTableAccessData();

        // Search functionality
        $('#searchInput').on('keyup', function () {
            table.search(this.value).draw();
        });

        // Select All functionality
        $('#selectAllSwitch').on('change', function () {
            const isChecked = $(this).is(':checked');
            $('.table-checkbox:visible').prop('checked', isChecked);
            updateSelectAllState();
        });

        // Individual checkbox change
        $(document).on('change', '.table-checkbox', function () {
            updateSelectAllState();
        });

        // Update Select All state based on individual checkboxes
        function updateSelectAllState() {
            const totalVisible = $('.table-checkbox:visible').length;
            const checkedVisible = $('.table-checkbox:visible:checked').length;

            if (checkedVisible === 0) {
                $('#selectAllSwitch').prop('indeterminate', false).prop('checked', false);
            } else if (checkedVisible === totalVisible) {
                $('#selectAllSwitch').prop('indeterminate', false).prop('checked', true);
            } else {
                $('#selectAllSwitch').prop('indeterminate', true).prop('checked', false);
            }
        }

        // Save functionality
        $('#saveBtn').on('click', function () {
            saveTableAccess();
        });

        function loadTableAccessData() {
            $.ajax({
                url: '@Url.Action("GetTableAccessData", "TableAccess")',
                type: 'GET',
                success: function (response) {
                    if (response.success) {
                        tableAccessData = response.data;
                        originalData = JSON.parse(JSON.stringify(response.data)); // Deep copy
                        updateTableDisplay();
                    } else {
                        showNotification('Error loading data: ' + response.message, 'error');
                    }
                },
                error: function () {
                    showNotification('Error loading table access data', 'error');
                }
            });
        }

        function updateTableDisplay() {
            table.clear();

            if (tableAccessData && tableAccessData.length > 0) {
                tableAccessData.forEach(function (item) {
                    const row = [
                        item.ID,
                        item.SchemaName || '',
                        item.TableName || '',
                        `<div class="form-check form-switch" style="min-height:0px">
                            <input class="form-check-input table-checkbox" type="checkbox"
                                   ${item.IsChecked ? 'checked' : ''}
                                   data-id="${item.ID}"
                                   role="switch" />
                         </div>`,
                        `<span class="badge ${item.IsActive ? 'bg-success' : 'bg-secondary'}">
                            ${item.IsActive ? 'Active' : 'Inactive'}
                         </span>`,
                        formatDate(item.CreatedDate),
                        formatDate(item.LastModifiedDate)
                    ];
                    table.row.add(row);
                });
            }

            table.draw();
            updateSelectAllState();
        }

        function saveTableAccess() {
            const updatedData = [];

            $('.table-checkbox').each(function () {
                const id = parseInt($(this).data('id'));
                const isChecked = $(this).is(':checked');
                const originalItem = originalData.find(item => item.ID === id);

                if (originalItem && originalItem.IsChecked !== isChecked) {
                    const updatedItem = { ...originalItem };
                    updatedItem.IsChecked = isChecked;
                    updatedData.push(updatedItem);
                }
            });

            if (updatedData.length === 0) {
                showNotification('No changes to save', 'info');
                return;
            }

            $('#saveBtn').prop('disabled', true).text('Saving...');

            $.ajax({
                url: '@Url.Action("UpdateTableAccess", "TableAccess")',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(updatedData),
                success: function (response) {
                    if (response.success) {
                        showNotification('Table access updated successfully', 'success');
                        loadTableAccessData(); // Reload data
                    } else {
                        showNotification('Error: ' + response.message, 'error');
                    }
                },
                error: function () {
                    showNotification('Error saving table access data', 'error');
                },
                complete: function () {
                    $('#saveBtn').prop('disabled', false).text('Save');
                }
            });
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-GB') + ' ' + date.toLocaleTimeString('en-GB', { hour12: false });
        }

        function showNotification(message, type) {
            // You can implement your preferred notification system here
            // For now, using a simple alert
            if (type === 'success') {
                alert('Success: ' + message);
            } else if (type === 'error') {
                alert('Error: ' + message);
            } else {
                alert(message);
            }
        }
    });
</script> *@

