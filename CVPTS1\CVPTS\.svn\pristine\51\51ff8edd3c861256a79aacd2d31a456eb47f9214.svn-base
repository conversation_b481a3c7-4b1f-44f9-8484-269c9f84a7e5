﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Data;
using System.Diagnostics;
using System.Text;
namespace BCM.UI.Areas.BCMCommon.Controllers;
[Area("BCMConfiguration")]
public class ConfigSettingsController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly BCMMail _BCMMail;
    readonly CVLogger _CVLogger;
    public ConfigSettingsController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, BCMMail BCMMail) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _BCMMail = BCMMail;
    }

    public IActionResult ConfigSettings()
    {
        VaultSettings objVaultSettings = new VaultSettings();
        try
        {
            ViewBag.LanguageList = new SelectList(_Utilities.GetAllLanguage(), "LanguageName", "UICulture");
            ViewBag.OrgGroup = new SelectList(_Utilities.GetOrgGroupList(), "OrgGroupID", "OrganizationGroupName");
            ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString()), "Id", "OrganizationName");
            ViewBag.OrgGropID = _UserDetails.OrgGroupID.ToString();
            ViewBag.OrgID = _UserDetails.OrgID.ToString();
            ViewBag.Months = new SelectList(_Utilities.PopulateMonths(), "MonthId", "MonthName");
            objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);

            List<string> strList = new List<string>();
            string AllowedExt = objVaultSettings.AllowedExtension;
            if (AllowedExt != null)
            {
                string[] values = AllowedExt.Split(',');

                foreach (string value in values)
                {
                    strList.Add(value.Trim());
                }
            }
            ViewBag.CheckExt = strList;
            int i = Convert.ToInt32(objVaultSettings.WrongLoginAttempts) + 1;
            /*objVaultSettings.WrongLoginAttempts = "0";*/ //i.ToString();

            List<JobConfigMasterInfo> objJobMaster = new List<JobConfigMasterInfo>();

            objJobMaster = _ProcessSrv.GetJobConfigMaster_AllJobs(Convert.ToInt32(_UserDetails.OrgID));

            ViewBag.Jobmaster = objJobMaster;


            #region Extension Master


            List<ExtensionMasterInfo> ObjMaster = _ProcessSrv.GetExtensionMasterList();

            ViewBag.Extensionmaster = ObjMaster;




            #endregion

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(objVaultSettings);
    }

    [HttpPost]
    public JsonResult SaveAuthentication(int iTwoFactorIsActive, string iTwoFactorBySMS, string iTwoFactorByMail, string iTwoFactorExpirationTime)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettings = new VaultSettings();
            objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);
            objVaultSettings.TwoFactorIsActive = iTwoFactorIsActive;
            objVaultSettings.TwoFactorByMail = iTwoFactorByMail.ToString();
            objVaultSettings.TwoFactorBySMS = iTwoFactorBySMS.ToString();
            objVaultSettings.TwoFactorExpirationTime = iTwoFactorExpirationTime.ToString();
            bSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettings);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Authentication Setting Saved Successfully." : "Authentication Setting Failed To Save." });
    }

    [HttpPost]
    public JsonResult SaveSmsServerSettings([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettingsInfo = new VaultSettings();
            objVaultSettingsInfo = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);
            objVaultSettingsInfo.SMSUserName = objVaultSettings.SMSUserName;
            objVaultSettingsInfo.SenderID = objVaultSettings.SenderID;
            objVaultSettingsInfo.SMSUrl = objVaultSettings.SMSUrl;
            objVaultSettingsInfo.CustomerID = objVaultSettings.CustomerID;
            objVaultSettingsInfo.AllowAnyNumber = objVaultSettings.AllowAnyNumber;
            objVaultSettingsInfo.SMSUserPassword = objVaultSettings.SMSUserPassword;
            objVaultSettingsInfo.DefaultSMSNo = objVaultSettings.DefaultSMSNo;
            objVaultSettingsInfo.CallBackURL = objVaultSettings.CallBackURL;
            objVaultSettingsInfo.ReplyNumber = objVaultSettings.ReplyNumber;
            objVaultSettingsInfo.InboxID = objVaultSettings.InboxID;
            objVaultSettingsInfo.OrgID = _UserDetails.OrgID;
            objVaultSettingsInfo.ChangedBy = _UserDetails.UserID;
            bSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettingsInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "SMS Server Setting Saved Successfully." : "SMS Server Setting Failed To Save." });
    }


    [HttpPost]
    public JsonResult SaveSMTPServerDetails([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettingsInfo = new VaultSettings();
            objVaultSettingsInfo = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);
            objVaultSettingsInfo.SMTPServer = objVaultSettings.SMTPServer;
            objVaultSettingsInfo.SMTPUserName = objVaultSettings.SMTPUserName;
            objVaultSettingsInfo.SMTPPassword = objVaultSettings.SMTPPassword;
            objVaultSettingsInfo.OutGoingMailPort = objVaultSettings.OutGoingMailPort;
            objVaultSettingsInfo.DefaultMailID = objVaultSettings.DefaultMailID;
            objVaultSettingsInfo.MailFrom = objVaultSettings.MailFrom;
            objVaultSettingsInfo.EnableSSL = objVaultSettings.EnableSSL;
            bSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettingsInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "SMTP Server Setting Saved Successfully." : "SMTP Server Setting Failed To Save." });
    }

    [HttpPost]
    public JsonResult SaveApplicationSettings([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettingsInfo = new VaultSettings();
            objVaultSettingsInfo = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);
            objVaultSettingsInfo.ApplicationDirectory = objVaultSettings.ApplicationDirectory;
            objVaultSettingsInfo.DevMode = objVaultSettings.DevMode;
            objVaultSettingsInfo.PDFFilesPath = objVaultSettings.PDFFilesPath;
            objVaultSettingsInfo.AttachmentFolder = objVaultSettings.AttachmentFolder;
            objVaultSettingsInfo.DateFormat = objVaultSettings.DateFormat;
            bSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettingsInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Application Setting Saved Successfully." : "Application Setting Failed To Save." });
    }


    [HttpPost]
    public JsonResult SaveUserSettings([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettingsInfo = new VaultSettings();
            objVaultSettingsInfo = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);
            objVaultSettingsInfo.VerificationInterval = objVaultSettings.VerificationInterval;
            objVaultSettingsInfo.VerificationDaysPrior = objVaultSettings.VerificationDaysPrior;
            objVaultSettingsInfo.VerificationStopDays = objVaultSettings.VerificationStopDays;
            bSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettingsInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Call Tree Verification Saved Successfully." : "Call Tree Verification Failed To Save." });
    }

    [HttpPost]
    public JsonResult SaveAutomateRevisionReminder([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettingsInfo = new VaultSettings();
            objVaultSettingsInfo = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);
            objVaultSettingsInfo.PlanRevisionDaysBefore = objVaultSettings.PlanRevisionDaysBefore;
            objVaultSettingsInfo.PlanRevisionDaysAfter = objVaultSettings.PlanRevisionDaysAfter;
            bSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettingsInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Revision Reminder Saved Successfully." : "Revision Reminder Failed To Save." });
    }

    [HttpPost]
    public IActionResult SaveAttachmentSettings(List<string> chkExtentions, string FileSize)
    {
        bool bIsSuccess = false;
        try
        {
            string commaSeparatedList = chkExtentions.Aggregate((a, x) => a + "," + x);

            VaultSettings objVaultSettingsInfo = new VaultSettings();
            objVaultSettingsInfo = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);

            objVaultSettingsInfo.FileSize = FileSize;
            objVaultSettingsInfo.AllowedExtension = commaSeparatedList.ToString();

            bIsSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettingsInfo);

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

        }
        return RedirectToAction("ConfigSettings");
    }

    [HttpPost]
    public JsonResult SaveCultureSettings([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {

            VaultSettings objVaultSettingsInfo = new VaultSettings();
            objVaultSettingsInfo = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);

            objVaultSettingsInfo.Culture = objVaultSettings.Culture;

            bSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettingsInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Culture Saved Successfully." : "Culture Failed To Save." });
    }

    [HttpPost]
    public JsonResult SaveDateTimeFormatSettings([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettingsInfo = new VaultSettings();
            objVaultSettingsInfo = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);
            objVaultSettingsInfo.DateFormat = objVaultSettings.DateFormat;
            objVaultSettingsInfo.DateTimeFormat = objVaultSettings.DateTimeFormat;
            bSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettingsInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Date Format Saved Successfully." : "Date Format Failed To Save." });
    }

    [HttpPost]
    public JsonResult SavePasswordExpirationSettings([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettingsInfo = new VaultSettings();
            objVaultSettingsInfo = _ProcessSrv.GetVaultSettingsByOrgID(_UserDetails.OrgID);
            objVaultSettingsInfo.PasswordExpiration = objVaultSettings.PasswordExpiration;
            objVaultSettingsInfo.WrongLoginAttempts = objVaultSettings.WrongLoginAttempts;
            objVaultSettingsInfo.DormantAccountDeactivationDays = objVaultSettings.DormantAccountDeactivationDays;
            objVaultSettingsInfo.PasswordHistory = objVaultSettings.PasswordHistory;
            bSuccess = _ProcessSrv.VaultSettingsSave(objVaultSettingsInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Password Expiration Setting Saved Successfully." : "Password Expiration Setting Failed To Save." });
    }


    [HttpPost]
    public JsonResult SendTestSMS([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettingsInfo = new VaultSettings();
            string MoblieNo = string.Empty;
            string MailBody = "This Is Test SMS. - Continuity Vault.";
            string ErrorMessage = string.Empty;
            // BCPSms.TestSMSSend1(txtDefaultMailID.Text,tx
            ErrorMessage = BCPSms.TestSMSSend(objVaultSettings.DefaultSMSNo, MailBody, objVaultSettings.SMSUserName, objVaultSettings.CustomerID, objVaultSettings.SMSUrl, objVaultSettings.SMSUserPassword, objVaultSettings.OrgID.ToString(), objVaultSettings.SenderID);
            bSuccess = ErrorMessage.Length == 0 ? true : false;            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Test SMS Sent Successfully." : "Test SMS Failed To Sent." });
    }

    [HttpPost]
    public JsonResult SendTestMail([FromBody] VaultSettings objVaultSettings)
    {
        bool bSuccess = false;
        try
        {
            VaultSettings objVaultSettingsInfo = new VaultSettings();
            string MailTo = string.Empty;
            string MailBody = "This Is Test Mail.<br/> - Continuity Vault.";
            string ErrorMessage = string.Empty;
            bool Enablessl = false;

            if (objVaultSettings.EnableSSL == "True")
            {
                Enablessl = true;
            }

            ErrorMessage = _BCMMail.SendTestMail("Test Mail", MailBody, objVaultSettings.DefaultMailID, objVaultSettings.SMTPUserName, objVaultSettings.SMTPPassword,
                 objVaultSettings.SMTPServer, objVaultSettings.OutGoingMailPort, objVaultSettings.MailFrom, Enablessl, objVaultSettings.OrgID.ToString());
            bSuccess = ErrorMessage.Length == 0 ? true : false;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Test Mail Sent Successfully." : "Test Mail Failed To Sent." });
    }

    [HttpPost]
    public JsonResult SaveJobMaster(string OrgID, string JobID, string executionSetting, string txtMinutes, string txtHours, 
        string txtDay, string ddlHour, string ddlMinutes, string ddlMonths = null, string monthTimeType = null, 
        string dayOfMonth = null, string dayOfMonthHours = null, string dayOfMonthMinutes = null, 
        string selectedWeekdays = null, string weekdayHours = null, string weekdayMinutes = null)
    {
        bool bSuccess = false;
        try
        {            
            List<JobConfigMasterInfo> objJobMasterColl = new List<JobConfigMasterInfo>();
            objJobMasterColl = _ProcessSrv.GetJobConfigMaster_AllJobsByOrgID(_UserDetails.OrgID);
            objJobMasterColl = objJobMasterColl.Where(x => x.JobID == Convert.ToInt32(JobID)).ToList();
            JobConfigMasterInfo objJobConfigmasterInfo = new JobConfigMasterInfo();
            objJobConfigmasterInfo.JobID = objJobMasterColl[0].JobID;
            objJobConfigmasterInfo.JobConfigID = objJobMasterColl[0].JobConfigID;
            objJobConfigmasterInfo.JobNameText = objJobMasterColl[0].JobNameText;
            objJobConfigmasterInfo.JobNameValue = objJobMasterColl[0].JobNameValue;
            objJobConfigmasterInfo.OrgID = 1;
            objJobConfigmasterInfo.IsActive = 1;
            objJobConfigmasterInfo.CreatedBy = String.IsNullOrEmpty(_UserDetails.UserID.ToString()) ? 0 : _UserDetails.UserID;
            objJobConfigmasterInfo.UpdatedBy = String.IsNullOrEmpty(_UserDetails.UserID.ToString()) ? 0 : _UserDetails.UserID;
            
            // Create cron expression based on execution setting
            if (executionSetting == "Month(s)")
            {
                // Handle month execution setting
                if (monthTimeType == "1") // Day of Month
                {
                    // Format: 0 MM HH DD MM ? - At HH:MM on day DD of every month
                    objJobConfigmasterInfo.CronExpression = string.Format("0 {0} {1} {2} {3} ?", 
                        dayOfMonthMinutes, dayOfMonthHours, dayOfMonth, ddlMonths);
                }
                else if (monthTimeType == "2") // Weekdays
                {
                    if (!string.IsNullOrEmpty(selectedWeekdays))
                    {
                        // Format for multiple weekdays: 0 MM HH ? * MON,TUE,WED
                        string[] weekdays = selectedWeekdays.Split(',');
                        string dayOfWeekExpression = ConvertWeekdaysToCronFormat(weekdays);
                        
                        objJobConfigmasterInfo.CronExpression = string.Format("0 {0} {1} ? {2} {3}", 
                            weekdayMinutes, weekdayHours, ddlMonths, dayOfWeekExpression);
                    }
                    else
                    {
                        // Default to all days if no weekdays selected
                        objJobConfigmasterInfo.CronExpression = string.Format("0 {0} {1} ? {2} *", 
                            weekdayMinutes, weekdayHours, ddlMonths);
                    }
                }
            }
            else
            {
                // Use existing method for other execution settings
                objJobConfigmasterInfo.CronExpression = CreateCronExpression(JobID, executionSetting, txtMinutes, txtHours, txtDay, ddlHour, ddlMinutes);
            }
            bSuccess = _ProcessSrv.JobConfigMaster_SaveColl(objJobConfigmasterInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
        }
        return Json(new { success = bSuccess, message = bSuccess ? "Job Setting Saved Successfully." : "Job Setting Failed To Save." });
    }

    public string CreateCronExpression(string JobID, string executionSetting, string txtMinutes, string txtHours, string txtDay, string ddlHour, string ddlMinutes)
    {
        string Expression = string.Empty;
        try
        {

            if (executionSetting == "Minute(s)")
            {
                Expression = string.Format("0 0/{0} * * * ?", txtMinutes);
            }
            else if (executionSetting == "Hour(s)")
            {
                Expression = string.Format("0 {0} 0/{1} * * ?", txtMinutes, txtHours);
            }
            else
            {
                Expression = string.Format("0 {0} {1} 1/{2} * ?", ddlMinutes, ddlHour, txtDay);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Expression;
    }

    public JsonResult GetJobDetailsByJobID(string JobID)
    {
        JobConfigMasterInfo objJobConfigmasterInfo = new JobConfigMasterInfo();
        try
        {
            List<JobConfigMasterInfo> objJobMasterColl = new List<JobConfigMasterInfo>();

            objJobMasterColl = _ProcessSrv.GetJobConfigMaster_AllJobsByOrgID(Convert.ToInt32("1"));

            objJobMasterColl = objJobMasterColl.Where(x => x.JobID == Convert.ToInt32(JobID)).ToList();
            objJobConfigmasterInfo = objJobMasterColl[0];

            string cronexpression = objJobConfigmasterInfo.CronExpression;

            // Check if it's a monthly schedule
            if (!string.IsNullOrEmpty(cronexpression) && cronexpression.Contains("?"))
            {
                string[] cronParts = cronexpression.Split(' ');
                if (cronParts.Length >= 6)
                {
                    // Format: seconds minutes hours day-of-month month day-of-week
                    string minutes = cronParts[1];
                    string hours = cronParts[2];
                    string dayOfMonth = cronParts[3];
                    string month = cronParts[4];
                    string dayOfWeek = cronParts[5];

                    // Check if it's a month-based schedule with specific weekdays
                    if (dayOfWeek != "?" && dayOfWeek != "*" && dayOfMonth == "?")
                    {
                        objJobConfigmasterInfo.ExecutionSetting = "Month(s)";
                        objJobConfigmasterInfo.MonthTimeType = "2"; // Weekdays
                        objJobConfigmasterInfo.SelectedMonth = month;
                        objJobConfigmasterInfo.WeekdayHours = hours;
                        objJobConfigmasterInfo.WeekdayMinutes = minutes;
                        objJobConfigmasterInfo.SelectedWeekdays = ConvertCronWeekdaysToNumbers(dayOfWeek);
                        return Json(objJobConfigmasterInfo);
                    }
                    // Check if it's a month-based schedule with specific day of month
                    else if (dayOfMonth != "?" && dayOfMonth != "*" && dayOfWeek == "?")
                    {
                        objJobConfigmasterInfo.ExecutionSetting = "Month(s)";
                        objJobConfigmasterInfo.MonthTimeType = "1"; // Day of Month
                        objJobConfigmasterInfo.SelectedMonth = month;
                        objJobConfigmasterInfo.DayOfMonth = dayOfMonth;
                        objJobConfigmasterInfo.DayOfMonthHours = hours;
                        objJobConfigmasterInfo.DayOfMonthMinutes = minutes;
                        return Json(objJobConfigmasterInfo);
                    }
                }
            }

            // Continue with existing logic for non-monthly schedules
            if (cronexpression != string.Empty)
            {
                string[] parts = cronexpression.Split('/');

                if (parts.Length > 1)
                {
                    if (parts[1].Contains(" * * * ?"))
                    {
                        objJobConfigmasterInfo.ExecutionSetting = "Minute(s)";
                        //    rdblstCronExpressionNew.SelectedValue = "Minute(s)";
                        //    Panel_MinuiteNew.Visible = true;
                        //    Panel_MinuiteNew.Enabled = true;

                        var m = cronexpression.Substring(cronexpression.LastIndexOf('/') + 1, cronexpression.IndexOf('*') - (cronexpression.LastIndexOf('/') + 1));
                        m = m.Trim();
                        objJobConfigmasterInfo.EveryMinute = m;
                    }
                    else if (parts[1].Contains("* * ?"))
                    {
                        objJobConfigmasterInfo.ExecutionSetting = "Hour(s)";
                        //    Panel_HourlyNew.Visible = true;
                        //    Panel_HourlyNew.Enabled = true;

                        var t = cronexpression.Substring(1, 3);
                        var w = cronexpression.Substring(cronexpression.LastIndexOf('/') + 1, cronexpression.IndexOf('*') - (cronexpression.LastIndexOf('/') + 1));
                        t = t.Trim();
                        w = w.Trim();
                        objJobConfigmasterInfo.EveryHour = w;
                        objJobConfigmasterInfo.EveryHourlyMinute = t;
                    }
                    else
                    {
                        objJobConfigmasterInfo.ExecutionSetting = "Day(s)";
                        //    Panel_DailyNew.Visible = true;
                        //    Panel_DailyNew.Enabled = true;
                        var d = cronexpression.Substring(cronexpression.LastIndexOf('/') + 1, cronexpression.IndexOf('*') - (cronexpression.LastIndexOf('/') + 1));

                        objJobConfigmasterInfo.EveryDaily = d.Trim();


                        var th = cronexpression.Substring(1, 3);
                        var tm = cronexpression.Substring(4, 4);
                        th = th.Trim();
                        tm = tm.Trim();
                        objJobConfigmasterInfo.EveryHour = tm;
                        objJobConfigmasterInfo.EveryMinute = th;
                    }
                }
                else
                {
                    // Handle simple cron expressions without '/'
                    string[] cronParts = cronexpression.Split(' ');
                    if (cronParts.Length >= 6)
                    {
                        objJobConfigmasterInfo.ExecutionSetting = "Day(s)";
                        objJobConfigmasterInfo.EveryMinute = cronParts[1]; // minutes
                        objJobConfigmasterInfo.EveryHour = cronParts[2];   // hours
                        objJobConfigmasterInfo.EveryDaily = "1";           // default to 1 day
                    }
                }
            }
            else
            {
                objJobConfigmasterInfo.EveryMinute = string.Empty;
                objJobConfigmasterInfo.EveryHour = string.Empty;
                objJobConfigmasterInfo.EveryHourlyMinute = string.Empty;
                objJobConfigmasterInfo.EveryDaily = string.Empty;

                objJobConfigmasterInfo.ExecutionSetting = "Minute(s)";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(objJobConfigmasterInfo);
    }

    // Helper method to convert cron weekday format to numeric values
    private string ConvertCronWeekdaysToNumbers(string cronWeekdays)
    {
        if (string.IsNullOrEmpty(cronWeekdays) || cronWeekdays == "*")
            return "";
        
        var weekdayNumbers = new List<string>();
        string[] days = cronWeekdays.Split(',');
        
        foreach (var day in days)
        {
            switch (day.Trim())
            {
                case "SUN": weekdayNumbers.Add("0"); break;
                case "MON": weekdayNumbers.Add("1"); break;
                case "TUE": weekdayNumbers.Add("2"); break;
                case "WED": weekdayNumbers.Add("3"); break;
                case "THU": weekdayNumbers.Add("4"); break;
                case "FRI": weekdayNumbers.Add("5"); break;
                case "SAT": weekdayNumbers.Add("6"); break;
            }
        }
        
        return string.Join(",", weekdayNumbers);
    }

    // Helper method to convert weekday numbers to cron format
    private string ConvertWeekdaysToCronFormat(string[] weekdays)
    {
        var cronDays = new List<string>();
        
        foreach (var day in weekdays)
        {
            if (int.TryParse(day, out int dayNum))
            {
                switch (dayNum)
                {
                    case 0: cronDays.Add("SUN"); break;
                    case 1: cronDays.Add("MON"); break;
                    case 2: cronDays.Add("TUE"); break;
                    case 3: cronDays.Add("WED"); break;
                    case 4: cronDays.Add("THU"); break;
                    case 5: cronDays.Add("FRI"); break;
                    case 6: cronDays.Add("SAT"); break;
                }
            }
        }
        
        return cronDays.Count > 0 ? string.Join(",", cronDays) : "*";
    }

}

