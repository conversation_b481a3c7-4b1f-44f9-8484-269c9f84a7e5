﻿// Dashboard Storage Management

class DashboardStorage {
    constructor() {
        this.storageKey = 'dashboard-builder-state';
        this.undoStack = [];
        this.redoStack = [];
        this.maxUndoSteps = 50;
        this.clipboard = null;
    }

    // Save current dashboard state
    saveState() {
        const widgets = [];
        $('.grid-stack-item').each(function () {
            const $gridItem = $(this);
            const $widget = $gridItem.find('.dashboard-widget');
            const gridData = $gridItem.data('_gridstack_node');

            if ($widget.length && gridData) {
                const widget = {
                    id: $gridItem.attr('gs-id'),
                    type: $widget.data('widget-type'),
                    title: $widget.find('.widget-header h5').text(),
                    x: gridData.x,
                    y: gridData.y,
                    w: gridData.w,
                    h: gridData.h,
                    data: $widget.data('widget-data')
                };
                widgets.push(widget);
            }
        });

        const state = {
            widgets: widgets,
            timestamp: new Date().toISOString(),
            version: '2.0' // Updated for Gridstack
        };

        try {
            localStorage.setItem(this.storageKey, JSON.stringify(state));
           // this.showNotification('Dashboard saved successfully!', 'success');
            return true;
        } catch (error) {
           // console.error('Failed to save dashboard:', error);
           // this.showNotification('Failed to save dashboard. Storage might be full.', 'error');
            return false;
        }
    }

    // Load dashboard state
    loadState() {
        try {
            const savedState = localStorage.getItem(this.storageKey);
            if (!savedState) {
               // this.showNotification('No saved dashboard found. Loading default layout.', 'info');
                return null;
            }

            const state = JSON.parse(savedState);
            //this.showNotification('Dashboard loaded successfully!', 'success');
            return state;
        } catch (error) {
            //console.error('Failed to load dashboard:', error);
           // this.showNotification('Failed to load dashboard. Using default layout.', 'error');
            return null;
        }
    }

    // Add state to undo stack
    pushUndoState() {
        const currentState = this.getCurrentState();
        this.undoStack.push(currentState);

        // Limit undo stack size
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }

        // Clear redo stack when new action is performed
        this.redoStack = [];

        this.updateUndoRedoButtons();
    }

    // Get current dashboard state
    getCurrentState() {
        const widgets = [];
        console.log('Getting current state...');
        console.log('Grid items found:', $('.grid-stack-item').length);

        $('.grid-stack-item').each(function () {
            const $gridItem = $(this);
            let $widget = $gridItem.find('.dashboard-widget');

            // If widget not found, try alternative selectors
            if ($widget.length === 0) {
                $widget = $gridItem.children('.dashboard-widget');
            }
            if ($widget.length === 0) {
                $widget = $gridItem.find('[data-widget-type]');
            }

            const gridData = $gridItem.data('_gridstack_node');

            console.log('Grid item:', $gridItem[0]);
            console.log('Widget found:', $widget.length, $widget[0]);
            console.log('Grid data:', gridData);

            if ($widget.length && gridData) {
                const widget = {
                    id: $gridItem.attr('gs-id') || $gridItem.attr('id'),
                    type: $widget.data('widget-type') || $widget.attr('data-widget-type'),
                    title: $widget.find('.widget-header h5').text() || $widget.find('h5').first().text() || 'Untitled Widget',
                    x: gridData.x || 0,
                    y: gridData.y || 0,
                    w: gridData.w || 4,
                    h: gridData.h || 3,
                    data: $widget.data('widget-data') || $widget.attr('data-widget-data') || 0
                };
                console.log('Adding widget to export:', widget);
                widgets.push(widget);
            } else {
                console.warn('Skipping grid item - widget or grid data missing');
            }
        });

        console.log('Total widgets for export:', widgets.length);
        return { widgets: widgets };
    }

    // Undo last action
    undo() {
        if (this.undoStack.length === 0) {
            this.showNotification('Nothing to undo', 'info');
            return;
        }

        // Save current state to redo stack
        const currentState = this.getCurrentState();
        this.redoStack.push(currentState);

        // Get previous state
        const previousState = this.undoStack.pop();
        this.restoreState(previousState);

        this.updateUndoRedoButtons();
        this.showNotification('Action undone', 'success');
    }

    // Redo last undone action
    redo() {
        if (this.redoStack.length === 0) {
            this.showNotification('Nothing to redo', 'info');
            return;
        }

        // Save current state to undo stack
        const currentState = this.getCurrentState();
        this.undoStack.push(currentState);

        // Get next state
        const nextState = this.redoStack.pop();
        this.restoreState(nextState);

        this.updateUndoRedoButtons();
        this.showNotification('Action redone', 'success');
    }

    // Restore dashboard to a specific state
    restoreState(state) {
        // Clear current widgets
        window.WidgetManager.grid.removeAll();

        // Restore widgets
        state.widgets.forEach(widgetData => {
            window.WidgetManager.createWidget(
                widgetData.type,
                widgetData.title,
                {
                    x: widgetData.x,
                    y: widgetData.y,
                    w: widgetData.w,
                    h: widgetData.h
                },
                widgetData.data,
                widgetData.id
            );
        });
    }

    // Copy widget to clipboard
    copyWidget(widgetId) {
        const $gridItem = $(`.grid-stack-item[gs-id="${widgetId}"]`);
        const $widget = $gridItem.find('.dashboard-widget');
        if ($widget.length === 0) return;

        const gridData = $gridItem.data('_gridstack_node');
        this.clipboard = {
            type: $widget.data('widget-type'),
            title: $widget.find('.widget-header h5').text(),
            w: gridData.w,
            h: gridData.h,
            data: $widget.data('widget-data')
        };

        this.showNotification('Widget copied to clipboard', 'success');
        $('#pasteBtn').prop('disabled', false);
    }

    // Paste widget from clipboard
    pasteWidget() {
        if (!this.clipboard) {
            this.showNotification('Nothing to paste', 'info');
            return;
        }

        // Calculate paste position (offset from original)
        const gridOptions = {
            x: 0,
            y: 0,
            w: this.clipboard.w,
            h: this.clipboard.h
        };

        // Create new widget
        const newId = 'widget-' + Date.now();
        window.WidgetManager.createWidget(
            this.clipboard.type,
            this.clipboard.title + ' (Copy)',
            gridOptions,
            this.clipboard.data,
            newId
        );

        this.showNotification('Widget pasted successfully', 'success');
    }

    // Update undo/redo button states
    updateUndoRedoButtons() {
        $('#undoBtn').prop('disabled', this.undoStack.length === 0);
        $('#redoBtn').prop('disabled', this.redoStack.length === 0);
    }

    // Show notification to user
    showNotification(message, type = 'info') {
        // Remove existing notifications
        $('.notification').remove();

        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'info': 'alert-info',
            'warning': 'alert-warning'
        }[type] || 'alert-info';

        const notification = $(`
            <div class="notification alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 80px; right: 20px; z-index: 9999; min-width: 300px;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);

        $('body').append(notification);

        // Auto-hide after 3 seconds
        //setTimeout(() => {
        //    notification.alert('close');
        //}, 3000);
    }

    // Export dashboard as JSON
    exportDashboard() {
        console.log('Starting dashboard export...');
        const state = this.getCurrentState();
        console.log('Export state:', state);

        if (!state.widgets || state.widgets.length === 0) {
            this.showNotification('No widgets to export. Add some widgets first.', 'warning');
            return;
        }

        const dataStr = JSON.stringify(state, null, 2);
        console.log('Export JSON:', dataStr);

        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `dashboard-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        this.showNotification(`Dashboard exported successfully (${state.widgets.length} widgets)`, 'success');
    }

    // Import dashboard from JSON file
    importDashboard(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const state = JSON.parse(e.target.result);
                this.pushUndoState(); // Save current state before importing
                this.restoreState(state);
                this.showNotification('Dashboard imported successfully', 'success');
            } catch (error) {
                //console.error('Failed to import dashboard:', error);
                this.showNotification('Failed to import dashboard. Invalid file format.', 'error');
            }
        };
        reader.readAsText(file);
    }

    // Clear all data
    clearStorage() {
        if (confirm('Are you sure you want to clear all saved data? This cannot be undone.')) {
            localStorage.removeItem(this.storageKey);
            this.undoStack = [];
            this.redoStack = [];
            this.clipboard = null;
            this.updateUndoRedoButtons();
           // this.showNotification('All data cleared', 'success');
        }
    }
}

// Initialize storage manager
window.DashboardStorage = new DashboardStorage();
