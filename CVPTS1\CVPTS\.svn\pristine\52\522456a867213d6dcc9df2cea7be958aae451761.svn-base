﻿@model BCM.BusinessClasses.ProcessBIALegalAndRegulatory
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@* <form asp-action="DeleteBIALegalAndRegularty" method="post">
    <div>
        <input type="hidden" asp-for="ID" />
    </div>
    <div class="modal-header d-grid text-center">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.DependencyReport</span>" ?</span>
    </div>
    <img src="~/img/isomatric/delete.svg" width="260" /> 
    <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>
 *@



<form asp-action="DeleteBIALegalAndRegularty" method="post">
    <div>
        <input type="hidden" asp-for="ID" />
        <input type="hidden" asp-for="DependencyReport" />
    </div>
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    <div class="modal-body d-grid px">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.DependencyReport</span>" ?</span>
    </div>
    <div class="modal-footer justify-content-center p-0">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>

