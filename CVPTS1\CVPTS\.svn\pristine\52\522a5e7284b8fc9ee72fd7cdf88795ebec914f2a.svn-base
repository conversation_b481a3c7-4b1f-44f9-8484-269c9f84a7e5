﻿@model BCM.BusinessClasses.ReviewReportMaster
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
<form id="editManagementReviewForm" asp-action="EditManagementReview" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="row">
        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group">
                <label class="form-label">Report Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-name"></i></span>
                    <input type="hidden" asp-for="ReportID" />
                    <input type="text" placeholder="Report Name" asp-for="ReportName"  class="form-control" required />
                </div>
                <div class="invalid-feedback">Enter Report Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Owner</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-owner"></i></span>
@*                        <select class="form-select form-select-sm">
                                    <option></option>
                                    <option>cerebrum it park</option>
                                </select> *@
                    <select class="form-select form-control" autocomplete="off" id="ddlResource" aria-label="Default select example" asp-for="OwnerID" asp-items="@(new SelectList(ViewBag.ResourceList,"ResourceId","ResourceName"))" required>
                        <option selected disabled value="">-- Select Owner --</option>
                    </select>

                </div>
                <div class="invalid-feedback">Select Owner</div>
            </div>
        </div>
        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group">
                <label class="form-label">Plan Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-calendar"></i></span>
                    <input type="date" class="form-control" asp-for="PlanDate" required />
                </div>
                <div class="invalid-feedback">Plan Date</div>
            </div>
            <div class="form-group">
                <label class="form-label">Description</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <textarea class="form-control" style="height:0px" asp-for="Description"></textarea>
                </div>
                <div class="invalid-feedback">Description</div>
            </div>


        </div>

    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm" id="btnSave">Save</button>
        </div>
    </div>
</form>


<script>
    $(document).ready(function () {
        //console.log("EditManagementReview form ready");

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for EditManagementReview form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('editManagementReviewForm');
                if (!form) {
                    console.error("Form not found with ID: editManagementReviewForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Custom function to add required field indicators without duplicates
                function addCustomRequiredFieldIndicators(form) {
                    const requiredInputs = form.querySelectorAll('[required]');

                    requiredInputs.forEach(function(input) {
                        const label = form.querySelector('label[for="' + input.id + '"]');
                        if (label) {
                            // Check if asterisk already exists
                            if (!label.querySelector('.required-asterisk')) {
                                // Create asterisk element
                                const asterisk = document.createElement('span');
                                asterisk.className = 'required-asterisk text-danger';
                                asterisk.textContent = ' *';
                                asterisk.style.fontWeight = 'bold';

                                // Append asterisk to label
                                label.appendChild(asterisk);
                                console.log("Added asterisk to label for:", input.id);
                            }
                        }
                    });
                }

                // Use custom function instead of the default one
                addCustomRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    if (!isValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }
    });
</script>