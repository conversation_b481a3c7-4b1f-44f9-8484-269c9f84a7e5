﻿@model IEnumerable<BCM.BusinessClasses.KPIMeasurementMasters>
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int iIndex = 0;
}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">KPI Measurement Master</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
       

        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        @* <a asp-controller="Unit" asp-action="AddUnit" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</a> *@
        <button type="button" class="btn icon-btn btn-primary btn-sm btnCreate" data-bs-toggle="modal" data-bs-target="#CreateModal"> <i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Objective Measured By</th>
                <th>Effectiveness Criteria</th>
                <th>Target</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>

            @foreach (var KPIItems in Model)
            {
                iIndex++;
                <tr>
                    <td>
                        @* @KPIItems.ID *@
                        @iIndex
                </td>
                <td>
                        @KPIItems.Objective
                </td>
                <td>
                        @KPIItems.Effectiveness
                </td>
                <td>
                        @KPIItems.Target
                </td>
                <td>
                        <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@KPIItems.ID"><i class="cv-edit" title="Edit"></i></span>
                        <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal" data-id="@KPIItems.ID"><i class="cv-delete text-danger" title="Delete"></i></span>
                </td>
            </tr>

            }
           @*  <tr>
                <td>
                    2
                </td>
                <td>
                    Management Review
                </td>
                <td>
                    Yes-5 No - 0
                </td>
                <td>
                    100%
                </td>
                <td>
                    <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                    <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                </td>
            </tr>
            <tr>
                <td>
                    3
                </td>
                <td>
                    RA
                </td>
                <td>
                    Yes-5 Review in progress - 2.5 No - 0
                </td>
                <td>
                    100%
                </td>
                <td>
                    <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                    <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                </td>
            </tr>
            <tr>
                <td>
                4
                </td>
                <td>
                    Plan review
                </td>
                <td>
                    Yes, reviewed and approved-5 Reviewed, however not approved - 2.5 No - 0
                </td>
                <td>
                    100%
                </td>
                <td>
                    <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                    <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                </td>
            </tr> *@
        </tbody>
    </table>




    <!-- Configuration Modal -->
@*     <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">KPI Measurement Master Configuration</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="configurationBody">
                    <div class="wizard-content">
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div> *@
</div>


<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">
                    KPI Measurement Master Configuration
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="configurationBody">
            </div>
        </div>
    </div>
</div>


<!--End Configuration Modal -->
<!-- Delete Modal -->
@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/delete.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div> *@
<!-- End Delete Modal -->

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div>


@section Scripts {
    <script>
        $(document).ready(function () {


            $(document).on('click', '.btnCreate', function () {
                debugger;
                $.get('@Url.Action("AddKPIMeasurementMaster", "KPIMeasurementMaster")', function (data) {
                    $('#configurationBody').html(data);
                    $('#CreateModal').modal('show');
                });
            });



            $(document).on('click', '.btnEdit', function () {
                debugger;
                //var iId = $('.btnEdit').val();
                var iId = $(this).data('id');
                $.get('@Url.Action("EditKPIMeasurementMaster", "KPIMeasurementMaster")', { iId: iId }, function (data) {
                    $('#configurationBody').html(data);
                    $('#CreateModal').modal('show');
                });
            });


            $(document).on('click', '.btnDelete', function () {
                debugger;
                //var iId = $('.btnEdit').val();
                var iId = $(this).data('id');
                $.get('@Url.Action("DeleteKPIMeasurementMaster", "KPIMeasurementMaster")', { iId: iId }, function (data) {
                    $('#deleteBody').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

        });

    </script>
}