﻿@* @model IEnumerable<BCM.BusinessClasses.ResourcesInfo> *@
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using BCM.BusinessClasses
@{
    List<BCMCalenderDetailss> lstUsersFYA = new List<BCMCalenderDetailss>();
    if (ViewBag.chkUserFYA != null)
    {
        lstUsersFYA = ViewBag.chkUserFYA;
    }
    foreach (var BCMTeamsAndResources in Model)
    {
        var checkedUsersFYA = lstUsersFYA.Any(x => x.UserId == BCMTeamsAndResources.ResourceId);
        <div class="col">
            <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
                <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                    <span>
                        <input class="form-check fyaUser" type="checkbox" id="<EMAIL>"
                               value="@BCMTeamsAndResources.ResourceId" @(checkedUsersFYA ? "checked" : "")
                               name="usersFYA" data-FYA="@BCMTeamsAndResources.ResourceId" />
                    </span>
                    <span class="">@BCMTeamsAndResources.ResourceName</span>
                </span>
            </div>
        </div>
    }
}