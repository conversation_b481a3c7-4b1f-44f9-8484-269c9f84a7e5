// DataTable Script
// Global variable to track DataTable initialization
var isDataTableInitialized = false;

// Selectize Select Input Script
$(document).ready(function () {
    //$('.form-select').selectize();

    // Close other dropdowns when one opens

    // Initialize all Selectize dropdowns
    
    // Initialize all selectize elements with the same class
    var selectizeInstances = [];

    // Initialize each select element with the 'selectize' class
    $('.form-select').each(function () {
        var instance = $(this).selectize()[0].selectize;
        selectizeInstances.push(instance);

        // Attach event handler for 'dropdown_open' event
        instance.on('dropdown_open', function () {
            // Close all other selectize dropdowns
            selectizeInstances.forEach(function (otherInstance) {
                if (otherInstance !== instance) {
                    otherInstance.close(); // Close any other open dropdown
                }
            });
        });
    });


});

// Wizard Script
var form = $("#example-form");
if (form.length > 0) {
    form.steps({
        headerTag: "h6",
        bodyTag: "section",
        transitionEffect: "fade",
        titleTemplate: '<span class="step">#index#</span> #title#'
    });
}

// Initialize wizard for recovery plan forms
$(document).ready(function() {
    $('.tab-wizard').each(function() {
        var $form = $(this);
        if (!$form.hasClass('wizard')) {
            // Determine if this is an edit form (has hidden ID field with value > 0)
            var isEdit = $form.find('#txtPlanIds').length > 0 && $form.find('#txtPlanIds').val() > 0;

            $form.steps({
                headerTag: "h6",
                bodyTag: "section",
                transitionEffect: "fade",
                titleTemplate: '<span class="step">#index#</span> #title#',
                labels: {
                    finish: isEdit ? "Update" : "Save",
                    next: "Next",
                    previous: "Previous"
                },
                onStepChanging: function (event, currentIndex, newIndex) {
                    // Control Previous button visibility when changing steps
                    return true;
                },
                onInit: function (event, currentIndex) {
                    // Hide Previous button on initialization (first step)
                    var $wizard = $(this);

                    // Multiple attempts to hide the Previous button with different selectors
                    function hidePreviousButton() {
                        $wizard.find('a[href="#previous"]').hide();
                        $wizard.find('.actions a[href="#previous"]').hide();
                        $('.wizard .actions a[href="#previous"]').hide();
                        $('a[href="#previous"]').hide();
                        console.log('Previous button hidden on init');
                    }

                    // Try immediately and with delays
                    hidePreviousButton();
                    setTimeout(hidePreviousButton, 50);
                    setTimeout(hidePreviousButton, 100);
                    setTimeout(hidePreviousButton, 200);
                },
                onStepChanged: function (event, currentIndex, priorIndex) {
                    // Control Previous button visibility after step change
                    var $wizard = $(this);

                    function controlPreviousButton() {
                        if (currentIndex === 0) {
                            // Hide Previous button on first step
                            $wizard.find('a[href="#previous"]').hide();
                            $wizard.find('.actions a[href="#previous"]').hide();
                            $('.wizard .actions a[href="#previous"]').hide();
                            $('a[href="#previous"]').hide();
                            console.log('Previous button hidden on step 0');
                        } else {
                            // Show Previous button on other steps
                            $wizard.find('a[href="#previous"]').show();
                            $wizard.find('.actions a[href="#previous"]').show();
                            $('.wizard .actions a[href="#previous"]').show();
                            $('a[href="#previous"]').show();
                            console.log('Previous button shown on step ' + currentIndex);
                        }
                    }

                    // Try immediately and with delays
                    controlPreviousButton();
                    setTimeout(controlPreviousButton, 10);
                    setTimeout(controlPreviousButton, 50);
                }
            });
        }
    });
});

$(document).ready(function () {
    // Check if we're on the ManageRecoveryPlans page
    var isManageRecoveryPlansPage = window.location.pathname.includes('ManageRecoveryPlans') &&
                                   window.location.pathname.includes('ManageRecoveryPlans') &&
                                   !window.location.pathname.includes('WorkflowConfiguration');

    // Only initialize DataTable if not on ManageRecoveryPlans page (to avoid conflicts)
    if (isManageRecoveryPlansPage) {
        console.log('ManageRecoveryPlans page detected, skipping global DataTable initialization');
        return;
    }

    // Prevent multiple DataTable initializations
    if (isDataTableInitialized) {
        console.log('DataTable already initialized, skipping...');
        return;
    }

    // Check if DataTable is already initialized to prevent duplicates
    if ($.fn.DataTable.isDataTable('#example')) {
        // Destroy existing DataTable first
        $('#example').DataTable().destroy();
        console.log('Existing DataTable destroyed');
    }

    // Initialize DataTable only if the table exists
    if ($('#example').length > 0) {
        var table = $('#example').DataTable({
            language: {
                paginate: {
                    next: '<i class="cv-right-arrow"></i>',
                    previous: '<i class="cv-left-arrow"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row px-2 pb-2 align-items-center g-0"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
        });


        // Bind search functionality
        $('#search-inp').off('keyup').on('keyup', function () {
            table.search($(this).val()).draw();
        });

        // Mark as initialized
        isDataTableInitialized = true;
        console.log('DataTable initialized successfully');
    } else {
        console.log('Table #example not found, skipping DataTable initialization');
    }
});