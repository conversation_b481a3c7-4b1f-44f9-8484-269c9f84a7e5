﻿
const sampleData = [
    { category: "Q1", value: 25 },
    { category: "Q2", value: 40 },
    { category: "Q3", value: 35 },
    { category: "Q4", value: 50 }
];

$(document).ready(function () {
    debugger



    $('#WidgetType').selectize()
    $('#SPDataset').selectize()
            $('#ChartType').selectize({
                onChange: function (value) {

                    $("#widgetCreationCard").empty()
                    console.log("Changed value (selectize):", value);
                    let name = $("#WidgetName").val();
                    let ChartType = $("#ChartType option:selected").val()
                    renderChart(ChartType, 'widgetCreationCard', sampleData, name);
                }
            });


    $("#search-widget").on('keyup', function () {

        var filter = $(this).val();
        let categoryFlagStatus = true
        $("#prebuildList .widgetName").each(function () {

            var $i = 0;

            var splitText = $(this).text().split(" ")
            if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }

            if ($i > 0) {
                $(this).closest("#prebuildList .widgetList").show();
                categoryFlagStatus = false
            } else {
                $(this).closest("#prebuildList .widgetList").hide();
            }
        });
    })

    $("#search-widget").on('keyup', function () {

        var filter = $(this).val();
        let categoryFlagStatus = true
        $("#customList .widgetName").each(function () {

            var $i = 0;

            var splitText = $(this).text().split(" ")
            if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }

            if ($i > 0) {
                $(this).closest("#customList .widgetList").show();
                categoryFlagStatus = false
            } else {
                $(this).closest("#customList .widgetList").hide();
            }
        });
    })


    WidgetBuilderList()



    //$("#ChartType").on('change', function () {

    //    debugger
    //    const selectedValue = $(this).val();
    //    OverallWidgetChart(selectedValue, 'widgetCreationCard')


    //})


    //$("#okBtn").on("click", function () {

    //    $("#previewZoom").modal('hide')

    //    $(".btn_save").removeClass("disabled")
    //})

    $("#createCanvas").on('click', function () {
        $(".btn_save").text("Save")
        WidgetAllClear()
        ShowOffcanvas()

    })

    $("#canvasClose").on('click', function () {


        HideOffcanvas()

    })

        

    $(".btn_save").on("click", async function (e) {
        //arr = []
        //let scrolbarText = $("#scrolbarText").val()
        //let scrolbarTextVH = $("#scrolbarTextVH").val()

        //if (scrolbarText != "" && scrolbarText != undefined) {
        //    $(".tablePropertiesSet").attr("style", 'height:calc(' + scrolbarTextVH + 'vh - ' + scrolbarText + 'px);overflow:auto')
        //}

        let name = $("#WidgetName").val();
        let WidgetType = $("#WidgetType option:selected").val()
        let ChartType = $("#ChartType option:selected").val()
        let WidgetDescription = $("#WidgetDescription").val();
        let SPDataset = $("#SPDataset option:selected").val()
        let SPDatasetName = $("#SPDataset option:selected").text();

        if (name == "") {
            return false;
        }
        let widgetHtml = $('#widgetCreationCard').children()[0];
        let objInfo = {}
        var href = ''

        var container = document.getElementById("widgetCreationCard"); /* full page */
        const originalCanvas = await html2canvas(container, {
            scale: 0.2,
            useCORS: true,
            allowTaint: true
        });

        const targetWidth = originalCanvas.width / 2; // 50% smaller
        const targetHeight = originalCanvas.height / 2;

        const resizedCanvas = document.createElement("canvas");
        resizedCanvas.width = targetWidth;
        resizedCanvas.height = targetHeight;

        const ctx = resizedCanvas.getContext("2d");
        ctx.drawImage(originalCanvas, 0, 0, targetWidth, targetHeight);

        href = resizedCanvas.toDataURL("image/jpeg",0.7); // small & optimized

        if (e.target.textContent == 'Update') {
            objInfo.id = e.target.getAttribute('widgetId')
        }
        else {
            const uuid = generateUUID();
            // data.__RequestVerificationToken = gettoken()

            objInfo.ReferenceId = uuid
        }
        const date = new Date()
        const comondate = formatDate(date)
        objInfo.Name = name
        objInfo.IsActive = true
        objInfo.CreatedBy = "3"
        objInfo.CreatedDate = new Date(comondate).toISOString()
        objInfo.LastModifiedBy = "3"
        objInfo.LastModifiedDate = new Date(comondate).toISOString()
        objInfo.Properties = JSON.stringify({
            WidgetType: WidgetType,
            ChartType: ChartType,
            WidgetDescription: WidgetDescription,
            datasetId: SPDataset,
            datasetName: SPDatasetName,
            hrefImage: href,
            libraryType:'custom'
        })

        $.ajax({
            type: "POST",
            url:'/BCMAdministration/WidgetList/SaveWidgetBuilder',
            data: JSON.stringify(objInfo),
            credentials: "include",
            dataType: "json",
            contentType: 'application/json',
            traditional: true,
            success: function (result) {

                if (result.success) {
                    $("#previewZoom").modal('hide')
                    $('#alertClass').removeClass("info-toast")
                    $('#alertClass').addClass("success-toast")
                    $('#notificationAlertmessage').text(result.message)
                    $('#mytoastrdata').toast({ delay: 3000 });
                    $('#mytoastrdata').toast('show');
                    $(".iconClass").removeClass("cp-exclamation")
                    $(".iconClass").addClass("cp-check")
                    WidgetBuilderList()
                   HideOffcanvas()
                }

            }
        })

    })


    $(".btn_preview").on("click", async function (e) {
        let length = $("#widgetCreationCard").children().length
        if (length == 0) {
            $("#previewZoom").modal("hide")
        } else {
            $("#previewZoom").modal("show")
        }

    })

})


function ShowOffcanvas() {
    const el = document.getElementById('offcanvasExample');
    const offcanvas = bootstrap.Offcanvas.getOrCreateInstance(el);
    offcanvas.show();

}

function HideOffcanvas() {
    const el = document.getElementById('offcanvasExample');
    const offcanvas = bootstrap.Offcanvas.getInstance(el);
    if (offcanvas) offcanvas.hide();
}

function editwidgetView(data) {

    debugger
    const WidgetName = data.getAttribute('name')
    const WidgetType = data.getAttribute('WidgetType')
    const ChartType = data.getAttribute('charttype')
    const SPDataset = data.getAttribute('charttype')
    const WidgetDescription = data.getAttribute('widgetdescription')
    let widgetId = data.getAttribute('id')
    $("#WidgetName").val(WidgetName);
    $('#WidgetType')[0].selectize.setValue(WidgetType);
    $('#ChartType')[0].selectize.setValue(ChartType);
    $('#SPDataset')[0].selectize.setValue(SPDataset);
    $("#WidgetDescription").val(WidgetDescription);
    renderChart(ChartType, 'widgetCreationCard', sampleData, WidgetName);
    $(".btn_save").attr("widgetId", widgetId)
    $(".btn_save").text("Update")
    ShowOffcanvas()
}

function deletewidgetListView(data) {
    debugger
    let widgetId = $(data).attr('id')

    $.ajax({
        type: "POST",
        url: '/BCMAdministration/WidgetList/DeleteWidgetBuilder',
        data: JSON.stringify({ id: widgetId }),
        contentType: 'application/json',
        success: function (result) {

            if (result.success) {

                $('#alertClass').removeClass("info-toast")
                $('#alertClass').addClass("success-toast")
                $('#notificationAlertmessage').text(result.message)
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                $(".iconClass").removeClass("cp-exclamation")
                $(".iconClass").addClass("cp-check")
            }

           HideOffcanvas()
            WidgetBuilderList()
        }
    })

}

function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}


function formatDate(date) {
    const pad = (n, digits = 2) => n.toString().padStart(digits, '0');

    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1); // months are 0-indexed
    const day = pad(date.getDate());
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());
    const milliseconds = pad(date.getMilliseconds(), 3);



    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
}


function WidgetAllClear() {

    $("#WidgetName").val("");
    $("#WidgetType ").val("")
    $("#ChartType").val("")
    $("#WidgetDescription").val("");
    $("#SPDataset").val("")
    $("#widgetCreationCard").empty()
}


function WidgetBuilderList() {



    $.ajax({
        url: '/BCMAdministration/WidgetList/WidgetBuilderList',
        type: 'GET',
        credentials: "include",
        contentType: "application/json",
        dataType: "json",
        success: async function (response) {
            debugger
            $("#prebuildList").empty()
            $("#customList").empty()
            if (response.success) {
                if (response.data.length != 0) {
                    response.data.forEach((data) => {

                        let widgetHtml = JSON.parse(data.properties)
                        let html = "";
                        html += '<div class="col-3 p-1 widgetList">'
                        html += '<div class="card border mb-0 h-100">'
                        html += '<div class="dropdown d-flex justify-content-md-end" >'
                        html += '<i class="cv-horizontal-dots p-1 show" role="button" data-bs-toggle="dropdown" title="More" aria-expanded="true"></i>'
                        html += '<ul class="dropdown-menu" style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(184px, 26px);" data-popper-placement="bottom-start">'
                        html += '<li class="editActionList" id="' + data.id + '" name="' + data.name + '" ChartType="' + widgetHtml.ChartType + '" WidgetType="' + widgetHtml.WidgetType + '" datasetId="' + widgetHtml.datasetId + '"  WidgetDescription="' + widgetHtml.WidgetDescription +'"   onclick = "editwidgetView(this,event)" > <a class="dropdown-item" href="#"><i class="cv-edit text-info me-2" title="Edit"></i>Edit</a></li >'
                        html += '<li class="deleteActionList " id="' + data.id + '"    name="' + data.name + '" onclick="deletewidgetListView(this)" data-bs-toggle="modal" data-bs-target="#DeleteModal"><a class="dropdown-item" href="#"><i class="cv-delete text-danger me-2" title="Delete"></i>Delete</a></li>'
                        html += '</ul >'
                        html += '</div >'
                        html += '<div class="card-body py-3">'
                        html += '<img src = "' + widgetHtml.hrefImage + '" class="w-100" style="height:170px; object-fit:scale-down; object-position: top; " />'
                        html += '</div>'
                        html += '<div class="card-footer widgetName list-title text-center" title="' + data.name + '">' + data.name + '</div>'
                        html += '</div>'
                        html += '</div>'
                        if (widgetHtml.libraryType == 'custom') {
                            $("#customList").append(html)
                        }
                        else {
                            $("#prebuildList").append(html)
                        }
                       
                       
                    })
                }
            }


        }

    })

}




