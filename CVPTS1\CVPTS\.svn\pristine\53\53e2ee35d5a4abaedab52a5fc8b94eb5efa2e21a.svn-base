﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model BCM.BusinessClasses.NotifyIncident

<div class="modal-body pt-0">
<div>
    <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password " type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="true" aria-controls="collapseExample"><i class="cv-minus  align-middle"></i></span>
        <h6 class="mb-0">Select Plan To Notify</h6>
    </div>
    <div class="row p-3 row-cols-2  collapse show" id="collapseExample">
        <div class="col-12">
            <div class="form-group">
                <label class="form-label">Plan Type</label>
                <div class="input-group">
                    <input type="hidden" id="txtIncidentID" value="0" />
                    @* <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" id="rdoRiskIncident" name="planType" value="0" checked>
                        <label class="form-check-label" for="rdoRiskIncident">Risk Incident Plan</label>
                    </div> *@
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" id="rdoRecoveryPlan" name="planType" value="1" checked>
                        <label class="form-check-label" for="rdoRecoveryPlan">Recovery Plan</label>
                    </div>
                </div>
            </div>

        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Plan</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-develop-Plan"></i></span>
                    <select class="form-select form-control" autocomplete="off" id="ddlDisaster" aria-label="Default select example" data-val="true" data-val-required="The OrgID field is required." name="OrgID">
                        <option selected="" value="0">-- Select Plan --</option>
                        @{
                            foreach (var plan in ViewBag.IncidentPlans)
                            {
                                <option value="@plan.Value">@plan.Text</option>
                            }
                        }
                    </select>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Notification Type</label>
                <div class="input-group">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" id="rdoDrill" name="notifiedType" value="1" checked>
                        <label class="form-check-label" for="rdoDrill">Drill</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="notifiedType" id="rdoLive" value="0">
                        <label class="form-check-label" for="rdoLive">Live</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="notifiedType" id="rdoTableTop" value="2">
                        <label class="form-check-label" for="rdoTableTop">TableTop</label>
                    </div>
                </div>
            </div>

        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Incident Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-calendar"></i></span>
                    <input type="datetime-local" class="form-control" id="incidentDate"/>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Notification Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-calendar"></i></span>
                    <input type="datetime-local" class="form-control" id="notificationDate"/>
                </div>
            </div>
        </div>
    </div>
</div>
<div>
    <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample2" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
        <h6 class="mb-0">Select Teams To Notify</h6>
    </div>

    <div class="row g-2 row-cols-xl-3 row-cols-3 collapse p-3" id="collapseExample2" style="height: calc(50vh - 300px) !important;overflow: auto;">
        @{
            foreach (var teams in ViewBag.BCMTeams)
            {
                <div class="col d-grid">
                    <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
                        <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                            <span>
                                <input class="form-check" type="checkbox" name="teams" value="@teams.GroupID">
                            </span>
                            <span class="">@teams.GroupName</span>
                        </span>
                    </div>
                </div>
            }
        }
    </div>
</div>
<div>
    <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
        <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample3" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
        <h6 class="mb-0">Additional Message To Be Sent</h6>
    </div>
    <div class="row g-2 collapse p-3" id="collapseExample3">
        <div>
            <textarea class="form-control" placeholder="Enter Mark As Completed" id="txtTemplateMarkup"> </textarea>
        </div>
    </div>
</div>
<div>
    <div class="form-group w-50">
        <label class="form-label">Password</label>
        <div class="input-group">
            <span class="input-group-text"><i class="cv-password"></i></span>
            <input type="password" class="form-control" placeholder="Enter Password" id="txtPassword"/>
        </div>
    </div>
</div>
</div>
<div class="modal-footer d-flex justify-content-between">
    <div class="d-flex flex-column w-100">        
        <span id="msg" class="text-danger mb-2"></span>
        <div class="d-flex justify-content-between w-100">
            <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
            <div>
                <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="mw-100 w-auto btn btn-danger btn-sm me-1" id="btnCreateAndNotify">Notify Incident</button>
                @* <button type="submit" class="mw-100 w-auto btn btn-danger btn-sm me-1" id="btnCreateAndNotify">Create Incident And Notify</button> *@
                @* <button type="submit" class="btn btn-primary btn-sm" id="btnCreate">Create Incident</button> *@
            </div>
        </div>
    </div>
</div>



<script>
    $(document).ready(function(){

        // $('input[type=radio][name=planType]').change(function(){

        //     $.ajax({
        //         url:'@Url.Action("BindPlanTyps", "NotifyIncidentForm", new { area = "BCMIncidentManagement" })',
        //         type:'GET',
        //         data:{iPlanType:$(this).val()},
        //         success:function(response){
        //             console.log('Response:', response);
        //             var planType = $('#ddlDisaster');
        //             planType.empty();
        //             planType.append('<option selected="" value="0">-- Select Plan --</option>');

        //             // if (response && response.length > 0) {
        //             //     $.each(response, function (i, item) {
        //             //         if ($(this).val() == 0) {                                
        //             //             planType.append('<option value="' + item.id + '">' + item.eventName + '</option>');
        //             //         } else {                                
        //             //             planType.append('<option value="' + item.id + '">' + item.planName + '</option>');
        //             //         }
        //             //     });
        //             // }
        //         },
        //         error: function(xhr, status, error) {
        //             console.error("AJAX Error:", status, error);
        //             var planType = $('#ddlDisaster');
        //             planType.empty();
        //             planType.append('<option selected="" value="0">-- Select Plan --</option>');
        //             $('#msg').text('Error loading plans. Please try again.');
        //         }
        //     });
        // })

        // Initialize form
        function SaveIncident(buttonType){
            // Get form values
            var planType = $('input[name="planType"]:checked').val();
            var notificationType = $('input[name="notifiedType"]:checked').val();
            var password = $('#txtPassword').val();
            var templateMarkup = $('#txtTemplateMarkup').val();
            var notificationDate = $('#notificationDate').val();
            var incidentDate = $('#incidentDate').val();
            var disasterID = $('#ddlDisaster').val();
            var disasterName = $('#ddlDisaster option:selected').text();
            var incidentID = $('#txtIncidentID').val();

            // Create form data
            var teams = $('input[name="teams"]:checked').map(function(){
                return parseInt($(this).val(),10);
            }).get();
            var formData = new FormData();
            teams.forEach(function(team){
                formData.append('IncidentTeams',team);
            });

            formData.append('PlanTypeID', planType);
            formData.append('NotifiedAs', notificationType);
            formData.append('Password', password);
            formData.append('TemplateText', templateMarkup);
            formData.append('NotificationTime', notificationDate);
            formData.append('DisasterTime', incidentDate);
            formData.append('DisasterId', disasterID);
            formData.append('EventName', disasterName);
            formData.append('IncidentID', incidentID);
            formData.append('ButtonType', buttonType);

            $.ajax({
                url:'@Url.Action("SaveIncident", "NotifyIncidentForm", new { area = "BCMIncidentManagement" })',
                type:'POST',
                data:formData,
                contentType: false,
                processData: false,
                success:function(response){
                    if (response.success)
                    {
                        $('#NotifyIncidentModal').modal('hide');
                        $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                        const toastElement = $('#liveToast');
                        toastElement.removeClass('bg-success bg-warning bg-danger');
                        toastElement.addClass('bg-success');
                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();
                    }
                    else
                    {
                        $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                        const toastElement = $('#liveToast');
                        toastElement.removeClass('bg-success bg-warning bg-danger');
                        toastElement.addClass('bg-danger');
                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();
                    }
                },
                error:function(error){
                    console.log('Error:', error);

                }
            })
        }

        $(document).on('click','#btnCreateAndNotify',function(e){
            e.preventDefault();
            SaveIncident('0')
        })

        // $(document).on('click','#btnCreate',function(e){
        //     e.preventDefault();
        //     SaveIncident('1')
        // })
    })
</script>