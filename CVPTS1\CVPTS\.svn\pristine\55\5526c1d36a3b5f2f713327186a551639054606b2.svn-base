﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Hosting.Server;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;

namespace BCM.UI.Areas.BCMCalendar.Controllers;
[Area("BCMCalendar")]
public class ManageBCMCalenderController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    int iNotifyUsersAsFYACheckedCount = 0;
    string strNotifyUsersAsFYA = string.Empty;
    int iNotifyUsersAsFYICheckedCount = 0;
    string strNotifyUsersAsFYI = string.Empty;

    int iNotifyTeamsAsFYACheckedCount = 0;
    string strNotifyTeamsAsFYA = string.Empty;
    int iNotifyTeamsAsFYICheckedCount = 0;
    string strNotifyTeamsAsFYI = string.Empty;

    int iCalendarID = 0;

    public ManageBCMCalenderController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult ManageBCMCalender()
    {
        List<AddCalendarActivity> objCalendarActivityColl = new List<AddCalendarActivity>();
        try
        {
            ViewBag.userID = _UserDetails.UserID;
            objCalendarActivityColl = _ProcessSrv.GetAddCalendarActivityList(Convert.ToInt32(_UserDetails.OrgID), (int)BCPEnum.EntityType.BCMCalender);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(objCalendarActivityColl);
    }
}

