﻿@model IEnumerable<BCM.BusinessClasses.MenuRights>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
<script src="~/js/password_toggle.js"></script>
<form>
    <div class="tree-menu">
        <ul class="tree">
            <li>
                <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Master Screens Privileges</span>
                <ul>
                    <li>
                        <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />OrgGroup</span>
                        <ul class="sub-parent">
                            <li>
                                <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Create</span>
                            </li>
                            <li>
                                <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Modify</span>
                            </li>
                            <li>
                                <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Delete</span>
                            </li>
                            <li>
                                <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Read Only</span>
                            </li>


                        </ul>
                    </li>
                    <li>
                        <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Organization</span>
                        <ul class="sub-parent">
                            <li>
                                <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />View</span>
                                <ul class="sub-parent">
                                    <li>
                                        <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Create</span>
                                    </li>
                                    <li>
                                        <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Modify</span>
                                    </li>
                                    <li>
                                        <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Delete</span>
                                    </li>
                                    <li>
                                        <span role="button"><input class="form-check-input me-1 mt-2" type="checkbox" />Read Only</span>
                                    </li>


                                </ul>
                            </li>

                        </ul>
                    </li>
                </ul>

            </li>
        </ul>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>





