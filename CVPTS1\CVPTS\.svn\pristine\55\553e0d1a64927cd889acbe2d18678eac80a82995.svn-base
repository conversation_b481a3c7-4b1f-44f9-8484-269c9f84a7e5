﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    Layout = "~/Views/Shared/_CustomLayout.cshtml";
    ViewData["Title"] = "BCM Team Response";
}

@* @{
    ViewBag.Title = "BCM Team Response";
} *@
<style>
    .message {
        text-align: center;
        font-size: 40px;
        font-weight: bold;
        margin-top: 20px;
    }

    .success {
        color: green;
    }

    .fail {
        color: red;
    }
</style>

@if (ViewBag.ResponseMessage == "Thank You For Your Response!!!")
{
    <div class="message success"> <label>@ViewBag.ResponseMessage</label> </div>
}
else if (ViewBag.ResponseMessage == "Response Is No Longer Valid!!!")
{
    <div class="message fail"> <label>@ViewBag.ResponseMessage</label> </div>
}