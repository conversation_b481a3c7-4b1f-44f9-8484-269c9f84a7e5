﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using DevExpress.XtraGauges.Core.Customization;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Data;
using System.Data.SqlTypes;
using System.Linq;
using static BCM.Shared.BCPEnum;
namespace BCM.UI.Controllers.Dashboard;

public class DashboardController : BaseController
{
    private readonly Utilities _Utilities;
    private ProcessSrv _ProcessSrv;
    readonly CVLogger _CvLogger;
    private readonly BCMMail _BCMMail;

    public DashboardController(Utilities Utilities, ProcessSrv iProcessSrv, CVLogger cVLogger, BCMMail BCMMail)
        : base(Utilities)
    {
        _ProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _CvLogger = cVLogger;
        _BCMMail = BCMMail;
    }

    public IActionResult Index()
    {
        try
        {
            GetOrgSummuryDetails();
            HttpContext.Session.SetString("ToDoStatus", "0");

            //ViewBag.ToDoList = GetToDoList();
            GetToDoList();

            //ViewBag.CurrentBCMCompliance = _Utilities.GetAveragePCIScore(Convert.ToInt32(_UserDetails.OrgID), 1, Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(BCPEnum.EntityType.BusinessProcess), 0) + "%";
            //ViewBag.FacilitiesInScope = _Utilities.GetCompliantEntitiesCount(Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(BCPEnum.EntityType.Facilities), 0, 1);

            ViewData["MyData"] = GetEvents();
            GetWidgetList();

            GetApprovalPopUpData();
            DisplayNotification(0);
            GetApprovalPendingCounts();
            GetDashBoardCount();

            GetRiskStatistics();
            GetRecoveryPlanStatistics();
            //GetAuditStatistics();
            GetVendorStatistics();

            DisplayWaitingForApprovalCount();

            GetRiskHeapData();

        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        return View();
    }

    public void GetRiskHeapData()
    {
        var lblImpactID = string.Empty;
        var lblProbabilityID = string.Empty;

        string dataCondition = "0";
        // Get risk data
        List<RiskManagement> objriskcoll = new List<RiskManagement>();
        try
        {
            if (_UserDetails != null && !string.IsNullOrEmpty(Convert.ToString(_UserDetails.OrgID)))
            {
                objriskcoll = _ProcessSrv.GetBCMRiskDetails(Convert.ToInt32(_UserDetails.OrgID));

                if (objriskcoll != null && objriskcoll.Count > 0)
                {

                    IEnumerable<RiskManagement> RiskCodeList = null;
                    IEnumerable<RiskManagement> RiskCodeListResidual = null;
                    //  var RiskCodeList;// = new RiskManagementColl();
                    if (dataCondition == "1")
                    {
                        RiskCodeList = from RiskManagement objRisk in objriskcoll
                                       where
                                       objRisk.Impact.Equals(lblImpactID) && objRisk.LikliHood.Equals(lblProbabilityID)
                                       select objRisk;

                    }
                    else if (dataCondition == "2")
                    {
                        RiskCodeList = from RiskManagement objRisk in objriskcoll
                                       where
                                       objRisk.ResidualImpact.Equals(lblImpactID) && objRisk.ResidualLikeliHood.Equals(lblProbabilityID)

                                       select objRisk;

                    }
                    else if (dataCondition == "0")
                    {
                        RiskCodeList = from RiskManagement objRisk in objriskcoll
                                       where
                                       (objRisk.Impact.Equals(lblImpactID) && objRisk.LikliHood.Equals(lblProbabilityID))

                                       select objRisk;

                        RiskCodeListResidual = from RiskManagement objRisk in objriskcoll
                                               where
                                               (objRisk.ResidualImpact.Equals(lblImpactID) && objRisk.ResidualLikeliHood.Equals(lblProbabilityID))
                                               select objRisk;
                    }

                    // Pass risk data to view as JSON for the JavaScript to use
                    ViewBag.RiskDataJson = Newtonsoft.Json.JsonConvert.SerializeObject(objriskcoll);
                }


            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        // Add null check for _UserDetails.OrgID

    }

    public void GetDashBoardCount()
    {
        try
        {
            DashboardCount objDashboardCount = new DashboardCount();
            objDashboardCount = _ProcessSrv.GetDashboardCounts(_UserDetails.OrgID.ToString(), _UserDetails.UserID.ToString());
            ViewBag.DashboardCount = objDashboardCount;

            // Calculate Department BIA counts by status
            BindDeptPieChart();

            // Calculate Review Meetings counts by status
            BindReviewMeetingColumnChart();

            // Calculate Overall KPI Status
            BindKPIDonutChart();

            // Calculate Business Processes BIA
            BindBusinessProcessBIAChart();

            // Calculate Review Progress BIA
            CalculateReviewProgressBIA();
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }

    private void BindDeptPieChart()
    {
        try
        {
            int iInitiated = 0;
            int iWaitingForApproval = 0;
            int iApproved = 0;

            List<DepartmentBIAEntities> listDepartmentBIA = _ProcessSrv.GetDepartmentBIA_All();
            if (listDepartmentBIA != null && listDepartmentBIA.Count > 0)
            {
                foreach (DepartmentBIAEntities objDepartment in listDepartmentBIA)
                {
                    if (objDepartment.Status == 0)
                    {
                        iInitiated++;
                    }
                    else if (objDepartment.Status == 1)
                    {
                        iWaitingForApproval++;
                    }
                    else if (objDepartment.Status == 2)
                    {
                        iApproved++;
                    }
                }
            }

            // For testing purposes, if no data is available, set some default values
            if (iInitiated == 0 && iWaitingForApproval == 0 && iApproved == 0)
            {
                iInitiated = 25;
                iWaitingForApproval = 40;
                iApproved = 35;
            }

            ViewBag.DepartmentBIAInitiated = iInitiated;
            ViewBag.DepartmentBIAWaitingForApproval = iWaitingForApproval;
            ViewBag.DepartmentBIAApproved = iApproved;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);

            // Set default values in case of error
            ViewBag.DepartmentBIAInitiated = 25;
            ViewBag.DepartmentBIAWaitingForApproval = 40;
            ViewBag.DepartmentBIAApproved = 35;
        }
    }

    [HttpGet]
    public IActionResult GetChartData(bool fromDashboard = false)
    {
        var data = new List<object>
    {
        new { y = "Rare", x = "Insignificant", color = "#48A422", value = 2 },
        new { y = "Unlikely", x = "Insignificant", color = "#FFCC00", value = 2 },
        new { y = "Possible", x = "Insignificant", color = "#FFCC00", value = 2 },
        new { y = "Rare", x = "Low", color = "#FFCC00", value = 2 },
        new { y = "Unlikely", x = "Low", color = "#FFCC00", value = 2 },
        new { y = "Almost Certain", x = "Critical", color = "#FF3300", value = 2 }
    };

        return Json(data);


        //try
        //{
        //    int iDefaultProfileID = _Utilities.RiskProfileID;
        //    string dataCondition = "0"; // Assign a default or appropriate value to dataCondition
        //    GetFilterRiskData(dataCondition);

        //    if (fromDashboard)
        //    {
        //        return PartialView("_RiskHeatMapDashboard");
        //    }
        //}
        //catch (Exception ex)
        //{
        //    _CvLogger.LogErrorApp(ex);
        //}
        //return View();
    }


    [HttpGet]
    public List<RiskManagement> GetFilterRiskData(string dataCondition = "0")
    {
        var Data = string.Empty;
        var lblImpactID = string.Empty;
        var lblProbabilityID = string.Empty;
        try
        {
            // Get risk data
            List<RiskManagement> objriskcoll = new List<RiskManagement>();

            // Add null check for _UserDetails.OrgID
            if (_UserDetails != null && !string.IsNullOrEmpty(Convert.ToString(_UserDetails.OrgID)))
            {
                objriskcoll = _ProcessSrv.GetBCMRiskDetails(Convert.ToInt32(_UserDetails.OrgID));

                if (objriskcoll != null && objriskcoll.Count > 0)
                {

                    IEnumerable<RiskManagement> RiskCodeList = null;
                    IEnumerable<RiskManagement> RiskCodeListResidual = null;
                    //  var RiskCodeList;// = new RiskManagementColl();
                    if (dataCondition == "1")
                    {
                        RiskCodeList = from RiskManagement objRisk in objriskcoll
                                       where
                                       objRisk.Impact.Equals(lblImpactID) && objRisk.LikliHood.Equals(lblProbabilityID)
                                       select objRisk;

                    }
                    else if (dataCondition == "2")
                    {
                        RiskCodeList = from RiskManagement objRisk in objriskcoll
                                       where
                                       objRisk.ResidualImpact.Equals(lblImpactID) && objRisk.ResidualLikeliHood.Equals(lblProbabilityID)

                                       select objRisk;

                    }
                    else if (dataCondition == "0")
                    {
                        RiskCodeList = from RiskManagement objRisk in objriskcoll
                                       where
                                       (objRisk.Impact.Equals(lblImpactID) && objRisk.LikliHood.Equals(lblProbabilityID))

                                       select objRisk;

                        RiskCodeListResidual = from RiskManagement objRisk in objriskcoll
                                               where
                                               (objRisk.ResidualImpact.Equals(lblImpactID) && objRisk.ResidualLikeliHood.Equals(lblProbabilityID))
                                               select objRisk;
                    }
                    // Pass risk data to view as JSON for the JavaScript to use
                    //ViewBag.RiskDataJson = Newtonsoft.Json.JsonConvert.SerializeObject(objriskcoll);                    
                }

            }
            return objriskcoll;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            //return Json(null);
            return null;
        }
    }

    private void BindReviewMeetingColumnChart()
    {
        try
        {
            int iInitiated = 0;
            int iWaitingForApproval = 0;
            int iApproved = 0;

            List<AddCalendarActivity> listCalendarActivity = _ProcessSrv.GetManageCalendarActivityDetailsList(Convert.ToString(_UserDetails.OrgID), Convert.ToString((int)BCPEnum.EntityType.BCMCalender));
            if (listCalendarActivity != null && listCalendarActivity.Count > 0)
            {
                foreach (AddCalendarActivity objCalendarActivity in listCalendarActivity)
                {
                    if (objCalendarActivity.Status == "0")
                    {
                        iInitiated++;
                    }
                    else if (objCalendarActivity.Status == "1")
                    {
                        iWaitingForApproval++;
                    }
                    else if (objCalendarActivity.Status == "2")
                    {
                        iApproved++;
                    }
                }
            }

            // For testing purposes, if no data is available, set some default values
            if (iInitiated == 0 && iWaitingForApproval == 0 && iApproved == 0)
            {
                iInitiated = 4;
                iWaitingForApproval = 6;
                iApproved = 7;
            }

            ViewBag.ReviewMeetingsInitiated = iInitiated;
            ViewBag.ReviewMeetingsWaitingForApproval = iWaitingForApproval;
            ViewBag.ReviewMeetingsApproved = iApproved;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);

            // Set default values in case of error
            ViewBag.ReviewMeetingsInitiated = 4;
            ViewBag.ReviewMeetingsWaitingForApproval = 6;
            ViewBag.ReviewMeetingsApproved = 7;
        }
    }

    private void BindKPIDonutChart()
    {
        try
        {
            int iKPICount = 0;
            int iApprovedCount = 0;
            int itotalcount = 0;
            int icount = 0;

            // Get performance evaluation data
            DataTable dtEvaluationDetails = _ProcessSrv.GetPerformanceEvaluationSC_ByOrgID(_UserDetails.OrgID.ToString());
            if (dtEvaluationDetails != null && dtEvaluationDetails.Rows.Count > 0)
            {
                foreach (DataRow dr in dtEvaluationDetails.Rows)
                {
                    itotalcount = itotalcount + 100;
                    if (dr.Table.Columns.Contains("OverallKPI") && dr["OverallKPI"] != DBNull.Value)
                    {
                        icount = icount + Convert.ToInt32(dr["OverallKPI"]);
                    }

                    if (dr["StatusID"].ToString() == "2")
                    {
                        iApprovedCount++;
                    }
                    iKPICount++;
                }
            }

            ViewBag.KPICount = iKPICount;
            ViewBag.ApprovedCount = iApprovedCount;
            ViewBag.KPIPercentage = iKPICount > 0 ? Math.Round((Convert.ToDouble(iApprovedCount) / Convert.ToDouble(iKPICount)) * 100) : 0;

            // Calculate percentages
            int completedPercentage = icount != 0 ? Convert.ToInt32((Convert.ToDouble(itotalcount - icount) / Convert.ToDouble(itotalcount)) * 100) : 100;
            int inProgressPercentage = 100 - completedPercentage;

            // For testing purposes, if no data is available, set some default values
            if (itotalcount == 0)
            {
                completedPercentage = 75;
                inProgressPercentage = 25;
            }

            ViewBag.KPICompletedPercentage = completedPercentage;
            ViewBag.KPIInProgressPercentage = inProgressPercentage;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);

            // Set default values in case of error
            ViewBag.KPICompletedPercentage = 75;
            ViewBag.KPIInProgressPercentage = 25;
        }
    }

    private void BindBusinessProcessBIAChart()
    {
        int iCritical = 0;
        int iNonCritical = 0;
        try
        {
            int iInitiated = 0;
            int iWaitingForApproval = 0;
            int iApproved = 0;
            int iTotal = 0;


            List<BusinessProcessInfo> listBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
            if (listBusinessProcess != null && listBusinessProcess.Count > 0)
            {
                foreach (BusinessProcessInfo objBusinessProcess in listBusinessProcess)
                {
                    if (objBusinessProcess.Status == 0)
                    {
                        iInitiated++;
                    }
                    else if (objBusinessProcess.Status == 1)
                    {
                        iWaitingForApproval++;
                    }
                    else if (objBusinessProcess.Status == 2)
                    {
                        iApproved++;
                    }
                }
                iCritical = listBusinessProcess.Count(x => x.IsCritical == 1);
                iNonCritical = listBusinessProcess.Count(x => x.IsCritical == 0);
                iTotal = listBusinessProcess.Count();
            }

            // For testing purposes, if no data is available, set some default values
            if (iInitiated == 0 && iWaitingForApproval == 0 && iApproved == 0)
            {
                iInitiated = 20;
                iWaitingForApproval = 30;
                iApproved = 50;
            }

            ViewBag.BusinessProcessInitiated = iInitiated;
            ViewBag.BusinessProcessWaitingForApproval = iWaitingForApproval;
            ViewBag.BusinessProcessApproved = iApproved;
            ViewBag.BusinessProcessTotal = iTotal;
            ViewBag.ProcessPercentage = iTotal > 0 ? Math.Round((Convert.ToDouble(iApproved) / Convert.ToDouble(iTotal)) * 100) : 0;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);

            // Set default values in case of error
            ViewBag.BusinessProcessInitiated = 20;
            ViewBag.BusinessProcessWaitingForApproval = 30;
            ViewBag.BusinessProcessApproved = 50;
        }
        ViewBag.CriticalbusinessprocessCount = iCritical;
        ViewBag.NonCriticalbusinessprocessCount = iNonCritical;
    }

    private void CalculateReviewProgressBIA()
    {
        try
        {
            int iInitiatedDept = 0;
            int iWaitingForApprovalDept = 0;
            int iApprovedDept = 0;
            int AllCount = 0;

            // Get Department BIA data
            List<DepartmentBIAEntities> objDepartmentBIAEntitiesColl = _ProcessSrv.GetDepartmentBIA_All();
            if (objDepartmentBIAEntitiesColl != null)
            {
                foreach (DepartmentBIAEntities objDepartment in objDepartmentBIAEntitiesColl)
                {
                    if (objDepartment.Status == 0)
                    {
                        iInitiatedDept++;
                    }
                    else if (objDepartment.Status == 1)
                    {
                        iWaitingForApprovalDept++;
                    }
                    else if (objDepartment.Status == 2)
                    {
                        iApprovedDept++;
                    }
                    AllCount++;
                }
            }

            int AllWaitingCount = iWaitingForApprovalDept;

            // Calculate Review Progress BIA percentage
            int ReviewProgressBia = AllCount > 0 ?
                Convert.ToInt32((Convert.ToDouble(AllCount - AllWaitingCount) / Convert.ToDouble(AllCount)) * 100) : 100;

            // Set ViewBag values for the chart
            ViewBag.ReviewProgressBiaPercentage = ReviewProgressBia;
            ViewBag.ReviewProgressBiaWaiting = AllWaitingCount;
            ViewBag.ReviewProgressBiaTotal = AllCount;
            ViewBag.ReviewProgressBiaLabel = $"Review Progress BIA ({AllWaitingCount}/{AllCount})";

            // Get Risk Assessment data from objDashboardCount
            DashboardCount objDashboardCount = ViewBag.DashboardCount;
            int riskAssessmentCount = 0;
            int riskAssessmentPendingCount = 0;

            if (objDashboardCount != null)
            {
                // Parse the RiskAssessmentCount and RiskAssessmentPendingCount
                if (!string.IsNullOrEmpty(objDashboardCount.RiskAssessmentCount))
                {
                    int.TryParse(objDashboardCount.RiskAssessmentCount, out riskAssessmentCount);
                }

                if (!string.IsNullOrEmpty(objDashboardCount.RiskAssessmentPendingCount))
                {
                    int.TryParse(objDashboardCount.RiskAssessmentPendingCount, out riskAssessmentPendingCount);
                }
            }

            // Calculate Review Progress RA percentage using the specified formula
            int ReviewProgressRa = 56; // Default value
            int raTotal = 9; // Default value
            int raWaiting = 4; // Default value

            if (objDashboardCount != null)
            {
                try
                {
                    // Check if RiskAssessmentCountReview and RiskAssessmentCount are not null or empty
                    if (!string.IsNullOrEmpty(objDashboardCount.RiskAssessmentCountReview) &&
                        !string.IsNullOrEmpty(objDashboardCount.RiskAssessmentCount))
                    {
                        int riskReviewCount = Convert.ToInt32(objDashboardCount.RiskAssessmentCountReview);
                        int totalRiskAssessmentCount = Convert.ToInt32(objDashboardCount.RiskAssessmentCount);

                        // Calculate percentage only if totalRiskAssessmentCount is not zero
                        if (totalRiskAssessmentCount > 0)
                        {
                            if (riskReviewCount != 0)
                            {
                                // Calculate percentage of risk assessments IN review
                                ReviewProgressRa = Convert.ToInt32(
                                    (Convert.ToDouble(riskReviewCount) /
                                    Convert.ToDouble(totalRiskAssessmentCount)) * 100);
                            }
                            else
                            {
                                ReviewProgressRa = 100; // If no reviews, 100% complete
                            }

                            // Set variables for the label
                            raTotal = totalRiskAssessmentCount;
                            raWaiting = riskReviewCount;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _CvLogger.LogErrorApp(ex);
                    // Use default values if conversion fails
                }
            }

            // Set ViewBag values for the RA chart
            ViewBag.ReviewProgressRaPercentage = ReviewProgressRa;
            ViewBag.ReviewProgressRaWaiting = raWaiting;
            ViewBag.ReviewProgressRaTotal = raTotal;
            ViewBag.ReviewProgressRaLabel = $"Review Progress RA ({raWaiting}/{raTotal})";
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);

            // Set default values in case of error
            ViewBag.ReviewProgressBiaPercentage = 50;
            ViewBag.ReviewProgressBiaWaiting = 3;
            ViewBag.ReviewProgressBiaTotal = 30;
            ViewBag.ReviewProgressBiaLabel = "Review Progress BIA (3/30)";

            ViewBag.ReviewProgressRaPercentage = 56;
            ViewBag.ReviewProgressRaWaiting = 4;
            ViewBag.ReviewProgressRaTotal = 9;
            ViewBag.ReviewProgressRaLabel = "Review Progress RA (4/9)";
        }
    }

    public void GetWidgetList()
    {
        try
        {
            List<DashboardWidgets> widgets = _ProcessSrv.GetDashBoardTemplateWidgetsByUserId(Convert.ToInt32(_UserDetails.UserRoleID));

            DashboardWidgets ObjWidget = new DashboardWidgets();

            ObjWidget.MyTaskList = widgets.Where(x => x.WidgetName == "My Task List").ToList().Count > 0 ? 1 : 0;
            ObjWidget.IncidentSummary = widgets.Where(x => x.WidgetName == "Incident Summary").ToList().Count > 0 ? 1 : 0;
            ObjWidget.ActivityStream = widgets.Where(x => x.WidgetName == "Activity Stream").ToList().Count > 0 ? 1 : 0;
            ObjWidget.ProcessRisk = widgets.Where(x => x.WidgetName == "Process Risk").ToList().Count > 0 ? 1 : 0;
            ObjWidget.PlanRisk = widgets.Where(x => x.WidgetName == "Plan Risk").ToList().Count > 0 ? 1 : 0;
            ObjWidget.ResourceRisk = widgets.Where(x => x.WidgetName == "Resource Risk").ToList().Count > 0 ? 1 : 0;
            ObjWidget.OtherBCMRisk = widgets.Where(x => x.WidgetName == "Other BCM Risk").ToList().Count > 0 ? 1 : 0;
            ObjWidget.BIASummaryRisk = widgets.Where(x => x.WidgetName == "BIA Summary").ToList().Count > 0 ? 1 : 0;
            ObjWidget.latestIncidentStatus = widgets.Where(x => x.WidgetName == "Latest Incident Status").ToList().Count > 0 ? 1 : 0;
            ObjWidget.TeamNotificationSummary = widgets.Where(x => x.WidgetName == "Team Notification Summary").ToList().Count > 0 ? 1 : 0;
            ObjWidget.PendingApprovals = widgets.Where(x => x.WidgetName == "Pending Approvals").ToList().Count > 0 ? 1 : 0;
            ObjWidget.ToDo = widgets.Where(x => x.WidgetName == "To Do").ToList().Count > 0 ? 1 : 0;
            ObjWidget.Messaging = widgets.Where(x => x.WidgetName == "Messaging").ToList().Count > 0 ? 1 : 0;
            ObjWidget.OpenRisk = widgets.Where(x => x.WidgetName == "Open Risks").ToList().Count > 0 ? 1 : 0;
            ObjWidget.UrgentAttension = widgets.Where(x => x.WidgetName == "Urgent Attention").ToList().Count > 0 ? 1 : 0;
            ObjWidget.BCMAreaReview = widgets.Where(x => x.WidgetName == "BCM Area Review").ToList().Count > 0 ? 1 : 0;
            ObjWidget.VendorRisk = widgets.Where(x => x.WidgetName == "Vendor Risk").ToList().Count > 0 ? 1 : 0;
            ObjWidget.MyTaskList = widgets.Where(x => x.WidgetName == "My Task List").ToList().Count > 0 ? 1 : 0;
            ObjWidget.ViewCuurentExcalations = widgets.Where(x => x.WidgetName == "View Current Escalations").ToList().Count > 0 ? 1 : 0;
            ObjWidget.ViewEscalatedEscalations = widgets.Where(x => x.WidgetName == "View escalated escalations").ToList().Count > 0 ? 1 : 0;
            ObjWidget.AllRiskStatus = widgets.Where(x => x.WidgetName == "All Risk Status").ToList().Count > 0 ? 1 : 0;
            ObjWidget.LatestIncident = widgets.Where(x => x.WidgetName == "Latest Incident/Team Notification").ToList().Count > 0 ? 1 : 0;
            ObjWidget.OrganisationSummary = widgets.Where(x => x.WidgetName == "Organization Summary").ToList().Count > 0 ? 1 : 0;
            ObjWidget.BCMCalender = widgets.Where(x => x.WidgetName == "BCM Calendar").ToList().Count > 0 ? 1 : 0;


            ViewBag.Widgets = ObjWidget;
        }
        catch (Exception ex)
        {

            _CvLogger.LogErrorApp(ex);
        }
    }

    public IActionResult RedirectToCalender()
    {
        return View("ManageBCMCalender");
    }
    public JsonResult GetEvents()
    {
        var events = new List<object> {
            new { title = "Event 1", start = "2025-01-15", end = "2025-01-15" },
            new { title = "Event 2", start = "2025-01-16", end = "2025-01-16" } };
        return Json(events);
    }
    public JsonResult GetCalendarData()
    {
        var events = new List<object>();
        try
        {
            _CvLogger.LogErrorApp(new Exception($"GetCalendarData called for OrgID: {_UserDetails.OrgID}"));

            List<AddCalendarActivity> objCalendarActivityColl = _ProcessSrv.GetAddCalendarActivityList(Convert.ToInt32(_UserDetails.OrgID), (int)BCPEnum.EntityType.BCMCalender);

            _CvLogger.LogErrorApp(new Exception($"Retrieved {objCalendarActivityColl?.Count ?? 0} calendar activities"));

            if (objCalendarActivityColl != null)
            {
                foreach (AddCalendarActivity item in objCalendarActivityColl)
                {
                    // Ensure proper date formatting for FullCalendar
                    var startDate = item.ScheduledStartDate.ToString("yyyy-MM-dd");
                    var endDate = item.ScheduledENDDate.ToString("yyyy-MM-dd");

                    // Log the event details for debugging
                    _CvLogger.LogErrorApp(new Exception($"Processing event: {item.ActivityName}, Start: {startDate}, End: {endDate}"));

                    var eventObj = new
                    {
                        eid = item.Id,
                        title = item.ActivityName,
                        start = startDate,
                        end = endDate
                    };

                    events.Add(eventObj);
                }
            }

            _CvLogger.LogErrorApp(new Exception($"Returning {events.Count} events to calendar"));
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);

            // Return some test events if there's an error
            events.Add(new { eid = 1, title = "Test Event 1", start = DateTime.Now.ToString("yyyy-MM-dd"), end = DateTime.Now.ToString("yyyy-MM-dd") });
            events.Add(new { eid = 2, title = "Test Event 2", start = DateTime.Now.AddDays(1).ToString("yyyy-MM-dd"), end = DateTime.Now.AddDays(1).ToString("yyyy-MM-dd") });
        }

        return Json(events);
    }


    public void GetApprovalPopUpData()
    {

        #region BusinessProcess
        List<BusinessProcessInfo> lstBusinessProcess = new List<BusinessProcessInfo>();

        lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
        lstBusinessProcess = GetBusinessProcess(lstBusinessProcess);

        if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
        {
            if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
            {
                lstBusinessProcess = _Utilities.FilterListByOrgGroupID(lstBusinessProcess, _UserDetails.OrgGroupID);
            }
            else
            {
                lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                lstBusinessProcess = _Utilities.FilterListByRoleID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
            }
            lstBusinessProcess = lstBusinessProcess.Where(x => x.ApproverID == _UserDetails.UserID || x.ProcessOwnerID == _UserDetails.UserID || x.AltProcessOwnerID == _UserDetails.UserID).ToList();

        }
        ViewBag.BusinessProcessWaiting = lstBusinessProcess.Where(x => x.ApproverID == _UserDetails.UserID && x.Status == (int)BCPEnum.ApprovalType.WaitingforApproval).ToList().Count;

        ViewBag.BusinessProcess = lstBusinessProcess;
        #endregion

        #region RecoveryPlan
        List<RecoveryPlan> objRecoveryPlan = new List<RecoveryPlan>();
        objRecoveryPlan = _ProcessSrv.GetRecoveryPlansList(Convert.ToInt32(_UserDetails.UserID), Convert.ToInt32(_UserDetails.OrgID));
        objRecoveryPlan = objRecoveryPlan.Where(x => x.IsActive == 1).ToList();
        ViewBag.RecoveryPlanWaiting = objRecoveryPlan.Where(x => x.PlanStageID == (int)BCPEnum.ApprovalType.WaitingforApproval).ToList().Count;
        ViewBag.RecoveryPlanWaitingForUser = objRecoveryPlan.Where(x => x.PlanStageID == (int)BCPEnum.ApprovalType.WaitingforApproval && x.PlanApproverID == _UserDetails.UserID).ToList().Count;
        ViewBag.RecoveryPlan = objRecoveryPlan;

        int iRecoveryPlanCount = objRecoveryPlan.Count();
        int iApprovedRecoveryPlanCount = objRecoveryPlan.Count(x => x.PlanStageID == (int)BCPEnum.ApprovalType.Approved);

        ViewBag.RecoveryPlanCount = iRecoveryPlanCount;
        ViewBag.ApprovedRecoveryPlanCount = iApprovedRecoveryPlanCount;
        ViewBag.RecoveryPlanPercentage = iRecoveryPlanCount > 0 ? Math.Round((Convert.ToDouble(iApprovedRecoveryPlanCount) / Convert.ToDouble(iRecoveryPlanCount)) * 100) : 0;
        #endregion

        #region RiskAssessment
        List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
        int iRiskProfileID = RiskProfileID();
        //lstRiskManagement = _ProcessSrv.getBCMRiskList("0", "0", "0", "0", "0", "1", "0", "0", "0", "0", string.Empty);
        lstRiskManagement = _ProcessSrv.getBCMRiskManageList1("0", "0", "0", "0", "0", "1", "0", "0", "0", "0", "", iRiskProfileID);
        //ViewBag.RiskAssessment = lstRiskManagement.Where(x => x.RiskChampionID == _UserDetails.UserID && x.RiskOwnerID == _UserDetails.UserID).ToList();
        ViewBag.RiskAssessment = lstRiskManagement;
        ViewBag.RiskAssessmentWaiting = lstRiskManagement.Where(x => x.Status == (int)BCPEnum.ApprovalType.WaitingforApproval && x.RiskChampionID == _UserDetails.UserID).ToList().Count;

        int iTotalRiskCount = lstRiskManagement.Count();
        int iApprovedRiskCount = lstRiskManagement.Count(x => x.Status == 2);

        ViewBag.TotalRiskCountForReview = iTotalRiskCount;
        ViewBag.ApprovedRiskCount = iApprovedRiskCount;
        ViewBag.RiskPercentage = iTotalRiskCount > 0 ? Math.Round((Convert.ToDouble(iApprovedRiskCount) / Convert.ToDouble(iTotalRiskCount)) * 100) : 0;
        #endregion
    }

    public int RiskProfileID()
    {
        if (_UserDetails != null)
        {
            return _ProcessSrv.GetDefaultProfile(Convert.ToInt32((_UserDetails.OrgID)));
        }
        else
        {
            return 0;
        }
    }
    public void GetOrgSummuryDetails()
    {
        try
        {
            int UnitCount = 0;
            int DepartmentCount = 0;
            int DivisionCount = 0;
            int ProcessCount = 0;
            List<OrgInfo> objOrgColl = new List<OrgInfo>();
            List<OrgUnit> objOrgUnitColl = new List<OrgUnit>();
            List<DepartmentInfo> objDeptColl = new List<DepartmentInfo>();
            List<SubFunction> objSubFunctionColl = new List<SubFunction>();
            List<BusinessProcessInfo> objBusinessProcesscoll = new List<BusinessProcessInfo>();
            objOrgColl = _ProcessSrv.GetOrganizationMasterList();

            objBusinessProcesscoll = _ProcessSrv.GetBusinessProcessListByEntityType(1);

            ViewBag.OrganizationCount = objOrgColl.Count;

            if (_Utilities.IsProductAdmin(_UserDetails.UserID.ToString()))
            {
                ProcessCount = objBusinessProcesscoll.Count;
                if (objOrgColl != null)
                {
                    foreach (OrgInfo objOrg in objOrgColl)
                    {
                        objOrgUnitColl = _ProcessSrv.GetOrganizationUnitList_New(Convert.ToInt32(objOrg.Id));
                        UnitCount += objOrgUnitColl.Count;
                        if (objOrgUnitColl != null)
                        {
                            foreach (OrgUnit objOrgUnit in objOrgUnitColl)
                            {
                                //get dept
                                objDeptColl = _ProcessSrv.GetDepartmentByUnitId(Convert.ToInt32(objOrgUnit.UnitID));
                                DepartmentCount = DepartmentCount + objDeptColl.Count;

                                if (objDeptColl != null)
                                {
                                    foreach (DepartmentInfo objOrgUnit1 in objDeptColl)
                                    {
                                        //get division
                                        objSubFunctionColl = _ProcessSrv.GetSubFunctionListByFunctionID(objOrgUnit1.DepartmentID.ToString());
                                        DivisionCount = DivisionCount + objSubFunctionColl.Count;
                                    }
                                }
                            }
                        }
                    }

                }

            }
            if (_Utilities.IsSuperAdmin(_UserDetails.UserID.ToString()))
            {
                foreach (BusinessProcessInfo item in objBusinessProcesscoll)
                {
                    if (item.OrgID == _UserDetails.OrgID)
                    {
                        ProcessCount = ProcessCount + 1;
                    }
                }
                if (objOrgColl != null)
                {
                    foreach (OrgInfo objOrg in objOrgColl)
                    {
                        if (objOrg.Id == _UserDetails.OrgID.ToString())
                        {
                            //unit
                            objOrgUnitColl = _ProcessSrv.GetOrganizationUnitList_New(Convert.ToInt32(objOrg.Id));
                            UnitCount += objOrgUnitColl.Count;
                            if (objOrgUnitColl != null)
                            {
                                foreach (OrgUnit objOrgUnit in objOrgUnitColl)
                                {
                                    //get dept
                                    objDeptColl = _ProcessSrv.GetDepartmentByUnitId(Convert.ToInt32(objOrgUnit.UnitID));
                                    DepartmentCount = DepartmentCount + objDeptColl.Count;

                                    if (objDeptColl != null)
                                    {
                                        foreach (DepartmentInfo objOrgUnit1 in objDeptColl)
                                        {
                                            //get division
                                            objSubFunctionColl = _ProcessSrv.GetSubFunctionListByFunctionID(objOrgUnit1.DepartmentID.ToString());
                                            DivisionCount += objSubFunctionColl.Count;
                                        }
                                    }

                                }
                            }
                        }

                    }

                }
            }
            else
            {
                foreach (BusinessProcessInfo item in objBusinessProcesscoll)
                {
                    if (item.OrgID == _UserDetails.OrgID)
                    {
                        ProcessCount = ProcessCount + 1;
                    }
                }
                if (objOrgColl != null)
                {
                    foreach (OrgInfo objOrg in objOrgColl)
                    {
                        if (_UserDetails.OrgID.ToString() == objOrg.Id)
                        {
                            //unit
                            objOrgUnitColl = _ProcessSrv.GetOrganizationUnitList_New(Convert.ToInt32(objOrg.Id));
                            UnitCount = objOrgUnitColl.Count;
                            if (objOrgUnitColl != null)
                            {
                                foreach (OrgUnit objOrgUnit in objOrgUnitColl)
                                {
                                    //get dept
                                    objDeptColl = _ProcessSrv.GetDepartmentByUnitId(Convert.ToInt32(objOrgUnit.UnitID));
                                    DepartmentCount = DepartmentCount + objDeptColl.Count;

                                    if (objDeptColl != null)
                                    {
                                        foreach (DepartmentInfo objOrgUnit1 in objDeptColl)
                                        {
                                            //get division
                                            objSubFunctionColl = _ProcessSrv.GetSubFunctionListByFunctionID(objOrgUnit1.DepartmentID.ToString());
                                            DivisionCount = DivisionCount + objSubFunctionColl.Count;
                                        }
                                    }

                                }
                            }
                        }

                    }

                }
            }

            ViewBag.UnitCount = UnitCount;
            ViewBag.DepartmentCount = DepartmentCount;
            ViewBag.DivisionCount = DivisionCount;
            ViewBag.ProcessCount = ProcessCount;


        }
        catch (Exception ex)
        {

            _CvLogger.LogErrorApp(ex);
        }
    }
    public void GetApprovalPendingCounts()
    {
        try
        {
            DashboardApprovalPendingCount ApprovalPendingCount = new DashboardApprovalPendingCount();
            ApprovalPendingCount = _ProcessSrv.GetDashboardApprovalPendingCount(_UserDetails.OrgID.ToString(), _UserDetails.UserID.ToString());
            ViewBag.ApprovalPendingCount = ApprovalPendingCount;
        }
        catch (Exception ex)
        {

            _CvLogger.LogErrorApp(ex);
        }
    }

    #region Notifications
    public IActionResult DisplayNotification(int iNotificationTab)
    {
        try
        {
            if (iNotificationTab == 0)
            {
                DisplayIncidentNotification();
            }
            else if (iNotificationTab == 1)
            {
                DisplayTeamNotificationData();
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        ViewData["iNotificationTab"] = iNotificationTab;
        return PartialView("_IncidentNotifications");
    }

    private void DisplayIncidentNotification()
    {
        List<IncidentManagement> lstIncidentManagement = new List<IncidentManagement>();
        try
        {
            lstIncidentManagement = _ProcessSrv.GetIncidentManagementList(_UserDetails.OrgID);
            lstIncidentManagement.Where(x => x.Status == "1" && x.Status == "2");
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        ViewBag.IncidentNotification = lstIncidentManagement;
    }

    private void DisplayTeamNotificationData()
    {
        List<BCMGroupNotification> lstBCMGroupNotification = new List<BCMGroupNotification>();
        var newBCMGroupNotification = new List<BCMGroupNotification>();
        int iTotalCount = 0;
        int iResponseCount = 0;
        try
        {
            lstBCMGroupNotification = _ProcessSrv.GetBCMGroupNotificationByOrgID(_UserDetails.OrgID);
            List<BCMGroupResources> lstGroupResources = new List<BCMGroupResources>();

            foreach (BCMGroupNotification objNotification in lstBCMGroupNotification)
            {
                lstGroupResources = _ProcessSrv.GetBCMGroupNotificationResource_ByNotificationID(objNotification.NotificationID);

                var objGroupFYA = (from BCMGroupResources objGroupResource in lstGroupResources
                                   where objGroupResource.NotificationAs == ((int)BCPEnum.NotificationAs.FYA).ToString()
                                   select objGroupResource
                                   );

                if (objGroupFYA != null)
                {
                    var TotalCount = (from BCMGroupResources objGroupResource in objGroupFYA
                                      group objGroupResource by new { Name = objGroupResource.ResourceName, GroupMapID = objGroupResource.GroupMapID } into obj
                                      select new { Name = obj.Key.Name, GroupMapID = obj.Key.GroupMapID }
                                       );

                    var ResponseCount = (from BCMGroupResources objGroupResource in objGroupFYA
                                         where objGroupResource.ResponseDate != DateTime.MinValue
                                         select objGroupResource
                                       );
                    if (ResponseCount != null)
                    {
                        var RespondedCount = (from BCMGroupResources objGroupResource in ResponseCount
                                              group objGroupResource by new { Name = objGroupResource.ResourceName, GroupMapID = objGroupResource.GroupMapID } into obj
                                              select new { Name = obj.Key.Name, GroupMapID = obj.Key.GroupMapID }
                                       );

                        if (RespondedCount != null)
                        {
                            iResponseCount = RespondedCount.Count();
                        }
                    }
                    iTotalCount = TotalCount.Count();
                }

                newBCMGroupNotification.Add(new BCMGroupNotification
                {
                    NotificationID = objNotification.NotificationID,
                    groupMapId = objNotification.groupMapId,
                    Subject = objNotification.Subject,
                    ResourceName = objNotification.ResourceName,
                    NotiDate = objNotification.NotiDate,
                    TotalCount = iTotalCount,
                    ResponseCount = iResponseCount
                });
            }

            ViewBag.BCMGroupNotification = newBCMGroupNotification;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }



    //private void DisplayTeamNotificationData()
    //{
    //    DataTable dtTeamNotification = new DataTable();
    //    dtTeamNotification.Columns.Add("Subject");
    //    dtTeamNotification.Columns.Add("NotifiedAs");
    //    dtTeamNotification.Columns.Add("NotiDate", typeof(DateTime));
    //    //  dtTeamNotification.Columns.Add("GroupName");
    //    dtTeamNotification.Columns.Add("EventName");
    //    dtTeamNotification.Columns.Add("IncidentID");
    //    dtTeamNotification.Columns.Add("NotificationID");
    //    dtTeamNotification.Columns.Add("GroupMapID");

    //    int iOrgID = Convert.ToInt32(_UserDetails.OrgID);


    //    //BCMGroupNotificationColl objIMColl = _oProcSrv.GetBCMGroupNotification(0, iOrgID);

    //    List<BCMGroupNotification> objIMColl = _ProcessSrv.GetBCMGroupNotificationByOrgID(iOrgID);


    //    //if (objIMColl != null)
    //    //{
    //    //    if (!(_oUser.UserRole.Equals(CVGlobal.ProductAdminRole)))
    //    //    {
    //    //        if (Utilities.Utilities.IsOrgHead(_oUser.UserID))
    //    //        {
    //    //            foreach (BCMGroupNotification objIM in objIMColl)
    //    //            {
    //    //                if (_oUser.OrgID.Equals(objIM.OrgId))
    //    //                    dtTeamNotification.Rows.Add(objIM.Subject, objIM.ResourceName, objIM.NotiDate, objIM.GroupName, objIM.IncidentName, objIM.IncidentID, objIM.NotificationID, objIM.groupMapId);
    //    //            }

    //    //        }
    //    //        else
    //    //        {
    //    //            foreach (BCMGroupNotification objIM in objIMColl)
    //    //            {
    //    //                if (_oUser.UnitID.Equals(objIM.UnitId))
    //    //                    dtTeamNotification.Rows.Add(objIM.Subject, objIM.ResourceName, objIM.NotiDate, objIM.GroupName, objIM.IncidentName, objIM.IncidentID, objIM.NotificationID, objIM.groupMapId);
    //    //            }
    //    //        }
    //    //    }

    //    //    else
    //    //    {
    //    //        foreach (BCMGroupNotification objIM in objIMColl)
    //    //        {
    //    //            dtTeamNotification.Rows.Add(objIM.Subject, objIM.ResourceName, objIM.NotiDate, objIM.GroupName, objIM.IncidentName, objIM.IncidentID, objIM.NotificationID, objIM.groupMapId);
    //    //        }
    //    //    }
    //    //}

    //    if (objIMColl != null)
    //    {
    //        foreach (BCMGroupNotification objIM in objIMColl)
    //        {
    //            dtTeamNotification.Rows.Add(objIM.Subject, objIM.ResourceName, objIM.NotiDate, objIM.IncidentName, objIM.IncidentID, objIM.NotificationID, objIM.groupMapId);
    //        }
    //    }

    //    //objIMColl = objIMColl.OrderByDescending(x => x.NotiDate).ToList();

    //    ViewBag.BCMGroupNotification = objIMColl;
    //    //DataView dv = new DataView(dtTeamNotification);

    //    //dv.Sort = "NotiDate DESC";

    //    //lstTeamNotification.DataSource = dv;
    //    //lstTeamNotification.DataBind();

    //    //ViewState["TeamNotificationData_Tab"] = dtTeamNotification;
    //}
    #endregion

    public JsonResult getUserDetails()
    {        
        return Json(_UserDetails);
    }
    public List<BusinessProcessInfo> GetBusinessProcess(List<BusinessProcessInfo> lstBusinessProcess)
    {
        try
        {
            lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
            if (_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole))
            {
                lstBusinessProcess = _Utilities.GetBusinessProcess(lstBusinessProcess, 0, 0, 0, 0, -1);
            }
            else
            {
                lstBusinessProcess = _Utilities.FilterListForOwner(lstBusinessProcess, 0, 0, 0, 0, Convert.ToInt32(BCPEnum.EntityType.BusinessProcess), -1);
            }

        }
        catch (Exception)
        {

           
        }
        return lstBusinessProcess;
    }
    public IActionResult ApprovalPopu()
    {
        List<BIAApprovalAndButtonAccess> lstApproval = new List<BIAApprovalAndButtonAccess>();
        return PartialView("_ApprovalPopup", lstApproval);
    }

    public IActionResult ApprovalPopup(int EntityTypeID, int iStatusID)
    {
        if (iStatusID == 0)
            iStatusID = 1;


        List<BusinessProcessInfo> lstBusinessProcess = new List<BusinessProcessInfo>();
        List<BIAApprovalAndButtonAccess> lstApproval = new List<BIAApprovalAndButtonAccess>();

        if (EntityTypeID == (int)BCPEnum.EntityType.BusinessProcess)
        {
            lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
            lstBusinessProcess = GetBusinessProcess(lstBusinessProcess);


            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    lstBusinessProcess = _Utilities.FilterListByOrgGroupID(lstBusinessProcess, _UserDetails.OrgGroupID);

                }
                else
                {
                    lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstBusinessProcess = _Utilities.FilterListByRoleID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                }
                lstBusinessProcess = lstBusinessProcess.Where(x => x.ApproverID == _UserDetails.UserID || x.ProcessOwnerID == _UserDetails.UserID || x.AltProcessOwnerID == _UserDetails.UserID).ToList();

            }

            foreach (BusinessProcessInfo item in lstBusinessProcess)
            {
                BIAApprovalAndButtonAccess objBIAApproval = new BIAApprovalAndButtonAccess();
                objBIAApproval.BIAApproval = new BIAApproval();
                objBIAApproval.ButtonAcces = new ButtonAcces();

                objBIAApproval.BIAApproval.RecordID = item.ProcessID;
                objBIAApproval.BIAApproval.RecordName = item.ProcessName;
                objBIAApproval.BIAApproval.StatusID = item.Status;
                objBIAApproval.BIAApproval.Version = item.Version;
                objBIAApproval.BIAApproval.EntityCode = item.ProcessCode;

                objBIAApproval.BIAApproval.OwnerName = item.ProcessOwner;
                objBIAApproval.BIAApproval.OwnerID = item.ProcessOwnerID;
                objBIAApproval.BIAApproval.OwnerEmail = item.OwnerEmail;
                objBIAApproval.BIAApproval.OwnerPhone = item.ProcessOwnerMobile;

                objBIAApproval.BIAApproval.ApprovarName = item.ApproverName;
                objBIAApproval.BIAApproval.ApproverID = item.ApproverID;
                objBIAApproval.BIAApproval.ApproverEmail = item.ApproverEmail;
                objBIAApproval.BIAApproval.ApproverPhone = item.ProcessOwnerMobile;
                objBIAApproval.BIAApproval.RTO = item.OwnerRTO;
                objBIAApproval.ButtonAcces = _Utilities.ShowButtonsByAccess((item.Status).ToString(), item.ApproverID.ToString(), item.ProcessOwnerID.ToString(),
                                                        item.AltProcessOwnerID.ToString(), _UserDetails.UserID.ToString(),
                                                        Convert.ToInt32(BCPEnum.PrivilegeID.Modify));
                lstApproval.Add(objBIAApproval);

            }
            lstApproval = lstApproval.Where(x => x.BIAApproval.StatusID == iStatusID).ToList();
            ViewBag.strEntityType = "Process";
            ViewBag.EntityTypeID = (int)BCPEnum.EntityType.BusinessProcess;

        }
        if (EntityTypeID == (int)BCPEnum.EntityType.RiskAssessment)
        {
            List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
            lstRiskManagement = _ProcessSrv.getRiskAssementList(_UserDetails.OrgID);
            lstRiskManagement = lstRiskManagement.Where(x => x.Status == iStatusID && x.RiskChampionID == _UserDetails.UserID).ToList();
            foreach (RiskManagement item in lstRiskManagement)
            {
                ResourcesInfo objOwner = _ProcessSrv.GetResourcesByResourceID(item.RiskOwnerID);
                ResourcesInfo objChampion = _ProcessSrv.GetResourcesByResourceID(item.RiskChampionID);
                BIAApprovalAndButtonAccess objBIAApproval = new BIAApprovalAndButtonAccess();
                objBIAApproval.BIAApproval = new BIAApproval();
                objBIAApproval.ButtonAcces = new ButtonAcces();

                objBIAApproval.BIAApproval.RecordID = item.ID;
                objBIAApproval.BIAApproval.RecordName = item.RiskName;
                objBIAApproval.BIAApproval.StatusID = item.Status;
                objBIAApproval.BIAApproval.Version = item.Version;
                objBIAApproval.BIAApproval.EntityCode = item.RiskCode;

                objBIAApproval.BIAApproval.OwnerName = objOwner.ResourceName;
                objBIAApproval.BIAApproval.OwnerID = objOwner.ResourceId;
                objBIAApproval.BIAApproval.OwnerEmail = objOwner.CompanyEmail;
                objBIAApproval.BIAApproval.OwnerPhone = objOwner.MobilePhone.ToString();

                objBIAApproval.BIAApproval.ApprovarName = objChampion.ResourceName;
                objBIAApproval.BIAApproval.ApproverID = objChampion.ResourceId;
                objBIAApproval.BIAApproval.ApproverEmail = objChampion.CompanyEmail;
                objBIAApproval.BIAApproval.ApproverPhone = objChampion.MobilePhone;
                objBIAApproval.BIAApproval.RTO = "0";

                objBIAApproval.ButtonAcces = _Utilities.ShowButtonsByAccess((item.Status).ToString(), item.RiskChampionID.ToString(), item.RiskOwnerID.ToString(),
                                                        item.RiskOwnerID.ToString(), _UserDetails.UserID.ToString(),
                                                        Convert.ToInt32(BCPEnum.PrivilegeID.Modify));
                lstApproval.Add(objBIAApproval);

            }
            lstApproval = lstApproval.Where(x => x.BIAApproval.StatusID == iStatusID).ToList();

            ViewBag.strEntityType = "Risk";
            ViewBag.EntityTypeID = (int)BCPEnum.EntityType.RiskAssessment;
        }
        if (EntityTypeID == (int)BCPEnum.EntityType.RecoveryPlan)
        {
            List<RecoveryPlan> objRP = new List<RecoveryPlan>();
            if (_Utilities.IsSuperAdmin(_UserDetails.UserRole) || _Utilities.IsProductAdmin(_UserDetails.UserRole))
            {
            }
            else
            {
                objRP = _ProcessSrv.GetRecoveryPlansList(_UserDetails.UserID, _UserDetails.OrgID);
                objRP = objRP.Where(x => x.IsActive == 1 && x.PlanStageID == iStatusID).ToList();
            }

            if (objRP.Count > 0)
            {
                foreach (RecoveryPlan item in objRP)
                {
                    ResourcesInfo objOwner = _ProcessSrv.GetResourceNameByID(item.PlanOwnerID);
                    BIAApprovalAndButtonAccess objBIAApproval = new BIAApprovalAndButtonAccess();
                    objBIAApproval.BIAApproval = new BIAApproval();
                    objBIAApproval.ButtonAcces = new ButtonAcces();

                    objBIAApproval.BIAApproval.RecordID = item.ID;
                    objBIAApproval.BIAApproval.RecordName = item.PlanName;
                    objBIAApproval.BIAApproval.StatusID = item.PlanStageID;
                    objBIAApproval.BIAApproval.Version = item.Version == null ? "0" : item.Version;
                    objBIAApproval.BIAApproval.EntityCode = item.PlanCode;

                    objBIAApproval.BIAApproval.OwnerName = objOwner.ResourceName;
                    objBIAApproval.BIAApproval.OwnerID = objOwner.ResourceId;
                    objBIAApproval.BIAApproval.OwnerEmail = objOwner.CompanyEmail;
                    objBIAApproval.BIAApproval.OwnerPhone = objOwner.MobilePhone.ToString();

                    objBIAApproval.BIAApproval.ApprovarName = item.PlanApprovarName;
                    objBIAApproval.BIAApproval.ApproverID = item.PlanApproverID;
                    objBIAApproval.BIAApproval.ApproverEmail = item.ApproverEmail;
                    objBIAApproval.BIAApproval.ApproverPhone = item.ApproverMobile;

                    objBIAApproval.ButtonAcces = _Utilities.ShowButtonsByAccess((item.PlanStageID).ToString(), item.PlanApproverID.ToString(), item.PlanOwnerID.ToString(),
                                                        "0", _UserDetails.UserID.ToString(),
                                                        Convert.ToInt32(BCPEnum.PrivilegeID.Modify));
                    lstApproval.Add(objBIAApproval);
                }
                //lstApproval = lstApproval.Where(x => x.BIAApproval.StatusID == iStatusID).ToList();
                ViewBag.strEntityType = "Recovery Plan";
                ViewBag.EntityTypeID = (int)BCPEnum.EntityType.RecoveryPlan;
            }
        }

        ViewBag.iStatusID = iStatusID;

        return PartialView("_ApprovalPopup", lstApproval);
    }

    public void UpdateEntityStatus(int iId, int iStatusId, int iEntityTypeID)
    {
        try
        {
            UpdateApprovalStatusAndSendMail(iEntityTypeID, iId, iStatusId);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        
    }

    public bool UpdateApprovalStatusAndSendMail(int EntityTypeID, int RecordID, int StatusID)
    {
        try
        {
            ResourcesInfo ObjOwnerInfo = new ResourcesInfo();
            ResourcesInfo ObjAltOwnerInfo = new ResourcesInfo();
            ResourcesInfo ObjApproverInfo = new ResourcesInfo();

            BusinessProcessInfo objBusinesProcess = new BusinessProcessInfo();
            List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
            RecoveryPlan objRecoveryPlan = new RecoveryPlan();
            bool IsApprovalStatusUpdate = false;
            string EntityTypeName = string.Empty;
            string MailBody = string.Empty;
            string MailSubject = string.Empty;
            bool Send = false;
            switch (EntityTypeID)
            {
                case (int)BCPEnum.EntityType.BusinessProcess:
                    EntityTypeName = "Business Process";
                    IsApprovalStatusUpdate = _ProcessSrv.BussinessProcessBIA_Update_ApprovalStatus(RecordID.ToString(), StatusID, 0, Convert.ToInt32(_UserDetails.UserID));
                    objBusinesProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(RecordID), 1);
                    ObjOwnerInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(objBusinesProcess.ProcessOwnerID));
                    ObjAltOwnerInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(objBusinesProcess.ProcessOwnerID));
                    ObjApproverInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(objBusinesProcess.ApproverID));
                    #region "Sending Mail to Owner"
                    MailBody = GetMailBody(ObjOwnerInfo.ResourceName, objBusinesProcess.ProcessID.ToString(), ObjOwnerInfo.ResourceId.ToString(), EntityTypeID, StatusID.ToString(), objBusinesProcess.ProcessName, ObjApproverInfo.ResourceName, EntityTypeName);
                    MailSubject = GetMailSubject(EntityTypeName, objBusinesProcess.Status, objBusinesProcess.ProcessName);
                    Send = _BCMMail.SendMail(MailSubject, MailBody, ObjOwnerInfo.CompanyEmail, "", "", "", _UserDetails.OrgID.ToString());
                    EntityApprovalRecordsSave(EntityTypeID, objBusinesProcess.ProcessID, null, _UserDetails.UserID, ObjApproverInfo.ResourceId, StatusID, _UserDetails.UserID);

                    #endregion
                    #region "Sending Mail to Owner"
                    MailBody = GetMailBody(ObjAltOwnerInfo.ResourceName, objBusinesProcess.ProcessID.ToString(), ObjOwnerInfo.ResourceId.ToString(), EntityTypeID, StatusID.ToString(), objBusinesProcess.ProcessName, ObjApproverInfo.ResourceName, EntityTypeName);
                    Send = _BCMMail.SendMail(MailSubject, MailBody, ObjAltOwnerInfo.CompanyEmail, "", "", "", _UserDetails.OrgID.ToString());
                    EntityApprovalRecordsSave(EntityTypeID, objBusinesProcess.ProcessID, null, _UserDetails.UserID, ObjApproverInfo.ResourceId, StatusID, _UserDetails.UserID);

                    #endregion
                    break;
                case (int)BCPEnum.EntityType.RiskAssessment:
                    EntityTypeName = "Risk Assessment";
                    lstRiskManagement = _ProcessSrv.getBCMRiskList("0", "0", "0", "0", RecordID.ToString(), "1", "0", "0", "0", "0", string.Empty);
                    foreach (RiskManagement RiskItem in lstRiskManagement)
                    {
                        RiskItem.Status = StatusID;
                        RiskItem.RiskID = Convert.ToInt32(RecordID);
                        int iiD = _ProcessSrv.RiskAssessmentProcessFormUpdate(RiskItem);

                        ResourcesInfo objChampionResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(RiskItem.RiskChampionID));
                        ResourcesInfo objOwnerResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(RiskItem.RiskOwnerID));

                        if (objOwnerResource != null)
                        {
                            MailBody = GetMailBody(objOwnerResource.ResourceName, RecordID.ToString(), Convert.ToString(RiskItem.RiskChampionID), (int)BCPEnum.EntityType.RiskAssessment, StatusID.ToString(),
                                RiskItem.ProcessName, objChampionResource.ResourceName, BCPEnum.EntityType.RiskAssessment.ToString());
                            MailSubject = GetMailSubject(EntityTypeName, RiskItem.Status, RiskItem.RiskName);
                            Send = _BCMMail.SendMail(MailSubject, MailBody, ObjAltOwnerInfo.CompanyEmail, "", "", "", _UserDetails.OrgID.ToString());
                            EntityApprovalRecordsSave(EntityTypeID, RecordID, null, _UserDetails.UserID, objChampionResource.ResourceId, StatusID, _UserDetails.UserID);
                        }
                    }
                    break;
                case (int)BCPEnum.EntityType.RecoveryPlan:
                    EntityTypeName = "Recovery Plan";
                    bool success = false;
                    objRecoveryPlan = _ProcessSrv.GetRecoveryPlanByID(RecordID);
                    if (objRecoveryPlan != null)
                    {
                        objRecoveryPlan.PlanStageID = StatusID;
                        objRecoveryPlan.EntityType = ((int)BCPEnum.EntityType.RecoveryPlan).ToString();
                        objRecoveryPlan.ChangedBy = _UserDetails.UserID;
                        objRecoveryPlan.LastReviewDate = objRecoveryPlan.LastReviewDate == null || objRecoveryPlan.LastReviewDate == Convert.ToDateTime("01-01-001 00:00:00")
                                    ? DateTime.Parse("1753-01-01 00:00:00")
                                    : objRecoveryPlan.LastReviewDate;
                        success = _ProcessSrv.RecoveryPlanSave(objRecoveryPlan) > 0;

                        ObjOwnerInfo = _ProcessSrv.GetResourceNameByID(Convert.ToInt32(objBusinesProcess.ProcessOwnerID));
                        ObjApproverInfo = _ProcessSrv.GetResourceNameByID(Convert.ToInt32(objBusinesProcess.ApproverID));

                        EntityApprovalRecordsSave(EntityTypeID, objRecoveryPlan.ID, null, _UserDetails.UserID, ObjApproverInfo.ResourceId, StatusID, _UserDetails.UserID);
                    }
                    break;
            }

        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        return true;
    }

    public string GetMailBody(string strUserName, string ProcessID, string strUserID, int EntityTypeId, string statusID, string EntityName, string ApproverName, string EntityTypeName)
    {
        string strMailBody = string.Empty;
        string AppLink = string.Empty;
        try
        {
            //string AppLink = _Utilities.VaultConfigurations.ApplicationDirectory; // System.Configuration.ConfigurationManager.AppSettings["App_ROOT"].ToString();



            if (EntityTypeId == (int)(BCPEnum.EntityType.Application))
                AppLink = AppLink + @"/BCMApprovals/ApproveApplications.aspx";
            else if (EntityTypeId == (int)(BCPEnum.EntityType.BusinessProcess))
                AppLink = AppLink + @"/BCMProcessBIA/ManageBusinessProcesses/ManageBusinessProcess";
            else if (EntityTypeId == (int)(BCPEnum.EntityType.RiskManagement))
                AppLink = AppLink + @"/BCMRiskAssessment/ApproveRiskAssement.aspx";
            else
                AppLink = AppLink + @"/BCMEntities/ApprovalBCMEntity.aspx";

            //string Approverlink = string.Empty;

            ///string Approverlink = Server.HtmlEncode(AppLink + "?ProcessID=" + BCP.Security.CryptographyHelper.BCPEncrypt(ProcessID) + "&UserID=" + BCP.Security.CryptographyHelper.BCPEncrypt(strUserID) + "&OrgID=" + BCP.Security.CryptographyHelper.BCPEncrypt(_oUser.OrgID) + "&Usr_Name=" + BCP.Security.CryptographyHelper.BCPEncrypt(ApproverName) + "&Usr_ID=" + BCP.Security.CryptographyHelper.BCPEncrypt(strUserID));

            string Approverlink = AppLink;
            if (statusID.Equals(((int)BCPEnum.ApprovalType.WaitingforApproval).ToString()))
            {
                strMailBody = "Dear " + strUserName + ",<br /><br />Please <a href='" + Approverlink + "'>Click here</a> to Approve or Disapprove the BIA for  " + EntityTypeName + " : " + EntityName + "<br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
            }
            else if (statusID.Equals(((int)BCPEnum.ApprovalType.Approved).ToString()))
            {
                strMailBody = "Dear " + strUserName + ",<br /><br /> " + EntityTypeName + " :" + EntityName + " has been Approved by " + ApproverName + ".<br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
            }
            else if (statusID.Equals(((int)BCPEnum.ApprovalType.Disapproved).ToString()))
            {
                strMailBody = "Dear " + strUserName + ",<br /><br /> " + EntityTypeName + " :" + EntityName + " has been Disapproved by " + ApproverName + ".<br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        return strMailBody;
    }
    public string GetMailSubject(string EntityTypeName, int iStatusID, string EntityName)
    {
        string subjectline = string.Empty;
        try
        {
            if (iStatusID.Equals(((int)BCPEnum.ApprovalType.WaitingforApproval).ToString()))
                subjectline = " for Approval - ";
            else if (iStatusID.Equals(((int)BCPEnum.ApprovalType.Approved).ToString()))
                subjectline = " Approved - ";
            else if (iStatusID.Equals(((int)BCPEnum.ApprovalType.Disapproved).ToString()))
                subjectline = " Disapproved - ";

            subjectline = EntityTypeName + subjectline + EntityName;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        return subjectline;
    }

    public void EntityApprovalRecordsSave(int EntityTypeID, int EntityID, string Remarks, int SenderID, int ReceiverID, int StatusID, int CreatedBy)
    {
        EntityApprovalRecords objEntityApprovalRecords = new EntityApprovalRecords();
        objEntityApprovalRecords.EntityID = EntityTypeID;
        objEntityApprovalRecords.RecordID = EntityID;
        objEntityApprovalRecords.Remarks = Remarks;
        objEntityApprovalRecords.SenderID = SenderID;
        objEntityApprovalRecords.ReceiverID = ReceiverID;
        objEntityApprovalRecords.Status = StatusID;
        objEntityApprovalRecords.CreatedBy = CreatedBy;
        bool saved = _ProcessSrv.EntityApprovalRecordsSave(objEntityApprovalRecords);
    }


    public IActionResult GetRiskHeatmapData()
    {
        try
        {
            // Redirect to the RiskHeatmap_DynamicNew controller's action
            return RedirectToAction("RiskHeatmap_DynamicNew", "RiskHeatmap_DynamicNew", new { area = "BCMRiskAssessment" });
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return Content("Error loading Risk Heatmap data: " + ex.Message);
        }
    }

    public void GetRiskStatistics()
    {
        try
        {
            RiskStatistics objRiskStatistics = _ProcessSrv.GetRiskStatistics();

            DashboardCount objDashboardCount = new DashboardCount();
            objDashboardCount = _ProcessSrv.GetDashboardCounts(_UserDetails.OrgID.ToString(), _UserDetails.UserID.ToString());
            // Replace this line
            // ViewBag.RiskAssessmentCount = objDashboardCount.RiskAssessmentCount < 0 ? 0 : objDashboardCount.RiskAssessmentCount;

            // With this corrected version
            int riskAssessmentCount = 0;
            if (!string.IsNullOrEmpty(objDashboardCount.RiskAssessmentCount) && int.TryParse(objDashboardCount.RiskAssessmentCount, out riskAssessmentCount))
            {
                ViewBag.RiskAssessmentCount = riskAssessmentCount < 0 ? 0 : riskAssessmentCount;
            }
            else
            {
                ViewBag.RiskAssessmentCount = 0;
            }
            //ViewBag.TotalInitiatedRiskCount = objRiskStatistics.TotalInitiatedRiskCount < 0 ? 0 : objRiskStatistics.TotalInitiatedRiskCount;
            ViewBag.UpComingRisksForReviews = objRiskStatistics.UpComingRisksForReviews < 0 ? 0 : objRiskStatistics.UpComingRisksForReviews;
            ViewBag.PastReviewDateRisks = objRiskStatistics.PastReviewedDateRiskCount < 0 ? 0 : objRiskStatistics.PastReviewedDateRiskCount;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }

    public void GetRecoveryPlanStatistics()
    {
        try
        {
            RecoveryPlanStatistics objRecoveryPlanStatistics = _ProcessSrv.GetRecoveryPlanStatistics();
            ViewBag.TotalRecoveryPlanCount = objRecoveryPlanStatistics.TotalRecoveryPlanCount < 0 ? 0 : objRecoveryPlanStatistics.TotalRecoveryPlanCount;
            ViewBag.TotalInitiatedRecoveryPlanCount = objRecoveryPlanStatistics.TotalInitiatedRecoveryPlanCount < 0 ? 0 : objRecoveryPlanStatistics.TotalInitiatedRecoveryPlanCount;
            ViewBag.UpComingRecoveryPlansForReviews = objRecoveryPlanStatistics.UpComingRecoveryPlanForReviews < 0 ? 0 : objRecoveryPlanStatistics.UpComingRecoveryPlanForReviews;
            ViewBag.PastRecoveryPlansForReviews = objRecoveryPlanStatistics.PastRecoveryPlanForReviews < 0 ? 0 : objRecoveryPlanStatistics.PastRecoveryPlanForReviews;
            ViewBag.TotalWaitingForApproval = objRecoveryPlanStatistics.TotalWaitingForApproval < 0 ? 0 : objRecoveryPlanStatistics.TotalWaitingForApproval;
            ViewBag.TotalRecoveryPlanPercentage = objRecoveryPlanStatistics.TotalRecoveryPlanPercentage < 0 ? 0 : objRecoveryPlanStatistics.TotalRecoveryPlanPercentage;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }

    public void GetAuditStatistics()
    {
        try
        {
            AuditStatistics objAuditStatistics = _ProcessSrv.GetAuditStatistics();
            ViewBag.TotalAuditCount = objAuditStatistics.TotalAuditCount < 0 ? 0 : objAuditStatistics.TotalAuditCount;
            ViewBag.TotalInitiatedAuditCount = objAuditStatistics.TotalInitiatedAuditCount < 0 ? 0 : objAuditStatistics.TotalInitiatedAuditCount;
            ViewBag.UpComingAuditForReviews = objAuditStatistics.UpComingAuditForReviews < 0 ? 0 : objAuditStatistics.UpComingAuditForReviews;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }
    public void GetVendorStatistics()
    {
        try
        {
            VendorStatistics objVendorStatistics = _ProcessSrv.GetVendorStatistics();
            //ViewBag.TotalVendorCount = objVendorStatistics.TotalVendorCount < 0 ? 0 : objVendorStatistics.TotalVendorCount;
            ViewBag.TotalVendorCount = GetVendorCount();
            //ViewBag.TotalInitiatedVendorCount = objVendorStatistics.TotalInitiatedAuditCount < 0 ? 0 : objVendorStatistics.TotalInitiatedAuditCount;
            ViewBag.UpComingVendorForReviews = objVendorStatistics.UpComingVendorForReviews < 0 ? 0 : objVendorStatistics.UpComingVendorForReviews;
            //ViewBag.PastVendorForReviews = objVendorStatistics.PastVendorForReviews < 0 ? 0 : objVendorStatistics.PastVendorForReviews;

            ViewBag.PastVendorForReviews = ManagePastReviewCountVendor();
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }


    public int GetVendorCount()
    {
        List<BusinessProcessInfo> lstVendor = new List<BusinessProcessInfo>();
        try
        {
       
            lstVendor = _ProcessSrv.GetBIAVendor_OrgUnitLevel(_UserDetails.OrgID);

            lstVendor = GetBusinessProcess(lstVendor);
            ViewBag.SelectedOrgID = _UserDetails.OrgID.ToString();


            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    lstVendor = _Utilities.FilterListByOrgGroupID(lstVendor, _UserDetails.OrgGroupID);

                }
                else
                {

                    lstVendor = _Utilities.FilterListByOrgID(lstVendor, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstVendor = _Utilities.FilterListByRoleID(lstVendor, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                }
            }

            ViewBag.Vendor = lstVendor;
            int count = lstVendor.Count();
            return count;
        }
        catch (Exception ex)
        {
            return 1;
        }
    }




    public int ManagePastReviewCountVendor()
    {
        List<BusinessProcessInfo> lstVendor = new List<BusinessProcessInfo>();
        try
        {
           

            lstVendor = _ProcessSrv.GetBIAVendor_OrgUnitLevel(_UserDetails.OrgID);
            //lstVendor = GetBusinessProcess(lstVendor);
            ViewBag.SelectedOrgID = _UserDetails.OrgID.ToString();


            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    lstVendor = _Utilities.FilterListByOrgGroupID(lstVendor, _UserDetails.OrgGroupID);

                }
                else
                {

                    lstVendor = _Utilities.FilterListByOrgID(lstVendor, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstVendor = _Utilities.FilterListByRoleID(lstVendor, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                }
            }
            
            //ViewBag.TotalCount = lstVendor.Count;
            //ViewBag.UnderBCMCount = lstVendor.Where(x => x.ProcessCode != string.Empty).ToList().Count;
            //ViewBag.CriticalCount = lstVendor.Where(x => x.IsCritical == 1 && x.ProcessCode != string.Empty).ToList().Count;
            //ViewBag.NonCriticalCount = lstVendor.Where(x => x.IsCritical == 0 && x.ProcessCode != string.Empty).ToList().Count;

            ViewBag.ProcessCode = lstVendor
                              .GroupBy(p => new { p.ProcessCode, p.ProcessName }).Select(g => g.First())
                              .ToList();
            //if (upcoming == 1)
            //{
            //    var today = DateTime.Today;
            //    var next7 = today.AddDays(7);

            //    lstVendor = lstVendor
            //    .Where(r =>

            //         r.ContractEndDate >= today && r.ContractEndDate <= next7
            //    )
            //    .ToList();
            //    ViewBag.Vendor = lstVendor;
            //}


            
                var today = DateTime.Today;

                lstVendor = lstVendor
                .Where(r =>

                    r.ContractEndDate < today
                )
                .ToList();
                ViewBag.Vendor = lstVendor;
           

            ViewBag.Vendor = lstVendor;
            int Reviewcount = lstVendor.Count();
            return Reviewcount;
        }
        catch (Exception ex)
        {

            return 1;
        }

      
    }
    #region To Do List    

    private IActionResult GetToDoList()
    {
        //DataTable dtToDoList = new DataTable();
        //dtToDoList.Columns.Add("ID");
        //dtToDoList.Columns.Add("TaskDescription");
        //dtToDoList.Columns.Add("Status");
        //dtToDoList.Columns.Add("ChangedAt");
        //string strStatus = HttpContext.Session.GetString("ToDoStatus");
        //List<ToDoList> objToDoListNew = new List<ToDoList>();

        List<ToDoList> objToDoListVColl = _ProcessSrv.GetToDoListByUserID(Convert.ToInt32(_UserDetails.UserID), 0, Convert.ToInt32(_UserDetails.OrgID));
        //if (objToDoListVColl != null)
        //{
        //    foreach (ToDoList objToDoList in objToDoListVColl)
        //    {
        //        if (strStatus.Equals("0"))
        //        {
        //            objToDoListNew.Add(objToDoList);
        //            /// dtToDoList.Rows.Add(objToDoList.ID, objToDoList.TaskDescription, objToDoList.Status, Convert.ToDateTime(objToDoList.ChangedAt).ToString(Common.Common.DateTimeFormat));
        //        }
        //        else
        //        {
        //            if (strStatus.Equals(objToDoList.Status))
        //            {
        //                objToDoListNew.Add(objToDoList);
        //                //   dtToDoList.Rows.Add(objToDoList.ID, objToDoList.TaskDescription, objToDoList.Status, Convert.ToDateTime(objToDoList.ChangedAt).ToString(Common.Common.DateTimeFormat));
        //            }
        //        }
        //    }
        //}
        //HttpContext.Session.SetString("objToDoListVColl", JsonConvert.SerializeObject(objToDoListNew));
        //Session["ToDoList_Session"] = dtToDoList;


        ViewBag.ToDoList = objToDoListVColl;
        return PartialView("_TodoItemsList");
    }

    [HttpPost]
    public JsonResult AddTodoItem(string description)
    {
        ToDoList objItems = new ToDoList();
        bool bSuccess = false;
        try
        {
            objItems.ID = 0;
            objItems.TaskDescription = description;
            objItems.Status = (int)BCPEnum.ToDoStatus.New;
            objItems.ChangedBy = _UserDetails.UserID;
            objItems.OrgID = _UserDetails.OrgID;
            bSuccess = _ProcessSrv.SaveToDoList(objItems);
            GetToDoList();
            if (bSuccess)
            {
                return Json(new { success = true });
            }
            return Json(new { success = false, message = "Error" });
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpPost]
    public JsonResult UpdateTodoItem(int id, string description)
    {
        bool bSuccess = false;
        try
        {
            List<ToDoList> lstList = _ProcessSrv.GetToDoListByUserID(0, id, _UserDetails.OrgID);
            if (lstList != null && lstList.Count > 0)
            {
                ToDoList objTodoList = lstList[0];
                objTodoList.TaskDescription = description;
                objTodoList.ChangedBy = _UserDetails.UserID;
                objTodoList.ChangedAt = DateTime.Now.ToString();
                bSuccess = _ProcessSrv.SaveToDoList(objTodoList);

                // Get the updated list
                GetToDoList();

                if (bSuccess)
                {
                    return Json(new { success = true });
                }
                else
                {
                    return Json(new { success = false, message = "Failed to update item" });
                }
            }
            else
            {
                return Json(new { success = false, message = "Item not found" });
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpPost]
    public JsonResult TodoBulkAction(List<int> itemIds, string action)
    {
        try
        {
            int iStatusValue = 0;
            bool bSuccess = false;

            // Convert action string to corresponding enum value                        
            if (action.Equals("done", StringComparison.OrdinalIgnoreCase))
            {
                iStatusValue = (int)BCPEnum.ToDoStatus.Done;
            }
            else if (action.Equals("archive", StringComparison.OrdinalIgnoreCase))
            {
                iStatusValue = (int)BCPEnum.ToDoStatus.Archived;
            }
            else if (action.Equals("remove", StringComparison.OrdinalIgnoreCase))
            {
                iStatusValue = (int)BCPEnum.ToDoStatus.Removed;
            }

            if (iStatusValue > 0)
            {
                foreach (int item in itemIds)
                {
                    List<ToDoList> lstList = _ProcessSrv.GetToDoListByUserID(0, item, _UserDetails.OrgID);
                    if (lstList != null && lstList.Count > 0)
                    {
                        ToDoList objTodoList = lstList[0];
                        objTodoList.Status = iStatusValue;
                        objTodoList.ChangedBy = _UserDetails.UserID;
                        objTodoList.ChangedAt = DateTime.Now.ToString();
                        bSuccess = _ProcessSrv.SaveToDoList(objTodoList);
                    }
                }
            }

            // Get the updated list
            GetToDoList();

            return Json(new
            {
                success = true,
                message = $"Successfully processed {itemIds.Count} item(s)"
            });
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return Json(new { success = false, message = ex.Message });
        }
    }

    public IActionResult FilterTodoItems(string filter)
    {
        try
        {
            // Get all todo items for the current user
            List<ToDoList> todoItems = _ProcessSrv.GetToDoListByUserID(_UserDetails.UserID, 0, _UserDetails.OrgID);

            // Filter based on the selected filter type
            switch (filter.ToLower())
            {
                case "all":
                    // No filtering needed, return all items
                    break;

                //case "new":
                //    todoItems = todoItems.Where(t => t.Status == (int)BCPEnum.ToDoStatus.New).ToList();
                //    break;

                case "done":
                    todoItems = todoItems.Where(t => t.Status == (int)BCPEnum.ToDoStatus.Done).ToList();
                    break;

                case "archived":
                    todoItems = todoItems.Where(t => t.Status == (int)BCPEnum.ToDoStatus.Archived).ToList();
                    break;

                default:
                    // Invalid filter type, return all items
                    break;
            }

            // Store the current filter in session for later use
            HttpContext.Session.SetString("ToDoStatus", filter);

            // Pass the filtered items to the partial view
            ViewBag.ToDoList = todoItems;

            return PartialView("_TodoItemsList");
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return Content("Error loading todo items: " + ex.Message);
        }
    }

    #endregion

    #region WaitingForApproval

    private void DisplayWaitingForApprovalCount()
    {
        try
        {
            ViewBag.WFAProcessCount = GetProcessWFACount();
            ViewBag.WFARPCount = GetRPWFACount();
            ViewBag.WFARACount = GetRAWFACount();
            ViewBag.WFABCMAuditCount = GetBCMAuditWFACount();
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }

    // Start Business Process //

    private int GetProcessWFACount()
    {
        try
        {
            int iUserID = _UserDetails.UserID;

            //if (_Utilities.IsProductAdmin(_UserDetails.UserRole.ToString()))
            //{

            //}
            //else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole.ToString()))
            //{

            //}
            //else
            //{

            //}
            return GetProcessData().Count(x =>
                (x.ApproverID == iUserID ||
                 x.UnitHeadID == iUserID ||
                 x.DepartmentHeadID == iUserID ||
                 x.SubfunctionID == iUserID ||
                 x.CreatedBy == iUserID) &&
                x.Status == (int)BCPEnum.ApprovalType.WaitingforApproval);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return 0;
        }

    }

    private List<BusinessProcessInfo> GetProcessData()
    {
        try
        {
            return _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return new List<BusinessProcessInfo>();
        }
    }

    // End Business Process //

    // Start Recovery Plan //
    private int GetRPWFACount()
    {
        try
        {
            int iUserID = _UserDetails.UserID;

            return GetRPData().Count(x =>
                (x.PlanApproverID == iUserID ||
                 x.UnitHeadID == iUserID ||
                 x.AltUnitHeadID == iUserID ||
                 x.UnitBCPCorID == iUserID ||
                 x.AltBCPCorID == iUserID ||
                 x.CreatedBy == iUserID
                 ) && (x.PlanStageID == (int)BCPEnum.ApprovalType.WaitingforApproval) && x.IsActive == 1);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return 0;
        }
    }

    private List<RecoveryPlan> GetRPData()
    {
        try
        {
            return _ProcessSrv.GetRecoveryPlansList(_UserDetails.UserID, _UserDetails.OrgID);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return new List<RecoveryPlan>();
        }
    }

    // End Recovery Plan //

    // Start Risk Assement //

    private int GetRAWFACount()
    {
        try
        {
            return GetRAData().Count(x => x.Status == (int)BCPEnum.ApprovalType.WaitingforApproval && x.RiskChampionID == _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return 0;
        }
    }

    private List<RiskManagement> GetRAData()
    {
        try
        {
            return _ProcessSrv.getRiskAssementList(_UserDetails.OrgID);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return new List<RiskManagement>();
        }
    }

    // End Risk Assement //

    // Start BCM Audit //
    private int GetBCMAuditWFACount()
    {
        try
        {
            return GetBCMAuditData().Count(x => x.Status == (int)BCPEnum.ApprovalType.WaitingforApproval);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return 0;
        }
    }

    private List<BCMAuditMaster> GetBCMAuditData()
    {
        try
        {
            return _ProcessSrv.GetBCMAuditList(_UserDetails.OrgGroupID, _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return new List<BCMAuditMaster>();
        }
    }
    // End BCM Audit //

    #endregion
}
