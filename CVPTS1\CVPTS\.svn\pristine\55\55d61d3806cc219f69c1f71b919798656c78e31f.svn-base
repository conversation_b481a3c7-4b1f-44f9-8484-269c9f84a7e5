﻿@model BCM.BusinessClasses.SubFunction


@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@* <form asp-action="DeleteSubDepartment" method="post">
    <div class="modal-header d-grid text-center">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold">@Model.SubFunctionName</span>" ?</span>
    </div>

    <div>
        <input type="hidden" asp-for="SubFunctionID" />
    </div>
    <div class="modal-body text-center">
        <img src="~/img/isomatric/deletevctor.svg" width="260" />
    </div>
    <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
        <button type="submit" class="btn btn-primary btn-sm">Yes delete the file</button>
    </div>
</form> *@

<form asp-action="DeleteSubDepartment" method="post">
    <div>
        <input type="hidden" asp-for="SubFunctionID" />
        <input type="hidden" asp-for="SubFunctionName" />
    </div>
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    @* <div class="modal-header d-grid text-center">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.DepartmentName</span>" ?</span>
    </div> *@
    <div class="modal-body d-grid px">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.SubFunctionName</span>" ?</span>
    </div>
    <div class="modal-footer justify-content-center p-0">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>

@* <form asp-action="DeleteSubDepartment" method="post">
    <div>
        <input type="hidden" asp-for="SubFunctionID" />
    </div>
    <div class="modal-header d-grid text-center">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.SubFunctionName</span>" ?</span>
    </div>
    @* <img src="~/img/isomatric/delete.svg" width="260" /> 
    <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form> *@