﻿@model BCM.BusinessClasses.BCMTrainingMaster

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    ViewBag.Title = "Delete BCM Training Master";
}

@* <form asp-action="DeleteBCMTrainingMaster" method="post">
    <div>
        <input type="hidden" asp-for="ID" />
    </div>
    <div class="modal-header d-grid text-center">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.TrainingName</span>" ?</span>
    </div>
    <img src="~/img/isomatric/delete.svg" width="260" />
    <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form> *@



<form asp-action="DeleteBCMTrainingMaster" method="post">
    <div>
        <input type="hidden" name="ID" value="@Model.ID" />
        <input type="hidden" name="TrainingMasterID" value="@Model.TrainingMasterID" />
        <input type="hidden" name="TrainingName" value="@Model.TrainingName" />
        <input type="hidden" name="Status" value="@Model.Status" />
        <input type="hidden" name="OwnerID" value="@Model.OwnerID" />
    </div>
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    <div class="modal-body d-grid px-4 text-center">
        <h5 class="fw-semibold mb-3">Delete Training Confirmation</h5>
        <p class="mb-2">Do you really want to delete this training?</p>
        <div class="alert d-flex align-items-center mb-3" role="alert">
           
            <div>
                <strong>Training Name:</strong>
                <span class="text-danger fw-bold">@Model.TrainingName</span>
            </div>
        </div>
        <p class="text-muted small mb-0">
            <i class="cv-info-circle me-1"></i>
            This action will permanently delete the training and all associated questions and options.
        </p>
    </div>
    <div class="modal-footer justify-content-center p-3">
        <button type="button" class="btn btn-secondary btn-sm me-2" data-bs-dismiss="modal">
            <i class="cv-close me-1"></i> Cancel
        </button>
        <button type="submit" class="btn btn-danger btn-sm">
            <i class="cv-delete me-1"></i> Delete Training
        </button>
    </div>
</form>
