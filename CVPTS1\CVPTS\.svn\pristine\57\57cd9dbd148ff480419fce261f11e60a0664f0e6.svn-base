﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class DatasetController : BaseController
{
    private readonly ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;

    public DatasetController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    public IActionResult Dataset()
    {
        return View(); 
    }
    [HttpGet]
    public JsonResult GetAll()
    {
        try
        {
            var data = _ProcessSrv.GetAllDatasets();
            return Json(new { success = true, data });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "Failed to load datasets." });
        }
    }

    [HttpGet]
    public JsonResult GetTableAccessData()
    {
        try
        {
            List<TableAccessInfo> lstTableAccess = _ProcessSrv.GetCheckedTableAccess();
            return Json(new { success = true, data = lstTableAccess });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "Error loading table access data" });
        }
    }

    [HttpGet]
    public JsonResult GetById(int id)
    {
        try
        {
            var dataset = _ProcessSrv.GetDatasetById(id);
            if (dataset != null)
            {
                return Json(new { success = true, data = dataset });
            }
            return Json(new { success = false, message = "Dataset not found." });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "Error fetching dataset." });
        }
    }

    [HttpPost]
    public JsonResult Delete(int id)
    {
        try
        {
            var success = _ProcessSrv.DeleteDataset(id);
            return Json(new
            {
                success,
                message = success ? "Dataset deleted successfully." : "Failed to delete dataset."
            });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "Error deleting dataset." });
        }
    }
    [HttpGet]
    public JsonResult RunQuery(string runQuery)
    {
        try
        {
            var result = _ProcessSrv.RunQuery(runQuery);
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "Error executing query." });
        }
    }

    [HttpPost]
    public JsonResult GetTableColumns([FromBody] TableColumnRequest request)
    {
        try
        {           
            var tableName = request.TableName;

            List<string> columns = _ProcessSrv.GetColumnsBySchemaAndTable(tableName);
            return Json(new { success = true, data = columns });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "Error loading table columns." });
        }
    }

    [HttpPost]
    public JsonResult CreateOrUpdate([FromForm] DatasetInfo model)
    {
        try
        {
            bool success = _ProcessSrv.CreateOrUpdateDataset(model);
            return Json(new { success });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "Error saving dataset." });
        }
    }


}