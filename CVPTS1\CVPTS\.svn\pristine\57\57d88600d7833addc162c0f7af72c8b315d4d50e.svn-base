﻿using BCM.UI.Areas.BCMReports.ReportModels.BIAReport;
using BCM.UI.Controllers.PreBuildReport;
using Newtonsoft.Json;

namespace BCM.UI.Areas.BCMReports.ReportTemplate;

public partial class BIAReport : DevExpress.XtraReports.UI.XtraReport
{
    public BIAReport(string biareportdata)
    {
        InitializeComponent();

        if (!string.IsNullOrWhiteSpace(biareportdata))
        {
            var reportDetails = JsonConvert.DeserializeObject<BIAReportModel>(biareportdata);

            if (reportDetails != null)
            {
                var processIdentifications = reportDetails._processIdentifications;
                var vitalRecords = reportDetails._vitalRecords;
                var workspaceRequirements = reportDetails._workspaceRequirements;
                var hrRequirements = reportDetails._hrRequirements;
                var itRequirements = reportDetails._itRequirements;
                var workAreaRecovery = reportDetails._workAreaRecovery;
                var thirdParties = reportDetails._thirdParties;

                if (processIdentifications is not null && processIdentifications.Count > 0)
                {
                    var worksheet1 = processIdentifications.Where(x =>
                    x.ImpactName == "HSSE" || x.ImpactName == "Reputational" ||
                    x.ImpactTypeName == "HSSE Impact" || x.ImpactTypeName == "Reputational Impact").
                    Select(p => new ProcessIdentification
                    {
                        ProcessName = p.ProcessName,
                        ProcessOwner = p.ProcessOwner,
                        CriticalityAssessment = p.CriticalityAssessment = null,
                        HSSE = p.ImpactName == "HSSE" ? p.Cost : null,
                        Reputation = p.ImpactName == "Reputational" ? p.Cost : null,
                        ProgramDelivery = p.ImpactTypeName = null,
                        PriorityActivityList = p.PriorityActivityList = null
                    }).ToList();

                    var processIdentificationsDto = worksheet1.OrderBy(o => o.ProcessName).Distinct().ToList();

                    this.DetailReport.Visible = true;
                    this.DetailReport.DataSource = processIdentificationsDto;
                }
                else
                {
                    this.DetailReport.Visible = false;
                }

                if (vitalRecords is not null && vitalRecords.Count > 0)
                {
                    // Apply duplicate prevention logic - group by ProcessName and VitalRecordsName to preserve data
                    var vitalRecordsDto = vitalRecords
                        .GroupBy(x => new { x.ProcessName, x.VitalRecordsName })
                        .Select(g => g.First()) // Take the first record for each unique combination
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport.Visible = true;
                    this.DetailReport1.DataSource = vitalRecordsDto;
                }
                else
                {
                    this.DetailReport1.Visible = false;
                }

                if (workspaceRequirements is not null && workspaceRequirements.Count > 0)
                {
                    // Apply duplicate prevention logic - group by ProcessName and PrimaryLocation to preserve data
                    var workspaceRequirementDto = workspaceRequirements
                        .GroupBy(x => new { x.ProcessName, x.PrimaryLocation })
                        .Select(g => g.First()) // Take the first record for each unique combination
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport2.Visible = true;
                    this.DetailReport2.DataSource = workspaceRequirementDto;
                }
                else
                {
                    this.DetailReport2.Visible = false;
                }

                if (hrRequirements is not null && hrRequirements.Count > 0)
                {
                    // Apply duplicate prevention logic similar to HR Report, but only by ProcessName to preserve data
                    // The issue was that using all fields in DistinctBy was filtering out records with actual data
                    var hrRequirementsDto = hrRequirements
                        .GroupBy(x => x.ProcessName)
                        .Select(g => g.First()) // Take the first record for each process name
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport3.Visible = true;
                    this.DetailReport3.DataSource = hrRequirementsDto;
                }
                else
                {
                    this.DetailReport3.Visible = false;
                }

                if (itRequirements is not null && itRequirements.Count > 0)
                {
                    // Apply duplicate prevention logic - group by ProcessName and ITApplication to preserve data
                    var itRequirementsDto = itRequirements
                        .GroupBy(x => new { x.ProcessName, x.ITApplication })
                        .Select(g => g.First()) // Take the first record for each unique combination
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport4.Visible = true;
                    this.DetailReport4.DataSource = itRequirementsDto;
                }
                else
                {
                    this.DetailReport4.Visible = false;
                }

                if (workAreaRecovery is not null && workAreaRecovery.Count > 0)
                {
                    // Apply duplicate prevention logic - group by ProcessName and EquipmentSupply to preserve data
                    var workAreaRecoveryDto = workAreaRecovery
                        .GroupBy(x => new { x.ProcessName, x.EquipmentSupply })
                        .Select(g => g.First()) // Take the first record for each unique combination
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport5.Visible = true;
                    this.DetailReport5.DataSource = workAreaRecoveryDto;
                }
                else
                {
                    this.DetailReport5.Visible = false;
                }

                if (thirdParties is not null && thirdParties.Count > 0)
                {
                    // Apply duplicate prevention logic - group by ProcessName and ThirdPartName to preserve data
                    var thirdPartiesDto = thirdParties
                        .GroupBy(x => new { x.ProcessName, x.ThirdPartName })
                        .Select(g => g.First()) // Take the first record for each unique combination
                        .OrderBy(o => o.ProcessName)
                        .ToList();
                    this.DetailReport6.Visible = true;
                    this.DetailReport6.DataSource = thirdPartiesDto;
                }
                else
                {
                    this.DetailReport6.Visible = false;
                }
            }
        }

        lblUserName.Text = PreBuildReportController.ReportGeneratedBy.ToString();

        this.DisplayName = "BIA Report_" + DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss tt");
    }
    public BIAReport(int orgId, int unitId, int departId, int subdepartId)
    {
        InitializeComponent();
    }
}