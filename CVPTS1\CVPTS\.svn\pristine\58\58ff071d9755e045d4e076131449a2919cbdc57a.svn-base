﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Security.Helper;
using BCM.Shared;
using Newtonsoft.Json.Linq;
using Serilog;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CV.WebAPI.IncidentData
{
    public class CVIncidentManagementWeb
    {
        private readonly ProcessSrv _ProcessSrv;
        private readonly BCMMail _BCMMail;
        readonly CVLogger _CVLogger;
        private readonly IHttpContextAccessor _HttpContextAccessor;

        public CVIncidentManagementWeb(ProcessSrv ProcessSrv, BCMMail BCMMail, CVLogger CVLogger, IHttpContextAccessor HttpContextAccessor)
        {
            _ProcessSrv = ProcessSrv;
            _BCMMail = BCMMail;
            _CVLogger = CVLogger;
            _HttpContextAccessor = HttpContextAccessor;
        }

        public bool NotifyIncidentByIncidentID(NotifyIncident objNotifyIncident)
        {
            int iErrorCount = -1;
            try
            {
                int iIncidnetID = objNotifyIncident.IncidentID;
                bool SentNotificationToUser = false;
                IncidentManagement objIM = _ProcessSrv.GetIncidentManagementGetByIncidentID(iIncidnetID);
                if (objIM != null)
                {
                    if (objNotifyIncident.PlanExecutionType == 0)
                    {
                        List<RecoveryPlan> lstRecoveryPlan = _ProcessSrv.GetRecoveryPlanListByDisasterID(objIM.DisasterId);
                        if (lstRecoveryPlan.Count > 0)
                        {
                            foreach (RecoveryPlan objRecoveryPlan in lstRecoveryPlan)
                            {
                                List<RecoveryTaskStepInfo> lstRecoveryTaskStepInfo = Parse_JsonToListNew(objRecoveryPlan.ID.ToString());
                                if (lstRecoveryTaskStepInfo.Count > 0)
                                {
                                    foreach (RecoveryTaskStepInfo recoverySteps in lstRecoveryTaskStepInfo)
                                    {
                                        int iIncidentStepID = 0;
                                        recoverySteps.PlanID = objRecoveryPlan.ID.ToString();
                                        recoverySteps.DisasterID = objIM.DisasterId.ToString();
                                        recoverySteps.IncidentID = iIncidnetID.ToString();
                                        recoverySteps.ExecutedBy = "0";
                                        recoverySteps.StepStatus = recoverySteps.StepStatus;
                                        recoverySteps.Remarks = "";
                                        recoverySteps.ChangedBy = objNotifyIncident.UserID.ToString();
                                        iIncidentStepID = _ProcessSrv.RecoveryTaskStepStatusSave(recoverySteps);
                                        if (iIncidentStepID > 0)
                                        {
                                            NotifyIncidentToAllStackHolders(iIncidentStepID, objNotifyIncident.OrgID);
                                            if (!SentNotificationToUser||recoverySteps.Interdependency.Equals("0"))
                                            {
                                                if (recoverySteps.Sequence == "1")
                                                {
                                                    NotifyIncidentforTaskAssignment(iIncidentStepID, objNotifyIncident.OrgID.ToString());
                                                    SentNotificationToUser = true;
                                                }                                                
                                            }
                                        }
                                        else
                                        {
                                            iErrorCount += 1;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        List<RecoveryTaskStepInfo> lstRecoveryTaskStep = Parse_JsonToListNew(objIM.DisasterId.ToString());
                        if (lstRecoveryTaskStep.Count>0)
                        {
                            foreach (RecoveryTaskStepInfo objRecoveryTaskStep in lstRecoveryTaskStep)
                            {
                                int iIncidentStepID = 0;
                                iErrorCount = 0;
                                objRecoveryTaskStep.PlanID = objIM.DisasterId.ToString();
                                objRecoveryTaskStep.DisasterID = objIM.DisasterId.ToString();
                                objRecoveryTaskStep.IncidentID = iIncidnetID.ToString();
                                objRecoveryTaskStep.ExecutedBy="0";
                                objRecoveryTaskStep.StepStatus = objRecoveryTaskStep.StepStatus;
                                objRecoveryTaskStep.Remarks="";
                                objRecoveryTaskStep.ChangedBy = objNotifyIncident.UserID.ToString();
                                iIncidentStepID = _ProcessSrv.RecoveryTaskStepStatusSave(objRecoveryTaskStep);
                                if (iIncidentStepID > 0)
                                {
                                    NotifyIncidentToAllStackHolders(iIncidentStepID, objNotifyIncident.OrgID);
                                    if (!SentNotificationToUser||objRecoveryTaskStep.Interdependency.Equals("0"))
                                    {
                                        if (objRecoveryTaskStep.Sequence == "0")
                                        {
                                            NotifyIncidentforTaskAssignment(iIncidentStepID, objNotifyIncident.OrgID.ToString());
                                            SentNotificationToUser = true;
                                        }                                        
                                    }
                                }
                                else
                                {
                                    iErrorCount += 1;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return iErrorCount <= 0;
        }

        public List<RecoveryTaskStepInfo> Parse_JsonToListNew(string strplanId)
        {
            List<RecoveryTaskStepInfo> objRecoveryTaskStepColl = new List<RecoveryTaskStepInfo>();
            try
            {
                var workflow = _ProcessSrv.GetWorkflow_FromRecoveryPlan_ByPlanId(strplanId);
                if (workflow != null && !string.IsNullOrEmpty(workflow.WorkflowXoml))
                {
                    if (workflow.WorkflowXoml.TrimStart().StartsWith("["))
                    {
                        // Handle JSON array  
                        var jsonArray = JArray.Parse(workflow.WorkflowXoml);

                        // First pass: Create all step objects with sequence numbers
                        int sequence = 0;
                        Dictionary<string, RecoveryTaskStepInfo> stepsByUniqueId = new Dictionary<string, RecoveryTaskStepInfo>();
                        foreach (var item in jsonArray)
                        {
                            // Map each item to RecoveryTaskStepInfo  
                            RecoveryTaskStepInfo stepInfo = _ProcessSrv.GetRecoveryTaskStepsByPlanID(Convert.ToInt32(strplanId)).Where(x => x.StepID == item["stepId"].ToString()).ToList().FirstOrDefault();
                            if (stepInfo != null)
                            {
                                // Set sequence number
                                stepInfo.Sequence = (++sequence).ToString();
                                if (stepInfo.Sequence=="1")
                                {
                                    stepInfo.StepStatus="1";
                                }

                                else
                                {
                                    stepInfo.StepStatus="0";
                                }
                                // Default values
                                stepInfo.SuccessStepID = "0";
                                stepInfo.FailureStepID = "0";
                                stepInfo.IsCondition = "0";

                                // Store in dictionary for second pass
                                string uniqueId = item["uniqueId"]?.ToString();
                                if (!string.IsNullOrEmpty(uniqueId))
                                {
                                    stepsByUniqueId[uniqueId] = stepInfo;
                                }

                                objRecoveryTaskStepColl.Add(stepInfo);
                            }
                        }

                        // Second pass: Process conditions and set success/failure IDs
                        foreach (var item in jsonArray)
                        {
                            var conditionDetails = item["conditionDetails"];
                            if (conditionDetails != null && conditionDetails.HasValues)
                            {
                                string uniqueId = item["uniqueId"]?.ToString();
                                if (stepsByUniqueId.TryGetValue(uniqueId, out var stepInfo))
                                {
                                    // Mark as condition
                                    stepInfo.IsCondition = "1";

                                    // Process if condition (success path)
                                    var ifCondition = conditionDetails.FirstOrDefault(c => c["condition"]?.ToString() == "ifcondition");
                                    if (ifCondition != null)
                                    {
                                        string gotoAction = ifCondition["gotoAction"]?.ToString();
                                        if (!string.IsNullOrEmpty(gotoAction) && stepsByUniqueId.TryGetValue(gotoAction, out var successStep))
                                        {
                                            stepInfo.SuccessStepID = successStep.Sequence;
                                        }
                                    }

                                    // Process else condition (failure path)
                                    var elseCondition = conditionDetails.FirstOrDefault(c => c["condition"]?.ToString() == "elsecondition");
                                    if (elseCondition != null)
                                    {
                                        string gotoAction = elseCondition["gotoAction"]?.ToString();
                                        if (!string.IsNullOrEmpty(gotoAction) && stepsByUniqueId.TryGetValue(gotoAction, out var failureStep))
                                        {
                                            stepInfo.FailureStepID = failureStep.Sequence;
                                        }
                                    }
                                }
                            }
                        }

                        // Third pass: Set success steps for non-condition nodes (point to next sequence)
                        for (int i = 0; i < objRecoveryTaskStepColl.Count - 1; i++)
                        {
                            var currentStep = objRecoveryTaskStepColl[i];
                            var nextStep = objRecoveryTaskStepColl[i + 1];

                            // Only set SuccessStepID if it's not already set (by a condition)
                            if (currentStep.IsCondition == "0" || currentStep.SuccessStepID == "0")
                            {
                                currentStep.SuccessStepID = nextStep.Sequence;
                            }
                        }
                    }
                    else
                    {
                        // Handle JSON object  
                        var jsonObj = JObject.Parse(workflow.WorkflowXoml);
                        // Map jsonObj to RecoveryTaskStepInfo if needed  
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }

            return objRecoveryTaskStepColl;
        }
        //public List<RecoveryTaskStepInfo> Parse_JsonToList(string strplanId)
        //{
        //    List<RecoveryTaskStepInfo> objRecoveryTaskStepColl = new List<RecoveryTaskStepInfo>();
        //    int sequence = 0;
        //    try
        //    {
        //        var workflow = _ProcessSrv.GetWorkflow_FromRecoveryPlan_ByPlanId(strplanId);
        //        var jsonObj = JObject.Parse(workflow.WorkflowXoml);

        //        var firstStep = jsonObj["shapes"]?.Where(s => s["type"]?.ToString() == "rectangle").OrderBy(s => s["y"]?.ToObject<int>()).FirstOrDefault();
        //        var firstConnector = jsonObj["connectors"]?.FirstOrDefault(c => c["beginItemKey"]?.ToString() == firstStep?["key"]?.ToString());

        //        if (firstStep != null && firstConnector != null)
        //        {
        //            var firstStepName = firstStep["text"]?.ToString();
        //            var firstSuccessStepId = jsonObj["shapes"]?.FirstOrDefault(s => s["key"]?.ToString() == firstConnector["endItemKey"]?.ToString())?["customData"]?["stepId"]?.ToString();
        //            var currentStepKey = firstConnector["endItemKey"]?.ToString();

        //            RecoveryTaskStepInfo objRecoveryTaskStepInfo = _ProcessSrv.GetRecoveryTaskStepsByPlanID(Convert.ToInt32(strplanId)).Where(x => x.StepName == firstStepName).ToList().FirstOrDefault();
        //            objRecoveryTaskStepInfo.Sequence= sequence.ToString();
        //            //objRecoveryTaskStepInfo.SuccessStepID= firstSuccessStepId;
        //            objRecoveryTaskStepInfo.SuccessStepID= (sequence + 1).ToString();
        //            objRecoveryTaskStepInfo.FailureStepID= "0";
        //            objRecoveryTaskStepInfo.IsCondition= "0";
        //            objRecoveryTaskStepInfo.StepStatus= "1";

        //            var chkCondition = jsonObj["shapes"]?.FirstOrDefault(s => s["key"]?.ToString() == currentStepKey?.ToString() && s["type"]?.ToString() == "diamond");

        //            if (chkCondition != null)
        //            {
        //                var connectors = jsonObj["connectors"] as JArray;

        //                // Find Yes and No connectors
        //                var yesKey = connectors?.FirstOrDefault(c => (c["texts"] as JObject)?.Properties().Any(p => p.Value.ToString().Equals("Yes", StringComparison.OrdinalIgnoreCase)) == true && c["beginItemKey"]?.ToString() == chkCondition["key"]?.ToString());
        //                var noKey = connectors?.FirstOrDefault(c => (c["texts"] as JObject)?.Properties().Any(p => p.Value.ToString().Equals("No", StringComparison.OrdinalIgnoreCase)) == true && c["beginItemKey"]?.ToString() == chkCondition["key"]?.ToString());

        //                // Get next step and failure step
        //                var nextStep = jsonObj["shapes"]?.FirstOrDefault(s => s["key"]?.ToString() == yesKey?["endItemKey"]?.ToString() && s["type"]?.ToString() == "rectangle");
        //                var failureStep = jsonObj["shapes"]?.FirstOrDefault(s => s["key"]?.ToString() == noKey?["endItemKey"]?.ToString() && s["type"]?.ToString() == "rectangle");

        //                // Assign values with null checks
        //                currentStepKey = nextStep?["key"]?.ToString() ?? string.Empty;
        //                //objRecoveryTaskStepInfo.SuccessStepID = nextStep?["customData"]?["stepId"]?.ToString() ?? string.Empty;
        //                //objRecoveryTaskStepInfo.FailureStepID = failureStep?["customData"]?["stepId"]?.ToString() ?? string.Empty; 

        //                RecoveryTaskStepInfo objRecoveryTaskStepSequence = objRecoveryTaskStepColl.FirstOrDefault(x => x.StepID.ToString()==(failureStep?["customData"]?["stepId"]?.ToString() ?? string.Empty));
        //                objRecoveryTaskStepInfo.FailureStepID = objRecoveryTaskStepSequence.Sequence;
        //                objRecoveryTaskStepInfo.SuccessStepID =(sequence + 1).ToString();

        //                objRecoveryTaskStepInfo.IsCondition = "1";
        //            }

        //            objRecoveryTaskStepColl.Add(objRecoveryTaskStepInfo);


        //            while (currentStepKey != null)
        //            {
        //                chkCondition=null;

        //                var currentStep = jsonObj["shapes"]?.FirstOrDefault(s => s["key"]?.ToString() == currentStepKey && s["type"]?.ToString() == "rectangle");

        //                if (currentStep == null) break;

        //                sequence++;
        //                var currentStepName = currentStep["text"]?.ToString();
        //                var nextConnector = jsonObj["connectors"]?.FirstOrDefault(c => c["beginItemKey"]?.ToString() == currentStepKey);
        //                var nextStepId = jsonObj["shapes"]?.FirstOrDefault(s => s["key"]?.ToString() == nextConnector?["endItemKey"]?.ToString())?["customData"]?["stepId"]?.ToString();
        //                currentStepKey = nextConnector?["endItemKey"]?.ToString();

        //                RecoveryTaskStepInfo objRecoveryTaskStepnxt = _ProcessSrv.GetRecoveryTaskStepsByPlanID(Convert.ToInt32(strplanId)).Where(x => x.StepName == currentStepName).ToList().FirstOrDefault();
        //                objRecoveryTaskStepnxt.Sequence= sequence.ToString();
        //                //objRecoveryTaskStepnxt.SuccessStepID= nextStepId;
        //                objRecoveryTaskStepnxt.SuccessStepID= (sequence+1).ToString();
        //                objRecoveryTaskStepnxt.FailureStepID= "0";
        //                objRecoveryTaskStepnxt.IsCondition= "0";
        //                objRecoveryTaskStepnxt.StepStatus= "0";

        //                chkCondition = jsonObj["shapes"]?.FirstOrDefault(s => s["key"]?.ToString() == currentStepKey?.ToString() && s["type"]?.ToString() == "diamond");

        //                if (chkCondition != null)
        //                {
        //                    var connectors = jsonObj["connectors"] as JArray;

        //                    // Find Yes and No connectors
        //                    var yesKey = connectors?.FirstOrDefault(c => (c["texts"] as JObject)?.Properties().Any(p => p.Value.ToString().Equals("Yes", StringComparison.OrdinalIgnoreCase)) == true && c["beginItemKey"]?.ToString() == chkCondition["key"]?.ToString());
        //                    var noKey = connectors?.FirstOrDefault(c => (c["texts"] as JObject)?.Properties().Any(p => p.Value.ToString().Equals("No", StringComparison.OrdinalIgnoreCase)) == true && c["beginItemKey"]?.ToString() == chkCondition["key"]?.ToString());

        //                    // Get next step and failure step
        //                    var nextStep = jsonObj["shapes"]?.FirstOrDefault(s => s["key"]?.ToString() == yesKey?["endItemKey"]?.ToString() && s["type"]?.ToString() == "rectangle");
        //                    var failureStep = jsonObj["shapes"]?.FirstOrDefault(s => s["key"]?.ToString() == noKey?["endItemKey"]?.ToString() && s["type"]?.ToString() == "rectangle");

        //                    // Assign values with null checks
        //                    currentStepKey = nextStep?["key"]?.ToString() ?? string.Empty;
        //                    //objRecoveryTaskStepnxt.SuccessStepID = nextStep?["customData"]?["stepId"]?.ToString() ?? string.Empty;
        //                    //objRecoveryTaskStepnxt.FailureStepID = failureStep?["customData"]?["stepId"]?.ToString() ?? string.Empty;

        //                    RecoveryTaskStepInfo objRecoveryTaskStepSequence = objRecoveryTaskStepColl.FirstOrDefault(x => x.StepID.ToString()==(failureStep?["customData"]?["stepId"]?.ToString() ?? string.Empty));
        //                    objRecoveryTaskStepnxt.FailureStepID = objRecoveryTaskStepSequence.Sequence;
        //                    objRecoveryTaskStepnxt.SuccessStepID = (sequence+1).ToString();
        //                    objRecoveryTaskStepnxt.IsCondition = "1";
        //                }


        //                objRecoveryTaskStepColl.Add(objRecoveryTaskStepnxt);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //    }

        //    return objRecoveryTaskStepColl;
        //}

        public bool NotifyIncidentToAllStackHolders(int iIncidentStepID, int iOrgID)
        {
            bool bSuccess = false;
            try
            {
                RecoveryTaskStepInfo objTaskStepInfo = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(iIncidentStepID);
                IncidentManagement objIM = _ProcessSrv.GetIncidentManagementGetByIncidentID(Convert.ToInt32(objTaskStepInfo.IncidentID));
                bSuccess = NotifyIncidentToAllStackHolders_Thread(objTaskStepInfo, objIM, iOrgID);
                return bSuccess;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return bSuccess;
            }
        }

        public bool NotifyIncidentToAllStackHolders_Thread(RecoveryTaskStepInfo objTaskStepInfo, IncidentManagement objIM, int iOrgID)
        {
            bool Success = false;
            try
            {
                //Thread NotifyThread = new Thread(delegate ()
                //{
                    Success = SendMailsToAllStackHolders(objTaskStepInfo, objIM, iOrgID);
                //});
                //NotifyThread.Start();


                // Start a new background task using Task.Run instead of Thread
                //Task.Run(() =>
                //{
                //    Success = SendMailsToAllStackHolders(objTaskStepInfo, objIM, iOrgID);
                //});
                return Success;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return Success;
            }
        }

        private bool SendMailsToAllStackHolders(RecoveryTaskStepInfo objTaskStepInfo, IncidentManagement objIM, int iOrgID)
        {
            VaultSettings ObjVaultSettings = new VaultSettings();
            bool success = false;
            string strMailBodyAltOwner = string.Empty;
            string strMailBody = string.Empty;
            string AppUrl = string.Empty;

            StringBuilder sb = new StringBuilder();
            StringBuilder sb1 = new StringBuilder();
            StringBuilder sb2 = new StringBuilder();
            StringBuilder sbOwner = new StringBuilder();
            StringBuilder sbAltOwner = new StringBuilder();

            try
            {
                sb.Append(objTaskStepInfo.IncidentName);
                sb.Append("( " + objTaskStepInfo.IncidentCode + " )");
                sb.Append(" has been notified by ");
                sb.Append(objIM.NotifierName + " on " + objIM.NotificationTime);
                sb.Append(". As you are a part of the recovery plan, a related recovery task might be assigned to you shortly.");
                sb.Append(" Please Send" + "< EVENT A" + objTaskStepInfo.IncidentStepID + " > ");
                sb.Append(" to @@@@@ to confirm your availability for task execution.");

                strMailBody = sb.ToString();

                ObjVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(iOrgID);
                if (ObjVaultSettings.ApplicationDirectory != null)
                    AppUrl = ObjVaultSettings.ApplicationDirectory;

                //var httpContext = _HttpContextAccessor.HttpContext;
                //AppUrl = $"{httpContext.Request.Scheme}://{httpContext.Request.Host}{httpContext.Request.PathBase}";

                
                string strLink = $"{AppUrl}" + "/BCMIncidentManagement/UpdateTaskSteps/UpdateTaskSteps";

                string MailBody = "";
                string MailBodyAltStepOwner = "";


                sb1.Append("<br /><br /> ");

                //sb1.Append("Please" + " <a href='" + strLink + "?IncidentStepID=" + BCM.Security.Helper.CryptographyHelper.Encrypt(objTaskStepInfo.IncidentStepID.ToString()));
                //sbOwner.Append("&UserID=" + BCM.Security.Helper.CryptographyHelper.Encrypt(objTaskStepInfo.StepOwnerID.ToString()));
                //sbAltOwner.Append("&UserID=" + BCM.Security.Helper.CryptographyHelper.Encrypt(objTaskStepInfo.AltStepOwnerID.ToString()));
                //sb2.Append("&OrgID=" + BCM.Security.Helper.CryptographyHelper.Encrypt(objTaskStepInfo.OrgID.ToString()) + "'>");

                sb1.Append("Please" + " <a href='" + strLink + "?IncidentStepID=" + objTaskStepInfo.IncidentStepID.ToString());
                sbOwner.Append("&UserID=" + objTaskStepInfo.StepOwnerID.ToString());
                sbAltOwner.Append("&UserID=" + objTaskStepInfo.AltStepOwnerID.ToString());
                sb2.Append("&OrgID=" + objTaskStepInfo.OrgID.ToString() + "'>");

                sb2.Append("Click here</a> to Update the status of this task.<br /><br /> <br /> <br />");


                if (!string.IsNullOrEmpty(objTaskStepInfo.EmailTemplateText))
                {
                    sb2.Append("<b>Remarks : </b><br/>" + objTaskStepInfo.EmailTemplateText + " <br/><br /> <br /> ");
                }

                sb2.Append("<br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault");

                MailBody = "Dear " + objTaskStepInfo.StepOwnerName + ", <br/><br/>" + sb.ToString() + sb1.ToString() + sbOwner.ToString() + sb2.ToString(); //",<br /><br /> " + strMailBody + "<br /><br /> Please <a href='" + strLink + "?IncidentStepID=" + BCP.Security.CryptographyHelper.BCPEncrypt(objTaskStepInfo.IncidentStepID.ToString()) + "&UserID=" + BCP.Security.CryptographyHelper.BCPEncrypt(objTaskStepInfo.StepOwnerID.ToString()) + "&OrgID=" + BCP.Security.CryptographyHelper.BCPEncrypt(objTaskStepInfo.OrgID) + "'>Click here</a> to Update the status of this task<br /><br /> <br /> <br /> <b>Remarks : </b><br/>" + objTaskStepInfo.EmailTemplateText + " <br/><br /> <br /> <br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                MailBodyAltStepOwner = "Dear " + objTaskStepInfo.AltStepOwnerName + ", <br/><br/>" + sb.ToString() + sb1.ToString() + sbAltOwner.ToString() + sb2.ToString();  //+",<br /><br /> " + strMailBody + "<br /><br /> Please <a href='" + strLink + "?IncidentStepID=" + BCP.Security.CryptographyHelper.BCPEncrypt(objTaskStepInfo.IncidentStepID.ToString()) + "&UserID=" + BCP.Security.CryptographyHelper.BCPEncrypt(objTaskStepInfo.StepOwnerID.ToString()) + "&OrgID=" + BCP.Security.CryptographyHelper.BCPEncrypt(objTaskStepInfo.OrgID) + "'>Click here</a> to Update the status for this task<br /><br /> <br /> <br /> <b> Remarks : </b> <br/>" + objTaskStepInfo.EmailTemplateText + " <br/><br /> <br /> <br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";

                string SMSBodyOwner = strMailBody; //"Dear " + objTaskStepInfo.StepOwnerName + ", this is to inform you that " + strMailBody;
                string SMSBodyAltOwner = strMailBody;// "Dear " + objTaskStepInfo.AltStepOwnerName + ", this is to inform you that " + strMailBody;
                if (objTaskStepInfo != null)
                {

                    string strSubject = objIM.IncidentCode + "-FYA-Incident Notification_" + objTaskStepInfo.IncidentName;

                    string[] strMailCC = GetAllTeamIncidentResources(Convert.ToInt32(objTaskStepInfo.IncidentID));
                    string[] strMailTo = new string[] { objTaskStepInfo.StepOwnerEmail };

                    string[] strMailBCC = new string[] { string.Empty };
                    string[] strAttachment = new string[] { string.Empty };
                    strAttachment = GetAttachmentsByStepID(objTaskStepInfo.StepID);

                    success = _BCMMail.SendMail(strSubject, MailBody, strMailTo, strMailCC, strMailBCC, strAttachment, iOrgID.ToString());

                    AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                                            objTaskStepInfo.ChangedBy.ToString(), objTaskStepInfo.StepOwnerID.ToString(), strSubject, MailBody,
                                            Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());

                    if (success)
                    {
                        IncidentNotification_AddToLogger(objTaskStepInfo, "Mail Sent Successfully to Stacke Holder (FYI)  : " + objTaskStepInfo.StepOwnerName + " . On Mail ID : " + objTaskStepInfo.StepOwnerEmail, 1);
                    }
                    else
                    {
                        IncidentNotification_AddToLogger(objTaskStepInfo, "Mail Sent failed to Stacke Holder (FYI)  : " + objTaskStepInfo.StepOwnerName + " . On Mail ID : " + objTaskStepInfo.StepOwnerEmail, 0);
                    }

                    string[] strAltMailTo = new string[] { objTaskStepInfo.AltStepOwnerEmail };

                    success = _BCMMail.SendMail(strSubject, MailBodyAltStepOwner, strAltMailTo, strMailCC, strMailBCC, strAttachment, iOrgID.ToString());
                    AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                                            objTaskStepInfo.ChangedBy.ToString(), objTaskStepInfo.AltStepOwnerID.ToString(), strSubject, MailBodyAltStepOwner,
                                            Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());
                    if (success)
                    {
                        IncidentNotification_AddToLogger(objTaskStepInfo, "Mail Sent Successfully to Stacke Holder (FYI) : " + objTaskStepInfo.AltStepOwnerName + " . On Mail ID : " + objTaskStepInfo.AltStepOwnerEmail, 1);
                    }
                    else
                    {
                        IncidentNotification_AddToLogger(objTaskStepInfo, "Mail Sent failed to Stacke Holder (FYI) : " + objTaskStepInfo.AltStepOwnerName + " . On Mail ID : " + objTaskStepInfo.AltStepOwnerEmail, 0);
                    }
                    //success = BCPSms.SMSSend(objTaskStepInfo.StepOwnerMobileNo, objTaskStepInfo.StepOwnerName, SMSBodyOwner, iOrgID);
                    //AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                    //                    objTaskStepInfo.ChangedBy.ToString(), objTaskStepInfo.StepOwnerID.ToString(), "", SMSBodyOwner,
                    //                    Convert.ToInt16(BCPEnum.NotificationType.SMS).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());
                    //if (success)
                    //{
                    //    IncidentNotification_AddToLogger(objTaskStepInfo, "SMS Sent Successfully to Stake Holder (FYI) : " + objTaskStepInfo.StepOwnerName + " . On Mail ID : " + objTaskStepInfo.StepOwnerMobileNo, 1);
                    //}
                    //else
                    //{
                    //    IncidentNotification_AddToLogger(objTaskStepInfo, "SMS Sent failed to Stake Holder (FYI) : " + objTaskStepInfo.StepOwnerName + " . On Mail ID : " + objTaskStepInfo.StepOwnerMobileNo, 0);
                    //}

                    //success = BCPSms.SMSSend(objTaskStepInfo.AltStepOwnerMobileNo, objTaskStepInfo.AltStepOwnerName, SMSBodyAltOwner, iOrgID);
                    //AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                    //                    objTaskStepInfo.ChangedBy.ToString(), objTaskStepInfo.AltStepOwnerID.ToString(), "", SMSBodyAltOwner,
                    //                    Convert.ToInt16(BCPEnum.NotificationType.SMS).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());
                    //if (success)
                    //{
                    //    IncidentNotification_AddToLogger(objTaskStepInfo, "SMS Sent Successfully to Stake Holder (FYI) : " + objTaskStepInfo.AltStepOwnerName + " . On Mail ID : " + objTaskStepInfo.AltStepOwnerMobileNo, 1);
                    //}
                    //else
                    //{
                    //    IncidentNotification_AddToLogger(objTaskStepInfo, "SMS Sent failed to Stake Holder (FYI) : " + objTaskStepInfo.AltStepOwnerName + " . On Mail ID : " + objTaskStepInfo.AltStepOwnerMobileNo, 0);
                    //}
                }
                return success;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return success;
            }
        }

        public string[] GetAllTeamIncidentResources(int iIncidentId)
        {
            try
            {
                List<IncidentNotificationTeam> objIncTeamColl = _ProcessSrv.GetBCMGroupMemberResourcesListByIncidentId(iIncidentId);
                if (objIncTeamColl != null)
                {
                    string[] strResources = new string[objIncTeamColl.Count];
                    int i = 0;
                    foreach (IncidentNotificationTeam objIncTeam in objIncTeamColl)
                    {
                        strResources[i++] = objIncTeam.ResourceMail;
                    }

                    return strResources;
                }
                else
                    return null;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return null;
            }
        }

        private string[] GetAttachmentsByStepID(string stepID)
        {
            List<string> attchlist = new List<string>();
            try
            {
                Attachments objAttachment = _ProcessSrv.GetOrgLogoAttachmentListByID((int)BCPEnum.EntityType.RecoveryPlan_Step, Convert.ToInt32(stepID)); // BLOB TABLE
                if (objAttachment != null)
                {
                    if (objAttachment.AttchmentName != null)
                    {
                        if (objAttachment.AttachmentObj != null)
                        {
                            byte[] bytes = (Byte[])(objAttachment.AttachmentObj);


                            string filePath = "";//HttpRuntime.AppDomainAppPath; // Path.Combine(HttpRuntime.AppDomainAppPath, "email/teste.html");
                            string strPath = @"" + filePath + "Attachments\\";

                            // Directory.CreateDirectory(strPath);

                            if (!Directory.Exists(strPath))
                                if (!Directory.Exists(strPath))
                                    Directory.CreateDirectory(strPath);

                            string fileDownloadPath = strPath + "Step_" + stepID + "_" + DateTime.Now.ToString("yyyyMMddHHmm") + "_" + objAttachment.AttchmentName.ToString();
                            if (File.Exists(fileDownloadPath))
                                File.Delete(fileDownloadPath);
                            FileStream fs = new FileStream(fileDownloadPath, FileMode.OpenOrCreate, FileAccess.Write);
                            fs.Write(bytes, 0, bytes.Length);
                            fs.Close();
                            attchlist.Add(fileDownloadPath);
                        }
                    }
                }
                return attchlist.ToArray();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return attchlist.ToArray();
            }
        }

        public int AddIncidentNotification(string strIncidentID, string strIncidentStepID, string strStepID,
            string strNotifiedBy, string strNotifiedTo, string strSubject, string strBody, string strCommunicationID, string strTeamID,
            string strStatus, string strInBound, string StepExecutionStatus = "")
        {

            try
            {
                IncidentNotificationHistory obj = new IncidentNotificationHistory();

                obj.IncidentID = Convert.ToInt32(strIncidentID);
                obj.IncidentStepID = Convert.ToInt32(strIncidentStepID);
                obj.StepID = Convert.ToInt32(strStepID);
                obj.CommunicationID = Convert.ToInt32(strCommunicationID);
                obj.NotifiedBy = Convert.ToInt32(strNotifiedBy);
                obj.Subject = strSubject;
                obj.Body = strBody;
                obj.TeamID = Convert.ToInt32(strTeamID);
                obj.NotifiedTo = strNotifiedTo;
                obj.Status = strStatus;
                obj.IsInbound = Convert.ToInt32(strInBound);
                obj.StepExecutionStatus = StepExecutionStatus;

                return _ProcessSrv.IncidentNotificationHistorySave(obj);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return 0;
            }
        }

        private void IncidentNotification_AddToLogger(RecoveryTaskStepInfo objTaskStepInfo, string strMsg, int IsSuccess)
        {
            try
            {
                string strMsgLoc = string.Empty;
                if (IsSuccess == 1)
                {
                    _CVLogger.LogInfo(strMsg);
                }
                else
                {
                    _CVLogger.LogInfo(strMsg);
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
        }

        private void IncidentNotification_AddToLogger(RecoveryTaskStepInfo objTaskStepInfo, int SMSOrMail, int OwnerType)
        {
            try
            {
                _CVLogger.LogInfo("Plan" + objTaskStepInfo.PlanID + "," + "Incident" + objTaskStepInfo.IncidentID);

                string strMsg = string.Empty;

                if (SMSOrMail == 2 && OwnerType == 1)
                {
                    strMsg = "Step Assignment (FYA) - E-Mail sent to  Owner : " + objTaskStepInfo.StepOwnerName + " of Step : " + objTaskStepInfo.StepName + "(" + objTaskStepInfo.StepID + ")" + " on Email ID  : " + objTaskStepInfo.StepOwnerEmail;
                }
                else if (SMSOrMail == 2 && OwnerType == 2)
                {
                    strMsg = "Step Assignment (FYA) - E-Mail sent to Alternate Owner : " + objTaskStepInfo.AltStepOwnerName + " of Step : " + objTaskStepInfo.StepName + "(" + objTaskStepInfo.StepID + ")" + " on Email ID  : " + objTaskStepInfo.AltStepOwnerEmail;
                }
                else if (SMSOrMail == 1 && OwnerType == 1)
                {
                    strMsg = "Step Assignment (FYA) - SMS sent to Owner : " + objTaskStepInfo.StepOwnerName + " of Step : " + objTaskStepInfo.StepName + "(" + objTaskStepInfo.StepID + ")" + " on Mobile No : " + objTaskStepInfo.StepOwnerMobileNo;
                }
                else if (SMSOrMail == 1 && OwnerType == 2)
                {
                    strMsg = "Step Assignment (FYA) - SMS sent to Alternate Owner : " + objTaskStepInfo.AltStepOwnerName + " of Step : " + objTaskStepInfo.StepName + "(" + objTaskStepInfo.StepID + ")" + " on Mobile No : " + objTaskStepInfo.AltStepOwnerMobileNo;
                }

                _CVLogger.LogInfo(strMsg);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                _CVLogger.LogInfo(ex.Message);
            }
        }

        public bool NotifyIncidentforTaskAssignment(int iIncidentStepID, string iOrgID)
        {
            bool success = false;
            try
            {
                RecoveryTaskStepInfo objTaskStepInfo = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(iIncidentStepID);

                if (objTaskStepInfo != null)
                {
                    if (!objTaskStepInfo.StepStatus.Equals(Convert.ToInt16(BCPEnum.StepStatus.Completed).ToString()))
                    {
                        Notify_Thread(objTaskStepInfo, iOrgID);
                        objTaskStepInfo.StepStatus = ((int)(BCPEnum.StepStatus.TaskAssigned)).ToString();
                        objTaskStepInfo.NotificationSentTime = DateTime.Now.ToString();
                        //success = _ProcessSrv.RecoveryTaskStepStatusSave(objTaskStepInfo) > 0;
                        return true;
                    }
                }
                return success;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return success;
            }
        }

        public void Notify_Thread(RecoveryTaskStepInfo objTaskStepInfo, string iOrgID, string MessageToBeForworded = "")
        {
            bool Success = false;
            try
            {
                //Thread NotifyThread = new Thread(delegate ()
                //{
                    Success = Notify(objTaskStepInfo, iOrgID, MessageToBeForworded);
                //});
                //NotifyThread.Start();

                
                // Start a new background task using Task.Run instead of Thread
                //Task.Run(() =>
                //{
                //    Success = Notify(objTaskStepInfo, iOrgID, MessageToBeForworded);
                //});
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
        }

        private bool Notify(RecoveryTaskStepInfo objTaskStepInfo, string iOrgID, string MessageToBeForworded = "")
        {
            VaultSettings ObjVaultSettings = new VaultSettings();
            bool success = false;

            string StepOwnerID = "0";
            string StepOwnerName = string.Empty;
            string StepOwnerEmail = string.Empty;
            string StepOwerMobile = string.Empty;
            string AltStepOwnerID = "0";
            string AltStepOwerName = string.Empty;
            string AltStepOwnerEmail = string.Empty;
            string AltStepOwnerMobile = string.Empty;
            string ReassignedOwnerID = string.Empty;
            string ReassignedOwnerName = string.Empty;
            string ReassignedOwnerEmail = string.Empty;
            string ReassignedOwnerMobile = string.Empty;
            string strSMSBodyOwner = string.Empty;
            string strSMSBodyAltOwner = string.Empty;
            string strSMSBodyReassignedOwner = string.Empty;
            string AppUrl = string.Empty;
            try
            {
                StepOwnerID = objTaskStepInfo.StepOwnerID;
                StepOwnerName = objTaskStepInfo.StepOwnerName;
                StepOwnerEmail = objTaskStepInfo.StepOwnerEmail;
                StepOwerMobile = objTaskStepInfo.StepOwnerMobileNo;

                AltStepOwnerID = objTaskStepInfo.AltStepOwnerID;
                AltStepOwerName = objTaskStepInfo.AltStepOwnerName;
                AltStepOwnerEmail = objTaskStepInfo.AltStepOwnerEmail;
                AltStepOwnerMobile = objTaskStepInfo.AltStepOwnerMobileNo;

                ReassignedOwnerID = objTaskStepInfo.ReassignedTo;
                ReassignedOwnerName = objTaskStepInfo.ReassignedOwnerName;
                ReassignedOwnerEmail = objTaskStepInfo.ReassignedOwnerEmail;
                ReassignedOwnerMobile = objTaskStepInfo.ReassignedOwnerMobileNo;
                string strMailBody = GetEmailBodyForAssignment(objTaskStepInfo);

                if (!string.IsNullOrEmpty(MessageToBeForworded))
                {
                    //strSMSBodyOwner = GetSMSBodyForTaskAssignment(objTaskStepInfo, StepOwnerName) + " < " + MessageToBeForworded + " >";
                    //strSMSBodyAltOwner = GetSMSBodyForTaskAssignment(objTaskStepInfo, AltStepOwerName) + " < " + MessageToBeForworded + " >";

                    //strSMSBodyReassignedOwner = GetSMSBodyForTaskAssignment(objTaskStepInfo, ReassignedOwnerName) + " < " + MessageToBeForworded + " >";
                }
                else
                {
                    //strSMSBodyOwner = GetSMSBodyForTaskAssignment(objTaskStepInfo, StepOwnerName);
                    //strSMSBodyAltOwner = GetSMSBodyForTaskAssignment(objTaskStepInfo, AltStepOwerName);

                    //strSMSBodyReassignedOwner = GetSMSBodyForTaskAssignment(objTaskStepInfo, ReassignedOwnerName);
                }

                ObjVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt32(string.IsNullOrEmpty(iOrgID) == false ? iOrgID : "0"));
                if (ObjVaultSettings.ApplicationDirectory != null)
                    AppUrl = ObjVaultSettings.ApplicationDirectory;

                //var httpContext = _HttpContextAccessor.HttpContext;
                //AppUrl = $"{httpContext.Request.Scheme}://{httpContext.Request.Host}{httpContext.Request.PathBase}";


                string strLink = $"{AppUrl}" + "/BCMIncidentManagement/UpdateTaskSteps/UpdateTaskSteps";
                string MailBodyStepOwner = "";
                string MailBodyAltStepOwner = "";
                string MailBodyReassignedStepOwner = "";



                StringBuilder sb = new StringBuilder();
                StringBuilder sbOwner = new StringBuilder();
                StringBuilder sbAltOwner = new StringBuilder();
                StringBuilder sbReAssigned = new StringBuilder();
                StringBuilder sbTrail = new StringBuilder();

                sb.Append(",<br /><br /> " + strMailBody + "<br /><br />");
                sb.Append("Please " + " <a href='" + strLink);

                //sb.Append("?IncidentStepID=" + BCM.Security.Helper.CryptographyHelper.Encrypt(objTaskStepInfo.IncidentStepID.ToString()));
                //sbOwner.Append("&UserID=" + BCM.Security.Helper.CryptographyHelper.Encrypt(objTaskStepInfo.StepOwnerID.ToString()));
                //sbAltOwner.Append("&UserID=" + BCM.Security.Helper.CryptographyHelper.Encrypt(objTaskStepInfo.AltStepOwnerID.ToString()));
                //sbReAssigned.Append("&UserID=" + BCM.Security.Helper.CryptographyHelper.Encrypt(objTaskStepInfo.ReassignedTo.ToString()));                
                //sbTrail.Append("&OrgID=" + BCM.Security.Helper.CryptographyHelper.Encrypt(objTaskStepInfo.OrgID.ToString()));

                sb.Append("?IncidentStepID=" + objTaskStepInfo.IncidentStepID.ToString());
                sbOwner.Append("&UserID=" + objTaskStepInfo.StepOwnerID.ToString());
                sbAltOwner.Append("&UserID=" + objTaskStepInfo.AltStepOwnerID.ToString());
                sbReAssigned.Append("&UserID=" + objTaskStepInfo.ReassignedTo.ToString());
                sbTrail.Append("&OrgID=" + objTaskStepInfo.OrgID.ToString());


                sbTrail.Append("'>Click here</a>");
                sbTrail.Append(" to Update the status for this task.<br /><br /> <br /> <br />");

                if (!string.IsNullOrEmpty(objTaskStepInfo.EmailTemplateText))
                {
                    sbTrail.Append("<b>Remarks : </b><br/>" + objTaskStepInfo.EmailTemplateText + " <br/><br /> <br /> <br />");
                }

                if (!string.IsNullOrEmpty(MessageToBeForworded))
                {
                    sbTrail.Append("<b>User Comment : </b><br/>" + MessageToBeForworded + " <br/><br /> <br /> <br />");
                }

                sbTrail.Append("Thank you.<br /><br/><b>Admin</b><br />Continuity Vault");



                MailBodyStepOwner = "Dear " + StepOwnerName + sb.ToString() + sbOwner.ToString() + sbTrail.ToString();
                MailBodyAltStepOwner = "Dear " + AltStepOwerName + sb.ToString() + sbAltOwner.ToString() + sbTrail.ToString(); ;
                MailBodyReassignedStepOwner = "Dear " + ReassignedOwnerName + sb.ToString() + sbReAssigned.ToString() + sbTrail.ToString();


                string strSubjectOwner = objTaskStepInfo.IncidentCode + "-FYA-TaskStep(" + objTaskStepInfo.IncidentStepID + ")-" + objTaskStepInfo.IncidentName;
                string strSubjectAltOwner = objTaskStepInfo.IncidentCode + "-FYA-TaskStep(" + objTaskStepInfo.IncidentStepID + ")-" + objTaskStepInfo.IncidentName;
                string[] strMailCC = GetAllTeamIncidentResources(Convert.ToInt32(objTaskStepInfo.IncidentID));
                string[] strMailTo = new string[] { StepOwnerEmail };
                string[] strMailBCC = new string[] { string.Empty };
                string[] strAttachment = new string[] { string.Empty };


                success = _BCMMail.SendMail(strSubjectOwner, MailBodyStepOwner, strMailTo, strMailCC, strMailBCC, strAttachment, iOrgID);

                AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                                        objTaskStepInfo.ChangedBy.ToString(), StepOwnerID.ToString(), strSubjectOwner, MailBodyStepOwner,
                                        Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());

                IncidentNotification_AddToLogger(objTaskStepInfo, 2, 1);

                string[] strAltMailTo = new string[] { AltStepOwnerEmail };

                success = _BCMMail.SendMail(strSubjectAltOwner, MailBodyAltStepOwner, strAltMailTo, strMailCC, strMailBCC, strAttachment, iOrgID);
                AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                                        objTaskStepInfo.ChangedBy.ToString(), AltStepOwnerID.ToString(), strSubjectAltOwner, MailBodyAltStepOwner,
                                        Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());

                IncidentNotification_AddToLogger(objTaskStepInfo, 2, 2);

                //success = BCPSms.SMSSend(StepOwerMobile, StepOwnerName, strSMSBodyOwner, iOrgID);
                //AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                //                        objTaskStepInfo.ChangedBy.ToString(), StepOwnerID.ToString(), "", strSMSBodyOwner,
                //                        Convert.ToInt16(BCPEnum.NotificationType.SMS).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());

                //IncidentNotification_AddToLogger(objTaskStepInfo, 1, 1);

                //success = BCPSms.SMSSend(AltStepOwnerMobile, AltStepOwerName, strSMSBodyAltOwner, iOrgID);
                //AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                //                        objTaskStepInfo.ChangedBy.ToString(), AltStepOwnerID.ToString(), "", strSMSBodyAltOwner,
                //                        Convert.ToInt16(BCPEnum.NotificationType.SMS).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());
                //IncidentNotification_AddToLogger(objTaskStepInfo, 1, 2);

                if (!objTaskStepInfo.ReassignedTo.Equals("0"))
                {


                    string[] strReassignedTo = new string[] { ReassignedOwnerEmail };
                    success = _BCMMail.SendMail(strSubjectAltOwner, MailBodyReassignedStepOwner, strReassignedTo, strMailCC, strMailBCC, strAttachment, iOrgID);
                    AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                                            objTaskStepInfo.ChangedBy.ToString(), ReassignedOwnerID.ToString(), strSubjectAltOwner, MailBodyReassignedStepOwner,
                                            Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());

                    IncidentNotification_AddToLogger(objTaskStepInfo, 3, 1);



                    //success = BCPSms.SMSSend(ReassignedOwnerMobile, ReassignedOwnerName, strSMSBodyReassignedOwner, iOrgID);
                    //AddIncidentNotification(objTaskStepInfo.IncidentID.ToString(), objTaskStepInfo.IncidentStepID.ToString(), objTaskStepInfo.StepID.ToString(),
                    //                        objTaskStepInfo.ChangedBy.ToString(), ReassignedOwnerMobile.ToString(), "", strSMSBodyReassignedOwner,
                    //                        Convert.ToInt16(BCPEnum.NotificationType.SMS).ToString(), "0", (success) ? "1" : "0", "0", objTaskStepInfo.StepStatus.ToString());
                    //IncidentNotification_AddToLogger(objTaskStepInfo, 3, 2);
                }
                return success;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return success;
            }
        }

        private string GetEmailBodyForAssignment(RecoveryTaskStepInfo objTaskStepInfo)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.Append("Please start working on this Recovery Step now.");
                sb.AppendLine("<br/>");
                sb.AppendLine("<br/>");
                sb.AppendLine("Incident Name: ");
                sb.Append(objTaskStepInfo.IncidentName);
                sb.AppendLine("<br/>");
                sb.AppendLine("Step Name: ");
                sb.Append(objTaskStepInfo.StepName);
                sb.AppendLine("<br/>");
                sb.AppendLine("Step Description: ");
                sb.Append(objTaskStepInfo.StepDescription);

                return sb.ToString();

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return string.Empty;
            }
        }

        public bool UpdateStepStatus(RecoveryTaskStepInfo objRecoveryTaskStep, int iOrgID)
        {
            int iErrorCount = 0;
            try
            {
                if (_ProcessSrv.RecoveryTaskStepStatusSave(objRecoveryTaskStep) == 0)
                {
                    iErrorCount += 1;
                }
                else
                {
                    AddIncidentNotification(objRecoveryTaskStep.IncidentID.ToString(), objRecoveryTaskStep.IncidentStepID.ToString(),
                        objRecoveryTaskStep.StepID.ToString(), objRecoveryTaskStep.ChangedBy.ToString(), objRecoveryTaskStep.StepOwnerID, "", "",
                                        objRecoveryTaskStep.UpdatedBy, "0", "1", "1", objRecoveryTaskStep.StepStatus.ToString());
                    if (objRecoveryTaskStep.StepStatus.ToString().Equals(((int)BCPEnum.StepStatus.Completed).ToString()) ||
                        objRecoveryTaskStep.StepStatus.ToString().Equals(((int)BCPEnum.StepStatus.Failed).ToString()))
                    {
                        IncidentNotification_AddToLogger(objRecoveryTaskStep, "Step Status Changed - " + objRecoveryTaskStep.StepName +
                            " (" + objRecoveryTaskStep.StepID + ") - To " + (BCPEnum.StepStatus)Convert.ToInt32(objRecoveryTaskStep.StepStatus), 1);

                        NotifyToNextUserForTaskAssignment3(Convert.ToInt32(objRecoveryTaskStep.IncidentStepID), objRecoveryTaskStep.OrgID);
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }            
            return iErrorCount == 0;
        }

        public bool NotifyToNextUserForTaskAssignment3(int iIncidentStepID, int iOrgID)
        {
            bool success = false;
            try
            {
                RecoveryTaskStepInfo objTaskStepInfo = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(iIncidentStepID);

                if (objTaskStepInfo != null)
                {
                    int iIncidentID = Convert.ToInt32(objTaskStepInfo.IncidentID);

                    string MessageToForword = string.Empty;
                    string strNextIncidentStepID = "0";

                    List<RecoveryTaskStepInfo> objIncidentColl = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(iIncidentID, 0);

                    if (objIncidentColl != null)
                    {
                        if (objTaskStepInfo.IncidentStepID.ToString().Equals(iIncidentStepID.ToString()) &&
                            (objTaskStepInfo.IncidentID.ToString().Equals(iIncidentID.ToString())))
                        {
                            MessageToForword = GetMessageToForword(iIncidentStepID, iOrgID);

                            strNextIncidentStepID = GetNextIncidentStepID_For3(objIncidentColl, objTaskStepInfo, iOrgID, MessageToForword);

                            if (strNextIncidentStepID.Equals("0")) return true;

                            RecoveryTaskStepInfo objNextStepData = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(Convert.ToInt32(strNextIncidentStepID));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return success;
        }

        private List<RecoveryTaskStepInfo> CheckForCurrentSteps_DependentSteps(RecoveryTaskStepInfo objNextStepData, List<RecoveryTaskStepInfo> objIncidentColl)
        {
            List<RecoveryStepsDependentMapping> objStepsDependentMapp = new List<RecoveryStepsDependentMapping>();
            // List<RecoveryTaskStepInfo> objStepsCollResult = new List<RecoveryTaskStepInfo>();
            List<RecoveryTaskStepInfo> objRecStepsColl = new List<RecoveryTaskStepInfo>();
            try
            {


                objStepsDependentMapp = _ProcessSrv.RecoveryStepsDependentMapping_ByDependentStepID(objNextStepData.StepID);

                if (objIncidentColl != null && objStepsDependentMapp != null && objIncidentColl.Count > 0 && objStepsDependentMapp.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo item in objIncidentColl)
                    {
                        foreach (RecoveryStepsDependentMapping itemInner in objStepsDependentMapp)
                        {
                            if (itemInner.StepID == item.StepID)
                            {
                                objRecStepsColl.Add(item);
                            }
                        }
                    }
                }

                return objRecStepsColl;

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return objRecStepsColl;
            }
        }

        private string GetMessageToForword(int iIncidentStepID, int OrgID)
        {
            string MsgToForword = string.Empty;
            SMSResponse_Ooredoo obj = new SMSResponse_Ooredoo();
            try
            {

                obj = _ProcessSrv.GetMessageForNextStepUser(iIncidentStepID, OrgID);
                if (obj != null)
                {
                    if (!string.IsNullOrEmpty(obj.Message))
                        MsgToForword = obj.ResourceName + " : " + obj.Message;
                    else
                        MsgToForword = string.Empty;
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return MsgToForword;
        }

        private string GetNextIncidentStepID_For3(List<RecoveryTaskStepInfo> objRecoveryTaskStepColl, RecoveryTaskStepInfo objTaskStep, int iOrgID, string MassageForward)
        {
            string strNextIncidentStepID = "0";
            try
            {
                //List<RecoveryTaskStepInfo> objIncidentColl = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(Convert.ToInt32(objTaskStep.IncidentID), 0);
                if (objTaskStep.StepStatus.Equals((((int)BCPEnum.StepStatus.Completed)).ToString()))
                {
                    RecoveryTaskStepInfo ObjNextStep = objRecoveryTaskStepColl.FirstOrDefault(X => Convert.ToInt32(X.IncidentStepID) > Convert.ToInt32(objTaskStep.IncidentStepID));
                    if (ObjNextStep != null)
                    {
                        strNextIncidentStepID = ObjNextStep.IncidentStepID.ToString();
                        string nextStepID = strNextIncidentStepID;
                        Notify_MultipleDependentSteps_OnSucess(nextStepID, objTaskStep, objRecoveryTaskStepColl, Convert.ToInt32(iOrgID), MassageForward);
                    }
                }

                if (objTaskStep.StepStatus.Equals((((int)BCPEnum.StepStatus.Failed)).ToString()))
                {
                    //strNextIncidentStepID = objTaskStep.FailureStepID.ToString().Equals("0") ? objTaskStep.SuccessStepID.ToString() : objTaskStep.FailureStepID.ToString();
                    int iNxtIncID = Convert.ToInt32(objTaskStep.FailureStepID);
                    strNextIncidentStepID = iNxtIncID < 0 ? objTaskStep.SuccessStepID.ToString() : objTaskStep.FailureStepID.ToString();
                    string nextStepID = GetIncidentStepID_SequenceID(strNextIncidentStepID, objRecoveryTaskStepColl);

                    Notify_MultipleDependentSteps_OnFailed(nextStepID, objTaskStep, objRecoveryTaskStepColl, Convert.ToInt32(iOrgID), MassageForward);
                    //iCurrentAssignStep = Convert.ToInt32(strNextIncidentStepID);
                    //iFailedStep = Convert.ToInt32(objTaskStep.Sequence);
                    //NotifyIndependentStep_BetweenLoop(objRecoveryTaskStepColl, iCurrentAssignStep, iFailedStep, iOrgID);
                }
                if (objTaskStep.Interdependency.Equals("0"))
                    strNextIncidentStepID = objTaskStep.SuccessStepID.ToString();


                foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in objRecoveryTaskStepColl)
                {
                    if (objTaskStep.DependentOn.Equals("0") && objTaskStep.DependentOn.Equals(objRecoveryTaskStepInfo.StepID.ToString()))
                    {
                        if (!objRecoveryTaskStepInfo.StepStatus.Equals((((int)BCPEnum.StepStatus.Completed)).ToString()))
                        {
                            strNextIncidentStepID = "0";
                            break;
                        }
                    }

                    if (objTaskStep.IncidentStepID.Equals(objRecoveryTaskStepInfo.IncidentStepID))
                    {
                        break;
                    }
                }


                foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in objRecoveryTaskStepColl)
                {
                    if (objRecoveryTaskStepInfo.Sequence.ToString().Equals(strNextIncidentStepID))
                    {

                        if (objRecoveryTaskStepInfo.Interdependency.ToString().Equals("0") && (objTaskStep.StepStatus.Equals((((int)BCPEnum.StepStatus.Failed)).ToString()) || objTaskStep.StepStatus.Equals((((int)BCPEnum.StepStatus.Completed)).ToString())))
                        {
                            strNextIncidentStepID = objRecoveryTaskStepInfo.IncidentStepID;
                        }
                        else if (objRecoveryTaskStepInfo.Interdependency.ToString().Equals("0"))
                        {
                            strNextIncidentStepID = objRecoveryTaskStepInfo.SuccessStepID;
                            continue;
                        }
                        else if (objRecoveryTaskStepInfo.Interdependency.ToString().Equals("1") || objRecoveryTaskStepInfo.IsCondition.ToString().Equals("1"))
                        {
                            strNextIncidentStepID = objRecoveryTaskStepInfo.IncidentStepID.ToString();
                        }
                        else
                        {
                            strNextIncidentStepID = "0";
                        }
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return strNextIncidentStepID;
        }

        private string GetIncidentStepID_SequenceID(string sqID, List<RecoveryTaskStepInfo> objRecoveryTaskStepColl)
        {
            string strNextIncidentStepID = "0";
            try
            {
                foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in objRecoveryTaskStepColl)
                {
                    if (objRecoveryTaskStepInfo.Sequence.ToString().Equals(sqID))
                    {
                        strNextIncidentStepID = objRecoveryTaskStepInfo.IncidentStepID;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return strNextIncidentStepID;
        }

        private void Notify_MultipleDependentSteps_OnSucess(string strNextIncidentStepID, RecoveryTaskStepInfo objTaskStepInfo,
            List<RecoveryTaskStepInfo> objIncidentColl, int iOrgID, string MessageToForword)
        {
            try
            {
                RecoveryTaskStepInfo objNextStepData1 = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(Convert.ToInt32(strNextIncidentStepID));

                string strIncidentStepID_From1 = string.IsNullOrEmpty(objNextStepData1.IncidentStepID) ? "0" : objNextStepData1.IncidentStepID.ToString();

                if (strIncidentStepID_From1.ToString().Equals(strNextIncidentStepID))
                {
                    {
                        IncidentNotification_AddToLogger(objNextStepData1, "Next Step Assignment - " + objNextStepData1.StepName + "(" + objNextStepData1.StepID + ") - To Owner : " + objNextStepData1.StepOwnerName + " And Alternate Owner : " + objNextStepData1.AltStepOwnerName, 1);

                        Notify_Thread(objNextStepData1, iOrgID.ToString(), MessageToForword);

                        if (objNextStepData1.StepStatus == ((int)BCPEnum.StepStatus.Completed).ToString() || objNextStepData1.StepStatus == ((int)BCPEnum.StepStatus.Failed).ToString())
                        {
                            objNextStepData1.StepStatus = ((int)BCPEnum.StepStatus.ReInitiated).ToString();
                        }
                        else
                        {
                            objNextStepData1.StepStatus = ((int)(BCPEnum.StepStatus.TaskAssigned)).ToString();
                        }

                        objNextStepData1.NotificationSentTime = DateTime.Now.ToString();
                        objNextStepData1.Remarks = string.Empty;

                        _ProcessSrv.RecoveryTaskStepStatusSave(objNextStepData1);

                        NotifyToRemainingUsers(objIncidentColl, Convert.ToInt32(objNextStepData1.IncidentStepID), iOrgID.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
        }

        private void Notify_MultipleDependentSteps_OnFailed(string strNextIncidentStepID, RecoveryTaskStepInfo objTaskStepInfo, List<RecoveryTaskStepInfo> objIncidentColl, int iOrgID, string MessageToForword)
        {
            try
            {
                RecoveryTaskStepInfo objNextStepData1 = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(Convert.ToInt32(strNextIncidentStepID));
                string strIncidentStepID_From1 = string.IsNullOrEmpty(objNextStepData1.IncidentStepID) ? "0" : objNextStepData1.IncidentStepID.ToString();

                if (strIncidentStepID_From1.ToString().Equals(strNextIncidentStepID))
                {
                    {
                        IncidentNotification_AddToLogger(objNextStepData1, "Next Step Assignment - " + objNextStepData1.StepName + "(" + objNextStepData1.StepID + ") - To Owner : " + objNextStepData1.StepOwnerName + " And Alternate Owner : " + objNextStepData1.AltStepOwnerName, 1);

                        Notify_Thread(objNextStepData1, iOrgID.ToString(), MessageToForword);

                        if (objNextStepData1.StepStatus == ((int)BCPEnum.StepStatus.Completed).ToString() || objNextStepData1.StepStatus == ((int)BCPEnum.StepStatus.Failed).ToString())
                        {
                            objNextStepData1.StepStatus = ((int)BCPEnum.StepStatus.ReInitiated).ToString();
                        }
                        else
                        {
                            objNextStepData1.StepStatus = ((int)(BCPEnum.StepStatus.TaskAssigned)).ToString();
                        }
                        objNextStepData1.NotificationSentTime = DateTime.Now.ToString();
                        objNextStepData1.Remarks = string.Empty;
                        _ProcessSrv.RecoveryTaskStepStatusSave(objNextStepData1);
                        NotifyToRemainingUsers(objIncidentColl, Convert.ToInt32(objNextStepData1.IncidentStepID), iOrgID.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
        }

        public void NotifyToRemainingUsers(List<RecoveryTaskStepInfo> objRecoveryTaskStepColl, int iIncidentStepID, string iOrgID)
        {
            string strCompletedStepName = string.Empty;
            string strInProgressStepName = string.Empty;
            //string strStepName = string.Empty;
            //string strExpectedTime = string.Empty;
            //string strStepOwner = string.Empty;
            try
            {
                for (int i = 0; i < objRecoveryTaskStepColl.Count - 1; i++)
                {
                    RecoveryTaskStepInfo objRecoveryTaskStepInfo = objRecoveryTaskStepColl[i];
                    if (objRecoveryTaskStepInfo.StepStatus.Equals(((int)BCPEnum.StepStatus.InProgress).ToString()) ||
                        objRecoveryTaskStepInfo.StepStatus.Equals(((int)BCPEnum.StepStatus.TaskAssigned).ToString()))
                    {
                        strInProgressStepName = objRecoveryTaskStepInfo.StepName;
                        if (i > 0)
                        {
                            RecoveryTaskStepInfo objRecoveryTaskStepInfoPrevious = objRecoveryTaskStepColl[i - 1];
                            if (objRecoveryTaskStepInfoPrevious.StepStatus.Equals(((int)BCPEnum.StepStatus.Completed).ToString()))
                            {
                                strCompletedStepName = objRecoveryTaskStepInfoPrevious.StepName;
                            }
                        }
                        break;
                    }
                }
                NotifyToRemainingUsers_Thread(objRecoveryTaskStepColl, iIncidentStepID, iOrgID, strCompletedStepName, strInProgressStepName);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
        }

        private void NotifyToRemainingUsers_Thread(List<RecoveryTaskStepInfo> objIncidentColl, int iIncidentStepID, string iOrgID, string strCompletedStepName, string strInProgressStepName)
        {
            try
            {
                //Thread NotifyThread = new Thread(delegate ()
                //{
                    SendNotificationsToRemainingUsers(objIncidentColl, iIncidentStepID, iOrgID, strCompletedStepName, strInProgressStepName);
                //});
                //NotifyThread.Start();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            //Task.Run(() =>
            //{
            //    Success = SendNotificationsToRemainingUsers(objTaskStepInfo, iOrgID, MessageToBeForworded);
            //});
        }

        public void SendNotificationsToRemainingUsers(List<RecoveryTaskStepInfo> objRecoveryTaskStepColl, int iIncidentStepID,
            string iOrgID, string strCompletedStepName, string strInProgressStepName)
        {
            try
            {
                foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfo in objRecoveryTaskStepColl)
                {
                    string strMailBody1 = "Step " + strCompletedStepName + " has been completed.";
                    string strMailBody2 = "Currently, step " + strInProgressStepName + " is in progress";
                    string strMailBody3 = " and you are expected to be available to complete your task in approximately  ";
                    string strMailBody5 = string.Empty;  // " in case primary owner is not available ";
                    string strMailBody6 = CreateIncidentEmailBody(Convert.ToInt32(objRecoveryTaskStepInfo.IncidentID));

                    DateTime dtNow = DateTime.Now;
                    foreach (RecoveryTaskStepInfo objRecoveryTaskStepInfoInner in objRecoveryTaskStepColl)
                    {
                        if (objRecoveryTaskStepInfo.IncidentStepID.Equals(objRecoveryTaskStepInfoInner.IncidentStepID)) break;

                        switch (Convert.ToInt16(objRecoveryTaskStepInfoInner.TimeUnit))
                        {
                            case 1:
                                {
                                    dtNow = dtNow.AddMinutes(Convert.ToDouble(objRecoveryTaskStepInfoInner.EstTime));
                                    break;
                                }
                            case 2:
                                {
                                    dtNow = dtNow.AddHours(Convert.ToDouble(objRecoveryTaskStepInfoInner.EstTime));
                                    break;
                                }
                            case 3:
                                {
                                    dtNow = dtNow.AddDays(Convert.ToDouble(objRecoveryTaskStepInfoInner.EstTime));
                                    break;
                                }
                            case 4:
                                {
                                    dtNow = dtNow.AddMonths(Convert.ToInt32(objRecoveryTaskStepInfoInner.EstTime));
                                    break;
                                }
                        }
                    }

                    string strTimeUnit = string.Empty;
                    string strMailBody4 = GetCompletionTime(DateTime.Now, dtNow);
                    string strMailBody = string.Empty;
                    string MailBodyText = string.Empty;
                    string strAltMailBody = string.Empty;
                    string StrReassignedMailBody = string.Empty;
                    bool success = false;

                    if (string.IsNullOrEmpty(strCompletedStepName))
                    {
                        MailBodyText = strMailBody2 + strMailBody3 + strMailBody4 + " at " + dtNow.ToString();
                    }
                    else
                    {
                        MailBodyText = strMailBody1 + strMailBody2 + strMailBody3 + strMailBody4 + " at " + dtNow.ToString();
                    }

                    MailBodyText = "Following is the recovery status of the Incident-";


                    string StepOwnerID = string.Empty;
                    string StepOwnerName = string.Empty;
                    string StepOwnerEmail = string.Empty;
                    string StepOwerMobile = string.Empty;
                    string AltStepOwnerID = string.Empty;
                    string AltStepOwerName = string.Empty;
                    string AltStepOwnerEmail = string.Empty;
                    string AltStepOwerMobile = string.Empty;
                    string ReassignedOwnerID = string.Empty;
                    string ReassignedOwnerName = string.Empty;
                    string ReassignedOwnerEmail = string.Empty;
                    string ReassignedOwnerMobile = string.Empty;

                    StepOwnerID = objRecoveryTaskStepInfo.StepOwnerID;
                    StepOwnerName = objRecoveryTaskStepInfo.StepOwnerName;

                    StepOwnerEmail = objRecoveryTaskStepInfo.StepOwnerEmail;
                    StepOwerMobile = objRecoveryTaskStepInfo.StepOwnerMobileNo;
                    AltStepOwnerID = objRecoveryTaskStepInfo.AltStepOwnerID;
                    AltStepOwerName = objRecoveryTaskStepInfo.AltStepOwnerName;
                    AltStepOwnerEmail = objRecoveryTaskStepInfo.AltStepOwnerEmail;
                    AltStepOwerMobile = objRecoveryTaskStepInfo.AltStepOwnerMobileNo;


                    if (Convert.ToInt32(objRecoveryTaskStepInfo.ReassignedTo) > 0)
                    {
                        ReassignedOwnerID = objRecoveryTaskStepInfo.ReassignedTo;
                        ReassignedOwnerName = objRecoveryTaskStepInfo.ReassignedOwnerName;
                        ReassignedOwnerEmail = objRecoveryTaskStepInfo.ReassignedOwnerEmail;
                        ReassignedOwnerMobile = objRecoveryTaskStepInfo.ReassignedOwnerMobileNo;

                        if (!string.IsNullOrEmpty(objRecoveryTaskStepInfo.EmailTemplateText))
                        {
                            StrReassignedMailBody = "Dear " + objRecoveryTaskStepInfo.ReassignedOwnerName + ",<br /><br /> " + MailBodyText + "<br /><br> " + strMailBody6 + "<br/>  <br /> <br /> <br /> <b> Remarks : </b> <br/> " + objRecoveryTaskStepInfo.EmailTemplateText + "<br/><br /> <br /> <br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                        }
                        else
                        {
                            StrReassignedMailBody = "Dear " + objRecoveryTaskStepInfo.ReassignedOwnerName + ",<br /><br /> " + MailBodyText + "<br /><br> " + strMailBody6 + "<br/>Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                        }
                    }


                    if (!string.IsNullOrEmpty(objRecoveryTaskStepInfo.EmailTemplateText))
                    {
                        strMailBody = "Dear " + objRecoveryTaskStepInfo.StepOwnerName + ",<br /><br /> " + MailBodyText + "<br /><br> " + strMailBody6 + "<br/>  <br /> <br /> <br /> <b> Remarks : </b> <br/> " + objRecoveryTaskStepInfo.EmailTemplateText + "<br/><br /> <br /> <br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                        strAltMailBody = "Dear " + objRecoveryTaskStepInfo.AltStepOwnerName + ",<br /><br /> " + MailBodyText + strMailBody5 + "<br /><br> " + strMailBody6 + "<br/> <br /> <br /> <br /> <b> Remarks : </b> <br/>" + objRecoveryTaskStepInfo.EmailTemplateText + "<br/> <br /> <br /> <br /> Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                    }
                    else
                    {
                        strMailBody = "Dear " + objRecoveryTaskStepInfo.StepOwnerName + ",<br /><br /> " + MailBodyText + "<br /><br> " + strMailBody6 + "<br/>Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                        strAltMailBody = "Dear " + objRecoveryTaskStepInfo.AltStepOwnerName + ",<br /><br /> " + MailBodyText + strMailBody5 + "<br /><br> " + strMailBody6 + "<br/>Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
                    }


                    string strSubject = objRecoveryTaskStepInfo.IncidentCode + "-FYI-Incident Status-" + objRecoveryTaskStepInfo.IncidentName;

                    string[] strMailCC = GetAllTeamIncidentResources(Convert.ToInt32(objRecoveryTaskStepInfo.IncidentID));

                    string[] strMailTo = new string[] { StepOwnerEmail };
                    string[] strMailBCC = new string[] { string.Empty };
                    string[] strAttachment = new string[] { string.Empty };


                    success = _BCMMail.SendMail(strSubject, strMailBody, strMailTo, strMailCC, strMailBCC, strAttachment, iOrgID);
                    AddIncidentNotification(objRecoveryTaskStepInfo.IncidentID.ToString(), objRecoveryTaskStepInfo.IncidentStepID.ToString(), objRecoveryTaskStepInfo.StepID.ToString(),
                                            objRecoveryTaskStepInfo.ChangedBy.ToString(), StepOwnerID, strSubject, strMailBody,
                                            Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (success) ? "1" : "0", "0", objRecoveryTaskStepInfo.StepStatus.ToString());

                    if (success)
                    {
                        IncidentNotification_AddToLogger(objRecoveryTaskStepInfo, "Mail Sent Successfully to Remaining Resource (FYI)  : " + objRecoveryTaskStepInfo.StepOwnerName + " . On Mail ID : " + objRecoveryTaskStepInfo.StepOwnerEmail, 1);
                    }
                    else
                    {
                        IncidentNotification_AddToLogger(objRecoveryTaskStepInfo, "Mail Sent failed to Remaining Resource (FYI)  : " + objRecoveryTaskStepInfo.StepOwnerName + " . On Mail ID : " + objRecoveryTaskStepInfo.StepOwnerEmail, 0);
                    }

                    string[] strAltMailTo = new string[] { AltStepOwnerEmail };

                    success = _BCMMail.SendMail(strSubject, strAltMailBody, strAltMailTo, strMailCC, strMailBCC, strAttachment, iOrgID);
                    AddIncidentNotification(objRecoveryTaskStepInfo.IncidentID.ToString(), objRecoveryTaskStepInfo.IncidentStepID.ToString(), objRecoveryTaskStepInfo.StepID.ToString(),
                                            objRecoveryTaskStepInfo.ChangedBy.ToString(), AltStepOwnerID, strSubject, strAltMailBody,
                                            Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (success) ? "1" : "0", "0", objRecoveryTaskStepInfo.StepStatus.ToString());

                    if (success)
                    {
                        IncidentNotification_AddToLogger(objRecoveryTaskStepInfo, "Mail Sent Successfully to Remaining Resource (FYI)  : " + objRecoveryTaskStepInfo.AltStepOwnerName + " . On Mail ID : " + objRecoveryTaskStepInfo.AltStepOwnerEmail, 1);
                    }
                    else
                    {
                        IncidentNotification_AddToLogger(objRecoveryTaskStepInfo, "Mail Sent failed to Remaining Resource (FYI)  : " + objRecoveryTaskStepInfo.AltStepOwnerName + " . On Mail ID : " + objRecoveryTaskStepInfo.AltStepOwnerEmail, 0);
                    }

                    if (Convert.ToInt32(objRecoveryTaskStepInfo.ReassignedTo) > 0)
                    {
                        string[] strReassignedMailTo = new string[] { ReassignedOwnerEmail };

                        success = _BCMMail.SendMail(strSubject, StrReassignedMailBody, strReassignedMailTo, strMailCC, strMailBCC, strAttachment, iOrgID);
                        AddIncidentNotification(objRecoveryTaskStepInfo.IncidentID.ToString(), objRecoveryTaskStepInfo.IncidentStepID.ToString(), objRecoveryTaskStepInfo.StepID.ToString(),
                                                objRecoveryTaskStepInfo.ChangedBy.ToString(), ReassignedOwnerID, strSubject, strAltMailBody,
                                                Convert.ToInt16(BCPEnum.NotificationType.EMail).ToString(), "0", (success) ? "1" : "0", "0", objRecoveryTaskStepInfo.StepStatus.ToString());

                        if (success)
                        {
                            IncidentNotification_AddToLogger(objRecoveryTaskStepInfo, "Mail Sent Successfully to Remaining Resource (FYI)  : " + objRecoveryTaskStepInfo.ReassignedOwnerName + " . On Mail ID : " + objRecoveryTaskStepInfo.ReassignedOwnerEmail, 1);
                        }
                        else
                        {
                            IncidentNotification_AddToLogger(objRecoveryTaskStepInfo, "Mail Sent failed to Remaining Resource (FYI)  : " + objRecoveryTaskStepInfo.ReassignedOwnerName + " . On Mail ID : " + objRecoveryTaskStepInfo.ReassignedOwnerEmail, 0);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
        }

        public string CreateIncidentEmailBody(int iIncidentId)
        {
            StringBuilder sb = new StringBuilder();
            DateTime dtNow = DateTime.Now;
            try
            {
                IncidentManagement objIncidentManagement = _ProcessSrv.GetIncidentManagementGetByIncidentID(iIncidentId);
                List<RecoveryTaskStepInfo> objIncidentColl = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(iIncidentId, 0);

                if (objIncidentColl != null && objIncidentManagement != null)
                {
                    sb.Append("<html xmlns=#unknown>");
                    sb.Append("<head><title>Continuity Vault Email Notification</title></head>");
                    sb.Append("<body>");
                    sb.Append("<table runat='server' style='margin-bottom: 0; width: 100%; border: 1px solid #DDDDDD; left: 0; padding: 0; position: relative; top: 0; background-color: rgba(0, 0, 0, 0); border-collapse: collapse; border-spacing: 0; max-width: 100%;'  class='table'>");
                    sb.Append("<thead>");
                    sb.Append("<tr ><th colspan='11' style='border: 1px solid #DDDDDD; color: #005580; font-family: Arial; border-bottom: 1px solid #DDDDDD; font-size:16px; font-weight:bold; text-align:left; line-height: 20px; padding-top: 8px; padding-bottom: 8px; padding-left: 5px; background: none repeat scroll 0 0 #F1F1F1;  '>Incident Progress Status</th></tr>");

                    sb.Append("<tr>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle; font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Step Name</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Step Description</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Status</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Previous Step(s)</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Next Step(s)</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Estimated Time to Complete</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Step Assign Time</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Actual Completion Time</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Step Owner</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Alt.Step Owner</th>");
                    sb.Append("<th style='border-top: 0 none; vertical-align: middle;  font-size:11px; font-family: Arial; font-weight: bold; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>Updated By</th>");

                    sb.Append("</thead>");


                    foreach (RecoveryTaskStepInfo objStep in objIncidentColl)
                    {
                        sb.Append("<tr>");
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objStep.StepName + " (" + objStep.IncidentStepID + ") " + "</td>"); //step Name
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objStep.StepDescription + "</td>"); // Step Description
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>");  // Status

                        if (Convert.ToInt32(objStep.StepStatus) == (int)BCPEnum.StepStatus.Acknowledged)
                        {
                            sb.Append("<div style='color: #41546f; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: yellow;'>" + GetStatus(objStep.StepStatus) + "</div>");
                        }
                        if (Convert.ToInt32(objStep.StepStatus) == (int)BCPEnum.StepStatus.NotInitiated)
                        {
                            sb.Append("<div style=' color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #3A87AD;'>" + GetStatus(objStep.StepStatus) + "</div>");
                        }
                        if (Convert.ToInt32(objStep.StepStatus) == (int)BCPEnum.StepStatus.InProgress)
                        {
                            sb.Append("<div style='color: #41546f; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #00e600;'>" + GetStatus(objStep.StepStatus) + "</div>");
                        }
                        if (Convert.ToInt32(objStep.StepStatus) == (int)BCPEnum.StepStatus.Completed)
                        {
                            sb.Append("<div style='color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #004d00;'>" + GetStatus(objStep.StepStatus) + "</div>");
                        }
                        if (Convert.ToInt32(objStep.StepStatus) == (int)BCPEnum.StepStatus.Failed)
                        {
                            sb.Append("<div style='color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #004d00;'>" + GetStatus(objStep.StepStatus) + "</div>");
                        }

                        if (Convert.ToInt32(objStep.StepStatus) == (int)BCPEnum.StepStatus.TaskAssigned)
                        {
                            sb.Append("<div style='color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #ff8000;'>" + GetStatus(objStep.StepStatus) + "</div>");
                        }

                        if (Convert.ToInt32(objStep.StepStatus) == (int)BCPEnum.StepStatus.ReInitiated)
                        {
                            sb.Append("<div style='color: #FFFFFF; display: inline-block; font-size: 11.844px; font-weight: bold; line-height: 14px; padding: 5px; text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); vertical-align: middle; white-space: nowrap; border-radius: 3px; background-color: #33adff;'>" + GetStatus(objStep.StepStatus) + "</div>");
                        }

                        sb.Append("</td>");  // Status
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>");  // Previous steps
                        sb.Append(CheckForCurrentSteps_DependentOnSteps(objStep.StepID, objIncidentColl));
                        sb.Append("</td>");   // Previous steps
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>");  // Next steps

                        if (Convert.ToInt32(objStep.StepStatus) == (int)BCPEnum.StepStatus.Completed)
                        {
                            sb.Append(getDependentStepSteps_OnCuerrentStep(objStep.StepID, objIncidentColl));
                        }
                        else if (Convert.ToInt32(objStep.StepStatus) == (int)BCPEnum.StepStatus.Failed)
                        {
                            sb.Append(GetIncidentStepIDBySequence(objStep.FailureStepID, objIncidentColl));
                        }
                        else
                        {
                            sb.Append("NA");
                        }

                        sb.Append("</td>");   // Next steps
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objStep.EstTime + " " + GetTimeValue(objStep.TimeUnit) + "</td>"); // Estimated Time and Unit
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objStep.NotificationSentTime + "</td>"); // Step Assigned time

                        string CompletionTime = string.IsNullOrEmpty(objStep.CompletionTime) == false ? objStep.CompletionTime != DateTime.MinValue.ToString() ? objStep.CompletionTime : "NA" : "NA";

                        if (Convert.ToInt32(objStep.StepStatus) != ((int)BCPEnum.StepStatus.Completed) && Convert.ToInt32(objStep.StepStatus) != ((int)BCPEnum.StepStatus.Failed))
                        {
                            CompletionTime = "NA";
                        }

                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + CompletionTime + "</td>");  // Completion Time    //strTime + "<br />" +
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objStep.StepOwnerName + "<br/>" + objStep.StepOwnerMobileNo + "<br/>" + objStep.StepOwnerEmail + "</td>");
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + objStep.AltStepOwnerName + "<br/>" + objStep.AltStepOwnerMobileNo + "<br/>" + objStep.AltStepOwnerEmail + "</td>");
                        sb.Append("<td class='boldCell' style='border-top: 0 none; font-family: Arial; font-size:11px; vertical-align: middle; line-height: 20px; padding: 8px; text-align: left; background: none repeat scroll 0 0 #F1F1F1; color: #333333; border-bottom: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; box-shadow: 1px 1px 0 #FFFFFF inset; text-shadow: 1px 1px 0 #FFFFFF;'>" + GetUpdatedStatus(objStep.UpdatedBy) + "</td>");
                        sb.Append("</tr>");
                    }
                    sb.Append("</table>");
                    sb.Append("</body>");
                    sb.Append("</html>");
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return sb.ToString();
        }

        private int CheckForCurrentSteps_DependentOnStepsCompletedOrFailed(RecoveryTaskStepInfo objNextStepData, List<RecoveryTaskStepInfo> objIncidentColl)
        {
            try
            {
                List<RecoveryStepsDependentMapping> objStepsDependentMapp = new List<RecoveryStepsDependentMapping>();

                List<RecoveryTaskStepInfo> objStepsCollResult = new List<RecoveryTaskStepInfo>();

                objStepsDependentMapp = _ProcessSrv.RecoveryStepsDependentMapping_ByStepID(objNextStepData.StepID);

                if (objIncidentColl != null && objStepsDependentMapp != null && objIncidentColl.Count > 0 && objStepsDependentMapp.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo item in objIncidentColl)
                    {
                        foreach (RecoveryStepsDependentMapping itemInner in objStepsDependentMapp)
                        {
                            if (itemInner.DependentStepID == item.StepID)
                            {
                                objStepsCollResult.Add(item);
                            }
                        }
                    }
                }

                int iResult_AllDependentStepsCompleted = 1;

                if (objStepsCollResult.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo item in objStepsCollResult)
                    {
                        if (Convert.ToInt32(item.StepStatus) == (int)BCPEnum.StepStatus.Completed || Convert.ToInt32(item.StepStatus) == (int)BCPEnum.StepStatus.Failed)
                        {
                            iResult_AllDependentStepsCompleted = 1;
                            break;
                        }
                        else
                        {
                            iResult_AllDependentStepsCompleted = 0;
                            //break;//commented by pravin for goto step
                        }
                    }
                }

                return iResult_AllDependentStepsCompleted;

            }
            catch (Exception)
            {
                return 1;
            }
        }

        #region EmailTemplate Methods

        private string GetCompletionTime(DateTime strNotificationDate, DateTime strCompletionDate)
        {
            StringBuilder sbtime = new StringBuilder();
            try
            {
                TimeSpan diffdate = strCompletionDate - strNotificationDate;
                if (diffdate.Days > 0)
                {
                    sbtime.Append(diffdate.Days + " Day(s) ");
                }
                if (diffdate.Hours > 0)
                {
                    sbtime.Append(diffdate.Hours + " Hour(s) ");
                }
                if (diffdate.Minutes > 0)
                {
                    sbtime.Append(diffdate.Minutes + " Minute(s) ");
                }
                if (diffdate.Seconds > 0)
                {
                    sbtime.Append(diffdate.Seconds + " Second(s)");
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return sbtime.ToString();
        }

        private string GetTimeValue(object objTime)
        {
            string strName = "";
            try
            {
                if (objTime.ToString().Equals(((int)BCPEnum.StepTimeUnit.Minute).ToString()))
                {
                    strName = BCPEnum.StepTimeUnit.Minute.ToString() + "(s)";
                }
                if (objTime.ToString().Equals(((int)BCPEnum.StepTimeUnit.Hour).ToString()))
                {
                    strName = BCPEnum.StepTimeUnit.Hour.ToString() + "(s)";
                }
                if (objTime.ToString().Equals(((int)BCPEnum.StepTimeUnit.Day).ToString()))
                {
                    strName = BCPEnum.StepTimeUnit.Day.ToString() + "(s)";
                }
                if (objTime.ToString().Equals(((int)BCPEnum.StepTimeUnit.Month).ToString()))
                {
                    strName = BCPEnum.StepTimeUnit.Month.ToString() + "(s)";
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return strName;
        }

        private string GetStatus(object objStatus)
        {
            string strStatus = "";
            try
            {
                if (objStatus != null)
                {
                    if (objStatus.ToString().Equals(((int)BCPEnum.StepStatus.NotInitiated).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.NotInitiated.ToString();
                    }
                    else if (objStatus.ToString().Equals(((int)BCPEnum.StepStatus.Acknowledged).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.Acknowledged.ToString();
                    }
                    else if (objStatus.ToString().Equals(((int)BCPEnum.StepStatus.InProgress).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.InProgress.ToString();
                    }
                    else if (objStatus.ToString().Equals(((int)BCPEnum.StepStatus.Completed).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.Completed.ToString();
                    }
                    else if (objStatus.ToString().Equals(((int)BCPEnum.StepStatus.Failed).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.Completed.ToString();  //BCPEnum.StepStatus.Failed.ToString();
                    }
                    else if (objStatus.ToString().Equals(((int)BCPEnum.StepStatus.TaskAssigned).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.TaskAssigned.ToString();
                    }
                    else if (objStatus.ToString().Equals(((int)BCPEnum.StepStatus.ReInitiated).ToString()))
                    {
                        strStatus = BCPEnum.StepStatus.ReInitiated.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return strStatus;
        }

        private string GetUpdatedStatus(object objUpdateStatus)
        {
            string strStatus = "";
            try
            {
                if (objUpdateStatus != null)
                {
                    if (objUpdateStatus.ToString().Equals(((int)BCPEnum.NotificationType.EMail).ToString()))
                    {
                        strStatus = BCPEnum.NotificationType.EMail.ToString();
                    }
                    if (objUpdateStatus.ToString().Equals(((int)BCPEnum.NotificationType.SMS).ToString()))
                    {
                        strStatus = BCPEnum.NotificationType.SMS.ToString();
                    }
                    if (objUpdateStatus.ToString().Equals(((int)BCPEnum.NotificationType.Call).ToString()))
                    {
                        strStatus = BCPEnum.NotificationType.Call.ToString();
                    }
                    if (objUpdateStatus.ToString().Equals(((int)BCPEnum.NotificationType.CVault).ToString()))
                    {
                        strStatus = BCPEnum.NotificationType.CVault.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return strStatus;
        }

        private string getDependentStepSteps_OnCuerrentStep(string istepID, List<RecoveryTaskStepInfo> objIncidentColl)
        {
            string strDepedentSteps = "";
            try
            {
                List<RecoveryStepsDependentMapping> objStepsDependentMapp = new List<RecoveryStepsDependentMapping>();
                List<RecoveryTaskStepInfo> objRecStepsColl = new List<RecoveryTaskStepInfo>();
                objStepsDependentMapp = _ProcessSrv.RecoveryStepsDependentMapping_ByDependentStepID(istepID);

                if (objIncidentColl != null && objStepsDependentMapp != null && objIncidentColl.Count > 0 && objStepsDependentMapp.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo item in objIncidentColl)
                    {
                        foreach (RecoveryStepsDependentMapping itemInner in objStepsDependentMapp)
                        {
                            if (itemInner.StepID == item.StepID)
                            {
                                objRecStepsColl.Add(item);
                            }
                        }
                    }
                }

                if (objRecStepsColl != null && objRecStepsColl.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo item in objRecStepsColl)
                    {
                        strDepedentSteps += item.StepName + " (" + item.IncidentStepID + ") ,";
                    }
                }

                if (string.IsNullOrEmpty(strDepedentSteps))
                {
                    strDepedentSteps = "NA";
                }
                else
                {
                    strDepedentSteps = strDepedentSteps.TrimEnd(',');
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return strDepedentSteps;
        }

        private string GetIncidentStepIDBySequence(string seq, List<RecoveryTaskStepInfo> objIncidentColl) // Return StepName and StepID Both.
        {
            string strIDName = string.Empty;
            try
            {
                foreach (RecoveryTaskStepInfo objStep in objIncidentColl)
                {
                    if (seq.Equals(objStep.Sequence))
                    {
                        strIDName = objStep.StepName + "(" + objStep.IncidentStepID + ") ";
                        break;
                    }
                }

                if (string.IsNullOrEmpty(strIDName))
                {
                    strIDName = "NA";
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
            }
            return strIDName;
        }

        private string CheckForCurrentSteps_DependentOnSteps(string iStepID, List<RecoveryTaskStepInfo> objIncidentColl)
        {
            string strDepedentSteps = "";
            try
            {
                List<RecoveryStepsDependentMapping> objStepsDependentMapp = new List<RecoveryStepsDependentMapping>();

                List<RecoveryTaskStepInfo> objStepsCollResult = new List<RecoveryTaskStepInfo>();

                objStepsDependentMapp = _ProcessSrv.RecoveryStepsDependentMapping_ByStepID(iStepID);

                if (objIncidentColl != null && objStepsDependentMapp != null && objIncidentColl.Count > 0 && objStepsDependentMapp.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo item in objIncidentColl)
                    {
                        foreach (RecoveryStepsDependentMapping itemInner in objStepsDependentMapp)
                        {
                            if (itemInner.DependentStepID == item.StepID)
                            {
                                objStepsCollResult.Add(item);
                            }
                        }
                    }
                }

                if (objStepsCollResult != null && objStepsCollResult.Count > 0)
                {
                    foreach (RecoveryTaskStepInfo item in objStepsCollResult)
                    {
                        strDepedentSteps += item.StepName + " (" + item.IncidentStepID + ") ,";
                    }
                }

                if (string.IsNullOrEmpty(strDepedentSteps))
                {
                    strDepedentSteps = "NA";
                }
                else
                {
                    strDepedentSteps = strDepedentSteps.TrimEnd(',');
                }

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);            
                return "NA";
            }
            return strDepedentSteps;

        }

        #endregion
    }
}
