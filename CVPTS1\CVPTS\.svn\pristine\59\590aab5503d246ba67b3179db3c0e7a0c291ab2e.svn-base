﻿$(document).ready(function () {
    var selectizeInstance = $('#ChartType').selectize()[0].selectize;
    selectizeInstance.on('change', function (value) {
        debugger
        OverallWidgetChart(value, 'widgetCreationCard')

    });
});

$("#search-widget").on('keyup', function () {

    var filter = $(this).val();
    let categoryFlagStatus = true
    $("#prebuildList .widgetName").each(function () {

        var $i = 0;

        var splitText = $(this).text().split(" ")
        if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
            $i++;
        }

        if ($i > 0) {
            $(this).closest("#prebuildList .widgetList").show();
            categoryFlagStatus = false
        } else {
            $(this).closest("#prebuildList .widgetList").hide();
        }
    });
})

$("#search-widget").on('keyup', function () {

    var filter = $(this).val();
    let categoryFlagStatus = true
    $("#customList .widgetName").each(function () {

        var $i = 0;

        var splitText = $(this).text().split(" ")
        if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
            $i++;
        }

        if ($i > 0) {
            $(this).closest("#customList .widgetList").show();
            categoryFlagStatus = false
        } else {
            $(this).closest("#customList .widgetList").hide();
        }
    });
})


WidgetBuilderList()







$(".btn_save").on("click", async function (e) {
    //arr = []
    //let scrolbarText = $("#scrolbarText").val()
    //let scrolbarTextVH = $("#scrolbarTextVH").val()

    //if (scrolbarText != "" && scrolbarText != undefined) {
    //    $(".tablePropertiesSet").attr("style", 'height:calc(' + scrolbarTextVH + 'vh - ' + scrolbarText + 'px);overflow:auto')
    //}

    let name = $("#WidgetName").val();
    let WidgetType = $("#WidgetType option:selected").val()
    let ChartType = $("#ChartType option:selected").val()
    let WidgetDescription = $("#WidgetDescription").val();
    let SPDataset = $("#SPDataset option:selected").val()
    let SPDatasetName = $("#SPDataset option:selected").text();

    if (name == "") {
        return false;
    }
    let widgetHtml = $('#widgetCreationCard').children()[0];
    let data = {}
    var href = ''

        var container = document.getElementById("widgetCreationCard"); /* full page */
        await html2canvas(container, { allowTaint: true }).then(function (canvas) {

            href = canvas.toDataURL();

        });

    if (e.target.textContent == 'Update') {
        data.id = e.target.getAttribute('widgetid')
    }
    else {
        const uuid = generateUUID();
        // data.__RequestVerificationToken = gettoken()
       
        data.ReferenceId = uuid
    }
    const date = new Date()
    const comondate = formatDate(date) 
    data.Name = name
    data.IsActive = true
    data.CreatedBy = 3
    data.CreatedDate = comondate
    data.LastModifiedBy = 3
    data.LastModifiedDate = comondate
    data.Properties = JSON.stringify({
        WidgetType: WidgetType,
        ChartType: ChartType,
        WidgetDescription: WidgetDescription,
        datasetId: SPDataset,
        datasetName: SPDatasetName,
        xView: $("#xaxisvalue option:selected").text(),
        yview: $("#yaxisvalue option:selected").text(),
        hrefImage: href
    })

    $.ajax({
        type: "POST",
        url: RootUrl + 'BCMAdministration/WidgetList/SaveWidgetBuilder',
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {

                $('#alertClass').removeClass("info-toast")
                $('#alertClass').addClass("success-toast")
                $('#notificationAlertmessage').text(result.message)
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                $(".iconClass").removeClass("cp-exclamation")
                $(".iconClass").addClass("cp-check")
                WidgetBuilderList()
            }

        }
    })

})


$(".btn_preview").on("click", async function (e) {

    $("#previewZoom").modal("show")

})




function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}


function formatDate(date) {
    const pad = (n, digits = 2) => n.toString().padStart(digits, '0');

    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1); // months are 0-indexed
    const day = pad(date.getDate());
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());
    const milliseconds = pad(date.getMilliseconds(), 3);

    // add fake micro/nanoseconds if needed
    const extraDigits = "5042"; // fake last 4 digits for .2585042

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}${extraDigits}`;
}


function WidgetBuilderList() {



    $.ajax({
        url: '/BCMAdministration/WidgetList/WidgetBuilderList',
        type: 'GET',
        credentials: "include",
        contentType: "application/json",
        dataType: "json",
        success: async function (response) {
            debugger
            $("#prebuildList").empty()
            $("#customList").empty()
            if (response.success) {
                if (response.data.length != 0) {
                    response.data.forEach((data) => {

                        let widgetHtml = JSON.parse(data.properties)
                        let html = "";
                        html += '<div class="col-3 p-1 widgetList">'
                        html += '<div class="card border mb-0 h-100">'
                        html += '<div class="dropdown d-flex justify-content-md-end" >'
                        html += '<i class="cv-horizontal-dots p-1 show" role="button" data-bs-toggle="dropdown" title="More" aria-expanded="true"></i>'
                        html += '<ul class="dropdown-menu" style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(184px, 26px);" data-popper-placement="bottom-start">'
                        html += '<li class="editActionList" id="' + data.id + '" type=' + widgetHtml.type + ' name=' + data.name + '   onclick = "editwidgetView(this,event)" > <a class="dropdown-item" href="#"><i class="cv-edit text-info me-2" title="Edit"></i>Edit</a></li >'
                        html += '<li class="deleteActionList " id="' + data.id + '"   name=' + data.name + ' onclick="deletewidgetListView(this)" data-bs-toggle="modal" data-bs-target="#DeleteModal"><a class="dropdown-item" href="#"><i class="cv-delete text-danger me-2" title="Delete"></i>Delete</a></li>'
                        html += '</ul >'
                        html += '</div >'
                        html += '<div class="card-body py-3">'
                        html += '<img src = "' + widgetHtml.hrefImage + '" class="w-100" style="height:170px; object-fit:scale-down; object-position: top; " />'
                        html += '</div>'
                        html += '<div class="card-footer widgetName list-title text-center" title="' + data.name + '">' + data.name + '</div>'
                        html += '</div>'
                        html += '</div>'
                        $("#prebuildList").append(html)
                        $("#customList").append(html)
                    })
                }
            }


        }

    })

}




