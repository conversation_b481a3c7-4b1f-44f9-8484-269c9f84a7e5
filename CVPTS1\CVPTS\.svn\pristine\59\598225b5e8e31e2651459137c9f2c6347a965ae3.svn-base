var myColor;
// var containerEl = document.getElementById('external-events-list');
// new FullCalendar.Draggable(containerEl, {
//     itemSelector: '.fc-event',
//     eventData: function (eventEl) {
//         return {
//             title: eventEl.innerText.trim()
//         }
//     }
// });

document.addEventListener("DOMContentLoaded", function () {
  var calendarEl = document.getElementById("calendar");

  var calendar = new FullCalendar.Calendar(calendarEl, {
    initialView: "dayGridMonth",
    initialDate: new Date(),
    themeSystem: "bootstrap",
    headerToolbar: {
      left: "prev,next today",
      center: "title",
      right: "dayGridMonth,timeGridWeek,timeGridDay",
    },
    eventRender: function (event, element) {
      /*alert(element, "element: " + event);*/
      // element.attr('title', event.eventTitle);
      // element.find('.fc-title').html(event.eventTitle);
      // var isAcceptByApplicant = event.data.isAcceptByApplicant;
      // if (isAcceptByApplicant == null) {
      //     $(element).addClass("new_interview");
      //     $(element).find(".fc-content").attr("style", "border-left:3px solid #004085 !important");
      // }
      // else if (isAcceptByApplicant == false) {
      //     $(element).addClass("cancel_interview");
      //     $(element).find(".fc-content").attr("style", "border-left:3px solid #721c24 !important");
      // }
      // else {
      //     $(element).addClass("accept_interview");
      //     $(element).find(".fc-content").attr("style", "border-left:3px solid #219653 !important");
      // }
    },
    editable: true,
    droppable: true, // this allows things to be dropped onto the calendar
    drop: function (arg) {
      // is the "remove after drop" checkbox checked?
      if (document.getElementById("drop-remove").checked) {
        // if so, remove the element from the "Draggable Events" list
        arg.draggedEl.parentNode.removeChild(arg.draggedEl);
      }
    },
    navLinks: true, // can click day/week names to navigate views
    selectable: true,
    selectMirror: true,
    select: function (arg) {
      var title = prompt("Event Title:");
      if (title) {
        calendar.addEvent({
          title: title,
          start: arg.start,
          end: arg.end,
          allDay: arg.allDay,
        });
      }
      calendar.unselect();
    },

    eventClick: function (info) {
      //if (confirm('Are you sure you want to delete this event?')) {

      //alert(info.event.id);

  /*    alert("Event: " + info.event.extendedProps.description);*/

      //window.location.href = "/BCMCalendar/ManageBCMCalender/ManageBCMCalender/" + info.event.id;

      ///arg.event.remove()
      //}
    },
    editable: true,
    dayMaxEvents: true, // allow "more" link when too many events
    events: function (fetchInfo, successCallback, failureCallback) {
      $.ajax({
        // url: 'http://**************/CVCore/Dashboard/GetCalendarData',
        //url: 'http://localhost:5164//Dashboard/GetCalendarData',
        url: '@Url.Action("GetCalendarData", "Dashboard")',
        method: "GET",
        dataType: "json",

        success: function (response) {
          var events = [];

          $.each(response, function (i, response) {
            events.push({
              id: response.eid,
              title: response.title,
              start: response.start,
              end: response.end,
              color: "#D6CCE6",
              textColor: "#333",
              image_url:
                "https://img.freepik.com/premium-vector/young-smiling-man-avatar-man-with-brown-beard-mustache-hair-wearing-yellow-sweater-sweatshirt-3d-vector-people-character-illustration-cartoon-minimal-style_365941-860.jpg?w=2000",

              //title: 'DRJ Event',
              //start: '2025-01-03',
              //end: '2025-01-05',
              //color: "#D6CCE6",
              //textColor: "#333",
              //image_url: 'https://img.freepik.com/premium-vector/young-smiling-man-avatar-man-with-brown-beard-mustache-hair-wearing-yellow-sweater-sweatshirt-3d-vector-people-character-illustration-cartoon-minimal-style_365941-860.jpg?w=2000',
            });
          });
          // console.log(events);
          successCallback(events);
        },

        //events.push(response);
        //for (var i in response) {
        //    console.log(i[0].start);
        //    events.push({
        //        title: i.title,
        //        start: i.start,
        //        end: i.end,
        //        color: "#D6CCE6",
        //        textColor: "#333",
        //        image_url: 'https://img.freepik.com/premium-vector/young-smiling-man-avatar-man-with-brown-beard-mustache-hair-wearing-yellow-sweater-sweatshirt-3d-vector-people-character-illustration-cartoon-minimal-style_365941-860.jpg?w=2000',

        //    });
        //}

        // Transform aggregated data into FullCalendar event format
        //var events = [];
        //for (var date in aggregatedEvents) {
        //    for (var status in aggregatedEvents[date]) {
        //        for (var end in aggregatedEvents[end]) {
        //            events.push({
        //                title: title,
        //                start: date,
        //                end: end,
        //                extendedProps: {
        //                    status: status,
        //                    statusCount: aggregatedEvents[date][status]
        //                }
        //            });
        //        }
        //    }
        //}

        //} else {
        //    console.log('Error: Data status is not successful');
        //    failureCallback();
        //}
        //},
        //error: function (jqXHR, textStatus, errorThrown) {
        //    console.log('Error: ' + textStatus);
        //    failureCallback();
        //}
      });
    },

    //events: [
    //    {
    //        title: 'DRJ Event',
    //        start: '2024-09-08',
    //        color: "#D1F5E0",
    //        textColor: "#333",
    //        image_url: 'https://img.freepik.com/premium-vector/young-smiling-man-avatar-man-with-brown-beard-mustache-hair-wearing-yellow-sweater-sweatshirt-3d-vector-people-character-illustration-cartoon-minimal-style_365941-860.jpg?w=2000',
    //    },
    //    {
    //        title: 'Review Meeting',
    //        start: '2024-09-06',
    //        end: '2024-09-03',
    //        color: "#D6CCE6",
    //        textColor:"#333",
    //        image_url: 'https://img.freepik.com/premium-vector/young-smiling-man-avatar-man-with-brown-beard-mustache-hair-wearing-yellow-sweater-sweatshirt-3d-vector-people-character-illustration-cartoon-minimal-style_365941-860.jpg?w=2000',

    //    },

    //],
    color: $(this).data("color"),
    eventContent: function (arg) {
      let arrayOfDomNodes = [];
      // title event
      let titleEvent = document.createElement("div");
      if (arg.event._def.title) {
        titleEvent.innerHTML = arg.event._def.title;
        titleEvent.classList = "fc-event-title fc-sticky";
      }

      // image event
      let imgEventWrap = document.createElement("div");
      if (arg.event.extendedProps.image_url) {
        let imgEvent =
          '<img src="' +
          arg.event.extendedProps.image_url +
          '" class="img-fluid rounded-circle" style="width:25px">';
        imgEventWrap.classList = "fc-event-img";
        imgEventWrap.innerHTML = imgEvent;
      }
      arrayOfDomNodes = [titleEvent, imgEventWrap];

      return { domNodes: arrayOfDomNodes };
    },
  });

  calendar.render();
});
