﻿@model IEnumerable<BCM.BusinessClasses.ResourceTrainingInfo>
@{
    ViewBag.Title = "ResourceTraining";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int iIndex = 1;
}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Resource Training</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">


        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="Modal"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>



<div class="Page-Condant card border-0" id="tblContent">
    <table id="example" class="table table-hover" style="width:100%">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Resource Name</th>
                <th>Role</th>
                <th>Primary Skills</th>
                <th>Training</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="tblBody">
            @if (Model != null)
            {
                @foreach (var objResourceTrainingInfo in Model)
                {
                    <tr>
                        <td>@iIndex</td>
                        <td><a class="text-primary" href="#">@objResourceTrainingInfo.ResourceName</a></td>
                        <td>
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 1)
                            {
                                <p>*</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 2)
                            {
                                <p>**</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 3)
                            {
                                <p>BCMS Manager</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 4)
                            {
                                <p>BCM Lead</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 5)
                            {
                                <p>BCM Champions</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 6)
                            {
                                <p>ITDR Champions</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 7)
                            {
                                <p>Risk Champions</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 8)
                            {
                                <p>CxO</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 38)
                            {
                                <p>BCM Lead Cop</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 40)
                            {
                                <p>BCM Lead Cops</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 43)
                            {
                                <p>BCM Lead Copx</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 44)
                            {
                                <p>BCMS Manager1</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 45)
                            {
                                <p>BCMS_Manager</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 54)
                            {
                                <p>BCM Manager</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 55)
                            {
                                <p>Test Role</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 56)
                            {
                                <p>BCM Manager Test One</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 57)
                            {
                                <p>BCM Manager Test Two</p>
                            }
                            @if (Convert.ToInt32(objResourceTrainingInfo.Role) == 58)
                            {
                                <p>TEST</p>
                            }
                        </td>
                        <td>@objResourceTrainingInfo.PrimarySkills</td>
                        <td>@objResourceTrainingInfo.Training</td>
                        <td>
                            <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@objResourceTrainingInfo.ResourceTrainingID"><i class="cv-edit" title="Edit"></i></span>
                            @* &nbsp; *@
                            <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@objResourceTrainingInfo.ResourceTrainingID"><i class="cv-delete text-danger" title="Delete"></i></span>
                        </td>
                    </tr>
                    iIndex++;
                }
            }
        </tbody>
    </table>
</div>


<div class="modal fade" id="Modal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle"></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div>


@section Scripts {
    <script>
        $(document).ready(function () {

            $('#btnCreate').click(function () {
                //$.get('/BCMResourceManagement/ResourceTraining/AddResourceTraining', function (data) {
                $.get('@Url.Action("AddResourceTraining", "ResourceTraining")', function (data) {
                    $('.modal-body').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Resource Training Configuration');
                });
            });

            $(document).on('click', '.btnEdit', function () {
                var iId = $(this).data('id');
                //$.get('/BCMResourceManagement/ResourceTraining/EditResourceTraining/', { iId: iId }, function (data) {
                $.get('@Url.Action("EditResourceTraining", "ResourceTraining")', { iId: iId }, function (data) {
                    $('.modal-body').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Update Resource Training');
                });
            })

            $(document).on('click', '.btnDelete', function () {
                var iId = $(this).data('id');
               // $.get('/BCMResourceManagement/ResourceTraining/DeleteResourceTraining/', { iId: iId }, function (data) {
                $.get('@Url.Action("DeleteResourceTraining", "ResourceTraining")', { iId: iId }, function (data) {
                    $('#deleteBody').html(data);
                    $('#DeleteModal').modal('show');
                    $('#modaltitle').text('Delete Resource Training');
                });
            })

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        location.reload();
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    </script>
}