﻿@model IEnumerable<BCM.BusinessClasses.EscalationMatrix>

@{
    ViewBag.Title = "ManageEscalationMatrix";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int index = 1;
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<link href="~/css/timeline.css" rel="stylesheet" />
<style>
    .nav-link.active .card {
        background: linear-gradient(242deg, rgba(230, 56, 117, 1) 0%, rgba(50, 2, 132, 1) 100%) !important;
        color: #fff;
    }

    .escalation-timeline {
        position: relative;
        padding: 20px 0;
    }

    .timeline-item {
        position: relative;
        padding: 20px 0 20px 60px;
        border-left: 2px solid #e9ecef;
    }

        .timeline-item:last-child {
            border-left: none;
        }

    .timeline-marker {
        position: absolute;
        left: -8px;
        top: 25px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #e9ecef;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .start-escalation .timeline-marker {
        background-color: #28a745;
        box-shadow: 0 0 0 2px #28a745;
    }

    .escalation-level .timeline-marker {
        background-color: #007bff;
        box-shadow: 0 0 0 2px #007bff;
    }

    .end-escalation .timeline-marker {
        background-color: #dc3545;
        box-shadow: 0 0 0 2px #dc3545;
    }

    .nav-link.disabled {
        pointer-events: none;
        opacity: 0.6;
    }

    .wizard-step-completed {
        background-color: #28a745 !important;
        color: white !important;
    }

    .wizard-step-active {
        background-color: #007bff !important;
        color: white !important;
    }
</style>

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Escalation Matrix</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="Modal"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Escalation&nbsp;Matrix&nbsp;Code</th>
                <th>Name&nbsp;Details</th>
                <th>Used</th>
                <th style="display:none">Create&nbsp;Update&nbsp;Details</th>
                <th>Approval&nbsp;Status</th>
                <th>Approvar</th>
                <th style="display:none">Approval&nbsp;Status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            @if (Model != null)
            {
                @foreach (var EsclationInfo in Model)
                {
                    EsclationInfo.EscMatApproverName = string.IsNullOrEmpty(EsclationInfo.EscMatApproverName) ? "NA" : EsclationInfo.EscMatApproverName;
                    <tr>
                        <td>@index</td>
                        @* <td><a class="text-primary btnShowConfiguration" href="javascript:void(0)" data-id="@EsclationInfo.EscMatID" data-code="@EsclationInfo.EscMatCode">@EsclationInfo.EscMatCode</a></td> *@
                        <td>@EsclationInfo.EscMatCode</td>
                        <td>
                            <table>
                                <tbody>
                                    <tr title="Escalation Matrix Name">
                                        @* <td><i class="cv-name "></i></td>
                                        <td> : </td> *@
                                        <td>@EsclationInfo.EscMatName</td>
                                    </tr>
                                    <tr title="Escalation Matrix Description" style="display:none">
                                        <td><i class="cv-description "></i></td>
                                        <td>:</td>
                                        <td>@EsclationInfo.EscMatDesc</td>
                                    </tr>
                                    <tr title=" Escalation Matrix Type" style="display:none">
                                        <td><i class="cv-type "></i></td>
                                        <td>:</td>
                                        <td>@EsclationInfo.EscMatType</td>
                                    </tr>
                                </tbody>
                            </table>

                        </td>
                        <td>
                            Escalation Matrix has been attached @EsclationInfo.MatrixCount times
                            Total Escalations triggered 0 times
                        </td>
                        <td style="display:none">
                            <table>
                                <tbody>
                                    <tr title="Created By">
                                        <td><i class="cv-user "></i></td>
                                        <td> : </td>
                                        <td>@EsclationInfo.CreatedByName</td>
                                    </tr>
                                    <tr title="Created Date">
                                        <td><i class="cv-calendar "></i></td>
                                        <td>:</td>
                                        <td>@EsclationInfo.CreatedDate</td>
                                    </tr>
                                    <tr title="Updated By">
                                        <td><i class="cv-user "></i></td>
                                        <td>:</td>
                                        <td>@EsclationInfo.UpdatedByName</td>
                                    </tr>
                                    <tr title="Updated Date">
                                        <td><i class="cv-calendar "></i></td>
                                        <td>:</td>
                                        <td>@EsclationInfo.UpdatedDate</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>
                            @*     @if (@EsclationInfo.EscMatStatus == "Initiated")
                            {
                                <span class="text-info">@EsclationInfo.EscMatStatus</span>
                            }
                            @if (@EsclationInfo.EscMatStatus == "WaitingforApproval")
                            {
                                <span class="text-warning">@EsclationInfo.EscMatStatus</span>
                            }
                            @if (@EsclationInfo.EscMatStatus == "Approved")
                            {
                                <span class="text-success">@EsclationInfo.EscMatStatus</span>
                            }
                            @if (@EsclationInfo.EscMatStatus == "Disapproved")
                            {
                                <span class="text-danger">@EsclationInfo.EscMatStatus</span>
                            } *@
                            @{
                                int statusId = Convert.ToInt32(EsclationInfo.EscMatStatus);
                            }
                            <span class="d-flex align-items-center @BCM.Shared.Utilities.ApprovalStatusWiseTextClass(statusId)">
                                <i class="@BCM.Shared.Utilities.ApprovalStatusWiseClass(statusId) me-2"></i>
                                @BCM.Shared.Utilities.ApprovalStatus(statusId)
                            </span>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    <tr title="Approver">
                                        @* <td><i class="cv-approver "></i></td>
                                        <td>:</td> *@
                                        <td>@EsclationInfo.EscMatApproverName</td>
                                    </tr>
                                    <tr title="Approval Date" style="display:none">
                                        <td><i class="cv-calendar "></i></td>
                                        <td>:</td>
                                        <td>@EsclationInfo.EscMatApprovedDate</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td style="display:none">
                            <table>
                                <tbody>
                                    <tr title="Approval Status">
                                        <td><i class="cv-status "></i></td>
                                        <td> : </td>
                                        <td>
                                            @if (@EsclationInfo.EscMatStatus == "Initiated")
                                            {
                                                <span class="text-info">@EsclationInfo.EscMatStatus</span>
                                            }
                                            @if (@EsclationInfo.EscMatStatus == "WaitingforApproval")
                                            {
                                                <span class="text-warning">@EsclationInfo.EscMatStatus</span>
                                            }
                                            @if (@EsclationInfo.EscMatStatus == "Approved")
                                            {
                                                <span class="text-success">@EsclationInfo.EscMatStatus</span>
                                            }
                                            @if (@EsclationInfo.EscMatStatus == "Disapproved")
                                            {
                                                <span class="text-danger">@EsclationInfo.EscMatStatus</span>
                                            }
                                        </td>
                                    </tr>
                                    <tr title="Approver">
                                        @* <td><i class="cv-approver "></i></td>
                                        <td>:</td> *@
                                        <td>@EsclationInfo.EscMatApproverName</td>
                                    </tr>
                                    <tr title="Approval Date">
                                        <td><i class="cv-calendar "></i></td>
                                        <td>:</td>
                                        <td>@EsclationInfo.EscMatApprovedDate</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@EsclationInfo.EscMatID"><i class="cv-edit" title="Edit"></i></span>
                            @* &nbsp; *@
                            <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@EsclationInfo.EscMatID"><i class="cv-delete text-danger" title="Delete"></i></span>
                            <span class="btn-action btnEscalationLevel" type="button" data-bs-toggle="modal" data-bs-target="#LevelModal" data-id="@EsclationInfo.EscMatID" data-code="@EsclationInfo.EscMatCode" title="Escalation Level"><i class="cv-my-escalations"></i></span>
                            <span class="btn-action btnShowConfiguration" type="button" data-id="@EsclationInfo.EscMatID" data-code="@EsclationInfo.EscMatCode" title="Escalation Level Chart"><i class="cv-escalation-matrix"></i></span>
                        </td>
                    </tr>
                    index++;
                }
            }
        </tbody>
    </table>
</div>

<div class="modal fade" id="Modal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle"></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-data">
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div>
<!-- Delete Modal -->
<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Escalation Matrix ESC-2024-18</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0">
                <div class="row">
                    <div class="col-12 dots-bg">
                        <div style="height:calc(100vh - 160px);overflow-y:auto">
                            <div class="mx-auto" style="width: 30rem;">
                                <button type="button" class="btn btn-primary btn-sm">End Escalation</button>
                                <div class="Escalation_Timeline" style=" margin-left: 8%;  margin-top: 0%">
                                    <ul class="ul">
                                        <li class="li">
                                            <div class="Escalation_Timeline_Card card border-danger">
                                                <div class="d-flex align-items-center">
                                                    <span class="Timeline_Card_Level px-0"> </span>
                                                    <div class="d-grid ms-2">
                                                        <h6 class="mb-1 text-truncate" title="Management">
                                                            Escalation Level : 1
                                                        </h6>

                                                    </div>

                                                </div>
                                                <div>
                                                    <div class="card card-body">
                                                        <div class="card-footer p-0">
                                                            <table class="table-sm table table-bordered">
                                                                <tr>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">Time To Escalate</p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">03-05-2024 12:25:00</p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">Will EscalateAfter</p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">
                                                                            <span class="ms-1 text-danger">
                                                                                15 Minute(s)
                                                                            </span>
                                                                        </p>
                                                                    </td>
                                                                </tr>

                                                            </table>
                                                        </div>
                                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                                            <div>
                                                                <p class="mb-0"><i class="cv-user me-1 align-middle"></i><span class="text-primary me-1">David Gunawan</span>(Step Owner)</p>
                                                                <p class="mb-0"><i class="cv-mail me-1 align-middle"></i><span class="me-1"><EMAIL></span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                <p class="mb-0"><i class="cv-Mobile me-1 align-middle"></i><span class="me-1">9087534131</span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                            </div>
                                                            @*  <p class="mb-0 text-success fw-semibold">Acknowledged</p> *@
                                                        </div>

                                                    </div>

                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                    <button type="button" class="btn btn-primary btn-sm" style="margin-top: -3%; margin-left: -13%;">
                                        Start Escalation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!-- Level Modal -->
<div class="modal fade" id="LevelModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Define   Escalation Levels -  <span class="text-primary" id="escalationMatrixCode">ESC-2022-16</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0" style="min-height:350px">

                <div class="License_Nav nav nav-tabs nav-justified" role="tablist" id="myTab">
                    <div class="nav-item">
                        <a class="nav-link active" role="presentation" id="nav-server-tab" data-bs-toggle="tab"
                           data-bs-target="#nav-server" role="tab" aria-controls="nav-server"
                           aria-selected="true">
                            <div class="mb-0 text-center card" role="button">
                                <div class="card-body py-2">
                                    <p class="mb-0 fw-semibold">Level Details - Step 1</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a class="nav-link" id="nav-database-tab" " data-bs-toggle="tab"
                           data-bs-target="#nav-database" role="tab" aria-controls="nav-database"
                           aria-selected="false">
                            <div class="mb-0 text-center card" role="button">
                                <div class="card-body py-2">
                                    <p class="mb-0 fw-semibold">Members - Step 2</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a class="nav-link" id="nav-application-tab" data-bs-toggle="tab"
                           data-bs-target="#nav-application" role="tab"
                           aria-controls="nav-application" aria-selected="false">
                            <div class="mb-0 text-center card" role="button">
                                <div class="card-body py-2">
                                    <p class="mb-0 fw-semibold">Teams - Step 3</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="License_Tab tab-content" id="myTabContent">
                    <div class="tab-pane fade active show" id="nav-server" role="tabpanel" aria-labelledby="nav-server-tab">
                        <div class="row mt-2">
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">Level Name <small class="text-muted"></small></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-org-revel-escalations"></i></span>
                                        <input type="text" id="txtLevelName" placeholder="Level Name" class="form-control" readonly />
                                    </div>
                                    <div class="invalid-feedback">Enter Level Name</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center gap-2">
                                    <div class="form-group w-50">
                                        <label class="form-label">Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-clock"></i></span>
                                            <input type="number" id="txtEscTime" placeholder="Enter Time" class="form-control" min="1" />
                                        </div>
                                        <div class="invalid-feedback">Enter Time</div>
                                    </div>
                                    <div class="form-group w-50">
                                        <label class="form-label">Time Unit</label>
                                        <select id="ddlTimeUnit" class="form-control">
                                            <option value="1">Minute(s)</option>
                                            <option value="2">Hour(s)</option>
                                            <option value="3">Day(s)</option>
                                            <option value="4">Week(s)</option>
                                            <option value="5">Month(s)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">Level Description</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-description"></i></span>
                                        <textarea id="txtLevelDescription" placeholder="Enter Level Description" class="form-control" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <button type="button" id="btnNextStep1" class="btn btn-primary btn-sm">Next</button>
                                <input type="hidden" id="hdnLevConID" value="0" />
                                <input type="hidden" id="hdnEscMatID" value="0" />
                            </div>
                            <div class="col-12">
                                <div style="height:300px;overflow:auto">
                                    <table class="table table-info" id="escalationLevelsTable">
                                        <thead class="position-sticky top-0 z-3">
                                            <tr>
                                                <th>#</th>
                                                <th>Level Name</th>
                                                <th>Time</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="escalationLevelsTableBody">
                                            <!-- Dynamic content will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="nav-database" role="tabpanel" aria-labelledby="nav-database-tab">
                        <div class="row mt-2">
                            <div class="col-5">
                                <div class="form-group">
                                    <label class="form-label">Members in Level</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-org-revel-escalations"></i></span>
                                        <input type="text" id="txtSelectedMembers" placeholder="Currently No Members Selected in Level" class="form-control" readonly />
                                    </div>
                                </div>
                            </div>
                            <div class="col-7">
                                <div class="d-flex align-items-center gap-2">
                                    <div class="form-group flex-fill">
                                        <label class="form-label">Search Members To Add</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-search"></i></span>
                                            <input type="text" id="txtSearchMembers" placeholder="Search Members To Add" class="form-control" />
                                        </div>
                                    </div>
                                    <button type="button" id="btnSearchMembers" class="btn btn-primary btn-sm">Search Resources</button>
                                </div>

                            </div>
                            <div class="col-12 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Approval Settings</h6>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="chkCanApprove" checked>
                                                    <label class="form-check-label" for="chkCanApprove">
                                                        Can Approve
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-9" id="approvalTypeContainer">
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="approvalType" id="rdbCanApproveAny" value="1" checked>
                                                    <label class="form-check-label" for="rdbCanApproveAny">
                                                        Any
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="approvalType" id="rdbCanApproveAll" value="2">
                                                    <label class="form-check-label" for="rdbCanApproveAll">
                                                        All
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div style="height:300px;overflow:auto">
                                    <table class="table table-info" id="membersTable">
                                        <thead class="position-sticky top-0 z-3">
                                            <tr>
                                                <th>#</th>
                                                <th>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="chkSelectAllMembers">
                                                        <label class="form-check-label" for="chkSelectAllMembers">
                                                            Select All
                                                        </label>
                                                    </div>
                                                </th>
                                                <th>Resource Name</th>
                                                <th>OrgLevel</th>
                                            </tr>
                                        </thead>
                                        <tbody id="membersTableBody">
                                            <!-- Dynamic content will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <button type="button" id="btnPreviousStep2" class="btn btn-secondary btn-sm">Previous</button>
                                <button type="button" id="btnNextStep2" class="btn btn-primary btn-sm">Next</button>
                            </div>
                        </div>

                    </div>
                    <div class="tab-pane fade" id="nav-application" role="tabpanel" aria-labelledby="nav-application-tab">
                        <div class="row mt-2">
                            <div class="col-5">
                                <div class="form-group">
                                    <label class="form-label">Teams in Level</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-org-revel-escalations"></i></span>
                                        <input type="text" id="txtSelectedTeams" placeholder="Currently No Team Selected in Level" class="form-control" readonly />
                                    </div>
                                </div>
                            </div>
                            <div class="col-7">
                                <div class="d-flex align-items-center gap-2">
                                    <div class="form-group flex-fill">
                                        <label class="form-label">Search Teams To Add</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-search"></i></span>
                                            <input type="text" id="txtSearchTeams" placeholder="Search Teams To Add" class="form-control" />
                                        </div>
                                    </div>
                                    <button type="button" id="btnSearchTeams" class="btn btn-primary btn-sm">Search Teams</button>
                                </div>

                            </div>
                            <div class="col-12">
                                <div style="height:300px;overflow:auto">
                                    <table class="table table-info" id="teamsTable">
                                        <thead class="position-sticky top-0 z-3">
                                            <tr>
                                                <th>#</th>
                                                <th>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="chkSelectAllTeams">
                                                        <label class="form-check-label" for="chkSelectAllTeams">
                                                            Select All
                                                        </label>
                                                    </div>
                                                </th>
                                                <th>Group Name</th>
                                                <th>Org Level</th>
                                            </tr>
                                        </thead>
                                        <tbody id="teamsTableBody">
                                            <!-- Dynamic content will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <button type="button" id="btnPreviousStep3" class="btn btn-secondary btn-sm">Previous</button>
                                <button type="button" id="btnSaveEscalationLevel" class="btn btn-primary btn-sm">Save Escalation Level</button>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
            <div class="modal-footer d-flex justify-content-end align-items-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!--End Level Modal -->
<!-- Escalation Configuration Modal -->
<div class="modal fade" id="ConfigurationModal" tabindex="-1" aria-labelledby="ConfigurationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Escalation Matrix - <span class="text-primary" id="configurationMatrixCode">ESC-2022-16</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0" style="min-height:400px">
                <div class="row">
                    <div class="col-12">
                        <div class="timeline-container">
                            <div class="timeline">
                                <div id="escalationTimeline" class="escalation-timeline">
                                    <!-- Dynamic timeline content will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-end align-items-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->





<div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Escalation Matrix ESC-2024-18</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0">
                <div class="row">
                    <div class="col-12 dots-bg">
                        <div style="height:calc(100vh - 160px);overflow-y:auto">
                            <div class="mx-auto" style="width: 30rem;">
                                <button type="button" class="btn btn-primary btn-sm">End Escalation</button>
                                <div class="Escalation_Timeline" style=" margin-left: 8%;  margin-top: 0%">
                                    <ul class="ul">
                                        <li class="li">
                                            <div class="Escalation_Timeline_Card card border-danger">
                                                <div class="d-flex align-items-center">
                                                    <span class="Timeline_Card_Level px-0"> </span>
                                                    <div class="d-grid ms-2">
                                                        <h6 class="mb-1 text-truncate" title="Management">
                                                            Escalation Level : 1
                                                        </h6>

                                                    </div>

                                                </div>
                                                <div>
                                                    <div class="card card-body">
                                                        <div class="card-footer p-0">
                                                            <table class="table-sm table table-bordered">
                                                                <tr>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">Time To Escalate</p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">03-05-2024 12:25:00</p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">Will EscalateAfter</p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">
                                                                            <span class="ms-1 text-danger">
                                                                                15 Minute(s)
                                                                            </span>
                                                                        </p>
                                                                    </td>
                                                                </tr>

                                                            </table>
                                                        </div>
                                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                                            <div>
                                                                <p class="mb-0"><i class="cv-user me-1 align-middle"></i><span class="text-primary me-1">David Gunawan</span>(Step Owner)</p>
                                                                <p class="mb-0"><i class="cv-mail me-1 align-middle"></i><span class="me-1"><EMAIL></span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                <p class="mb-0"><i class="cv-Mobile me-1 align-middle"></i><span class="me-1">9087534131</span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                            </div>
                        @*  <p class="mb-0 text-success fw-semibold">Acknowledged</p> *@
                                                        </div>

                                                    </div>

                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                    <button type="button" class="btn btn-primary btn-sm" style="margin-top: -3%; margin-left: -13%;">
                                        Start Escalation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
</div>






                        @section Scripts {
    <script>
        $(document).ready(function () {
            $(document).on("click", ".Closebtn", function(){
              location.reload();
            });

            $('#btnCreate').click(function () {
                $.get('@Url.Action("AddEscalationMatrix", "EscalationMatrix")', function (data) {
                    $('.modal-data').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Escalation Matrix Configuration');
                });
            });

            $(document).on('click', '.btnEdit', function () {
                var iId = $(this).data('id');
                $.get('@Url.Action("EditEscalationMatrix", "EscalationMatrix")', { iId: iId }, function (data) {
                    $('.modal-data').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Update Escalation Matrix');
                });
            })

            $(document).on('click', '.btnDelete', function () {
                var iId = $(this).data('id');
                $.get('@Url.Action("DeleteEscalationMatrix", "EscalationMatrix")', { iId: iId }, function (data) {
                    $('#deleteBody').html(data);
                    $('#DeleteModal').modal('show');
                    $('#modaltitle').text('Delete Escalation Matrix');
                });
            })

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        location.reload();
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

            // Escalation Level Management
            $(document).on('click', '.btnEscalationLevel', function () {
                var escMatId = $(this).data('id');
                var escMatCode = $(this).data('code');

                $('#hdnEscMatID').val(escMatId);
                $('#escalationMatrixCode').text(escMatCode);

                // Reset wizard to step 1
                showStep(1);
                loadEscalationLevels(escMatId);
                clearLevelForm();
                clearWizardData();

                // Set auto-incrementing level name
                setNextLevelName(escMatId);

                // Set default Can Approve settings for new level
                setDefaultApprovalSettings();

                // Preload resources and teams data for better user experience
                console.log("Preloading resources and teams data...");
                loadAllResources();
                loadAllTeams();

                // Show the modal
                $('#LevelModal').modal('show');
            });

            function loadEscalationLevels(escMatId) {
                $.get('@Url.Action("GetEscalationLevels", "EscalationMatrix")', { escMatId: escMatId }, function (data) {
                    var tbody = $('#escalationLevelsTableBody');
                    tbody.empty();

                    if (data && data.length > 0) {
                        $.each(data, function (index, level) {
                            var row = '<tr>' +
                                '<td>' + (index + 1) + '</td>' +
                                '<td>' + (level.levName || '') + '</td>' +
                                '<td>' + (level.escTime || '') + '</td>' +
                                '<td>' +
                                '<span class="btn-action btnEditLevel me-2" data-id="' + level.levConID + '" title="Edit"><i class="cv-edit"></i></span>' +
                                '<span class="btn-action btnDeleteLevel text-danger" data-id="' + level.levConID + '" title="Delete"><i class="cv-delete"></i></span>' +
                                '</td>' +
                                '</tr>';
                            tbody.append(row);
                        });
                    } else {
                        tbody.append('<tr><td colspan="4" class="text-center">No escalation levels found</td></tr>');
                    }
                }).fail(function () {
                    alert('Error loading escalation levels');
                });
            }

            // Set auto-incrementing level name (following EscalationMatrixNewForm.aspx.cs pattern)
            function setNextLevelName(escMatId) {
                $.get('@Url.Action("GetNextLevelSequence", "EscalationMatrix")', { escMatId: escMatId }, function (data) {
                    if (data && data.nextSequence) {
                        $('#txtLevelName').val(data.nextSequence.toString());
                        console.log("Next level sequence set to:", data.nextSequence);
                    }
                }).fail(function () {
                    // Default to 1 if there's an error
                    $('#txtLevelName').val('1');
                    console.log("Error getting next level sequence, defaulting to 1");
                });
            }

            // Set default Can Approve settings for new level (following EscalationMatrixNewForm.aspx.cs pattern)
            function setDefaultApprovalSettings() {
                $('#chkCanApprove').prop('checked', true);
                $('#rdbCanApproveAny').prop('checked', true);
                $('#rdbCanApproveAll').prop('checked', false);
                $('#approvalTypeContainer').show();
            }

            // Can Approve checkbox change handler (following EscalationMatrixNewForm.aspx.cs pattern)
            $(document).on('change', '#chkCanApprove', function () {
                var isChecked = $(this).is(':checked');
                console.log("Can Approve changed:", isChecked);

                if (isChecked) {
                    $('#approvalTypeContainer').show();
                    // Default to "Any" when enabling Can Approve
                    $('#rdbCanApproveAny').prop('checked', true);
                    $('#rdbCanApproveAll').prop('checked', false);
                } else {
                    $('#approvalTypeContainer').hide();
                    $('#rdbCanApproveAny').prop('checked', false);
                    $('#rdbCanApproveAll').prop('checked', false);
                }
            });

            function clearLevelForm() {
                // Don't clear txtLevelName as it's auto-generated and read-only
                $('#txtLevelDescription').val('');
                $('#txtEscTime').val('');
                $('#ddlTimeUnit').val('1');
                $('#hdnLevConID').val('0');
            }

            function clearWizardData() {
                // Clear selected members and teams
                $('#txtSelectedMembers').val('');
                $('#txtSelectedTeams').val('');
                $('.resource-checkbox').prop('checked', false);
                $('.team-checkbox').prop('checked', false);
                $('#chkSelectAllMembers').prop('checked', false);
                $('#chkSelectAllTeams').prop('checked', false);

                // Clear checkbox states from localStorage
                clearCheckboxStates();

                // Reset Can Approve settings to defaults (following EscalationMatrixNewForm.aspx.cs pattern)
                $('#chkCanApprove').prop('checked', true);
                $('#rdbCanApproveAny').prop('checked', true);
                $('#rdbCanApproveAll').prop('checked', false);
                $('#approvalTypeContainer').show();
            }

            // Wizard Navigation Functions
            function showStep(stepNumber) {
                console.log("showStep called with stepNumber:", stepNumber);

                // Hide all tabs
                $('.nav-link').removeClass('active');
                $('.tab-pane').removeClass('active show');

                // Show selected tab
                if (stepNumber === 1) {
                    $('#nav-server-tab').addClass('active');
                    $('#nav-server').addClass('active show');
                } else if (stepNumber === 2) {
                    $('#nav-database-tab').addClass('active');
                    $('#nav-database').addClass('active show');
                    console.log("Loading resources for step 2...");
                    loadAllResources(); // Load resources when step 2 is shown
                } else if (stepNumber === 3) {
                    $('#nav-application-tab').addClass('active');
                    $('#nav-application').addClass('active show');
                    console.log("Loading teams for step 3...");
                    loadAllTeams(); // Load teams when step 3 is shown
                }
            }

            // Step Navigation Event Handlers
            $('#btnNextStep1').click(function () {
                if (validateStep1()) {
                    showStep(2);
                }
            });

            $('#btnPreviousStep2').click(function () {
                showStep(1);
            });

            $('#btnNextStep2').click(function () {
                showStep(3);
            });

            $('#btnPreviousStep3').click(function () {
                showStep(2);
            });

            function validateStep1() {
                var levelName = $('#txtLevelName').val().trim();
                var escTime = $('#txtEscTime').val().trim();

                // Level name is auto-generated, so no need to validate it
                // Just ensure it exists (should always be set by setNextLevelName)
                if (!levelName) {
                    alert('Level name is not set. Please try again.');
                    return false;
                }
                if (!escTime || escTime <= 0) {
                    alert('Please enter valid time');
                    $('#txtEscTime').focus();
                    return false;
                }
                return true;
            }

            // Save Escalation Level (Final Step)
            $('#btnSaveEscalationLevel').click(function () {
                if (!validateStep1()) {
                    showStep(1);
                    return;
                }

                var levelName = $('#txtLevelName').val().trim();
                var levelDescription = $('#txtLevelDescription').val().trim();
                var escTime = $('#txtEscTime').val().trim();
                var timeUnit = $('#ddlTimeUnit').val();
                var levConId = $('#hdnLevConID').val();
                var escMatId = $('#hdnEscMatID').val();

                // Get selected resources
                var selectedResources = [];
                $('.resource-checkbox:checked').each(function () {
                    selectedResources.push($(this).val());
                });

                // Get selected teams
                var selectedTeams = [];
                $('.team-checkbox:checked').each(function () {
                    selectedTeams.push($(this).val());
                });

                // Get Can Approve settings (following EscalationMatrixNewForm.aspx.cs pattern)
                var canApprove = $('#chkCanApprove').is(':checked') ? "1" : "0";
                var anyOrAll = "0"; // Default value
                if (canApprove === "1") {
                    anyOrAll = $('#rdbCanApproveAny').is(':checked') ? "1" : "2";
                }

                var escalationLevelData = {
                    LevConID: parseInt(levConId),
                    EscMatID: parseInt(escMatId),
                    LevName: levelName,
                    LevDetails: levelDescription,
                    EscTime: escTime,
                    TimeUnit: timeUnit,
                    CanApprove: canApprove,
                    AnyOrAll: anyOrAll,
                    SelectedResources: selectedResources.join(','),
                    SelectedTeams: selectedTeams.join(',')
                };

                $.post('@Url.Action("SaveEscalationLevel", "EscalationMatrix")', escalationLevelData, function (response) {
                    if (response.success) {
                        alert(response.message);
                        $('#LevelModal').modal('hide');
                        loadEscalationLevels(escMatId);
                        clearLevelForm();
                        clearWizardData();
                    } else {
                        alert(response.message);
                    }
                }).fail(function () {
                    alert('Error saving escalation level');
                });
            });

            $(document).on('click', '.btnEditLevel', function () {
                var levConId = $(this).data('id');

                $.get('@Url.Action("GetEscalationLevel", "EscalationMatrix")', { levConId: levConId }, function (data) {
                    if (data) {
                        $('#txtLevelName').val(data.levName || ''); // Keep existing level name for editing
                        $('#txtLevelDescription').val(data.levDetails || '');
                        $('#txtEscTime').val(data.escTime || '');
                        $('#ddlTimeUnit').val(data.timeUnit || '1');
                        $('#hdnLevConID').val(data.levConID);

                        // Set Can Approve settings with defaults (following EscalationMatrixNewForm.aspx.cs pattern)
                        var canApprove = true; // Default to true
                        if (data.canApprove !== undefined && data.canApprove !== null) {
                            canApprove = data.canApprove === "1" || data.canApprove === true;
                        }
                        $('#chkCanApprove').prop('checked', canApprove);

                        if (canApprove) {
                            $('#approvalTypeContainer').show();
                            // Default to "Any" if no value or if value is "1"
                            if (data.anyOrAll === undefined || data.anyOrAll === null || data.anyOrAll === "" || data.anyOrAll === "1") {
                                $('#rdbCanApproveAny').prop('checked', true);
                                $('#rdbCanApproveAll').prop('checked', false);
                            } else {
                                $('#rdbCanApproveAny').prop('checked', false);
                                $('#rdbCanApproveAll').prop('checked', true);
                            }
                        } else {
                            $('#approvalTypeContainer').hide();
                            $('#rdbCanApproveAny').prop('checked', false);
                            $('#rdbCanApproveAll').prop('checked', false);
                        }

                        // Preload all resources and teams data first
                        console.log("Preloading resources and teams data for editing...");
                        loadAllResources();
                        loadAllTeams();

                        // Load existing resources and teams for this level (after a short delay to ensure data is loaded)
                        setTimeout(function() {
                            loadLevelResources(levConId);
                            loadLevelTeams(levConId);
                        }, 500);

                        // Show step 1 for editing
                        showStep(1);
                        $('#LevelModal').modal('show');
                    }
                }).fail(function () {
                    alert('Error loading escalation level details');
                });
            });

            function loadLevelResources(levConId) {
                $.get('@Url.Action("GetLevelResources", "EscalationMatrix")', { levConId: levConId }, function (data) {
                    if (data && data.length > 0) {
                        // Check the resources that are already assigned to this level
                        $.each(data, function (index, resource) {
                            $('#chkResource' + resource.ResourceId).prop('checked', true);
                        });
                        updateSelectedMembers();
                        // Save the checkbox states to localStorage for persistence
                        saveResourceCheckboxStates();
                    }
                }).fail(function () {
                    console.log('Error loading level resources');
                });
            }

            function loadLevelTeams(levConId) {
                $.get('@Url.Action("GetLevelTeams", "EscalationMatrix")', { levConId: levConId }, function (data) {
                    if (data && data.length > 0) {
                        // Check the teams that are already assigned to this level
                        $.each(data, function (index, team) {
                            $('#chkTeam' + team.GroupID).prop('checked', true);
                        });
                        updateSelectedTeams();
                        // Save the checkbox states to localStorage for persistence
                        saveTeamCheckboxStates();
                    }
                }).fail(function () {
                    console.log('Error loading level teams');
                });
            }

            $(document).on('click', '.btnDeleteLevel', function () {
                var levConId = $(this).data('id');
                var escMatId = $('#hdnEscMatID').val();

                if (confirm('Are you sure you want to delete this escalation level?')) {
                    $.post('@Url.Action("DeleteEscalationLevel", "EscalationMatrix")', { levConId: levConId }, function (response) {
                        if (response.success) {
                            alert(response.message);
                            loadEscalationLevels(escMatId);
                        } else {
                            alert(response.message);
                        }
                    }).fail(function () {
                        alert('Error deleting escalation level');
                    });
                }
            });

            // Tab click handlers (for manual navigation)
            $(document).on('click', '#nav-server-tab', function () {
                showStep(1);
            });

            $(document).on('click', '#nav-database-tab', function () {
                if (validateStep1()) {
                    showStep(2);
                } else {
                    showStep(1);
                    return false;
                }
            });

            $(document).on('click', '#nav-application-tab', function () {
                if (validateStep1()) {
                    showStep(3);
                } else {
                    showStep(1);
                    return false;
                }
            });

            function loadAllResources() {
                console.log("loadAllResources function called");
                $.get('@Url.Action("GetAllResources", "EscalationMatrix")', function (data) {
                    console.log("Resources data received:", data);
                    console.log("Resources data type:", typeof data);
                    console.log("Resources data length:", data ? data.length : 'null/undefined');

                    var tbody = $('#membersTableBody');
                    console.log("Members table body found:", tbody.length > 0);
                    tbody.empty();

                    if (data && data.length > 0) {
                        console.log("Processing", data.length, "resources");
                        var rowsAdded = 0;
                        $.each(data, function (index, resource) {
                            // Log the actual resource object to see the property names
                            console.log("Processing resource", index + 1, ":", resource.ResourceName || resource.resourceName || "NAME_NOT_FOUND");
                            console.log("Resource object:", resource);

                            // Try different possible property name variations
                            var resourceId = resource.ResourceId || resource.resourceId || resource.ID || resource.id || index;
                            var resourceName = resource.ResourceName || resource.resourceName || resource.Name || resource.name || 'Unknown';
                            var companyEmail = resource.CompanyEmail || resource.companyEmail || resource.Email || resource.email || '';
                            var mobilePhone = resource.MobilePhone || resource.mobilePhone || resource.Phone || resource.phone || '';
                            var organizationName = resource.OrganizationName || resource.organizationName || resource.Organization || resource.organization || '';
                            var unitName = resource.UnitName || resource.unitName || resource.Unit || resource.unit || '';
                            var departmentName = resource.DepartmentName || resource.departmentName || resource.Department || resource.department || '';

                            var row = '<tr>' +
                                '<td>' + (index + 1) + '</td>' +
                                '<td>' +
                                '<div class="form-check">' +
                                '<input class="form-check-input resource-checkbox" type="checkbox" value="' + resourceId + '" id="chkResource' + resourceId + '">' +
                                '<label class="form-check-label" for="chkResource' + resourceId + '"></label>' +
                                '</div>' +
                                '</td>' +
                                '<td>' +
                                '<ul class="ps-0 mb-0">' +
                                '<li class="list-group-item fw-semibold"><i class="cv-user"></i> : ' + resourceName + '</li>' +
                                '<li class="list-group-item"><i class="cv-mail"></i> : ' + companyEmail + '</li>' +
                                '<li class="list-group-item"><i class="cv-phone"></i> : ' + mobilePhone + '</li>' +
                                '</ul>' +
                                '</td>' +
                                '<td>' +
                                '<table>' +
                                '<tbody>' +
                                '<tr>' +
                                '<td class="fw-semibold"><i class="cv-organization"></i></td>' +
                                '<td> : </td>' +
                                '<td>' + organizationName + '</td>' +
                                '</tr>' +
                                '<tr>' +
                                '<td class="fw-semibold"><i class="cv-unit"></i></td>' +
                                '<td>:</td>' +
                                '<td>' + unitName + '</td>' +
                                '</tr>' +
                                '<tr>' +
                                '<td class="fw-semibold"><i class="cv-department"></i></td>' +
                                '<td>:</td>' +
                                '<td>' + departmentName + '</td>' +
                                '</tr>' +
                                '</tbody>' +
                                '</table>' +
                                '</td>' +
                                '</tr>';
                            tbody.append(row);
                            rowsAdded++;
                        });
                        console.log("Added", rowsAdded, "resource rows to table");

                        // Restore checkbox states after loading data
                        setTimeout(function() {
                            restoreResourceCheckboxStates();
                        }, 100);
                    } else {
                        console.log("No resources found or empty data");
                        tbody.append('<tr><td colspan="4" class="text-center">No resources found</td></tr>');
                    }
                }).fail(function (xhr, status, error) {
                    console.error('Error loading resources:', error);
                    alert('Error loading resources: ' + error);
                });
            }

            function loadAllTeams() {
                console.log("loadAllTeams function called");
                $.get('@Url.Action("GetAllTeams", "EscalationMatrix")', function (data) {
                    console.log("Teams data received:", data);
                    console.log("Teams data type:", typeof data);
                    console.log("Teams data length:", data ? data.length : 'null/undefined');

                    var tbody = $('#teamsTableBody');
                    console.log("Teams table body found:", tbody.length > 0);
                    tbody.empty();

                    if (data && data.length > 0) {
                        console.log("Processing", data.length, "teams");
                        var rowsAdded = 0;
                        $.each(data, function (index, team) {
                            // Log the actual team object to see the property names
                            console.log("Processing team", index + 1, ":", team.GroupName || team.groupName || "NAME_NOT_FOUND");
                            console.log("Team object:", team);

                            // Try different possible property name variations
                            var groupId = team.GroupID || team.groupId || team.ID || team.id || index;
                            var groupName = team.GroupName || team.groupName || team.Name || team.name || 'Unknown';
                            var groupEmail = team.GroupEmailID || team.groupEmailId || team.Email || team.email || '';
                            var ownerName = team.OwnerName || team.ownerName || team.Owner || team.owner || '';
                            var organizationName = team.OrganizationName || team.organizationName || team.Organization || team.organization || '';
                            var unitName = team.UnitName || team.unitName || team.Unit || team.unit || '';
                            var departmentName = team.DepartmentName || team.departmentName || team.Department || team.department || '';

                            var row = '<tr>' +
                                '<td>' + (index + 1) + '</td>' +
                                '<td>' +
                                '<div class="form-check">' +
                                '<input class="form-check-input team-checkbox" type="checkbox" value="' + groupId + '" id="chkTeam' + groupId + '">' +
                                '<label class="form-check-label" for="chkTeam' + groupId + '"></label>' +
                                '</div>' +
                                '</td>' +
                                '<td>' +
                                '<ul class="ps-0 mb-0">' +
                                '<li class="list-group-item fw-semibold"><i class="cv-user"></i> : ' + groupName + '</li>' +
                                '<li class="list-group-item"><i class="cv-mail"></i> : ' + groupEmail + '</li>' +
                                '<li class="list-group-item"><i class="cv-phone"></i> : ' + ownerName + '</li>' +
                                '</ul>' +
                                '</td>' +
                                '<td>' +
                                '<table>' +
                                '<tbody>' +
                                '<tr>' +
                                '<td class="fw-semibold"><i class="cv-organization"></i></td>' +
                                '<td> : </td>' +
                                '<td>' + organizationName + '</td>' +
                                '</tr>' +
                                '<tr>' +
                                '<td class="fw-semibold"><i class="cv-unit"></i></td>' +
                                '<td>:</td>' +
                                '<td>' + unitName + '</td>' +
                                '</tr>' +
                                '<tr>' +
                                '<td class="fw-semibold"><i class="cv-department"></i></td>' +
                                '<td>:</td>' +
                                '<td>' + departmentName + '</td>' +
                                '</tr>' +
                                '</tbody>' +
                                '</table>' +
                                '</td>' +
                                '</tr>';
                            tbody.append(row);
                            rowsAdded++;
                        });
                        console.log("Added", rowsAdded, "team rows to table");

                        // Restore checkbox states after loading data
                        setTimeout(function() {
                            restoreTeamCheckboxStates();
                        }, 100);
                    } else {
                        console.log("No teams found or empty data");
                        tbody.append('<tr><td colspan="4" class="text-center">No teams found</td></tr>');
                    }
                }).fail(function (xhr, status, error) {
                    console.error('Error loading teams:', error);
                    alert('Error loading teams: ' + error);
                });
            }

            // Select All Resources functionality
            $(document).on('change', '#chkSelectAllMembers', function () {
                var isChecked = $(this).is(':checked');
                $('.resource-checkbox').prop('checked', isChecked);
                updateSelectedMembers();
                // Save checkbox state to localStorage
                saveResourceCheckboxStates();
            });

            // Select All Teams functionality
            $(document).on('change', '#chkSelectAllTeams', function () {
                var isChecked = $(this).is(':checked');
                $('.team-checkbox').prop('checked', isChecked);
                updateSelectedTeams();
                // Save checkbox state to localStorage
                saveTeamCheckboxStates();
            });

            // Individual resource checkbox change
            $(document).on('change', '.resource-checkbox', function () {
                updateSelectedMembers();

                // Update select all checkbox state
                var totalResources = $('.resource-checkbox').length;
                var checkedResources = $('.resource-checkbox:checked').length;
                $('#chkSelectAllMembers').prop('checked', totalResources === checkedResources);

                // Save checkbox state to localStorage
                saveResourceCheckboxStates();
            });

            // Individual team checkbox change
            $(document).on('change', '.team-checkbox', function () {
                updateSelectedTeams();

                // Update select all checkbox state
                var totalTeams = $('.team-checkbox').length;
                var checkedTeams = $('.team-checkbox:checked').length;
                $('#chkSelectAllTeams').prop('checked', totalTeams === checkedTeams);

                // Save checkbox state to localStorage
                saveTeamCheckboxStates();
            });

            function updateSelectedMembers() {
                var selectedMembers = [];
                $('.resource-checkbox:checked').each(function () {
                    var resourceName = $(this).closest('tr').find('td:nth-child(3) li:first').text().replace(':', '').trim();
                    selectedMembers.push(resourceName);
                });
                $('#txtSelectedMembers').val(selectedMembers.join(', '));
            }

            function updateSelectedTeams() {
                var selectedTeams = [];
                $('.team-checkbox:checked').each(function () {
                    var teamName = $(this).closest('tr').find('td:nth-child(3) li:first').text().replace(':', '').trim();
                    selectedTeams.push(teamName);
                });
                $('#txtSelectedTeams').val(selectedTeams.join(', '));
            }

            // Search functionality for resources
            $('#btnSearchMembers').click(function () {
                var searchTerm = $('#txtSearchMembers').val().toLowerCase();
                filterTable('#membersTable', searchTerm);
            });

            $('#txtSearchMembers').on('keyup', function () {
                var searchTerm = $(this).val().toLowerCase();
                filterTable('#membersTable', searchTerm);
            });

            // Search functionality for teams
            $('#btnSearchTeams').click(function () {
                var searchTerm = $('#txtSearchTeams').val().toLowerCase();
                filterTable('#teamsTable', searchTerm);
            });

            $('#txtSearchTeams').on('keyup', function () {
                var searchTerm = $(this).val().toLowerCase();
                filterTable('#teamsTable', searchTerm);
            });

            function filterTable(tableId, searchTerm) {
                $(tableId + ' tbody tr').each(function () {
                    var rowText = $(this).text().toLowerCase();
                    if (rowText.indexOf(searchTerm) === -1) {
                        $(this).hide();
                    } else {
                        $(this).show();
                    }
                });
            }

            // Show Escalation Configuration
            $(document).on('click', '.btnShowConfiguration', function () {
                var escMatId = $(this).data('id');
                var escMatCode = $(this).data('code');

                $('#configurationMatrixCode').text(escMatCode);
                showEscalationConfiguration(escMatId);
                $('#ConfigurationModal').modal('show');
            });

            function showEscalationConfiguration(escMatId) {
                $.get('@Url.Action("GetEscalationMatrixConfiguration", "EscalationMatrix")', { escMatId: escMatId }, function (data) {
                    var timeline = $('#escalationTimeline');
                    timeline.empty();





                    if (data && data.length > 0) {
                        // Add Start Escalation
                        timeline.append('<div class="timeline-item d-flex mb-3">' +
                            '<div class="timeline-marker-container">' +
                            '<button class="btn btn-outline-secondary btn-sm position-absolute">Start Escalation</button>' +
                            '</div>' +
                            '</div>');

                        // Add each escalation level
                        $.each(data, function (index, level) {
                            var timeText = level.escTime + ' ' + level.timeUnitText;
                                timeline.append('<div class="timeline-item d-flex" style="width:72%;">' +
            '<div class="timeline-marker position-relative"></div>' +
        '<div class="border rounded p-2 flex-grow-1" style="border-color: #2a6496 !important;">' +
                                '<h6 class="text-primary mb-2">Escalation Level ' + ': ' + level.levName + '</h6>' +
                                '<p class="mb-1"><strong>Time To Escalate:</strong> </p>' +
                                '<p class="mb-1"><strong>Will EscalateAfter:</strong> ' + timeText + '</p>' +
                                '<div class="row">' +
                                            '<div class="col-md-2">' +
                                                '<div class="form-check">' +
                                                    '<input class="form-check-input" type="checkbox" id="chkCanApprove" checked>' +
                                                    '<label class="form-check-label" for="chkCanApprove">' +
                                                        'Can Approve' +
                                                    '</label>' +
                                                '</div>' +
                                            '</div>' +
                                            '<div class="col-md-6" id="approvalTypeContainer">' +
                                                '<div class="form-check form-check-inline">' +
                                                    '<input class="form-check-input" type="radio" name="approvalType" id="rdbCanApproveAny" value="1" checked>' +
                                                    '<label class="form-check-label" for="rdbCanApproveAny">' +
                                                        'Any' +
                                                    '</label>' +
                                                '</div>' +
                                                '<div class="form-check form-check-inline">' +
                                                    '<input class="form-check-input" type="radio" name="approvalType" id="rdbCanApproveAll" value="2">' +
                                                    '<label class="form-check-label" for="rdbCanApproveAll">' +
                                                        'All' +
                                                    '</label>' +
                                                '</div>' +
                                            '</div>' +
                                        '</div>' +
                                '</div>' +
                                '</div>');
                        });

                        // Add End Escalation
                        timeline.append('<div class="timeline-item d-flex">' +
                            ' <div class="timeline-marker-container">' +
                            '<button class="btn btn-outline-secondary btn-sm position-absolute">End Escalation</button>' +
                            '</div>' +
                            '</div>');
                    } else {
                        timeline.append('<div class="text-center py-4">' +
                            '<p class="text-muted">No escalation levels configured for this matrix</p>' +
                            '</div>');
                    }
                }).fail(function () {
                    alert('Error loading escalation configuration');
                });
            }

            // Test data binding function (for debugging)
            function testDataBinding() {
                $.get('@Url.Action("TestDataBinding", "EscalationMatrix")', function (data) {
                    console.log("Test Data Binding Result:", data);
                    alert("Test completed. Check console for details.\nResource Count: " + data.resourceCount + "\nTeam Count: " + data.teamCount);
                }).fail(function (xhr, status, error) {
                    console.error('Test Data Binding Error:', error);
                    alert('Test failed: ' + error);
                });
            }

            // Manual test functions for debugging
            function testLoadResources() {
                console.log("=== MANUAL TEST: Loading Resources ===");
                loadAllResources();
            }

            function testLoadTeams() {
                console.log("=== MANUAL TEST: Loading Teams ===");
                loadAllTeams();
            }

            function testDirectEndpoints() {
                console.log("=== TESTING DIRECT ENDPOINTS ===");

                // Test GetAllResources endpoint
                $.get('@Url.Action("GetAllResources", "EscalationMatrix")')
                    .done(function(data) {
                        console.log("Direct GetAllResources success:", data);
                        console.log("Data type:", typeof data);
                        console.log("Data length:", data ? data.length : 'null/undefined');
                        if (data && data.length > 0) {
                            console.log("First resource:", data[0]);
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Direct GetAllResources failed:", error);
                        console.error("Status:", status);
                        console.error("Response:", xhr.responseText);
                    });

                // Test GetAllTeams endpoint
                $.get('@Url.Action("GetAllTeams", "EscalationMatrix")')
                    .done(function(data) {
                        console.log("Direct GetAllTeams success:", data);
                        console.log("Data type:", typeof data);
                        console.log("Data length:", data ? data.length : 'null/undefined');
                        if (data && data.length > 0) {
                            console.log("First team:", data[0]);
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Direct GetAllTeams failed:", error);
                        console.error("Status:", status);
                        console.error("Response:", xhr.responseText);
                    });
            }

            function checkTableElements() {
                console.log("=== CHECKING TABLE ELEMENTS ===");

                var membersTable = $('#membersTable');
                var membersTableBody = $('#membersTableBody');
                var teamsTable = $('#teamsTable');
                var teamsTableBody = $('#teamsTableBody');
                var levelModal = $('#LevelModal');

                console.log("Members table exists:", membersTable.length > 0);
                console.log("Members table visible:", membersTable.is(':visible'));
                console.log("Members table body exists:", membersTableBody.length > 0);
                console.log("Members table body visible:", membersTableBody.is(':visible'));
                console.log("Teams table exists:", teamsTable.length > 0);
                console.log("Teams table visible:", teamsTable.is(':visible'));
                console.log("Teams table body exists:", teamsTableBody.length > 0);
                console.log("Teams table body visible:", teamsTableBody.is(':visible'));
                console.log("Level modal exists:", levelModal.length > 0);
                console.log("Level modal visible:", levelModal.is(':visible'));
                console.log("Level modal has 'show' class:", levelModal.hasClass('show'));

                // Check current active tab
                var activeTab = $('.nav-link.active');
                console.log("Active tab:", activeTab.attr('id'));
                console.log("Active tab pane:", $('.tab-pane.active.show').attr('id'));
            }

            function testHardcodedData() {
                console.log("=== TESTING HARDCODED DATA ===");

                // Test hardcoded resources
                $.get('@Url.Action("GetTestResources", "EscalationMatrix")')
                    .done(function(data) {
                        console.log("Hardcoded resources success:", data);

                        // Try to populate the members table with hardcoded data
                        var tbody = $('#membersTableBody');
                        tbody.empty();

                        if (data && data.length > 0) {
                            $.each(data, function (index, resource) {
                                var row = '<tr>' +
                                    '<td>' + (index + 1) + '</td>' +
                                    '<td>' +
                                    '<div class="form-check">' +
                                    '<input class="form-check-input resource-checkbox" type="checkbox" value="' + resource.ResourceId + '" id="chkResource' + resource.ResourceId + '">' +
                                    '<label class="form-check-label" for="chkResource' + resource.ResourceId + '"></label>' +
                                    '</div>' +
                                    '</td>' +
                                    '<td>' +
                                    '<ul class="ps-0 mb-0">' +
                                    '<li class="list-group-item fw-semibold"><i class="cv-user"></i> : ' + (resource.ResourceName || '') + '</li>' +
                                    '<li class="list-group-item"><i class="cv-mail"></i> : ' + (resource.CompanyEmail || '') + '</li>' +
                                    '<li class="list-group-item"><i class="cv-phone"></i> : ' + (resource.MobilePhone || '') + '</li>' +
                                    '</ul>' +
                                    '</td>' +
                                    '<td>' +
                                    '<table>' +
                                    '<tbody>' +
                                    '<tr>' +
                                    '<td class="fw-semibold"><i class="cv-organization"></i></td>' +
                                    '<td> : </td>' +
                                    '<td>' + (resource.OrganizationName || '') + '</td>' +
                                    '</tr>' +
                                    '<tr>' +
                                    '<td class="fw-semibold"><i class="cv-unit"></i></td>' +
                                    '<td>:</td>' +
                                    '<td>' + (resource.UnitName || '') + '</td>' +
                                    '</tr>' +
                                    '<tr>' +
                                    '<td class="fw-semibold"><i class="cv-department"></i></td>' +
                                    '<td>:</td>' +
                                    '<td>' + (resource.DepartmentName || '') + '</td>' +
                                    '</tr>' +
                                    '</tbody>' +
                                    '</table>' +
                                    '</td>' +
                                    '</tr>';
                                tbody.append(row);
                            });
                            console.log("Successfully populated members table with hardcoded data");
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Hardcoded resources failed:", error);
                    });

                // Test hardcoded teams
                $.get('@Url.Action("GetTestTeams", "EscalationMatrix")')
                    .done(function(data) {
                        console.log("Hardcoded teams success:", data);

                        // Try to populate the teams table with hardcoded data
                        var tbody = $('#teamsTableBody');
                        tbody.empty();

                        if (data && data.length > 0) {
                            $.each(data, function (index, team) {
                                var row = '<tr>' +
                                    '<td>' + (index + 1) + '</td>' +
                                    '<td>' +
                                    '<div class="form-check">' +
                                    '<input class="form-check-input team-checkbox" type="checkbox" value="' + team.GroupID + '" id="chkTeam' + team.GroupID + '">' +
                                    '<label class="form-check-label" for="chkTeam' + team.GroupID + '"></label>' +
                                    '</div>' +
                                    '</td>' +
                                    '<td>' +
                                    '<ul class="ps-0 mb-0">' +
                                    '<li class="list-group-item fw-semibold"><i class="cv-user"></i> : ' + (team.GroupName || '') + '</li>' +
                                    '<li class="list-group-item"><i class="cv-mail"></i> : ' + (team.GroupEmailID || '') + '</li>' +
                                    '<li class="list-group-item"><i class="cv-phone"></i> : ' + (team.OwnerName || '') + '</li>' +
                                    '</ul>' +
                                    '</td>' +
                                    '<td>' +
                                    '<table>' +
                                    '<tbody>' +
                                    '<tr>' +
                                    '<td class="fw-semibold"><i class="cv-organization"></i></td>' +
                                    '<td> : </td>' +
                                    '<td>' + (team.OrganizationName || '') + '</td>' +
                                    '</tr>' +
                                    '<tr>' +
                                    '<td class="fw-semibold"><i class="cv-unit"></i></td>' +
                                    '<td>:</td>' +
                                    '<td>' + (team.UnitName || '') + '</td>' +
                                    '</tr>' +
                                    '<tr>' +
                                    '<td class="fw-semibold"><i class="cv-department"></i></td>' +
                                    '<td>:</td>' +
                                    '<td>' + (team.DepartmentName || '') + '</td>' +
                                    '</tr>' +
                                    '</tbody>' +
                                    '</table>' +
                                    '</td>' +
                                    '</tr>';
                                tbody.append(row);
                            });
                            console.log("Successfully populated teams table with hardcoded data");
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Hardcoded teams failed:", error);
                    });
            }

            function testResourcesWithLogging() {
                console.log("=== TESTING RESOURCES WITH DETAILED LOGGING ===");

                $.get('@Url.Action("GetResourcesWithLogging", "EscalationMatrix")')
                    .done(function(response) {
                        console.log("GetResourcesWithLogging response:", response);
                        console.log("Success:", response.success);
                        console.log("Resources count:", response.resources ? response.resources.length : 'null/undefined');
                        console.log("Debug info:", response.debugInfo);

                        if (response.debugInfo && response.debugInfo.length > 0) {
                            console.log("=== DETAILED DEBUG INFO ===");
                            response.debugInfo.forEach(function(info, index) {
                                console.log(`${index + 1}. ${info}`);
                            });
                        }

                        if (response.resources && response.resources.length > 0) {
                            console.log("First resource details:", response.resources[0]);
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.error("GetResourcesWithLogging failed:", error);
                        console.error("Status:", status);
                        console.error("Response text:", xhr.responseText);
                    });
            }

            function forcePopulateTestData() {
                console.log("=== FORCE POPULATING TEST DATA ===");

                // Force populate members table with simple test data
                var membersTableBody = $('#membersTableBody');
                console.log("Members table body found:", membersTableBody.length > 0);

                if (membersTableBody.length > 0) {
                    membersTableBody.empty();

                    var testRow = '<tr>' +
                        '<td>1</td>' +
                        '<td><input type="checkbox" class="resource-checkbox" value="1"></td>' +
                        '<td>Test User Name<br><EMAIL><br>123-456-7890</td>' +
                        '<td>Test Org<br>Test Unit<br>Test Dept</td>' +
                        '</tr>';

                    membersTableBody.append(testRow);
                    console.log("Added test row to members table");
                    console.log("Members table HTML:", membersTableBody.html());
                } else {
                    console.error("Members table body not found!");
                }

                // Force populate teams table with simple test data
                var teamsTableBody = $('#teamsTableBody');
                console.log("Teams table body found:", teamsTableBody.length > 0);

                if (teamsTableBody.length > 0) {
                    teamsTableBody.empty();

                    var testRow = '<tr>' +
                        '<td>1</td>' +
                        '<td><input type="checkbox" class="team-checkbox" value="1"></td>' +
                        '<td>Test Team Name<br><EMAIL><br>Team Owner</td>' +
                        '<td>Test Org<br>Test Unit<br>Test Dept</td>' +
                        '</tr>';

                    teamsTableBody.append(testRow);
                    console.log("Added test row to teams table");
                    console.log("Teams table HTML:", teamsTableBody.html());
                } else {
                    console.error("Teams table body not found!");
                }
            }

            function debugModalAndTabs() {
                console.log("=== DEBUGGING MODAL AND TABS ===");

                var modal = $('#LevelModal');
                var membersTab = $('#nav-database-tab');
                var teamsTab = $('#nav-application-tab');
                var membersPane = $('#nav-database');
                var teamsPane = $('#nav-application');

                console.log("Modal exists:", modal.length > 0);
                console.log("Modal is visible:", modal.is(':visible'));
                console.log("Modal has 'show' class:", modal.hasClass('show'));

                console.log("Members tab exists:", membersTab.length > 0);
                console.log("Teams tab exists:", teamsTab.length > 0);
                console.log("Members pane exists:", membersPane.length > 0);
                console.log("Teams pane exists:", teamsPane.length > 0);

                console.log("Members pane is visible:", membersPane.is(':visible'));
                console.log("Teams pane is visible:", teamsPane.is(':visible'));

                console.log("Active tab:", $('.nav-link.active').attr('id'));
                console.log("Active pane:", $('.tab-pane.active.show').attr('id'));

                // Try to show the modal if it's not visible
                if (!modal.is(':visible')) {
                    console.log("Modal is not visible, trying to show it...");
                    modal.modal('show');

                    setTimeout(function() {
                        console.log("After showing modal - is visible:", modal.is(':visible'));
                    }, 1000);
                }
            }

            function inspectDataStructure() {
                console.log("=== INSPECTING DATA STRUCTURE ===");

                // Get resources data
                $.get('@Url.Action("GetAllResources", "EscalationMatrix")')
                    .done(function(resourcesData) {
                        console.log("=== RESOURCES DATA STRUCTURE ===");
                        console.log("Resources array length:", resourcesData.length);
                        if (resourcesData.length > 0) {
                            console.log("First resource object:", resourcesData[0]);
                            console.log("First resource properties:");
                            for (var prop in resourcesData[0]) {
                                console.log("  " + prop + ":", resourcesData[0][prop]);
                            }
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Failed to get resources:", error);
                    });

                // Get teams data
                $.get('@Url.Action("GetAllTeams", "EscalationMatrix")')
                    .done(function(teamsData) {
                        console.log("=== TEAMS DATA STRUCTURE ===");
                        console.log("Teams array length:", teamsData.length);
                        if (teamsData.length > 0) {
                            console.log("First team object:", teamsData[0]);
                            console.log("First team properties:");
                            for (var prop in teamsData[0]) {
                                console.log("  " + prop + ":", teamsData[0][prop]);
                            }
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Failed to get teams:", error);
                    });
            }

            // ===== CHECKBOX PERSISTENCE LOGIC (Following BIA Profile Pattern) =====

            // Save checkbox states to localStorage when checkboxes change
            function saveResourceCheckboxStates() {
                let resourceCheckboxStates = {};
                $('.resource-checkbox').each(function() {
                    resourceCheckboxStates[$(this).val()] = $(this).is(':checked');
                });
                localStorage.setItem('escalationResourceCheckboxStates', JSON.stringify(resourceCheckboxStates));
                console.log("Saved resource checkbox states:", resourceCheckboxStates);
            }

            function saveTeamCheckboxStates() {
                let teamCheckboxStates = {};
                $('.team-checkbox').each(function() {
                    teamCheckboxStates[$(this).val()] = $(this).is(':checked');
                });
                localStorage.setItem('escalationTeamCheckboxStates', JSON.stringify(teamCheckboxStates));
                console.log("Saved team checkbox states:", teamCheckboxStates);
            }

            // Restore checkbox states from localStorage
            function restoreResourceCheckboxStates() {
                let resourceCheckboxStates = JSON.parse(localStorage.getItem('escalationResourceCheckboxStates')) || {};
                $('.resource-checkbox').each(function() {
                    if (resourceCheckboxStates.hasOwnProperty($(this).val())) {
                        $(this).prop('checked', resourceCheckboxStates[$(this).val()]);
                    }
                });
                console.log("Restored resource checkbox states:", resourceCheckboxStates);
            }

            function restoreTeamCheckboxStates() {
                let teamCheckboxStates = JSON.parse(localStorage.getItem('escalationTeamCheckboxStates')) || {};
                $('.team-checkbox').each(function() {
                    if (teamCheckboxStates.hasOwnProperty($(this).val())) {
                        $(this).prop('checked', teamCheckboxStates[$(this).val()]);
                    }
                });
                console.log("Restored team checkbox states:", teamCheckboxStates);
            }

            // Clear checkbox states (call when form is submitted successfully)
            function clearCheckboxStates() {
                localStorage.removeItem('escalationResourceCheckboxStates');
                localStorage.removeItem('escalationTeamCheckboxStates');
                console.log("Cleared checkbox states");
            }

            // Get selected resources and teams for saving
            function getSelectedResourcesAndTeams() {
                let selectedResources = [];
                let selectedTeams = [];

                $('.resource-checkbox:checked').each(function() {
                    selectedResources.push({
                        id: $(this).val(),
                        checked: true
                    });
                });

                $('.team-checkbox:checked').each(function() {
                    selectedTeams.push({
                        id: $(this).val(),
                        checked: true
                    });
                });

                console.log("Selected resources:", selectedResources);
                console.log("Selected teams:", selectedTeams);

                return {
                    resources: selectedResources,
                    teams: selectedTeams
                };
            }

            // Make test functions globally available
            window.testLoadResources = testLoadResources;
            window.testLoadTeams = testLoadTeams;
            window.testDirectEndpoints = testDirectEndpoints;
            window.checkTableElements = checkTableElements;
            window.testHardcodedData = testHardcodedData;
            window.testResourcesWithLogging = testResourcesWithLogging;
            window.forcePopulateTestData = forcePopulateTestData;
            window.debugModalAndTabs = debugModalAndTabs;
            window.inspectDataStructure = inspectDataStructure;

            // Make checkbox persistence functions globally available
            window.saveResourceCheckboxStates = saveResourceCheckboxStates;
            window.saveTeamCheckboxStates = saveTeamCheckboxStates;
            window.restoreResourceCheckboxStates = restoreResourceCheckboxStates;
            window.restoreTeamCheckboxStates = restoreTeamCheckboxStates;
            window.clearCheckboxStates = clearCheckboxStates;
            window.getSelectedResourcesAndTeams = getSelectedResourcesAndTeams;
            window.testCheckboxPersistence = testCheckboxPersistence;

            function testCheckboxPersistence() {
                console.log("=== TESTING CHECKBOX PERSISTENCE ===");

                // Check current localStorage state
                var resourceStates = JSON.parse(localStorage.getItem('escalationResourceCheckboxStates')) || {};
                var teamStates = JSON.parse(localStorage.getItem('escalationTeamCheckboxStates')) || {};

                console.log("Current resource states in localStorage:", resourceStates);
                console.log("Current team states in localStorage:", teamStates);

                // Count checked checkboxes
                var checkedResources = $('.resource-checkbox:checked').length;
                var checkedTeams = $('.team-checkbox:checked').length;

                console.log("Currently checked resources:", checkedResources);
                console.log("Currently checked teams:", checkedTeams);

                // Test save and restore
                console.log("Testing save and restore...");
                saveResourceCheckboxStates();
                saveTeamCheckboxStates();

                // Clear all checkboxes
                $('.resource-checkbox').prop('checked', false);
                $('.team-checkbox').prop('checked', false);
                console.log("Cleared all checkboxes");

                // Restore from localStorage
                setTimeout(function() {
                    restoreResourceCheckboxStates();
                    restoreTeamCheckboxStates();

                    var restoredResources = $('.resource-checkbox:checked').length;
                    var restoredTeams = $('.team-checkbox:checked').length;

                    console.log("Restored resources:", restoredResources);
                    console.log("Restored teams:", restoredTeams);

                    if (restoredResources === checkedResources && restoredTeams === checkedTeams) {
                        console.log("✅ CHECKBOX PERSISTENCE TEST PASSED!");
                    } else {
                        console.log("❌ CHECKBOX PERSISTENCE TEST FAILED!");
                    }
                }, 500);
            }

            // Add test buttons (temporary for debugging)
            $(document).ready(function() {
                if (window.location.href.indexOf('debug=1') > -1) {
                    var debugButtons = '<div style="position:fixed;top:10px;right:10px;z-index:9999;max-height:90vh;overflow-y:auto;">' +
                        '<button onclick="testDataBinding()" style="background:red;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Test Data Binding</button>' +
                        '<button onclick="testDirectEndpoints()" style="background:blue;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Test Direct Endpoints</button>' +
                        '<button onclick="testLoadResources()" style="background:green;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Test Load Resources</button>' +
                        '<button onclick="testLoadTeams()" style="background:orange;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Test Load Teams</button>' +
                        '<button onclick="checkTableElements()" style="background:purple;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Check Table Elements</button>' +
                        '<button onclick="testHardcodedData()" style="background:darkgreen;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Test Hardcoded Data</button>' +
                        '<button onclick="testResourcesWithLogging()" style="background:navy;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Test With Logging</button>' +
                        '<button onclick="forcePopulateTestData()" style="background:maroon;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Force Populate Data</button>' +
                        '<button onclick="debugModalAndTabs()" style="background:teal;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Debug Modal & Tabs</button>' +
                        '<button onclick="inspectDataStructure()" style="background:darkblue;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Inspect Data Structure</button>' +
                        '<button onclick="testCheckboxPersistence()" style="background:darkred;color:white;padding:5px;margin:2px;display:block;width:150px;font-size:11px;">Test Checkbox Persistence</button>' +
                        '</div>';
                    $('body').prepend(debugButtons);
                }
            });
        });
    </script>
}