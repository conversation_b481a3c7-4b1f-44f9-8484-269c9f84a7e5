﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Areas.BCMPerformanceEvaluation.Controllers;
[Area("BCMPerformanceEvaluation")]
public class PerformaneEvaluationFormSCController : Controller
{

    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;


    ManageUsersDetails _UserDetails = new ManageUsersDetails();
    int iEntityTypeID = 0;

    public PerformaneEvaluationFormSCController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _UserDetails = _Utilities.LoginUserDetails();

        if (_UserDetails == null)
        {
            RedirectToAction("Login", "Login");
        }
        _CVLogger = CVLogger;
    }

    public IActionResult Index()
    {
        return View();
    }
    
    [HttpGet]
    public IActionResult PerformaneEvaluationFormSC(string ID)
    {
        List<PerformanceEvaluation> lstPerformaneEvaluationForm = new List<PerformanceEvaluation>();
        try
        {
            var performanceEvaluation = _ProcessSrv.GetPerformanceEvaluationSC_ByID(ID);
            if (performanceEvaluation != null)
            {
                lstPerformaneEvaluationForm.Add(performanceEvaluation);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View(lstPerformaneEvaluationForm);
    }

}

