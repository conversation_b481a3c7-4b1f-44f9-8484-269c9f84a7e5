﻿function renderChart(type, containerId, data, title = "", xaxis, yaxis) {
    
    if (am5.registry.rootElements.length > 0) {
        am5.registry.rootElements.forEach(root => root.dispose());
    }

    const root = am5.Root.new(containerId);
    root.setThemes([am5themes_Animated.new(root)]);
    let xFieldValue = xaxis;
    let yFieldValue = yaxis;

    let chart, series;

    switch (type) {
        case 'line':
        case 'column':
        case 'bar':
            chart = root.container.children.push(am5xy.XYChart.new(root, {
                panX: true,
                panY: true,
                wheelX: "panX",
                wheelY: "zoomX",
                layout: root.verticalLayout
            }));

            const xField = xFieldValue;
            const yField = yFieldValue;

            const xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
                categoryField: xField,
                renderer: type === 'bar'
                    ? am5xy.AxisRendererY.new(root, {})
                    : am5xy.AxisRendererX.new(root, {})
            }));

            const yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                renderer: type === 'bar'
                    ? am5xy.AxisRendererX.new(root, {})
                    : am5xy.AxisRendererY.new(root, {})
            }));

            if (type === 'line') {
                series = chart.series.push(am5xy.LineSeries.new(root, {
                    name: "Line Series",
                    xAxis: xAxis,
                    yAxis: yAxis,
                    valueYField: yField,
                    categoryXField: xField,
                    tooltip: am5.Tooltip.new(root, {
                        labelText: "{valueY}"
                    })
                }));
            } else {
                series = chart.series.push((type === 'bar' ? am5xy.ColumnSeries : am5xy.ColumnSeries).new(root, {
                    name: "Bar/Column Series",
                    xAxis: xAxis,
                    yAxis: yAxis,
                    valueYField: yField,
                    categoryXField: xField,
                    tooltip: am5.Tooltip.new(root, {
                        labelText: "{valueY}"
                    })
                }));
            }

            xAxis.data.setAll(data);
            series.data.setAll(data);
            break;

        case 'radar':

            chart = root.container.children.push(am5radar.RadarChart.new(root, {
                startAngle: 160,
                endAngle: 380
            }));

            const xRadar = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
                categoryField: xFieldValue,
                renderer: am5radar.AxisRendererCircular.new(root, {})
            }));

            const yRadar = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                renderer: am5radar.AxisRendererRadial.new(root, {})
            }));

            series = chart.series.push(am5radar.RadarLineSeries.new(root, {
                name: "Radar Series",
                xAxis: xRadar,
                yAxis: yRadar,
                valueYField: yFieldValue,
                categoryXField: xFieldValue,
                tooltip: am5.Tooltip.new(root, {
                    labelText: "{valueY}"
                })
            }));

            xRadar.data.setAll(data);
            series.data.setAll(data);
            break;

        case 'pie':
        case 'donut':
            chart = root.container.children.push(am5percent.PieChart.new(root, {
                layout: root.verticalLayout,
                innerRadius: type === 'donut' ? am5.percent(40) : 0
            }));

            series = chart.series.push(am5percent.PieSeries.new(root, {
                valueField: yFieldValue,
                categoryField: xFieldValue,
                tooltip: am5.Tooltip.new(root, {
                    labelText: "{category}: {valuePercentTotal.formatNumber('#.0')}%"
                })
            }));

            series.data.setAll(data);
            break;

        case 'table':
            $("#" + containerId).empty();
            const container = document.getElementById(containerId);
                const table = document.createElement('table');
                const thead = document.createElement('thead');
                const tbody = document.createElement('tbody');

                // Create header row
                const headerRow = document.createElement('tr');
                Object.keys(data[0]).forEach(key => {
                    const th = document.createElement('th');
                    th.innerText = key;
                    headerRow.appendChild(th);
                });
                thead.appendChild(headerRow);

                // Create data rows
                data.forEach(item => {
                    const row = document.createElement('tr');
                    Object.values(item).forEach(value => {
                        const td = document.createElement('td');
                        td.innerText = value;
                        row.appendChild(td);
                    });
                    tbody.appendChild(row);
                });

                table.appendChild(thead);
                table.appendChild(tbody);
            container.appendChild(table);
            break;
        default:
            console.error("Invalid chart type:", type);
            return;
    }
    if (type != 'table') {
        // Add title
        if (title) {
            chart.children.unshift(am5.Label.new(root, {
                text: title,
                fontSize: 20,
                fontWeight: "500",
                centerX: am5.percent(50),
                x: am5.percent(50),
                paddingBottom: 15
            }));
        }

        series.appear(1000);
        chart.appear(1000, 100);
    }
}