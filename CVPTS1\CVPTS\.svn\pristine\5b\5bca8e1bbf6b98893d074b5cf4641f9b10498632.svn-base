﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model BCM.BusinessClasses.Applications
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}
<form asp-action="AddBCMApplication" method="post" id="addBCMApplication" class="needs-validation progressive-validation" novalidate>
    <div class="row row-cols-2">
        <div style="display:none">
            <input type="hidden" asp-for="ApplicationCode" id="ProcessID" />
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Organization</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                    <select class="form-select form-control selectized" autocomplete="off" id="ddlOrganization" asp-for=OrgID aria-label="Default select example" asp-items="@(new SelectList(ViewBag.OrgInfo,"Id","OrganizationName"))" required>
                        <option selected value="0">-- Select Organizations --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Organization</div>
            </div>
            <div class="form-group">
                <label class="form-label">Department</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-department"></i></span>
                    <select class="form-select form-control selectized ddlDepartment" autocomplete="off" id="ddlDepartment" asp-for=DepartmentID aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Department,"DepartmentID","DepartmentName"))" required>
                        <option selected value="0">-- Select Departments --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Departments</div>
            </div>
            <div class="form-group">
                <label class="form-label">Application Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-Application-name"></i></span>
                    <input class="form-control" type="text" id="ApplicationName" asp-for=ApplicationName placeholder="Enter Application Name" required pattern="[A-Za-z][a-zA-Z0-9'&quot;,._$?!+&=#%`~\\/<>;:|{}\*\-+=()\s]{0,499}$" title="Application name must starts with character" />
                </div>
                <div class="invalid-feedback">Enter Application Name</div>
            </div>
            <div class="form-group">
                <label class="form-label">Description</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span> 
                    <input class="form-control" type="text" asp-for=ApplicationDetails placeholder="Enter Description"  />
                </div>
                <div class="invalid-feedback">Enter Description</div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Unit</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-unit"></i></span>
                    <select class="form-select form-control selectized" autocomplete="off" asp-for=UnitID id="ddlUnit" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Unit,"UnitID","UnitName"))" required>
                        <option selected value="0">-- Select Units --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Unit</div>
            </div>

            <div class="form-group">
                <label class="form-label">Sub Department</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                    <select class="form-select form-control selectized ddlSubDepartment" autocomplete="off" asp-for=SubfunctionId id="ddlSubDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.SubDepartment,"SubFunctionID","SubFunctionName"))" required>
                        <option selected value="0">-- Select Sub Departments --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select SubDepartment</div>
            </div>
            <div class="form-group">
                <label class="form-label">Application Owner</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <select class="form-select form-control selectized" autocomplete="off" asp-for=OwnerID id="ddlResources" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.lstResource,"ResourceId","ResourceName"))" required>
                        <option selected value="0">-- Select Application Owner --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Application Owner</div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>

<script>
    // Wrap everything in a timeout to avoid conflicts with other scripts
    setTimeout(function() {
        console.log("=== INITIALIZING BUSINESS PROCESS FORM ===");

        // Avoid conflicts by using a namespace
        window.BusinessProcessForm = {
            init: function() {
                this.setupFormValidation();
                this.setupDropdownHandlers();
                this.setupProcessNameValidation();
                this.testEndpoints();
            },

            testEndpoints: function() {
                console.log("Testing AJAX endpoints...");

                // Test CheckProcessNameExists with a simple GET request
                var testUrl = '@Url.Action("CheckProcessNameExists", "ManageApplication")';
                console.log("Testing URL:", testUrl);

                jQuery.get(testUrl, { processName: 'TEST', processId: 0 })
                    .done(function(response) {
                        console.log("✓ CheckProcessNameExists working:", response);
                    })
                    .fail(function(xhr, status, error) {
                        console.error("✗ CheckProcessNameExists failed:", status, error);
                        console.error("Response:", xhr.responseText);
                    });
            },

            setupFormValidation: function() {
                var form = document.getElementById('addBCMApplication');
                if (!form) {
                    console.error("Form not found!");
                    return;
                }

                console.log("Setting up form validation...");

                // Initialize BCM Validation if available
                if (typeof window.BCMValidation !== 'undefined') {
                    try {
                        window.BCMValidation.init();
                        window.BCMValidation.addRequiredFieldIndicators(form);
                        window.BCMValidation.addFormatIndicators(form);
                        console.log("BCM Validation initialized");
                    } catch (e) {
                        console.error("BCM Validation error:", e);
                    }
                }

                // Form submission handler
                var self = this;
                form.addEventListener('submit', function(e) {
                    console.log("=== FORM SUBMIT TRIGGERED ===");

                    // Always prevent default submission
                    e.preventDefault();
                    e.stopPropagation();

                    // Get form data
                    var processName = document.getElementById('ApplicationName').value.trim();
                    var processId = document.getElementById('ProcessID').value || 0;

                    console.log("Process Name:", processName);
                    console.log("Process ID:", processId);

                    // Step 1: Check process name if provided
                    if (processName && processName.length >= 2) {
                        console.log("=== STEP 1: CHECKING PROCESS NAME ===");

                        self.checkProcessName(processName, processId, function(exists) {
                            if (exists) {
                                console.log("=== PROCESS NAME EXISTS - BLOCKING ===");
                                alert("This Application Name already exists. Please choose another name.");
                                document.getElementById('ApplicationName').focus();
                            } else {
                                console.log("=== PROCESS NAME UNIQUE - CONTINUING ===");
                                self.validateAndSubmit(form);
                            }
                        });
                    } else {
                        console.log("=== NO PROCESS NAME CHECK NEEDED ===");
                        self.validateAndSubmit(form);
                    }
                });
            },

            checkProcessName: function(processName, processId, callback) {
                var checkUrl = '@Url.Action("CheckProcessNameExists", "ManageApplication")';
                console.log("Checking process name at:", checkUrl);

                jQuery.ajax({
                    url: checkUrl,
                    type: 'GET',
                    data: {
                        processName: processName,
                        processId: processId
                    },
                    timeout: 10000,
                    success: function(response) {
                        console.log("=== PROCESS NAME CHECK SUCCESS ===");
                        console.log("Response:", response);
                        callback(response && response.exists === true);
                    },
                    error: function(xhr, status, error) {
                        console.log("=== PROCESS NAME CHECK ERROR ===");
                        console.error("Status:", status);
                        console.error("Error:", error);
                        console.error("Response Text:", xhr.responseText);
                        alert("Error checking process name. Please try again.");
                        callback(false); // Assume unique on error
                    }
                });
            },

            validateAndSubmit: function(form) {
                console.log("=== STEP 2: FORM VALIDATION ===");

                // Use BCM validation if available
                if (typeof window.BCMValidation !== 'undefined') {
                    try {
                        window.BCMValidation.showAllValidationMessages(form);
                        var isValid = window.BCMValidation.validateForm(form);
                        console.log("BCM Validation result:", isValid);

                        if (!isValid) {
                            console.log("=== FORM VALIDATION FAILED ===");
                            var firstInvalid = form.querySelector('.is-invalid');
                            if (firstInvalid) {
                                firstInvalid.focus();
                            }
                            return;
                        }
                    } catch (e) {
                        console.error("BCM Validation error:", e);
                        // Fall back to HTML5 validation
                        if (!form.checkValidity()) {
                            console.log("=== HTML5 VALIDATION FAILED ===");
                            form.reportValidity();
                            return;
                        }
                    }
                } else {
                    // Fallback to HTML5 validation
                    if (!form.checkValidity()) {
                        console.log("=== HTML5 VALIDATION FAILED ===");
                        form.reportValidity();
                        return;
                    }
                }

                // All validation passed - submit form
                console.log("=== STEP 3: SUBMITTING FORM ===");

                // Create a temporary form to submit without event listeners
                var tempForm = document.createElement('form');
                tempForm.method = form.method;
                tempForm.action = form.action;

                // Copy all form data
                var formData = new FormData(form);
                for (var pair of formData.entries()) {
                    var input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = pair[0];
                    input.value = pair[1];
                    tempForm.appendChild(input);
                }

                document.body.appendChild(tempForm);
                tempForm.submit();
            },

            setupProcessNameValidation: function() {
                var self = this;
                var processNameTimeout;

                jQuery('#ApplicationName').on('input', function() {
                    var processName = jQuery(this).val().trim();
                    var processId = jQuery('#ProcessID').val() || 0;

                    clearTimeout(processNameTimeout);

                    if (processName.length >= 2) {
                        processNameTimeout = setTimeout(function() {
                            console.log("Real-time validation for:", processName);

                            self.checkProcessName(processName, processId, function(exists) {
                                var input = jQuery('#ApplicationName');
                                var feedback = input.closest('.form-group').find('.invalid-feedback');

                                if (exists) {
                                    // Show error
                                    input.addClass('is-invalid');
                                    input.closest('.input-group').addClass('is-invalid');
                                    feedback.text('This Application Name already exists. Please choose another.').show();
                                } else {
                                    // Clear error if no other validation issues
                                    if (input[0].checkValidity()) {
                                        input.removeClass('is-invalid');
                                        input.closest('.input-group').removeClass('is-invalid');
                                        feedback.hide();
                                    }
                                }
                            });
                        }, 500);
                    }
                });
            },

            setupDropdownHandlers: function() {
                // Unit dropdown change handler
                jQuery(document).on("change", "#ddlUnit", function() {
                    var iUnitID = jQuery(this).val();
                    console.log("=== UNIT DROPDOWN CHANGED ===");
                    console.log("Selected Unit ID:", iUnitID);

                    if (!iUnitID || iUnitID == "0") {
                        console.log("No unit selected, clearing departments");
                        return;
                    }

                    var departmentsUrl = '@Url.Action("GetAllDepartments", "ManageApplication")';
                    console.log("Departments URL:", departmentsUrl);

                    jQuery.ajax({
                        url: departmentsUrl,
                        type: 'GET',
                        data: { iUnitID: iUnitID },
                        timeout: 10000,
                        success: function(response) {
                            console.log("=== DEPARTMENTS AJAX SUCCESS ===");
                            console.log("Response:", response);

                            try {
                                var selectizeInstance = jQuery(".ddlDepartment")[0].selectize;
                                if (selectizeInstance) {
                                    selectizeInstance.clear();
                                    selectizeInstance.clearOptions();
                                    selectizeInstance.addOption({ value: "0", text: "-- Select Departments --" });
                                    selectizeInstance.addItem("0");

                                    if (response && Array.isArray(response)) {
                                        response.forEach(function(item) {
                                            if (item.departmentID && item.departmentName) {
                                                selectizeInstance.addOption({
                                                    value: item.departmentID,
                                                    text: item.departmentName
                                                });
                                            }
                                        });
                                        console.log("Added", response.length, "departments to dropdown");
                                    } else {
                                        console.log("No departments returned or invalid response format");
                                    }
                                } else {
                                    console.error("Selectize instance not found for department dropdown");
                                }
                            } catch (error) {
                                console.error("Error updating department dropdown:", error);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log("=== DEPARTMENTS AJAX ERROR ===");
                            console.error("Status:", status);
                            console.error("Error:", error);
                            console.error("Response Text:", xhr.responseText);
                            console.error("Status Code:", xhr.status);
                        }
                    });
                });


                // Department dropdown change handler
                jQuery(document).on("change", ".ddlDepartment", function() {
                    var iDepartmentID = jQuery(this).val();
                    console.log("=== DEPARTMENT DROPDOWN CHANGED ===");
                    console.log("Selected Department ID:", iDepartmentID);

                    if (!iDepartmentID || iDepartmentID == "0") {
                        console.log("No department selected, clearing subdepartments");
                        return;
                    }

                    var subdepartmentsUrl = '@Url.Action("GetAllSubDepartments", "ManageApplication")';
                    console.log("Subdepartments URL:", subdepartmentsUrl);

                    jQuery.ajax({
                        url: subdepartmentsUrl,
                        type: 'GET',
                        data: { iDepartmentID: iDepartmentID },
                        timeout: 10000,
                        success: function(response) {
                            console.log("=== SUBDEPARTMENTS AJAX SUCCESS ===");
                            console.log("Response:", response);

                            try {
                                var selectizeInstance = jQuery('.ddlSubDepartment')[0].selectize;
                                if (selectizeInstance) {
                                    selectizeInstance.clear();
                                    selectizeInstance.clearOptions();
                                    selectizeInstance.addOption({ value: "0", text: "-- Select SubDepartments --" });
                                    selectizeInstance.addItem("0");

                                    if (response && Array.isArray(response)) {
                                        response.forEach(function(item) {
                                            if (item.subFunctionID && item.subFunctionName) {
                                                selectizeInstance.addOption({
                                                    value: item.subFunctionID,
                                                    text: item.subFunctionName
                                                });
                                            }
                                        });
                                        console.log("Added", response.length, "subdepartments to dropdown");
                                    } else {
                                        console.log("No subdepartments returned or invalid response format");
                                    }
                                } else {
                                    console.error("Selectize instance not found for subdepartment dropdown");
                                }
                            } catch (error) {
                                console.error("Error updating subdepartment dropdown:", error);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log("=== SUBDEPARTMENTS AJAX ERROR ===");
                            console.error("Status:", status);
                            console.error("Error:", error);
                            console.error("Response Text:", xhr.responseText);
                            console.error("Status Code:", xhr.status);
                        }
                    });
                });
            }
        };

        // Initialize everything
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                window.BusinessProcessForm.init();
            });
        } else {
            window.BusinessProcessForm.init();
        }

    }, 1000); // 1 second delay to avoid conflicts

</script>