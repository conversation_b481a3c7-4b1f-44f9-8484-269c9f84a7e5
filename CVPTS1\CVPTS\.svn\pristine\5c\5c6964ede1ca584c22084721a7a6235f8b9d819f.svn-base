@{
    ViewData["Title"] = "Training Document";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@Html.AntiForgeryToken()

<link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
<link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />

<style>
    .document-viewer-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 20px 0;
    }

    .document-viewer-wrapper {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        width: 100%;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 40px);
    }

    .document-header {
        background: #fff;
        border-bottom: 1px solid #dee2e6;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
    }

    .document-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
        margin: 0;
    }

    .document-actions {
        display: flex;
        gap: 10px;
    }

    .document-content {
        background: white;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .pdf-viewer {
        width: 100%;
        height: 100%;
        border: none;
        flex: 1;
    }

    .document-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 400px;
        color: #6c757d;
    }

    .document-placeholder i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 4px;
        margin: 20px;
    }

    .toolbar {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 10px 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 20px;
        flex-shrink: 0;
    }

    .toolbar-left {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .toolbar-center {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .toolbar-right {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .zoom-controls {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .zoom-btn {
        background: none;
        border: 1px solid #dee2e6;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
    }

    .zoom-btn:hover {
        background: #e9ecef;
    }

    .page-info {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .btn:disabled,
    .btn.disabled {
        opacity: 0.5 !important;
        cursor: not-allowed !important;
        pointer-events: none;
    }

    .toolbar-center {
        min-width: 120px;
        text-align: center;
    }
</style>

<script src="~/js/thirdparty.bundle.js" asp-append-version="true"></script>
<script src="~/js/viewer.part.bundle.js" asp-append-version="true"></script>

<div class="document-viewer-container">
    <div class="container-fluid">
        <div class="document-viewer-wrapper">
            @if (ViewBag.DocumentData != null)
            {
                <!-- Document Header -->
                <div class="document-header">
                    <h5 class="document-title">
                        <i class="cv-document me-2"></i>
                        @(ViewBag.DocumentName ?? "Training Document")
                    </h5>
                    <div class="document-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="printDocument()">
                            <i class="fas fa-print me-1"></i>
                            Print
                        </button>
                        <a href="@Url.Action("DownloadTrainingDoc", "TrainingDoc", new { id = ViewBag.DocumentId })"
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-download me-1"></i>
                            Download
                        </a>
                        @if (ViewBag.TrainingId != null || ViewBag.DocumentId != null)
                        {
                            <a href="@Url.Action("TrainingQuestionPaper", "TrainingandExamination", new {
                                Area = "BCMTraining",
                                TrainingID = ViewBag.TrainingId ?? ViewBag.DocumentId,
                                watch = ViewBag.Watch ?? 0,
                                publishid = ViewBag.PublishId ?? 0
                            })"
                               class="btn btn-success btn-sm"
                               onclick="return confirm('Are you ready to start the training test? Once started, the timer will begin.')">
                                <i class="fas fa-play me-1"></i>
                                Start Test
                            </a>
                        }
                        else
                        {
                            <button class="btn btn-success btn-sm" onclick="startTest()">
                                <i class="fas fa-play me-1"></i>
                                Start Test
                            </button>
                        }
                    </div>
                </div>

                <!-- Document Toolbar -->
                <div class="toolbar">
                    <button class="btn btn-outline-secondary btn-sm" onclick="goToPreviousPage()">
                        <i class="fas fa-chevron-left"></i>
                        Previous
                    </button>

                    <div class="toolbar-center">
                        <span class="text-muted">Page 1</span>
                    </div>

                    <button class="btn btn-outline-secondary btn-sm" onclick="goToNextPage()">
                        Next
                        <i class="fas fa-chevron-right"></i>
                    </button>

                    <div class="toolbar-right">
                        <div class="zoom-controls">
                            <button class="zoom-btn" onclick="zoomOut()">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <span id="zoomLevel">100%</span>
                            <button class="zoom-btn" onclick="zoomIn()">
                                <i class="fas fa-search-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Document Content -->
                <div class="document-content">
                    @if (ViewBag.IsPdf == true)
                    {
                        <div id="pdfContainer" class="pdf-container">
                            <iframe class="pdf-viewer"
                                    src="@Url.Action("ViewPDF", "TrainingDoc", new { id = ViewBag.DocumentId })"
                                    type="application/pdf"
                                    allow="fullscreen"
                                    id="pdfViewer">
                                <p>Your browser does not support PDFs.
                                   <a href="@Url.Action("DownloadTrainingDoc", "TrainingDoc", new { id = ViewBag.DocumentId })">Download the PDF</a>.
                                </p>
                            </iframe>
                        </div>
                    }
                    else
                    {
                        <div class="document-placeholder">
                            <i class="cv-document"></i>
                            <h5>@(ViewBag.DocumentName ?? "Training Document")</h5>
                            <p>This document type cannot be previewed in the browser.</p>
                            <a href="@Url.Action("DownloadTrainingDoc", "TrainingDoc", new { id = ViewBag.DocumentId })" 
                               class="btn btn-primary">
                                <i class="fas fa-download me-1"></i>
                                Download Document
                            </a>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @(ViewBag.ErrorMessage ?? "Document not found.")
                </div>
                <div class="document-placeholder">
                    <i class="cv-document"></i>
                    <h5>No Document Available</h5>
                    <p>The requested training document could not be loaded.</p>
                    <a href="@Url.Action("TrainingandExamination", "TrainingandExamination")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Training
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<script>
    let currentZoom = 100; // Set default zoom to 100%
    let currentPage = 1;
    let totalPages = 999; // Start with high number to allow navigation
    let documentId = @ViewBag.DocumentId;
    let navigationMethod = 'url'; // 'url' or 'pdfjs'

    function printDocument() {
        if (document.querySelector('.pdf-viewer')) {
            document.querySelector('.pdf-viewer').contentWindow.print();
        } else {
            window.print();
        }
    }

    function startTest() {
        // Get TrainingID, watch, and publishId from ViewBag
        var trainingId = @(ViewBag.TrainingId ?? ViewBag.DocumentId ?? 0);
        var watch = @(ViewBag.Watch ?? 0);  // Default to 0 for normal mode (not watch mode)
        var publishId = @(ViewBag.PublishId ?? 0);

        console.log('TrainingDoc startTest values:');
        console.log('TrainingId:', trainingId);
        console.log('Watch:', watch);
        console.log('PublishId:', publishId);
        console.log('ViewBag.TrainingId:', @(ViewBag.TrainingId ?? 0));
        console.log('ViewBag.DocumentId:', @(ViewBag.DocumentId ?? 0));
        console.log('ViewBag.PublishId:', @(ViewBag.PublishId ?? 0));
        console.log('ViewBag.Watch:', @(ViewBag.Watch ?? 0));

        if (!trainingId || trainingId === 0) {
            alert('Training ID not found. Cannot start test.');
            return;
        }

        // Show confirmation dialog
        if (confirm('Are you ready to start the training test? Once started, the timer will begin.')) {
            // Navigate directly to TrainingQuestionPaper with required parameters
            var testUrl = '@Url.Action("TrainingQuestionPaper", "TrainingandExamination", new { Area = "BCMTraining" })' +
                '?TrainingID=' + trainingId +
                '&watch=' + watch +
                '&publishid=' + publishId;

            console.log('Navigating to TrainingQuestionPaper with URL:', testUrl);
            window.location.href = testUrl;
        }
    }

    function zoomIn() {
        if (currentZoom < 200) {
            currentZoom += 10;
            updateZoom();
        }
    }

    function zoomOut() {
        if (currentZoom > 50) {
            currentZoom -= 10;
            updateZoom();
        }
    }

    function updateZoom() {
        document.getElementById('zoomLevel').textContent = currentZoom + '%';
        const iframe = document.querySelector('.pdf-viewer');
        if (iframe) {
            iframe.style.transform = `scale(${currentZoom / 100})`;
            iframe.style.transformOrigin = 'top left';
            iframe.style.width = (100 / (currentZoom / 100)) + '%';
            iframe.style.height = (800 * (currentZoom / 100)) + 'px';
        }
    }

    // Simple and reliable navigation functions
    function goToPreviousPage() {
        console.log('🔙 Previous page button clicked!');
        console.log('Current page before:', currentPage);

        if (currentPage > 1) {
            currentPage--;
            console.log('New page will be:', currentPage);
            navigateToPage(currentPage);
        } else {
            console.log('❌ Already on first page, cannot go back');
            alert('Already on the first page');
        }
    }

    function goToNextPage() {
        console.log('🔜 Next page button clicked!');
        console.log('Current page before:', currentPage);

        currentPage++;
        console.log('New page will be:', currentPage);
        navigateToPage(currentPage);
    }

    function navigateToPage(pageNumber) {
        console.log('🚀 Navigating to page:', pageNumber);

        const iframe = document.querySelector('.pdf-viewer');
        if (!iframe) {
            console.error('❌ PDF iframe not found!');
            return;
        }

        console.log('✅ PDF iframe found');

        const baseUrl = '@Url.Action("ViewPDF", "TrainingDoc", new { Area = "BCMTraining" })';
        const newUrl = baseUrl + '?id=' + documentId + '#page=' + pageNumber + '&zoom=100&toolbar=1';

        console.log('📄 Current iframe src:', iframe.src);
        console.log('🔗 New URL will be:', newUrl);

        // Show loading indicator
        const pageDisplay = document.querySelector('.toolbar-center');
        if (pageDisplay) {
            pageDisplay.innerHTML = '<span class="text-muted"><i class="fas fa-spinner fa-spin"></i> Loading...</span>';
        }

        // Change the iframe source
        iframe.src = newUrl;

        // Update UI after a short delay
        setTimeout(function() {
            updatePageDisplay();
            updateNavigationButtons();
            console.log('✅ UI updated for page:', pageNumber);
        }, 500);

        // Try to detect total pages after navigation
        setTimeout(function() {
            detectTotalPages();
        }, 2000);
    }

    function updatePageDisplay() {
        const pageDisplay = document.querySelector('.toolbar-center');
        if (pageDisplay) {
            if (totalPages && totalPages < 999) {
                pageDisplay.innerHTML = '<span class="text-muted">Page ' + currentPage + ' of ' + totalPages + '</span>';
            } else {
                pageDisplay.innerHTML = '<span class="text-muted">Page ' + currentPage + '</span>';
            }
        }
    }

    function updateNavigationButtons() {
        console.log('🔄 Updating navigation buttons...');

        // Try multiple selectors to find the buttons
        let prevButton = document.querySelector('button[onclick*="goToPreviousPage"]');
        if (!prevButton) {
            prevButton = document.querySelector('button[onclick*="Previous"]');
        }
        if (!prevButton) {
            prevButton = document.querySelector('button:contains("Previous")');
        }

        let nextButton = document.querySelector('button[onclick*="goToNextPage"]');
        if (!nextButton) {
            nextButton = document.querySelector('button[onclick*="Next"]');
        }
        if (!nextButton) {
            nextButton = document.querySelector('button:contains("Next")');
        }

        console.log('🔍 Button detection:');
        console.log('Previous button found:', !!prevButton);
        console.log('Next button found:', !!nextButton);
        console.log('Current page:', currentPage, 'Total pages:', totalPages);

        // Previous button
        if (prevButton) {
            if (currentPage <= 1) {
                prevButton.disabled = true;
                prevButton.style.opacity = '0.5';
                prevButton.style.cursor = 'not-allowed';
                console.log('⬅️ Previous button DISABLED (on first page)');
            } else {
                prevButton.disabled = false;
                prevButton.style.opacity = '1';
                prevButton.style.cursor = 'pointer';
                console.log('⬅️ Previous button ENABLED');
            }
        } else {
            console.error('❌ Previous button not found!');
        }

        // Next button - only disable if we know total pages and we're on the last page
        if (nextButton) {
            if (totalPages && totalPages < 999 && currentPage >= totalPages) {
                nextButton.disabled = true;
                nextButton.style.opacity = '0.5';
                nextButton.style.cursor = 'not-allowed';
                console.log('➡️ Next button DISABLED (on last page)');
            } else {
                nextButton.disabled = false;
                nextButton.style.opacity = '1';
                nextButton.style.cursor = 'pointer';
                console.log('➡️ Next button ENABLED');
            }
        } else {
            console.error('❌ Next button not found!');
        }
    }

    // Simple page detection function
    function detectTotalPages() {
        const iframe = document.querySelector('.pdf-viewer');
        if (iframe && iframe.contentWindow) {
            try {
                const pdfApp = iframe.contentWindow.PDFViewerApplication;
                if (pdfApp) {
                    let detectedPages = null;

                    // Try different methods to get page count
                    if (pdfApp.pdfDocument && pdfApp.pdfDocument.numPages) {
                        detectedPages = pdfApp.pdfDocument.numPages;
                    } else if (pdfApp.pagesCount) {
                        detectedPages = pdfApp.pagesCount;
                    }

                    if (detectedPages && detectedPages > 0) {
                        totalPages = detectedPages;
                        console.log('✅ Total pages detected:', totalPages);
                        updatePageDisplay();
                        updateNavigationButtons();
                        return true;
                    }
                }
            } catch (e) {
                console.log('Page detection error:', e.message);
            }
        }
        return false;
    }

    // Initialize zoom level and setup
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 Document loaded, initializing PDF viewer...');
        console.log('📊 Initial values - Current page:', currentPage, 'Total pages:', totalPages, 'Document ID:', documentId);

        updateZoom();
        updatePageDisplay();
        updateNavigationButtons(); // Initialize button states

        // Test button detection
        setTimeout(function() {
            console.log('🔍 Testing button detection...');
            const prevBtn = document.querySelector('button[onclick*="goToPreviousPage"]');
            const nextBtn = document.querySelector('button[onclick*="goToNextPage"]');
            console.log('Previous button found:', !!prevBtn);
            console.log('Next button found:', !!nextBtn);

            if (prevBtn) {
                console.log('Previous button onclick:', prevBtn.getAttribute('onclick'));
            }
            if (nextBtn) {
                console.log('Next button onclick:', nextBtn.getAttribute('onclick'));
            }
        }, 1000);

        // Add keyboard shortcuts for the entire page
        document.addEventListener('keydown', function(e) {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                if (e.key === 'ArrowLeft' || e.key === 'PageUp') {
                    e.preventDefault();
                    console.log('⌨️ Left arrow key pressed');
                    goToPreviousPage();
                } else if (e.key === 'ArrowRight' || e.key === 'PageDown') {
                    e.preventDefault();
                    console.log('⌨️ Right arrow key pressed');
                    goToNextPage();
                }
            }
        });

        // Add visual feedback for navigation
        const iframe = document.querySelector('.pdf-viewer');
        if (iframe) {
            console.log('✅ PDF iframe found');
            iframe.onload = function() {
                console.log('📄 PDF loaded successfully');

                // Try to detect total pages after load
                setTimeout(function() {
                    console.log('🔍 Attempting page detection (2s delay)...');
                    detectTotalPages();
                }, 2000);

                // Try again after a longer delay
                setTimeout(function() {
                    console.log('🔍 Attempting page detection (5s delay)...');
                    detectTotalPages();
                }, 5000);
            };
        } else {
            console.error('❌ PDF iframe not found during initialization!');
        }
    });


</script>
