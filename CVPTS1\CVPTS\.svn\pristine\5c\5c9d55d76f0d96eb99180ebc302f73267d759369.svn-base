﻿@model BCM.BusinessClasses.DepartmentInfo

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewBag.Title = "Delete Department";
}

@* <form asp-action="DeleteDepartment" method="post">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">@Model.DepartmentName</span>" ?</span>
            </div>

            <div>
                <input type="hidden" asp-for="DepartmentID" />
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/deletevctor.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="submit" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</form> *@


<form asp-action="DeleteDepartment" method="post">
    <div>
        <input type="hidden" asp-for="DepartmentID" />
        <input type="hidden" asp-for="DepartmentName" />
    </div>
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    @* <div class="modal-header d-grid text-center">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.DepartmentName</span>" ?</span>
    </div> *@
    <div class="modal-body d-grid px">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="unitCodeSpan">@Model.DepartmentName</span>" ?</span>
    </div>
    <div class="modal-footer justify-content-center p-0">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>
