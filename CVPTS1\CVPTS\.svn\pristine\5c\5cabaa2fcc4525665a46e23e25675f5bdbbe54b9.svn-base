﻿
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@model BCM.BusinessClasses.DashboardTemplateEntity
@*

    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<form asp-action="EditDashboardTemplate" method="post">
    <div class="row row-cols-2">
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">Organization</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                    <select class="form-select form-select-sm" asp-for="OrgID">
                        <option value="value">All Organization</option>
                        @foreach (var organization in ViewBag.OrgInfo)
                        {
                            <option value="@organization.Value">@organization.Text</option>
                        }
                    </select>
                </div>
            </div>
          
        </div>
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">Template Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-entity-type"></i></span>
                    <input class="form-control" type="text" asp-for="TempName" placeholder="Template Name" />
                    <input type="hidden" asp-for="TempId" />
                </div>
            </div>

        </div>
        <div class="col-12">
            <div class="form-group">
                <label class="form-label">Template Description</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <input class="form-control" asp-for="TempDescription" type="text" placeholder="Template Description" />
                </div>
            </div>
        </div>
    </div>


    <div class="col d-grid">
        <div class="card">
            <div class="card-body p-0">
                <div class="mt-3">
                    <h6 class="Sub-Title">Dashboard Widgets</h6>

                    <div class="g-2 row row-cols-xl-3 row-cols-3 p-2  collapse show" id="collapseExample" style="max-height:250px;overflow:auto">
                        @await Html.PartialAsync("_WidgetList")
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>
