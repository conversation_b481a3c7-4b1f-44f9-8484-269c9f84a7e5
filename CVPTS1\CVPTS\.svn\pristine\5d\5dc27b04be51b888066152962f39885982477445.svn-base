﻿@model BCM.BusinessClasses.DepartmentInfo

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}

<form id="addDepartmentForm" asp-action="AddDepartment" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="modal-body pt-0">
        <div class="row row-cols-2">
            <div class="col">
                <div class="form-group">
                    <label for="validationCustom01" class="form-label">Department Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-department"></i></span>
                        <input type="text" class="form-control" required placeholder="Enter Department Name" asp-for="DepartmentName" id="DepartmentName">
                    </div>
                    <div class="invalid-feedback">Enter Department Name</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Head</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" required id="headlist" autocomplete="off" aria-label="Default select example" asp-for="DeptHeadID">
                            <option selected  value="0">-- All Resources --</option>
                            @foreach (var objResource in ViewBag.ResourcesInfo)
                            {
                                <option value="@objResource.Value">@objResource.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Select Head</div>
                    <div class="mt-2">
                        <table>
                            <tbody>
                                <tr>
                                    <div class="form-group">
                                        <label class="form-label">Email</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-mail"></i></span>
                                            <input type="text" readonly class="form-control" id="CompanyEmail" asp-for="HeadEmail">
                                        </div>
                                    </div>
                                <tr>
                                    <div class="form-group">
                                        <label class="form-label">Mobile</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-phone"></i></span>
                                            <input type="text" readonly class="form-control" id="mobilePhone" asp-for="HeadMobilePhone">
                                        </div>
                                    </div>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Unit Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-unit"></i></span>
                        <select id="unitlist" class="form-select form-control selectized unitlist" required autocomplete="off" aria-label="Default select example" asp-for="UnitID">
                            <option selected value="0">-- All Units --</option>
                            @foreach (var objUnit in ViewBag.OrgUnit)
                            {
                                <option value="@objUnit.Value">@objUnit.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Enter Unit Name</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Alt Head</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-user"></i></span>
                        <select class="form-select form-control selectized" required id="altheadlist" autocomplete="off" aria-label="Default select example" asp-for="DeptAltHeadID">
                            <option selected  value="0">-- All Resources --</option>
                            @foreach (var objResource in ViewBag.ResourcesInfo)
                            {
                                <option value="@objResource.Value">@objResource.Text</option>
                            }
                        </select>
                    </div>
                    <div class="invalid-feedback">Select Alt Head</div>
                    <div class="mt-2">
                        <table>
                            <tbody>
                                <tr>
                                    <div class="form-group">
                                        <label class="form-label">Email</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-mail"></i></span>
                                            <input type="text" readonly class="form-control" id="altcompanyEmail" asp-for="AltHeadEmail">
                                        </div>
                                    </div>
                                </tr>
                                <tr>
                                    <div class="form-group">
                                        <label class="form-label">Mobile</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-phone"></i></span>
                                            <input type="text" readonly class="form-control" id="altmobilePhone" asp-for="AltHeadMobilePhone">
                                        </div>
                                    </div>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>




<script>
    $(document).ready(function () {
        console.log("=== DEPARTMENT FORM INITIALIZING ===");

        // Department Form Validation - Clean Implementation
        window.DepartmentForm = {
            init: function() {
                console.log("Initializing Department Form...");
                this.setupFormValidation();
                this.setupDropdownHandlers();
                this.setupDepartmentNameValidation();
                this.setupUnitChangeHandler();
                console.log("Department Form initialized successfully");
            },

            // Check if department name exists in selected unit
            checkDepartmentName: function(departmentName, unitId, departmentId, callback) {
                console.log("=== CHECKING DEPARTMENT NAME ===");
                console.log("Department Name:", departmentName);
                console.log("Unit ID:", unitId);
                console.log("Department ID:", departmentId);

                var checkUrl = '@Url.Action("CheckDepartmentNameExists", "Department", new { area = "BCMOrgStructure" })';
                console.log("AJAX URL:", checkUrl);

                $.ajax({
                    url: checkUrl,
                    type: 'GET',
                    data: {
                        departmentName: departmentName,
                        unitId: unitId,
                        departmentId: departmentId
                    },
                    timeout: 10000,
                    success: function(response) {
                        console.log("=== AJAX SUCCESS ===");
                        console.log("Response:", response);
                        callback(response && response.exists === true);
                    },
                    error: function(xhr, status, error) {
                        console.log("=== AJAX ERROR ===");
                        console.error("Status:", status);
                        console.error("Error:", error);
                        console.error("Response Text:", xhr.responseText);
                        alert("Error checking department name. Please try again.");
                        callback(false); // Assume unique on error
                    }
                });
            },

            // Setup form validation and submission
            setupFormValidation: function() {
                var self = this;
                var form = document.getElementById('addDepartmentForm');

                // Initialize BCM Validation if available
                if (typeof window.BCMValidation !== 'undefined') {
                    try {
                        window.BCMValidation.init();
                        window.BCMValidation.addRequiredFieldIndicators(form);
                        window.BCMValidation.addFormatIndicators(form);
                    } catch (e) {
                        console.error("BCM Validation error:", e);
                    }
                }

                // Form submission handler
                $('#addDepartmentForm').on('submit', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var departmentName = $('#DepartmentName').val().trim();
                    var unitId = self.getUnitId();
                    var departmentId = 0; // For add mode

                    console.log("Form Submit - Department Name:", departmentName);
                    console.log("Form Submit - Unit ID:", unitId);

                    // Check department name if both name and unit are provided
                    if (departmentName && departmentName.length >= 2 && unitId && unitId !== "0") {
                        self.checkDepartmentName(departmentName, unitId, departmentId, function(exists) {
                            if (exists) {
                                alert("This Department Name already exists in the selected unit. Please choose another name.");
                                $('#DepartmentName').focus();
                            } else {
                                self.validateAndSubmit(form);
                            }
                        });
                    } else {
                        self.validateAndSubmit(form);
                    }
                });
            },

            // Validate and submit form
            validateAndSubmit: function(form) {
                var self = this;

                // Use BCM validation if available
                if (typeof window.BCMValidation !== 'undefined') {
                    try {
                        window.BCMValidation.showAllValidationMessages(form);
                        var isValid = window.BCMValidation.validateForm(form);

                        // Also validate that head and alt head are different
                        var rolesValid = self.validateRolesDifferent();

                        if (!isValid || !rolesValid) {
                            var firstInvalid = form.querySelector('.is-invalid');
                            if (firstInvalid) {
                                firstInvalid.focus();
                            }
                            return;
                        }
                    } catch (e) {
                        console.error("BCM Validation error:", e);
                        // Fall back to HTML5 validation
                        if (!form.checkValidity()) {
                            form.reportValidity();
                            return;
                        }
                    }
                } else {
                    // Fallback to HTML5 validation
                    if (!form.checkValidity()) {
                        form.reportValidity();
                        return;
                    }

                    // Also validate roles for HTML5 fallback
                    if (!self.validateRolesDifferent()) {
                        return;
                    }
                }

                // All validation passed - submit form via AJAX to handle success/error toasts
                self.submitFormWithToast(form);
            },

            // Submit form with AJAX to show success/error toasts
            submitFormWithToast: function(form) {
                var formData = new FormData(form);

                $.ajax({
                    url: form.action,
                    type: form.method,
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // Show success toast
                        if (window.ToastManager) {
                            window.ToastManager.showSuccess('Department added successfully!');
                        }

                        // Close modal if it exists
                        var modal = $('#addDepartmentForm').closest('.modal');
                        if (modal.length > 0) {
                            modal.modal('hide');
                        }

                        // Refresh the page or reload data table if needed
                        setTimeout(function() {
                            if (typeof window.location !== 'undefined') {
                                window.location.reload();
                            }
                        }, 1500);
                    },
                    error: function(xhr, status, error) {
                        // Show error toast
                        if (window.ToastManager) {
                            var errorMessage = 'Failed to add department. Please try again.';

                            // Try to get specific error message from response
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            } else if (xhr.responseText) {
                                try {
                                    var errorData = JSON.parse(xhr.responseText);
                                    if (errorData.message) {
                                        errorMessage = errorData.message;
                                    }
                                } catch (e) {
                                    // Use default error message
                                }
                            }

                            window.ToastManager.showError(errorMessage);
                        }
                    }
                });
            },

            // Setup dropdown handlers for head and alt head
            setupDropdownHandlers: function() {
                var self = this;

                $('#headlist,#altheadlist').change(function () {
                    var selectedDDL = $(this).attr('id');
                    var iId = $(this).val();

                    // Load resource details
                    $.ajax({
                        url: '@Url.Action("GetResourceDetails", "Department")',
                        type: "GET",
                        data: { iId: iId },
                        success: function (data) {
                            if (data) {
                                if (selectedDDL == "headlist") {
                                    $("#CompanyEmail").val(data.mail);
                                    $("#mobilePhone").val(data.mobile);
                                } else if (selectedDDL == "altheadlist") {
                                    $("#altcompanyEmail").val(data.mail);
                                    $("#altmobilePhone").val(data.mobile);
                                }
                            }
                        },
                        error: function (data) {
                            console.log('Error loading resource details');
                        }
                    });

                    // Clear validation state when user makes a selection
                    var $this = $(this);
                    if ($this.val() && $this.val() !== "" && $this.val() !== "0") {
                        $this.removeClass('is-invalid');
                        var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                        if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be the same person')) {
                            feedbackElement.removeClass('custom-validation show').hide();
                        }
                    }

                    // Small delay to allow the change to complete, then validate roles
                    setTimeout(function() {
                        self.validateRolesDifferent();
                    }, 100);
                });

                // Handle selectized dropdown changes for role validation
                setTimeout(function() {
                    if ($('#headlist')[0] && $('#headlist')[0].selectize) {
                        $('#headlist')[0].selectize.on('change', function(value) {
                            setTimeout(function() {
                                self.validateRolesDifferent();
                            }, 100);
                        });
                    }

                    if ($('#altheadlist')[0] && $('#altheadlist')[0].selectize) {
                        $('#altheadlist')[0].selectize.on('change', function(value) {
                            setTimeout(function() {
                                self.validateRolesDifferent();
                            }, 100);
                        });
                    }
                }, 500);
            },

            // Helper function to get the actual unit ID value
            getUnitId: function() {
                // Try to get from the original select first
                var unitId = $('.unitlist').val();

                // If selectized is being used and original is 0, try to get from selectized
                if ((!unitId || unitId === "0") && $('.unitlist')[0] && $('.unitlist')[0].selectize) {
                    unitId = $('.unitlist')[0].selectize.getValue();
                }

                console.log("Getting Unit ID - Original:", $('.unitlist').val(), "Selectized:", unitId);
                return unitId;
            },

            // Setup real-time department name validation
            setupDepartmentNameValidation: function() {
                console.log("Setting up department name validation...");
                var self = this;
                var departmentNameTimeout;

                $('#DepartmentName').on('input keyup paste', function() {
                    console.log("=== DEPARTMENT NAME INPUT EVENT ===");
                    var departmentName = $(this).val().trim();
                    var unitId = self.getUnitId();
                    var departmentId = 0; // For add mode

                    console.log("Input - Department Name:", departmentName);
                    console.log("Input - Unit ID:", unitId);

                    clearTimeout(departmentNameTimeout);

                    // Clear existing validation state
                    var input = $(this);
                    var feedback = input.closest('.form-group').find('.invalid-feedback');
                    input.removeClass('is-invalid');
                    input.closest('.input-group').removeClass('is-invalid');
                    feedback.hide();

                    // Validate if both name and unit are provided
                    if (departmentName.length >= 2 && unitId && unitId !== "0") {
                        console.log("Triggering validation timeout...");
                        departmentNameTimeout = setTimeout(function() {
                            console.log("Timeout triggered - calling checkDepartmentName");
                            self.checkDepartmentName(departmentName, unitId, departmentId, function(exists) {
                                console.log("Validation callback - exists:", exists);
                                if (exists) {
                                    input.addClass('is-invalid');
                                    input.closest('.input-group').addClass('is-invalid');
                                    feedback.text('This Department Name already exists in the selected unit. Please choose another.').show();
                                } else {
                                    // Clear error if no other validation issues
                                    if (input[0].checkValidity()) {
                                        input.removeClass('is-invalid');
                                        input.closest('.input-group').removeClass('is-invalid');
                                        feedback.hide();
                                    }
                                }
                            });
                        }, 500);
                    } else {
                        console.log("Validation conditions not met - Name length:", departmentName.length, "Unit ID:", unitId);
                    }
                });
            },

            // Setup unit change handler to trigger department name validation
            setupUnitChangeHandler: function() {
                console.log("Setting up unit change handler...");
                var self = this;

                // Handle both regular select and selectized dropdown
                $('.unitlist').on('change', function() {
                    console.log("=== UNIT DROPDOWN CHANGED ===");
                    var departmentName = $('#DepartmentName').val().trim();
                    var unitId = self.getUnitId();

                    console.log("Unit Change - Department Name:", departmentName);
                    console.log("Unit Change - Unit ID:", unitId);

                    // Clear existing validation state
                    var input = $('#DepartmentName');
                    var feedback = input.closest('.form-group').find('.invalid-feedback');
                    input.removeClass('is-invalid');
                    input.closest('.input-group').removeClass('is-invalid');
                    feedback.hide();

                    // Validate if both name and unit are provided
                    if (departmentName.length >= 2 && unitId && unitId !== "0") {
                        console.log("Unit change validation triggered");
                        setTimeout(function() {
                            self.checkDepartmentName(departmentName, unitId, 0, function(exists) {
                                console.log("Unit change validation result:", exists);
                                if (exists) {
                                    input.addClass('is-invalid');
                                    input.closest('.input-group').addClass('is-invalid');
                                    feedback.text('This Department Name already exists in the selected unit. Please choose another.').show();
                                }
                            });
                        }, 100);
                    } else {
                        console.log("Unit change validation conditions not met");
                    }
                });

                // Handle selectized dropdown changes
                // Wait for selectize to be initialized
                setTimeout(function() {
                    if ($('.unitlist')[0] && $('.unitlist')[0].selectize) {
                        $('.unitlist')[0].selectize.on('change', function(value) {
                            console.log("=== SELECTIZED UNIT CHANGED ===", value);
                            // Trigger the regular change event
                            $('.unitlist').trigger('change');
                        });
                    }
                }, 500);
            },

            // Function to validate that head and alt head are different people
            validateRolesDifferent: function() {
                var headID = this.getSelectValue('#headlist');
                var altHeadID = this.getSelectValue('#altheadlist');

                var isValid = true;

                // Clear previous error states for role validation
                this.clearRoleValidationErrors();

                // Validate Head vs Alt Head
                if (headID && altHeadID && headID !== "" && headID !== "0" && altHeadID !== "" && altHeadID !== "0" && headID === altHeadID) {
                    this.showRoleValidationError('#headlist', 'Head and Alternate Head cannot be the same person');
                    this.showRoleValidationError('#altheadlist', 'Head and Alternate Head cannot be the same person');
                    isValid = false;
                }

                return isValid;
            },

            // Helper function to get select value (handles both regular and selectized)
            getSelectValue: function(selector) {
                var element = $(selector);
                var value = element.val();

                // If selectized is being used and original is 0, try to get from selectized
                if ((!value || value === "0") && element[0] && element[0].selectize) {
                    value = element[0].selectize.getValue();
                }

                return value;
            },

            // Function to show role validation error
            showRoleValidationError: function(selector, message) {
                var element = $(selector);
                element.addClass('is-invalid').removeClass('is-valid');

                var formGroup = element.closest('.form-group');
                var feedbackElement = formGroup.find('.invalid-feedback');
                if (feedbackElement.length > 0) {
                    feedbackElement.text(message);
                    feedbackElement.addClass('custom-validation show').show();
                }
            },

            // Function to clear role validation errors
            clearRoleValidationErrors: function() {
                var roleSelectors = ['#headlist', '#altheadlist'];

                roleSelectors.forEach(function(selector) {
                    var element = $(selector);
                    var formGroup = element.closest('.form-group');
                    var feedbackElement = formGroup.find('.invalid-feedback');

                    // Only clear if the current message is a role validation error
                    if (feedbackElement.length > 0 && feedbackElement.text().includes('cannot be the same person')) {
                        element.removeClass('is-invalid');
                        feedbackElement.removeClass('custom-validation show').hide();

                        // Restore original message
                        var originalMessages = {
                            '#headlist': 'Select Head',
                            '#altheadlist': 'Select Alt Head'
                        };

                        if (originalMessages[selector]) {
                            feedbackElement.text(originalMessages[selector]);
                        }
                    }
                });
            }
        };

        // Toast Implementation using existing #liveToast from layout
        window.ToastManager = {
            // Show success toast
            showSuccess: function(message) {
                this.showToast(message, 'success');
            },

            // Show error toast
            showError: function(message) {
                this.showToast(message, 'danger');
            },

            // Show warning toast
            showWarning: function(message) {
                this.showToast(message, 'warning');
            },

            // Show info toast
            showInfo: function(message) {
                this.showToast(message, 'info');
            },

            // Generic toast show function using existing #liveToast
            showToast: function(message, type) {
                console.log("Showing toast:", message, "Type:", type);

                // Update toast message using the existing structure
                $('#liveToast .toast-body .d-flex span:last-child').text(message);

                // Update toast color
                const toastElement = $('#liveToast');
                toastElement.removeClass('bg-success bg-warning bg-danger bg-info');
                toastElement.addClass('bg-' + type);

                // Show the toast using existing #liveToast
                const toastLiveExample = document.getElementById('liveToast');
                if (toastLiveExample) {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                    toastBootstrap.show();
                } else {
                    console.error("liveToast element not found in layout");
                }
            },

            // Test function to verify toasts are working
            test: function() {
                console.log("Testing toasts...");
                this.showSuccess('Test success message');
                setTimeout(() => this.showError('Test error message'), 1500);
                setTimeout(() => this.showWarning('Test warning message'), 3000);
                setTimeout(() => this.showInfo('Test info message'), 4500);
            }
        };

        // Wait for DOM and other scripts to load
        setTimeout(function() {
            console.log("Initializing Department Form after delay...");
            window.DepartmentForm.init();
        }, 100);
    });
</script>