﻿@model IEnumerable<BCM.BusinessClasses.Attachments>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    ViewBag.Title = "BCMPolicy";
    Layout = "~/Views/Shared/_Layout.cshtml";

    var selectedOrgID = ViewBag.selectedOrgID;
}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">BCM Policy</h6>
    <div class="d-flex gap-2 justify-content-end align-items-end" style="width:85%;">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <div class="dropdown">
            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                <i class="cv-filter align-middle" title="View Filter"></i>
            </button>
            <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                <div class="mb-3">
                    <label>Organizations</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                        <select class="form-select form-control" aria-label="Default select example" id="ddlOrg">
                            <option value="0" selected>All Organizations</option>
                            @{
                                foreach (var objOrg in ViewBag.OrgName)
                                {
                                    <!option value="@objOrg.Value" @(objOrg.Value == selectedOrgID.ToString() ? "selected=\"selected=\"" : "")>@objOrg.Text</!option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Units</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                        <select class="form-select form-control" id="ddlUnit" aria-label="Default select example">
                            <option value="0" selected>All Units</option>
                            @{
                                foreach (var objUnits in ViewBag.Unit)
                                {
                                    <option value="@objUnits.Value">@objUnits.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Departments</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-department"></i></span>
                        <select class="form-control" id="ddlDepartment" aria-label="Default select example">
                            <option value="0" selected>All Departments</option>
                            @{
                                foreach (var objDepartment in ViewBag.Department)
                                {
                                    <option value="@objDepartment.Value">@objDepartment.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label>Sub Departments</label>
                    <div class="input-group w-30">
                        <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
                        <select class="form-control" id="ddlSubDepartment" aria-label="Default select example">
                            <option value="0" selected>All Sub Departments</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
        <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"> <i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant card border-0" id="">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Doc&nbsp;Code</th>
                <th>Attchment&nbsp;Name</th>
                <th>Description</th>
                <th>Owner&nbsp;Name</th>
                <th>Create/Update</th>
                <th>Approver</th>
                <th>Org&nbsp;Level</th>
                <th>Status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="tblBody">
            @if (Model!=null)
            {
                int iIndex = 0;
                foreach(var item in Model)
                {
                    iIndex++;
                    <tr>
                        <td>@iIndex</td>
                        <td>@item.DocCode</td>
                        <td class="truncate">
                            @item.AttchmentName <br />
                            <span class="text-secondary">Version  : <span class="text-dark"> @item.version</span></span>
                        </td>
                        <td>@item.Description</td>
                        <td>
                            <table>
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                        <td> : </td>
                                        <td>@item.username</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                                        <td>:</td>
                                        <td>@item.Usermobile</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                                        <td>:</td>
                                        <td class="truncate">@item.usermail</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    <tr title="RTO">
                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                        <td> : </td>
                                        <td>
                                            @item.CreaterName
                                        </td>
                                    </tr>
                                    <tr title="User MTPOD">
                                        <td class="fw-semibold"><i class="cv-calendar"></i> </td>
                                        <td>:</td>
                                        <td>@item.CreatedDate</td>
                                    </tr>
                                    <tr title="Calculated RTO">
                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                        <td> : </td>
                                        <td>
                                            @item.UpdaterName
                                        </td>
                                    </tr>
                                    <tr title="Calculated MTPOD">
                                        <td class="fw-semibold"><i class="cv-calendar"></i> </td>
                                        <td>:</td>
                                        <td>@item.UpdatedDate</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                        <td> : </td>
                                        <td> @item.ApproverName</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                                        <td>:</td>
                                        <td>@item.App_Usermobile</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                                        <td>:</td>
                                        <td class="truncate">@item.App_usermail</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    <tr title="Org Name">
                                        <td><i class="cv-organization"></i></td>
                                        <td> : </td>
                                        <td>@item.OrgName</td>
                                    </tr>
                                    <tr title="Unit ">
                                        <td><i class="cv-unit"></i></td>
                                        <td>:</td>
                                        <td>@item.UnitName</td>
                                    </tr>
                                    <tr title="Department">
                                        <td><i class="cv-department"></i></td>
                                        <td>:</td>
                                        <td>@item.DepartmentName</td>
                                    </tr>
                                    <tr title="Sub Department">
                                        <td><i class="cv-subdepartment"></i></td>
                                        <td>:</td>
                                        <td>@item.SubFunctionName</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <span class="text-danger">@item.Status </span>
                        </td>
                        <td>
                            <span class="btn-action" type="button"><i class="cv-edit me-1 btnEdit" data-id="@item.AttachmentId"></i></span>
                            <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                        </td>
                    </tr>
                }
            }
        </tbody>
    </table>
</div>


<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">BCM Policy Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-data" id="createBody">

            </div>
            <div class="modal-footer d-flex justify-content-between d-none">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-primary btn-sm me-1" data-bs-dismiss="modal">End Review</button>
                    <button type="submit" class="btn btn-primary btn-sm">Show History</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!--Notify Configuration Modal -->
<div class="modal fade" id="NotifyModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Notify Teams Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Org Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-organization"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Perpetuuiti</option>
                                    <option value="value">Perpetuuiti</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Notification Type</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notfication"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Select</option>

                                </select>
                            </div>
                        </div>

                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Unit Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-unit"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Admin Groups</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Incident to Notify</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notify-incident"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Select</option>
                                </select>
                            </div>
                        </div>

                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Team Notification Subject</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notfication"></i></span>
                                <textarea class="form-control" placeholder="Team Notification Subject" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Team Notification Message</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-notify-team"></i></span>
                                <textarea class="form-control" placeholder="Team Notification Message" style="height:0px"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-group d-flex align-items-center">
                                <span class="form-label mb-0"><i class="cv-user me-1"></i>User Response Required</span>

                                <input type="checkbox" class="form-check  ms-2" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Accept User Response for</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-user"></i></span>
                                <input type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group">
                            @* <label class="form-label">Accept User Response for</label> *@
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-file-size"></i></span>
                                <input type="file" class="form-control" />
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary btn-sm me-1">Filter</button>
                    <button type="submit" class="btn btn-primary btn-sm">Add Team</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Notify Configuration Modal -->
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-header p-0">
                <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->



@section Scripts {
    <script>
        $(document).ready(function () {

            $('#btnCreate').click(function(){
                //$.get('/BCMDocuments/BCMPolicy/AddOtherBCMDocument', function (data) {
                $.get('@Url.Action("AddOtherBCMDocument", "BCMPolicy")', function (data) {
                    $('#createBody').html(data);
                    $('#CreateModal').modal('show');
                })
            })

            // $('.btnEdit').click(function () {
            //     var iDocumentId = $(this).data('id');
            //     $.get('/BCMDocuments/BCMPolicy/EditOtherBCMDocument/', { iDocumentId: iDocumentId }, function (data) {
            //         $('#createBody').html(data);
            //         $('#CreateModal').modal('show');
            //     })
            // })

            // Function to call FilteredBCMPolicies with current dropdown values
            function callFilteredBCMPolicies() {
                var iOrgID = $('#ddlOrg').val();
                var iUnitID = $('#ddlUnit').val();
                var iDepartmentID = $('#ddlDepartment').val();
                var iSubDepartmentID = $('#ddlSubDepartment').val();

                $.get('@Url.Action("FilteredBCMPolicies", "BCMPolicy", new { area = "BCMDocuments" })',
                    { iOrgID: iOrgID, iUnitID: iUnitID, iDepartmentID: iDepartmentID, iSubDepartmentID: iSubDepartmentID },
                    function(data){
                        var tblData = $('#tblBody');
                        tblData.empty();
                        $('#tblBody').html(data);
                    });
            }

            // Handle unit change to populate departments
            $(document).on('change', '#ddlOrg,#ddlUnit, #ddlDepartment, #ddlSubDepartment', function () {
                // var iUnitID = $(this).val();
                var selectedDDL = $(this).attr('id');
                var iOrgID = $('#ddlOrg').val();
                var iUnitID = $('#ddlUnit').val();
                var iDepartmentID = $('#ddlDepartment').val();
                var iSubDepartmentID = $('#ddlSubDepartment').val();

                if(selectedDDL == "ddlOrg"){
                    callFilteredBCMPolicies();
                }
                if(selectedDDL == "ddlUnit"){
                    BindDepartments(iUnitID);
                    callFilteredBCMPolicies();
                }
                if(selectedDDL == "ddlDepartment"){
                    BindSubDepartments(iDepartmentID);
                    callFilteredBCMPolicies();
                }
                if(selectedDDL == "ddlSubDepartment"){
                    callFilteredBCMPolicies();
                }

                // if (iUnitID) {
                //     $.ajax({
                //         url:'@Url.Action("GetAllDepartments", "BCMPolicy", new { area = "BCMDocuments" })',
                //         type: 'GET',
                //         data: { iUnitID: iUnitID },
                //         success: function (data) {
                //             var department = null
                //             if (selectedDDL == "ddlUnit") {
                //                 department = $('#ddlDepartment');
                //             }
                //             else {
                //                 department = $('#ddlDepartmentPartial');
                //             }
                //             department.empty();
                //             department.append('<option value="0" selected>All Departments</option>');
                //             $.each(data, function (index, item) {
                //                 department.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                //             });

                //             // Call filter function after populating departments
                //             callFilteredBCMPolicies();
                //         },
                //         error: function (xhr, status, error) {
                //             console.log(error);
                //             console.error(xhr.status);
                //             console.error(xhr.responseText);
                //         }
                //     });
                // }
            });

            function BindDepartments(iUnitID){
                $.ajax({
                    url:'@Url.Action("GetAllDepartments", "BCMPolicy", new { area = "BCMDocuments" })',
                    type: 'GET',
                    data: { iUnitID: iUnitID },
                    success: function (data) {
                        var department = $('#ddlDepartment');                        
                        department.empty();
                        department.append('<option value="0" selected>All Departments</option>');
                        $.each(data, function (index, item) {
                            department.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                        });
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            }

            function BindSubDepartments(iDepartmentID){
                $.ajax({
                    url:'@Url.Action("GetAllSubDepartments", "BCMPolicy", new { area = "BCMDocuments" })',
                    type: 'GET',
                    data: { iDepartmentID: iDepartmentID },
                    success: function (data) {
                        var subDepartment = $('#ddlSubDepartment');
                        subDepartment.empty();
                        subDepartment.append('<option value="0" selected>All Sub Departments</option>');
                        $.each(data, function (index, item) {
                            subDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>')
                        });
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            }

            // Handle changes on other dropdowns
            $(document).on('change', '#ddlOrg,#ddlUnit, #ddlDepartment, #ddlSubDepartment', function() {
                // callFilteredBCMPolicies();
            });
        });
    </script>
}