@using BCM.UI.Areas.BCMReports
@using BCM.UI.Areas.BCMReports.ReportTemplate
@using DevExpress.AspNetCore

@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">BIA Report </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
            <select id="orglist" class="form-select form-control" autocomplete="off" aria-label="Default select example">
                <option selected value="0">-- All Organizations --</option>
                @foreach (var organization in ViewBag.OrgInfo)
                {
                    <option value="@organization.Value">@organization.Text</option>
                }
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
            <select id="unitlist" class="form-select form-control" autocomplete="off" aria-label="Default select example">
                <option selected value="0">-- All Units --</option>
                @foreach (var objUnit in ViewBag.OrgUnit)
                {
                    <option value="@objUnit.Value">@objUnit.Text</option>
                }
            </select>
        </div>
        <div class="input-group w-30">
            <select id="departmentlist" class="form-select form-control" autocomplete="off" aria-label="Default select example">
                <option selected value="0">-- All Departments --</option>
                @foreach (var objDepartment in ViewBag.DepartmentInfo)
                {
                    <option value="@objDepartment.Value">@objDepartment.Text</option>
                }
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
            <select id="subdepartmentlist" class="form-select form-control" autocomplete="off" aria-label="Default select example">
                <option selected value="0">-- All Sub Departments --</option>
                @foreach (var objSubDepartment in ViewBag.SubFunction)
                {
                    <option value="@objSubDepartment.Value">@objSubDepartment.Text</option>
                }
            </select>
        </div>
        <div class="input-group Search-Input1">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        
        <button type="button" id="btnViewReport" class="btn icon-btn btn-primary btn-sm">
            <i class="cv-view_report"></i> View Report
        </button>
    </div>
</div>

<div id="partialContainer">
    @if (Model != null)
    {
        @await Html.PartialAsync("_BIAReport", (BIAReport)Model)
    }
    else
    {
        <div class="alert alert-info mt-4">
            <p>Please select a department and click "View Report" to generate the BIA report.</p>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Function to load report with selected filters
            function loadReport() {
                var iOrgId = $('#orglist').val();
                var iUnitId = $('#unitlist').val();
                var iDepartmentId = $('#departmentlist').val();
                var iSubDeptId = $('#subdepartmentlist').val();
                
                if (iDepartmentId > 0) { // Department is required
                    $.ajax({
                        url: '@Url.Action("BIAReport", "ViewBIAReport")',
                        type: 'GET',
                        data: { 
                            iDepartmentId: iDepartmentId,
                            iOrgId: iOrgId,
                            iUnitId: iUnitId,
                            iSubDeptId: iSubDeptId
                        },
                        success: function (data) {
                            $('#partialContainer').html(data);
                        },
                        error: function (xhr, status, error) {
                            console.error(error);
                        }
                    });
                }
            }
            
            // Bind change events to all dropdowns
            $('#departmentlist').change(loadReport);
            $('#orglist').change(loadReport);
            $('#unitlist').change(loadReport);
            $('#subdepartmentlist').change(loadReport);
            
            // Bind click event to the view report button
            $('#btnViewReport').click(loadReport);
            
            // Handle form submissions
            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        location.reload();
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    </script>
}
