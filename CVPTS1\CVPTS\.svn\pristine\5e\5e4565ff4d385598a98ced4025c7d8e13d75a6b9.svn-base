﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Security.Helper;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc;
using static BCM.Shared.Common;
using static BCM.Shared.BCPEnum;

using Microsoft.AspNetCore.Mvc.Rendering;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Xml;
using Microsoft.AspNetCore.Http;
using Microsoft.VisualStudio.Web.CodeGenerators.Mvc.Templates.Blazor;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Hosting.Server;
using System.Diagnostics;
using Google.Protobuf.WellKnownTypes;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMProcessBIA.Controllers;
[Area("BCMProcessBIA")]
public class BusinessProcessFormController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly CVLogger _CvLogger;
    readonly Utilities _Utilities;
    private int iEntityTypeID = 0;
    private int iRecordID = 0;
    private int iprocessID = 0;
    private bool IsReviewPending = false;
    int iProcessID = 0;
    int iETime, iEUnit;
    int icount = 0;
    DateTime dvalue;
    private readonly BCMMail _BCMMail;

    public BusinessProcessFormController(ProcessSrv iProcessSrv, Utilities Utilities, CVLogger cVLogger, BCMMail BCMMail) : base(Utilities)
    {
        _ProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _CvLogger = cVLogger;
        _BCMMail = BCMMail;
    }

    public IActionResult BusinessProcessForm(string strRecordID, string strEntityTypeID, string strProcessID)
    {

        //if (Convert.ToInt32(strIsActionMethod) != 1)
        //{



        try
        {
            strRecordID = CryptographyHelper.Decrypt(strRecordID.ToString());
            strEntityTypeID = CryptographyHelper.Decrypt(strEntityTypeID.ToString());
            strProcessID = CryptographyHelper.Decrypt(strProcessID.ToString());
            iRecordID = Convert.ToInt32(strRecordID);
            iEntityTypeID = Convert.ToInt32(strEntityTypeID);
            iprocessID = Convert.ToInt32(strProcessID);


            HttpContext.Session.SetString("strRecordID", strRecordID);
            HttpContext.Session.SetString("strEntityTypeID", strEntityTypeID);

            HttpContext.Session.SetString("IsReviewPending", IsReviewPending.ToString());

            if (strProcessID == "0")
            {
                iprocessID = Convert.ToInt32(HttpContext.Session.GetString("iProcessID"));
            }
            else
            {
                HttpContext.Session.SetString("iProcessID", strProcessID.ToString());
            }
            //}
            //PopulateEntities(iEntityTypeID, _UserDetails.OrgID);
            BusinessProcessInfoAndReviewHistory objBusinessProcess = new BusinessProcessInfoAndReviewHistory();

            BusinessProcessInfoAndReviewHistory objBusinessProcData = new BusinessProcessInfoAndReviewHistory();
            List<EntityReview> ObjEntityReviewColl = new List<EntityReview>();
            ObjEntityReviewColl = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(strEntityTypeID, strRecordID);

            if (ObjEntityReviewColl.Count > 3)
            {
                ViewBag.ShowHsitoryVisible = "Show";
            }
            else
            {
                ViewBag.ShowHsitoryVisible = "Hide";
            }

            ObjEntityReviewColl = ObjEntityReviewColl.OrderBy(o => o.Status).Take(3).ToList();
            List<EntityReview> EntityReview = ObjEntityReviewColl.Where(x => x.Status.ToString() == "1").ToList();
            ViewBag.ReviewHistory = ObjEntityReviewColl;
            if (EntityReview.Count > 0)
            {
                ViewBag.startReview = "1";
                IsReviewPending = false;
                HttpContext.Session.SetString("IsReviewPending", IsReviewPending.ToString());

            }
            else
            {
                ViewBag.startReview = "0";
                HttpContext.Session.SetString("IsReviewPending", "true");
            }
            BusinessProcessInfo BusinessProcessInfo = new BusinessProcessInfo();
            objBusinessProcData.EntityReview = ObjEntityReviewColl;
            objBusinessProcess.EntityReview = ObjEntityReviewColl;

            TempData["RecordID"] = strRecordID;
            PopulateDropdowns();

            if (Convert.ToInt32(strProcessID) == 0)
            {
                objBusinessProcess = DisplayData(strEntityTypeID);
                //ViewBag.lstOrg = _Utilities.BindOrg(objBusinessProcess.BusinessProcessInfo.OrgGroupID);
                PopulateDropDown(objBusinessProcess.BusinessProcessInfo);
                PopulateEntities(iEntityTypeID, _UserDetails.OrgID, objBusinessProcess);
                if (objBusinessProcess == null)
                {
                    return NotFound("No Records Found.");
                }
                objBusinessProcess.ButtonAcces = _Utilities.ShowButtonsByAccess((objBusinessProcess.BusinessProcessInfo.Status).ToString(), objBusinessProcess.BusinessProcessInfo.ApproverID.ToString(), objBusinessProcess.BusinessProcessInfo.ProcessOwnerID.ToString(),
                                                        objBusinessProcess.BusinessProcessInfo.AltProcessOwnerID.ToString(), _UserDetails.UserID.ToString(),
                                                        Convert.ToInt32(BCPEnum.PrivilegeID.Create));
                objBusinessProcess.ButtonAcces.btnUpdate = "";
                //ViewBag.ButtonText = "Add To BCM";

            }
            else
            {
                BusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(strProcessID), 1);
                objBusinessProcess.BusinessProcessInfo = BusinessProcessInfo;
                strProcessID = BusinessProcessInfo.ProcessID.ToString();
                HttpContext.Session.SetString("iProcessID", iProcessID.ToString());
                //BindOrg(objBusinessProcess.BusinessProcessInfo);
                PopulateDropDown(BusinessProcessInfo);
                PopulateEntities(iEntityTypeID, _UserDetails.OrgID, objBusinessProcess);
                objBusinessProcess.ButtonAcces = _Utilities.ShowButtonsByAccess((objBusinessProcess.BusinessProcessInfo.Status).ToString(), objBusinessProcess.BusinessProcessInfo.ApproverID.ToString(), objBusinessProcess.BusinessProcessInfo.ProcessOwnerID.ToString(),
                                                        objBusinessProcess.BusinessProcessInfo.AltProcessOwnerID.ToString(), _UserDetails.UserID.ToString(),
                                                        Convert.ToInt32(BCPEnum.PrivilegeID.Create));
                //ViewBag.ButtonText = "Update";
            }
            if (strProcessID == "0")
            {
                ViewBag.ButtonText = "Add To BCM";
            }
            else
            {
                ViewBag.ButtonText = "Update";
            }
            //Default profile ID;
            objBusinessProcess.BusinessProcessInfo.ProfileID = 1;





            return View("BusinessProcessForm", objBusinessProcess);
        }
        catch (Exception ex)
        {

            _CvLogger.LogErrorApp(ex);
            return NotFound("No Records Found.");
        }
    }

    public void PopulateDropDown(BusinessProcessInfo businessProcessInfo)
    {
        ViewBag.lstOrg = _Utilities.BindOrg(businessProcessInfo.OrgGroupID);
        ViewBag.lstUnit = _Utilities.BindUnit(businessProcessInfo.OrgID);
        ViewBag.lstDepartment = _Utilities.BindFunction(businessProcessInfo.UnitID);
        ViewBag.lstSubDepartment = _Utilities.BindSubFunction(businessProcessInfo.DepartmentID);
    }

    public BusinessProcessInfoAndReviewHistory DisplayData(string strEntityTypeID)
    {
        BusinessProcessInfoAndReviewHistory objBusinessProcess = new BusinessProcessInfoAndReviewHistory();
        objBusinessProcess.BusinessProcessInfo = new BusinessProcessInfo();
        objBusinessProcess.EntityReview = new List<EntityReview>();
        List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
        if (Convert.ToInt32(strEntityTypeID) == (int)EntityType.BusinessProcess)
        {

            BusinessProcessInfo BusinessProcessInfo = GetEntityDetailsByEntityTypeAndRecordID(iRecordID, iEntityTypeID);
            iprocessID = BusinessProcessInfo.ProcessID;
            PopulateEntities((int)EntityType.BusinessProcess, _UserDetails.OrgID, objBusinessProcess);
            objBusinessProcess.BusinessProcessInfo = BusinessProcessInfo;
            ViewBag.ReviewHistory = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(iEntityTypeID.ToString(), iRecordID.ToString());
            ViewBag.NextReviewDate = BusinessProcessInfo.ReviewDate;
            ViewBag.LastReviewDate = BusinessProcessInfo.LastReviewDate;
        }
        if (iEntityTypeID == (int)EntityType.BCMEntity)
        {
            lstBusinessProcessInfo = _ProcessSrv.GetBIAOtherBCMEntities_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID));

            lstBusinessProcessInfo = lstBusinessProcessInfo.Where(x => x.EntityTypeID == iEntityTypeID && x.RecordID == iRecordID).ToList();
            if (lstBusinessProcessInfo == null || !lstBusinessProcessInfo.Any())
            {
                objBusinessProcess = null;
            }

            foreach (BusinessProcessInfo objBusinessProc in lstBusinessProcessInfo)
            {
                iprocessID = objBusinessProc.ProcessID;
                objBusinessProcess.BusinessProcessInfo = objBusinessProc;

                iProcessID = objBusinessProcess.BusinessProcessInfo.IsAddedToBCM;
                if (objBusinessProcess.BusinessProcessInfo.IsAddedToBCM != 0)
                {
                    objBusinessProcess.BusinessProcessInfo.IsBCMEntity = 1;
                }
                iRecordID = objBusinessProcess.BusinessProcessInfo.ProcessID;
                if (objBusinessProcess.BusinessProcessInfo.ProcessReviewDate.ToString() == "01-01-0001 00:00:00")
                {
                    objBusinessProcess.BusinessProcessInfo.ProcessReviewDate = DateTime.Now;
                }
                objBusinessProcess.BusinessProcessInfo.RecordID = objBusinessProcess.BusinessProcessInfo.ProcessID;
                //objBusinessProcData.EntityReview.RecordID = objBusinessProcData.BusinessProcessInfo.ProcessID;
                //objBusinessProcData.EntityReview.EntityID = objBusinessProcData.BusinessProcessInfo.EntityTypeID;

                if (iProcessID > 0)
                {
                    objBusinessProcess.BusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(iProcessID.ToString() == "" ? "0" : iProcessID), (int)EntityType.BCMEntity);
                    if (objBusinessProcess.BusinessProcessInfo.ProcessCode != string.Empty)
                    {
                        objBusinessProcess.BusinessProcessInfo.IsBCMEntity = 1;
                    }
                    gridDataBind(objBusinessProcess.BusinessProcessInfo.BPProfileID);
                }
            }
        }
        if (Convert.ToInt32(strEntityTypeID) == (int)EntityType.Application)
        {
            Applications objApplication = new Applications();
            if (Convert.ToInt32(iRecordID) > 0)
            {
                objApplication = _ProcessSrv.GetApplicationByApplicationId(Convert.ToInt32(iRecordID));
                if (objApplication != null)
                {

                    objBusinessProcess.BusinessProcessInfo.ProcessName = objApplication.ApplicationName;
                    objBusinessProcess.BusinessProcessInfo.EntityTypeID = Convert.ToInt32(((int)EntityType.Application).ToString());
                    objBusinessProcess.BusinessProcessInfo.RecordID = objApplication.ApplicationId;
                    objBusinessProcess.BusinessProcessInfo.DepartmentID = objApplication.DepartmentID;
                    objBusinessProcess.BusinessProcessInfo.UnitID = objApplication.UnitID;
                    objBusinessProcess.BusinessProcessInfo.OrgID = objApplication.OrgID;
                    objBusinessProcess.BusinessProcessInfo.SubfunctionID = objApplication.SubfunctionId;
                    objBusinessProcess.BusinessProcessInfo.OrgGroupID = objApplication.OrgGroupID;
                    PopulateEntities((int)EntityType.Application, _UserDetails.OrgID, objBusinessProcess);

                    //objBusinessProcess.EntityReview.EntityID = objApplication.EntityTypeID;
                    //objBusinessProcess.EntityReview.RecordID = Convert.ToInt32(strRecordID);
                }
            }

        }

        if (Convert.ToInt32(strEntityTypeID) == (int)EntityType.ThirdParty)
        {
            CompanyMasterInfo objCompany = new CompanyMasterInfo();
            if (Convert.ToInt32(iRecordID) > 0)
            {
                objCompany = _ProcessSrv.GetCompanyByCopmanyId(Convert.ToInt32(iRecordID));
                if (objCompany != null)
                {
                    objBusinessProcess.BusinessProcessInfo.ProcessName = objCompany.CompanyName;
                    objBusinessProcess.BusinessProcessInfo.EntityTypeID = Convert.ToInt32(((int)EntityType.ThirdParty).ToString());
                    objBusinessProcess.BusinessProcessInfo.RecordID = objCompany.CompanyID;
                    objBusinessProcess.BusinessProcessInfo.DepartmentID = objCompany.DepartmentID;
                    objBusinessProcess.BusinessProcessInfo.UnitID = objCompany.UnitID;
                    objBusinessProcess.BusinessProcessInfo.OrgID = objCompany.OrgID;
                    objBusinessProcess.BusinessProcessInfo.SubfunctionID = objCompany.DepartmentID;
                    objBusinessProcess.BusinessProcessInfo.OrgGroupID = objCompany.OrgGroupID;
                    objBusinessProcess.BusinessProcessInfo.ProcessOwnerID = _UserDetails.UserID;
                    PopulateEntities((int)EntityType.ThirdParty, _UserDetails.OrgID, objBusinessProcess);
                }
            }
        }

        if (Convert.ToInt32(strEntityTypeID) == (int)EntityType.Facilities)
        {
            objBusinessProcess.BusinessProcessInfo = GetEntityDetailsByEntityTypeAndRecordID(iRecordID, iEntityTypeID);

            PopulateEntities((int)EntityType.Facilities, _UserDetails.OrgID, objBusinessProcess);
            FacilityAndHoliday objFacilityInfo = new FacilityAndHoliday();
            objFacilityInfo.Facility = _ProcessSrv.GetFacilitiesById(Convert.ToInt32(iRecordID));
            objBusinessProcess.BusinessProcessInfo.ProcessName = objFacilityInfo.Facility.FacilityName;
            objBusinessProcess.BusinessProcessInfo.EntityTypeID = Convert.ToInt32(((int)EntityType.Facilities).ToString());
            objBusinessProcess.BusinessProcessInfo.RecordID = iRecordID;
            objBusinessProcess.BusinessProcessInfo.UnitID = objFacilityInfo.Facility.FacilityUnitID;
            objBusinessProcess.BusinessProcessInfo.OrgID = objFacilityInfo.Facility.OrgID;
        }

        if (Convert.ToInt32(strEntityTypeID) == (int)EntityType.Location)
        {
            LocationMaster objLocation = new LocationMaster();
            if (Convert.ToInt32(iRecordID) > 0)
            {
                objLocation = _ProcessSrv.GetLocationById(Convert.ToInt32(iRecordID));
                if (objLocation != null)
                {
                    objBusinessProcess.BusinessProcessInfo.ProcessName = objLocation.LocationName;
                    objBusinessProcess.BusinessProcessInfo.EntityTypeID = Convert.ToInt32(((int)EntityType.Location).ToString());
                    objBusinessProcess.BusinessProcessInfo.RecordID = objLocation.Id;
                    //objBusinessProcess.BusinessProcessInfo.DepartmentID = objLocation.d;
                    objBusinessProcess.BusinessProcessInfo.UnitID = objLocation.UnitID;
                    objBusinessProcess.BusinessProcessInfo.OrgID = objLocation.OrgID;
                    //objBusinessProcess.BusinessProcessInfo.SubfunctionID = objLocation.DepartmentID;
                    objBusinessProcess.BusinessProcessInfo.OrgGroupID = objLocation.OrgGroupID;
                    PopulateEntities((int)EntityType.Location, _UserDetails.OrgID, objBusinessProcess);
                }
            }
        }

        return objBusinessProcess;
    }


    public IActionResult OtherBCMEntitiesForm(string strProcessID)
    {
        strProcessID = CryptographyHelper.Decrypt(strProcessID.ToString());
        BusinessProcessInfo objBusinessProcess = new BusinessProcessInfo();
        List<BIASection> lstBIASectionsNew = new List<BIASection>();
        try
        {
            PopulateDropdowns();

            HttpContext.Session.SetString("ProcessID", strProcessID);

            if (Convert.ToUInt32(strProcessID) > 0)
            {
                objBusinessProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(strProcessID.ToString() == "" ? "0" : strProcessID), (int)EntityType.BusinessProcess);

                objBusinessProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(strProcessID.ToString() == "" ? "0" : strProcessID), (int)EntityType.BCMEntity);

                List<BIASection> lstBIASections = _ProcessSrv.GetAllBIASectionsByProcess(Convert.ToInt32(strProcessID), 0);

                List<BIAProfileSection> objBIASectionsInProfile = _ProcessSrv.GetBIAProfileSectionByProfId(Convert.ToInt32(objBusinessProcess.ProfileID));

                if (lstBIASections != null && objBIASectionsInProfile != null)
                {
                    lstBIASectionsNew = lstBIASections.Where(b => objBIASectionsInProfile.Any(a => a.SectionID == b.SectionID)).ToList();
                }

                ViewBag.lstBIASection = lstBIASectionsNew;
            }

            HttpContext.Session.SetString("ProcessNameWithCode", objBusinessProcess.ProcessName + " ( " + objBusinessProcess.ProcessCode + " )");

        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        return View(objBusinessProcess);
    }




    public BusinessProcessInfo GetEntityDetailsByEntityTypeAndRecordID(int iRecordID, int iEntityTypeID)
    {
        BusinessProcessInfo objBusinessProcess = new BusinessProcessInfo();
        try
        {
            if (iEntityTypeID == (int)EntityType.BusinessProcess)
            {
                objBusinessProcess = _ProcessSrv.GetBusinessProcessMasterByProcessID(iRecordID);
                iProcessID = objBusinessProcess.IsAddedToBCM;
                if (objBusinessProcess.IsAddedToBCM != 0)
                {
                    objBusinessProcess.IsBCMEntity = 1;
                }
                ResourcesInfo OwnerInfo = _ProcessSrv.GetResourcesByResourceID(objBusinessProcess.ProcessOwnerID);
                objBusinessProcess.ProcessOwnerName = OwnerInfo.ResourceName;
                objBusinessProcess.OwnerEmail = OwnerInfo.CompanyEmail;
                objBusinessProcess.ProcessOwnerMobile = OwnerInfo.MobilePhone;
                ResourcesInfo AltOwnerInfo = _ProcessSrv.GetResourcesByResourceID(objBusinessProcess.AltProcessOwnerID);
                objBusinessProcess.AltProcessOwner = AltOwnerInfo.ResourceName;
                objBusinessProcess.AltProcessOwnerEmail = AltOwnerInfo.CompanyEmail;
                objBusinessProcess.AltProcessOwnerMobile = AltOwnerInfo.MobilePhone;

                iRecordID = objBusinessProcess.ProcessID;
                if (objBusinessProcess.ProcessReviewDate.ToString() == "01-01-0001 00:00:00")
                {
                    objBusinessProcess.ProcessReviewDate = DateTime.Now;
                }
                objBusinessProcess.RecordID = objBusinessProcess.ProcessID;
                if (iProcessID > 0)
                {
                    objBusinessProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(iProcessID.ToString() == "" ? "0" : iProcessID), (int)EntityType.BusinessProcess);
                    if (objBusinessProcess.ProcessCode != string.Empty)
                    {
                        objBusinessProcess.IsBCMEntity = 1;
                    }

                    gridDataBind(objBusinessProcess.BPProfileID);

                }
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        return objBusinessProcess;
    }

    public void PopulateDropdowns()
    {
        try
        {
            ViewBag.lstorgGroupInfoList = _Utilities.GetOrgGroupList();

            ViewBag.lstRoleMasterList = _Utilities.GetUserRoleMasterByOrgID(_UserDetails.OrgID);

            ViewBag.lstType = _Utilities.GetTypeInfoByEntityID(Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.ResourcePlan));

            ViewBag.lstCompany = _Utilities.GetCompanyMasterInfoList(_UserDetails.OrgID);

            ViewBag.lstResourceDesignation = _Utilities.GetAllResourceDesignationList();

            ViewBag.lstResource = new SelectList(_Utilities.GetResources(_UserDetails.OrgID), "ResourceId", "ResourceName");

            ViewBag.EntityTypes = _Utilities.PopulateBCMEntities();

            List<BIAProfileMaster> lstBIAProfile = _ProcessSrv.BIAProfileMasterListAll();
            ViewBag.lstBIAProfile = lstBIAProfile.Where(x => x.Status == "2");

            ViewBag.lstTimeUnit = _Utilities.PopulateStepTimeUnit();

            ViewBag.lstParameterProfile = _Utilities.GetBusinessParameterProfileList(Convert.ToInt32(_UserDetails.OrgID));
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }

    public ActionResult ReviweHistoryDetails(int RecordID, int EntityTypeID)
    {
        List<EntityReview> lstEntityHistory = new List<EntityReview>();
        try
        {
            lstEntityHistory = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(EntityTypeID.ToString(), RecordID.ToString());
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        return PartialView("ReviewHistoryDetails", lstEntityHistory);
    }

    public JsonResult ddlEntitiesSelectedIndexChange(int EntityTypeID, int OrgID)
    {
        try
        {
            if (EntityTypeID == (int)EntityType.BusinessProcess)
            {
                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    ViewBag.lstEntities = _ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(OrgID));
                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    if (OrgID == 0)
                    {
                        ViewBag.lstEntities = _ProcessSrv.GetBusinessProcessMasterListByOrgGroupID(Convert.ToInt32(OrgID));
                    }
                    else
                    {
                        ViewBag.lstEntities = new SelectList(_ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(OrgID)), "ProcessID", "ProcessName");
                    }
                }
                else
                {
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(OrgID)), "ProcessID", "ProcessName");
                    //ViewBag.lstEntities = _ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(OrgID));
                }

            }
            else if (EntityTypeID == (int)EntityType.Facilities)


                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetAllFacilitieslistByUnitIDOrgId(_UserDetails.OrgID, _UserDetails.UnitID, _UserDetails.OrgGroupID.ToString()), "FacilityID", "FacilityName");
                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    var facColl = _ProcessSrv.GetFacilitiesList();
                    var facColl1 = (from Facility facility in facColl
                                    where facility.OrgGroupID.ToString() == _UserDetails.OrgGroupID.ToString()
                                    select facility);
                    ViewBag.lstEntities = new SelectList(facColl1, "FacilityID", "FacilityName");
                }
                else
                {
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetAllFacilitieslistByUnitIDOrgId(Convert.ToInt32(_UserDetails.OrgID), 0, "0"), "FacilityID", "FacilityName");

                    return Json(ViewBag.lstEntities);
                }
            else if (EntityTypeID == (int)EntityType.Location)

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateLocation(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.PopulateLocationByOrgGroupID(Convert.ToInt32(_UserDetails.OrgGroupID)), "Id", "LocationName");

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //Utilities.Utilities.PopulateLocationByOrgGroupID(ddlEntity, Convert.ToInt32(_UserDetails.OrgGroupID));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.PopulateLocationByOrgGroupID(Convert.ToInt32(_UserDetails.OrgGroupID)), "Id", "LocationName");

                }
                else
                {
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetBIALocation_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID)), "ProcessID", "ProcessName");

                }




            //Utilities.Utilities.PopulateBCMGroupsByUnitId(ddlBusinessProcess, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(ddlUnit.SelectedValue));
            else if (EntityTypeID == (int)EntityType.People)
                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetResourcesListForDDL(Convert.ToInt32(_UserDetails.OrgID)), "ResourceId", "ResourceName");
                    return Json(ViewBag.lstEntities);
                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.GetResourcesListByOrgGrpID(ddlEntity, _UserDetails.OrgGroupID);
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    //}


                }
                else
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //   Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    //}
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetResourcesListForDDL(Convert.ToInt32(_UserDetails.OrgID)), "ResourceId", "ResourceName");
                    return Json(ViewBag.lstEntities);
                }


            else if (EntityTypeID == (int)EntityType.BCMEntity)
            {

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    ///Utilities.Utilities.PopulateOtherBCMEntities(ddlEntity, Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(_UserDetails.UnitID), 0, 0);

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.PopulateOtherBCMEntitiesByOrgGroupID(ddlEntity, Convert.ToInt32(_UserDetails.OrgGroupID), Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_UserDetails.UnitID), 0, 0);
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateOtherBCMEntitiesNew(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_UserDetails.UnitID), 0, 0);
                    //}
                }
                else
                {
                    // Utilities.Utilities.PopulateOtherBCMEntitiesNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(_UserDetails.UnitID), 0, 0);
                }



                //Utilities.Utilities.PopulateOtherBCMEntities(ddlEntity,Convert.ToInt32(ddlOrg.SelectedValue),Convert.ToInt32(ddlUnit.SelectedValue),Convert.ToInt32(ddlDepartment.SelectedValue),Convert.ToInt32(ddlSubDeparment.SelectedValue));
                // Utilities.Utilities.PopulateOtherBCMEntities(ddlEntity, FilterString);
            }
            else if (EntityTypeID == (int)EntityType.ThirdParty)
            {

                //if ((_UserDetails.UserRole.Equals(CVGlobal.ProductAdminRole)) || (_UserDetails.UserRole.Equals(CVGlobal.SuperAdminRole)))
                //{
                //    Utilities.Utilities.PopulateCompany(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                //}
                //else
                //{
                //    Utilities.Utilities.PopulateCompanyNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                //}


                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateCompany(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.PopulateCompanyByOrgGroupID(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_UserDetails.OrgGroupID));
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateCompany(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    //}
                }
                else
                {
                    // Utilities.Utilities.PopulateCompanyNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                }

            }
            else if (EntityTypeID == (int)EntityType.Application)

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    //ApplicationColl objApplicationsColl = new ApplicationColl();
                    //objApplicationsColl = objProcessSrv.GetApplicationList(Convert.ToInt32(_UserDetails.OrgID));

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //   // Utilities.Utilities.PopulateApplicationsByOrgGroupID(ddlEntity, _UserDetails.OrgGroupID);
                    //}
                    //else
                    //{
                    //   // Utilities.Utilities.PopulateApplicationsByOrgID(ddlEntity, ddlOrg.SelectedValue);
                    //}
                }
                else
                {
                    //ViewBag.lstEntities = _ProcessSrv.GetApplicationList(Convert.ToInt16(_UserDetails.OrgID));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetApplicationList(Convert.ToInt32(_UserDetails.OrgID)), "ApplicationId", "ApplicationName");
                    //TempData["RecordID"] = "0";
                    //_Utilities.PopulateApplicationsNew(ddlEntity, _UserDetails.OrgID);
                }



            //if (EntityTypeID == (int)EntityType.BusinessProcess)
            //{
            //    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(_UserDetails.OrgID)), "ProcessID", "ProcessName");
            //    //ViewBag.lstBusinessProcessMaster = _ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(_UserDetails.OrgID));
            //}else if(EntityTypeID == (int)EntityType.Application)
            //{

            //    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetApplicationByApplicationID(Convert.ToInt32(EntityTypeID)), "ApplicationId", "ApplicationName");
            //   // ViewBag.lstBusinessProcessMaster = _ProcessSrv.GetBIAApplication_OrgUnitLevel(_UserDetails.OrgID);
            //}
            return Json(ViewBag.lstEntities);
        }
        catch (Exception)
        {

            throw;
        }
    }
    public void PopulateEntities(int EntityTypeID, int OrgID, BusinessProcessInfoAndReviewHistory ObjbusinessProcess)
    {
        try
        {


            if (EntityTypeID == (int)EntityType.BusinessProcess)
                ViewBag.lstEntities = new SelectList(_ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(_UserDetails.OrgID)), "ProcessID", "ProcessName");
            //BindProcesses();


            else if (EntityTypeID == (int)EntityType.Facilities)


                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateFacilitiesByUnitIdOrgid(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(ddlUnit.SelectedValue));
                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //Utilities.Utilities.PopulateFacilitiesByOrgGroup(ddlEntity, Convert.ToInt32(_UserDetails.OrgGroupID));
                }
                else
                {
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetAllFacilitieslistByUnitIDOrgId(Convert.ToInt32(ObjbusinessProcess.BusinessProcessInfo.OrgID), Convert.ToInt32(ObjbusinessProcess.BusinessProcessInfo.UnitID), ObjbusinessProcess.BusinessProcessInfo.OrgGroupID.ToString()), "FacilityID", "FacilityName");
                    //_Utilities.PopulateFacilitiesByUnitIdOrgidNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(_UserDetails.UnitID));
                }
            else if (EntityTypeID == (int)EntityType.Location)

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateLocation(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //Utilities.Utilities.PopulateLocationByOrgGroupID(ddlEntity, Convert.ToInt32(_UserDetails.OrgGroupID));

                }
                else
                {
                    // Utilities.Utilities.PopulateLocationNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                    //lstBusinessProcess = _ProcessSrv.GetBIALocation_OrgUnitLevel(_UserDetails.OrgID);
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetBIALocation_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID)), "RecordID", "ProcessName");
                }




            //Utilities.Utilities.PopulateBCMGroupsByUnitId(ddlBusinessProcess, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(ddlUnit.SelectedValue));
            else if (EntityTypeID == (int)EntityType.People)
                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.GetResourcesListByOrgGrpID(ddlEntity, _UserDetails.OrgGroupID);
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    //}


                }
                else
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    //}
                }


            else if (EntityTypeID == (int)EntityType.BCMEntity)
            {

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    ///Utilities.Utilities.PopulateOtherBCMEntities(ddlEntity, Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(_UserDetails.UnitID), 0, 0);

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.PopulateOtherBCMEntitiesByOrgGroupID(ddlEntity, Convert.ToInt32(_UserDetails.OrgGroupID), Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_UserDetails.UnitID), 0, 0);
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateOtherBCMEntitiesNew(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_UserDetails.UnitID), 0, 0);
                    //}
                }
                else
                {
                    //ViewBag.lstEntities = _ProcessSrv.GetOtherBCMEntities(Convert.ToInt32(ObjbusinessProcess.BusinessProcessInfo.OrgID), Convert.ToInt32(ObjbusinessProcess.BusinessProcessInfo.UnitID));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetOtherBCMEntities(Convert.ToInt32(ObjbusinessProcess.BusinessProcessInfo.OrgID), Convert.ToInt32(ObjbusinessProcess.BusinessProcessInfo.UnitID), 0, 0), "ID", "EntityName");

                    // Utilities.Utilities.PopulateOtherBCMEntitiesNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(_UserDetails.UnitID), 0, 0);
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetBIAOtherBCMEntities_OrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID)), "RecordID", "ProcessName");
                }



                //Utilities.Utilities.PopulateOtherBCMEntities(ddlEntity,Convert.ToInt32(ddlOrg.SelectedValue),Convert.ToInt32(ddlUnit.SelectedValue),Convert.ToInt32(ddlDepartment.SelectedValue),Convert.ToInt32(ddlSubDeparment.SelectedValue));
                // Utilities.Utilities.PopulateOtherBCMEntities(ddlEntity, FilterString);
            }
            else if (EntityTypeID == (int)EntityType.ThirdParty)
            {

                //if ((_UserDetails.UserRole.Equals(CVGlobal.ProductAdminRole)) || (_UserDetails.UserRole.Equals(CVGlobal.SuperAdminRole)))
                //{
                //    Utilities.Utilities.PopulateCompany(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                //}
                //else
                //{
                //    Utilities.Utilities.PopulateCompanyNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                //}


                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateCompany(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.PopulateCompanyByOrgGroupID(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_UserDetails.OrgGroupID));
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateCompany(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    //}
                }
                else
                {
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetCompanyList(Convert.ToInt32(_UserDetails.OrgID)), "CompanyID", "CompanyName");
                }
            }
            else if (EntityTypeID == (int)EntityType.Application)

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    //ApplicationColl objApplicationsColl = new ApplicationColl();
                    //objApplicationsColl = objProcessSrv.GetApplicationList(Convert.ToInt32(_UserDetails.OrgID));

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //   // Utilities.Utilities.PopulateApplicationsByOrgGroupID(ddlEntity, _UserDetails.OrgGroupID);
                    //}
                    //else
                    //{
                    //   // Utilities.Utilities.PopulateApplicationsByOrgID(ddlEntity, ddlOrg.SelectedValue);
                    //}
                }
                else
                {
                    //ViewBag.lstEntities = _ProcessSrv.GetApplicationList(Convert.ToInt16(_UserDetails.OrgID));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetApplicationList(Convert.ToInt32(_UserDetails.OrgID)), "ApplicationId", "ApplicationName");
                    //TempData["RecordID"] = "0";
                    //_Utilities.PopulateApplicationsNew(ddlEntity, _UserDetails.OrgID);
                }



            //if (EntityTypeID == (int)EntityType.BusinessProcess)
            //{
            //    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(_UserDetails.OrgID)), "ProcessID", "ProcessName");
            //    //ViewBag.lstBusinessProcessMaster = _ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(_UserDetails.OrgID));
            //}else if(EntityTypeID == (int)EntityType.Application)
            //{

            //    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetApplicationByApplicationID(Convert.ToInt32(EntityTypeID)), "ApplicationId", "ApplicationName");
            //   // ViewBag.lstBusinessProcessMaster = _ProcessSrv.GetBIAApplication_OrgUnitLevel(_UserDetails.OrgID);
            //}
        }
        catch (Exception)
        {

            throw;
        }
    }


    public void gridDataBind(int ProfileID)
    {
        List<BusinessProfileParameter> ListBusinessProfileParameter = _ProcessSrv.BusinessProfileParameterbyProfID(ProfileID);

        ViewBag.lstProfileParameter = ListBusinessProfileParameter;
    }

    [HttpPost]
    public IActionResult BusinessPocessSave(BusinessProcessInfo businessProcessInfo)
    {
        string strRecordID = string.Empty;
        string strEntityTypeID = string.Empty;
        string strIsActionMethod = string.Empty;
        string strProcessID = string.Empty;
        bool bSuccess = false;

        string SuccesMessege = string.Empty;
        try
        {
            businessProcessInfo.ProfileID = 1;
            //DateTime ReviewDate = (DateTime)businessProcessInfo.ReviewDate;

            if (Convert.ToInt32(businessProcessInfo.ProcessID) > 0)
            {
                businessProcessInfo.LastReviewDate = (DateTime)businessProcessInfo.ReviewDate;
                bSuccess = _ProcessSrv.BusinessprocessUpdate(businessProcessInfo);
                strRecordID = CryptographyHelper.Encrypt(businessProcessInfo.RecordID.ToString());
                strEntityTypeID = CryptographyHelper.Encrypt(businessProcessInfo.EntityTypeID.ToString());
                strProcessID = CryptographyHelper.Encrypt(businessProcessInfo.ProcessID.ToString());
                strIsActionMethod = "1";
                SuccesMessege = businessProcessInfo.ProcessName + " Updated Successfully";
            }
            else
            {
                businessProcessInfo = BuildData(businessProcessInfo);
                iProcessID = _ProcessSrv.BusinessProcessSave(businessProcessInfo);
                bSuccess = true;
                strRecordID = CryptographyHelper.Encrypt(businessProcessInfo.RecordID.ToString());
                strEntityTypeID = CryptographyHelper.Encrypt(businessProcessInfo.EntityTypeID.ToString());
                strProcessID = CryptographyHelper.Encrypt(businessProcessInfo.ProcessID.ToString());
                strIsActionMethod = "1";
                SuccesMessege = businessProcessInfo.ProcessName + " Added to BCM.";
                HttpContext.Session.SetString("iProcessID", iProcessID.ToString());
            }

        }
        catch (Exception ex)
        {

            _CvLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? SuccesMessege : "Failed to update Business Process." });
        }
        return RedirectToAction("ManageBusinessProcess", "ManageBusinessProcesses");//new { strRecordID, strEntityTypeID, strIsActionMethod, strProcessID });
    }

    public void PopulateDropdown_BusinessProfile(string OrgID)
    {

        try
        {
            List<BIAProfileMaster> businessProfileList = _ProcessSrv.BIAProfileMasterListAll();
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }

    private string GetRPOInMinutes(string strRPO, string strRPOUnit)
    {
        string RPO = string.Empty;
        int TimeUnit = Convert.ToInt32(strRPOUnit);
        switch (TimeUnit)
        {
            case 1:
                RPO = strRPO;
                break;
            case 2:
                RPO = (Convert.ToInt32(strRPO) * 60).ToString();
                break;
            case 3:
                RPO = (Convert.ToInt32(strRPO) * 60 * 24).ToString();
                break;
            case 4:
                RPO = (Convert.ToInt32(strRPO) * 60 * 24 * 30).ToString();
                break;

        }
        return RPO;
    }
    private BusinessProcessInfo BuildData(BusinessProcessInfo objBusinessProcess)
    {
        if (objBusinessProcess.RecordID == 0)
        {
            objBusinessProcess.RecordID = objBusinessProcess.ProcessID;
        }
        //Default Business Process profile ID Passed;
        objBusinessProcess.ProfileID = 1;

        objBusinessProcess.ProcessID = 0;
        objBusinessProcess.IsActive = 1;
        objBusinessProcess.Version = "1.0";

        objBusinessProcess.ChangedBy = _UserDetails.UserID;
        objBusinessProcess.CreatedBy = _UserDetails.UserID;

        objBusinessProcess.IsEffective = 1;

        objBusinessProcess.IsCritical = 0;

        if (objBusinessProcess.LastReviewDate.ToString() == "01-01-0001 00:00:00")
        {
            objBusinessProcess.LastReviewDate = null;
        }
        if (objBusinessProcess.ReviewDate.ToString() == "01-01-1754 00:00:00")
        {
            objBusinessProcess.ReviewDate = null;
        }
        return objBusinessProcess;
    }



    [HttpGet]
    public IActionResult GetResourceDetails(int iId)
    {
        try
        {
            var objResourcesInfo = _ProcessSrv.GetResourcesByResourceID(iId);
            if (objResourcesInfo != null)
            {
                return Json(new { mail = objResourcesInfo.CompanyEmail, mobile = objResourcesInfo.MobilePhone });
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        return Json(null);
    }

    public JsonResult SendForApproval(string strProcessID)
    {
        List<BusinessProcessInfo> lstBusinessProcess = new List<BusinessProcessInfo>();
        string strRecordID = string.Empty;
        string strEntityTypeID = string.Empty;
        bool success = false;
        BusinessProcessInfo objBusinesProcess = new BusinessProcessInfo();
        bool IsReviewPending = HttpContext.Session.GetString("IsReviewPending") == "true"? true : false;

        if (IsReviewPending)
        {
            try
            {

                PopulateDropdowns();
                strProcessID = CryptographyHelper.Decrypt(strProcessID.ToString());
                objBusinesProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(strProcessID), 1);

                List<EntityReview> ObjEntityReviewColl = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(objBusinesProcess.EntityTypeID.ToString(), objBusinesProcess.RecordID.ToString());

                if (ObjEntityReviewColl.Count > 3)
                {
                    ViewBag.ShowHsitoryVisible = "Show";
                }
                else
                {
                    ViewBag.ShowHsitoryVisible = "Hide";
                }
                objBusinesProcess.Status = (int)BCPEnum.ApprovalType.WaitingforApproval;
                objBusinesProcess.EntityTypeID = objBusinesProcess.EntityTypeID;
                success = _ProcessSrv.BusinessprocessUpdate(objBusinesProcess);
                UpdateApprovalStatusAndSendMail(objBusinesProcess.EntityTypeID, objBusinesProcess.ProcessID, objBusinesProcess.Status);
                //success = true;
                strRecordID = CryptographyHelper.Encrypt(objBusinesProcess.RecordID.ToString());
                strEntityTypeID = CryptographyHelper.Encrypt(objBusinesProcess.EntityTypeID.ToString());
                strProcessID = CryptographyHelper.Encrypt(strProcessID.ToString());

            }
            catch (Exception ex)
            {
                _CvLogger.LogErrorApp(ex);
                //return NotFound("No any record");
            }
        }
        if (success)
        {
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new { success = success, message = success ? objBusinesProcess.ProcessName + " Send For Approval Successfully" : "Failed to Send For Approval. Please try again." });
            }
        }
        else
        {
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new { success = IsReviewPending, message = IsReviewPending ? objBusinesProcess.ProcessName + " Send For Approval Successfully" : "First Complete the Review Process to Send For Approval." });
            }
        }



        return Json(success);
        //return RedirectToAction("BusinessProcessForm",new { strRecordID = strRecordID, strEntityTypeID = strEntityTypeID, strProcessID = strProcessID });
        //return RedirectByEntityType(Convert.ToInt32(objBusinesProcess.EntityTypeID));
        //return RedirectToAction("ManageApplication", "ManageApplication", new { area = "BCMApplicationBIA" });
    }

    public ActionResult RedirectByEntityType(int EntityTypeID)
    {
        if (EntityTypeID == (int)EntityType.BusinessProcess)
            return RedirectToAction("ManageBusinessProcesses", "ManageBusinessProcesses", new { area = "BCMProcessBIA" });
        if (EntityTypeID == (int)EntityType.Facilities)
            return RedirectToAction("ManageFacility", "ManageFacility", new { area = "BCMFacility" });
        if (EntityTypeID == (int)EntityType.Application)
            return RedirectToAction("ManageApplication", "ManageApplication", new { area = "BCMApplicationBIA" });
        if (EntityTypeID == (int)EntityType.policy)
            return RedirectToAction("BCMPolicy");
        if (EntityTypeID == (int)EntityType.BCMEntity)
            return RedirectToAction("ManageBCMEntities");
        if (EntityTypeID == (int)EntityType.ThirdParty)
            return RedirectToAction("ManageVendor");
        if (EntityTypeID == (int)EntityType.Location)
            return RedirectToAction("ManageLocation");
        return RedirectToAction("ManageBusinessProcesses");
    }

    public JsonResult btnApproveClick(string strProcessID)
    {
        BusinessProcessInfo lstBusinessProcess = new BusinessProcessInfo();
        bool isuccess = false;
        try
        {
            strProcessID = CryptographyHelper.Decrypt(strProcessID.ToString());
            PopulateDropdowns();
            lstBusinessProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(strProcessID), 1);
            lstBusinessProcess.Status = (int)BCPEnum.ApprovalType.Approved;
            isuccess = _ProcessSrv.BusinessprocessUpdate(lstBusinessProcess);
            UpdateApprovalStatusAndSendMail(lstBusinessProcess.EntityTypeID, lstBusinessProcess.ProcessID, lstBusinessProcess.Status);
            isuccess = true;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = isuccess, message = isuccess ? lstBusinessProcess.ProcessName + " Approved Successfully" : "Failed to Approve. Please try again." });
        }

        return Json(isuccess);
    }

    public bool UpdateApprovalStatusAndSendMail(int EntityTypeID, int RecordID, int StatusID)
    {
        try
        {
            ResourcesInfo ObjOwnerInfo = new ResourcesInfo();
            ResourcesInfo ObjAltOwnerInfo = new ResourcesInfo();
            ResourcesInfo ObjApproverInfo = new ResourcesInfo();

            BusinessProcessInfo objBusinesProcess = new BusinessProcessInfo();
            bool IsApprovalStatusUpdate = false;
            string EntityTypeName = string.Empty;
            string MailBody = string.Empty;
            string MailSubject = string.Empty;
            bool Send = false;
            switch (EntityTypeID)
            {
                case (int)EntityType.BusinessProcess:
                    EntityTypeName = "Business Process";
                    IsApprovalStatusUpdate = _ProcessSrv.BussinessProcessBIA_Update_ApprovalStatus(RecordID.ToString(), StatusID, 0, Convert.ToInt32(_UserDetails.UserID));
                    objBusinesProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(RecordID), 1);
                    ObjOwnerInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(objBusinesProcess.ProcessOwnerID));
                    ObjAltOwnerInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(objBusinesProcess.ProcessOwnerID));
                    ObjApproverInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(objBusinesProcess.ApproverID));
                    #region "Sending Mail to Owner"
                    MailBody = GetMailBody(ObjOwnerInfo.ResourceName, objBusinesProcess.ProcessID.ToString(), ObjOwnerInfo.ResourceId.ToString(), EntityTypeID, StatusID.ToString(), objBusinesProcess.ProcessName, ObjApproverInfo.ResourceName, EntityTypeName);
                    MailSubject = GetMailSubject(EntityTypeName, objBusinesProcess.Status, objBusinesProcess.ProcessName);
                    Send = _BCMMail.SendMail(MailSubject, MailBody, ObjOwnerInfo.CompanyEmail, "", "", "", _UserDetails.OrgID.ToString());
                    EntityApprovalRecordsSave(EntityTypeID, objBusinesProcess.ProcessID, null, _UserDetails.UserID, ObjApproverInfo.ResourceId, StatusID, _UserDetails.UserID);

                    #endregion
                    #region "Sending Mail to Owner"
                    MailBody = GetMailBody(ObjAltOwnerInfo.ResourceName, objBusinesProcess.ProcessID.ToString(), ObjOwnerInfo.ResourceId.ToString(), EntityTypeID, StatusID.ToString(), objBusinesProcess.ProcessName, ObjApproverInfo.ResourceName, EntityTypeName);
                    Send = _BCMMail.SendMail(MailSubject, MailBody, ObjAltOwnerInfo.CompanyEmail, "", "", "", _UserDetails.OrgID.ToString());
                    EntityApprovalRecordsSave(EntityTypeID, objBusinesProcess.ProcessID, null, _UserDetails.UserID, ObjApproverInfo.ResourceId, StatusID, _UserDetails.UserID);

                    #endregion
                    break;
                case (int)EntityType.Application:
                    EntityTypeName = "Application";
                    IsApprovalStatusUpdate = _ProcessSrv.BussinessProcessBIA_Update_ApprovalStatus(RecordID.ToString(), StatusID, 0, Convert.ToInt32(_UserDetails.UserID));
                    Applications objApplication = new Applications();
                    objApplication = _ProcessSrv.GetApplicationByApplicationId(Convert.ToInt32(RecordID));
                    int iSucess = 0;
                    objApplication.Status = StatusID.ToString();
                    iSucess = _ProcessSrv.ApplicationUpdate(objApplication);
                    objBusinesProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(RecordID), 1);
                    ObjOwnerInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(objBusinesProcess.ProcessOwnerID));
                    ObjAltOwnerInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(objBusinesProcess.ProcessOwnerID));
                    ObjApproverInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(objBusinesProcess.ApproverID));

                    #region "Sending Mail to Owner"

                    MailBody = GetMailBody(ObjOwnerInfo.ResourceName, objBusinesProcess.ProcessID.ToString(), ObjOwnerInfo.ResourceId.ToString(), EntityTypeID, StatusID.ToString(), objBusinesProcess.ProcessName, ObjApproverInfo.ResourceName, EntityTypeName);
                    MailSubject = GetMailSubject(EntityTypeName, objBusinesProcess.Status, objBusinesProcess.ProcessName);
                    Send = _BCMMail.SendMail(MailSubject, MailBody, ObjOwnerInfo.CompanyEmail, "", "", "", _UserDetails.OrgID.ToString());
                    EntityApprovalRecordsSave(EntityTypeID, objBusinesProcess.ProcessID, null, _UserDetails.UserID, ObjApproverInfo.ResourceId, StatusID, _UserDetails.UserID);

                    #endregion
                    #region "Sending Mail to Owner"

                    MailBody = GetMailBody(ObjAltOwnerInfo.ResourceName, objBusinesProcess.ProcessID.ToString(), ObjOwnerInfo.ResourceId.ToString(), EntityTypeID, StatusID.ToString(), objBusinesProcess.ProcessName, ObjApproverInfo.ResourceName, EntityTypeName);
                    Send = _BCMMail.SendMail(MailSubject, MailBody, ObjAltOwnerInfo.CompanyEmail, "", "", "", _UserDetails.OrgID.ToString());
                    EntityApprovalRecordsSave(EntityTypeID, objBusinesProcess.ProcessID, null, _UserDetails.UserID, ObjApproverInfo.ResourceId, StatusID, _UserDetails.UserID);

                    #endregion
                    break;
            }

        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        return true;
    }

    public void EntityApprovalRecordsSave(int EntityTypeID, int EntityID, string Remarks, int SenderID, int ReceiverID, int StatusID, int CreatedBy)
    {
        EntityApprovalRecords objEntityApprovalRecords = new EntityApprovalRecords();
        objEntityApprovalRecords.EntityID = EntityTypeID;
        objEntityApprovalRecords.RecordID = EntityID;
        objEntityApprovalRecords.Remarks = Remarks;
        objEntityApprovalRecords.SenderID = SenderID;
        objEntityApprovalRecords.ReceiverID = ReceiverID;
        objEntityApprovalRecords.Status = StatusID;
        objEntityApprovalRecords.CreatedBy = CreatedBy;
        bool saved = _ProcessSrv.EntityApprovalRecordsSave(objEntityApprovalRecords);
    }

    public string GetMailSubject(string EntityTypeName, int iStatusID, string EntityName)
    {
        string subjectline = string.Empty;
        try
        {
            if (iStatusID.Equals(((int)BCPEnum.ApprovalType.WaitingforApproval).ToString()))
                subjectline = " for Approval - ";
            else if (iStatusID.Equals(((int)BCPEnum.ApprovalType.Approved).ToString()))
                subjectline = " Approved - ";
            else if (iStatusID.Equals(((int)BCPEnum.ApprovalType.Disapproved).ToString()))
                subjectline = " Disapproved - ";

            subjectline = EntityTypeName + subjectline + EntityName;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        return subjectline;
    }

    public string GetMailBody(string strUserName, string ProcessID, string strUserID, int EntityTypeId, string statusID, string EntityName, string ApproverName, string EntityTypeName)
    {
        string strMailBody = string.Empty;
        string AppLink = _Utilities.VaultConfigurations.ApplicationDirectory; // System.Configuration.ConfigurationManager.AppSettings["App_ROOT"].ToString();
                                                                              //string AppLink = string.Empty;


        //if (EntityTypeId == (int)(BCPEnum.EntityType.Application))
        //    AppLink = AppLink + @"/BCMApprovals/ApproveApplications.aspx";
        //else if (EntityTypeId == (int)(BCPEnum.EntityType.BusinessProcess))
        //AppLink = AppLink + @"/BCMProcessBIA/ManageBusinessProcesses/ManageBusinessProcess";
        //else
        //AppLink = AppLink + @"/BCMEntities/ApprovalBCMEntity.aspx";

        //string Approverlink = string.Empty;

        ///string Approverlink = Server.HtmlEncode(AppLink + "?ProcessID=" + BCP.Security.CryptographyHelper.BCPEncrypt(ProcessID) + "&UserID=" + BCP.Security.CryptographyHelper.BCPEncrypt(strUserID) + "&OrgID=" + BCP.Security.CryptographyHelper.BCPEncrypt(_oUser.OrgID) + "&Usr_Name=" + BCP.Security.CryptographyHelper.BCPEncrypt(ApproverName) + "&Usr_ID=" + BCP.Security.CryptographyHelper.BCPEncrypt(strUserID));

        string Approverlink = AppLink;
        if (statusID.Equals(((int)BCPEnum.ApprovalType.WaitingforApproval).ToString()))
        {
            strMailBody = "Dear " + strUserName + ",<br /><br />Please <a href='" + Approverlink + "'>Click here</a> to Approve or Disapprove the BIA for  " + EntityTypeName + " : " + EntityName + "<br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
        }
        else if (statusID.Equals(((int)BCPEnum.ApprovalType.Approved).ToString()))
        {
            strMailBody = "Dear " + strUserName + ",<br /><br /> " + EntityTypeName + " :" + EntityName + " has been Approved by " + ApproverName + ".<br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
        }
        else if (statusID.Equals(((int)BCPEnum.ApprovalType.Disapproved).ToString()))
        {
            strMailBody = "Dear " + strUserName + ",<br /><br /> " + EntityTypeName + " :" + EntityName + " has been Disapproved by " + ApproverName + ".<br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
        }
        return strMailBody;
    }


    public JsonResult btnDisApproveClick(string strProcessID)
    {
        BusinessProcessInfo objBusinesProcess = new BusinessProcessInfo();
        bool isuccess = false;
        try
        {
            strProcessID = CryptographyHelper.Decrypt(strProcessID.ToString());
            PopulateDropdowns();
            objBusinesProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(strProcessID), 1);
            objBusinesProcess.Status = (int)BCPEnum.ApprovalType.Disapproved;
            isuccess = _ProcessSrv.BusinessprocessUpdate(objBusinesProcess);
            UpdateApprovalStatusAndSendMail(objBusinesProcess.EntityTypeID, objBusinesProcess.ProcessID, objBusinesProcess.Status);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = isuccess, message = isuccess ? objBusinesProcess.ProcessName + " DisApproved Successfully" : "Failed to DisApprove. Please try again." });
        }
        return Json(true);
    }

    [HttpGet]
    public JsonResult GetAllUnits(int iOrgID)
    {
        try
        {
            var objDepartmentList = _Utilities.BindUnit(iOrgID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID)
    {
        try
        {
            var objDepartmentList = _ProcessSrv.GetDepartmentByUnitId(iUnitID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllSubDepartments(int iDepartmentID)
    {
        try
        {
            var objSubDepartmentList = _Utilities.BindSubFunction(iDepartmentID);
            return Json(objSubDepartmentList);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public bool EndReviewHistoryClick(string strProcessID)
    {
        return true;
    }

    #region Review Section
    [HttpGet]
    public IActionResult ReviewSection()
    {
        string? strRecordID = HttpContext.Session.GetString("strRecordID");
        string? strEntityTypeID = HttpContext.Session.GetString("strEntityTypeID");

        ViewBag.RecordID = strRecordID;
        ViewBag.EntityID = strEntityTypeID;

        List<EntityReview> lstEntityReview = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(strEntityTypeID, strRecordID);

        //bind next review date
        var strNextReviewDate = lstEntityReview
            .OrderByDescending(e => e.NextReviewDate)
            .Skip(1)
            .Select(e => e.NextReviewDate)
            .FirstOrDefault();
        ViewBag.NextReviewDate = strNextReviewDate;



        return PartialView("~/Areas/BCMProcessBIA/Views/BusinessProcessForm/ReviewSection.cshtml", lstEntityReview);
    }

    [HttpPost]
    public JsonResult StartReviewSection(string EntityID, string RecordID, string NextReviewDate)
    {
        EntityReview objEntityReviews = new EntityReview();
        bool isSuccess = false;
        try
        {
            objEntityReviews.EntityID = Convert.ToInt32(EntityID);
            objEntityReviews.RecordID = Convert.ToInt32(RecordID);
            objEntityReviews.NextReviewDate = Convert.ToDateTime(NextReviewDate);
            objEntityReviews.ReviewerID = _UserDetails.UserID;
            isSuccess = _ProcessSrv.EntityReviewSave(objEntityReviews);

            List<EntityReview> ObjEntityReviewColl = new List<EntityReview>();
            ObjEntityReviewColl = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(EntityID, RecordID);
            ObjEntityReviewColl = ObjEntityReviewColl.OrderBy(o => o.Status).Take(3).ToList();
            ViewBag.ReviewHistory = ObjEntityReviewColl;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);

        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = isSuccess, message = isSuccess ? "Review Started" : "Failed to Start Review. Please try again." });
        }
        return Json(isSuccess);
        //ReviewSection();
    }

    [HttpPost]
    public JsonResult EndReviewSection(string EntityID, string RecordID, string NextReviewDate)
    {
        bool Success = false;
        try
        {


            EntityReview objEntityReview = new EntityReview();
            objEntityReview.EntityID = Convert.ToInt32(EntityID);
            objEntityReview.RecordID = Convert.ToInt32(RecordID);
            objEntityReview.NextReviewDate = Convert.ToDateTime(NextReviewDate);
            objEntityReview.ReviewerID = _UserDetails.UserID;

            if (true)
            {
                GetUpdateReviewStatusofRACycle(objEntityReview);
                Success = true;
            }

            bool SuccessUpdated = false;

            if (Success)
            {

                List<EscalationMatrix> lstEscalationMatrix = _ProcessSrv.EscalationMatrixMapping_GetAll(1);

                if (lstEscalationMatrix != null)
                {
                    int iID = string.IsNullOrEmpty(Convert.ToString(EntityID)) ? 0 : Convert.ToInt32(RecordID);

                    var EscDetails = from EscalationMatrix objEsc1 in lstEscalationMatrix
                                     where
                                    Convert.ToInt32(objEsc1.EscMatType) == (int)BCPEnum.EscalationMatrixType.Review
                                     &&
                                    Convert.ToInt32(objEsc1.BCMEntityType) == (string.IsNullOrEmpty(Convert.ToString(EntityID)) ? 0 : Convert.ToInt32(EntityID))
                                     &&
                                    Convert.ToInt32(objEsc1.BCMEntityID) == Convert.ToInt32(RecordID)
                                     select objEsc1;


                    if (EscDetails.Any())
                    {
                        List<EscalationMatrix> lstEscalationMatrixs = EscDetails.ToList();

                        foreach (EscalationMatrix item in lstEscalationMatrixs)
                        {
                            objEntityReview.EscalationExist = "1";
                            objEntityReview.EscalationMatrixID = item.EscMatID;
                            objEntityReview.EscalationMapID = item.EscMapID;
                            objEntityReview.RecordID = Convert.ToInt32(RecordID);
                            objEntityReview.EscalationTime = item.EscTime;
                            objEntityReview.EscalationTimeUnit = item.EscTimeUnit;
                        }

                        if (!string.IsNullOrEmpty(Convert.ToString(objEntityReview.EscalationMapID)))
                        {
                            EscalationMatrix objEscalationMatrix2 = new EscalationMatrix();
                            objEscalationMatrix2.EscMapID = objEntityReview.EscalationMapID;
                            objEscalationMatrix2.UpdatedBy = _UserDetails.UserID;

                            SuccessUpdated = _ProcessSrv.EscalationMatrixMapping_Update_IsReviewed(objEscalationMatrix2);
                        }
                    }
                }

                if ((string.IsNullOrEmpty(objEntityReview.EscalationExist) ? 0 : Convert.ToInt32(objEntityReview.EscalationExist)) == 1)
                {
                    if (SuccessUpdated)
                    {
                        EscalationMatrix EscalationMatrix3 = new EscalationMatrix();

                        EscalationMatrix3.BCMEntityType = string.IsNullOrEmpty(Convert.ToString(EntityID)) ? "0" : Convert.ToString(EntityID);
                        EscalationMatrix3.BCMEntityID = string.IsNullOrEmpty(Convert.ToString(RecordID)) ? 0 : Convert.ToInt32(RecordID);
                        EscalationMatrix3.EscMatID = string.IsNullOrEmpty(Convert.ToString(objEntityReview.EscalationMatrixID)) ? 0 : objEntityReview.EscalationMatrixID;
                        EscalationMatrix3.IsActive = 1;
                        EscalationMatrix3.CreatedBy = _UserDetails.UserID;
                        EscalationMatrix3.EscMatType = ((int)BCPEnum.EscalationMatrixType.Review).ToString();
                        EscalationMatrix3.EscStartDate = Convert.ToDateTime(NextReviewDate);
                        EscalationMatrix3.EscTime = objEntityReview.EscalationTime;
                        EscalationMatrix3.EscTimeUnit = objEntityReview.EscalationTimeUnit;

                        int ires = _ProcessSrv.EscalationMatrixMapping_Save(EscalationMatrix3);

                        List<EscalationLevelConfig> lstEscalationLevelConfig = new List<EscalationLevelConfig>();
                        List<EscalationLevel> lstEscalationLevel = new List<EscalationLevel>();

                        lstEscalationLevelConfig = _ProcessSrv.EscalationLevelConfig_GetAllByMatrixID(string.IsNullOrEmpty(Convert.ToString(objEntityReview.EscalationMatrixID)) ? 0 : Convert.ToInt32(objEntityReview.EscalationMatrixID));
                        lstEscalationLevel = _ProcessSrv.EscalationLevel_GetAllByEscMatID(Convert.ToInt32(string.IsNullOrEmpty(Convert.ToString(objEntityReview.EscalationMatrixID)) ? 0 : Convert.ToInt32(objEntityReview.EscalationMatrixID)));

                        Save_DependentDataOfMatrixProfile(ires, lstEscalationLevelConfig, lstEscalationLevel, Convert.ToDateTime(NextReviewDate));

                        EscalationMatrix objEscalationMatrix4 = new EscalationMatrix();

                        List<EscalationMatrixFyi> lstEscalationMatrixFyi = _ProcessSrv.EscalationMatrixFyi_GetByMapID(Convert.ToInt32(objEntityReview.EscalationMapID));
                        if (lstEscalationMatrixFyi != null)
                        {
                            foreach (EscalationMatrixFyi item in lstEscalationMatrixFyi)
                            {
                                item.CreatedBy = _UserDetails.UserID;
                                item.EscMapID = ires;
                            }

                            bool Successs = _ProcessSrv.EscalationMatFyi_SaveColl(lstEscalationMatrixFyi);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = Success, message = Success ? "Review Ended." : "Failed to End Review." });
        }
        return Json(Success);
    }

    public void Save_DependentDataOfMatrixProfile(int iEscMatMapID, List<EscalationLevelConfig> lstEscalationLevelConfig, List<EscalationLevel> lstEscalationLevel, DateTime dateTime)
    {
        try
        {
            EntityReview objEntityReview = new EntityReview();

            int iLevelsSaved = 0;
            int iLevRes = 0;
            int iLevConRes = 0;

            //Method to Save Levels & resources for Approval & Review
            foreach (EscalationLevelConfig levConfig in lstEscalationLevelConfig)
            {
                levConfig.EscMatMapID = Convert.ToInt32(iEscMatMapID);

                levConfig.EscLevStatus = "1";
                levConfig.CreatedBy = _UserDetails.UserID;
                int count = 0;
                string EscalatedOndate = string.Empty;

                DateTime EscalatedOndate_new = AddtoDate(Convert.ToDateTime(dateTime.ToString()), Convert.ToInt32(levConfig.EscTime), Convert.ToInt32(levConfig.TimeUnit));

                levConfig.LevEscDate = EscalatedOndate_new.ToString();
                levConfig.IsApproved = 1;
                levConfig.IncidentStepID = 0;
                levConfig.IsLevelNotified = 0;
                iLevConRes = _ProcessSrv.EscalationLevConfigResult_Save(levConfig);

                var varEscLevelsList = from EscalationLevel levels in lstEscalationLevel
                                       where Convert.ToInt32(levels.EscLevConfigID) == Convert.ToInt32(levConfig.LevConID)
                                       select levels;

                foreach (EscalationLevel varEscLev in varEscLevelsList)
                {

                    varEscLev.EscLevConfigID = Convert.ToInt32(iLevConRes);
                    varEscLev.CreatedBy = _UserDetails.UserID;
                    varEscLev.IsApproved = 1;

                    if ((Convert.ToString(varEscLev.EntityID) == "1"))
                    {
                        varEscLev.FromTeamID = "-1";
                        iLevRes = _ProcessSrv.EscalationLevelsResult_Save(varEscLev);
                    }
                    else
                    {
                        int iTeamResid = 0;
                        while (count.Equals(0))
                        {
                            varEscLev.FromTeamID = "-1";
                            iTeamResid = Convert.ToInt32(varEscLev.ResourceID);

                            int iLevResTeam = _ProcessSrv.EscalationLevelsResult_Save(varEscLev);
                            count++;
                        }

                        varEscLev.FromTeamID = iTeamResid.ToString();

                        List<EscalationLevel> lstEscalLevel = new List<EscalationLevel>();
                        lstEscalLevel = _ProcessSrv.EscalationLevel_GetAllTeamMembers(iTeamResid);

                        varEscLev.EscLevConfigID = Convert.ToInt32(iLevConRes);
                        varEscLev.CreatedBy = _UserDetails.UserID;

                        foreach (EscalationLevel esclevelteam in lstEscalLevel)
                        {
                            varEscLev.EntityID = 1;
                            varEscLev.ResourceID = esclevelteam.ResourceID;
                            iLevelsSaved = _ProcessSrv.EscalationLevelsResult_Save(varEscLev);
                        }
                    }
                }

            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }

    public DateTime AddtoDate(DateTime dfinalDate, int iEndTime, int EUnit)
    {
        DateTime finalDate = Convert.ToDateTime("01/01/1990 00:00:00");
        if (icount == 0)
        {
            finalDate = dfinalDate;
            iETime = iEndTime;
            iEUnit = EUnit;

            icount++;
        }
        else
        {
            if (iEUnit.Equals(Convert.ToInt32("1")))
            {
                finalDate = dvalue.AddMinutes(Convert.ToInt32(iETime));
            }
            else if (iEUnit.Equals(Convert.ToInt32("2")))
            {
                finalDate = dvalue.AddHours(Convert.ToInt32(iETime));
            }
            else if (iEUnit.Equals("3"))
            {
                finalDate = dvalue.AddDays(Convert.ToInt32(iETime));
            }
        }

        dvalue = finalDate;
        iETime = iEndTime;
        iEUnit = EUnit;
        return finalDate;
    }

    private void GetUpdateReviewStatusofRACycle(EntityReview entityReview)
    {
        try
        {
            EntityReview objEntityReview = new EntityReview();

            objEntityReview.EntityID = entityReview.EntityID;
            objEntityReview.RecordID = entityReview.RecordID;
            objEntityReview.Status = "2";
            objEntityReview.NextReviewDate = entityReview.NextReviewDate;
            objEntityReview.ChangedBy = _UserDetails.UserID;

            _ProcessSrv.EntityReviewUpdateStatus(objEntityReview);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
    }

    #endregion

    public ActionResult btnBackClick(string strProcessID)
    {
        try
        {

        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }
        return RedirectToAction("PerformProcessBIA", "PerformProcessBIA", new { area = "BCMProcessBIA", strProcessID = strProcessID });
    }
}

