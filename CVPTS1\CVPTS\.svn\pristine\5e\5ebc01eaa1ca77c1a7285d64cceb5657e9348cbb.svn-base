﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc;
using System.Xml.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using static System.Runtime.InteropServices.JavaScript.JSType;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMAdministration.Controllers;

[Area("BCMAdministration")]
public class OrglevelDepDiagramController : BaseController
{
    private readonly Utilities _Utilities;
    private ProcessSrv _ProcessSrv;
    readonly CVLogger _CvLogger;

    public OrglevelDepDiagramController(Utilities Utilities, ProcessSrv iProcessSrv, CVLogger cVLogger, BCMMail BCMMail) : base(Utilities, cVLogger)
    {
        _ProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _CvLogger = cVLogger;
    }
    [HttpGet]
    public IActionResult OrglevelDepDiagram()
    {
        try
        {
            // _UserDetails is automatically initialized by BaseController.OnActionExecuting

            if (_UserDetails == null)
            {
                _CvLogger.LogErrorApp(new Exception("User details not found in session"));
                ViewBag.ChartData = new List<object>
                {
                    new
                    {
                        name = "Session expired - please login again",
                        fill = "#FF0000",
                        error = true,
                        level = "red",
                        children = new List<object>()
                    }
                };
                return View();
            }

            // Get the data and pass it to the view
            var chartDataResult = OrglevelDepDiagramData();
            var chartData = chartDataResult.Value;

            // Log what we're sending to the view
            _CvLogger.LogInfo($"Sending chart data to view: {JsonSerializer.Serialize(chartData)}");
            ViewBag.ChartData = chartData;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            // Create a simple fallback data structure in case of error
            ViewBag.ChartData = new List<object>
            {
                new
                {
                    name = "Error loading data: " + ex.Message,
                    fill = "#1B75BD",
                    error = true,
                    level = "red",
                    children = new List<object>()
                }
            };
        }

        return View();
    }




    // Helper class for chart node data
    private class ChartNode
    {
        public string name { get; set; }
        public string fill { get; set; }
        public bool error { get; set; }
        public string level { get; set; }
        public List<ChartNode> children { get; set; } = new List<ChartNode>();

        // Optional property for business processes
        public string subname { get; set; }
    }

    // Method to get business processes for a sub-department
    private List<ChartNode> GetBusinessProcesses(string subDepartmentId)
    {
        var businessProcesses = new List<ChartNode>();

        try
        {
            // Try to get real business processes from the database
            List<BusinessProcessInfo> realBusinessProcesses = GetRealBusinessProcesses(subDepartmentId);

            if (realBusinessProcesses != null && realBusinessProcesses.Any())
            {
                // Use real data if available
                realBusinessProcesses = realBusinessProcesses.Where(x => x.SubfunctionID == Convert.ToInt32(subDepartmentId)).ToList();
                foreach (BusinessProcessInfo bp in realBusinessProcesses)
                {

                    if (bp.SubfunctionID.ToString() == subDepartmentId)
                    {
                        businessProcesses.Add(new ChartNode
                        {
                            name = bp.ProcessName,
                            subname = "",
                            fill = bp.IsActive.ToString() == "1" ? "#28a745" : "#fd7e14", // Green for active, orange for inactive
                            error = false,
                            level = "green",
                            children = new List<ChartNode>()
                        });
                    }

                }
            }
            else
            {
                // Fallback to simulated data if no real data is available
                // Simulate 1-3 business processes per sub-department
                int count = new Random().Next(1, 4);
                for (int i = 1; i <= count; i++)
                {
                    businessProcesses.Add(new ChartNode
                    {
                        name = $"Business Process {i}",
                        subname = " ",
                        fill = "#008000", // Green for business processes
                        error = false,
                        level = "green",
                        children = new List<ChartNode>()
                    });
                }
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
        }

        return businessProcesses;
    }

    // Helper class for business process data
    private class BusinessProcess
    {
        public string Name { get; set; }
        public string Code { get; set; }
        public string Status { get; set; }
    }

    // Method to get real business processes from the database
    private List<BusinessProcessInfo> GetRealBusinessProcesses(string subDepartmentId)
    {
        try
        {

            //List<BCM.BusinessClasses.BusinessProcessInfo> lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);

            List<BusinessProcessInfo> lstBusinessProcess = _ProcessSrv.GetBusinessProcessMasterList(_UserDetails.OrgID);
            //lstBusinessProcess = GetBusinessProcess(lstBusinessProcess);
            //if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            //{
            //    if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
            //    {
            //        lstBusinessProcess = _Utilities.FilterListByOrgGroupID(lstBusinessProcess, _UserDetails.OrgGroupID);

            //    }
            //    else
            //    {

            //        lstBusinessProcess = _Utilities.FilterListByOrgID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
            //        lstBusinessProcess = _Utilities.FilterListByRoleID(lstBusinessProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
            //    }
            //}
            // This is where you would make a call to your database or service
            // to get the actual business processes for the given sub-department

            // For now, we'll return null to use the simulated data
            // In a real implementation, you would replace this with actual data retrieval

            // Example of how you might retrieve data:
            // return _ProcessSrv.GetBusinessProcessesBySubDepartmentId(subDepartmentId);

            return lstBusinessProcess;
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return null;
        }
    }

    public List<BusinessProcessInfo> GetBusinessProcess(List<BusinessProcessInfo> lstBusinessProcess)
    {
        try
        {
            lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
            if (_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole))
            {
                lstBusinessProcess = _Utilities.GetBusinessProcess(lstBusinessProcess, 0, 0, 0, 0, -1);
            }
            else
            {
                lstBusinessProcess = _Utilities.FilterListForOwner(lstBusinessProcess, 0, 0, 0, 0, Convert.ToInt32(BCPEnum.EntityType.BusinessProcess), -1);
            }

        }
        catch (Exception)
        {

            throw;
        }
        return lstBusinessProcess;
    }

    // Main method to generate the dependency matrix data
    public JsonResult OrglevelDepDiagramData()
    {
        var events = new List<object>();
        try
        {
            // Ensure user details are available
            if (_UserDetails == null)
            {
                _CvLogger.LogErrorApp(new Exception("User details are null in OrglevelDepDiagramData"));
                events.Add(new ChartNode
                {
                    name = "User session not found",
                    fill = "#FF0000",
                    error = true,
                    level = "red"
                });
                return Json(events);
            }

            // Log user details for debugging
            _CvLogger.LogInfo($"OrglevelDepDiagramData - User: {_UserDetails.UserName}, OrgID: {_UserDetails.OrgID}, OrgGroupID: {_UserDetails.OrgGroupID}");

            // Get organization data
            List<OrgInfo> lstOrgInfo = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());
            _CvLogger.LogInfo($"Organizations found: {lstOrgInfo?.Count ?? 0}");

            // Get units data
            List<OrgUnit> lstOrgUnits = _ProcessSrv.GetOrganizationUnitList_New(_UserDetails.OrgID);
            _CvLogger.LogInfo($"Units found: {lstOrgUnits?.Count ?? 0}");

            // Get departments data
            List<DepartmentInfo> lstDepartmentInfo = _ProcessSrv.GetDepartmentMasterAll();
            _CvLogger.LogInfo($"Departments found: {lstDepartmentInfo?.Count ?? 0}");

            // Get sub-departments data
            List<SubFunction> lstSubFunction = _ProcessSrv.GetSubFunctionList_ByOrgGroupID(_UserDetails.OrgGroupID);
            _CvLogger.LogInfo($"Sub-departments found: {lstSubFunction?.Count ?? 0}");
            // Build the hierarchical data structure for the dependency matrix
            _CvLogger.LogInfo($"Building hierarchy for {lstOrgInfo?.Count ?? 0} organizations");

            if (lstOrgInfo != null && lstOrgInfo.Any())
            {
                foreach (OrgInfo item in lstOrgInfo)
                {
                    _CvLogger.LogInfo($"Processing organization: {item.OrganizationName} (ID: {item.Id})");
                    var unitsForOrg = new List<ChartNode>();
                    if (item.Id == _UserDetails.OrgID.ToString())
                    {
                        _CvLogger.LogInfo($"Organization matches user's OrgID: {_UserDetails.OrgID}");
                        // Get units for this organization
                        var filteredUnits = lstOrgUnits?.Where(u => u.OrgID.ToString() == item.Id).ToList() ?? new List<OrgUnit>();
                        _CvLogger.LogInfo($"Found {filteredUnits.Count} units for organization {item.Id}");

                        foreach (OrgUnit orgUnit in filteredUnits)
                        {
                            _CvLogger.LogInfo($"Processing unit: {orgUnit.UnitName} (ID: {orgUnit.UnitID})");
                            var departmentsForUnit = new List<ChartNode>();

                            // Get departments for this unit
                            var filteredDepartments = lstDepartmentInfo?.Where(d => d.UnitID == orgUnit.UnitID).ToList() ?? new List<DepartmentInfo>();
                            _CvLogger.LogInfo($"Found {filteredDepartments.Count} departments for unit {orgUnit.UnitID}");

                            foreach (DepartmentInfo department in filteredDepartments)
                            {
                                _CvLogger.LogInfo($"Processing department: {department.DepartmentName} (ID: {department.DepartmentID})");
                                var subDepartmentsForDept = new List<ChartNode>();

                                // Get sub-departments for this department
                                var filteredSubDepts = lstSubFunction?.Where(s => s.FunctionId == department.DepartmentID.ToString()).ToList() ?? new List<SubFunction>();
                                _CvLogger.LogInfo($"Found {filteredSubDepts.Count} sub-departments for department {department.DepartmentID}");

                                foreach (SubFunction subDept in filteredSubDepts)
                                {
                                    _CvLogger.LogInfo($"Processing sub-department: {subDept.SubFunctionName} (ID: {subDept.SubFunctionID})");
                                    // Get business processes for this sub-department
                                    var businessProcesses = GetBusinessProcesses(subDept.SubFunctionID ?? "0");

                                    // Add sub-department to the list
                                    subDepartmentsForDept.Add(new ChartNode
                                    {
                                        name = subDept.SubFunctionName,
                                        fill = "#dc3545", // Red for sub-departments
                                        error = false,
                                        level = "red",
                                        children = businessProcesses
                                    });
                                }

                                // Add department to the list
                                departmentsForUnit.Add(new ChartNode
                                {
                                    name = department.DepartmentName,
                                    fill = "#fd7e14", // Orange for departments
                                    error = false,
                                    level = "orange",
                                    children = subDepartmentsForDept
                                });
                            }

                            // Add unit to the list
                            unitsForOrg.Add(new ChartNode
                            {
                                name = orgUnit.UnitName,
                                fill = "#ffc107", // Yellow for units
                                error = false,
                                level = "yellow",
                                children = departmentsForUnit
                            });
                        }

                        // Add organization to the main list
                        var orgNode = new ChartNode
                        {
                            name = item.OrganizationName ?? "Unknown Organization",
                            fill = "#007bff", // Blue for organizations
                            error = false,
                            level = "blue",
                            children = unitsForOrg
                        };
                        events.Add(orgNode);
                        _CvLogger.LogInfo($"Added organization '{orgNode.name}' with {unitsForOrg.Count} units to events");
                    }
                    else
                    {
                        _CvLogger.LogInfo($"Skipping organization {item.OrganizationName} (ID: {item.Id}) - doesn't match user's OrgID: {_UserDetails.OrgID}");
                    }
                }

                // If no organizations were added, add a fallback
                if (events.Count == 0)
                {
                    _CvLogger.LogWarning("No organizations found matching user criteria, adding fallback data");
                    events.Add(new ChartNode
                    {
                        name = "No Organizations Found",
                        fill = "#FF8C00",
                        error = false,
                        level = "yellow",
                        children = new List<ChartNode>()
                    });
                }
            }
            else
            {
                _CvLogger.LogWarning("No organizations found in database");
                events.Add(new ChartNode
                {
                    name = "No Organizations Available",
                    fill = "#FF0000",
                    error = true,
                    level = "red",
                    children = new List<ChartNode>()
                });
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);

            // Return a default structure if an error occurs
            events.Add(new ChartNode
            {
                name = "Error loading data",
                fill = "#1B75BD",
                error = true,
                level = "red"
            });
        }

        // Return the first organization or a default structure
        if (events.Count > 0)
        {
            //_CvLogger.LogInfo($"Returning organization data: {events[0].name} with {events[0].children.Count} units");
            return Json(events[0]); // Return single organization object, not array
        }
        else
        {
            _CvLogger.LogWarning("No events to return, creating default structure");
            return Json(new ChartNode
            {
                name = "No Data Available",
                fill = "#FF0000",
                error = true,
                level = "red",
                children = new List<ChartNode>()
            });
        }
    }



    // Method that follows the legacy pattern more closely
    [HttpGet]
    public JsonResult OrglevelDepDiagramDataLegacy()
    {
        try
        {
            if (_UserDetails == null)
            {
                return Json(new List<object>
                {
                    new { name = "User session not found", error = true, children = new List<object>() }
                });
            }

            // Get organization using legacy pattern
            var org = _ProcessSrv.GetOrganizationMasterByOrgId(Convert.ToString(_UserDetails.OrgID));

            if (org == null)
            {
                return Json(new List<object>
                {
                    new { name = "Organization not found", error = true, children = new List<object>() }
                });
            }

            var result = new List<object>();
            var orgChildren = new List<object>();

            // Get units for this organization
            var objOrgUnitColl = _ProcessSrv.GetOrganizationUnitList_New(_UserDetails.OrgID);

            if (objOrgUnitColl != null && objOrgUnitColl.Any())
            {
                foreach (var unit in objOrgUnitColl)
                {
                    var unitChildren = new List<object>();

                    // Get departments for this unit
                    var deptColl = _ProcessSrv.GetDepartmentByUnitId(unit.UnitID);

                    if (deptColl != null && deptColl.Any())
                    {
                        foreach (var dept in deptColl)
                        {
                            var deptChildren = new List<object>();

                            // Get sub-functions for this department
                            var subdeptColl = _ProcessSrv.GetSubFunctionListByFunctionID(Convert.ToString(dept.DepartmentID));

                            if (subdeptColl != null && subdeptColl.Any())
                            {
                                foreach (var subdept in subdeptColl)
                                {
                                    var subdeptChildren = new List<object>();

                                    // Get business processes for this sub-department
                                    // This would need the equivalent of GetFilteredBIAProcessDataTable
                                    // For now, using a simplified approach
                                    var businessProcesses = GetBusinessProcessesForSubDept(Convert.ToInt32(subdept.SubFunctionID));

                                    foreach (var process in businessProcesses)
                                    {
                                        subdeptChildren.Add(new
                                        {
                                            name = process.ProcessName,
                                            ImpactType = "Process",
                                            children = new List<object>() // Process dependencies would go here
                                        });
                                    }

                                    deptChildren.Add(new
                                    {
                                        name = subdept.SubFunctionName,
                                        ImpactType = "SubDept",
                                        children = subdeptChildren
                                    });
                                }
                            }

                            unitChildren.Add(new
                            {
                                name = dept.DepartmentName,
                                ImpactType = "Dept",
                                children = deptChildren
                            });
                        }
                    }

                    orgChildren.Add(new
                    {
                        name = unit.UnitName,
                        ImpactType = "Unit",
                        children = unitChildren
                    });
                }
            }

            result.Add(new
            {
                name = org.OrganizationName,
                ImpactType = "Org",
                children = orgChildren
            });

            return Json(result);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return Json(new List<object>
            {
                new { name = $"Error: {ex.Message}", error = true, children = new List<object>() }
            });
        }
    }

    // Helper method to get business processes for a sub-department
    private List<BusinessProcessInfo> GetBusinessProcessesForSubDept(int subFunctionId)
    {
        try
        {
            // This is a simplified version - you might need to implement the exact filtering logic
            // from the legacy GetFilteredBIAProcessDataTable method
            var allProcesses = _ProcessSrv.GetBusinessProcessList();
            return allProcesses?.Where(p => p.SubfunctionID == subFunctionId).ToList() ?? new List<BusinessProcessInfo>();
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            return new List<BusinessProcessInfo>();
        }
    }


}

