﻿
class WidgetManager {
    constructor() {
        this.widgetCounter = 0;
        this.selectedWidget = null;
        this.amCharts = {}; // Store amCharts instances
        this.intervals = {}; // Store intervals for dynamic widgets
        this.grid = null; // Gridstack instance
    }

    // Initialize Gridstack
    initializeGrid() {
        this.grid = GridStack.init({
            cellHeight: 70,
            margin: 8, // Small margin that won't cause overflow
            minRow: 1,
            animate: true,
            float: false,
            removable: false,
            resizable: {
                handles: 'se'
            },
            draggable: {
                handle: '.widget-header'
            },
            // Additional spacing configuration
            marginUnit: 'px',
            column: 12, // 12 column grid
            maxRow: 0, // Unlimited rows
            // Prevent horizontal scroll
            disableOneColumnMode: false,
            oneColumnModeDomSort: true,
            // Responsive settings
            alwaysShowResizeHandle: false
        });

        // Setup grid events
        this.setupGridEvents();
    }

    // Setup Gridstack events
    setupGridEvents() {
        this.grid.on('change', (event, items) => {
            // Auto-save after changes
            setTimeout(() => window.DashboardStorage.saveState(), 500);
        });

        this.grid.on('dragstart', (event, el) => {
            window.DashboardStorage.pushUndoState();
        });

        this.grid.on('resizestart', (event, el) => {
            window.DashboardStorage.pushUndoState();
        });

        this.grid.on('resizestop', (event, el) => {
            // Refresh amCharts after resize
            const $widget = $(el).find('.dashboard-widget');
            const type = $widget.data('widget-type');
            if (type && type.startsWith('amchart-')) {
                const amChartContainer = $widget.find('.amchart-container')[0];
                if (amChartContainer && this.amCharts[amChartContainer.id]) {
                    setTimeout(() => {
                        const chartData = this.amCharts[amChartContainer.id];
                        if (chartData.root) {
                            chartData.root.resize();
                        }
                    }, 100);
                }
            }
        });

        this.grid.on('dragstop', (event, el) => {
            // Reinitialize amCharts after drag
            const $widget = $(el).find('.dashboard-widget');
            const type = $widget.data('widget-type');
            if (type && type.startsWith('amchart-')) {
                setTimeout(() => {
                    this.reinitializeAmChart($widget, type);
                }, 200);
            }
        });
    }

    // Create a new widget
    createWidget(type, title, gridOptions = null, dataIndex = null, id = null) {
        console.log('=== CREATE WIDGET START ===');
        console.log('Type:', type);
        console.log('Title:', title);
        console.log('Grid Options:', gridOptions);
        console.log('Data Index:', dataIndex);
        console.log('ID:', id);

        const widgetId = id || `widget-${++this.widgetCounter}`;
        const defaultGridOptions = gridOptions || this.getDefaultGridSize(type);

        console.log('Widget ID:', widgetId);
        console.log('Default Grid Options:', defaultGridOptions);
        console.log('Grid instance:', this.grid);

        // Save state for undo
        if (window.DashboardStorage && typeof window.DashboardStorage.pushUndoState === 'function') {
            window.DashboardStorage.pushUndoState();
        } else {
            console.warn('DashboardStorage.pushUndoState not available');
        }

        // Get widget content first
        const contentHtml = this.getWidgetContent(type, dataIndex);
        console.log('Widget content HTML for type', type, ':', contentHtml);

        if (!contentHtml || contentHtml.trim() === '') {
            console.error('Widget content is empty for type:', type);
            return null;
        }

        const widgetContent = $(`
            <div class="dashboard-widget widget-fade-in"
                 data-widget-type="${type}" data-widget-data="${dataIndex || 0}">
                <div class="widget-header">
                    <h5>${title}</h5>
                </div>
                <div class="widget-content">
                    ${contentHtml}
                </div>
                <div class="widget-toolbar">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-light widget-duplicate" title="Duplicate">
                            <i class="fas fa-clone"></i>
                        </button>
                        <button type="button" class="btn btn-outline-light widget-copy" title="Copy">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger widget-delete" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `);

        console.log('Created widget element:', widgetContent.length);
        console.log('Widget element HTML:', widgetContent[0].outerHTML);

        // Add widget to grid
        const gridItem = this.grid.addWidget(widgetContent[0], {
            id: widgetId,
            x: defaultGridOptions.x,
            y: defaultGridOptions.y,
            w: defaultGridOptions.w,
            h: defaultGridOptions.h
        });

        const $gridItem = $(gridItem);
        let $widget = $gridItem.find('.dashboard-widget');

        // If widget not found in grid item, the widget might be the grid item content itself
        if ($widget.length === 0) {
            $widget = $gridItem.children('.dashboard-widget');
        }

        // If still not found, check if the grid item contains the widget directly
        if ($widget.length === 0) {
            $widget = $gridItem.hasClass('dashboard-widget') ? $gridItem : $gridItem.find('[data-widget-type]');
        }

        console.log('Grid item:', $gridItem[0]);
        console.log('Found widget:', $widget.length, $widget[0]);

        // For amChart widgets, wait for grid to fully render before initializing
        if (type.startsWith('amchart-')) {
            // Use requestAnimationFrame to ensure DOM is fully rendered
            requestAnimationFrame(() => {
                setTimeout(() => {
                    if ($widget.length > 0) {
                        this.initializeWidget($widget, type, dataIndex);
                    } else {
                        console.error('Widget element not found for initialization');
                    }
                }, 100);
            });
        } else {
            // Initialize other widgets immediately
            if ($widget.length > 0) {
                this.initializeWidget($widget, type, dataIndex);
            } else {
                console.error('Widget element not found for initialization');
            }
        }

        console.log('=== CREATE WIDGET END ===');
        console.log('Returning widget:', $widget.length > 0 ? $widget[0] : 'null');
        return $widget;
    }

    // Get default grid size for widget type
    getDefaultGridSize(type) {
        const sizes = {
            'kpi': { x: 0, y: 0, w: 3, h: 3 },
            'table': { x: 0, y: 0, w: 8, h: 4 },
            'text': { x: 0, y: 0, w: 6, h: 3 },
            'progress': { x: 0, y: 0, w: 4, h: 4 },
            'amchart-line': { x: 0, y: 0, w: 6, h: 4 },
            'amchart-column': { x: 0, y: 0, w: 6, h: 4 },
            'amchart-pie': { x: 0, y: 0, w: 4, h: 4 },
            'amchart-radar': { x: 0, y: 0, w: 4, h: 4 },
            'amchart-area': { x: 0, y: 0, w: 6, h: 4 },
            'amchart-scatter': { x: 0, y: 0, w: 5, h: 4 },
            'weather': { x: 0, y: 0, w: 4, h: 5 },
            'calendar': { x: 0, y: 0, w: 5, h: 6 },
            'clock': { x: 0, y: 0, w: 3, h: 3 },
            'gauge': { x: 0, y: 0, w: 4, h: 4 },
            'map': { x: 0, y: 0, w: 6, h: 5 },
            'news': { x: 0, y: 0, w: 5, h: 6 },
            'social': { x: 0, y: 0, w: 4, h: 5 }
        };
        return sizes[type] || { x: 0, y: 0, w: 4, h: 3 };
    }

    // Get widget content based on type
    getWidgetContent(type, dataIndex) {
        console.log('Creating widget content for type:', type, 'dataIndex:', dataIndex);

        let content = '';
        switch (type) {
            case 'kpi':
                content = this.createKPIContent(dataIndex);
                break;
            case 'table':
                content = this.createTableContent();
                break;
            case 'text':
                content = this.createTextContent(dataIndex);
                break;
            case 'progress':
                content = this.createProgressContent();
                break;
            case 'amchart-line':
            case 'amchart-column':
            case 'amchart-pie':
            case 'amchart-radar':
            case 'amchart-area':
            case 'amchart-scatter':
                content = this.createAmChartContent(type);
                break;
            case 'weather':
                content = this.createWeatherContent();
                break;
            case 'calendar':
                content = this.createCalendarContent();
                break;
            case 'clock':
                content = this.createClockContent();
                break;
            case 'gauge':
                content = this.createGaugeContent();
                break;
            case 'map':
                content = this.createMapContent();
                break;
            case 'news':
                content = this.createNewsContent();
                break;
            case 'social':
                content = this.createSocialContent();
                break;
            default:
                content = '<p>Unknown widget type: ' + type + '</p>';
                break;
        }

        console.log('Generated content length:', content.length);
        return content;
    }

    // Create KPI widget content
    createKPIContent(dataIndex) {
        const kpi = SampleData.kpis[dataIndex] || SampleData.kpis[0];
        return `
            <div class="kpi-widget">
                <div class="kpi-icon">
                    <i class="${kpi.icon}"></i>
                </div>
                <div class="kpi-value">${kpi.value}</div>
                <div class="kpi-label">${kpi.title}</div>
                <div class="kpi-change ${kpi.changeType}">
                    <i class="fas fa-arrow-${kpi.changeType === 'positive' ? 'up' : 'down'}"></i>
                    ${kpi.change}
                </div>
            </div>
        `;
    }



    // Create table content
    createTableContent() {
        const data = SampleData.tableData;
        let tableHTML = `
            <div class="table-widget">
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        data.forEach(row => {
            const statusClass = row.status === 'Active' ? 'success' :
                row.status === 'Inactive' ? 'danger' : 'warning';
            tableHTML += `
                <tr>
                    <td>${row.id}</td>
                    <td>${row.name}</td>
                    <td>${row.email}</td>
                    <td><span class="badge bg-${statusClass}">${row.status}</span></td>
                    <td><strong>${row.revenue}</strong></td>
                </tr>
            `;
        });

        tableHTML += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        return tableHTML;
    }

    // Create text content
    createTextContent(dataIndex) {
        const textKey = dataIndex || 'welcome';
        const content = SampleData.textContent[textKey] || SampleData.textContent.welcome;
        return `
            <div class="text-widget">
                <h3>${content.title}</h3>
                ${content.content}
            </div>
        `;
    }

    // Create progress content
    createProgressContent() {
        const data = SampleData.progressData;
        let progressHTML = '<div class="progress-widget">';

        data.forEach(item => {
            progressHTML += `
                <div class="progress-item">
                    <div class="progress-label">
                        <span>${item.label}</span>
                        <span>${item.value}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-${item.color}" 
                             style="width: ${item.value}%" 
                             role="progressbar" 
                             aria-valuenow="${item.value}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                        </div>
                    </div>
                </div>
            `;
        });

        progressHTML += '</div>';
        return progressHTML;
    }

    // Create amChart content
    createAmChartContent(type) {
        const chartId = `amchart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const content = `<div class="amchart-container" id="${chartId}" style="width: 100%; height: 100%; min-height: 250px;"></div>`;
        console.log('Created amChart content:', content);
        return content;
    }

    // Create weather widget content
    createWeatherContent() {
        const weather = SampleData.weatherData;
        return `
            <div class="weather-widget">
                <div class="weather-header">
                    <h4>${weather.location}</h4>
                    <div class="weather-main">
                        <div class="weather-temp">${weather.temperature}°F</div>
                        <div class="weather-icon">
                            <i class="${weather.icon}"></i>
                        </div>
                    </div>
                    <div class="weather-condition">${weather.condition}</div>
                </div>
                <div class="weather-details">
                    <div class="weather-detail">
                        <i class="fas fa-tint"></i>
                        <span>Humidity: ${weather.humidity}%</span>
                    </div>
                    <div class="weather-detail">
                        <i class="fas fa-wind"></i>
                        <span>Wind: ${weather.windSpeed} mph</span>
                    </div>
                </div>
                <div class="weather-forecast">
                    ${weather.forecast.map(day => `
                        <div class="forecast-day">
                            <div class="forecast-day-name">${day.day}</div>
                            <i class="${day.icon}"></i>
                            <div class="forecast-temps">${day.high}°/${day.low}°</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Create calendar widget content
    createCalendarContent() {
        const calendar = SampleData.calendarData;
        const today = new Date();
        const currentMonth = today.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

        return `
            <div class="calendar-widget">
                <div class="calendar-header">
                    <h4>${currentMonth}</h4>
                </div>
                <div class="calendar-grid">
                    <div class="calendar-day-header">Sun</div>
                    <div class="calendar-day-header">Mon</div>
                    <div class="calendar-day-header">Tue</div>
                    <div class="calendar-day-header">Wed</div>
                    <div class="calendar-day-header">Thu</div>
                    <div class="calendar-day-header">Fri</div>
                    <div class="calendar-day-header">Sat</div>
                    ${this.generateCalendarDays(today)}
                </div>
                <div class="calendar-events">
                    <h5>Upcoming Events</h5>
                    ${calendar.events.map(event => `
                        <div class="calendar-event">
                            <div class="event-time">${event.time}</div>
                            <div class="event-title">${event.title}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Create clock widget content
    createClockContent() {
        const now = new Date();
        return `
            <div class="clock-widget">
                <div class="digital-clock">
                    <div class="clock-time" id="clock-time-${Date.now()}">
                        ${now.toLocaleTimeString()}
                    </div>
                    <div class="clock-date">
                        ${now.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        })}
                    </div>
                </div>
                <div class="clock-timezone">
                    <small>Local Time</small>
                </div>
            </div>
        `;
    }

    // Create gauge widget content
    createGaugeContent() {
        const gauges = SampleData.gaugeData;
        return `
            <div class="gauge-widget">
                ${gauges.map(gauge => `
                    <div class="gauge-item">
                        <div class="gauge-label">${gauge.label}</div>
                        <div class="gauge-container">
                            <div class="gauge-bar">
                                <div class="gauge-fill" style="width: ${gauge.value}%; background-color: ${gauge.color};"></div>
                            </div>
                            <div class="gauge-value">${gauge.value}%</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    // Create map widget content
    createMapContent() {
        return `
            <div class="map-widget">
                <div class="map-container" id="map-${Date.now()}" style="width: 100%; height: 100%;">
                    <div class="map-placeholder">
                        <i class="fas fa-map-marked-alt"></i>
                        <p>Interactive Map</p>
                        <small>Click to view locations</small>
                    </div>
                </div>
            </div>
        `;
    }

    // Create news widget content
    createNewsContent() {
        const news = SampleData.newsData;
        return `
            <div class="news-widget">
                <div class="news-header">
                    <h4><i class="fas fa-newspaper me-2"></i>Latest News</h4>
                </div>
                <div class="news-list">
                    ${news.map(article => `
                        <div class="news-item">
                            <div class="news-category">${article.category}</div>
                            <h5 class="news-title">${article.title}</h5>
                            <p class="news-summary">${article.summary}</p>
                            <div class="news-time">
                                <i class="fas fa-clock me-1"></i>${article.time}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Create social media widget content
    createSocialContent() {
        const social = SampleData.socialData;
        return `
            <div class="social-widget">
                <div class="social-header">
                    <h4><i class="fas fa-share-alt me-2"></i>Social Media</h4>
                </div>
                <div class="social-stats">
                    <div class="social-stat">
                        <div class="stat-value">${social.followers.toLocaleString()}</div>
                        <div class="stat-label">Followers</div>
                    </div>
                    <div class="social-stat">
                        <div class="stat-value">${social.likes.toLocaleString()}</div>
                        <div class="stat-label">Likes</div>
                    </div>
                    <div class="social-stat">
                        <div class="stat-value">${social.engagement}%</div>
                        <div class="stat-label">Engagement</div>
                    </div>
                </div>
                <div class="social-posts">
                    <h5>Recent Posts</h5>
                    ${social.recentPosts.map(post => `
                        <div class="social-post">
                            <div class="post-platform">${post.platform}</div>
                            <div class="post-content">${post.content}</div>
                            <div class="post-stats">
                                <span><i class="fas fa-heart"></i> ${post.likes}</span>
                                <span class="post-time">${post.time}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Helper method to generate calendar days
    generateCalendarDays(date) {
        const year = date.getFullYear();
        const month = date.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        let days = '';
        for (let i = 0; i < 42; i++) {
            const currentDate = new Date(startDate);
            currentDate.setDate(startDate.getDate() + i);
            const isCurrentMonth = currentDate.getMonth() === month;
            const isToday = currentDate.toDateString() === new Date().toDateString();

            days += `<div class="calendar-day ${isCurrentMonth ? 'current-month' : 'other-month'} ${isToday ? 'today' : ''}">
                ${currentDate.getDate()}
            </div>`;
        }
        return days;
    }

    // Initialize widget functionality
    initializeWidget($widget, type, dataIndex) {
        const widgetId = $widget.closest('.grid-stack-item').attr('gs-id') || $widget.attr('id');

        // Initialize special widgets after DOM is ready
        if (type.startsWith('amchart-')) {
            // amCharts - ensure container exists and widget is properly rendered
            console.log('Initializing amChart widget...');
            console.log('Widget element:', $widget[0]);
            console.log('Widget content:', $widget.find('.widget-content').html());

            // Wait for widget to be properly rendered in the grid
            this.waitForWidgetToBeReady($widget, type, dataIndex);
        } else if (type === 'clock') {
            // Digital clock
            setTimeout(() => {
                this.initializeClock($widget);
            }, 100);
        } else if (type === 'map') {
            // Map widget
            setTimeout(() => {
                this.initializeMap($widget);
            }, 200);
        }

        // Widget selection
        $widget.on('click', (e) => {
            e.stopPropagation();
            this.selectWidget(widgetId);
        });

        // Toolbar events
        $widget.find('.widget-duplicate').on('click', (e) => {
            e.stopPropagation();
            this.duplicateWidget(widgetId);
        });
        $widget.find('.widget-copy').on('click', (e) => {
            e.stopPropagation();
            window.DashboardStorage.copyWidget(widgetId);
        });
        $widget.find('.widget-delete').on('click', (e) => {
            e.stopPropagation();
            this.deleteWidget(widgetId);
        });
    }

    // Wait for widget to be ready for chart initialization (legacy method)
    waitForWidgetReady($widget, callback, maxAttempts = 20) {
        let attempts = 0;

        const checkReady = () => {
            attempts++;
            const container = $widget.find('.amchart-container')[0];

            if (!container) {
                console.error('amChart container not found');
                return;
            }

            // Check if container is visible and has dimensions
            const isVisible = container.offsetWidth > 0 && container.offsetHeight > 0;
            const isInDOM = document.contains(container);

            console.log(`Attempt ${attempts}: Container visible: ${isVisible}, In DOM: ${isInDOM}, Dimensions: ${container.offsetWidth}x${container.offsetHeight}`);

            if (isVisible && isInDOM) {
                console.log('Widget ready, initializing chart...');
                callback();
            } else if (attempts < maxAttempts) {
                setTimeout(checkReady, 100);
            } else {
                console.error('Widget failed to become ready after', maxAttempts, 'attempts');
                // Try to force initialization anyway
                callback();
            }
        };

        // Start checking after a small delay
        setTimeout(checkReady, 50);
    }

    // Wait for dynamically added widget to be ready
    waitForWidgetToBeReady($widget, type, dataIndex, maxAttempts = 30) {
        let attempts = 0;

        const checkAndInitialize = () => {
            attempts++;
            console.log(`Checking widget readiness, attempt ${attempts}...`);

            // Check if widget exists and is in DOM
            if (!$widget || $widget.length === 0) {
                console.error('Widget element not found');
                return;
            }

            const widgetElement = $widget[0];
            if (!widgetElement) {
                console.error('Widget DOM element is null');
                return;
            }

            const isInDOM = document.contains(widgetElement);
            const hasSize = widgetElement.offsetWidth > 0 && widgetElement.offsetHeight > 0;

            console.log(`Widget in DOM: ${isInDOM}, Has size: ${hasSize} (${widgetElement.offsetWidth}x${widgetElement.offsetHeight})`);

            // Check if container exists
            let $container = $widget.find('.amchart-container');
            if ($container.length === 0) {
                console.warn('amChart container missing, recreating...');
                const amChartContent = this.createAmChartContent(type);
                $widget.find('.widget-content').html(amChartContent);
                $container = $widget.find('.amchart-container');
            }

            const containerElement = $container[0];
            const containerHasSize = containerElement && containerElement.offsetWidth > 0 && containerElement.offsetHeight > 0;

            console.log(`Container exists: ${!!containerElement}, Container has size: ${containerHasSize}`);

            if (isInDOM && hasSize && containerElement && containerHasSize) {
                console.log('Widget is ready, initializing chart...');
                this.initializeAmChart($widget, type, dataIndex);
            } else if (attempts < maxAttempts) {
                setTimeout(checkAndInitialize, 100);
            } else {
                console.error('Widget failed to become ready after', maxAttempts, 'attempts, forcing initialization...');
                // Force initialization anyway
                this.initializeAmChart($widget, type, dataIndex);
            }
        };

        // Start checking immediately
        checkAndInitialize();
    }



    // Initialize amChart
    initializeAmChart($widget, type, dataIndex, retryCount = 0) {
        console.log('Attempting to initialize amChart for type:', type, 'retry:', retryCount);

        // Prevent infinite retries
        if (retryCount > 3) {
            console.error('Max retries reached for amChart initialization');
            return;
        }

        // Check if amCharts libraries are loaded
        if (typeof am5 === 'undefined') {
            console.error('amCharts 5 core is not loaded');
            return;
        }

        if (typeof am5xy === 'undefined') {
            console.error('amCharts 5 XY module is not loaded');
            return;
        }

        if (typeof am5percent === 'undefined') {
            console.error('amCharts 5 Percent module is not loaded');
            return;
        }

        if (typeof am5themes_Animated === 'undefined') {
            console.error('amCharts 5 Animated theme is not loaded');
            return;
        }

        // Check for radar module if needed
        if (type === 'amchart-radar' && typeof am5radar === 'undefined') {
            console.error('amCharts 5 Radar module is not loaded');
            return;
        }

        // Find amChart container - try multiple approaches
        let $container = $widget.find('.amchart-container');
        console.log('amChart container jQuery object:', $container.length);
        console.log('Widget HTML:', $widget.html());
        console.log('Widget content HTML:', $widget.find('.widget-content').html());

        // If container not found, try finding in the grid item
        if ($container.length === 0) {
            const $gridItem = $widget.closest('.grid-stack-item');
            $container = $gridItem.find('.amchart-container');
            console.log('Container found in grid item:', $container.length);
        }

        // If still not found, force recreate the amChart content
        if ($container.length === 0) {
            console.log('Container not found, force recreating amChart content...');
            const amChartContent = this.createAmChartContent(type);
            console.log('New amChart content:', amChartContent);

            // Clear and set new content
            const $widgetContent = $widget.find('.widget-content');
            $widgetContent.empty();
            $widgetContent.html(amChartContent);

            // Force DOM update and wait longer for grid to settle
            setTimeout(() => {
                $container = $widget.find('.amchart-container');
                console.log('Container after forced recreation:', $container.length);

                if ($container.length === 0) {
                    console.error('Still failed to create amChart container after recreation');
                    console.log('Widget content after recreation:', $widgetContent.html());
                    console.log('Full widget HTML:', $widget[0].outerHTML);

                    // Last resort: try one more time with longer delay
                    if (retryCount < 2) {
                        setTimeout(() => {
                            this.initializeAmChart($widget, type, dataIndex, retryCount + 1);
                        }, 500);
                    }
                    return;
                }

                // Continue with initialization
                this.continueAmChartInit($widget, type, dataIndex, retryCount, $container[0]);
            }, 100);
            return;
        }

        // Continue with normal initialization
        this.continueAmChartInit($widget, type, dataIndex, retryCount, $container[0]);
    }

    // Continue amChart initialization with container
    continueAmChartInit($widget, type, dataIndex, retryCount, container) {
        console.log('Continuing amChart initialization...');
        console.log('Container element:', container);
        console.log('Container ID:', container.id);

        // Dispose existing chart if it exists
        if (this.amCharts[container.id]) {
            console.log('Disposing existing chart:', container.id);
            this.amCharts[container.id].root.dispose();
            delete this.amCharts[container.id];
        }

        const chartType = type.replace('amchart-', '');

        try {
            // Ensure container has dimensions
            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn('Container has no dimensions, setting defaults and forcing layout');
                container.style.width = '100%';
                container.style.height = '300px';
                container.style.minHeight = '250px';
                container.style.display = 'block';

                // Force a reflow
                container.offsetHeight;

                // Wait a bit more for layout to settle
                setTimeout(() => {
                    this.initializeAmChart($widget, type, dataIndex, retryCount + 1);
                }, 200);
                return;
            }

            console.log('Container dimensions:', container.offsetWidth, 'x', container.offsetHeight);

            // Create root element
            const root = am5.Root.new(container.id);

            // Set themes
            root.setThemes([am5themes_Animated.new(root)]);

            let chart;

            switch (chartType) {
                case 'line':
                    chart = this.createAmLineChart(root);
                    break;
                case 'column':
                    chart = this.createAmColumnChart(root);
                    break;
                case 'pie':
                    chart = this.createAmPieChart(root);
                    break;
                case 'radar':
                    chart = this.createAmRadarChart(root);
                    break;
                case 'area':
                    chart = this.createAmAreaChart(root);
                    break;
                case 'scatter':
                    chart = this.createAmScatterChart(root);
                    break;
                default:
                    console.error('Unknown chart type:', chartType);
                    return;
            }

            this.amCharts[container.id] = { root, chart, type: chartType };
            console.log('amChart initialized successfully:', container.id);

        } catch (error) {
            console.error('Error initializing amChart:', error);
            // Retry with backoff
            if (retryCount < 3) {
                setTimeout(() => {
                    console.log('Retrying amChart initialization...');
                    this.initializeAmChart($widget, type, dataIndex, retryCount + 1);
                }, 1000 * (retryCount + 1));
            }
        }
    }

    // Initialize all amCharts on the dashboard
    initializeAllAmCharts() {
        $('.dashboard-widget[data-widget-type^="amchart-"]').each((index, element) => {
            const $widget = $(element);
            const type = $widget.data('widget-type');
            setTimeout(() => {
                this.initializeAmChart($widget, type, null);
            }, index * 200); // Stagger initialization
        });
    }

    // Debug method to check amChart status
    debugAmCharts() {
        console.log('=== amCharts Debug Info ===');
        console.log('amCharts instances:', Object.keys(this.amCharts).length);
        console.log('amChart containers on page:', $('.amchart-container').length);

        $('.amchart-container').each((index, container) => {
            console.log(`Container ${index}:`, {
                id: container.id,
                dimensions: `${container.offsetWidth}x${container.offsetHeight}`,
                visible: container.offsetWidth > 0 && container.offsetHeight > 0,
                hasChart: !!this.amCharts[container.id]
            });
        });

        console.log('amCharts library status:', {
            am5: typeof am5 !== 'undefined',
            am5xy: typeof am5xy !== 'undefined',
            am5percent: typeof am5percent !== 'undefined',
            am5radar: typeof am5radar !== 'undefined',
            am5themes_Animated: typeof am5themes_Animated !== 'undefined'
        });
    }

    // Reinitialize amChart after drag (fixes drag and drop issue)
    reinitializeAmChart($widget, type) {
        const container = $widget.find('.amchart-container')[0];
        if (!container) return;

        // Small delay to ensure DOM is settled after drag
        setTimeout(() => {
            this.initializeAmChart($widget, type, null);
        }, 100);
    }

    // Create amCharts line chart
    createAmLineChart(root) {
        try {
            const chart = root.container.children.push(am5xy.XYChart.new(root, {
                panX: true,
                panY: true,
                wheelX: "panX",
                wheelY: "zoomX",
                layout: root.verticalLayout
            }));

            const data = [
                { month: "Jan", sales: 12, profit: 8 },
                { month: "Feb", sales: 19, profit: 15 },
                { month: "Mar", sales: 3, profit: 2 },
                { month: "Apr", sales: 5, profit: 4 },
                { month: "May", sales: 2, profit: 1 },
                { month: "Jun", sales: 3, profit: 2 }
            ];

            const xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
                categoryField: "month",
                renderer: am5xy.AxisRendererX.new(root, {
                    minGridDistance: 30
                })
            }));

            const yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                renderer: am5xy.AxisRendererY.new(root, {})
            }));

            const series = chart.series.push(am5xy.LineSeries.new(root, {
                name: "Sales",
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: "sales",
                categoryXField: "month",
                tooltip: am5.Tooltip.new(root, {
                    labelText: "{valueY}"
                })
            }));

            series.strokes.template.setAll({
                strokeWidth: 2
            });

            xAxis.data.setAll(data);
            series.data.setAll(data);

            // Make stuff animate on load
            series.appear(1000);
            chart.appear(1000, 100);

            return chart;
        } catch (error) {
            console.error('Error creating line chart:', error);
            return null;
        }
    }

    // Create amCharts column chart
    createAmColumnChart(root) {
        try {
            const chart = root.container.children.push(am5xy.XYChart.new(root, {
                panX: false,
                panY: false,
                wheelX: "none",
                wheelY: "none",
                layout: root.verticalLayout
            }));

            const data = [
                { quarter: "Q1", revenue: 65 },
                { quarter: "Q2", revenue: 59 },
                { quarter: "Q3", revenue: 80 },
                { quarter: "Q4", revenue: 81 }
            ];

            const xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
                categoryField: "quarter",
                renderer: am5xy.AxisRendererX.new(root, {
                    minGridDistance: 30
                })
            }));

            const yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                renderer: am5xy.AxisRendererY.new(root, {})
            }));

            const series = chart.series.push(am5xy.ColumnSeries.new(root, {
                name: "Revenue",
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: "revenue",
                categoryXField: "quarter",
                tooltip: am5.Tooltip.new(root, {
                    labelText: "{valueY}"
                })
            }));

            series.columns.template.setAll({
                cornerRadiusTL: 5,
                cornerRadiusTR: 5,
                strokeOpacity: 0
            });

            xAxis.data.setAll(data);
            series.data.setAll(data);

            // Make stuff animate on load
            series.appear(1000);
            chart.appear(1000, 100);

            return chart;
        } catch (error) {
            console.error('Error creating column chart:', error);
            return null;
        }
    }

    // Create amCharts pie chart
    createAmPieChart(root) {
        try {
            const chart = root.container.children.push(am5percent.PieChart.new(root, {
                layout: root.verticalLayout
            }));

            const series = chart.series.push(am5percent.PieSeries.new(root, {
                valueField: "value",
                categoryField: "category",
                tooltip: am5.Tooltip.new(root, {
                    labelText: "{category}: {valuePercentTotal.formatNumber('#.0')}%"
                })
            }));

            const data = [
                { category: "Desktop", value: 55 },
                { category: "Mobile", value: 35 },
                { category: "Tablet", value: 10 }
            ];

            series.slices.template.setAll({
                strokeWidth: 3,
                stroke: am5.color("#ffffff")
            });

            series.labelsContainer.set("paddingTop", 30);

            // Add legend
            const legend = chart.children.push(am5.Legend.new(root, {
                centerX: am5.percent(50),
                x: am5.percent(50),
                marginTop: 15,
                marginBottom: 15
            }));

            legend.data.setAll(series.dataItems);

            series.data.setAll(data);

            // Make stuff animate on load
            series.appear(1000, 100);

            return chart;
        } catch (error) {
            console.error('Error creating pie chart:', error);
            return null;
        }
    }

    // Create amCharts radar chart
    createAmRadarChart(root) {
        try {
            if (typeof am5radar === 'undefined') {
                console.error('am5radar module not available, creating column chart instead');
                return this.createAmColumnChart(root);
            }

            const chart = root.container.children.push(am5radar.RadarChart.new(root, {
                panX: false,
                panY: false,
                wheelX: "none",
                wheelY: "none"
            }));

            const data = [
                { category: "Performance", value: 80 },
                { category: "Reliability", value: 90 },
                { category: "Usability", value: 75 },
                { category: "Security", value: 85 },
                { category: "Scalability", value: 70 }
            ];

            const xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
                categoryField: "category",
                renderer: am5radar.AxisRendererCircular.new(root, {})
            }));

            const yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                renderer: am5radar.AxisRendererRadial.new(root, {})
            }));

            const series = chart.series.push(am5radar.RadarLineSeries.new(root, {
                name: "Metrics",
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: "value",
                categoryXField: "category",
                tooltip: am5.Tooltip.new(root, {
                    labelText: "{category}: {valueY}"
                })
            }));

            xAxis.data.setAll(data);
            series.data.setAll(data);

            // Make stuff animate on load
            series.appear(1000);
            chart.appear(1000, 100);

            return chart;
        } catch (error) {
            console.error('Error creating radar chart:', error);
            return this.createAmColumnChart(root);
        }
    }

    // Create amCharts area chart
    createAmAreaChart(root) {
        try {
            const chart = root.container.children.push(am5xy.XYChart.new(root, {
                panX: true,
                panY: true,
                wheelX: "panX",
                wheelY: "zoomX",
                layout: root.verticalLayout
            }));

            const data = [
                { month: "Jan", value1: 12, value2: 8 },
                { month: "Feb", value1: 19, value2: 15 },
                { month: "Mar", value1: 15, value2: 12 },
                { month: "Apr", value1: 25, value2: 18 },
                { month: "May", value1: 22, value2: 16 },
                { month: "Jun", value1: 30, value2: 22 }
            ];

            const xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
                categoryField: "month",
                renderer: am5xy.AxisRendererX.new(root, {
                    minGridDistance: 30
                })
            }));

            const yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                renderer: am5xy.AxisRendererY.new(root, {})
            }));

            const series1 = chart.series.push(am5xy.LineSeries.new(root, {
                name: "Series 1",
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: "value1",
                categoryXField: "month",
                fill: am5.color("#3b82f6"),
                tooltip: am5.Tooltip.new(root, {
                    labelText: "Series 1: {valueY}"
                })
            }));

            series1.fills.template.setAll({
                fillOpacity: 0.3,
                visible: true
            });

            const series2 = chart.series.push(am5xy.LineSeries.new(root, {
                name: "Series 2",
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: "value2",
                categoryXField: "month",
                fill: am5.color("#ef4444"),
                tooltip: am5.Tooltip.new(root, {
                    labelText: "Series 2: {valueY}"
                })
            }));

            series2.fills.template.setAll({
                fillOpacity: 0.3,
                visible: true
            });

            xAxis.data.setAll(data);
            series1.data.setAll(data);
            series2.data.setAll(data);

            // Make stuff animate on load
            series1.appear(1000);
            series2.appear(1000);
            chart.appear(1000, 100);

            return chart;
        } catch (error) {
            console.error('Error creating area chart:', error);
            return null;
        }
    }

    // Create amCharts scatter chart
    createAmScatterChart(root) {
        try {
            const chart = root.container.children.push(am5xy.XYChart.new(root, {
                panX: true,
                panY: true,
                wheelX: "panX",
                wheelY: "zoomX",
                layout: root.verticalLayout
            }));

            const data = [
                { x: 10, y: 14, size: 5 },
                { x: 15, y: 18, size: 8 },
                { x: 20, y: 12, size: 6 },
                { x: 25, y: 22, size: 10 },
                { x: 30, y: 16, size: 7 },
                { x: 35, y: 28, size: 12 },
                { x: 40, y: 20, size: 9 },
                { x: 45, y: 32, size: 15 }
            ];

            const xAxis = chart.xAxes.push(am5xy.ValueAxis.new(root, {
                renderer: am5xy.AxisRendererX.new(root, {})
            }));

            const yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                renderer: am5xy.AxisRendererY.new(root, {})
            }));

            const series = chart.series.push(am5xy.LineSeries.new(root, {
                name: "Scatter Series",
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: "y",
                valueXField: "x",
                tooltip: am5.Tooltip.new(root, {
                    labelText: "X: {valueX}, Y: {valueY}"
                })
            }));

            series.strokes.template.set("visible", false);
            series.bullets.push(function () {
                const bulletCircle = am5.Circle.new(root, {
                    radius: 5,
                    fill: series.get("fill")
                });
                return am5.Bullet.new(root, {
                    sprite: bulletCircle
                });
            });

            series.data.setAll(data);

            // Make stuff animate on load
            series.appear(1000);
            chart.appear(1000, 100);

            return chart;
        } catch (error) {
            console.error('Error creating scatter chart:', error);
            return null;
        }
    }

    // Initialize clock widget
    initializeClock($widget) {
        const clockElement = $widget.find('[id^="clock-time-"]');
        if (clockElement.length === 0) return;

        const updateClock = () => {
            const now = new Date();
            clockElement.text(now.toLocaleTimeString());
        };

        // Update every second
        const interval = setInterval(updateClock, 1000);
        this.intervals[clockElement.attr('id')] = interval;
    }

    // Initialize map widget
    initializeMap($widget) {
        const mapContainer = $widget.find('[id^="map-"]');
        if (mapContainer.length === 0) return;

        // Simple map placeholder - in a real app, you'd integrate with Google Maps, Leaflet, etc.
        const mapData = SampleData.mapData;
        const mapHtml = `
            <div class="map-locations">
                ${mapData.markers.map(marker => `
                    <div class="map-marker">
                        <i class="fas fa-map-marker-alt"></i>
                        <div class="marker-info">
                            <strong>${marker.title}</strong>
                            <p>${marker.info}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        mapContainer.html(mapHtml);
    }

    // Select widget
    selectWidget(widgetId) {
        $('.dashboard-widget').removeClass('selected');
        const $gridItem = $(`.grid-stack-item[gs-id="${widgetId}"]`);
        $gridItem.find('.dashboard-widget').addClass('selected');
        this.selectedWidget = widgetId;
    }

    // Duplicate widget
    duplicateWidget(widgetId) {
        const $gridItem = $(`.grid-stack-item[gs-id="${widgetId}"]`);
        const $widget = $gridItem.find('.dashboard-widget');
        const type = $widget.data('widget-type');
        const title = $widget.find('.widget-header h5').text() + ' (Copy)';
        const dataIndex = $widget.data('widget-data');

        // Get grid position and offset it
        const gridData = $gridItem.data('_gridstack_node');
        const newGridOptions = {
            x: (gridData.x + 1) % 12,
            y: gridData.y + 1,
            w: gridData.w,
            h: gridData.h
        };

        this.createWidget(type, title, newGridOptions, dataIndex);
        window.DashboardStorage.showNotification('Widget duplicated', 'success');
    }

    // Delete widget
    deleteWidget(widgetId) {
        if (confirm('Are you sure you want to delete this widget?')) {
            window.DashboardStorage.pushUndoState();

            const $gridItem = $(`.grid-stack-item[gs-id="${widgetId}"]`);
            const $widget = $gridItem.find('.dashboard-widget');



            // Cleanup amCharts
            const amChartContainer = $widget.find('.amchart-container')[0];
            if (amChartContainer && this.amCharts[amChartContainer.id]) {
                this.amCharts[amChartContainer.id].root.dispose();
                delete this.amCharts[amChartContainer.id];
            }

            // Cleanup intervals (for clock widgets)
            const clockElement = $widget.find('[id^="clock-time-"]');
            if (clockElement.length > 0) {
                const clockId = clockElement.attr('id');
                if (this.intervals[clockId]) {
                    clearInterval(this.intervals[clockId]);
                    delete this.intervals[clockId];
                }
            }

            // Remove from grid
            this.grid.removeWidget($gridItem[0]);
            window.DashboardStorage.showNotification('Widget deleted', 'success');
        }
    }

    // Load default dashboard
    loadDefaultDashboard() {
        SampleData.defaultLayout.forEach(widgetData => {
            this.createWidget(
                widgetData.type,
                widgetData.title,
                {
                    x: widgetData.x,
                    y: widgetData.y,
                    w: widgetData.w,
                    h: widgetData.h
                },
                widgetData.data,
                widgetData.id
            );
        });
    }

    // Clear all widgets
    clearDashboard() {
        if (confirm('Are you sure you want to clear all widgets?')) {
            window.DashboardStorage.pushUndoState();

            // Destroy all amCharts
            Object.values(this.amCharts).forEach(chartData => {
                if (chartData.root) {
                    chartData.root.dispose();
                }
            });
            this.amCharts = {};

            // Clear all intervals
            Object.values(this.intervals).forEach(interval => clearInterval(interval));
            this.intervals = {};

            // Clear grid
            this.grid.removeAll();

            window.DashboardStorage.showNotification('Dashboard cleared', 'success');
        }
    }




}




// Initialize widget manager
window.WidgetManager = new WidgetManager();


