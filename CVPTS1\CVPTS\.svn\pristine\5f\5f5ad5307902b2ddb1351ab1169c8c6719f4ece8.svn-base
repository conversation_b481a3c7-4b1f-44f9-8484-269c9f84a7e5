﻿$(function () {
    const datasetURL = {
        getPagination: "/BCMAdministration/Dataset/GetAll",
        getTableNames: "/BCMAdministration/Dataset/GetTableAccessData",
        getSchemaNames: "/BCMAdministration/Dataset/GetSchemaNamesByTableName",
        getTableColumns: "/BCMAdministration/Dataset/GetTableColumns",
        nameExistUrl: "/BCMAdministration/Dataset/DataSetNameExist",
        runQuery: "/BCMAdministration/Dataset/RunQuery",
        datasetCreateOrUpdate: "/BCMAdministration/Dataset/CreateOrUpdate",
        datasetDelete: "/BCMAdministration/Dataset/Delete",
        getById: "/BCMAdministration/Dataset/GetById"
    };
    let dbValue = window.dbValue || '';
    let queryResult = '';
    $(document).ready(function () {

        const dataTable = $('#datasetTable').DataTable({
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            processing: true,
            serverSide: false,
            filter: true,
            order: [],
            ajax: {
                type: "GET",
                url: datasetURL.getPagination,
                dataType: "json",
                dataSrc: function (json) {
                    return json?.data || [];
                }
            },
            columns: [
                {
                    data: null,
                    name: "Sr. No.",
                    orderable: false,
                    render: function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    }
                },
                {
                    data: "dataSetName",
                    name: "Name",
                    render: (data, type) =>
                        type === 'display' ? (data || 'NA') : data
                },
                {
                    data: "description",
                    name: "Description",
                    render: (data, type) =>
                        type === 'display'
                            ? `<span title="${data || 'NA'}" class="text-truncate" style="max-width:450px; display:inline-block">${data || 'NA'}</span>`
                            : data
                },
                {
                    data: "storedQuery",
                    name: "Stored Query",
                    render: (data, type) =>
                        type === 'display'
                            ? `<span title="${data || 'NA'}" class="text-truncate" style="max-width:600px; display:inline-block">${data || 'NA'}</span>`
                            : data
                },
                {
                    data: null,
                    orderable: false,
                    render: function (data, type, row) {
                        return `
                    <div class="d-flex align-items-center gap-2">
                        <span class="btn-action btnEdit" role="button" data-id="${row.id}"  data-bs-target="#Modal">
                            <i class="cv-edit" title="Edit"></i>
                        </span>
                        <span class="btn-action btnDelete" role="button" data-id="${row.id}" data-bs-target="#Modal">
                            <i class="cv-delete text-danger" title="Delete"></i>
                        </span>
                    </div>`;
                    }
                }
            ]
        });
        function updateQuery() {
            let leftDDL = document.getElementById("leftList");
            let rightDDL = document.getElementById("rightList");
            if (leftDDL.options && leftDDL.options.length == 0 && rightDDL.options && rightDDL.options.length != 0) { return; }
            if (dbValue?.toLowerCase() == 'mysql') {
                $("#datasetStoredQuery").val(
                    "Select " + (queryResult || '*') + " from `" + $('#schemaName').val() + "`.`" + $('#tableName').val() + "`"
                );
            } else if (dbValue?.toLowerCase() == 'oracle') {
                const columns = queryResult.length
                    ? queryResult.split(',').map(column => `"${column.trim()}"`).join(', ')
                    : '';
                $("#datasetStoredQuery").val(
                    'SELECT ' + (columns || '*') + ' FROM "' + $('#schemaName').val() + '"."' + $('#tableName').val() + '"'
                );
            } else {
                $("#datasetStoredQuery").val(
                    "Select " + (queryResult || '*') + " from [" + $('#schemaName').val() + "].[dbo].[" + $('#tableName').val() + "]"
                );
            }
            $('#StoredQuery-error').text('').removeClass('field-validation-error');
        }
        function checkColumnData() {
            setTimeout(function () {
                $('#datasetLoader').addClass('d-none').hide();
                if ($("#datasetColumnName_to").children().length && $("#rightList").children().length) {
                    $("#btnRunQuery").show();
                } else {
                  
                }
            }, 100);
        }
        function getAllQuery() {
            let schema = $('#schemaName').val() || $('#schemaName').text(),
                db = dbValue?.toLowerCase(),
                tableName = $('#tableName option:selected').text() || $('#tableName').text();
            if (tableName) {
                $("#datasetStoredQuery").val(
                    db === 'mysql'
                        ? `Select * from \`${schema}\`.\`${tableName}\``
                        : db === 'oracle'
                            ? `SELECT * FROM "${schema}"."${tableName}"`
                            : `Select * from [${schema}].[dbo].[${tableName}]`
                );
            }
        }
        function updateQuery() {
            const schema = $('#schemaName').text()?.trim();
            const table = $('#tableName').text()?.trim();
            const db = dbValue?.toLowerCase();

            // Get selected columns from rightList <ul>
            const columns = $('#rightList li').map(function () {
                return $(this).text().trim();
            }).get();

            if (!schema || !table) return;

            const formattedColumns = columns.length
                ? (db === 'oracle'
                    ? columns.map(c => `"${c}"`).join(', ')
                    : columns.join(', '))
                : '*';

            let query = '';

            if (db === 'mysql') {
                query = `SELECT ${formattedColumns} FROM \`${schema}\`.\`${table}\``;
            } else if (db === 'oracle') {
                query = `SELECT ${formattedColumns} FROM "${schema}"."${table}"`;
            } else {
                query = `SELECT ${formattedColumns} FROM [${schema}].[dbo].[${table}]`;
            }

            $('#datasetStoredQuery').val(query);
            $('#StoredQuery-error').text('').removeClass('field-validation-error');
        }
        function populateModalFields(datasetData) {
            // Clear modal
            $('#datasetName').val('');
            $('#datasetDesc').val('');
            $('#datasetStoredQuery').val('');
            $('#id').val('');
            $('#leftList, #rightList').empty();
            const schemaSelect = $('#schemaName')[0]?.selectize;
            const tableSelect = $('#tableName')[0]?.selectize;

            if (schemaSelect) {
                schemaSelect.clearOptions();
                schemaSelect.clear();
            }

            if (tableSelect) {
                tableSelect.clearOptions();
                tableSelect.clear();
            }

            // Set values
            $('#datasetName').val(datasetData?.dataSetName || '');
            $('#datasetDesc').val(datasetData?.description || '');
            $('#datasetStoredQuery').val(datasetData?.storedQuery || '');
            $('#id').val(datasetData?.id || '');

            const schemaName = extractSchemaFromQuery(datasetData?.storedQuery);
            const tableName = datasetData?.tableName;

            if (schemaSelect) {
                schemaSelect.addOption({ value: schemaName, text: schemaName });
                schemaSelect.setValue(schemaName);
            }

            if (tableSelect) {
                tableSelect.addOption({ value: datasetData?.id, text: tableName });
                tableSelect.setValue(datasetData?.id);
            }

            // Split stored query SELECT clause into columns
            const selectedCols = extractColumnsFromQuery(datasetData?.storedQuery);

             //Fetch all columns from schema + table, then populate dual list
            $.ajax({
                url: datasetURL.getTableColumns,
                type: "POST",
                contentType: "application/json",
               dataType: "json",
               data: JSON.stringify({ schemaName: schemaName, tableName: tableName }),
               success: function (res) {
                   if (res?.success && Array.isArray(res.data)) {
                       res.data.forEach(col => {
                           const li = `<li draggable="true">${col}</li>`;
                           if (selectedCols.includes(col)) {
                               $('#rightList').append(li);
                            } else {
                               $('#leftList').append(li);
                           }
                        });
                       updateQuery(); // generate stored query
                        checkColumnData();
                   } else {
                        showNotification('warning', 'No column data found for this table.');
                    }
                },
                error: function () {
                    showNotification('error', 'Error fetching columns from server.');
                }
            });
        }
        function extractSchemaFromQuery(query) {
            if (!query) return '';
            const db = dbValue?.toLowerCase();
            if (db === 'mysql') {
                const match = query.match(/from `([^`]+)`\.`([^`]+)`/i);
                return match ? match[1] : '';
            } else if (db === 'oracle') {
                const match = query.match(/from "([^"]+)"\."([^"]+)"/i);
                return match ? match[1] : '';
            } else {
                const match = query.match(/\[([^\]]+)\]\.\[dbo\]\.\[([^\]]+)\]/i);
                return match ? match[1] : '';
            }
        }
        function extractColumnsFromQuery(query) {
            if (!query) return [];
            const db = dbValue?.toLowerCase();
            let colPart = '';

            if (db === 'oracle') {
                const match = query.match(/SELECT\s+(.*?)\s+FROM/i);
                colPart = match?.[1] || '';
                return colPart.replace(/"/g, '').split(',').map(c => c.trim()).filter(Boolean);
            }

            const selectMatch = query.split(/from/i)[0];
            colPart = selectMatch.replace(/select/i, '').trim();
            return colPart === '*' ? [] : colPart.split(',').map(c => c.trim()).filter(Boolean);
        }
        function validateQuery(query) {
            let queryPattern;
            switch (dbValue?.toLowerCase()) {
                case 'oracle':
                    queryPattern = /^SELECT\s+.*\s+FROM\s+("[a-zA-Z0-9_.]+"|\b[a-zA-Z0-9_]+\b)(\s*\.\s*("[a-zA-Z0-9_]+"|\b[a-zA-Z0-9_]+\b))?\s*;?$/i;
                    break;
                case 'mysql':
                    queryPattern = /^SELECT\s+.*\s+FROM\s+(`?[a-zA-Z0-9_.]+`?)(\s*\.\s*(`?[a-zA-Z0-9_]+`?))?\s*;?$/i;
                    break;
                case '':
                    queryPattern = /^SELECT\s+.*\s+FROM\s+\[[a-zA-Z0-9_.]+\]\.\[dbo\]\.\[[a-zA-Z0-9_]+\]\s*;?$/i;
                    break;
                default:
                    $('#StoredQuery-error').text('Unsupported database type').addClass('field-validation-error');
                    return false;
            }
            if (!query?.trim()) {
                $('#StoredQuery-error').text('Enter query').addClass('field-validation-error');
                return false;
            }
            else if (!queryPattern.test(query.trim())) {
                $('#StoredQuery-error').text('Enter valid query').addClass('field-validation-error');
                return false;
            }
            $('#StoredQuery-error').text('').removeClass('field-validation-error');
            return true;
        }

        async function RunQuery(query) {
            const isValid = validateQuery(query);
            if (!isValid) {
                $('#StoredQuery-error').text('Please enter a valid query.').addClass('field-validation-error');
                return [];
            }

            return new Promise((resolve, reject) => {
                $.ajax({
                    method: "GET",
                    url: datasetURL.runQuery,
                    data: { runQuery: query },
                    dataType: "json",
                    success: function (response) {
                        $("#datasetWrapper").empty();
                        $("#tableData").empty();
                        $("#tablerow").empty();

                        if (response?.success && response?.data) {
                            let tableDataArr = typeof response.data.tableValue === "string"
                                ? JSON.parse(response.data.tableValue)
                                : response.data.tableValue;

                            if (!tableDataArr || tableDataArr.length === 0) {
                                $("#datasetTable").hide();
                                const imageHtml = `<img src="../../img/isomatric/no_data_found.svg" style="width: 385px;padding-top: 81px;" class="Card_NoData_Img">`;
                                $("#datasetWrapper").css('text-align', 'center').html(imageHtml).show();
                                return resolve([]);
                            }

                            resolve(tableDataArr);
                        } else {
                            $('#StoredQuery-error').text('Enter valid query').addClass('field-validation-error');
                            resolve([]);
                        }
                    },
                    error: function (xhr) {
                        $('#StoredQuery-error').text('An error occurred while running query').addClass('field-validation-error');
                        console.error("RunQuery error:", xhr);
                        reject([]);
                    }
                });
            });
        }

        $('#datasetColumnName_rightSelected').on('click', function () {
            $('#leftList li.selected').each(function () {
                $(this).removeClass('selected').appendTo('#rightList');
            });
            updateQuery();
            checkColumnData();
        });

        $('#datasetColumnName_leftSelected').on('click', function () {
            $('#rightList li.selected').each(function () {
                $(this).removeClass('selected').appendTo('#leftList');
            });
            setTimeout(function () {
                if ($('#rightList').children().length === 0) {
                    $('#datasetStoredQuery').val('');
                } else {
                    if ($('#leftList').children().length || $('#rightList').children().length) {
                        updateQuery();
                    }
                }
            }, 100);

            checkColumnData();
        });

        $('#datasetColumnName_rightAll').on('click', function () {
            getAllQuery();
            let rightDDL = $("#datasetColumnName option");
            let newQueryResult = rightDDL.map((_, opt) => opt.text).get().join(',');
            queryResult = queryResult ? `${queryResult},${newQueryResult}` : newQueryResult;
            checkColumnData();
        });

        $('#datasetColumnName_leftAll').on('click', function () {
            queryResult = '';
            $("#datasetStoredQuery").val("");
            checkColumnData();
        });

        // Bind Create Dataset button click
        $('#createDataset').on('click', () => {            
            ['#datasetName', '#datasetDesc', '#datasetStoredQuery', '#searchLeft', '#searchRight'].forEach(id => {
                $(id).val('');
            });
            $('#leftList, #rightList').empty();
            ['#schemaName', '#tableName'].forEach(selector => {
                const selectize = $(selector)[0]?.selectize;
                if (selectize) {
                    selectize.clearOptions();
                    selectize.clear();
                } else {
                    $(selector).empty();
                }
            });
            loadSchemas();
        });

        $('.btn-close').on("click", function () {
            location.reload();
        });

        $('#btnDatasetSave').on("click", function () {
            const id = datasetId; // assuming this is the dataset Id
            const name = $("#datasetName").val()?.trim();
            const description = $("#datasetDesc").val()?.trim();
            const storedQuery = $("#datasetStoredQuery").val()?.trim();
            const tableName = $("#tableName option:selected").text()?.trim();
            const schemaName = $("#schemaName").val();

            if (!name || !storedQuery || !tableName) {
                showNotification('warning', 'Please fill in required fields.');
                return;
            }

            $.ajax({
                url: datasetURL.datasetCreateOrUpdate,
                type: "POST",
                dataType: "json",
                data: {
                    Id: id,
                    DataSetName: name,
                    Description: description,
                    TableName: tableName, 
                    StoredQuery: storedQuery,
                    StoredProcedureName: "", 
                    TableAccessId: null,    
                    IsActive: true
                },
                success: function (result) {
                    if (result?.success) {
                        showNotification('success', 'Dataset saved successfully.');
                        $('#AddModal').modal('hide');
                        location.reload();
                        
                    } else {
                        showNotification('error', result.message || 'Failed to save dataset.');
                    }
                },
                error: function () {
                    showNotification('error', 'An error occurred while saving dataset.');
                }
            });
        });
        let datasetId;
        $('#datasetTable').on('click', '.btnEdit', function () {
             datasetId = $(this).data('id');
            $.ajax({
                url: datasetURL.getById,
                type: "GET",
                dataType: "json",
                data: { id: datasetId },
                success: function (result) {
                    if (result?.success) {
                        populateModalFields(result.data);
                        $('#SaveFunction').text('Update');
                        $('#AddModal').modal('show');
                        checkColumnData();
                    } else {
                        showNotification('error', result.message || 'Failed to load dataset.');
                    }
                },
                error: function () {
                    showNotification('error', 'An error occurred while loading dataset.');
                }
            });
        });
        // Delete dataset entry
        $('#datasetTable').on('click', '.btnDelete', function () {
            const datasetId = $(this).data('id');
            const confirmed = confirm("Are you sure you want to delete this dataset?");
            if (!confirmed || !datasetId) return;
            $.ajax({
                url: datasetURL.datasetDelete,
                type: "POST",
                dataType: "json",
                data: { id: datasetId },
                success: function (result) {
                    if (result?.success) {
                        showToast('success', 'Dataset deleted successfully.');
                        $('#datasetTable').DataTable().ajax.reload();
                    } else {
                        showToast('error', result.message || 'Failed to delete dataset.');
                    }
                },
                error: function () {
                    showToast('error', 'An error occurred while deleting dataset.');
                }
            });
        });

        $('#schemaName').on("change", function () {
            const selectedSchema = $(this).val();
            loadTablesBySchema(selectedSchema);
        });

        $('#tableName').on('change', function () {
            $('#datasetStoredQuery').val('');
            $('#leftList').empty();
            $('#rightList').empty();
            const tablename = $('#tableName option:selected').text().trim();
            $.ajax({
                url: datasetURL.getTableColumns,
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify({ tableName: tablename }),
                success: function (result) {
                    if (result?.success && Array.isArray(result.data)) {
                        $('#leftList').empty();
                        result.data.forEach(column => {
                            $('#leftList').append(`<li draggable="true">${column}</li>`);
                        });
                    } else {
                        showNotification('warning', 'No columns found for selected table.');
                    }
                },
                error: function () {
                    showNotification('error', 'Failed to load column data from TableAccess.');
                }
            });
        });

        $('#btnRunQuery').on('click', async () => {
            const query = $('#datasetStoredQuery').val()?.trim();

            if (!query) {
                $('#StoredQuery-error').text('Please provide a stored query.').addClass('field-validation-error');
                return;
            }

            $('#btnRunQuery, #tableName, #schemaName').prop('disabled', true);
            $('#datasetLoader').removeClass('d-none').show();

            $("#tablerow").empty();
            $("#tableData").empty();

            $("#datasetTable").hide();
            $("#datasetWrapper").show();

            try {
                const result = await RunQuery(query); 

                if (result && result.length > 0) {
                    // Show modal
                    $('#AddModal').modal('hide');
                    $('#QueryListModal').modal('show');

                    // Scroll reset
                    $('#QueryListModal').off('shown.bs.modal').on('shown.bs.modal', function () {
                        $(this).find('.modal-body > div[style*="overflow:auto"]').scrollTop(0);
                    });

                    // Build headers from first row keys
                    const headers = Object.keys(result[0]);
                    headers.forEach(h => {
                        $('#tablerow').append(`<th>${h}</th>`);
                    });

                    // Build rows
                    result.forEach(row => {
                        const rowHtml = headers.map(h => `<td>${row[h]}</td>`).join('');
                        $('#tableData').append(`<tr>${rowHtml}</tr>`);
                    });
                } else {
                    showToast('No records returned from the query.', 'info');
                }
            } catch (error) {
                console.error(error);
                showToast('Error running query.', 'error');
            } finally {
                $('#datasetLoader').addClass('d-none').hide();
                $('#btnRunQuery, #tableName, #schemaName').prop('disabled', false);
            }
        });
        function loadSchemas() {
            $.ajax({
                url: datasetURL.getTableNames,
                type: "GET",
                dataType: "json",
                success: function (result) {
                    if (result?.success && Array.isArray(result.data)) {
                        const schemaSelect = $('#schemaName')[0].selectize;
                        schemaSelect.clearOptions();
                        schemaSelect.addOption({ value: "", text: "Select schema" });

                        const addedSchemas = new Set();
                        result.data.forEach(table => {
                            const schemaName = table.schemaName?.trim() ?? '';
                            if (schemaName && !addedSchemas.has(schemaName)) {
                                addedSchemas.add(schemaName);
                                schemaSelect.addOption({ value: schemaName, text: schemaName });
                            }
                        });
                    }
                },
                error: function () {
                    showNotification('error', 'Failed to load schema names from TableAccess.');
                }
            });
        }
        function loadTablesBySchema(schemaName) {
            if (!schemaName) {
                return;
            }

            $.ajax({
                url: datasetURL.getTableNames,
                type: "GET",
                dataType: "json",
                success: function (result) {
                    if (result?.success && Array.isArray(result.data)) {
                        const tableSelect = $('#tableName')[0].selectize;
                        tableSelect.clearOptions();
                        tableSelect.addOption({ value: "", text: "Select Table" });

                        result.data.forEach(table => {
                            const tableSchema = table.schemaName?.trim();
                            const tableName = table.tableName?.trim();
                            const tableId = table.id ?? '';
                            if (tableSchema === schemaName && tableName) {
                                tableSelect.addOption({ value: tableId, text: tableName });
                            }
                        });
                    }
                },
                error: function () {
                    showNotification('error', 'Failed to load table names from TableAccess.');
                }
            });
        }
        function showNotification(message, type) {
            if (typeof toastr !== 'undefined') {
                if (type === 'success') toastr.success(message);
                else if (type === 'error') toastr.error(message);
                else toastr.info(message);
            } else if (typeof notificationAlert === 'function') {
                notificationAlert(type, message);
            } else {
                alert((type === 'success' ? '✅ ' : type === 'error' ? '❌ ' : '') + message);
            }
        }
    });
});