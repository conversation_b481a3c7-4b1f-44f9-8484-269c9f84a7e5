﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc;
using BCM.Shared;
using System.Text;
using BCM.Security.Helper;


namespace BCM.UI.Areas.BCMResourceManagement.Controllers;

[Area("BCMResourceManagement")]
public class EmailVerificationController : Controller
{
    private readonly ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    readonly CVLogger _CvLogger;

    // Link expiration time in seconds (5 minutes = 300 seconds)
    private const int LINK_EXPIRATION_SECONDS = 300;

    public EmailVerificationController(ProcessSrv processSrv, Utilities utilities , CVLogger cvLogger)
    {
        _ProcessSrv = processSrv;
        _Utilities = utilities;
        _CvLogger = cvLogger;
    }

    //public IActionResult Index()
    //{
    //    return View();
    //}

    [HttpGet]
    public IActionResult VerifyEmail(string resourceId, string orgId, string timestamp)
    {
        try
        {
            var result = ProcessEmailVerification(resourceId, orgId, timestamp);

            // Set ViewBag data that the layout might need
            ViewBag.Title = "Email Verification";
            ViewBag.IsVerificationPage = true;

            return View(result);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            // If there's an error, return a simple view without the main layout
            var errorResult = new EmailVerificationResult
            {
                Success = false,
                Message = "An error occurred during verification.",
                MessageType = "error"
            };

            ViewBag.Title = "Email Verification";
            ViewBag.IsVerificationPage = true;

            return View(errorResult);
        }
    }

    //[HttpGet]
    //public IActionResult EmailVerification()
    //{
    //    return View();
    //}

    private EmailVerificationResult ProcessEmailVerification(string encryptedResourceId, string encryptedOrgId, string encryptedTimestamp)
    {
        var result = new EmailVerificationResult();

        try
        {
            // Decrypt the IDs and timestamp
            string resourceId = DecryptUrlParameter(encryptedResourceId);
            string orgId = DecryptUrlParameter(encryptedOrgId);
            string timestamp = DecryptUrlParameter(encryptedTimestamp);

            if (string.IsNullOrEmpty(resourceId) || resourceId == "0" ||
                string.IsNullOrEmpty(orgId) || orgId == "0")
            {
                result.Success = false;
                result.Message = "Invalid verification link.";
                return result;
            }

            // Validate link expiration
            if (!IsLinkValid(timestamp, LINK_EXPIRATION_SECONDS))
            {
                result.Success = false;
                result.Message = "This verification link has expired. Please request a new reminder email.";
                result.MessageType = "warning";
                return result;
            }

            // Create response object
            var response = new ResourcesReminder
            {
                ResourceID = Convert.ToInt32(resourceId),
                MobilePhone = "0000", // Set dummy mobile number
                CommunicationMode = (int)BCPEnum.NotificationType.EMail,
                InOut_bound = (int)BCPEnum.CommunicationType.Outbound,
                SenderID = 0
            };

            // Get vault settings for the organization
            var vaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt32(orgId));

            if (vaultSettings != null)
            {
                // Add response with verification interval and days prior
                bool success = _ProcessSrv.AddResponseByResourceID(response,
                    vaultSettings.VerificationInterval,
                    vaultSettings.VerificationDaysPrior);

                if (success)
                {
                    result.Success = true;
                    result.Message = "Thank You For Verification!";
                    result.MessageType = "success";
                }
                else
                {
                    result.Success = false;
                    result.Message = "Error Occurred In Verification.";
                    result.MessageType = "error";
                }
            }
            else
            {
                result.Success = false;
                result.Message = "Organization settings not found.";
                result.MessageType = "error";
            }
        }
        catch (Exception ex)
        {
            // Log error using your logging mechanism
            // _Utilities.CreateCVCoreLogger("EmailVerificationError", ex.Message);
            _CvLogger.LogErrorApp(ex);
            result.Success = false;
            result.Message = "Error Occurred In Verification.";
            result.MessageType = "error";
        }

        return result;
    }

    private string DecryptUrlParameter(string encryptedValue)
    {
        try
        {
            if (string.IsNullOrEmpty(encryptedValue))
                return "0";

            // Use CryptographyHelper for decryption
            return CryptographyHelper.Decrypt(encryptedValue);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);            
            return "0";
        }
    }

    // Helper method to generate verification link (to be used in email templates)
    public string GenerateVerificationLink(int resourceId, int orgId, string baseUrl)
    {
        try
        {
            // TODO: Implement URL encryption logic similar to UrlUtility.GetEncrptedIDForURL
            // This should match your existing URL encryption mechanism

            // Example implementation (replace with your actual encryption logic):
            string encryptedResourceId = EncryptUrlParameter(resourceId.ToString());
            string encryptedOrgId = EncryptUrlParameter(orgId.ToString());

            return $"{baseUrl}/BCMResourceManagement/EmailVerification/VerifyEmail?resourceId={encryptedResourceId}&orgId={encryptedOrgId}";
        }
        catch
        {
            return string.Empty;
        }
    }

    private string EncryptUrlParameter(string value)
    {
        try
        {
            // Use CryptographyHelper for encryption
            return CryptographyHelper.Encrypt(value);
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);            
            return value;
        }
    }

    private bool IsLinkValid(string timestamp, int expirationSeconds)
    {
        try
        {
            if (string.IsNullOrEmpty(timestamp))
                return false;

            // Parse the timestamp from the link
            if (!long.TryParse(timestamp, out long linkTimestamp))
                return false;

            // Get current timestamp
            long currentTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            // Calculate the difference in seconds
            long timeDifference = currentTimestamp - linkTimestamp;

            // Check if the link is still valid (within expiration time)
            return timeDifference <= expirationSeconds && timeDifference >= 0;
        }
        catch
        {
            return false;
        }
    }
}

public class EmailVerificationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = "";
    public string MessageType { get; set; } = "info"; // success, error, warning, info
}
