﻿@model BCM.BusinessClasses.RecoveryPlan
@{
    ViewBag.Title = "RecoveryPlanSummary";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@{
    int index = 1;

    string GetTimeUnitText(string timeUnit)
    {
        if (string.IsNullOrEmpty(timeUnit))
            return "";

        switch (timeUnit)
        {
            case "0":
                return "Minute(s)";
            case "1":
                return "Hour(s)";
            case "2":
                return "Day(s)";
            case "3":
                return "Month(s)";
            default:
                return "";
        }
    }
}

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<style>
    .dataTables_scrollBody {
        max-height: calc(100vh - 235px);
        height: calc(100vh - 235px);
    }

    #example {
        width: 100% !important;
        table-layout: fixed;
    }
</style>

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Recovery Plan Summary</h6>
</div>
<div class="Page-Condant card border-0">
    <div>
        <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password " type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="true" aria-controls="collapseExample"><i class="cv-minus  align-middle"></i></span>
            <h6 class="mb-0">Recovery Plan Configuration</h6>
        </div>
        <div class="row p-3 row-cols-2  collapse show" id="collapseExample">
            <div class="col">
                <div class="form-group">
                    <div>
                        <input type="hidden" id="txtPlanIds" asp-for="ID" />
                    </div>
                    <label class="form-label">Plan Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" disabled id="txtPlanName" asp-for="PlanName" class="form-control" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Organization Code</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        @* <select class="form-select form-control" asp-for="OrgID" autocomplete="off" id="ddlOrganizations" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.OrgInfo,"Id","OrganizationName"))">
                        <option selected value="0">-- Select Organizations --</option>
                        </select> *@
                        <input type="text" disabled id="txtOrganizations" asp-for="OrgName" class="form-control" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Department</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-department"></i></span>
                        @*    <select class="form-select form-control" asp-for="DepartmentID" autocomplete="off" id="ddlDepartments" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Department,"DepartmentID","DepartmentName"))">
                        <option selected value="0">-- select Departments --</option>
                        </select> *@
                        <input type="text" disabled id="ddlDepartments" asp-for="DepartmentName" class="form-control" />

                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Plan Owner</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-owner"></i></span>
                        @* <select class="form-select form-control" asp-for="PlanOwnerID" autocomplete="off" id="ddlOwners" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ResourceList,"ResourceId","ResourceName"))">
                        <option selected value="0">-- select Owner --</option>
                        </select> *@
                        <input type="text" disabled id="txtOwners" asp-for="PlanOwnerName" class="form-control" />

                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Estimated Time</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                        <input type="time" disabled id="txtEstimatedTime" asp-for="EstimatedRecoveryTime" class="form-control" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Plan Creation Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                        <input type="text" disabled id="txtPlanCreationDate" asp-for="PlanCreateDate" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Plan Code</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <input type="text" disabled id="txtPlanCode" asp-for="PlanCode" class="form-control" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Unit</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-unit"></i></span>
                        @*     <select class="form-select form-control" asp-for="UnitID" autocomplete="off" id="ddlUnits" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Unit,"UnitID","UnitName"))">
                        <option selected value="0">-- Select Units --</option>
                        </select> *@
                        <input type="text" disabled id="txtUnit" asp-for="UnitName" class="form-control" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Sub Department</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                        @* <select class="form-select form-control" asp-for="SubfunctionID" autocomplete="off" id="ddlSubDepartments" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.SubDepartment,"SubFunctionID","SubFunctionName"))">
                        <option selected value="0">-- Select SubDepartments --</option>
                        </select> *@
                        <input type="text" disabled id="txtSubDepartment" asp-for="SubfunctionName" class="form-control" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Plan Approver</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-develop-Plan"></i></span>
                        @* <select class="form-select form-control" asp-for="PlanApproverID" autocomplete="off" id="ddlApprovers" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ResourceList,"ResourceId","ResourceName"))">
                        <option selected value="0">-- select Approver --</option>
                        </select> *@
                        <input type="text" disabled id="txtApprover" asp-for="PlanApprovarName" class="form-control" />

                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Plan Review Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                        <input type="text" disabled id="txtPlanReviewDate" asp-for="PlanReviewDate" class="form-control" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <div class="input-group">
                        <span class="input-group-text"><i></i></span>
                        <input type="text" disabled id="txtStatus" asp-for="Status" Value="Initiated" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Comments</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-comment"></i></span>
                    <input type="text" disabled id="txtComment" asp-for="Comments" class="form-control" placeholder="Enter Comments" />
                </div>
            </div>
        </div>
    </div>
    <div>
        <table id="example" class="table table-hover" style="width: 100% !important; max-width: none !important;">
            <thead>
                <tr>
                    <th class="SrNo_th">#</th>
                    <th>Step Name</th>
                    <th>Step Description</th>
                    <th>Step Owner</th>
                    <th>Alternate Step Owner</th>
                    <th>Est. Time</th>
                </tr>
            </thead>
            <tbody>
                @if (ViewBag.recoveryTaskSteps != null)
                {
                    foreach (var item in ViewBag.recoveryTaskSteps)
                    {
                        {
                            <tr>
                                <td>@index</td>
                                <td>@item.StepName</td>
                                <td>@item.StepDescription</td>
                                <td>@item.StepOwnerName</td>
                                <td>@item.AltStepOwnerName</td>
                                <td>@(!string.IsNullOrEmpty(@item.EstTime) ? $"{@item.EstTime} {GetTimeUnitText(@item.TimeUnit ?? "")}" : "")</td>
                            </tr>
                            index++;
                        }
                    }
                }
            </tbody>
        </table>
    </div>


    @*  <div>
    <div class="d-flex align-items-center gap-3 mb-3 border-bottom border-secondary-subtle pb-2">
    <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample2" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
    <h6 class="mb-0">Recovery PlanReview Section</h6>
    </div>

    <div class="row g-2 collapse p-3" id="collapseExample2" style="height: calc(50vh - 300px) !important; width: 100% !important;">
    <table id="example" class="table table-hover" style="width: 100% !important; max-width: none !important;">
    <thead>
    <tr>
    <th class="SrNo_th">#</th>
    <th>Step Name</th>
    <th>Step Description</th>
    <th>Step Owner</th>
    <th>Alternate Step Owner</th>
    <th>Est. Time</th>
    </tr>
    </thead>
    <tbody>
    @if (ViewBag.recoveryTaskSteps != null)
    {
    foreach (var item in ViewBag.recoveryTaskSteps)
    {
    {
    <tr>
    <td>@index</td>
    <td>@item.StepName</td>
    <td>@item.StepDescription</td>
    <td>@item.StepOwnerName</td>
    <td>@item.AltStepOwnerName</td>
    <td>@(!string.IsNullOrEmpty(@item.EstTime) ? $"{@item.EstTime} {GetTimeUnitText(@item.TimeUnit ?? "")}" : "")</td>
    </tr>
    index++;
    }
    }
    }
    </tbody>
    </table>
    </div>
    </div> *@
    <div class="justify-content-between d-flex">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" id="btnViewAll">View All</button>
            <button type="button" class="btn btn-secondary btn-sm me-1" id="btnBack">Back</button>
            <button type="button" class="btn btn-primary btn-sm" id="btnSendForApproval">Send For Approval</button>
            <button type="button" class="btn btn-success btn-sm" id="btnApprove">Approve</button>
            <button type="button" class="btn btn-warning btn-sm" id="btnDisapprove">Disapprove</button>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $("#btnViewAll").click(function () {
                window.location.href = '/BCMFunctionRecoveryPlan/ManageRecoveryPlans/ManageRecoveryPlans/';
        });

        // $("#btnBack").click(function () {
        //     var planId = $("#txtPlanIds").val();
        //     window.location.href = '/BCMFunctionRecoveryPlan/WorkflowConfiguration/WorkflowConfiguration/?strPlanID=' + '@BCM.Security.Helper.CryptographyHelper.Encrypt(Model.ID.ToString())';
        // });

        $("#btnBack").click(function () {
            var planId = $("#txtPlanIds").val();
            window.location.href = '/BCMFunctionRecoveryPlan/WorkflowConfiguration/ManageWorkflowConfiguration/?strPlanID=0';
        });

        $('#btnSendForApproval').click(function () {
            var planId = $("#txtPlanIds").val();
            // $.get('@Url.Action("GetFileredVendor", "ManageVendor")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
            $.get('@Url.Action("SendPlanForApproval", "FunctionalRecoveryPlanSummary")', { planId: planId, ApprovalType: 1 }, function (data) {
                // alert(data);
            });
        });

        $('#btnApprove').click(function () {
            var planId = $("#txtPlanIds").val();
            // $.get('/BCMFunctionRecoveryPlan/FunctionalRecoveryPlanSummary/SendPlanForApproval/?PlanId='+planId+"&ApprovalType=2", function (data) {
            $.get('@Url.Action("SendPlanForApproval", "FunctionalRecoveryPlanSummary")', { planId: planId, ApprovalType: 2 }, function (data) {
                // alert(data);
            });
        });

        $('#btnDisapprove').click(function () {
            var planId = $("#txtPlanIds").val();
            // $.get('/BCMFunctionRecoveryPlan/FunctionalRecoveryPlanSummary/SendPlanForApproval/?PlanId=' + planId + "&ApprovalType=3", function (data) {
            $.get('@Url.Action("SendPlanForApproval", "FunctionalRecoveryPlanSummary")', { planId: planId, ApprovalType: 3 }, function (data) {
                // alert(data);
            });
        });
    </script>
}