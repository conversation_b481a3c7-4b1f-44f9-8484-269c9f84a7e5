﻿@model BCM.BusinessClasses.RiskManagement
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Risk Heat Map";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">@ViewBag.Title</h6>
    </div>
</div>
<style>
    #RiskHeatMap_Chart {
        width: 50%;
        height: calc(100vh - 265px);
    }

    /* Fixed column widths for both tables */
    #tblheatmapSeverity, #tblheatmapSeverity1 {
        width: 100%;
        table-layout: fixed;
    }

        /* First column width */
        #tblheatmapSeverity th:first-child,
        #tblheatmapSeverity td:first-child,
        #tblheatmapSeverity1 th:first-child,
        #tblheatmapSeverity1 td:first-child {
            width: 150px;
        }

        /* Other columns equal width */
        #tblheatmapSeverity th:not(:first-child),
        #tblheatmapSeverity td:not(:first-child),
        #tblheatmapSeverity1 th:not(:first-child),
        #tblheatmapSeverity1 td:not(:first-child) {
            width: calc((100% - 150px) / 5);
        }

        /* Equal height for heat map cells */
        #tblheatmapSeverity td {
            height: 100px;
            vertical-align: middle;
        }

        /* Larger height for risk severity description cells */
        #tblheatmapSeverity1 td {
            height: 100px;
            vertical-align: middle;
        }

        /* Ensure text wrapping in cells */
        #tblheatmapSeverity td, #tblheatmapSeverity th,
        #tblheatmapSeverity1 td, #tblheatmapSeverity1 th {
            word-wrap: break-word;
        }

    /* Select element styling */
    #profileSelect {
        font-size: 0.875rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.2rem;
        border-color: #ced4da;
        background-color: #fff;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

        #profileSelect:focus {
            border-color: #86b7fe;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

    /* Input group with select styling */
    .input-group-with-select {
        display: flex;
        align-items: center;
    }
</style>


@* <div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Risk Heatmap</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
    </div>
</div> *@
<!-- Risk HeatMap Configuration -->
@* <div class="d-flex align-items-center mb-3">
    <i class="cv-edit-pencil me-2 text-primary"></i>
    <span class="fw-semibold text-primary">Risk HeatMap Configuration</span>
</div>
 *@


    <div class="card-header header border-0 pb-0" id="RiskFilterList">
        <form class="d-flex tab-design mb-0">
            <div class="form-check form-check-inline mb-0">
                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="0" checked>
                <label class="form-check-label" for="inlineRadio1">All Risks</label>
            </div>
            <div class="form-check form-check-inline mb-0">
                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="1">
                <label class="form-check-label" for="inlineRadio2">Inherent Risks</label>
            </div>
            <div class="form-check form-check-inline mb-0">
                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="2">
                <label class="form-check-label" for="inlineRadio3">Residual Risks</label>
            </div>
        </form>

    </div>
    <div class="card-body pt-0">
        <div class="row">
            <div class="col-12">
                <div class="p-2 bg-secondary-subtle text-center my-2">
                    <span>Impact( As a % of Annual Revenues )</span>
                </div>

                <!-- Heat Map Table -->
                <div class="bg-white rounded p-3 mb-4">
                    <div class="table-responsive">
                        <table class="table table-bordered text-center mb-3 risk-table" id="tblheatmapSeverity">
                            <thead>
                                <tr class="bg-light">
                                    <th class="text-center">HEAT MAP</th>
                                    <th style="background-color: #48A422;">Insignificant</th>
                                    <th style="background-color: #FFCC00;">Low</th>
                                    <th style="background-color: #FFA500;">Medium</th>
                                    <th style="background-color: #FF3300;">High</th>
                                    <th style="background-color: #FF3300;">Critical</th>
                                </tr>
                                <tr>
                                    <th></th>
                                    <th class="text-center">1</th>
                                    <th class="text-center">2</th>
                                    <th class="text-center">3</th>
                                    <th class="text-center">4</th>
                                    <th class="text-center">5</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-center bg-light">Rare<br>1</td>
                                    <td style="background-color: #48A422;"></td>
                                    <td style="background-color: #FFCC00;"></td>
                                    <td style="background-color: #FFA500;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                </tr>
                                <tr>
                                    <td class="text-center bg-light">Unlikely<br>2</td>
                                    <td style="background-color: #FFCC00;"></td>
                                    <td style="background-color: #FFCC00;"></td>
                                    <td style="background-color: #FFA500;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                </tr>
                                <tr>
                                    <td class="text-center bg-light">Possible<br>3</td>
                                    <td style="background-color: #FFA500;"></td>
                                    <td style="background-color: #FFA500;"></td>
                                    <td style="background-color: #FFA500;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                </tr>
                                <tr>
                                    <td class="text-center bg-light">Likely<br>4</td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                </tr>
                                <tr>
                                    <td class="text-center bg-light">Almost Certain<br>5</td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                    <td style="background-color: #FF3300;"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Risk Severity Section -->
                    <div class="table-responsive mt-4">
                        <table class="table table-bordered text-center risk-table risk-severity-table" id="tblheatmapSeverity1">
                            <thead>
                                <tr class="bg-light">
                                    <td class="text-center">RISK SEVERITY<br>( Probability x Impact )</td>
                                    <th class="text-center" style="background-color: #48A422;">Insignificant</th>
                                    <th class="text-center" style="background-color: #FFCC00;">Low</th>
                                    <th class="text-center" style="background-color: #FFA500;">Medium</th>
                                    <th class="text-center" style="background-color: #FF3300;">High</th>
                                    <th class="text-center" style="background-color: #FF3300;">Critical</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td></td>
                                    <td>Potential to cause<br>insignificant impact on<br>objectives</td>
                                    <td>Potential to cause minor<br>impact that, in most cases,<br>can be absorbed</td>
                                    <td>Potential to cause noticeable<br>impact on objectives</td>
                                    <td>Potential to cause major<br>impact on objectives</td>
                                    <td>Potential to cause severe<br>impact on objectives</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="RiskHeatMap_Chart"></div>
            </div>

        </div>

    </div>
    <!-- Configuration Modal -->
    <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">Risk Profile Configuration</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Profile Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-profile"></i></span>
                                    <input type="text" class="form-control" placeholder="Enter Profile Name" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Impact Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Impact Description" style="height:0px"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Risk Severity Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-risk-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Risk Severity Description" style="height:0px"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">

                            <div class="form-group">
                                <label class="form-label">Profile Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Profile Description" style="height:0px"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Probability Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Probability Description" style="height:0px"></textarea>
                                </div>
                            </div>
                            <div class="form-group d-flex align-items-center gap-2">
                                <div>
                                    <label class="form-label">Impact Scale</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-impact"></i></span>
                                        <input type="text" class="form-control" placeholder="Enter Impact Scale" />
                                    </div>
                                </div>
                                <div>
                                    <label class="form-label">Probability Scale</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-impact"></i></span>
                                        <input type="text" class="form-control" placeholder="Enter Probability Scale" />
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--End Configuration Modal -->
    <!-- Configuration Modal -->
    <div class="modal fade" id="AddModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">RAO-2023 104(1.0) Accidental Hazards</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-4">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-risk-status me-1 "></i>Risk Status
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Open
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-department-name me-1"></i>IT Service Name
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Continuity vault
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Inherent Probability

                                        </td>
                                        <td>:</td>
                                        <td>
                                            Very Low(1)
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Residual Probability
                                        </td>
                                        <td>:</td>
                                        <td>
                                            N/A
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-4">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-owner me-1"></i>Risk Entry Date
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Open
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-profile me-1"></i>Risk Owner
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Continuity vault
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Inherent Impact
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Minor (1)

                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Residual Impact
                                        </td>
                                        <td>:</td>
                                        <td>
                                            N/A
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-4">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Threat
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Man made disasters
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-department-name me-1"></i>Next Review Date
                                        </td>
                                        <td>:</td>
                                        <td>
                                            25/02/2023
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Inherent Risk Rating
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Residual(1)
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Residual Risk Rating
                                        </td>
                                        <td>:</td>
                                        <td>
                                            N/A
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <!--End Configuration Modal -->


@section Scripts {
    <script src="~/lib/amcharts4/core.js"></script>
    <script src="~/lib/amcharts4/charts.js"></script>
    <script src="~/lib/amcharts4/animated.js"></script>

    <script>
        // Initialize risk data from the server
        var riskDataJson = @Html.Raw(ViewBag.RiskDataJson ?? "[]")
    </script>

    <script>
        $('#inlineRadio1,#inlineRadio2,#inlineRadio3').change (function () {

          var dataCondition;

                 if ($("#inlineRadio1").prop("checked")) {

                          dataCondition = $('#inlineRadio1').val();
                    }
                    else if ($("#inlineRadio2").prop("checked")) {

                          dataCondition = $('#inlineRadio2').val();
                    }
                    else if ($("#inlineRadio3").prop("checked")) {

                          dataCondition = $('#inlineRadio3').val();
                    }

                      $.get('@Url.Action("GetFilterRiskData", "RiskHeatmap_DynamicNew")',  function (data) {

                     var tableData = $('#riskfilterlist');
                        var tableData = $('#riskfilterlist');
                        tableData.empty();
                        $('#riskfilterlist').html(data);

                });
             });
    </script>





    <script>
        // Initialize the risk data mapping when the DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Define the probability and impact labels
            const probabilityLabels = ["Rare", "Unlikely", "Possible", "Likely", "Almost Certain"];
            const impactLabels = ["Insignificant", "Low", "Medium", "High", "Critical"];

            // Function to map probability values to labels
            function mapProbabilityToLabel(probability) {
                if (!probability) return '';

                // Convert to string and normalize
                var prob = String(probability).toLowerCase();

                if (prob.includes('1') || prob.includes('rare')) return 'Rare';
                if (prob.includes('2') || prob.includes('unlikely')) return 'Unlikely';
                if (prob.includes('3') || prob.includes('possible')) return 'Possible';
                if (prob.includes('4') || prob.includes('likely')) return 'Likely';
                if (prob.includes('5') || prob.includes('almost')) return 'Almost Certain';

                return '';
            }

            // Function to map impact values to labels
            function mapImpactToLabel(impact) {
                if (!impact) return '';

                // Convert to string and normalize
                var imp = String(impact).toLowerCase();

                if (imp.includes('1') || imp.includes('insignificant')) return 'Insignificant';
                if (imp.includes('2') || imp.includes('low')) return 'Low';
                if (imp.includes('3') || imp.includes('medium')) return 'Medium';
                if (imp.includes('4') || imp.includes('high')) return 'High';
                if (imp.includes('5') || imp.includes('critical')) return 'Critical';

                return '';
            }

            // Function to clear table cells
            function clearTableCells(table) {
                if (!table) return;

                // Start from row 2 (skip headers) and column 1 (skip first column)
                for (var i = 2; i < table.rows.length; i++) {
                    var row = table.rows[i];
                    for (var j = 1; j < row.cells.length; j++) {
                        row.cells[j].textContent = '';
                    }
                }
            }

            // Function to show risk details when a risk code is clicked
            function showRiskDetails(riskCode)
            {
                console.log('Showing details for risk code:', riskCode);

                // Find the risk with the matching code
                var risk = riskDataJson.find(function(r) {
                    return r.RiskCode === riskCode;
                });

               if (risk)
                       {
                         // Create a formatted message with risk details
                          var message = '<b>Risk Details:</b> <hr></hr> ' ;

                          message += ' <b>Risk Code:</b>' + (risk.RiskCode || 'N/A') + '<br>';
                          message += ' <b>Risk Name:</b>' + (risk.RiskName || 'N/A') + '<br>';
                          message += ' <b>Risk Description:</b>' + (risk.RiskDescription || 'N/A')+'<br>';
                          message += ' <b>Risk Category:</b>' + (risk.RiskCategory || 'N/A') + '<br><br>';

                          message += ' <b>Risk Owner:</b>' + (risk.RiskOwnerName || 'N/A') + '<br>';
                          message += ' <b>Risk Entry Date:</b>' + (risk.RiskEntryDate || 'N/A') + '<br>';
                          message += ' <b>Last Review Date:</b>' + (risk.LastRevDate || 'N/A') + '<br>';
                          message += ' <b>Next Review Date:</b>' + (risk.NxtRevDate || 'N/A') + '<br><br>';

                          message += ' <b>Inherent Impact:</b>' + (risk.Impact || 'N/A') +'&nbsp;' ;
                          message += ' <b>Inherent Likelihood:</b>' + (risk.LikliHood || 'N/A') + '<br><br>';
                          message += ' <b>Residual Impact:</b>' + (risk.ResidualImpact || 'N/A') +'&nbsp;' ;
                          message += ' <b>Residual Likelihood:</b>' + (risk.ResidualLikeliHood || 'N/A') + '<br><br>';

                          // Add More Information link button
                          // message += '<div style="text-align: center; margin-top: 10px;">';
                          // message += '<a href="/BCMRiskAssessment/ManageRisk/ManageRisk?RiskCode=' + (risk.RiskCode || '') + '" ';
                          // // message += '<a href="/BCMRiskAssessment/ManageRisk/ManageRisk?RiskID=' + (risk.RiskID || '') + '" ';
                          // message += 'style="display: inline-block; padding: 6px 12px; background-color: #007bff; color: white; ';
                          // message += 'text-decoration: none; border-radius: 4px; font-weight: bold; transition: background-color 0.3s;" ';
                          // message += 'onmouseover="this.style.backgroundColor=\'#0056b3\'" onmouseout="this.style.backgroundColor=\'#007bff\'">';
                          // message += 'More Information</a></div>';


                          // Show the details in a tooltip instead of an alert
                          var tooltipElement = document.createElement('div');
                          tooltipElement.className = 'tooltip-risk-details';
                          tooltipElement.innerHTML = message; // Use innerHTML instead of textContent

                          // Style the tooltip
                          tooltipElement.style.position = 'absolute';
                          tooltipElement.style.backgroundColor = '#fff';
                          tooltipElement.style.border = '1px solid #ccc';
                          tooltipElement.style.padding = '15px';
                          tooltipElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                          tooltipElement.style.zIndex = '1000';
                          tooltipElement.style.maxWidth = '550px';
                          tooltipElement.style.borderRadius = '5px';
                          tooltipElement.style.lineHeight = '1.5';
                          tooltipElement.style.fontSize = '14px';

                          // Append the tooltip to the body
                          document.body.appendChild(tooltipElement);

                          // Position the tooltip near the clicked element
                          tooltipElement.style.left = event.pageX + 'px';
                          tooltipElement.style.top = event.pageY + 'px';

                          // Remove the tooltip after a delay (10 seconds to give more time to read)
                          setTimeout(function()
                          {
                            document.body.removeChild(tooltipElement);
                          }, 10000);
                      }

            else
            {
              alert('Risk details not found for code: ' + riskCode);
            }
        }

            // Function to bind risk data to tables
            function bindRiskDataToTables(filterType) {
                console.log('Binding risk data to tables with filter:', filterType);

                // Get references to both tables
                var heatmapTable = document.getElementById('tblheatmapSeverity');
                var severityTable = document.getElementById('tblheatmapSeverity1');

                if (!heatmapTable || !severityTable) {
                    console.error('Tables not found in the DOM');
                    return;
                }

                // Clear any existing data in the cells
                clearTableCells(heatmapTable);

                // Create a data structure to track risk counts and codes in each cell
                var riskData = {};
                debugger;
                // Process each risk and update the data structure
                riskDataJson.forEach(function(risk) {
                    var probability = '';
                    var impact = '';
                    var riskCode = risk.RiskCode || 'Unknown';

                    if (filterType === 'inherent' || filterType === 'all') {
                        // Use inherent risk values
                        probability = mapProbabilityToLabel(risk.LikliHood);
                        impact = mapImpactToLabel(risk.Impact);

                        // If we have valid probability and impact values
                        if (probability && impact) {
                            // Create a key for this cell
                            var key = probability + '|' + impact;

                            // Initialize the cell data if it doesn't exist
                            if (!riskData[key]) {
                                riskData[key] = {
                                    count: 0,
                                    riskCodes: []
                                };
                            }

                            // Increment the count
                            riskData[key].count++;

                            // Add the risk code if it's not already in the list
                            if (riskCode && !riskData[key].riskCodes.includes(riskCode)) {
                                riskData[key].riskCodes.push(riskCode);
                            }
                        }
                    }

                    if (filterType === 'residual' || filterType === 'all') {
                        // Use residual risk values if we're filtering for residual or showing all
                        var resProbability = mapProbabilityToLabel(risk.ResidualLikeliHood);
                        var resImpact = mapImpactToLabel(risk.ResidualImpact);

                        // If we have valid residual values and we're either showing residual or all risks
                        if (resProbability && resImpact && (filterType === 'residual' || filterType === 'all')) {
                            // Create a key for this cell
                            var resKey = resProbability + '|' + resImpact;

                            // Initialize the cell data if it doesn't exist
                            if (!riskData[resKey]) {
                                riskData[resKey] = {
                                    count: 0,
                                    riskCodes: []
                                };
                            }

                            // Increment the count
                            riskData[resKey].count++;

                            // Add the risk code if it's not already in the list
                            if (riskCode && !riskData[resKey].riskCodes.includes(riskCode)) {
                                riskData[resKey].riskCodes.push(riskCode);
                            }
                        }
                    }
                });

                // Update the heatmap table with risk counts and risk codes
                for (var i = 0; i < probabilityLabels.length; i++)
                {
                    var probability = probabilityLabels[i];
                    var row = heatmapTable.rows[i + 2]; // +2 to skip the header rows

                    if (!row) {
                        console.warn('Row not found for probability:', probability);
                        continue;
                    }

                    for (var j = 0; j < impactLabels.length; j++) {
                        var impact = impactLabels[j];
                        var cell = row.cells[j + 1]; // +1 to skip the first column (labels)

                        if (!cell) {
                            console.warn('Cell not found for impact:', impact);
                            continue;
                        }

                        // Create a key for this cell
                        var key = probability + '|' + impact;

                        // Get the data for this cell
                        var cellData = riskData[key];

                        if (cellData && cellData.count > 0) {
                            // Create a div to hold the count and risk codes
                            var countDiv = document.createElement('div');
                            countDiv.className = 'risk-count';
                            countDiv.textContent = cellData.count;

                            // Create a div to hold the risk codes
                            var codesDiv = document.createElement('div');
                            codesDiv.className = 'risk-codes';

                         if (cellData.riskCodes && cellData.riskCodes.length > 0)
                                  {
                                    // Check if riskCodes length exceeds 3
                                     if (cellData.riskCodes.length > 3)
                                     {
                                         codesDiv.style.maxHeight = '100px'; // Set max height for scroll
                                         codesDiv.style.overflowY = 'auto'; // Enable vertical scroll
                                     }

                                     cellData.riskCodes.forEach(function(code)
                                     {
                                       var codeSpan = document.createElement('span');
                                       codeSpan.className = 'risk-code';
                                       codeSpan.textContent = code;
                                       codeSpan.title = 'Risk Code: ' + code;
                                       codeSpan.onclick = function()
                                       {
                                           showRiskDetails(code);
                                       };
                                       codesDiv.appendChild(codeSpan);

                                       // Add a line break between codes
                                       codesDiv.appendChild(document.createElement('br'));
                                      });
                                    }

                            // Clear the cell and add the new content
                            cell.innerHTML = '';
                            // cell.appendChild(countDiv);
                            cell.appendChild(codesDiv);

                            // Add a tooltip with all risk codes
                            if (cellData.riskCodes && cellData.riskCodes.length > 0)
                            {
                                cell.title = 'Risk Codes: ' + cellData.riskCodes.join(', ');
                            }
                        }
                        else
                        {
                            cell.textContent = '';
                            cell.title = '';
                        }
                    }
                }

                console.log('Tables updated with risk counts and codes');
            }

            // Initialize with all risks
            bindRiskDataToTables('all');

            // Add event listeners to the radio buttons
            var allRisksRadio = document.getElementById('inlineRadio1');
            var inherentRisksRadio = document.getElementById('inlineRadio2');
            var residualRisksRadio = document.getElementById('inlineRadio3');

            if(allRisksRadio) {
                allRisksRadio.addEventListener('change', function() {
                    if(this.checked) {
                        console.log('All risks selected');
                        bindRiskDataToTables('all');
                    }
                });
            }

            if(inherentRisksRadio) {
                inherentRisksRadio.addEventListener('change', function() {
                    if(this.checked) {
                        console.log('Inherent risks selected');
                        bindRiskDataToTables('inherent');
                    }
                });
            }

            if(residualRisksRadio) {
                residualRisksRadio.addEventListener('change', function() {
                    if(this.checked) {
                        console.log('Residual risks selected');
                        bindRiskDataToTables('residual');
                    }
                });
            }
        });
    </script>


    @* <script src="~/js/riskheatmap_dynamic.js"></script> *@
}
