﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Data;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore.Metadata;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMProcessBIAForms.Controllers;
[Area("BCMProcessBIAForms")]
public class WorkAreaRecoveryController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    int iBIAID = 0;
    int iProcessID = 0;
    int iSectionID = 0;
    int iIsBCMEntity = 0;


    public WorkAreaRecoveryController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult AddEquipment(int id)
    {
        Equipments equipment = null;
        try
        {
            List<Equipments> objEquipments = _ProcessSrv.GetAllEquipments();
            equipment = objEquipments.FirstOrDefault(e => e.ID == id);

            if (equipment == null)
            {

                return PartialView("_AddEquipment", equipment);
            }

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddEquipment", equipment);
    }

    [HttpPost]
    public IActionResult AddEquipment(Equipments objEquipments)
    {
        
        try
        {
           
            int iSuccess = _ProcessSrv.EquipmentName_Save(objEquipments);
            if (iSuccess == 0)
            {
                
                return PartialView("_AddEquipment");
            }

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("WorkAreaRecovery", new { strSectionID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID").ToString()), strProcessID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID").ToString()), strIsBCMEntity = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity").ToString()), strBIAID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")) });

    }

    [HttpPost]
    public IActionResult UpdateEquipment(int ID, string EquipmentName)
    {
        Equipments objEquipments = new Equipments();
        bool bSuccess = false;
        try
        {
            objEquipments.ID = ID;
            objEquipments.EquipmentName = EquipmentName;
            if (objEquipments.ID != 0)
            {
                bSuccess = _ProcessSrv.EquipmentName_Update(objEquipments);
            }

            if (bSuccess == false)
            {
                TempData["AlertMessage"] = "Record updated ";
                return PartialView("_AddEquipment");
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("WorkAreaRecovery", new { strSectionID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID").ToString()), strProcessID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID").ToString()), strIsBCMEntity = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity").ToString()), strBIAID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")) });

    }



    [HttpGet]
    public IActionResult DeleteEquipment(int id)
    {

        Equipments objEquipment = new Equipments();
        try
        {

            List<Equipments> objEquipments = _ProcessSrv.GetAllEquipments();
            objEquipment = objEquipments.FirstOrDefault(e => e.ID == id);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteEquipment", objEquipment);
    }

    [HttpPost]
    public IActionResult DeleteEquipment(Equipments objEquipment)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.EquipmentName_Delete(objEquipment);

        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("WorkAreaRecovery", new { strSectionID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID").ToString()), strProcessID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID").ToString()), strIsBCMEntity = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity").ToString()), strBIAID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")) });
    }
    public JsonResult GetWorkAreaRecoveryID(int? id)
    {

        try
        {

            HttpContext.Session.SetString("ID", id.Value.ToString());
            EquipmentSupply objWorkAreaRecoveryRecord = _ProcessSrv.ProcessBIAEquipmentSupplyGetByID(id.Value);
            DataTable equipmentQuantity = getPeopleTimeEditData(Convert.ToInt32(id));


            foreach (DataRow item in equipmentQuantity.Rows)
            {
                objWorkAreaRecoveryRecord.Day1 = Convert.ToInt32(item["Day1"]);
                objWorkAreaRecoveryRecord.Day3 = Convert.ToInt32(item["Day3"]);
                objWorkAreaRecoveryRecord.Day7 = Convert.ToInt32(item["Day7"]);
                objWorkAreaRecoveryRecord.Day14 = Convert.ToInt32(item["Day14"]);
                objWorkAreaRecoveryRecord.Day30 = Convert.ToInt32(item["Day30"]);
                objWorkAreaRecoveryRecord.Beyond = Convert.ToInt32(item["Beyond"]);


            }

            return Json(objWorkAreaRecoveryRecord);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public IActionResult DeleteWorkAreaRecovery(int id)
    {

        EquipmentSupply objEquipmentSupply = new EquipmentSupply();
        try
        {

            objEquipmentSupply = _ProcessSrv.ProcessBIAEquipmentSupplyGetByID(id);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteWorkAreaRecovery", objEquipmentSupply);
    }


    [HttpPost]
    public IActionResult DeleteWorkAreaRecovery(EquipmentSupply objEquipmentSupply)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.ProcessBIAEquipmentSupplyDeleteById(objEquipmentSupply.ID, _UserDetails.UserID);
            //int iBIAIDAfterDelete = UpdateBusinessProcessBIAByBIAID();
            //if(iBIAIDAfterDelete == 0)
            //{
            //    iBIAID = 0;
            //}
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objEquipmentSupply.EquipmentName + " Deleted Successfully" : " Failed To Delete." });            
        }
        return RedirectToAction("WorkAreaRecovery", new { strSectionID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID").ToString()), strProcessID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID").ToString()), strIsBCMEntity = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity").ToString()), strBIAID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")) });
    }

    protected int UpdateBusinessProcessBIAByBIAID()
    {
        string strBIAid = HttpContext.Session.GetString("BIAID");
        return _ProcessSrv.ProcessBIAUpdateByBIAID(Convert.ToInt32(strBIAid), Convert.ToInt32(_UserDetails.UserID));
    }

    public IActionResult WorkAreaRecovery(string strSectionID, string strProcessID, string strIsBCMEntity, string strBIAID)
    {
        try
        {
            iBIAID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strBIAID));
            iProcessID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strProcessID));
            iSectionID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strSectionID));
            iIsBCMEntity = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strIsBCMEntity));

            HttpContext.Session.SetString("BIAID", iBIAID.ToString());
            HttpContext.Session.SetString("SectionID", iSectionID.ToString());
            HttpContext.Session.SetString("IsBCMEntity", iIsBCMEntity.ToString());
            HttpContext.Session.SetString("ProcessID", iProcessID.ToString());
            GetTimeIntervalMaster();

            if (iBIAID == 0)
            {
                HttpContext.Session.SetString("BIAID", iBIAID.ToString());

                iBIAID = GetCurrentBIAID();
            }
            List<EquipmentSupply> lstEquipmentSupply = _ProcessSrv.ProcessBIAEquipmentSupplyBIAID(Convert.ToInt32(iBIAID));
            ViewBag.Questions = _ProcessSrv.GetBIASurveyQuestionListBySectionID(iSectionID);
            ViewBag.BIASectionVersion = _ProcessSrv.GetProcessBIASectionByBIAID(iBIAID);


            BIASection objBIASection = _ProcessSrv.GetBIASurveySectionById(iSectionID, _UserDetails.OrgID);
            ViewBag.Description = objBIASection.SectionDescription;


            ViewBag.EquipmentData = _ProcessSrv.GetAllEquipments();

            var timeMasterData = GetTimeMasterData(); 

            ViewBag.TimeMasterData = timeMasterData;
            ViewBag.ColumnNames = timeMasterData.FirstOrDefault()?.Keys.ToList() ?? new List<string>();


            if (ViewBag.BIASectionVersion.Version == null)
            {
                ViewBag.BIASectionVersion.Version = "1.0";
            }
            
            return View(lstEquipmentSupply);

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();

    }

    [HttpPost]
    public IActionResult AddUpdateWorkAreaRecovery(EquipmentSupply objEquipmentSupply)
    {
        bool bSuccess = false;
        string strError = string.Empty;
        int iIsComplete = 1;
        try
        {
            TempData["Version"] = objEquipmentSupply.Version;
            if (IsInCompleteData(objEquipmentSupply) != true)
            {
                iIsComplete = 0;
            }
            else
            {
                iIsComplete = 1;
            }
            #region
            //objEquipmentSupply.CreatorId = Convert.ToInt32(_UserDetails.UserID);
            //objEquipmentSupply.UpdatorId = Convert.ToInt32(_UserDetails.UserID);
            //objEquipmentSupply.BIAID = GetCurrentBIAID();            
            //objEquipmentSupply.IsComplete = iIsComplete;
            //List<TimeIntervalMaster> collectionTimes = new List<TimeIntervalMaster>();
            //bSuccess = _ProcessSrv.EquipmentSupplyInfoSaveandUpdate(objEquipmentSupply);
            #endregion

            var timeMasterData = GetTimeMasterData();
            var objString = timeMasterData;
            int iBIAID = GetCurrentBIAID();
            objEquipmentSupply.CreatorId = Convert.ToInt32(_UserDetails.UserID);
            objEquipmentSupply.UpdatorId = Convert.ToInt32(_UserDetails.UserID);
            objEquipmentSupply.BIAID = Convert.ToInt32(HttpContext.Session.GetString("BIAID"));

            objEquipmentSupply.IsComplete = iIsComplete;

            DataTable DTTimeMaster = GetTimeIntervalMaster();


            int iColCount = Convert.ToInt32(DTTimeMaster.Columns.Count);

            List<TimeIntervalMaster> _objtimeIntervalColl = _ProcessSrv.TimeIntervalMasterGetAll();

            List<TimeIntervalMaster> collectionTimes = GetTimeIntervalMasterListFromSession();

            List<equipmentsupply_quantity> equipmentsupply_quantityTimes = GetPeopleTimeDetailsFromSession();

            if (objEquipmentSupply.equipmentsupply_quantitylist == null)
            {
                objEquipmentSupply.equipmentsupply_quantitylist = new List<equipmentsupply_quantity>();
            }

            int[] iEquipment_Quantiy = [
                objEquipmentSupply.Day1,
                objEquipmentSupply.Day3,
                objEquipmentSupply.Day7,
                objEquipmentSupply.Day14,
                objEquipmentSupply.Day30,
                objEquipmentSupply.Beyond,
                ];

            int iIndex = 0;

            foreach (var item in iEquipment_Quantiy)
            {
                equipmentsupply_quantity objequipmentsupply_quantity = new equipmentsupply_quantity();

                string strColname = Convert.ToString(DTTimeMaster.Columns[iIndex].ColumnName);
                var getbymaster = collectionTimes.Where(c => c.Time == strColname).FirstOrDefault();
                var gettime = equipmentsupply_quantityTimes.Where(s => s.Time == strColname && s.ProcessBiaPeopleID == GetCurrentBIAID().ToString() && s.EquipmentSupplyId == objEquipmentSupply.ID).FirstOrDefault();
                if(gettime == null)
                {
                    objequipmentsupply_quantity.Id = 0;
                }
                else
                {
                    objequipmentsupply_quantity.Id = gettime.Id;

                }
                
                iIndex++;
                objequipmentsupply_quantity.CreatorId = Convert.ToInt32(_UserDetails.UserID);
                objequipmentsupply_quantity.UpdatorId = Convert.ToInt32(_UserDetails.UserID);
                objequipmentsupply_quantity.Time = strColname;
                objequipmentsupply_quantity.Quantity = item;
                objequipmentsupply_quantity.EquipmentSupplyId = objEquipmentSupply.ID;
                objequipmentsupply_quantity.TimeIntervalId = getbymaster.ID;
                objequipmentsupply_quantity.ProcessBiaPeopleID = GetCurrentBIAID().ToString();
                objEquipmentSupply.equipmentsupply_quantitylist.Add(objequipmentsupply_quantity);
            }

            bSuccess = _ProcessSrv.EquipmentSupplyInfoSaveandUpdate(objEquipmentSupply);
            if(bSuccess == true)
            {
                UpdateBusinessProcessBIAByBIAID();
            }


        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            if (objEquipmentSupply.ID > 0)
            {
                return Json(new { success = bSuccess, message = bSuccess ? objEquipmentSupply.EquipmentName + " Updated Successfully" : " Failed To Update." });
            }
            else
            {
                return Json(new { success = bSuccess, message = bSuccess ? objEquipmentSupply.EquipmentName + " Added Successfully" : " Failed To Add." });
            }
        }
        return RedirectToAction("WorkAreaRecovery", new { strSectionID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID").ToString()), strProcessID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID").ToString()), strIsBCMEntity = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity").ToString()), strBIAID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")) });
    }

    private bool IsInCompleteData(EquipmentSupply objEquipmentSupply)
    {
        if (objEquipmentSupply.EquipmentId == 0 || string.IsNullOrEmpty(objEquipmentSupply.Description))
        {
            return true;
        }
        return false;
    }

    private DataTable GetTimeIntervalMaster()
    {
        List<TimeIntervalMaster> listtimes = new List<TimeIntervalMaster>();
        DataTable dtTimeIntervalMaster = new DataTable();
        
        try
        {
            List<TimeIntervalMaster> _objtimeIntervalColl = _ProcessSrv.TimeIntervalMasterGetAll();
            if (_objtimeIntervalColl != null && _objtimeIntervalColl.Count > 0)
            {
                foreach (TimeIntervalMaster objTimeIntervalMaster in _objtimeIntervalColl)
                {
                    listtimes.Add(new TimeIntervalMaster() { ID = objTimeIntervalMaster.ID, Value = 0, Time = objTimeIntervalMaster.Time, Sequence = objTimeIntervalMaster.Sequence });
                    dtTimeIntervalMaster.Columns.Add(objTimeIntervalMaster.Time);
                }
                DataRow row = dtTimeIntervalMaster.NewRow();
                for (int i = 0; i < _objtimeIntervalColl.Count; i++)
                {
                    row[i] = 0;
                }
                dtTimeIntervalMaster.Rows.Add(row);

                GetPeopleTimeDetails();
                string strJsonListTimesString = JsonSerializer.Serialize(listtimes);
                HttpContext.Session.SetString("TimeIntervalMasterList", strJsonListTimesString);
            }
            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return dtTimeIntervalMaster;
    }

    private int GetCurrentBIAID()
    {
        BIASection objBIASection = new BIASection();

        int iBIAIDForActivity = Convert.ToInt32(HttpContext.Session.GetString("BIAID"));
        try
        {
            if (iBIAIDForActivity == 0)
            {

                
                var Version = TempData.Peek("Version") as string;
                objBIASection.Version = Version;
                objBIASection.VersionChangeDescription = "";
                objBIASection.ApprovalStatus = ((int)BCPEnum.ApprovalType.Initiated).ToString();
                objBIASection.ProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
                objBIASection.SectionID = Convert.ToInt32(HttpContext.Session.GetString("SectionID"));
                objBIASection.IsEffective = 1;
                objBIASection.CreatedBy = Convert.ToInt32(_UserDetails.UserID);
                objBIASection.ChangedBy = Convert.ToInt32(_UserDetails.UserID);
                objBIASection.IsBCMEntity = Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity"));

                iBIAIDForActivity = _ProcessSrv.ProcessBIASectionSave(objBIASection);


                HttpContext.Session.SetString("BIAID", iBIAIDForActivity.ToString());
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return iBIAIDForActivity;
    }

    private DataTable getPeopleTimeEditData(int EquipmentSupplyId)
    {
        List<TimeIntervalMaster> listtimes = new List<TimeIntervalMaster>();
        DataTable dtPeopleTimeEdit = new DataTable();
        try
        {
            List<TimeIntervalMaster> _objtimeIntervalColl = _ProcessSrv.TimeIntervalMasterGetAll();

            var peoplemasterdata = GetPeopleTimeDetailsFromSession();

            if (_objtimeIntervalColl != null && _objtimeIntervalColl.Count > 0)
            {
                foreach (TimeIntervalMaster objTimeIntervalMaster in _objtimeIntervalColl)
                {
                    listtimes.Add(new TimeIntervalMaster() { ID = objTimeIntervalMaster.ID, Value = objTimeIntervalMaster.Value, Time = objTimeIntervalMaster.Time, Sequence = objTimeIntervalMaster.Sequence });
                    dtPeopleTimeEdit.Columns.Add(objTimeIntervalMaster.Time);
                }

                DataRow row = dtPeopleTimeEdit.NewRow();

                for (int i = 0; i < dtPeopleTimeEdit.Columns.Count; i++)
                {
                    if (peoplemasterdata != null && peoplemasterdata.Count > 0)
                    {
                        var data = peoplemasterdata.Where(c => c.Time == dtPeopleTimeEdit.Columns[i].ColumnName && c.EquipmentSupplyId == EquipmentSupplyId).FirstOrDefault();
                        if (data != null)
                            row[i] = data.Quantity;
                        else
                            row[i] = 0;
                    }
                    else
                    {
                        row[i] = 0;
                    }
                }

                dtPeopleTimeEdit.Rows.Add(row);

                string strJsonListTimesString = JsonSerializer.Serialize(listtimes);
                HttpContext.Session.SetString("TimeIntervalMasterList", strJsonListTimesString);

            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }               
        return dtPeopleTimeEdit;
    }

    private DataTable GetPeopleTimeDetails()
    {
        List<equipmentsupply_quantity> listPeopleTimeDetails = new List<equipmentsupply_quantity>();
        DataTable dtPeopleTimeDetails = new DataTable();
        try 
        {
            List<equipmentsupply_quantity> _objPeopleTimeDetailsColl = _ProcessSrv.equipmentsupply_quantityDetailsGetAll();

            if (_objPeopleTimeDetailsColl != null && _objPeopleTimeDetailsColl.Count > 0)
            {
                foreach (equipmentsupply_quantity objPeopleTimeDetails in _objPeopleTimeDetailsColl)
                {
                    listPeopleTimeDetails.Add(new equipmentsupply_quantity()
                    {
                        Id = objPeopleTimeDetails.Id,
                        Quantity = objPeopleTimeDetails.Quantity,
                        Time = objPeopleTimeDetails.Time,
                        TimeIntervalId = objPeopleTimeDetails.TimeIntervalId,
                        ProcessBiaPeopleID = objPeopleTimeDetails.ProcessBiaPeopleID,
                        EquipmentSupplyId = objPeopleTimeDetails.EquipmentSupplyId
                    });
                }
            }

           
            string jsonString = JsonSerializer.Serialize(listPeopleTimeDetails);

           
            HttpContext.Session.SetString("equipmentsupply_quantityDetails", jsonString);

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
               
        return dtPeopleTimeDetails;
    }

    private List<equipmentsupply_quantity> GetPeopleTimeDetailsFromSession()
    {
        
        string jsonString = HttpContext.Session.GetString("equipmentsupply_quantityDetails");

        
        if (jsonString != null)
        {
            return JsonSerializer.Deserialize<List<equipmentsupply_quantity>>(jsonString);
        }

        return new List<equipmentsupply_quantity>();
    }

    private List<TimeIntervalMaster> GetTimeIntervalMasterListFromSession()
    {
        string jsonString = HttpContext.Session.GetString("TimeIntervalMasterList");
        if (jsonString != null)
        {
            return JsonSerializer.Deserialize<List<TimeIntervalMaster>>(jsonString);
        }

        return new List<TimeIntervalMaster>();
    }


    public static class DataTableHelper
    {
        public static List<Dictionary<string, string>> ConvertToDictionaryList(DataTable dataTable)
        {
            var columnNames = dataTable.Columns.Cast<DataColumn>().Select(c => c.ColumnName).ToList();
            var rows = dataTable.AsEnumerable().Select(row =>
                columnNames.ToDictionary(
                    col => col,
                    col => row[col].ToString()
                )
            ).ToList();

            return rows;
        }
    }

    public List<Dictionary<string, string>> GetTimeMasterData()
    {
        DataTable dtTimeMaster = GetTimeIntervalMaster(); 
        return DataTableHelper.ConvertToDictionaryList(dtTimeMaster);
    }
}

