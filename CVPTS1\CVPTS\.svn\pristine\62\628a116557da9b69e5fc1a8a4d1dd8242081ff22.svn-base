﻿@using BCM.BusinessClasses
@model IEnumerable<BCM.BusinessClasses.BCMGroupResources>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    var radioButtonChecked = ViewBag.Condition; // This holds 1 or 2 as per your logic
}

<div class="col-12 text-center">
    <input type="hidden" value="@ViewBag.NotificationID" id="notificationID" />
    @foreach (var item in ViewBag.RadioButtons)
    {
        // Check if the current item's ID matches the condition
        var isChecked = (radioButtonChecked == item.NotificationAsID);
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="NotificationAs" id="@item.NotificationAsName" value="@item.NotificationAsID" @(isChecked ? "checked" : "")>
            <label class="form-check-label" for="@item.NotificationAsName">@item.NotificationAsName</label>
        </div>
    }
</div>
<hr />
<div id="NotifiedAsUsersAndTeams">
    @await Html.PartialAsync("_NotifiedAsUsersAndTeams", Model)
</div>

<script>
    $(document).ready(function () {

        var notifiedId = $('input[name=NotificationAs]:checked').val();
        DisplayCount(notifiedId);

        $(document).on('click', '.form-check-input', function () {
            var iNotifiedID = $(this).val();
            var iNotificationID = $('#notificationID').val();            
            DisplayCount(iNotifiedID);

            $.ajax({
                url: '@Url.Action("RadioButtonClick", "GroupNotificationDetails")',
                type: 'GET',
                data: { iNotifiedID: iNotifiedID, iNotificationID: iNotificationID },
                dataType: 'html', // Specify the expected data type
                success: function (data) {
                    var userType = $('#NotifiedAsUsersAndTeams');
                    userType.empty();
                    userType.html(data);
                    DisplayCount(iNotifiedID);
                },
                error: function (xhr, status, error) {
                    console.log("Status: " + status);
                    console.log("Error: " + error);
                    console.error("Response: " + xhr.responseText);
                }
            });
        });

        function DisplayCount(iNotifiedID) {
            if (iNotifiedID == 1) {
                $('.colResponded').hide();
                $('.colResponseDate').hide();
                $('.rowResponded').hide();
                $('.rowResponseDate').hide();
                $('.colSentDate').show();
                $('.rowSentDate').show();
                $('#countDiv').hide();
            } else if (iNotifiedID == 2) {
                $('.colResponded').show();
                $('.colResponseDate').show();
                $('.rowResponded').show();
                $('.rowResponseDate').show();
                $('.colSentDate').hide();
                $('.rowSentDate').hide();
                $('#countDiv').show();
            }
        }
    });
</script>
