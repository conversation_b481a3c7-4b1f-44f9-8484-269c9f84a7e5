﻿@model IEnumerable<BCM.BusinessClasses.MenuRights>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@

<script src="~/js/password_toggle.js"></script>

<form method="post">
    <div>
        <button type="submit" class="btn btn-primary btn-sm mb-3" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">Show Widgets</button>
        <div class="collapse" id="collapseExample">
            <div>
                <div class="card-header fw-semibold h6">
                    Widgets List
                </div>
                <div class="card card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">Incident Summary</li>
                        <li class="list-group-item">Activity Stream</li>
                        <li class="list-group-item">Process Risk</li>
                        <li class="list-group-item">Plan Risk</li>
                        <li class="list-group-item">Resource Risk</li>
                    </ul>
                </div>
            </div>

        </div>
    </div>
    <div class="row g-3 row-cols-lg row-cols-md-4  row-cols-sm-2">
        <div class="col-3">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox1" value="option1">
            <label class="form-check-label" for="inlineCheckbox1">BCMSManager</label>
        </div>
        <div class="col-3">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox2" value="option2">
            <label class="form-check-label" for="inlineCheckbox2">ITDR Champion Template</label>
        </div>
        <div class="col-3">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox3" value="option3">
            <label class="form-check-label" for="inlineCheckbox3">BCM Champ Template</label>
        </div>
        <div class="col-3">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox4" value="option4">
            <label class="form-check-label" for="inlineCheckbox4">CxO Template</label>
        </div>
        <div class="col-3">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox5" value="option5">
            <label class="form-check-label" for="inlineCheckbox5">BCM Lead Template</label>
        </div>
        <div class="col-3">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox6" value="option6">
            <label class="form-check-label" for="inlineCheckbox6">ITDR Lead Template</label>
        </div>
        <div class="col-3">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox6" value="option6">
            <label class="form-check-label" for="inlineCheckbox6">Risk Champ Template</label>
        </div>
        <div class="col-3">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox6" value="option6">
            <label class="form-check-label" for="inlineCheckbox6">Test Template</label>
        </div>
        <div class="col-3">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox6" value="option6">
            <label class="form-check-label" for="inlineCheckbox6">Test2</label>
        </div>

    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>





