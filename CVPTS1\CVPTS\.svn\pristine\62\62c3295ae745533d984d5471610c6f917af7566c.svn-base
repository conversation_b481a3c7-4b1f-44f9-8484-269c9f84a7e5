﻿$(function () {
    const datasetURL = {
        getPagination: "/BCMAdministration/Dataset/GetAll",
        getTableNames: "/BCMAdministration/Dataset/GetTableAccessData",
        getSchemaNames: "/BCMAdministration/Dataset/GetSchemaNamesByTableName",
        getTableColumns: "/BCMAdministration/Dataset/GetTableColumns",
        nameExistUrl: "/BCMAdministration/Dataset/DataSetNameExist",
        runQuery: "/BCMAdministration/Dataset/RunQuery",
        datasetCreateOrUpdate: "/BCMAdministration/Dataset/CreateOrUpdate",
        datasetDelete: "/BCMAdministration/Dataset/Delete",
        getById: "/BCMAdministration/Dataset/GetById"
    };
    let dbValue = '';
    let queryResult = '';
    $(document).ready(function () {
        const dataTable = $('#datasetTable').DataTable({
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            processing: true,
            serverSide: false,
            filter: true,
            order: [],
            ajax: {
                type: "GET",
                url: datasetURL.getPagination,
                dataType: "json",
                dataSrc: function (json) {
                    return json?.data || [];
                }
            },
            columns: [
                {
                    data: null,
                    name: "Sr. No.",
                    orderable: false,
                    render: function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    }
                },
                {
                    data: "dataSetName",
                    name: "Name",
                    render: (data, type) =>
                        type === 'display' ? (data || 'NA') : data
                },
                {
                    data: "description",
                    name: "Description",
                    render: (data, type) =>
                        type === 'display'
                            ? `<span title="${data || 'NA'}" class="text-truncate" style="max-width:450px; display:inline-block">${data || 'NA'}</span>`
                            : data
                },
                {
                    data: "storedQuery",
                    name: "Stored Query",
                    render: (data, type) =>
                        type === 'display'
                            ? `<span title="${data || 'NA'}" class="text-truncate" style="max-width:600px; display:inline-block">${data || 'NA'}</span>`
                            : data
                },
                {
                    data: null,
                    orderable: false,
                    render: function (data, type, row) {
                        return `
                    <div class="d-flex align-items-center gap-2">
                        <span class="btn-action btnEdit" role="button" data-id="${row.id}" data-bs-target="#Modal">
                            <i class="cv-edit" title="Edit"></i>
                        </span>
                        <span class="btn-action btnDelete" role="button" data-id="${row.id}" data-bs-target="#Modal">
                            <i class="cv-delete text-danger" title="Delete"></i>
                        </span>
                    </div>`;
                    }
                }
            ],
            initComplete: function () {
                setPaginationButtonTitles();
            }
        });
        dataTable.on('draw.dt', function () {
            setPaginationButtonTitles();
        });
        function setPaginationButtonTitles() {
            $('li.paginate_button.previous > a').attr('title', 'Previous');
            $('li.paginate_button.next > a').attr('title', 'Next');
        }

        fetchDatabaseType();
        function fetchDatabaseType() {
            return $.ajax({
                url: '/BCMAdministration/Dataset/GetDatabaseType',
                method: 'GET',
                success: function (res) {
                    if (res?.success && res?.dbType) {
                        dbValue = res.dbType.toLowerCase();
                        console.log("Detected DB Type:", dbValue);
                    } else {
                        dbValue = 'unknown';
                    }
                },
                error: function () {
                    dbValue = 'unknown';
                    console.error("Failed to fetch DB type.");
                }
            });
        }
        function checkColumnData() {
            setTimeout(function () {
                $('#datasetLoader').addClass('d-none').hide();
                if ($("#datasetColumnName_to").children().length && $("#rightList").children().length) {
                    $("#btnRunQuery").show();
                } else {

                }
            }, 100);
        }
        function getAllQuery() {
            const schema = $('#schemaName').val() || $('#schemaName').text();
            const db = dbValue?.toLowerCase();
            const tableName = $('#tableName option:selected').text()?.trim();

            if (!tableName) return;

            let query = '';
            if (db === 'mysql') {
                query = `SELECT * FROM \`${schema}\`.\`${tableName}\``;
            } else if (db === 'oracle') {
                query = `SELECT * FROM "${schema}"."${tableName}"`;
            } else {
                query = `SELECT * FROM [${schema}].[dbo].[${tableName}]`;
            }

            $('#datasetStoredQuery').val(query);
            if (query) {
                $('#storedQueryValidation').text('').hide();
                $('#errorquery').removeClass('is-invalid');
            }
        }
        function updateQuery() {
            const schema = $('#schemaName').val()?.trim();
            const tableName = $('#tableName option:selected').text()?.trim();
            const db = dbValue?.toLowerCase();

            if (!schema || !tableName) return;

            // Just collect column names directly
            const columnNames = $('#rightList li').map(function () {
                return $(this).text().trim();
            }).get();

            if (columnNames.length === 0) return;
            const formattedColumns = db === 'oracle'
                ? columnNames.map(c => `"${c}"`).join(', ')
                : columnNames.join(', ');
            let query = '';
            if (db === 'mysql') {
                query = `SELECT ${formattedColumns} FROM \`${schema}\`.\`${tableName}\``;
            } else if (db === 'oracle') {
                query = `SELECT ${formattedColumns} FROM "${schema}"."${tableName}"`;
            } else {
                query = `SELECT ${formattedColumns} FROM [${schema}].[dbo].[${tableName}]`;
            }
            $('#datasetStoredQuery').val(query);
            $('#StoredQuery-error').text('').removeClass('field-validation-error');
            if (query) {
                $('#storedQueryValidation').text('').hide();
                $('#errorquery').removeClass('is-invalid');
            }
        }
        function loadTablesBySchema1(schemaName) {
            return new Promise((resolve, reject) => {
                if (!schemaName) return resolve([]);

                const tableSelect = $('#tableName')[0]?.selectize;
                if (!tableSelect) return resolve([]);

                $.ajax({
                    url: datasetURL.getTableNames,
                    type: "GET",
                    dataType: "json",
                    success: function (result) {
                        tableSelect.clearOptions();
                        const matchedTables = [];

                        if (result?.success && Array.isArray(result.data)) {
                            const added = new Set();

                            result.data.forEach(table => {
                                const tableSchema = table.schemaName?.trim();
                                const tableName = table.tableName?.trim();

                                if (tableSchema === schemaName && tableName && !added.has(tableName)) {
                                    added.add(tableName);
                                    matchedTables.push(tableName); // ✅ Collect for resolve
                                    tableSelect.addOption({ value: tableName, text: tableName });
                                }
                            });

                            tableSelect.refreshOptions(false);
                            resolve(matchedTables); // ✅ Resolve with matched tables
                        } else {
                            resolve([]); // 🔁 Graceful fallback
                        }
                    },
                    error: function () {
                        reject("Failed to load table list.");
                    }
                });
            });
        }
        function populateModalFields(datasetData) {
            const $name = $('#datasetName');
            const $desc = $('#datasetDesc');
            const $query = $('#datasetStoredQuery');
            const $id = $('#id');
            const $leftList = $('#leftList');
            const $rightList = $('#rightList');

            $name.add($desc).add($query).add($id).val('');
            $leftList.add($rightList).empty();

            const schemaSelect = $('#schemaName')[0]?.selectize;
            const tableSelect = $('#tableName')[0]?.selectize;
            schemaSelect?.clearOptions(); schemaSelect?.clear();
            tableSelect?.clearOptions(); tableSelect?.clear();

            $name.val(datasetData?.dataSetName || '');
            $desc.val(datasetData?.description || '');
            $id.val(datasetData?.id || '');

            const storedQuery = datasetData?.storedQuery || '';
            const dbType = dbValue?.toLowerCase();
            let schemaData = '', tableData = '', selectedCols = new Set();

            const patterns = {
                mysql: /from\s+`([^`]+)`\.`([^`]+)`/i,
                oracle: /from\s+"([^"]+)"\."([^"]+)"/i,
                default: /from\s+\[([^\]]+)\]\.\[dbo\]\.\[([^\]]+)\]/i
            };
            const regex = patterns[dbType] || patterns.default;
            const match = storedQuery.match(regex);

            if (match) {
                schemaData = match[1];
                tableData = match[2];
            }

            schemaSelect?.addOption({ value: schemaData, text: schemaData });
            schemaSelect?.setValue(schemaData);

            loadTablesBySchema1(schemaData).then(allTables => {
                if (!tableSelect) return;
                tableSelect.clearOptions(); tableSelect.clear();
                allTables.forEach(table => {
                    tableSelect.addOption({ value: table, text: table });
                });
                tableSelect.refreshOptions(false);
                if (allTables.includes(tableData)) {
                    tableSelect.setValue(tableData);
                }

                const isSelectAll = /select\s+\*/i.test(storedQuery);
                if (!isSelectAll) {
                    const selectMatch = storedQuery.match(/select\s+(.+?)\s+from/i);
                    if (selectMatch?.[1]) {
                        selectMatch[1]
                            .split(',')
                            .map(c => c.trim().replace(/["[\]]/g, '').split(/\s+as\s+/i).pop().toLowerCase())
                            .forEach(col => selectedCols.add(col));
                    }
                }
                return new Promise((resolve, reject) => {
                    if (!schemaData || !tableData) {
                        reject('Schema or Table not found');
                        return;
                    }
                    $.ajax({
                        url: datasetURL.getTableColumns,
                        type: "POST",
                        contentType: "application/json",
                        dataType: "json",
                        data: JSON.stringify({ schemaName: schemaData, tableName: tableData }),
                        success: function (res) {
                            if (res?.success && Array.isArray(res.data)) {
                                const rightItems = [];
                                const leftItems = [];
                                const isAll = isSelectAll;
                                res.data.forEach(col => {
                                    const colName = (col || '').trim();
                                    if (!colName) return;
                                    const li = `<li draggable="true">${colName}</li>`;
                                    if (isAll || selectedCols.has(colName.toLowerCase()))
                                        rightItems.push(li);
                                    else
                                        leftItems.push(li);
                                });
                                $rightList.html(rightItems.join(''));
                                $leftList.html(leftItems.join(''));
                                resolve();
                            } else {
                                reject('Failed to fetch column list');
                            }
                        },
                        error: () => reject('AJAX column error')
                    });
                });
            }).then(() => {
                setTimeout(() => $query.val(storedQuery), 200);
                updateQuery();
                checkColumnData?.();
            }).catch(err => {
                console.error("Error:", err);
                showToast('error', typeof err === 'string' ? err : 'Column loading failed');
            });
        }

        //function validateQuery(query) {
        //    let queryPattern;
        //    switch (dbValue?.toLowerCase()) {
        //        case 'oracle':                 
        //            queryPattern = /^SELECT\s+.+\s+FROM\s+"[^"]+"\."[^"]+"(?:\s+WHERE\s+.+)?$/i;
        //            break;
        //        case 'mysql':                   
        //            queryPattern = /^SELECT\s+.+\s+FROM\s+`[^`]+`\.`[^`]+`(?:\s+WHERE\s+.+)?$/i;
        //            break;
        //        case 'mssql':                   
        //            queryPattern = /^SELECT\s+.+\s+FROM\s+(\[[^\]]+\]\.)?\[dbo\]\.\[[^\]]+\](\s+\w+)?(,\s*(\[[^\]]+\]\.)?\[dbo\]\.\[[^\]]+\](\s+\w+)?)*\s*$/i;
        //            break;
        //        default:                    
        //            $('#storedQueryValidation').text('Fill the valid stored query').show();
        //            $('#errorquery').addClass('is-invalid');
        //            return false;
        //    }

        //    if (!query?.trim()) {
        //        $('#storedQueryValidation').text('Fill the stored query').show();
        //        $('#errorquery').addClass('is-invalid');
        //        return false;
        //    }

        //    const statements = query.trim().split(/;\s*|\n+/).filter(Boolean);

        //    const isValid = statements.every(statement => queryPattern.test(statement.trim()));

        //    if (!isValid) {
        //        $('#storedQueryValidation').text('Enter valid query').show();
        //        $('#errorquery').addClass('is-invalid');
        //        return false;
        //    }
        //    // Clear validation on success
        //    $('#storedQueryValidation').text('').hide();
        //    $('#errorquery').removeClass('is-invalid');
        //    return true;
        //}

        async function RunQuery(query) {
            //validateQuery(query);
            const tableNames = $('#tableName option:selected').map(function () {
                return $(this).text().trim().toLowerCase();
            }).get();

            if (!tableNames.length || !tableNames[0]) {               
                return false;
            }

            if (!query?.trim()) {
                $('#StoredQuery-error').text('Please provide a stored query.').addClass('field-validation-error');
                return false;
            }

            // Determine DB type
            const db = dbValue?.toLowerCase();

            let queryTables = [];
            if (db === 'mysql') {
                queryTables = [...query.matchAll(/`[^`]+`\.`([^`]+)`/gi)].map(m => m[1]?.toLowerCase());
            } else if (db === 'oracle') {
                queryTables = [...query.matchAll(/"[^"]+"\."([^"]+)"/gi)].map(m => m[1]?.toLowerCase());
            } else {
                // Default to MSSQL
                queryTables = [...query.matchAll(/\[dbo\]\.\[([^\]]+)\]/gi)].map(m => m[1]?.toLowerCase());
            }

            const unmatched = tableNames.filter(name => !queryTables.includes(name));
            if (unmatched.length > 0) {
                showToast('error', `Table(s) [${unmatched.join(', ')}] not found in the query.`);
                return false;
            }

            return new Promise((resolve, reject) => {
                $.ajax({
                    method: "GET",
                    url: datasetURL.runQuery,
                    data: { runQuery: query },
                    dataType: "json",
                    success: function (response) {
                        $("#datasetWrapper").empty();

                        if (response?.success && response?.data) {
                            let tableList = typeof response.data.tableValue === "string"
                                ? JSON.parse(response.data.tableValue)
                                : response.data.tableValue;

                            if (!tableList || tableList.length === 0) {
                                const imageHtml = `<img src=".../wwwroot/img/Report-icons/Common-icons/no_data_found.svg" style="width: 385px;padding-top: 81px;" class="Card_NoData_Img">`;
                                $("#datasetWrapper").css('text-align', 'center').html(imageHtml).show();
                                return resolve([]);
                            }

                            tableList.forEach((table, index) => {
                                if (!table || table.length === 0) return;

                                let columns = Object.keys(table[0]);

                                let tableHtml = `
                            <div class="card mb-4 shadow-sm p-3 bg-white rounded">
                                <h6 class="fw-bold">Result Table ${index + 1}</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead class="table-light">
                                            <tr>${columns.map(col => `<th>${col}</th>`).join("")}</tr>
                                        </thead>
                                        <tbody>
                                            ${table.map(row =>
                                    `<tr>${columns.map(col => `<td>${row[col] ?? ""}</td>`).join("")}</tr>`
                                ).join("")}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        `;

                                $("#datasetWrapper").append(tableHtml);
                            });

                            resolve(tableList);
                        } else {
                            $('#StoredQuery-error').text('Enter valid query').addClass('field-validation-error');
                            resolve([]);
                        }
                    },
                    error: function (xhr) {
                        $('#StoredQuery-error').text('An error occurred while running query').addClass('field-validation-error');
                        console.error("RunQuery error:", xhr);
                        reject([]);
                    }
                });
            });
        }
        function loadSchemas() {
            const $schemaEl = $('#schemaName');

            // Ensure selectize is initialized
            if (!$schemaEl[0].selectize) {
                $schemaEl.selectize();
            }

            const schemaSelect = $schemaEl[0].selectize;

            $.ajax({
                url: datasetURL.getTableNames,
                type: "GET",
                dataType: "json",
                success: function (result) {
                    if (result?.success && Array.isArray(result.data)) {
                        schemaSelect.clearOptions();
                        schemaSelect.addOption({ value: "", text: "Select schema" });

                        const addedSchemas = new Set();

                        result.data.forEach(table => {
                            const schemaName = table.schemaName?.trim();
                            if (schemaName && !addedSchemas.has(schemaName)) {
                                addedSchemas.add(schemaName);
                                schemaSelect.addOption({ value: schemaName, text: schemaName });
                            }
                        });
                    }
                },
                error: function () {
                    showToast('error', 'Failed to load schema names from TableAccess.');
                }
            });
        }
        function loadTablesBySchema(schemaName) {
            return new Promise((resolve, reject) => {
                if (!schemaName) return resolve();

                const tableSelect = $('#tableName')[0]?.selectize;
                if (!tableSelect) return resolve(); // no selectize instance

                $.ajax({
                    url: datasetURL.getTableNames,
                    type: "GET",
                    dataType: "json",
                    success: function (result) {
                        tableSelect.clearOptions();

                        if (result?.success && Array.isArray(result.data)) {
                            const added = new Set();

                            result.data.forEach(table => {
                                const tableSchema = table.schemaName?.trim();
                                const tableName = table.tableName?.trim();
                                if (tableSchema === schemaName && tableName && !added.has(tableName)) {
                                    added.add(tableName);
                                    tableSelect.addOption({ value: tableName, text: tableName });
                                }
                            });

                            tableSelect.refreshOptions(false);
                            resolve();
                        } else {
                            reject("No tables found.");
                        }
                    },
                    error: function () {
                        reject("Failed to load table list.");
                    }
                });
            });
        }
        function showToast(type, message) {
            const trimmedMessage = message?.trim() || '';

            if (type === 'success') {
                $('#successToastMessage').text(trimmedMessage);
                const toast = bootstrap.Toast.getOrCreateInstance(document.getElementById('successToast'));
                toast.show();
            } else if (type === 'error') {
                $('#errorToastMessage').text(trimmedMessage);
                const toast = bootstrap.Toast.getOrCreateInstance(document.getElementById('errorToast'));
                toast.show();
            } else if (type === 'warning') {
                $('#warningToastMessage').text(trimmedMessage);
                const toast = bootstrap.Toast.getOrCreateInstance(document.getElementById('warningToast'));
                toast.show();
            }
        }

        $('#search-inp').on('input', function () {
            const searchVal = $(this).val().toLowerCase();

            $('#datasetTable tbody tr').each(function () {
                const datasetName = $(this).find('td:nth-child(2)').text().toLowerCase();
                $(this).toggle(datasetName.startsWith(searchVal));
            });
        });

        $('#datasetColumnName_rightSelected').on('click', function () {
            $('#leftList li.selected').each(function () {
                $(this).removeClass('selected').appendTo('#rightList');
            });

            updateQuery();
            checkColumnData();
        });

        $('#datasetColumnName_leftSelected').on('click', function () {
            $('#rightList li.selected').each(function () {
                $(this).removeClass('selected').appendTo('#leftList');
            });
            setTimeout(function () {
                if ($('#rightList').children().length === 0) {
                    $('#datasetStoredQuery').val('');
                } else {
                    if ($('#leftList').children().length || $('#rightList').children().length) {
                        updateQuery();
                    }
                }
            }, 100);

            checkColumnData();
        });

        $('#datasetColumnName_rightAll').on('click', function () {
            getAllQuery();
            let rightDDL = $("#datasetColumnName option");
            let newQueryResult = rightDDL.map((_, opt) => opt.text).get().join(',');
            queryResult = queryResult ? `${queryResult},${newQueryResult}` : newQueryResult;
            checkColumnData();
        });

        $('#datasetColumnName_leftAll').on('click', function () {
            queryResult = '';
            $("#datasetStoredQuery").val("");
            checkColumnData();
        });
        // Bind Create Dataset button click
        $('#createDataset').on('click', () => {
            ['#datasetName', '#datasetDesc', '#datasetStoredQuery', '#searchLeft', '#searchRight'].forEach(id => {
                $(id).val('');
            });
            $('#leftList, #rightList').empty();
            ['#schemaName', '#tableName'].forEach(selector => {
                const selectize = $(selector)[0]?.selectize;
                if (selectize) {
                    selectize.clearOptions();
                    selectize.clear();
                } else {
                    $(selector).empty();
                }
            });
            loadSchemas();
        });

        $('.loadcls').on("click", function () {
            $('#AddModal').modal('show');
        });

        $('#modelcls').on("click", function () {
            location.reload();
        });
        // For #datasetName (input event)
        $('#datasetName').on("input", function () {
            const val = $(this).val()?.trim();

            if (!val) {
                $('#datasetNameValidation').text('Enter the dataset name').show();
                $('#errordataset').addClass('is-invalid');
                return;
            }
            if (val.length < 3) {
                $('#datasetNameValidation').text('Dataset name must be at least 3 characters long.').show();
                $('#errordataset').addClass('is-invalid');
                return;
            }
            if (/^\d/.test(val)) {
                $('#datasetNameValidation').text('Dataset name cannot start with a number.').show();
                $('#errordataset').addClass('is-invalid');
                return;
            }
            $('#datasetNameValidation').hide();
            $('#errordataset').removeClass('is-invalid');

            $.ajax({
                url: datasetURL.nameExistUrl,
                type: 'GET',
                data: { datasetName: val },
                success: function (response) {
                    if (!response.success) {
                        $('#datasetNameValidation').text(response.message).show();
                        $('#errordataset').addClass('is-invalid');
                    } else {
                        $('#datasetNameValidation').hide();
                        $('#errordataset').removeClass('is-invalid');
                    }
                },
                error: function () {
                    console.error('Error validating dataset name.');
                }
            });
        });

        // For #schemaName (change event)
        $('#schemaName').on("change", function () {
            const val = $(this).val();
            if (val) {
                $('#schemaNameValidation').hide();
                $('#errorschemaname').removeClass('is-invalid');
            }
        });

        // For #tableName (change event, multi-select)
        $('#tableName').on("change", function () {
            const val = $(this).val();
            if (val && val.length > 0) {
                $('#tableNameValidation').hide();
                $('#errortablename').removeClass('is-invalid');
            }
        });

        $('#btnDatasetSave').on("click", function () {
            const id = datasetId; // assuming this is the dataset Id
            const name = $("#datasetName").val()?.trim();
            const description = $("#datasetDesc").val()?.trim();
            const storedQuery = $("#datasetStoredQuery").val()?.trim();
            const tableName = $('#tableName option:selected').map(function () {
                return $(this).text().trim();
            }).get().join(', ');
            const tableId = $("#tableName").val();
            const schemaName = $("#schemaName").val();

            let isValid = true;

            const datasetNameValidation = $('#datasetName').val()?.trim();
            const schemaNameValidation = $('#schemaName').val();
            const tableNameValidation = $('#tableName').val();
            const storedQueryValidation = $('#datasetStoredQuery').val()?.trim();

            if (!datasetNameValidation) {
                $('#datasetNameValidation').text('Enter the dataset name').show();
                $('#errordataset').addClass('is-invalid');
                isValid = false;
            }

            if (!schemaNameValidation) {
                $('#schemaNameValidation').text('Select the schema name').show();
                $('#errorschemaname').addClass('is-invalid');
                isValid = false;
            }

            if (!tableNameValidation || tableNameValidation.length === 0) {
                $('#tableNameValidation').text('Select the table name').show();
                $('#errortablename').addClass('is-invalid');
                isValid = false;
            }

            if (!storedQueryValidation) {
                $('#storedQueryValidation').text('Fill the stored query').show();
                $('#errorquery').addClass('is-invalid');
                isValid = false;
            }
            // let queryForm = validateQuery(query);
            const tableNames = $('#tableName option:selected').map(function () {
                return $(this).text().trim().toLowerCase();
            }).get();
            if (!storedQuery?.trim()) {
                $('#storedQueryValidation').text('Fill the stored query').show();
                $('#errorquery').addClass('is-invalid');
                return false;
            }

            // Determine DB type
            const db = dbValue?.toLowerCase();

            let queryTables = [];
            if (db === 'mysql') {
                queryTables = [...storedQuery.matchAll(/`[^`]+`\.`([^`]+)`/gi)].map(m => m[1]?.toLowerCase());
            } else if (db === 'oracle') {
                queryTables = [...storedQuery.matchAll(/"[^"]+"\."([^"]+)"/gi)].map(m => m[1]?.toLowerCase());
            } else {
                // Default to MSSQL
                queryTables = [...storedQuery.matchAll(/\[dbo\]\.\[([^\]]+)\]/gi)].map(m => m[1]?.toLowerCase());
            }

            const unmatched = tableNames.filter(name => !queryTables.includes(name));
            if (unmatched.length > 0) {
                showToast('error', `Table(s) [${unmatched.join(', ')}] not found in the query.`);
                return false;
            }

            if (!isValid) return;

            $.ajax({
                url: datasetURL.datasetCreateOrUpdate,
                type: "POST",
                dataType: "json",
                data: {
                    Id: id,
                    DataSetName: name,
                    Description: description,
                    TableName: tableName,
                    StoredQuery: storedQuery,
                    StoredProcedureName: "",
                    TableAccessId: tableId,
                    IsActive: true
                },
                success: function (result) {
                    if (result?.success) {
                        $('#AddModal').modal('hide');
                        showToast('success', 'Dataset saved successfully.');
                        location.reload();
                    } else {
                        showToast('error', result.message || 'Failed to save dataset.');
                    }
                },
                error: function () {
                    showToast('error', 'An error occurred while saving dataset.');
                }
            });
        });

        $('#tableName').selectize({
            placeholder: 'Select Table Name',
            plugins: ['remove_button'],
            maxItems: null, // allow multiple selections
            persist: false,
            create: false
        });
        let datasetId;
        $('#datasetTable').on('click', '.btnEdit', function () {
            datasetId = $(this).data('id');
            $.ajax({
                url: datasetURL.getById,
                type: "GET",
                dataType: "json",
                data: { id: datasetId },
                success: function (result) {
                    if (result?.success) {
                        populateModalFields(result.data);
                        $('#SaveFunction').text('Update');
                        $('#AddModal').modal('show');
                        checkColumnData();
                    } else {
                        showToast('error', result.message || 'Failed to load dataset.');
                    }
                },
                error: function () {
                    showToast('error', 'An error occurred while loading dataset.');
                }
            });
        });
        // Delete dataset entry
        $('#datasetTable').on('click', '.btnDelete', function () {
            const datasetId = $(this).data('id');

            if (!datasetId) {
                showToast('error', 'Invalid dataset ID.');
                return;
            }
            const confirmed = confirm("Are you sure you want to delete this dataset?");
            if (!confirmed) return;
            $.ajax({
                url: datasetURL.datasetDelete,
                type: "POST",
                dataType: "json",
                data: { id: datasetId },

                success: function (result) {                  
                    if (result?.success) {
                        showToast('success', 'Dataset deleted successfully.');
                        $('#datasetTable').DataTable().ajax.reload(null, false); 
                    } else {
                        showToast('error', result.message || 'Failed to delete dataset.');
                    }
                },

                error: function (xhr, status, error) {                   
                    console.error('Delete failed:', error);
                    showToast('error', 'An error occurred while deleting the dataset.');
                }
            });
        });

        $('#schemaName').on("change", function () {
            const selectedSchema = $(this).val();
            loadTablesBySchema(selectedSchema);
        });

        $('#tableName').on('change', function () {
            $('#datasetStoredQuery').val('');
            $('#leftList').empty();
            $('#rightList').empty();

            const selectedTable = $('#tableName option:selected').text()?.trim();

            if (!selectedTable) {
                showToast('warning', 'Please select a table.');
                return;
            }

            $.ajax({
                url: datasetURL.getTableColumns,
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                data: JSON.stringify({ tableName: selectedTable }),
                success: function (result) {
                    if (result?.success && Array.isArray(result.data)) {
                        result.data.forEach(column => {
                            const formattedColumn = `${column}`;
                            $('#leftList').append(`<li draggable="true">${formattedColumn}</li>`);
                        });
                    } else {
                        showToast('warning', `No columns found for table: ${selectedTable}`);
                    }
                },
                error: function () {
                    showToast('error', `Failed to load column data for table: ${selectedTable}`);
                }
            });
        });

        $('#btnRunQuery').on('click', async () => {
            const tableName = $('#tableName option:selected').map(function () {
                return $(this).text().trim();
            }).get().join(', ');

            const query = $('#datasetStoredQuery').val()?.trim();

            if (!query) {
                $('#StoredQuery-error').text('Please provide a stored query.').addClass('field-validation-error');
                return false;
            }

            $('#btnRunQuery, #tableName, #schemaName').prop('disabled', true);
            $('#datasetLoader').removeClass('d-none').show();

            $("#tablerow").empty();
            $("#tableData").empty();

            $("#datasetTable").hide();
            $("#datasetWrapper").show();

            try {
                const result = await RunQuery(query);

                if (result && result.length > 0) {
                    // Show modal
                    $('#AddModal').modal('hide');
                    $('#QueryListModal').modal('show');

                    // Scroll reset
                    $('#QueryListModal').off('shown.bs.modal').on('shown.bs.modal', function () {
                        $(this).find('.modal-body > div[style*="overflow:auto"]').scrollTop(0);
                    });

                    // Build headers from first row keys
                    const headers = Object.keys(result[0]);
                    headers.forEach(h => {
                        $('#tablerow').append(`<th>${h}</th>`);
                    });

                    // Build rows
                    result.forEach(row => {
                        const rowHtml = headers.map(h => `<td>${row[h]}</td>`).join('');
                        $('#tableData').append(`<tr>${rowHtml}</tr>`);
                    });
                } else {
                    showToast('No records returned from the query.', 'info');
                }
            } catch (error) {
                console.error(error);
                showToast('Error running query.', 'error');
            } finally {
                $('#datasetLoader').addClass('d-none').hide();
                $('#btnRunQuery, #tableName, #schemaName').prop('disabled', false);
            }
        });        
    });
});