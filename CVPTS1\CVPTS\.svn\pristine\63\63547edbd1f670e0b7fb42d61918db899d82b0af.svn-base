// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

var chart = am4core.create("RiskHeatMap_Chart", am4charts.XYChart);
chart.hiddenState.properties.opacity = 0; // this creates initial fade-in
chart.maskBullets = false;
if (chart.logo) {
    chart.logo.disabled = true;
}
chart.padding(0, 0, 0, 0)

var xAxis = chart.xAxes.push(new am4charts.CategoryAxis());
var yAxis = chart.yAxes.push(new am4charts.CategoryAxis());

xAxis.dataFields.category = "x";
yAxis.dataFields.category = "y";

xAxis.renderer.grid.template.disabled = true;
xAxis.renderer.minGridDistance = 40;

yAxis.renderer.grid.template.disabled = true;
yAxis.renderer.inversed = true;
yAxis.renderer.minGridDistance = 30;

var series = chart.series.push(new am4charts.ColumnSeries());
series.dataFields.categoryX = "x";
series.dataFields.categoryY = "y";
series.dataFields.value = "value"; // Uncommented to use the value field
series.sequencedInterpolation = true;
series.defaultState.transitionDuration = 3000;


// Adding label bullets
var labelBullet = series.bullets.push(new am4charts.LabelBullet());
labelBullet.label.text = "{value}";
labelBullet.label.fontSize = 10;
labelBullet.label.truncate = false;
labelBullet.label.fill = am4core.color("#000");
labelBullet.label.background.fill = am4core.color("#fff");
labelBullet.label.dx = -10;
labelBullet.label.dy = 2;
labelBullet.label.wrap = true;
labelBullet.label.padding(5, 10, 5, 10); // Adding padding



// Set up column appearance
var column = series.columns.template;
column.strokeWidth = 5;
column.strokeOpacity = 1;
column.stroke = am4core.color("#ffffff");
column.tooltipText = "{x}, {y}: {value}";
column.width = am4core.percent(100);
column.height = am4core.percent(100);
column.column.cornerRadius(10, 10, 10, 10);
column.propertyFields.fill = "color";

// define colors
var colors = {
    "insignificant": "#00FF00",
    "low": "#FFFF00",
    "medium": "#FFA500",
    "high": "#FF0000",
    "critical": "#800000",
    "blank": "#fff"
};

// Get risk data from the server
var allRiskData = [];
var baseHeatmapData = [];
var currentDisplayData = [];

// Function to initialize the risk data

$(document).ready(function () {
    fetchChartData();
});

function updateChartData(data) {
    chart.data = data;
    //console.log(data);
    chart.invalidateRawData(); // Refresh the chart with new data
}
function initializeRiskData() {alert("initializeRiskData");
    // Get the risk data from the model passed to the view
    if (typeof riskDataFromServer !== 'undefined' && riskDataFromServer) {
        allRiskData = riskDataFromServer;
        // Set the initial display to show all risks
        filterRiskData('all');
    } else {
        console.warn('No risk data available from server');
    }
}

// Function to filter risk data based on selection
function filterRiskData(filterType) {
    // Start with the base heatmap data
    currentDisplayData = JSON.parse(JSON.stringify(baseHeatmapData));

    if (!allRiskData || allRiskData.length === 0) {
        // If no risk data, just use the base heatmap
        chart.data = currentDisplayData;
        console.log('No risk data available for filtering');
        return;
    }

    console.log('Filtering risk data:', filterType);
    console.log('Total risks:', allRiskData.length);

    // Filter the risk data based on the selected filter type
    var filteredRisks = [];

    switch (filterType) {
        case 'all':
            filteredRisks = allRiskData;
            console.log('Showing all risks');
            break;
        case 'inherent':
            filteredRisks = allRiskData.filter(function (risk) {
                return risk.LikliHood && risk.Impact;
            });
            console.log('Showing inherent risks:', filteredRisks.length);
            break;
        case 'residual':
            filteredRisks = allRiskData.filter(function (risk) {
                return risk.ResidualLikeliHood && risk.ResidualImpact;
            });
            console.log('Showing residual risks:', filteredRisks.length);
            break;
        default:
            filteredRisks = allRiskData;
            console.log('Default: showing all risks');
    }

    // Update the chart with the filtered data
    updateChartWithRiskData(filteredRisks, filterType);
}

// Function to bind risk data to tables
function bindRiskDataToTables(riskData) {
    console.log('Binding risk data to tables');

    // Get references to both tables
    var heatmapTable = document.getElementById('tblheatmapSeverity');
    var severityTable = document.getElementById('tblheatmapSeverity1');

    if (!heatmapTable || !severityTable) {
        console.error('Tables not found in the DOM');
        return;
    }

    // Clear any existing data in the cells
    clearTableCells(heatmapTable);

    // Get the probability labels (rows) and impact labels (columns)
    var probabilityLabels = ["Rare", "Unlikely", "Possible", "Likely", "Almost Certain"];
    var impactLabels = ["Insignificant", "Low", "Medium", "High", "Critical"];

    // Update the heatmap table with risk counts and risk codes
    for (var i = 0; i < probabilityLabels.length; i++) {
        var probability = probabilityLabels[i];
        var row = heatmapTable.rows[i + 2]; // +2 to skip the header rows

        if (!row) {
            console.warn('Row not found for probability:', probability);
            continue;
        }

        for (var j = 0; j < impactLabels.length; j++) {
            var impact = impactLabels[j];
            var cell = row.cells[j + 1]; // +1 to skip the first column (labels)

            if (!cell) {
                console.warn('Cell not found for impact:', impact);
                continue;
            }

            // Create a key for this cell
            var key = probability + '|' + impact;

            // Get the data for this cell
            var cellData = riskData[key];

            if (cellData && cellData.count > 0) {
                // Create a div to hold the count and risk codes
                var countDiv = document.createElement('div');
                countDiv.className = 'risk-count';
                countDiv.textContent = cellData.count;

                // Create a div to hold the risk codes
                var codesDiv = document.createElement('div');
                codesDiv.className = 'risk-codes';

                // Add each risk code
                if (cellData.riskCodes && cellData.riskCodes.length > 0) {
                    cellData.riskCodes.forEach(function (code) {
                        var codeSpan = document.createElement('span');
                        codeSpan.className = 'risk-code';
                        codeSpan.textContent = code;
                        codeSpan.title = 'Risk Code: ' + code;
                        codesDiv.appendChild(codeSpan);

                        // Add a space between codes
                        codesDiv.appendChild(document.createTextNode(' '));
                    });
                }

                // Clear the cell and add the new content
                cell.innerHTML = '';
                cell.appendChild(countDiv);
                cell.appendChild(codesDiv);

                // Add a tooltip with all risk codes
                if (cellData.riskCodes && cellData.riskCodes.length > 0) {
                    cell.title = 'Risk Codes: ' + cellData.riskCodes.join(', ');
                }
            } else {
                cell.textContent = '';
                cell.title = '';
            }
        }
    }

    console.log('Tables updated with risk counts and codes');
}

// Helper function to clear table cells
function clearTableCells(table) {
    if (!table) return;

    // Start from row 2 (skip headers) and column 1 (skip first column)
    for (var i = 2; i < table.rows.length; i++) {
        var row = table.rows[i];
        for (var j = 1; j < row.cells.length; j++) {
            row.cells[j].textContent = '';
        }
    }
}

// Function to update the chart with risk data
function updateChartWithRiskData(risks, filterType) {
    // Reset the chart data to the base heatmap
    chart.data = JSON.parse(JSON.stringify(baseHeatmapData));

    // If no risks, just return
    if (!risks || risks.length === 0) {
        console.log('No risks to display after filtering');
        chart.invalidateData();
        // Clear the tables
        bindRiskDataToTables({});
        return;
    }

    console.log('Updating chart with', risks.length, 'risks');

    // Create a data structure to track risk counts and codes in each cell
    var riskData = {};

    // Process each risk and update the data structure
    risks.forEach(function (risk) {
        var probability = '';
        var impact = '';
        var riskCode = risk.RiskCode || 'Unknown';

        if (filterType === 'inherent' || filterType === 'all') {
            // Use inherent risk values
            probability = mapProbabilityToLabel(risk.LikliHood);
            impact = mapImpactToLabel(risk.Impact);

            // If we have valid probability and impact values
            if (probability && impact) {
                // Create a key for this cell
                var key = probability + '|' + impact;

                // Initialize the cell data if it doesn't exist
                if (!riskData[key]) {
                    riskData[key] = {
                        count: 0,
                        riskCodes: []
                    };
                }

                // Increment the count
                riskData[key].count++;

                // Add the risk code if it's not already in the list
                if (riskCode && !riskData[key].riskCodes.includes(riskCode)) {
                    riskData[key].riskCodes.push(riskCode);
                }
            }
        }

        if (filterType === 'residual' || filterType === 'all') {
            // Use residual risk values if we're filtering for residual or showing all
            var resProbability = mapProbabilityToLabel(risk.ResidualLikeliHood);
            var resImpact = mapImpactToLabel(risk.ResidualImpact);

            // If we have valid residual values and we're either showing residual or all risks
            if (resProbability && resImpact && (filterType === 'residual' || filterType === 'all')) {
                // Create a key for this cell
                var resKey = resProbability + '|' + resImpact;

                // Initialize the cell data if it doesn't exist
                if (!riskData[resKey]) {
                    riskData[resKey] = {
                        count: 0,
                        riskCodes: []
                    };
                }

                // Increment the count
                riskData[resKey].count++;

                // Add the risk code if it's not already in the list
                if (riskCode && !riskData[resKey].riskCodes.includes(riskCode)) {
                    riskData[resKey].riskCodes.push(riskCode);
                }
            }
        }
    });

    // Update the chart data with the counts
    chart.data.forEach(function (cell) {
        var key = cell.y + '|' + cell.x;
        if (riskData[key] && riskData[key].count > 0) {
            cell.value = riskData[key].count;
        } else {
            cell.value = 0; // No risks in this cell
        }
    });

    // Update the chart
    chart.invalidateData();
    console.log('Chart updated with risk counts');

    // Bind the risk data to the tables
    bindRiskDataToTables(riskData);
}

// Helper function to map probability values to labels
function mapProbabilityToLabel(probability) {
    // This mapping should be adjusted based on your actual data
    if (!probability) return '';

    // Convert to string and normalize
    var prob = String(probability).toLowerCase();

    if (prob.includes('1') || prob.includes('rare')) return 'Rare';
    if (prob.includes('2') || prob.includes('unlikely')) return 'Unlikely';
    if (prob.includes('3') || prob.includes('possible')) return 'Possible';
    if (prob.includes('4') || prob.includes('likely')) return 'Likely';
    if (prob.includes('5') || prob.includes('almost')) return 'Almost Certain';

    return '';
}

// Helper function to map impact values to labels
function mapImpactToLabel(impact) {
    // This mapping should be adjusted based on your actual data
    if (!impact) return '';

    // Convert to string and normalize
    var imp = String(impact).toLowerCase();

    if (imp.includes('1') || imp.includes('insignificant')) return 'Insignificant';
    if (imp.includes('2') || imp.includes('low')) return 'Low';
    if (imp.includes('3') || imp.includes('medium')) return 'Medium';
    if (imp.includes('4') || imp.includes('high')) return 'High';
    if (imp.includes('5') || imp.includes('critical')) return 'Critical';

    return '';
}

// Define the probability (y-axis) and impact (x-axis) labels
const probabilityLabels = ["Rare", "Unlikely", "Possible", "Likely", "Almost Certain"];
const impactLabels = ["Insignificant", "Low", "Medium", "High", "Critical"];

// Function to determine color based on risk score
function getRiskColor(score) {
    if (score <= 1) return colors.insignificant;
    if (score <= 4) return colors.low;
    if (score <= 10) return colors.medium;
    if (score <= 16) return colors.high;
    return colors.critical;
}

// Generate heatmap data dynamically
let heatmapData = [];

// Loop through each probability and impact combination
for (let probIndex = 0; probIndex < probabilityLabels.length; probIndex++) {
    for (let impactIndex = 0; impactIndex < impactLabels.length; impactIndex++) {
        // Calculate risk score: (probability index + 1) * (impact index + 1)
        const probValue = probIndex + 1;
        const impactValue = impactIndex + 1;
        const riskScore = probValue * impactValue;

        // Add cell to heatmap data
        heatmapData.push({
            "y": probabilityLabels[probIndex],
            "x": impactLabels[impactIndex],
            "color": getRiskColor(riskScore),
            "value": riskScore
        });
    }
}

// Define the base heatmap data and initialize the chart
baseHeatmapData = heatmapData;
chart.data = baseHeatmapData; // Set the chart data immediately

var baseWidth = Math.min(chart.plotContainer.maxWidth, chart.plotContainer.maxHeight);
var maxRadius = baseWidth / Math.sqrt(chart.data.length) / 2 - 2; // 2 is just a margin

chart.plotContainer.events.on("maxsizechanged", function () {
    // Recalculate the max radius when the container size changes
    var newBaseWidth = Math.min(chart.plotContainer.maxWidth, chart.plotContainer.maxHeight);
    var newMaxRadius = newBaseWidth / Math.sqrt(chart.data.length) / 2 - 2;

    // Update the series bullet size if needed
    if (chart.series.length > 0 && chart.series[0].bullets.length > 0) {
        chart.series[0].bullets.getIndex(0).circle.radius = newMaxRadius;
    }
})

// Function to show risk details when a risk code is clicked
function showRiskDetails(riskCode) {
    console.log('Showing details for risk code:', riskCode);

    // Find the risk with the matching code
    var risk = allRiskData.find(function (r) {
        return r.RiskCode === riskCode;
    });

    if (risk) {
        // Create a formatted message with risk details
        var message = 'Risk Details:\n';
        message += '- Risk Code: ' + (risk.RiskCode || 'N/A') + '\n';
        message += '- Risk Description: ' + (risk.RiskDescription || 'N/A') + '\n';
        message += '- Risk Category: ' + (risk.RiskCategory || 'N/A') + '\n';
        message += '- Inherent Impact: ' + (risk.Impact || 'N/A') + '\n';
        message += '- Inherent Likelihood: ' + (risk.LikliHood || 'N/A') + '\n';
        message += '- Residual Impact: ' + (risk.ResidualImpact || 'N/A') + '\n';
        message += '- Residual Likelihood: ' + (risk.ResidualLikeliHood || 'N/A') + '\n';

        // Show the details in an alert
        alert(message);

        // Alternatively, you could populate a modal with this information
        // and show it instead of using an alert
    } else {
        alert('Risk details not found for code: ' + riskCode);
    }
}
var data1 = [];
function fetchChartData() {

    $.ajax({
        // url: 'http://**************/CVCore/Dashboard/GetCalendarData',
        url: 'http://localhost:5164/Dashboard/GetChartData',
        //url: '@Url.Action("GetChartData", "Dashboard")',
        method: "GET",
        dataType: "json",

        success: function (response) {
            var events = [];
            $.each(response, function (i, response) {
                events.push({
                    y: response.y,
                    x: response.x,
                    value: response.value,
                    color: "#D6CCE6"
                });
            });
            data1 = events;
            alert("Data");
            //console.log(data1);
            bindChartData(events);
            // console.log(events);
            //successCallback(events);
        },
        error: function (response) {
            alert('error');
        },
    });

    //fetch('/Dashboard/GetChartData', { // Update with your actual controller path
    //    method: 'GET',
    //    headers: {
    //        'Content-Type': 'application/json'
    //    }
    //})
    //    .then(response => response.json())
    //    .then(data => {
    //        debugger;
    //        var events = [];

    //        $.each(data, function (i, data) {
    //            events.push({

    //                y: data.y,
    //                x: data.x,
    //                value: data.value,
    //                color: "#D6CCE6",

    //            });
    //        });
    //        allRiskData = events;
    //        debugger;
    //        data1 = events;
    //        updateChartData(events);
    //    })
    //    .catch(error => {
    //        console.error('Error fetching data:', error);
    //    });
}


// Add event listeners for the radio buttons
document.addEventListener('DOMContentLoaded', function () {
    fetchChartData();
});


function bindChartData(chartData) {
    debugger;
    // Make sure the chart is initialized with the dynamic heatmap data
    if (!chartData.data || chartData.data.length === 0) {
        chartData.data = baseHeatmapData;
        console.log('Initialized chart with base heatmap data');
    }

    // Get the risk data from the server
    try {
        if (typeof chartData !== 'undefined' && chartData) {
            // The data is already parsed by Html.Raw and Newtonsoft.Json.JsonConvert.SerializeObject
            allRiskData = chartData;
            console.log('Successfully loaded risk data, count:', allRiskData.length);

            // Log a sample risk to see its structure
            if (allRiskData.length > 0) {
                console.log('Sample risk data:', allRiskData[0]);
            }

            // Initialize the tables with empty data
            bindRiskDataToTables({});

            // Add event delegation for risk code clicks
            document.addEventListener('click', function (event) {
                if (event.target.classList.contains('risk-code')) {
                    var riskCode = event.target.textContent;
                    showRiskDetails(riskCode);
                }
            });
        } else {
            console.warn('No risk data available');
        }
    }
    catch (e) {
        console.error('Error accessing risk data:', e);
    }

    // Get references to the radio buttons
    var allRisksRadio = document.getElementById('inlineRadio1');
    var inherentRisksRadio = document.getElementById('inlineRadio2');
    var residualRisksRadio = document.getElementById('inlineRadio3');

    // Add event listeners to the radio buttons
    if (allRisksRadio) {
        allRisksRadio.addEventListener('change', function () {
            if (this.checked) {
                console.log('All risks selected');
                filterRiskData('all');
            }
        });
    } else {
        console.warn('All risks radio button not found');
    }

    if (inherentRisksRadio) {
        inherentRisksRadio.addEventListener('change', function () {
            if (this.checked) {
                console.log('Inherent risks selected');
                filterRiskData('inherent');
            }
        });
    } else {
        console.warn('Inherent risks radio button not found');
    }

    if (residualRisksRadio) {
        residualRisksRadio.addEventListener('change', function () {
            if (this.checked) {
                console.log('Residual risks selected');
                filterRiskData('residual');
            }
        });
    } else {
        console.warn('Residual risks radio button not found');
    }

    // Initialize with all risks selected (default)
    if (allRisksRadio && allRisksRadio.checked) {
        console.log('Initializing with all risks (default)');
        filterRiskData('all');
    } else if (inherentRisksRadio && inherentRisksRadio.checked) {
        console.log('Initializing with inherent risks');
        filterRiskData('inherent');
    } else if (residualRisksRadio && residualRisksRadio.checked) {
        console.log('Initializing with residual risks');
        filterRiskData('residual');
    } else {
        console.log('No radio button selected, defaulting to all risks');
        filterRiskData('all');
    }
}