﻿@{
    ViewBag.Title = "ManageIMRecords";
    Layout = "~/Views/Shared/_Layout.cshtml";

}
<style>
  
</style>


<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">
        Manage IMRecords
    </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">


        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <a asp-controller="Unit" asp-action="AddUnit" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</a>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>
<div class="Page-Condant  border-0 pe-2" style="height: calc(100vh - 108px);overflow: auto;">
    <div class="card-body">


        <div class="row g-3">
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center gap-2">
                                <span>
                                   @*  <img src="/img/profile-img/usericon.svg" class="img-fluid" /> *@

                                </span>
                                <span class="d-grid">
                                    <span class="text-primary">INC-2024-160</span>
                                    <span>Name:  Test Recovery Plan 2</span>
                                </span>
                            </div>
                            <span class="badge text-bg-success">Completed</span>
                        </div>
                    </div>
                    <div class="card-body py-0">
                        <div>
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Details</span>
                                        <p class="mb-0"><span class="me-1">Neeraj Sahu</span><span>9850745056</span></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Incident Time</span>
                                        <p class="mb-0">03-06-2024 12:10:24</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Mail ID</span>
                                        <p class="mb-0"><EMAIL></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Notified As</span>
                                        <p class="mb-0">Drill Notication</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between py-1">
                        <div class="">
                            <button class="btn btn-sm btn-primary"><i class="cv-view_report me-1 align-middle"></i>View Report</button>
                            <button class="btn btn-sm btn-outline-primary"><i class="cv-actions me-1 align-middle"></i>Action</button>
                        </div>
                        <div>
                            <span class="text-muted">Cost</span>
                            <p class="fw-semibold mb-0">$1280.25</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center gap-2">
                                <span>
                                    @* <img src="/img/profile-img/usericon.svg" class="img-fluid" /> *@

                                </span>
                                <span class="d-grid">
                                    <span class="text-primary">INC-2024-160</span>
                                    <span>Name:  Test Recovery Plan 2</span>
                                </span>
                            </div>
                            <span class="badge text-bg-warning text-white">Pending Approval</span>
                        </div>
                    </div>
                    <div class="card-body py-0">
                        <div>
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Details</span>
                                        <p class="mb-0"><span class="me-1">Neeraj Sahu</span><span>9850745056</span></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Incident Time</span>
                                        <p class="mb-0">03-06-2024 12:10:24</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Mail ID</span>
                                        <p class="mb-0"><EMAIL></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Notified As</span>
                                        <p class="mb-0">Drill Notication</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between py-1">
                        <div class="">
                            <button class="btn btn-sm btn-primary"><i class="cv-view_report me-1 align-middle"></i>View Report</button>
                            <button class="btn btn-sm btn-outline-primary"><i class="cv-actions me-1 align-middle"></i>Action</button>
                        </div>
                        <div>
                            <span class="text-muted">Cost</span>
                            <p class="fw-semibold mb-0">$1280.25</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center gap-2">
                                <span>
                                    @* <img src="/img/profile-img/usericon.svg" class="img-fluid" /> *@

                                </span>
                                <span class="d-grid">
                                    <span class="text-primary">INC-2024-160</span>
                                    <span>Name:  Test Recovery Plan 2</span>
                                </span>
                            </div>
                            <span class="badge text-bg-danger">Rejected</span>
                        </div>
                    </div>
                    <div class="card-body py-0">
                        <div>
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Details</span>
                                        <p class="mb-0"><span class="me-1">Neeraj Sahu</span><span>9850745056</span></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Incident Time</span>
                                        <p class="mb-0">03-06-2024 12:10:24</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Mail ID</span>
                                        <p class="mb-0"><EMAIL></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Notified As</span>
                                        <p class="mb-0">Drill Notication</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between py-1">
                        <div class="">
                            <button class="btn btn-sm btn-primary"><i class="cv-view_report me-1 align-middle"></i>View Report</button>
                            <button class="btn btn-sm btn-outline-primary"><i class="cv-actions me-1 align-middle"></i>Action</button>
                        </div>
                        <div>
                            <span class="text-muted">Cost</span>
                            <p class="fw-semibold mb-0">$1280.25</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center gap-2">
                                <span>
                                    @* <img src="/img/profile-img/usericon.svg" class="img-fluid" /> *@

                                </span>
                                <span class="d-grid">
                                    <span class="text-primary">INC-2024-160</span>
                                    <span>Name:  Test Recovery Plan 2</span>
                                </span>
                            </div>
                            <span class="badge text-bg-success">Completed</span>
                        </div>
                    </div>
                    <div class="card-body py-0">
                        <div>
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Details</span>
                                        <p class="mb-0"><span class="me-1">Neeraj Sahu</span><span>9850745056</span></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Incident Time</span>
                                        <p class="mb-0">03-06-2024 12:10:24</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Mail ID</span>
                                        <p class="mb-0"><EMAIL></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Notified As</span>
                                        <p class="mb-0">Drill Notication</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between py-1">
                        <div class="">
                            <button class="btn btn-sm btn-primary"><i class="cv-view_report me-1 align-middle"></i>View Report</button>
                            <button class="btn btn-sm btn-outline-primary"><i class="cv-actions me-1 align-middle"></i>Action</button>
                        </div>
                        <div>
                            <span class="text-muted">Cost</span>
                            <p class="fw-semibold mb-0">$1280.25</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center gap-2">
                                <span>
                              @*       <img src="/img/profile-img/usericon.svg" class="img-fluid" /> *@

                                </span>
                                <span class="d-grid">
                                    <span class="text-primary">INC-2024-160</span>
                                    <span>Name:  Test Recovery Plan 2</span>
                                </span>
                            </div>
                            <span class="badge text-bg-danger">Rejected</span>
                        </div>
                    </div>
                    <div class="card-body py-0">
                        <div>
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Details</span>
                                        <p class="mb-0"><span class="me-1">Neeraj Sahu</span><span>9850745056</span></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Incident Time</span>
                                        <p class="mb-0">03-06-2024 12:10:24</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Mail ID</span>
                                        <p class="mb-0"><EMAIL></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Notified As</span>
                                        <p class="mb-0">Drill Notication</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between py-1">
                        <div class="">
                            <button class="btn btn-sm btn-primary"><i class="cv-view_report me-1 align-middle"></i>View Report</button>
                            <button class="btn btn-sm btn-outline-primary"><i class="cv-actions me-1 align-middle"></i>Action</button>
                        </div>
                        <div>
                            <span class="text-muted">Cost</span>
                            <p class="fw-semibold mb-0">$1280.25</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center gap-2">
                                <span>
                                    @* <img src="/img/profile-img/usericon.svg" class="img-fluid" /> *@

                                </span>
                                <span class="d-grid">
                                    <span class="text-primary">INC-2024-160</span>
                                    <span>Name:  Test Recovery Plan 2</span>
                                </span>
                            </div>
                            <span class="badge text-bg-warning text-white">Pending Approval</span>
                        </div>
                    </div>
                    <div class="card-body py-0">
                        <div>
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Details</span>
                                        <p class="mb-0"><span class="me-1">Neeraj Sahu</span><span>9850745056</span></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Incident Time</span>
                                        <p class="mb-0">03-06-2024 12:10:24</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="text-muted">Notifier Mail ID</span>
                                        <p class="mb-0"><EMAIL></p>

                                    </td>
                                    <td>
                                        <span class="text-muted">Notified As</span>
                                        <p class="mb-0">Drill Notication</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between py-1">
                        <div class="">
                            <button class="btn btn-sm btn-primary"><i class="cv-view_report me-1 align-middle"></i>View Report</button>
                            <button class="btn btn-sm btn-outline-primary"><i class="cv-actions me-1 align-middle"></i>Action</button>
                        </div>
                        <div>
                            <span class="text-muted">Cost</span>
                            <p class="fw-semibold mb-0">$1280.25</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>


    <!-- Configuration Modal -->
    <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                  @*   <h6 class="Page-Title">
                        Configure Language Master Form
                    </h6> *@
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pt-0">
                    <div class="row">
                        <div class="col-6">
                            <div class="card shadow-sm mb-2">
                                <div class="card-header">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="text-muted">Incident Name</span>
                                            <span class="fw-semibold">Test Recovery Plan (INC-2024-160)</span>
                                        </div>
                                        <span class="badge text-bg-success text-white">Completed</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="text-muted">Notification Date</span>
                                            <span class="text-primary fw-semibold">11-05-2024 12:10</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="text-muted">Notification Type:</span>
                                            <span class="text-primary fw-semibold">Drill</span>
                                        </div>
                                    </div>
                                    <div>
                                        <table class="table table-borderless mb-0">
                                            <tr>
                                                <td>
                                                    <span class="text-muted">Notifier Name</span>
                                                    <p class="mb-0 fw-semibold"><i class="cv-user me-1"></i>Neeraj Sahu</p>
                                                </td>
                                                <td>
                                                    <span class="text-muted">Notifier Phone</span>
                                                    <p class="mb-0 fw-semibold"><i class="cv-Mobile me-1"></i>9087756341</p>
                                                </td>
                                                <td>
                                                    <span class="text-muted">Notifier Email</span>
                                                    <p class="mb-0 fw-semibold"><i class="cv-mail me-1"></i><EMAIL></p>
                                                </td>
                                            </tr>

                                        </table>
                                    </div>
                                </div>

                            </div>
                            <div class="card shadow-sm">
                                <div class="card-header">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <span class="text-muted">Plan Name</span>
                                            <p class="mb-0 fw-semibold">Test Recovery Plan 1</p>
                                        </div>
                                        <p class="fw-semibold mb-0">Total of <span class="text-primary">6</span> Tasks Inplan</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                                        <i class="cv-process-bia fs-6 align-middle" style="color:var(--bs-orange)"></i>
                                                    </div>
                                                    <span class="fw-medium">Tasks Not Initiated</span>
                                                    <span class="fw-medium" style="color:var(--bs-orange)">05</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="icon-circle" style="background-color:var(--bs-info-bg-subtle)">
                                                        <i class="cv-process-bia fs-6 align-middle" style="color:var(--bs-primary-text-emphasis)"></i>
                                                    </div>
                                                    <span class="fw-medium">Task Assigned</span>
                                                    <span class="fw-medium" style="color:var(--bs-primary-text-emphasis)">00</span>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                                        <i class="cv-bia-section fs-6 align-middle" style="color:var(--bs-orange)"></i>
                                                    </div>
                                                    <span class="fw-medium">Task Acknowledged</span>
                                                    <span class="fw-medium" style="color:var(--bs-orange)">01</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="icon-circle" style="background-color:var(--bs-success-bg-subtle)">
                                                        <i class="cv-bia-section fs-6 align-middle text-success"></i>
                                                    </div>
                                                    <span class="fw-medium">Task In Progress</span>
                                                    <span class="fw-medium  text-success">01</span>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                                        <i class="cv-bia-section fs-6 align-middle" style="color:var(--bs-orange)"></i>
                                                    </div>
                                                    <span class="fw-medium">Task Completed</span>
                                                    <span class="fw-medium" style="color:var(--bs-orange)">01</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                                        <i class="cv-bia-section fs-6 align-middle text-warning"></i>
                                                    </div>
                                                    <span class="fw-medium">Tasks Reintiated</span>
                                                    <span class="fw-medium  text-warning">01</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                    <div class="text-end">
                                        <button class="btn btn-sm btn-primary">Send Status</button>
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-primary">Mark as Completed</button>
                                            <button class="btn btn-sm btn-secondary">Show All Plan Steps</button>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="col-6 dots-bg">
                            <div style="height:calc(100vh - 160px);overflow-y:auto">

                        
                            <div class="mx-auto" style="width: 30rem;">
                                <button type="button" class="btn btn-primary btn-sm">End</button>
                                    <div class="Escalation_Timeline" style=" margin-left: 8%;  margin-top: -2%">
                                    <ul class="ul">
                                        @* <li class="li"><button class="btn btn-sm btn-primary">End</button></li> *@
                                        <li class="li">
                                            <div class="Escalation_Timeline_Card card border-danger">
                                                <div class="d-flex align-items-center">
                                                    <span class="Timeline_Card_Level bg-danger badge bg-primary">
                                                            Step 1
                                                    </span>
                                                        <div class="d-grid ms-2">
                                                            <h6 class="mb-1 text-truncate" title="Management">
                                                                Step 1 (530)
                                                            </h6>
                                                           
                                                        </div>
                                                    <div class="d-flex ms-auto gap-2">
                                                        <span class="me-2 text-truncate">
                                                            Status:<span class="ms-1 text-warning">TaskAssigned</span>
                                                        </span>
                                                        <span class="me-2" role="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample"><i class="cv-down-arrow"></i></span>
                                                    </div>
                                                </div>
                                                <div class="collapse" id="collapseExample">
                                                    <div class="card card-body">
                                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                                            <div>
                                                                <p class="mb-0"><i class="cv-user me-1 align-middle"></i><span class="text-primary me-1">David Gunawan</span>(Step Owner)</p>
                                                                <p class="mb-0"><i class="cv-mail me-1 align-middle"></i><span class="me-1"><EMAIL></span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                <p class="mb-0"><i class="cv-Mobile me-1 align-middle"></i><span class="me-1">9087534131</span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                            </div>
                                                            <p class="mb-0 text-primary fw-semibold">Yet to Acknowledge</p>
                                                        </div>
                                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                                            <div>
                                                                <p class="mb-0">
                                                                    <i class="cv-user me-1 align-middle"></i><span class="text-primary me-1">Jim Jose</span>(Alternate Step Owner)
                                                                </p>
                                                                <p class="mb-0"><i class="cv-mail me-1 align-middle"></i><span class="me-1"><EMAIL></span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                <p class="mb-0"><i class="cv-Mobile me-1 align-middle"></i><span class="me-1">9087534131</span><i class="cv-error text-danger me-1 align-middle"></i></p>
                                                            </div>
                                                            <p class="mb-0 text-primary fw-semibold">Yet to Acknowledge</p>
                                                        </div>
                                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                                            <div>
                                                                <p class="mb-0"><i class="cv-user me-1 align-middle"></i><span class="text-primary me-1">David Gunawan</span>(Step Owner)</p>
                                                                <p class="mb-0"><i class="cv-mail me-1 align-middle"></i><span class="me-1"><EMAIL></span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                <p class="mb-0"><i class="cv-Mobile me-1 align-middle"></i><span class="me-1">9087534131</span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                            </div>
                                                            <p class="mb-0 text-success fw-semibold">Acknowledged</p>
                                                        </div>
                                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                                            <div>
                                                                <p class="mb-0 text-muted">Update Through:<span class="text-primary ms-1">CVault</span></p>
                                                                <p class="mb-0 text-muted">Step Description:<span class="text-dark ms-1">Step 2</span></p>

                                                            </div>
                                                            <button class="btn btn-sm btn-primary">View Escalation Status</button>
                                                        </div>
                                                    </div>
                                                    <div class="card-footer p-0">
                                                        <table class="table-sm table table-bordered">
                                                            <tr>
                                                                <td>
                                                                    <p class="mb-0 text-muted">Notification Date:<span class="ms-1">03-05-2024 12:25:00</span></p>
                                                                </td>
                                                                <td>
                                                                    <p class="mb-0 text-muted">Step After Success<span class="ms-1 text-success">Step 3 (532)</span></p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <p class="mb-0 text-muted">
                                                                        Est. Completion Time:<span class="ms-1">
                                                                            15 Minute(s)
                                                                        </span>
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="mb-0 text-muted">
                                                                        Step After Fails<span class="ms-1 text-danger">
                                                                            15 Minute(s)
                                                                        </span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <p class="mb-0 text-muted">
                                                                        Actual Completion Time
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="mb-0 text-muted">
                                                                        Interdependent<span class="ms-1 text-danger">
                                                                            Yes
                                                                        </span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <p class="mb-0 text-muted">
                                                                        Completion Time
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="mb-0 text-muted">
                                                                        Dependent On Step<span class="ms-1 text-danger">
                                                                            Step 1 (530)
                                                                        </span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <div class="d-flex align-items-center justify-content-end gap-2 mb-2 mx-2">
                                                            <button class="btn btn-sm btn-primary">Remarks</button>
                                                            <button class="btn btn-sm btn-primary">MarkAsCompleted</button>
                                                            <button class="btn btn-sm btn-primary">MarkAsFailed</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                            <li class="li">
                                                <div class="Escalation_Timeline_Card card border-warning">
                                                    <div class="d-flex align-items-center">
                                                        <span class="Timeline_Card_Level  badge bg-warning">
                                                            Step 2
                                                        </span>
                                                        <div class="d-grid ms-2">
                                                            <h6 class="mb-1 text-truncate" title="Management">
                                                                Step 2 (530)
                                                            </h6>

                                                        </div>
                                                        <div class="d-flex ms-auto gap-2">
                                                            <span class="me-2 text-truncate">
                                                                Status:<span class="ms-1 text-warning">TaskAssigned</span>
                                                            </span>
                                                            <span class="me-2" role="button" data-bs-toggle="collapse" data-bs-target="#collapseExample1" aria-expanded="false" aria-controls="collapseExample"><i class="cv-down-arrow"></i></span>
                                                        </div>
                                                    </div>
                                                    <div class="collapse" id="collapseExample1">
                                                        <div class="card card-body">
                                                            <div class="d-flex align-items-center justify-content-between mb-2">
                                                                <div>
                                                                    <p class="mb-0"><i class="cv-user me-1 align-middle"></i><span class="text-primary me-1">David Gunawan</span>(Step Owner)</p>
                                                                    <p class="mb-0"><i class="cv-mail me-1 align-middle"></i><span class="me-1"><EMAIL></span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                    <p class="mb-0"><i class="cv-Mobile me-1 align-middle"></i><span class="me-1">9087534131</span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                </div>
                                                                <p class="mb-0 text-primary fw-semibold">Yet to Acknowledge</p>
                                                            </div>
                                                            <div class="d-flex align-items-center justify-content-between mb-2">
                                                                <div>
                                                                    <p class="mb-0">
                                                                        <i class="cv-user me-1 align-middle"></i><span class="text-primary me-1">Jim Jose</span>(Alternate Step Owner)
                                                                    </p>
                                                                    <p class="mb-0"><i class="cv-mail me-1 align-middle"></i><span class="me-1"><EMAIL></span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                    <p class="mb-0"><i class="cv-Mobile me-1 align-middle"></i><span class="me-1">9087534131</span><i class="cv-error text-danger me-1 align-middle"></i></p>
                                                                </div>
                                                                <p class="mb-0 text-primary fw-semibold">Yet to Acknowledge</p>
                                                            </div>
                                                            <div class="d-flex align-items-center justify-content-between mb-2">
                                                                <div>
                                                                    <p class="mb-0"><i class="cv-user me-1 align-middle"></i><span class="text-primary me-1">David Gunawan</span>(Step Owner)</p>
                                                                    <p class="mb-0"><i class="cv-mail me-1 align-middle"></i><span class="me-1"><EMAIL></span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                    <p class="mb-0"><i class="cv-Mobile me-1 align-middle"></i><span class="me-1">9087534131</span><i class="cv-success text-success me-1 align-middle"></i></p>
                                                                </div>
                                                                <p class="mb-0 text-success fw-semibold">Acknowledged</p>
                                                            </div>
                                                            <div class="d-flex align-items-center justify-content-between mb-2">
                                                                <div>
                                                                    <p class="mb-0 text-muted">Update Through:<span class="text-primary ms-1">CVault</span></p>
                                                                    <p class="mb-0 text-muted">Step Description:<span class="text-dark ms-1">Step 2</span></p>

                                                                </div>
                                                                <button class="btn btn-sm btn-primary">View Escalation Status</button>
                                                            </div>
                                                        </div>
                                                        <div class="card-footer p-0">
                                                            <table class="table-sm table table-bordered">
                                                                <tr>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">Notification Date:<span class="ms-1">03-05-2024 12:25:00</span></p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">Step After Success<span class="ms-1 text-success">Step 3 (532)</span></p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">
                                                                            Est. Completion Time:<span class="ms-1">
                                                                                15 Minute(s)
                                                                            </span>
                                                                        </p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">
                                                                            Step After Fails<span class="ms-1 text-danger">
                                                                                15 Minute(s)
                                                                            </span>
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">
                                                                            Actual Completion Time
                                                                        </p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">
                                                                            Interdependent<span class="ms-1 text-danger">
                                                                                Yes
                                                                            </span>
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">
                                                                            Completion Time
                                                                        </p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="mb-0 text-muted">
                                                                            Dependent On Step<span class="ms-1 text-danger">
                                                                                Step 1 (530)
                                                                            </span>
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                            <div class="d-flex align-items-center justify-content-end gap-2 mb-2 mx-2">
                                                                <button class="btn btn-sm btn-primary">Remarks</button>
                                                                <button class="btn btn-sm btn-primary">MarkAsCompleted</button>
                                                                <button class="btn btn-sm btn-primary">MarkAsFailed</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>

                                    </ul>
                                        <button type="button" class="btn btn-primary btn-sm" style="margin-top: -3%; margin-left: -13%;">
                                            Start Escalation
                                        </button>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
              
            </div>
        </div>
    </div>
    <!--End Configuration Modal -->
    <!-- Delete Modal -->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header d-grid text-center">
                    <span class="fw-semibold">Do you really want to delete</span>
                    <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
                </div>
                <div class="modal-body text-center">
                    <img src="~/img/isomatric/delete.svg" width="260" />
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                    <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End Delete Modal -->
</div>
@* <div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Manage IMRecords</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

        <div class="input-group">
            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
            <select class="form-select form-select-sm" aria-label="Default select example">
                <option selected>Select Organization</option>
                <option value="1">PTS</option>
                <option value="2">TCS</option>
                <option value="3">Continuity Vault</option>
            </select>
        </div>



        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <a asp-controller="Unit" asp-action="AddUnit" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</a>

    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Incident Code</th>
                <th>Incident Name</th>
                <th>Incident Time</th>
                <th>Notifier Details</th>
                <th>Status</th>
                <th>Notified As</th>
                <th>Cost</th>
                <th>View Report</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>01</td>
                <td>
                    <ul class="ps-0 mb-0">
                        <li class="list-group-item fw-semibold text-primary">INC-2024-160</li>

                    </ul>
                </td>
                <td>Test Recovery Plan 2</td>
                <td>11/03/2024 12:10:05</td>
                <td>
                    <div class="d-flex">
                        <div class="User-icon">
                            <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                        </div>
                        <div>
                            <ul class="ps-0 mb-0">
                                <li class="list-group-item fw-semibold">Arun Patil</li>
                                <li class="list-group-item"><a class="text-primary" href="#"><EMAIL></a></li>
                                <li class="list-group-item">9021693184</li>

                            </ul>
                        </div>
                    </div>
                </td>
                <td class="text-success">Completed</td>
                <td class="text-warning">Drill</td>
                <td></td>
                <td>
                    <span class="btn-action" type="button"><i class="cv-view_report me-1"></i></span>

                </td>
                <td>
                    <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                    <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                </td>
            </tr>
            <tr>
                <td>02</td>
                <td>
                    <ul class="ps-0 mb-0">
                        <li class="list-group-item fw-semibold text-primary">INC-2024-161</li>

                    </ul>
                </td>
                <td>Test Recovery Plan 3</td>
                <td>11/03/2024 12:10:05</td>
                <td>
                    <div class="d-flex">
                        <div class="User-icon">
                            <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                        </div>
                        <div>
                            <ul class="ps-0 mb-0">
                                <li class="list-group-item fw-semibold">Arun Patil</li>
                                <li class="list-group-item"><a class="text-primary" href="#"><EMAIL></a></li>
                                <li class="list-group-item">9021693184</li>

                            </ul>
                        </div>
                    </div>
                </td>
                <td class="text-warning">InProgress</td>
                <td class="text-warning">Drill</td>
                <td></td>
                <td>
                    <span class="btn-action" type="button"><i class="cv-view_report me-1"></i></span>

                </td>
                <td>
                    <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                    <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                </td>
            </tr>
        </tbody>
    </table>




    <!-- Configuration Modal -->
    <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">Manage IMRecords Configuration</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <div class="accordion accordion-flush" id="accordionFlushExample">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                    Accordion Item #1
                                </button>
                            </h2>
                            <div id="flush-collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">Placeholder content for this accordion, which is intended to demonstrate the <code>.accordion-flush</code> class. This is the first item's accordion body.</div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                                    Accordion Item #2
                                </button>
                            </h2>
                            <div id="flush-collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">Placeholder content for this accordion, which is intended to demonstrate the <code>.accordion-flush</code> class. This is the second item's accordion body. Let's imagine this being filled with some actual content.</div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                                    Accordion Item #3
                                </button>
                            </h2>
                            <div id="flush-collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">Placeholder content for this accordion, which is intended to demonstrate the <code>.accordion-flush</code> class. This is the third item's accordion body. Nothing more exciting happening here in terms of content, but just filling up the space to make it look, at least at first glance, a bit more representative of how this would look in a real-world application.</div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--End Configuration Modal -->
    <!-- Delete Modal -->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header d-grid text-center">
                    <span class="fw-semibold">Do you really want to delete</span>
                    <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
                </div>
                <div class="modal-body text-center">
                    <img src="~/img/isomatric/delete.svg" width="260" />
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                    <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End Delete Modal -->
</div>
 *@