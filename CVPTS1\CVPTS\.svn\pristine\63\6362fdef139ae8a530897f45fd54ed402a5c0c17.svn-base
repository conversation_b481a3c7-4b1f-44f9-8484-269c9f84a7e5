﻿@model BCM.BusinessClasses.OtherBCMEntities
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    var SelectedOrgID = ViewBag.SelectedOrgID;
}

<form asp-action="EditOtherBCMEntities" method="post" id="editOtherBCMEntities" class="needs-validation progressive-validation" novalidate>
    <div class="row row-cols-2">
        <div style="display:none">
            <input type="hidden" asp-for="ID" id="Id" />
            <input type="hidden" asp-for="IsButtonVisible" id="IsVisible" />
            <input type="hidden" asp-for="EntityTypeID" id="EntityTypeID" />
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Organization</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                    <select id="orglist" class="form-select form-control" asp-for="OrgID" autocomplete="off" aria-label="Default select example" required>
                        <option selected disabled value="">-- All Organizations --</option>
                        @foreach (var objOrg in ViewBag.OrgInfo)
                        {
                            <!option value="@objOrg.Value" @(objOrg.Value == SelectedOrgID.ToString() ? "selected=\"selected\"" : "")>@objOrg.Text</!option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Organization</div>
            </div>
            <div class="form-group">
                <label class="form-label">Department</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-department"></i></span>
                    <select id="ddlDepartment" class="form-select form-control" asp-for="FunctionID" autocomplete="off" aria-label="Default select example">
                        <option selected value="0">-- All Departments --</option>
                        @foreach (var objDepartment in ViewBag.DepartmentInfo)
                        {
                            <option value="@objDepartment.Value">@objDepartment.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Departments</div>
            </div>
            <div class="form-group">
                <label class="form-label">Other BCM Entity</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-process-name"></i></span>
                    <input class="form-control" type="text" asp-for="EntityName" placeholder="Enter Process Name" required pattern="[A-Za-z][a-zA-Z0-9'&quot;,._$?!+&=#%`~\\/<>;:|{}\*\-+=()\s]{0,499}$" title="Process name must starts with character" />
                </div>
                <div class="invalid-feedback">Enter Other BCM Entity</div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Unit</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-unit"></i></span>
                    <select id="ddlUnit" class="form-select form-control" asp-for="UnitID" autocomplete="off" aria-label="Default select example">
                        <option selected value="0">-- All Units --</option>
                        @foreach (var objUnit in ViewBag.OrgUnit)
                        {
                            <option value="@objUnit.Value">@objUnit.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Units</div>
            </div>
            <div class="form-group">
                <label class="form-label">SubDepartment</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                    <select id="ddlSubDepartment" class="form-select form-control" asp-for="SubfunctionID" autocomplete="off" aria-label="Default select example">
                        <option selected value="0">-- All Sub Departments --</option>
                        @foreach (var objSubDepartment in ViewBag.SubFunction)
                        {
                            <option value="@objSubDepartment.Value">@objSubDepartment.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select SubDepartment</div>
            </div>
            <div class="form-group">
                <label class="form-label">Other BCM Entity Type</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-process-name"></i></span>
                    <select class="form-select form-select-sm" asp-for="OtherEntityType" required>
                        <option selected disabled value="">-- Select --</option>
                        <option value="0">Other</option>
                        <option value="1">Software</option>
                        <option value="2">HardWare</option>
                        <option value="3">equipment</option>
                        <option value="4">Functions</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Other BCM Entity Type</div>
            </div>
        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Entity Description</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <input class="form-control" type="text" placeholder="Enter Description" asp-for="EntityDescription" required />
                </div>
                <div class="invalid-feedback">Enter Description</div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
            <button type="button" class="btn btn-primary btn-sm" id="btnBcm">View BCM Details</button>
        </div>
    </div>
</form>


<script>

       $(document).ready(function () {
        // Show or hide the button based on the value of #IsVisible
        const isVisible = $("#IsVisible").val();
        if (isVisible == 0) {
            $("#btnBcm").hide();
        } else if (isVisible == 1) {
            $("#btnBcm").show();
        }

        // Button click event
        $('#btnBcm').click(function () {
            debugger;
            var iId = $("#Id").val();  // Capture the Id field value
            var iEntityTypeId =$("#EntityTypeID").val();
            // Check if the ID is present before proceeding
            if (iId) {
                $.ajax({
                    url: '@Url.Action("GetProcessId", "ManageBCMEntities", new { area = "BCMEntities" })',
                    type: 'POST',
                    data: { iId: iId , iEntityTypeId: iEntityTypeId  },  // Send the id as POST data
                    success: function (response) {
                        if (response.redirectUrl) {
                    window.location.href = response.redirectUrl;  // Redirect to the URL from the response
                }
                    },
                    error: function () {
                        alert("Error occurred");
                    }
                });
            } else {
                alert("ID not found");
            }
        });

         // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for editOtherBCMEntities form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('editOtherBCMEntities');
                if (!form) {
                    console.error("Form not found with ID: editOtherBCMEntities");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateEmail function similarly
                const originalValidateEmail = window.BCMValidation.validateEmail;
                window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateEmail(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validatePatternInput function similarly
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidatePatternInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    if (!isValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }
    });

</script>


<script>
    // Bind department data on unit change
    $(document).on('change', '#ddlUnit', function () {
        var iUnitID = $(this).val();
        if (iUnitID) {
            $.ajax({
                url: '@Url.Action("GetAllDepartments", "ManageOtherBCMEntities")',
                type: 'GET',
                data: { iUnitID: iUnitID },
                success: function (data) {
                    var ddldepartment = $('#ddlDepartment');
                    ddldepartment.empty();
                    ddldepartment.append('<option selected value="0">-- All Departments --</option>');
                    $.each(data, function (index, item) {
                        ddldepartment.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>');
                    });
                }
            });
        }
    });
    // Bind sub-department data on department change
    $(document).on('change', '#ddlDepartment', function () {
        var iDepartmentID = $(this).val();
        if (iDepartmentID) {
            $.ajax({
                url: '@Url.Action("GetAllSubDepartments", "ManageOtherBCMEntities")',
                type: 'GET',
                data: { iDepartmentID: iDepartmentID },
                success: function (data) {
                    var ddlSubDepartment = $('#ddlSubDepartment');
                    ddlSubDepartment.empty();
                    ddlSubDepartment.append('<option selected value="0">-- All Sub Departments --</option>');
                    $.each(data, function (index, item) {
                        ddlSubDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>');
                    });
                }
            });
        }
    });
</script>
