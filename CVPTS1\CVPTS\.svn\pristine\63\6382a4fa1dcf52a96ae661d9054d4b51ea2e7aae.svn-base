﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.JsonPatch.Internal;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using static System.Net.Mime.MediaTypeNames;

namespace BCM.UI.Areas.BCMRiskAssessment.Controllers;
[Area("BCMRiskAssessment")]
public class RiskTrendAnalysisController : BaseController
{
    private ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;

    public RiskTrendAnalysisController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult RiskTrendAnalysis()
    {
        List<RiskTrendInfo> lstRiskTrendInfo=new List<RiskTrendInfo>();
        try
        {
            PopulateRiskSeverity();
            lstRiskTrendInfo = GetAllRiskTrendAnalysisData();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstRiskTrendInfo);
    }

    public List<RiskTrendInfo> GetAllRiskTrendAnalysisData()
    {
        List<RiskTrendInfo> lstRiskTrendInfo = new List<RiskTrendInfo>();
        try
        {
            lstRiskTrendInfo = _ProcessSrv.GetRiskTrendAll_ByOrgID(_UserDetails.OrgID);
            if (lstRiskTrendInfo != null)
            {
                int i = 0;
                foreach (RiskTrendInfo objTrend in lstRiskTrendInfo)
                {
                    i++;
                    string strRatingText = string.Empty;
                    string strRatingValue = string.Empty;
                    int iImprStatus;

                    strRatingText = GetRiskRatingDescription(Convert.ToInt32(objTrend.ResidualRiskSeverity));
                    strRatingValue = GetRiskRatingValue(Convert.ToInt32(objTrend.ResidualRiskSeverity));
                    objTrend.ResidualRiskSeverityValue = strRatingValue;
                    objTrend.ResiRiskSeverity = Convert.ToInt32(objTrend.ResidualRiskSeverity);

                    int iImprReqd;
                    if (objTrend.RiskTrendStatus == 0)
                    {
                        iImprReqd = 0;
                        objTrend.ImprovementRequired = iImprReqd;
                    }
                    else if (objTrend.RiskTrendStatus == 1)
                    {
                        iImprReqd = 1;
                        objTrend.ImprovementRequired = iImprReqd;
                    }
                    else if (objTrend.RiskTrendStatus == 2)
                    {
                        iImprReqd = 2;
                        objTrend.ImprovementRequired = iImprReqd;
                    }


                    //string strImprStatusText = string.Empty;
                    //string strIMage_URL_1 = string.Empty;
                    //string strIMage_ToolTip_1 = string.Empty;

                    #region Code for Calculating Improvement Status for Trend

                    int iCount = 0;
                    int iTotal = 0;
                    int iInPrgCount = 0;
                    int iApprovedCount = 0;
                    int iCompletedCount = 0;

                    //string strTreatmentplan = "0";
                    //string strImprReqdText = string.Empty;

                    if (objTrend.RiskTrendStatus == Convert.ToInt32(BCPEnum.RiskTrendStatus.NoChange))
                    {
                        //strIMage_URL_1 = ""; strIMage_ToolTip_1 = "No Change"; 
                        objTrend.TreatmentPlan = "0"; objTrend.ImprovementRequiredText = "No";
                    }
                    else if (objTrend.RiskTrendStatus == Convert.ToInt32(BCPEnum.RiskTrendStatus.Upward))
                    {
                        //strIMage_URL_1 = ""; strIMage_ToolTip_1 = "Upward"; 
                        objTrend.TreatmentPlan = "1"; objTrend.ImprovementRequiredText = "Yes";
                    }
                    else if (objTrend.RiskTrendStatus == Convert.ToInt32(BCPEnum.RiskTrendStatus.Downward))
                    {
                        //strIMage_URL_1 = ""; strIMage_ToolTip_1 = "Downward"; 
                        objTrend.TreatmentPlan = "2"; objTrend.ImprovementRequiredText = "NA";
                    }

                    List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
                    lstRiskManagement = _ProcessSrv.GetRiskDetails_PACAStatus(objTrend.RiskID);

                    if (lstRiskManagement != null)
                    {
                        foreach (RiskManagement objRiskManagement in lstRiskManagement)
                        {
                            iTotal++;
                            if (objRiskManagement.PACAStatus.Equals(Convert.ToInt32(BCPEnum.PACAStatus.InProgress)))
                            {
                                iInPrgCount++;
                                iCount++;
                                objTrend.ImprovementStatusText = objRiskManagement.PACAStatus;
                            }
                            else if (objRiskManagement.PACAStatus.Equals(Convert.ToInt32(BCPEnum.PACAStatus.Completed)))
                            {
                                iCompletedCount++;
                                iCount++;
                                objTrend.ImprovementStatusText = objRiskManagement.PACAStatus;
                            }
                            else if (objRiskManagement.PACAStatus.Equals(Convert.ToInt32(BCPEnum.PACAStatus.Approved)))
                            {
                                iApprovedCount++;
                                iCount++;
                                objTrend.ImprovementStatusText = objRiskManagement.PACAStatus;
                            }
                        }
                    }

                    if (iInPrgCount > 0)
                    {
                        objTrend.ImprovementStatus = Convert.ToInt32(BCPEnum.PACAStatus.InProgress);
                        objTrend.ImprovementStatusText = BCPEnum.PACAStatus.InProgress.ToString();
                    }
                    else if (iTotal == iCompletedCount)
                    {
                        objTrend.ImprovementStatus = Convert.ToInt32(BCPEnum.PACAStatus.Completed);
                        objTrend.ImprovementStatusText = BCPEnum.PACAStatus.Completed.ToString();
                    }
                    else if (iTotal == iApprovedCount)
                    {
                        objTrend.ImprovementStatus = Convert.ToInt32(BCPEnum.PACAStatus.Approved);
                        objTrend.ImprovementStatusText = BCPEnum.PACAStatus.Approved.ToString();
                    }
                    else
                    {
                        objTrend.ImprovementStatus = Convert.ToInt32(BCPEnum.PACAStatus.Draft);
                        objTrend.ImprovementStatusText = BCPEnum.PACAStatus.Draft.ToString();
                    }
                    #endregion Code for Calculating Improvement Status for Trend  

                    objTrend.ResidualRiskSeverity = strRatingText;
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return lstRiskTrendInfo;
    }

    private string GetRiskRatingDescription(int iRatingVal)
    {
        string strText = string.Empty;
        try
        {
            List<RiskImpactMaster> lstRiskImpactMaster = new List<RiskImpactMaster>();
            int iDefaultProfileId = _Utilities.RiskProfileID;
            lstRiskImpactMaster = _ProcessSrv.GetAllRiskSeverityDetails(_UserDetails.OrgID, iDefaultProfileId);
            if (iRatingVal >= 0)
            {
                if (lstRiskImpactMaster != null)
                {
                    var objRating = (from RiskImpactMaster objImpact in lstRiskImpactMaster
                                     where iRatingVal >= objImpact.FromImpactRange && iRatingVal <= objImpact.ToImpactRange
                                     select objImpact).FirstOrDefault();
                    if (objRating != null)
                    {
                        strText=objRating.RiskSeverityName+"("+iRatingVal+")";
                    }
                }
            }
            else
            {
                strText = "NA";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return strText;
        }
        return strText;
    }

    private string GetRiskRatingValue(int iRatingVal)
    {
        string strText = string.Empty;
        try
        {
            List<RiskImpactMaster> lstRiskImpactMaster = new List<RiskImpactMaster>();
            int iDefaultProfileId = _Utilities.RiskProfileID;
            lstRiskImpactMaster = _ProcessSrv.GetAllRiskSeverityDetails(_UserDetails.OrgID, iDefaultProfileId);
            if (lstRiskImpactMaster != null)
                {
                    var objRating = (from RiskImpactMaster objImpact in lstRiskImpactMaster
                                     where iRatingVal >= objImpact.FromImpactRange && iRatingVal <= objImpact.ToImpactRange
                                     select objImpact).FirstOrDefault();
                    if (objRating != null)
                    {
                        strText = objRating.Id;
                    }
                }
            return strText;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return "";
        }
    }

    public void PopulateRiskSeverity()
    {
        try
        {
            ViewBag.RiskSeverity = new SelectList(_Utilities.PopulateRiskSeverity(_UserDetails.OrgID), "Id", "RiskSeverityName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public IActionResult FilterTableRecords(int iTrend,int iImpReq,int iResRiskSev, int iImpStatus)
    {
        List<RiskTrendInfo> lstRiskTrendInfo = new List<RiskTrendInfo>();
        try
        {            
            lstRiskTrendInfo = GetAllRiskTrendAnalysisData();
            if (iTrend >= 0)
            {
                lstRiskTrendInfo = lstRiskTrendInfo.Where(x => x.RiskTrendStatus == iTrend).ToList();
            }
            if (iImpReq >= 0)
            {
                lstRiskTrendInfo = lstRiskTrendInfo.Where(x => x.ImprovementRequired == iImpReq).ToList();
            }
            if (iResRiskSev > 0)
            {
                lstRiskTrendInfo = lstRiskTrendInfo.Where(x => x.ResidualRiskSeverityValue == iResRiskSev.ToString()).ToList();                
            }
            if (iImpStatus > 0) 
            {
                lstRiskTrendInfo = lstRiskTrendInfo.Where(x => x.ImprovementStatus == iImpStatus).ToList();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_FilterTableRecords", lstRiskTrendInfo);
    }    
}

