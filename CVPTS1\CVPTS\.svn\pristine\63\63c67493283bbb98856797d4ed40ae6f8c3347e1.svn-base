﻿@model BCM.BusinessClasses.BIAActivityInfo
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@{
    ViewBag.Title = "Prioritized Activities";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="Page-Condant card border-0">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        @* <p class="fw-semibold">Configure Prioritized Activities for </p> *@
        <p style="padding-left:1%" class="fw-bold mb-2">Configure Prioritized Activities for @ViewBag.ProcessName ( @ViewBag.ProcessCode )</p>
        <div class="align-items-right" style="padding-right:2%">
            <p class="fw-semibold">Version : @ViewBag.ProcessVersion</p>
        </div>
    </div>
    <div class="card-body">
        @* <div class="form-group">
            <div class="row">
                <div class="col-6">
                    <label class="form-label">Select Process</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-owner"></i></span>
                        <select class="form-select-sm" id="OwnerID" autocomplete="off" aria-label="Default select example">
                            <option selected value="0">-- Select Process --</option>
                            @foreach (var objResource in ViewBag.DepartmentList)
                            {
                                <option value="@objResource.DepartmentID">@objResource.DepartmentName</option>
                            }

                        </select>
                    </div>
                </div>
            </div>
        </div> *@
        <div class="row">
            <div class="col-12">               
                <div class="accordion accordion-flush" id="accordionFlushExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                Instructions and Guidelines
                            </button>
                        </h2>
                        <div id="flush-collapseOne" class="accordion-collapse collapse show"
                             data-bs-parent="#accordionFlushExample">
                            <div class="accordion-body">

                                @Html.Raw(ViewBag.Description)
                               @*  <p class="mb-2">
                                    HR Continuity Planning: The purpose of this section is to collect
                                    information to plan and respond to drop in staff levels and cases of manpower
                                    shortages (e.g. pandemic planning)
                                </p>
                                <ul class="ps-3">
                                    <li>
                                        Minimum possible number of personnel required to execute minimized work – starts
                                        with minimum acceptable level and ramps with time
                                    </li>
                                    <li>
                                        Critical Positions - For the earliest timeframe of HR requirements - indicate
                                        the positions required. Identify backups if currently available.
                                    </li>
                                    <li>
                                        Work Load Transfer - Are there cross trained personnel available in other teams
                                        who could be leveraged in case your process does not have minimum personnel? If
                                        yes, provide the name of the team.
                                    </li>
                                </ul> *@
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">
                <div>
                    <div class="">
                        <p class="text-primary d-flex align-items-center gap-1">
                            <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed"
                                  type="button" data-bs-toggle="collapse" data-bs-target="#collapsequestion1"
                                  aria-expanded="false" aria-controls="collapseExample">
                                <i class="cv-minus align-middle"></i>
                            </span>
                        </p>
                        <div class="ps-2 collapse show" id="collapsequestion1">

                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Prioritized Activity</th>
                                        <th style="text-align:right !important;">Action</th>
                                    </tr>
                                </thead>
                                <tbody id="tblBody">
                                    @{
                                        int iCount = 0;
                                        foreach (var que in ViewBag.Questions)
                                        {
                                            <tr data-bs-toggle="collapse" data-bs-target="#collapseQuestion"
                                                aria-expanded="false" aria-controls="collapseQuestion" role="button">
                                                <td class="bg-secondary-subtle"><i class="cv-down-arrow ms-2"></i></td>
                                                <td class="bg-secondary-subtle" colspan="10">Question: @que.QuestionDetails</td>
                                            </tr>
                                            foreach (var activity in ViewBag.Activities)
                                            {
                                                if (que.ID == activity.QuestionID)
                                                {
                                                    iCount++;
                                                    <tr>
                                                        <td>@iCount</td>
                                                        <td>@activity.ActivityDetails</td>
                                                        <td style="text-align:right !important;">
                                                            <span class="btn-action btnEdit" @ViewBag.ButtonAccess.btnUpdate
                                                                  data-activity-id="@activity.ActivityID"
                                                                  data-que-id="@activity.QuestionID"
                                                                  data-activity="@activity.ActivityDetails" type="button"
                                                                  data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                                            <span class="btn-action btnDelete" data-activity-id="@activity.ActivityID"
                                                                  data-que-id="@activity.QuestionID"
                                                                  data-activity="@activity.ActivityDetails" type="button"
                                                                  data-bs-toggle="#DeleteModal">
                                                                <i class="cv-delete text-danger" title="Delete"></i>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                }
                                            }
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <form action="AddUpdateActivity" method="post" id="addUpdateActivity" class="needs-validation progressive-validation" novalidate>
                <div class="col-12">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Questions</label>
                            <div class="radio-group" data-required="true">
                                @{
                                    foreach (var que in ViewBag.Questions)
                                    {
                                        <div class="form-check">
                                            <input class="form-check-input" checked type="radio" asp-for="QuestionID"
                                                   name="QuestionID" id="<EMAIL>" value="@que.ID">
                                            <label class="form-check-label" for="<EMAIL>">@que.QuestionDetails</label>
                                        </div>
                                    }
                                }
                            </div>
                            <div class="invalid-feedback">Please select a question</div>
                        </div>
                    </div>

                </div>
                <div class="col-12">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label"><i class="cv-description me-1"></i>Prioritized Activity</label>
                            <div class="accordion accordion-flush" id="accordionFlushExample">
                                <div class="accordion-item">
                                    <div>
                                        <div class="accordion-body">
                                            <input type="hidden" value="0" id="txtActivityID" asp-for="ActivityID">
                                            @* <textarea name="ActivityDetails" id="editor"
                                            asp-for="ActivityDetails"></textarea> *@
                                            <input type="text" class="form-control" id="editor1" name="ActivityDetails" asp-for="ActivityDetails" required placeholder="Enter Prioritized Activity" />
                                            <div class="invalid-feedback">Enter Prioritized Activity</div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 text-end">
                    <a class="btn btn-sm btn-outline-primary" role="button" asp-action="PerformProcessBIA"
                       asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA"
                       asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>
                    @* <a role="button" class="btn btn-sm btn-primary" asp-action="ManageBusinessProcess" 
                       asp-controller="ManageBusinessProcesses" asp-area="BCMProcessBIA">View All</a>*@
                    <button type="submit" @ViewBag.ButtonAccess.btnUpdate class="btn btn-sm btn-primary btnUpdate"
                            id="btnSubmit">
                        Save
                    </button>

                 

                    <button class="btn btn-sm btn-secondary btnCancel" id="btnCancel">Cancel</button>
                </div>
                @* <div class="col-12 text-end">
                    
                </div> *@
            </form>
           
        </div>
    </div>
</div>
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
     data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
                <form asp-action="DeleteActivity" method="post">
                    <div>
                        <input type="hidden" asp-for="ActivityID" value="0" id="txtActivity" />
                        <input type="hidden" asp-for="ActivityDetails" value="" id="activityDetailsTxt" />
                    </div>
                    <div class="modal-header p-0">
                        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
                    </div>
                    <div class="modal-body d-grid px">
                        <span class="fw-semibold">Do you really want to delete</span>
                        <span>"<span class="text-primary fw-semibold" id="txtActDetails"></span>" ?</span>
                    </div>                   
                    <div class="modal-footer justify-content-center p-0">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                            Cancel 
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Delete Modal -->
<script src="~/lib/ckeditor/js/ckeditor.js"></script>
<script>
    ClassicEditor.create(document.querySelector("#editor"));

    document.querySelector("form").addEventListener("submit", (e) => {
        // e.preventDefault();
        console.log(document.getElementById("editor").value);
    });

</script>

@section Scripts {
    <script>
        $(document).ready(function () {

            // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function() {
                        console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function() {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                console.log("Initializing validation for addUpdateActivity form");

                if (window.BCMValidation) {
                    console.log("BCMValidation found, initializing");

                    // Get the form element
                    const form = document.getElementById('addUpdateActivity');
                    if (!form) {
                        console.error("Form not found with ID: addUpdateActivity");
                        return;
                    }

                    // Store the original custom messages from invalid-feedback divs
                    const customMessages = {};
                    form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                        const originalMessage = element.textContent.trim();
                        console.log("Processing invalid-feedback element:", originalMessage);

                        // Find the associated input by looking in the form group
                        const formGroup = element.closest('.form-group');
                        let input = formGroup?.querySelector('input[required], select[required], textarea[required]');

                        // If not found, look for any input in the form group
                        if (!input) {
                            input = formGroup?.querySelector('input, select, textarea');
                        }

                        if (input) {
                            // Store the custom message using multiple keys for reliability
                            const keys = [
                                input.id,
                                input.name,
                                input.getAttribute('asp-for')
                            ].filter(key => key); // Remove null/undefined values

                            keys.forEach(key => {
                                customMessages[key] = originalMessage;
                                console.log("Stored custom message for key", key, ":", originalMessage);
                            });
                        } else {
                            console.log("No input found for invalid-feedback:", originalMessage);
                        }
                    });

                    // Custom validation function for radio button groups
                    function validateRadioGroup(radioGroup) {
                        const radios = radioGroup.querySelectorAll('input[type="radio"]');
                        const isChecked = Array.from(radios).some(radio => radio.checked);

                        if (!isChecked) {
                            radioGroup.classList.add(window.BCMValidation.classes.invalidClass);
                            const feedbackElement = radioGroup.parentElement.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                feedbackElement.style.display = 'block';
                            }
                            return false;
                        } else {
                            radioGroup.classList.remove(window.BCMValidation.classes.invalidClass);
                            const feedbackElement = radioGroup.parentElement.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                feedbackElement.style.display = 'none';
                            }
                            return true;
                        }
                    }

                    // Initialize the validation framework
                    window.BCMValidation.init();

                    // Add required field indicators (asterisks)
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for pattern-based inputs
                    window.BCMValidation.addFormatIndicators(form);

                    // Add user interaction validation for radio buttons
                    const radioGroups = form.querySelectorAll('.radio-group[data-required="true"]');
                    radioGroups.forEach(function(radioGroup) {
                        const radios = radioGroup.querySelectorAll('input[type="radio"]');
                        radios.forEach(function(radio) {
                            radio.addEventListener('change', function() {
                                // Remove validation-pending to show validation messages after interaction
                                const formGroup = radioGroup.closest('.form-group');
                                if (formGroup) {
                                    formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                                }
                                // Validate the radio group
                                validateRadioGroup(radioGroup);
                            });
                        });
                    });

                    // Add user interaction validation for other inputs
                    const allInputs = form.querySelectorAll('input:not([type="radio"]):not([type="hidden"]), select, textarea');
                    allInputs.forEach(function(input) {
                        // Add blur event listener to mark field as touched and validate
                        input.addEventListener('blur', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                // Mark field as touched and remove validation-pending
                                formGroup.classList.add(window.BCMValidation.classes.fieldTouchedClass);
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            // Validate the input using global validation
                            if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }
                        });

                        // Add input event listener for real-time validation (only after field is touched)
                        input.addEventListener('input', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup && formGroup.classList.contains(window.BCMValidation.classes.fieldTouchedClass)) {
                                // Validate the input
                                if (this.hasAttribute('pattern')) {
                                    window.BCMValidation.validatePatternInput(this);
                                } else {
                                    window.BCMValidation.validateInput(this);
                                }
                            }
                        });
                    });

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");

                        // Show all validation messages
                        window.BCMValidation.showAllValidationMessages(form);

                        // Validate radio button groups first
                        let isValid = true;
                        const radioGroups = form.querySelectorAll('.radio-group[data-required="true"]');
                        radioGroups.forEach(function(radioGroup) {
                            const radioResult = validateRadioGroup(radioGroup);
                            if (!radioResult) {
                                isValid = false;
                            }
                        });

                        // Validate the form using global validation
                        const globalValidationResult = window.BCMValidation.validateForm(form);
                        if (!globalValidationResult) {
                            isValid = false;
                        }

                        console.log("Form validation result:", isValid);

                        if (!isValid) {
                            console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                        }
                    });
                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }

            $('.btnEdit').click(function (e) {
                e.preventDefault();

                // Clear validation errors before populating new values
                const form = document.getElementById('addUpdateActivity');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Add validation-pending class to hide messages until user interaction
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }

                $('#editor1').val($(this).data('activity'));
                $('#que_' + $(this).data('que-id')).prop('checked', true);
                var txtActivityID = $(this).data('activity-id');
                $('#txtActivityID').val(txtActivityID);
                $('#btnSubmit').text('Update');
            })

            $('.btnDelete').click(function () {
                $('#txtActivity').val($(this).data('activity-id'));
                var txtActivityDetails = $(this).data('activity')
                $('#txtActDetails').text(txtActivityDetails);
                $('#activityDetailsTxt').val(txtActivityDetails);
                $('#DeleteModal').modal('show');
            })

            $('.btnCancel').click(function () {
                $('#txtActivity').val('');
                $('.btnUpdate').text('Save');
                $('#txtActivityID').val('0');
                $('#editor1').val('');

                // Clear validation errors when canceling
                const form = document.getElementById('addUpdateActivity');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Reset validation state
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }
                location.reload();
            })

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        })
    </script>
}