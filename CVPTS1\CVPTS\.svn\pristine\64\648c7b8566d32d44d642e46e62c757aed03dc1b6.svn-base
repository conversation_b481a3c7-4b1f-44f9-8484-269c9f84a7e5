﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;

namespace BCM.UI.Areas.BCMAdministration.Controllers
{
    [Area("BCMAdministration")]
    public class ReportListController : BaseController
    {
        private ProcessSrv _ProcessSrv;
        readonly Utilities _Utilities;
        readonly CVLogger _CVLogger;
        public ReportListController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
        {
            _ProcessSrv = ProcessSrv;
            _Utilities = Utilities;
            _CVLogger = CVLogger;
        }
        public IActionResult ReportList()
        {
            return View();
        }
    }
}
