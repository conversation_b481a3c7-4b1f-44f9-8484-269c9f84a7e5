﻿@model IEnumerable<BCM.BusinessClasses.ManageBIAProfileSectionMain>
@using BCM.Shared
@{
    ViewData["Title"] = "Manage BIA Profile";
    Layout = "~/Views/Shared/_Layout.cshtml";

}

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Manage BIA Profile</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm btnCreate" data-bs-toggle="modal" data-bs-target="#NormalModal"> <i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th>#</th>
                <th>Profile Name</th>
                <th>Org Name</th>
                <th>Approver Name</th>
                <th>Owner Name</th>
                <th>Create Date</th>
                <th>Status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            @if (Model != null)
            {
                int Index = 1;
                @foreach (var Profile in Model)
                {
                    <tr>
                        <td>@Index</td>
                        <td>
                            <div class="d-grid">
                                <span class="fw-semibold text-warning" style="display:none">@Profile.ProfileCode</span>
                                <span>@Profile.ProfileName</span>
                            </div>
                        </td>
                        <td>@Profile.OrgName</td>
                        <td>@Profile.ApproverName</td>
                        <td>@Profile.OwnerName</td>
                        <td>@Profile.CreateDate</td>

                        @if (Convert.ToString(@Profile.Status) == "0")
                        {
                            <td>
                                <span class="text-info fs-14 fw-normal">
                                    <i class="cv-initiated me-1"></i>
                                    Initiated
                                </span>
                            </td>
                        }
                        @if (Convert.ToString(@Profile.Status) == "1")
                        {
                            <td>
                                <span class="text-warning fs-14 fw-normal">
                                    <i class="cv-waiting me-1"></i>
                                    WaitingForApprove
                                </span>
                            </td>
                        }
                        @if (Convert.ToString(@Profile.Status) == "2")
                        {
                            <td>
                                <span class="text-success fs-14 fw-normal">
                                    <i class="cv-success me-1"></i>
                                    Approved
                                </span>
                            </td>

                        }
                        @if (Convert.ToString(@Profile.Status) == "3")
                        {
                            <td>
                                <span class="text-danger fs-14 fw-normal">
                                    <i class="cv-error me-1"></i>
                                    DisApproved
                                </span>
                            </td>
                        }

                        <td>
                            <div class="d-flex align-items-center">
                                <span style="display:none" class="btn-action btnActive btn_sm me-2" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@Profile.ID"><i class="cv-test-recovery-plan"></i></span>
                                <span class="btn-action btnEdit" type="button" data-id="@Profile.ID"><i class="cv-edit" title="Edit"></i></span>
                                <span class="btn-action btnDelete" type="button" data-id="@Profile.ID" data-bs-toggle="modal"><i class="cv-delete text-danger mx-2" title="Delete"></i></span>
                            </div>
                        </td>
                    </tr>
                    Index++;
                }
            }       
        </tbody>

    </table>
</div>



<!-- Configuration Modal -->
<div class="modal fade" id="NormalModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle"></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="configurationBody">
                <div class="wizard-content">
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!-- Delete Modal -->
@* <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Delete BIA Profile</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div> *@
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="Modal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle"></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>



@section Scripts {
    <script>
        $(document).ready(function () {
           
            $(document).on('click', '.btnCreate', function () {
                //$.get('/BCMBIAProfile/ManageBIAProfile/SaveBIAProfile/', function (data) {
                $.get('@Url.Action("SaveBIAProfile", "ManageBIAProfile")', function (data) {
                    console.log(data)
                    $('#configurationBody').html(data);

                    var form = $(".example-form");
                    form.steps({
                        headerTag: "h6",
                        bodyTag: "section",
                        transitionEffect: "fade",
                        titleTemplate: '<span class="step">#index#</span> #title#'
                    });
                    $('#NormalModal').modal('show');
                    $('#modaltitle').text('BIA Profile Configuration');
                });
            });

            $(document).on('click', '.btnEdit', function () {               
                var id = $(this).data('id');
                debugger;
                //$.get('/BCMBIAProfile/ManageBIAProfile/EditBIAProfile/', { id: id }, function (data) {
                $.get('@Url.Action("EditBIAProfile", "ManageBIAProfile")', { id: id }, function (data) {
                    $('.modal-body').html(data);
                    var form = $(".example-form");
                    form.steps({
                        headerTag: "h6",
                        bodyTag: "section",
                        transitionEffect: "fade",
                        titleTemplate: '<span class="step">#index#</span> #title#'
                    });
                    $('#NormalModal').modal('show');
                    $('#modaltitle').text('Update BIA Profile');
                });
            });

            $(document).on('click', '.btnDelete', function () {               
                var id = $(this).data('id');
                //$.get('/BCMBIAProfile/ManageBIAProfile/DeleteBIAProfile/', { id: id }, function (data) {
                $.get('@Url.Action("DeleteBIAProfile", "ManageBIAProfile")', { id: id }, function (data) {
                    $('.modal-body').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

        });

    </script>
}
