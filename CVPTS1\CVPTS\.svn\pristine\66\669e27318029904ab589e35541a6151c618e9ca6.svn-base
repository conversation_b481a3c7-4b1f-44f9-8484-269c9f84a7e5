﻿$(document).ready(function () {

    let loadingIcon = document.getElementById("loading");
    let noDataFound = document.getElementById("noDataFound");
    showLoadingIcon();

    $.ajax({
        url: "/BCMReports/IncidentSummaryReport/GetIncidentSummaryReportData",
        dataType: "json",
        type: 'GET',
        traditional: true,
        success: function (response) {
            if (response?.data && response.success !== false) {
                var reportName = "IncidentSummaryReport";
                var loadReportUrl = '/BCMReports/IncidentSummaryReport/LoadReport';
                loadAjax(loadReportUrl, reportName, response.data);
            }
            else {
                showNodataFoundIcon()
            }
        },
        error: function (xhr, status, error) {
            showNodataFoundIcon();
            console.error("Error in GetIncidentSummaryReportData:", error);
        }
    });

    function loadAjax(loadReportUrl, reportName, response) {
        $.ajax({
            url: loadReportUrl,
            type: 'POST',
            contentType: "application/json",
            data: JSON.stringify({
                reportName: reportName,
                responseData: response
            }),
            success: function (data) {
                if (data) {
                    hideIcons();
                    click1 = 0;
                    $('#incidentsummaryreportthtml').html(data).show();
                } else {
                    showNodataFoundIcon();
                    $('#incidentsummaryreportthtml').hide();
                    console.warn("No content returned from LoadReport.");
                }
            },
            error: function (error) {
                showNodataFoundIcon();
                console.error("Error during LoadReport:", error);
                $('#incidentsummaryreportthtml').hide();
            }
        });
    }

    function showNodataFoundIcon() {
        noDataFound.style.display = "block";
        loadingIcon.style.display = "none";
    }
    function showLoadingIcon() {
        noDataFound.style.display = "none";
        loadingIcon.style.display = "block";
    }
    function hideIcons() {
        noDataFound.style.display = "none";
        loadingIcon.style.display = "none";
    }

    $(document).on('click', '.dx-menu-item-wrapper', function () {
        var exportOptions = document.querySelectorAll('.dxrd-preview-export-item-text');
        exportOptions.forEach(function (option) {
            var text = option.textContent.trim().toLowerCase();
            if (text == 'pdf') {
                option.offsetParent.click();
            }
        });
        $(".dx-submenu").hide();
    });
});

