﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.BusinessProcessComponents;
using BCM.Security.Helper;
using BCM.Shared;
using BCM.UI.Controllers;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.CodeAnalysis.Options;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;
using MySqlX.XDevAPI.Relational;
using Org.BouncyCastle.Asn1.Mozilla;
using Org.BouncyCastle.Ocsp;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Xml.Linq;
using static BCM.Shared.BCPEnum;

namespace BCM.UI.Areas.BCMRiskAssessment.Controllers;
[Area("BCMRiskAssessment")]
public class ManageRiskController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    private readonly BCMMail _BCMMail;

    int iUnitID = 0;
    int iDepartmentID = 0;
    int iETime, iEUnit;
    int icount = 0;
    DateTime dvalue;
    public ManageRiskController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, BCMMail bCMMail) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _BCMMail = bCMMail;
    }

    #region Manage Risk
    [HttpGet]
    public IActionResult ManageRisk(int? upcoming, int? past)
    {
        List<RiskItem> lstriskItem = new List<RiskItem>();
        DataTable dtRiskData = new DataTable();

        try
        {
            PopulateDropdown();
            DataTable dtBuildTableStructure = BuildTableStructure();
            DataTable dtTableClone;
            int RiskByOpn = 0;
            object[] objLicensedEntities = GetLicensedEntityDataTable();

            if (RiskByOpn == 0)
            {
                string strFilter = " IsEffective = '1'";

                DataTable dtGetFilteredBIASurveyDataTable = _Utilities.GetFilteredBIASurveyDataTable(strFilter, Convert.ToInt32(_UserDetails.OrgID));
                int iRiskProfileID = RiskProfileID();
                DataTable dtGetBCMRiskManageList = _ProcessSrv.getBCMRiskManageList("0", "0", "0", "0", "0", "1", "0", "0", "0", "0", "", iRiskProfileID);
                if (dtGetBCMRiskManageList != null)
                {
                    ViewData["RiskColl"] = dtGetBCMRiskManageList;

                   
                        foreach (DataRow row in dtGetBCMRiskManageList.Rows)
                        {
                            var riskCode = row["id"].ToString();
                            var SubriskCode = row["SubRiskRegID"].ToString();

                        HttpContext.Session.SetString("riskCode", riskCode);
                        HttpContext.Session.SetString("SubriskCode", SubriskCode);

                        // Use riskCode and riskName as needed
                        }
                   

                }

                dtBuildTableStructure.Rows.Add("Process(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.BusinessProcess), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/processlist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "1", "0", "");
                dtBuildTableStructure.Rows.Add("People", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.People), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/usergroup.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "4", "0", "");
                dtBuildTableStructure.Rows.Add("Site(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.Facilities), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "2", "0", "");
                dtBuildTableStructure.Rows.Add("Location(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.Location), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/processlist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "3", "0", "");
                dtBuildTableStructure.Rows.Add("Other BCM Entity(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.BCMEntity), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "7", "0", "");
                dtBuildTableStructure.Rows.Add("Third Party(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.ThirdParty), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, BCPEnum.EntityType.ThirdParty, "0", "");
                dtBuildTableStructure.Rows.Add("Application(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.Application), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, BCPEnum.EntityType.Application, "0", "");
                dtBuildTableStructure.Rows.Add("IT Services(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.ITService), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, BCPEnum.EntityType.ITService, "0", "");

                foreach (DataRow dr in dtGetFilteredBIASurveyDataTable.Rows)
                {

                    if (dr["EntityTypeID"].ToString() == "77")
                    {
                        dtBuildTableStructure.Rows.Add(dr["ProcessName"].ToString(), dr["ProcessCode"], "ENT-" + dr["EntityTypeID"].ToString(), null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/curprocess.png", dr["OrgID"].ToString(), dr["UnitID"].ToString(), dr["DepartmentID"].ToString(), dr["SubfunctionID"].ToString(), _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "1", dr["ProcessID"].ToString(), "");
                        collectRiskAsperView(ref dtBuildTableStructure, "0", "0", "0", "0", "0", dr["EntityTypeId"].ToString(), "0", dr["ProcessID"].ToString(), dr["ProcessCode"].ToString(), dr["ProcessCode"].ToString());
                    }
                    else
                    {
                        string? a = dr["ProcessCode"].ToString();
                        dtBuildTableStructure.Rows.Add(dr["ProcessName"].ToString(), dr["ProcessCode"], "ENT-" + dr["EntityTypeID"].ToString(), null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/curprocess.png", dr["OrgID"].ToString(), dr["UnitID"].ToString(), dr["DepartmentID"].ToString(), dr["SubfunctionID"].ToString(), _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "1", dr["ProcessID"].ToString(), "");
                        collectRiskAsperView_New(ref dtBuildTableStructure, dr["EntityTypeId"].ToString(), dr["ProcessID"].ToString(), dr["ProcessCode"].ToString());
                    }
                }

                dtTableClone = dtBuildTableStructure;
            }
            else
            {
                string Filter = "EntityTypeId = '" + 9 + "' AND IsEffective = '1'";
                DataTable dt = _Utilities.GetFilteredBIASurveyDataTable(Filter, Convert.ToInt32(_UserDetails.OrgID));

                dtBuildTableStructure.Rows.Add("ddlBCMEntity.SelectedItem.Text" + "(s)", "ENT-" + 9, null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/processlist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, 9, "1", "0", "");
                foreach (DataRow dr in dt.Rows)
                {
                    if (objLicensedEntities.Contains(dr["ProcessCode"].ToString()))
                    {

                        dtBuildTableStructure.Rows.Add(dr["ProcessName"].ToString(), dr["ProcessCode"].ToString(), "ENT-" + 9, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/curprocess.png", dr["OrgID"].ToString(), dr["UnitID"].ToString(), dr["DepartmentID"].ToString(), dr["SubfunctionID"].ToString(), _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "1", dr["ProcessID"].ToString(), "");
                        if (!string.IsNullOrEmpty(dr["ProcessCode"].ToString()))
                            collectRiskAsperView(ref dtBuildTableStructure, "0", "0", "0", "0", "0", dr["EntityTypeId"].ToString(), "0", dr["ProcessID"].ToString(), dr["ProcessCode"].ToString(), dr["ProcessCode"].ToString());
                    }
                }
                dtTableClone = dtBuildTableStructure;
            }

            DataRow[] drFilterData;

            if (_UserDetails.UserRole.Equals("BCMS Manager"))
            {
                DataView dv = dtBuildTableStructure.DefaultView;
                dv.Sort = "Item asc";
                dtBuildTableStructure = dv.ToTable();
            }
            else
            {
                dtRiskData = dtTableClone.Clone();

                string strGetFilterString = GetFilterString();
                if (strGetFilterString == "")
                {
                    strGetFilterString = "  ID LIKE '%ENT%'";
                }
                else
                {
                    strGetFilterString = GetFilterString() + " OR ID LIKE '%ENT%' ";
                }
                drFilterData = dtTableClone.Select(strGetFilterString);

                if (drFilterData != null)
                {
                    if (drFilterData.Count() > 0)
                    {
                        foreach (DataRow dr in drFilterData)
                        {
                            dtRiskData.ImportRow(dr);
                        }
                    }
                }

                DataView dv = dtRiskData.DefaultView;
                dv.Sort = "Item asc";
                dtRiskData = dv.ToTable();
            }

            foreach (DataRow dr in dtBuildTableStructure.Rows)
            {
                if (dr["iRiskID"].ToString() != string.Empty)
                {
                    int iRiskId = Convert.ToString(dr["IRiskID"]) == "" ? 0 : Convert.ToInt32(dr["IRiskID"]);

                    RiskItem objRiskItem = new RiskItem();
                    objRiskItem.Item = dr["Item"].ToString();
                    objRiskItem.ParentID = dr["ParentID"].ToString();
                    objRiskItem.ID = dr["ID"].ToString();
                    objRiskItem.Responsibility = dr["Responsibility"].ToString();
                    
                    objRiskItem.NxtRevDate = dr["NxtRevDate"].ToString();
                    objRiskItem.Unit = dr["Unit"].ToString();
                    objRiskItem.Depart = dr["Depart"].ToString();
                    objRiskItem.SubDept = dr["SubDept"].ToString();
                    objRiskItem.Org = dr["Org"].ToString();
                    objRiskItem.RiskRating = dr["RiskRating"].ToString();
                    objRiskItem.RiskDescription = dr["RiskDescription"].ToString();
                    objRiskItem.RiskCategory = dr["RiskCategory"].ToString();
                    objRiskItem.IsEffective = dr["IsEffective"].ToString();
                    objRiskItem.Impact = dr["Impact"].ToString();
                    objRiskItem.Likelihood = dr["Likelihood"].ToString();
                    objRiskItem.IRiskID = dr["IRiskID"].ToString();
                    objRiskItem.ImageURL = dr["ImageURL"].ToString();
                    objRiskItem.OrgId = dr["OrgId"].ToString();
                    objRiskItem.UnitID = dr["UnitID"].ToString();
                    objRiskItem.DepartmentId = dr["DepartmentId"].ToString();
                    objRiskItem.SubFunctionID = dr["SubFunctionID"].ToString();
                    objRiskItem.AltUnitHeadId = dr["AltUnitHeadId"].ToString();
                    objRiskItem.UnitBCPCorId = dr["UnitBCPCorId"].ToString();
                    objRiskItem.AltBCPCorId = dr["AltBCPCorId"].ToString();
                    objRiskItem.DeptHeadId = dr["DeptHeadId"].ToString();
                    objRiskItem.AltDeptHeadId = dr["AltDeptHeadId"].ToString();
                    objRiskItem.SubFunOwnerId = dr["SubFunOwnerId"].ToString();
                    objRiskItem.AltSubFunOwnerId = dr["AltSubFunOwnerId"].ToString();
                    objRiskItem.RiskOwner = dr["RiskOwner"].ToString();
                    objRiskItem.RiskItemCategory = dr["RiskItemCategory"].ToString();
                    objRiskItem.ProcessID = dr["ProcessID"].ToString();
                    objRiskItem.RiskName = dr["RiskName"].ToString();
                    objRiskItem.ResidualImpact = dr["ResidualImpact"].ToString();
                    objRiskItem.ResidualLikeliHood = dr["ResidualLikeliHood"].ToString();
                    objRiskItem.ResidualRiskRating = dr["ResidualRiskRating"].ToString();
                    objRiskItem.ResidualRiskRatingName = dr["ResidualRiskRatingName"].ToString();
                    objRiskItem.RiskTreatmentStrategyAction = dr["RiskTreatmentStrategyAction"].ToString();
                    objRiskItem.Version = dr["Version"].ToString();
                    objRiskItem.RiskEntryDate = dr["RiskEntryDate"].ToString();
                    objRiskItem.IncidentTypeName = GetIncidentTypeName(iRiskId);  //dr["IncidentTypeName"].ToString();
                    objRiskItem.LastRevDate = dr["LastRevDate"].ToString() == "01-01-0001 00:00:00" ? "NA" : dr["LastRevDate"].ToString();
                    objRiskItem.RiskCloseStatus = GetStatus(dr["RiskCloseStatus"].ToString());
                    objRiskItem.Status = GetApprovalStatus(Convert.ToString(dr["Status"]));
                    objRiskItem.StatusID = Convert.ToString(dr["Status"]);
                    objRiskItem.SubRiskRegID = dr["SubRiskRegID"].ToString();
                    objRiskItem.RiskOwnerEmail = dr["companyemail"].ToString();
                    objRiskItem.RiskOwnerMobile = dr["mobilephone"].ToString();
                    objRiskItem.ApproverName = dr["ApproverName"].ToString();
                    objRiskItem.ApproverEmail = dr["ApproverEmail"].ToString();
                    objRiskItem.ApproverMobile = dr["ApproverMobile"].ToString();


                    objRiskItem.DisasterName = GetDisasterDetailsByRiskId(iRiskId);  //dr["DisasterName"].ToString();
                    lstriskItem.Add(objRiskItem);
                }

            }

            ViewBag.TotalCount = lstriskItem.Count;
            ViewBag.WaitingForApproval = lstriskItem.Where(x => Convert.ToInt32(x.StatusID) == 1 && !string.IsNullOrEmpty(x.IRiskID)).ToList().Count;
            ViewBag.ApprovedRisks = lstriskItem.Where(x => Convert.ToInt32(x.StatusID) == 2 && !string.IsNullOrEmpty(x.IRiskID)).ToList().Count;
            ViewBag.DisapprovedRisks = lstriskItem.Where(x => Convert.ToInt32(x.StatusID) == 3  && !string.IsNullOrEmpty(x.IRiskID)).ToList().Count;

            BusinessProcessInfo objBusinessProcessInfo = new BusinessProcessInfo();
            //if (IRiskID > 0)
            //{
            //    objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(IRiskID), 9);

            //    List<BCMEntitiesTypeMaster> lstBCMEntitiesTypeMaster = _Utilities.PopulateBCMEntitiesTypeMaster_ByEntityTypeID(objBusinessProcessInfo.EntityTypeID);
            //    ViewBag.BCMEntitiesTypeMaster = lstBCMEntitiesTypeMaster;
            //}
            //else
            //{
            List<BCMEntitiesTypeMaster> lstBCMEntitiesTypeMaster = _Utilities.PopulateBCMEntities();
            ViewBag.BCMEntitiesTypeMaster = lstBCMEntitiesTypeMaster;

            // Replace with your actual data retrieval

            if (upcoming == 1)
            {
                var today = DateTime.Today;
                var next7 = today.AddDays(7);

                lstriskItem = lstriskItem
                .Where(r =>
             
                    DateTime.TryParse(r.NxtRevDate, out var nxtRevDate) &&
                    nxtRevDate >= today && nxtRevDate <= next7
                )
                .ToList();
            }

            if (past == 1)
            {
                var today = DateTime.Today;

                lstriskItem = lstriskItem
                .Where(r =>

                    DateTime.TryParse(r.NxtRevDate, out var nxtRevDate) &&
                    nxtRevDate < today
                )
                .ToList();
            }

            //}
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstriskItem);
    }

    public void PopulateDropdown()
    {
        try
        {
            List<Disaster> objDisasterColl = new List<Disaster>();

            objDisasterColl = _ProcessSrv.GetIncidentTypesByDisasterID(Convert.ToInt32(0), 0);

            ViewBag.Threats = objDisasterColl;

            ViewBag.lstResource = new SelectList(_Utilities.GetResources(_UserDetails.OrgID), "ResourceId", "ResourceName");

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

    }
    [HttpGet]
    public ActionResult GetFileredRisk(string strRiskStatus = "0", string strTreatOption = "0", string iRiskOwner = "0", string DisasterTypeID = "0", string txtSearch = "",string iRiskItemCategory = "0")
    {
        List<RiskItem> lstriskItem = new List<RiskItem>();
        try
        {
            lstriskItem = GetAllRisk();
            if (txtSearch == string.Empty)
            {
                if (strRiskStatus == "1")
                {
                    strRiskStatus = "Open";
                }
                if (strRiskStatus == "3")
                {
                    strRiskStatus = "Close";
                }
                if (strRiskStatus == "2")
                {
                    strRiskStatus = "Monitor";
                }


                if (strTreatOption == "0")
                {
                    strTreatOption = "No Action";
                }
                if (strTreatOption == "3")
                {
                    strTreatOption = "Treat";
                }
                if (strTreatOption == "4")
                {
                    strTreatOption = "Transfer";
                }
                if (strTreatOption == "2")
                {
                    strTreatOption = "Terminate";
                }
                if (strTreatOption == "1")
                {
                    strTreatOption = "Tolerate";
                }

                if (strRiskStatus != "0")
                {
                    lstriskItem = lstriskItem.Where(x => x.RiskCloseStatus == strRiskStatus.ToString()).ToList();
                }
                if (strTreatOption != "-1")
                {
                    lstriskItem = lstriskItem.Where(x => x.RiskTreatmentStrategyAction == strTreatOption.ToString()).ToList();
                }
                if (iRiskOwner != "0")
                {
                    //ResourcesInfo resourcesInfo = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(iRiskOwner));
                    lstriskItem = lstriskItem.Where(x => x.RiskOwner == iRiskOwner.ToString()).ToList();
                }
                if (DisasterTypeID != "0")
                {
                    lstriskItem = lstriskItem.Where(x => x.RiskCategory == DisasterTypeID.ToString()).ToList();
                }
                //if (iWaitingforApproval != -1)
                //{
                //    lstriskItem = lstriskItem.Where(x => Convert.ToInt32(x.StatusID) == 1 && !string.IsNullOrEmpty(x.IRiskID)).ToList();
                //}
                //if (iApproved != -1)
                //{
                //    lstriskItem = lstriskItem.Where(x => Convert.ToInt32(x.StatusID) == 2 && !string.IsNullOrEmpty(x.IRiskID)).ToList();
                //}
                //if (iDisapproved != -1)
                //{
                //    lstriskItem = lstriskItem.Where(x => Convert.ToInt32(x.StatusID) == 3 && !string.IsNullOrEmpty(x.IRiskID)).ToList();
                //}
                if(iRiskItemCategory !="0")
                {
                    lstriskItem = lstriskItem.Where(x => x.RiskItemCategory == iRiskItemCategory.ToString()).ToList();
                }

            }
            else
            {
                lstriskItem = lstriskItem.Where(x => x.RiskName.Contains(txtSearch) || x.Item.Contains(txtSearch) || x.RiskCloseStatus.Contains(txtSearch)
                || x.RiskCloseStatus.Contains(txtSearch) || x.RiskDescription.Contains(txtSearch) || x.Responsibility.Contains(txtSearch) || x.Likelihood.Contains(txtSearch)
                || x.Unit.Contains(txtSearch) || x.Depart.Contains(txtSearch)).ToList();
            }



        }
        catch (Exception ex)
        {
                
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_FilterRisk", lstriskItem);
    }

    public List<RiskItem> GetAllRisk()
    {
        List<RiskItem> lstriskItem = new List<RiskItem>();
        DataTable dtRiskData = new DataTable();

        try
        {
            PopulateDropdown();
            DataTable dtBuildTableStructure = BuildTableStructure();
            DataTable dtTableClone;
            int RiskByOpn = 0;
            object[] objLicensedEntities = GetLicensedEntityDataTable();

            if (RiskByOpn == 0)
            {
                string strFilter = " IsEffective = '1'";

                DataTable dtGetFilteredBIASurveyDataTable = _Utilities.GetFilteredBIASurveyDataTable(strFilter, Convert.ToInt32(_UserDetails.OrgID));
                int iRiskProfileID = RiskProfileID();
                DataTable dtGetBCMRiskManageList = _ProcessSrv.getBCMRiskManageList("0", "0", "0", "0", "0", "1", "0", "0", "0", "0", "", iRiskProfileID);
                if (dtGetBCMRiskManageList != null)
                {
                    ViewData["RiskColl"] = dtGetBCMRiskManageList;
                }

                dtBuildTableStructure.Rows.Add("Process(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.BusinessProcess), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/processlist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "1", "0", "");
                dtBuildTableStructure.Rows.Add("People", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.People), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/usergroup.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "4", "0", "");
                dtBuildTableStructure.Rows.Add("Site(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.Facilities), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "2", "0", "");
                dtBuildTableStructure.Rows.Add("Location(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.Location), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/processlist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "3", "0", "");
                dtBuildTableStructure.Rows.Add("Other BCM Entity(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.BCMEntity), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "7", "0", "");
                dtBuildTableStructure.Rows.Add("Third Party(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.ThirdParty), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, BCPEnum.EntityType.ThirdParty, "0", "");
                dtBuildTableStructure.Rows.Add("Application(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.Application), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, BCPEnum.EntityType.Application, "0", "");
                dtBuildTableStructure.Rows.Add("IT Services(s)", "ENT-" + Convert.ToInt32(BCPEnum.EntityType.ITService), null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/sitelist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, BCPEnum.EntityType.ITService, "0", "");

                foreach (DataRow dr in dtGetFilteredBIASurveyDataTable.Rows)
                {

                    if (dr["EntityTypeID"].ToString() == "77")
                    {
                        dtBuildTableStructure.Rows.Add(dr["ProcessName"].ToString(), dr["ProcessCode"], "ENT-" + dr["EntityTypeID"].ToString(), null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/curprocess.png", dr["OrgID"].ToString(), dr["UnitID"].ToString(), dr["DepartmentID"].ToString(), dr["SubfunctionID"].ToString(), _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "1", dr["ProcessID"].ToString(), "");
                        collectRiskAsperView(ref dtBuildTableStructure, "0", "0", "0", "0", "0", dr["EntityTypeId"].ToString(), "0", dr["ProcessID"].ToString(), dr["ProcessCode"].ToString(), dr["ProcessCode"].ToString());
                    }
                    else
                    {
                        string? a = dr["ProcessCode"].ToString();
                        dtBuildTableStructure.Rows.Add(dr["ProcessName"].ToString(), dr["ProcessCode"], "ENT-" + dr["EntityTypeID"].ToString(), null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/curprocess.png", dr["OrgID"].ToString(), dr["UnitID"].ToString(), dr["DepartmentID"].ToString(), dr["SubfunctionID"].ToString(), _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "1", dr["ProcessID"].ToString(), "");
                        collectRiskAsperView_New(ref dtBuildTableStructure, dr["EntityTypeId"].ToString(), dr["ProcessID"].ToString(), dr["ProcessCode"].ToString());
                    }
                }

                dtTableClone = dtBuildTableStructure;
            }
            else
            {
                string Filter = "EntityTypeId = '" + 9 + "' AND IsEffective = '1'";
                DataTable dt = _Utilities.GetFilteredBIASurveyDataTable(Filter, Convert.ToInt32(_UserDetails.OrgID));

                dtBuildTableStructure.Rows.Add("ddlBCMEntity.SelectedItem.Text" + "(s)", "ENT-" + 9, null, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/processlist.png", string.Empty, string.Empty, string.Empty, string.Empty, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, 9, "1", "0", "");
                foreach (DataRow dr in dt.Rows)
                {
                    if (objLicensedEntities.Contains(dr["ProcessCode"].ToString()))
                    {

                        dtBuildTableStructure.Rows.Add(dr["ProcessName"].ToString(), dr["ProcessCode"].ToString(), "ENT-" + 9, null, null, null, null, null, null, null, null, null, null, null, null, false, "", "", "", "../Images/curprocess.png", dr["OrgID"].ToString(), dr["UnitID"].ToString(), dr["DepartmentID"].ToString(), dr["SubfunctionID"].ToString(), _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, _UserDetails.UserID, "1", dr["ProcessID"].ToString(), "");
                        if (!string.IsNullOrEmpty(dr["ProcessCode"].ToString()))
                            collectRiskAsperView(ref dtBuildTableStructure, "0", "0", "0", "0", "0", dr["EntityTypeId"].ToString(), "0", dr["ProcessID"].ToString(), dr["ProcessCode"].ToString(), dr["ProcessCode"].ToString());
                    }
                }
                dtTableClone = dtBuildTableStructure;
            }

            DataRow[] drFilterData;

            if (_UserDetails.UserRole.Equals("BCMS Manager"))
            {
                DataView dv = dtBuildTableStructure.DefaultView;
                dv.Sort = "Item asc";
                dtBuildTableStructure = dv.ToTable();
            }
            else
            {
                dtRiskData = dtTableClone.Clone();

                string strGetFilterString = GetFilterString();
                if (strGetFilterString == "")
                {
                    strGetFilterString = "  ID LIKE '%ENT%'";
                }
                else
                {
                    strGetFilterString = GetFilterString() + " OR ID LIKE '%ENT%' ";
                }
                drFilterData = dtTableClone.Select(strGetFilterString);

                if (drFilterData != null)
                {
                    if (drFilterData.Count() > 0)
                    {
                        foreach (DataRow dr in drFilterData)
                        {
                            dtRiskData.ImportRow(dr);
                        }
                    }
                }

                DataView dv = dtRiskData.DefaultView;
                dv.Sort = "Item asc";
                dtRiskData = dv.ToTable();
            }

            foreach (DataRow dr in dtBuildTableStructure.Rows)
            {
                if (dr["iRiskID"].ToString() != string.Empty)
                {
                    int iRiskId = Convert.ToString(dr["IRiskID"]) == "" ? 0 : Convert.ToInt32(dr["IRiskID"]);

                    RiskItem objRiskItem = new RiskItem();
                    objRiskItem.Item = dr["Item"].ToString();
                    objRiskItem.ParentID = dr["ParentID"].ToString();
                    objRiskItem.ID = dr["ID"].ToString();
                    objRiskItem.Responsibility = dr["Responsibility"].ToString();
                    objRiskItem.NxtRevDate = dr["NxtRevDate"].ToString();
                    objRiskItem.Unit = dr["Unit"].ToString();
                    objRiskItem.Depart = dr["Depart"].ToString();
                    objRiskItem.SubDept = dr["SubDept"].ToString();
                    objRiskItem.Org = dr["Org"].ToString();
                    objRiskItem.RiskRating = dr["RiskRating"].ToString();
                    objRiskItem.RiskDescription = dr["RiskDescription"].ToString();
                    objRiskItem.RiskCategory = dr["RiskCategory"].ToString();
                    objRiskItem.IsEffective = dr["IsEffective"].ToString();
                    objRiskItem.Impact = dr["Impact"].ToString();
                    objRiskItem.Likelihood = dr["Likelihood"].ToString();
                    objRiskItem.IRiskID = dr["IRiskID"].ToString();
                    objRiskItem.ImageURL = dr["ImageURL"].ToString();
                    objRiskItem.OrgId = dr["OrgId"].ToString();
                    objRiskItem.UnitID = dr["UnitID"].ToString();
                    objRiskItem.DepartmentId = dr["DepartmentId"].ToString();
                    objRiskItem.SubFunctionID = dr["SubFunctionID"].ToString();
                    objRiskItem.AltUnitHeadId = dr["AltUnitHeadId"].ToString();
                    objRiskItem.UnitBCPCorId = dr["UnitBCPCorId"].ToString();
                    objRiskItem.AltBCPCorId = dr["AltBCPCorId"].ToString();
                    objRiskItem.DeptHeadId = dr["DeptHeadId"].ToString();
                    objRiskItem.AltDeptHeadId = dr["AltDeptHeadId"].ToString();
                    objRiskItem.SubFunOwnerId = dr["SubFunOwnerId"].ToString();
                    objRiskItem.AltSubFunOwnerId = dr["AltSubFunOwnerId"].ToString();
                    objRiskItem.RiskOwner = dr["RiskOwner"].ToString();
                    objRiskItem.RiskItemCategory = dr["RiskItemCategory"].ToString();
                    objRiskItem.ProcessID = dr["ProcessID"].ToString();
                    objRiskItem.RiskName = dr["RiskName"].ToString();
                    objRiskItem.ResidualImpact = dr["ResidualImpact"].ToString();
                    objRiskItem.ResidualLikeliHood = dr["ResidualLikeliHood"].ToString();
                    objRiskItem.ResidualRiskRating = dr["ResidualRiskRating"].ToString();
                    objRiskItem.ResidualRiskRatingName = dr["ResidualRiskRatingName"].ToString();
                    objRiskItem.RiskTreatmentStrategyAction = dr["RiskTreatmentStrategyAction"].ToString();
                    objRiskItem.Version = dr["Version"].ToString();
                    objRiskItem.RiskEntryDate = Convert.ToString(dr["RiskEntryDate"]);
                    //objRiskItem.RiskEntryDate = Convert.ToDateTime(dr["RiskEntryDate"]).ToShortDateString();
                    objRiskItem.IncidentTypeName = GetIncidentTypeName(iRiskId);  //dr["IncidentTypeName"].ToString();
                    objRiskItem.LastRevDate = dr["LastRevDate"].ToString();
                    objRiskItem.RiskCloseStatus = GetStatus(dr["RiskCloseStatus"].ToString());
                    objRiskItem.Status = GetApprovalStatus(Convert.ToString(dr["Status"]));
                    objRiskItem.SubRiskRegID = dr["SubRiskRegID"].ToString();
                    objRiskItem.RiskOwnerEmail = dr["companyemail"].ToString();
                    objRiskItem.RiskOwnerMobile = dr["mobilephone"].ToString();
                    objRiskItem.ApproverName = dr["ApproverName"].ToString();
                    objRiskItem.ApproverEmail = dr["ApproverEmail"].ToString();
                    objRiskItem.ApproverMobile = dr["ApproverMobile"].ToString();
                    objRiskItem.DisasterName = GetDisasterDetailsByRiskId(iRiskId);  //dr["DisasterName"].ToString();
                    lstriskItem.Add(objRiskItem);
                }

            }

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return lstriskItem;
    }


    public string? GetDisasterDetailsByRiskId(int iRiskId)
    {
        string? strDisasterName = string.Empty;
        Disaster objDisaster = new Disaster();
        try
        {
            objDisaster = _ProcessSrv.GetDisasterNameByRiskId(iRiskId);
            if (objDisaster != null)
            {
                strDisasterName = objDisaster.DisasterName;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return strDisasterName;
    }
    public string? GetIncidentTypeName(int iRiskId)
    {
        string? strIncidentTypeName = string.Empty;
        IncidentManagement objIncidentManagement = new IncidentManagement();
        try
        {
            objIncidentManagement = _ProcessSrv.GetThreatDetailsByRiskId(iRiskId);
            if (objIncidentManagement != null)
            {
                strIncidentTypeName = objIncidentManagement.DisasterName;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return strIncidentTypeName;
    }
    public string GetStatus(string strRiskStatus)
    {
        string strStatus = string.Empty;
        try
        {
            if (strRiskStatus != "" || strRiskStatus != null)
            {
                if (strRiskStatus == "1")
                {
                    strStatus = "Open";
                }
                else if (strRiskStatus == "2")
                {
                    strStatus = "Monitor";
                }
                else if (strRiskStatus == "3")
                {
                    strStatus = "Close";
                }
                else
                {
                    strStatus = "";
                }
            }
            else
            {
                strStatus = "";
            }

            return strStatus;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return strStatus;
        }
    }
    public string GetApprovalStatus(string strApprovalID)
    {
        string strStatus = string.Empty;

        if (strApprovalID.Equals(((int)BCPEnum.ApprovalType.Initiated).ToString()))
        {
            strStatus = BCPEnum.ApprovalType.Initiated.ToString();
        }
        else if (strApprovalID.Equals(((int)BCPEnum.ApprovalType.WaitingforApproval).ToString()))
        {
            strStatus = BCPEnum.ApprovalType.WaitingforApproval.ToString();
        }
        else if (strApprovalID.Equals(((int)BCPEnum.ApprovalType.Approved).ToString()))
        {
            strStatus = BCPEnum.ApprovalType.Approved.ToString();
        }
        else if (strApprovalID.Equals(((int)BCPEnum.ApprovalType.Disapproved).ToString()))
        {
            strStatus = BCPEnum.ApprovalType.Disapproved.ToString();
        }
        return strStatus;
    }
    protected string GetFilterString()
    {
        string strUserID = Convert.ToString(_UserDetails.UserID);

        string strFilterString = string.Empty;
        string strUserFilterString = string.Empty;
        string strMainFilterString = string.Empty;
        try
        {
            return strMainFilterString;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return strMainFilterString;
        }
    }
    protected string FilterGridForCount(DataTable dtFilterTable)
    {
        string strCount = "0";
        try
        {
            if (dtFilterTable != null)
            {

                strCount = dtFilterTable.Select(" RiskName IS NOT NULL AND iRiskID IS NOT NULL AND Version IS NOT NULL ").Count().ToString();
            }
            return strCount;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return strCount;
        }
    }
    protected DataTable BuildTableStructure()
    {
        DataTable objtable = new DataTable();
        try
        {
            objtable.Columns.Add("Item", typeof(String));
            objtable.Columns.Add("ID", typeof(String));
            objtable.Columns.Add("ParentID", typeof(String));
            objtable.Columns.Add("ChangedOn", typeof(String));
            objtable.Columns.Add("Responsibility", typeof(String));
            
            objtable.Columns.Add("NxtRevDate", typeof(String));
            objtable.Columns.Add("Unit", typeof(String));
            objtable.Columns.Add("Depart", typeof(String));
            objtable.Columns.Add("SubDept", typeof(String));
            objtable.Columns.Add("Org", typeof(String));
            objtable.Columns.Add("RiskTo", typeof(String));
            objtable.Columns.Add("RiskRating", typeof(String));
            objtable.Columns.Add("RiskDescription", typeof(String));
            objtable.Columns.Add("RiskCategory", typeof(String));
            objtable.Columns.Add("RiskType", typeof(String));
            objtable.Columns.Add("IsEffective", typeof(String));
            objtable.Columns.Add("Impact", typeof(String));
            objtable.Columns.Add("Likelihood", typeof(String));
            objtable.Columns.Add("iRiskID", typeof(String));
            objtable.Columns.Add("ImageURL", typeof(String));
            objtable.Columns.Add("OrgId", typeof(String));
            objtable.Columns.Add("UnitID", typeof(String));
            objtable.Columns.Add("DepartmentId", typeof(String));
            objtable.Columns.Add("SubFunctionID", typeof(String));
            objtable.Columns.Add("UnitHeadId", typeof(String));
            objtable.Columns.Add("AltUnitHeadId", typeof(String));
            objtable.Columns.Add("UnitBCPCorId", typeof(String));
            objtable.Columns.Add("AltBCPCorId", typeof(String));
            objtable.Columns.Add("DeptHeadId", typeof(String));
            objtable.Columns.Add("AltDeptHeadId", typeof(String));
            objtable.Columns.Add("SubFunOwnerId", typeof(String));
            objtable.Columns.Add("AltSubFunOwnerId", typeof(String));
            objtable.Columns.Add("RiskOwner", typeof(String));
            objtable.Columns.Add("RiskItemCategory", typeof(String));
            objtable.Columns.Add("ProcessID", typeof(String));
            objtable.Columns.Add("RiskName", typeof(String));
            objtable.Columns.Add("ResidualImpact", typeof(String));
            objtable.Columns.Add("ResidualLikeliHood", typeof(String));
            objtable.Columns.Add("ResidualRiskRating", typeof(String));
            objtable.Columns.Add("ResidualRiskRatingName", typeof(String));
            objtable.Columns.Add("RiskTreatmentStrategyAction", typeof(String));
            objtable.Columns.Add("Version", typeof(String));
            objtable.Columns.Add("RiskEntryDate", typeof(String));
            objtable.Columns.Add("IncidentTypeName", typeof(String));
            objtable.Columns.Add("LastRevDate", typeof(String));
            objtable.Columns.Add("RiskCloseStatus", typeof(String));
            objtable.Columns.Add("Status", typeof(String));
            objtable.Columns.Add("SubRiskRegID", typeof(String));
            objtable.Columns.Add("companyemail", typeof(String));
            objtable.Columns.Add("mobilephone", typeof(String));
            objtable.Columns.Add("ApproverName", typeof(String));
            objtable.Columns.Add("ApproverEmail", typeof(String));
            objtable.Columns.Add("ApproverMobile", typeof(String));

            return objtable;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return objtable;
        }
    }
    private object[] GetLicensedEntityDataTable()
    {
        DataTable objLicensedEntities = new DataTable();
        try
        {
            objLicensedEntities = _ProcessSrv.GetDetailsDistributionByDetailID_DT(0, 1, Convert.ToInt32(_UserDetails.OrgID));

            objLicensedEntities.Columns.Add("Active");
            foreach (DataRow dr in objLicensedEntities.Rows)
            {
                if (dr["IsActive"].ToString() != "")
                {
                    string Active = "1"; //CryptographyHelper.Md5Decrypt(dr["IsActive"].ToString());
                    Active = Active.Substring(Active.IndexOf('(') + 1, 1);
                    dr["Active"] = Active.ToString();
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        var lstLicensedEntity = (from DataRow dr in objLicensedEntities.Rows
                                 where dr["Active"].ToString() == "1"
                                 select dr["ProcessCode"]).ToArray();
        return lstLicensedEntity;
    }
    protected void collectRiskAsperView(ref DataTable table, string strOrgId, string strUnitId, string strDepartId, string strsubDepartId, string strRiskId, string strRiskItemCategory, string strRiskCategory, string strRiskToItem, string strParent, string strProcessCode)
    {
        try
        {
            List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
            string strIsEffective = "0";

            lstRiskManagement = _ProcessSrv.getBCMRiskList(strOrgId, strUnitId, strDepartId, strsubDepartId, strRiskId, strIsEffective, strRiskItemCategory, strRiskCategory, "0", "", strProcessCode);
            if (lstRiskManagement != null)
            {
                foreach (RiskManagement RiskItem in lstRiskManagement)
                {
                    bool isExist = table.Select().ToList().Exists(row => row["ParentID"].ToString() == Convert.ToString(RiskItem.RiskCode));
                    if (!isExist)
                    {
                        table.Rows.Add(RiskItem.RiskCode, RiskItem.RiskCode, (RiskItem.IsEffective.ToString() == "1") ? strParent : RiskItem.RiskCode, RiskItem.ChangedOn, RiskItem.RiskOwner, RiskItem.NextReviewDate,
                             RiskItem.RiskTo, RiskItem.RiskRating, RiskItem.RiskDesCription, RiskItem.RiskType, RiskItem.IncidentTypeName,
                            (RiskItem.IsEffective.ToString() == "1") ? true : false, GetImpactText(Convert.ToInt32(RiskItem.Impact)), GetProbabilityText(Convert.ToInt32(RiskItem.LikliHood)), RiskItem.ID,
                            RiskItem.AltDeptHeadId, RiskItem.SubFunOwnerId, RiskItem.AltSubFunOwnerId, RiskItem.RiskOwnerID, RiskItem.RiskItemCategory, "",
                            RiskItem.RiskDesCription, GetImpactText(Convert.ToInt32(RiskItem.ResidualImpact)), GetProbabilityText(Convert.ToInt32(RiskItem.ResidualLikeliHood)) + "(" + GetProbabilityWeightage(Convert.ToInt32(RiskItem.ResidualLikeliHood)) + ")", RiskItem.ResidualRiskRating, RiskItem.MgmtDecision, RiskItem.Version, RiskItem.RiskEntryDate, RiskItem.CateName, GetDate(RiskItem.LastReviewDate.ToString("dd-MM-yyyy")), RiskItem.RiskCloseStatus, RiskItem.Status, RiskItem.BCMEntityID);
                    }
                    bool isExists = table.Select().ToList().Exists(row => row["ParentID"].ToString() == Convert.ToString(RiskItem.RiskCode));

                    if (!isExists)
                    {
                        string? strRiskCode = RiskItem == null ? string.Empty : RiskItem.RiskCode;
                        List<RiskManagement> lstRiskManagements = _ProcessSrv.getBCMRiskList(strOrgId, strUnitId, strDepartId, strsubDepartId, strRiskId, strIsEffective, strRiskItemCategory, strRiskCategory, "0", strRiskCode, strProcessCode);
                        if (lstRiskManagements != null)
                        {
                            foreach (RiskManagement RiskItemRisk in lstRiskManagements)
                            {
                                if (RiskItemRisk.Version != "1.0")
                                    table.Rows.Add(RiskItem.RiskCode, RiskItem.RiskCode, (RiskItem.IsEffective.ToString() == "1") ? strParent : RiskItem.RiskCode, RiskItem.ChangedOn, RiskItem.RiskOwner, RiskItem.NextReviewDate,
                                         RiskItem.RiskTo, RiskItem.RiskRating, RiskItem.RiskDesCription, RiskItem.RiskType, RiskItem.IncidentTypeName,
                                        (RiskItem.IsEffective.ToString() == "1") ? true : false, GetImpactText(Convert.ToInt32(RiskItem.Impact)), GetProbabilityText(Convert.ToInt32(RiskItem.LikliHood)), RiskItem.ID,
                                        RiskItem.AltDeptHeadId, RiskItem.SubFunOwnerId, RiskItem.AltSubFunOwnerId, RiskItem.RiskOwnerID, RiskItem.RiskItemCategory, "",
                                        RiskItem.RiskDesCription, GetImpactText(Convert.ToInt32(RiskItem.ResidualImpact)),
                                        GetProbabilityText(Convert.ToInt32(RiskItem.ResidualLikeliHood)) + "(" + GetProbabilityWeightage(Convert.ToInt32(RiskItem.ResidualLikeliHood)) + ")",
                                        RiskItem.ResidualRiskRating, RiskItem.MgmtDecision, RiskItem.Version, RiskItem.RiskEntryDate,
                                        RiskItem.CateName, GetDate(RiskItem.LastReviewDate.ToString("dd-MM-yyyy")), RiskItem.RiskCloseStatus, RiskItem.Status, RiskItem.BCMEntityID);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
    protected void collectRiskAsperView_New(ref DataTable table, string strEntityTypeId, string strProcessID, string strProcessCode)
    {
        try
        {
            string strFilter = "RiskItemCategory= " + strEntityTypeId + " and ProcessCode='" + strProcessCode + "'";

            DataTable? dtRisk = ViewData["RiskColl"] as DataTable;
            DataView dv = dtRisk.DefaultView;
            dv.RowFilter = strFilter;
            DataTable dtGetBCMRiskManageList = dv.ToTable();

            if (dtGetBCMRiskManageList != null)
            {
                foreach (DataRow drrow in dtGetBCMRiskManageList.Rows)
                {
                    //get risk treatment option
                    string strRiskTreatmentOption = string.Empty;
                    if (drrow["MgmtDecision"].ToString() == "0")
                    {
                        strRiskTreatmentOption = "No Action";
                    }
                    else if (drrow["MgmtDecision"].ToString() == "1")
                    {
                        strRiskTreatmentOption = "Tolerate";
                    }
                    else if (drrow["MgmtDecision"].ToString() == "2")
                    {
                        strRiskTreatmentOption = "Terminate";
                    }
                    else if (drrow["MgmtDecision"].ToString() == "3")
                    {
                        strRiskTreatmentOption = "Treat";
                    }
                    else if (drrow["MgmtDecision"].ToString() == "4")
                    {
                        strRiskTreatmentOption = "Transfer";
                    }
                   
                    

                    string? strCodes = Convert.ToString(drrow["RiskCode"]);
                    bool isExist = table.Select().ToList().Exists(row => row["ParentID"].ToString() == drrow["RiskCode"].ToString());
                    if (!isExist)
                    {
                        table.Rows.Add(drrow["RiskCode"], drrow["RiskCode"], (drrow["IsEffective"].ToString() == "1") ? strProcessCode : drrow["RiskCode"], drrow["ChangedOn"], drrow["OwnerName"], GetDate(drrow["NextReviewDate"].ToString()), (!string.IsNullOrEmpty(drrow["UnitName"].ToString())) ? drrow["UnitName"] : null,
                            (!string.IsNullOrEmpty(drrow["departmentname"].ToString())) ? drrow["departmentname"] : null, (!string.IsNullOrEmpty(drrow["subdept"].ToString())) ? drrow["subdept"] : null, (!string.IsNullOrEmpty(drrow["OrgName"].ToString())) ? drrow["OrgName"] : null, drrow["RiskTo"], drrow["RiskRating"], drrow["RiskDesCription"], drrow["RiskType"], drrow["IncidentTypeName"],
                            (drrow["IsEffective"].ToString() == "1") ? true : false, drrow["ImpactName"], drrow["LikehoodName"], drrow["ID"], "imageurl", drrow["OrgID"], drrow["UnitID"], drrow["DepartmentID"], drrow["SubDepartmentID"], drrow["UnitHeadId"], drrow["AltUnitHeadId"], drrow["UnitBCPCorId"], drrow["AltBCPCorId"], drrow["DeptHeadId"],
                            drrow["AltDeptHeadId"], drrow["SubFunOwnerId"], drrow["AltSubFunOwnerId"], drrow["RiskOwner"], drrow["RiskItemCategory"], "",
                            drrow["RiskDesCription"], drrow["ResidualImpactName"], drrow["ResidualLikeliHoodName"], drrow["ResidualRiskRating"], drrow["ResidualRiskRatingName"], strRiskTreatmentOption, drrow["Version"], drrow["RiskEntryDate"], drrow["CateName"], GetDate(drrow["LastReviewedDt"].ToString()), drrow["RiskCloseStatus"], drrow["Status"], drrow["SubRiskRegID"],
                            drrow["companyemail"], drrow["mobilephone"], drrow["ApproverName"], drrow["ApproverEmail"], drrow["ApproverMobile"]);
                    }
                    bool isExists = table.Select().ToList().Exists(row => row["ParentID"].ToString() == drrow["RiskCode"].ToString());
                    if (!isExists)
                    {
                        strFilter = "RiskItemCategory= " + strEntityTypeId + " and ProcessCode='" + strProcessCode + "' and RiskCode='" + drrow["RiskCode"].ToString() + "'";
                        DataView dvSubRiskList = dtRisk.DefaultView;
                        dvSubRiskList.RowFilter = strFilter;
                        DataTable dtSubRiskList = dvSubRiskList.ToTable();
                        if (dtSubRiskList != null)
                        {
                            foreach (DataRow drsub in dtSubRiskList.Rows)
                            {
                                if (drsub["Version"].ToString() != "1.0")
                                {
                                    table.Rows.Add(drsub["RiskCode"], drsub["RiskCode"], drsub["RiskCode"], drsub["ChangedOn"], drsub["OwnerName"], GetDate(drsub["NextReviewDate"].ToString()), (!string.IsNullOrEmpty(drsub["UnitName"].ToString())) ? drsub["UnitName"] : null,
                                    (!string.IsNullOrEmpty(drsub["departmentname"].ToString())) ? drsub["departmentname"] : null, (!string.IsNullOrEmpty(drsub["subdept"].ToString())) ? drsub["subdept"] : null, (!string.IsNullOrEmpty(drsub["OrgName"].ToString())) ? drsub["OrgName"] : null, drsub["RiskTo"], drsub["RiskRating"], drsub["RiskDesCription"], drsub["RiskType"], drsub["IncidentTypeName"],
                                    (drsub["IsEffective"].ToString() == "1") ? true : false, drsub["ImpactName"], drsub["LikehoodName"], drsub["ID"], "imageurl", drsub["OrgID"], drsub["UnitID"], drsub["DepartmentID"], drsub["SubDepartmentID"], drsub["UnitHeadId"], drsub["AltUnitHeadId"], drsub["UnitBCPCorId"], drsub["AltBCPCorId"], drsub["DeptHeadId"],
                                    drsub["AltDeptHeadId"], drsub["SubFunOwnerId"], drsub["AltSubFunOwnerId"], drsub["RiskOwner"], drsub["RiskItemCategory"], "", drsub["RiskDesCription"], drsub["ResidualImpactName"], drsub["ResidualLikeliHoodName"], drsub["ResidualRiskRating"], drrow["ResidualRiskRatingName"], strRiskTreatmentOption, drsub["Version"], GetDate(drsub["RiskEntryDate"].ToString()), drsub["CateName"], GetDate(drsub["LastReviewedDt"].ToString()), drsub["RiskCloseStatus"], drsub["Status"], drrow["SubRiskRegID"],
                                    drrow["companyemail"], drrow["mobilephone"], drrow["ApproverName"], drrow["ApproverEmail"], drrow["ApproverMobile"]);
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
    protected int GetProbabilityWeightage(int iLikelihood)
    {
        int iLikelihoodText = 0;
        try
        {
            int iDefaultProfileID = _Utilities.RiskProfileID;
            RiskImpactMaster objRiskImpactMaster = _ProcessSrv.RiskProbabilitymaster_GetByID(iDefaultProfileID, iLikelihood);
            if (objRiskImpactMaster != null)
            {
                if (iLikelihood >= 0)
                    iLikelihoodText = objRiskImpactMaster.ProbabilityWeightage;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return iLikelihoodText;
    }
    protected string? GetProbabilityText(int iLikelihood)
    {
        string? strLikelihoodText = "NA";
        try
        {
            int iDefaultProfileID = _Utilities.RiskProfileID;
            RiskImpactMaster objRiskImpactMaster = _ProcessSrv.RiskProbabilitymaster_GetByID(iDefaultProfileID, iLikelihood);
            if (objRiskImpactMaster != null)
            {
                if (iLikelihood >= 0)
                    strLikelihoodText = objRiskImpactMaster.ProbabilityName;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return strLikelihoodText;
    }
    protected string? GetImpactText(int iImpact)
    {
        string? strLikelihoodText = "NA";
        try
        {
            int DefaultProfileID = _Utilities.RiskProfileID;
            RiskImpactMaster objRiskImpactMaster = _ProcessSrv.RiskImpactmaster_GetByID(DefaultProfileID, iImpact);
            if (objRiskImpactMaster != null)
            {
                if (iImpact >= 0)
                    strLikelihoodText = objRiskImpactMaster.ImpactName;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return strLikelihoodText;
    }
    protected string GetDate(string CurrentDate)
    {
        string strDate = string.Empty;
        try
        {
            if (CurrentDate != "" || CurrentDate != null)
            {
                if (CurrentDate == "1/1/0001 12:00:00 AM")
                {
                    strDate = "N/A";
                }
                else
                {
                    strDate = CurrentDate;
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return strDate;
    }
    public int RiskProfileID()
    {
        if (_UserDetails != null)
        {
            return _ProcessSrv.GetDefaultProfile(Convert.ToInt32((_UserDetails.OrgID)));
        }
        else
        {
            return 0;
        }
    }
    #endregion

    #region for dropdownlist
    [HttpGet]
    public JsonResult BindProcesses(int iEntityId)
    {
        List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
        try
        {
            lstBusinessProcessInfo = _Utilities.PopulateBCMEntityByEntityId(iEntityId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(lstBusinessProcessInfo);
    }

    [HttpGet]
    public JsonResult BindFacility(int iEntityId)
    {
        try
        {
            List<Facility> lstfacilityList = new List<Facility>();

            if (iEntityId == 2)
            {
                lstfacilityList = _Utilities.PopulateGetAllFacilities_OrgId(_UserDetails.OrgID);
            }
            return Json(lstfacilityList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult BindOtherBCMEntities(int iEntityId)
    {
        try
        {
            List<OtherBCMEntities> lstOtherBCMEntities = new List<OtherBCMEntities>();

            if (iEntityId == 7)
            {
                lstOtherBCMEntities = _Utilities.PopulateOtherBCMEntities(_UserDetails.OrgID, _UserDetails.UnitID, _UserDetails.DepartmentID, _UserDetails.SubDepartmentID);
            }
            return Json(lstOtherBCMEntities);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult BindApplicationByEntityId(int iEntityId)
    {
        List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
        try
        {
            if (iEntityId == 9)
            {
                lstBusinessProcessInfo = _Utilities.PopulateApplicationByEntityTypeId(iEntityId);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(lstBusinessProcessInfo);
    }

    public JsonResult BindIncidentTypesByDisasterID(int iDisasterID)
    {
        List<Disaster> lstDisasterColl = new List<Disaster>();
        try
        {
            lstDisasterColl = _ProcessSrv.GetIncidentTypesByDisasterID(iDisasterID, 0);
            return Json(lstDisasterColl);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    public JsonResult BindLocationById(int iEntityId)
    {
        var lstLocationMaster = new List<LocationMaster>();
        try
        {
            lstLocationMaster = _Utilities.PopulateGetLocationMasterList_EntityId(iEntityId);
            return Json(lstLocationMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    public JsonResult BindITServiceMasterByEntityTypeId(int iEntityId)
    {
        var lstLocationMaster = new List<ITServiceMaster>();
        try
        {
            lstLocationMaster = _Utilities.PopulateGetITServiceMasterByEntityTypeId(iEntityId);
            return Json(lstLocationMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    public JsonResult BindCompanyMasterInfoByEntityID(int iEntityId)
    {

        var lstLocationMaster = new List<CompanyMasterInfo>();
        try
        {
            lstLocationMaster = _Utilities.PopulateGetCompanyMasterInfoByEntityTypeId(iEntityId);
            return Json(lstLocationMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    public JsonResult BindPeoplesInfoByEntityID(int iEntityId)
    {

        var lstResourcesInfo = new List<ResourcesInfo>();
        try
        {
            lstResourcesInfo = _Utilities.PopulateGetPeoplesByEntityTypeId(iEntityId);
            return Json(lstResourcesInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }
    #endregion

    #region Calculate risk rating
    [HttpGet]
    public JsonResult CalculateRiskRating(RiskImpactMaster objriskImpactMaster)
    {
        //RiskImpactMaster objRiskImpactMaster = new RiskImpactMaster();
        //try
        //{
        //    objRiskImpactMaster.RiskRating = Convert.ToInt32(objriskImpactMaster.ProbabilityID) * Convert.ToInt32(objriskImpactMaster.Impact);
        //    objRiskImpactMaster.ImpactRating = GetRiskRatingDescription(Convert.ToInt32(objRiskImpactMaster.RiskRating));
        //}
        //catch (Exception ex)
        //{
        //    _CVLogger.LogErrorApp(ex);
        //}

        ////return Json(objRiskImpactMaster);
        //return Json(new { riskRating = objRiskImpactMaster.RiskRating, impactRating= objRiskImpactMaster.ImpactRating });

        var result = new { riskRating = 0, impactRating = "NA" };
        try
        {
            if (objriskImpactMaster != null &&
                int.TryParse(objriskImpactMaster.ProbabilityID.ToString(), out int probability) &&
                int.TryParse(objriskImpactMaster.Impact.ToString(), out int impact))
            {
                int riskRating = probability * impact;
                string impactRating = GetRiskRatingDescription(riskRating);
                result = new { riskRating, impactRating };
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(result);
    }

    [HttpGet]
    public JsonResult CalculateReviseRiskRating(RiskManagement objriskImpactMaster)
    {
        RiskImpactMaster objRiskManagement = new RiskImpactMaster();
        try
        {
            objRiskManagement.ResidualRiskRatings = Convert.ToInt32(objriskImpactMaster.ResidualLikeliHood) * Convert.ToInt32(objriskImpactMaster.ResidualImpact);
            objRiskManagement.ResidualImpact = GetRiskRatingDescription(Convert.ToInt32(objRiskManagement.ResidualRiskRatings));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return Json(objRiskManagement);
    }

    private string GetRiskRatingDescription(int iRatingVal)
    {
        string strImpactRating = string.Empty;
        try
        {
            int iDefaultProfileID = _Utilities.RiskProfileID;
            List<RiskImpactMaster> lstRiskImpactMaster = _ProcessSrv.GetAllRiskSeverityDetails(Convert.ToInt32(_UserDetails.OrgID), iDefaultProfileID);

            if (iRatingVal >= 0)
            {

                if (lstRiskImpactMaster != null)
                {
                    var lstRating = (from RiskImpactMaster objImpact in lstRiskImpactMaster
                                     where iRatingVal >= Convert.ToInt32(objImpact.FromImpactRange) && iRatingVal <= Convert.ToInt32(objImpact.ToImpactRange)
                                     select objImpact).FirstOrDefault();

                    if (lstRating != null)
                    {
                        strImpactRating = lstRating.RiskSeverityName == null ? string.Empty : lstRating.RiskSeverityName;
                    }
                }
            }
            else
            {
                strImpactRating = "NA";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return strImpactRating;
    }
    #endregion

    #region AddRiskAssessmentProcessForm
    [HttpGet]
    public IActionResult AddRiskAssessmentProcessForm(int IRiskID)
    {
        try
        {
            BusinessProcessInfo objBusinessProcessInfo = new BusinessProcessInfo();
            if (IRiskID > 0)
            {
                objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(IRiskID), 9);

                List<BCMEntitiesTypeMaster> lstBCMEntitiesTypeMaster = _Utilities.PopulateBCMEntitiesTypeMaster_ByEntityTypeID(objBusinessProcessInfo.EntityTypeID);
                ViewBag.BCMEntitiesTypeMaster = lstBCMEntitiesTypeMaster;
            }
            else
            {
                List<BCMEntitiesTypeMaster> lstBCMEntitiesTypeMaster = _Utilities.PopulateBCMEntities();
                ViewBag.BCMEntitiesTypeMaster = lstBCMEntitiesTypeMaster;
            }

            List<Disaster> lstDisaster = _Utilities.PopulateDisasterTypeList(0);
            ViewBag.Disaster = lstDisaster;

            List<BusinessProcessInfo> lstBusinessProcessInfo = _Utilities.PopulateBusinessProcessMaster(_UserDetails.OrgID);
            ViewBag.BusinessProcessInfo = lstBusinessProcessInfo;

            List<Disaster> lstIncident = _Utilities.PopulateIncidentByDisasterID(0, 0);
            ViewBag.Incident = lstIncident;

            List<ResourcesInfo> lstRiskOwner = _Utilities.GetResourcesListForDDL(0);
            ViewBag.RiskOwner = lstRiskOwner;

            List<ResourcesInfo> lstRiskChampion = _Utilities.GetResourcesListForDDL(0);
            ViewBag.RiskChampion = lstRiskChampion;

            List<RiskImpactMaster> lstRiskProbabilitymaster = _Utilities.GetAllRiskProbabilitymaster(0);
            ViewBag.RiskProbabilitymaster = lstRiskProbabilitymaster;

            List<RiskImpactMaster> lstAllRiskImpactmaster = _Utilities.GetAllRiskImpactmaster(0);
            ViewBag.AllRiskImpactmaster = lstAllRiskImpactmaster;

            List<RecoveryPlan> lstApprovedRecoveryPlan = _Utilities.PopulateApprovedRecoveryPlan(Convert.ToInt32(_UserDetails.UserID), Convert.ToInt32(_UserDetails.OrgID));
            ViewBag.ApprovedRecoveryPlan = lstApprovedRecoveryPlan;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        //return PartialView("_AddRiskAssessmentProcess", new RiskManagement());
        return View();
    }

    [HttpPost]
    public IActionResult AddRiskAssessmentProcessForm(RiskManagement objRiskManagement)
    {
        string? strRiskID = "-1";

        try
        {
            if (strRiskID.Contains("-1"))
            {
                RiskManagement objRiskManagementEntity1 = new RiskManagement();
                objRiskManagementEntity1 = Build_RiskEntity_RiskRegister(objRiskManagement);
                objRiskManagementEntity1.RiskTrendStatus = 0;

                if (Convert.ToString(objRiskManagementEntity1.PlanID) == "0")
                {
                    objRiskManagementEntity1.IncidentDisplayName = string.Empty;
                }

                if (objRiskManagementEntity1 != null)
                {
                    objRiskManagementEntity1.RiskTrendStatus = 0;
                    bool iRiskID = _ProcessSrv.RiskAssessmentProcessFormSave(objRiskManagementEntity1, ref strRiskID);
                }
            }
            else
            {
                RiskManagement objRiskManagementEntity2 = new RiskManagement();
                objRiskManagementEntity2 = Build_RiskEntity_RiskRegister(objRiskManagement);
                objRiskManagementEntity2.RiskID = Convert.ToInt32(objRiskManagement.RiskID);

                int iPrevResiRating = 0;
                int iCurResiRating = 0;

                iCurResiRating = Convert.ToInt32(objRiskManagement.ResidualLikeliHood) * Convert.ToInt32(objRiskManagement.ResidualImpact);

                if (Convert.ToBoolean(objRiskManagement.Version) == true)
                {
                    int TrendSts = CalculateRiskTrendStatus(iPrevResiRating, iCurResiRating, Convert.ToString(objRiskManagementEntity2.RiskID));
                    objRiskManagementEntity2.RiskTrendStatus = TrendSts;
                }

                if (objRiskManagementEntity2 != null)
                {
                    bool isRiskAssessmentProcess = false;
                    int iiD = 0;
                    if (Convert.ToString(objRiskManagementEntity2.PlanID) == "0")
                    {
                        objRiskManagementEntity2.IncidentDisplayName = string.Empty;
                    }
                    iiD = _ProcessSrv.RiskAssessmentProcessFormUpdate(objRiskManagementEntity2);
                    if (iiD > 0)
                    { isRiskAssessmentProcess = true; }
                    else
                    { isRiskAssessmentProcess = false; }
                }
            }

            int iIsClosed = Convert.ToBoolean(objRiskManagement.RiskCloseStatus) ? 1 : 0;
            _ProcessSrv.RiskProcessFormUpdateCloseDateByRiskId(Convert.ToString(objRiskManagement.LastReviewDate), strRiskID, iIsClosed);

            RiskManagement objRiskManagementEntity = new RiskManagement();
            objRiskManagementEntity = Build_RiskEntity_RiskSubRegister(objRiskManagement, strRiskID);

            if (objRiskManagementEntity != null)
            {
                bool success = false;
                int iID = 0;
                if (objRiskManagementEntity.Version != "")
                {
                    iID = _ProcessSrv.RiskAssessmentSubRegisterProcessFormSave_1(objRiskManagementEntity);
                    if (iID > 0)
                    {
                        success = true;

                        DataTable dtTreatmentPlan = _ProcessSrv.TreatmentPlanByRiskSubID(Convert.ToInt32(iID));

                        if (dtTreatmentPlan.Rows.Count >= 0)
                        {
                            foreach (DataRow dr in dtTreatmentPlan.Rows)
                            {
                                int iRiskSubID = Convert.ToInt32(iID);
                                int iPlanID = Convert.ToInt32(dr["ID"]);
                                int iIsEffective = Convert.ToInt32(dr["IsEffectivePlan"]);
                                bool MapID = _ProcessSrv.SaveRiskTreatmentMap(iRiskSubID, iPlanID, iIsEffective);
                            }
                        }
                    }
                }
                else
                {
                    success = _ProcessSrv.RiskAssessmentSubRegisterProcessForm_Update(objRiskManagementEntity);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageRisk");
    }

    public BusinessProcessInfo GetAllProcessCodeByApplicationId(int iProcessId)
    {
        BusinessProcessInfo objBusinessProcessInfo = _ProcessSrv.GetProcessDetailsByProcessId(iProcessId);
        return objBusinessProcessInfo;
    }

    private RiskManagement Build_RiskEntity_RiskRegister(RiskManagement objRiskManagement)
    {
        string? strProcessId = Convert.ToString(objRiskManagement.ProcessID);

        RiskManagement objRiskManagementEntity = new RiskManagement();
        objRiskManagementEntity.RiskName = objRiskManagement.Vulnerability;
        objRiskManagementEntity.RiskItemCategory = objRiskManagement.RiskItemCategory;
        objRiskManagementEntity.RiskItemSubCategory = objRiskManagement.RiskItemSubCategory;
        objRiskManagementEntity.RiskCategory = objRiskManagement.RiskCategory;
        objRiskManagementEntity.RiskType = objRiskManagement.RiskType;

        BusinessProcessInfo objBusinessProcessInfo = GetAllProcessCodeByApplicationId(Convert.ToInt32(strProcessId));
        string? strProcessCode = objBusinessProcessInfo.ProcessCode;
        string? strProcessName = objBusinessProcessInfo.ProcessName;
        int iProcessID = objBusinessProcessInfo.ProcessID;
        objRiskManagementEntity.RiskToItem = iProcessID.ToString();

        objRiskManagementEntity.RiskTo = strProcessName;
        objRiskManagementEntity.ProcessCode = strProcessCode;
        objRiskManagementEntity.UnitID = objRiskManagement.UnitID;
        objRiskManagementEntity.DepartmentID = objRiskManagement.DepartmentID;
        objRiskManagementEntity.OrgID = objRiskManagement.OrgID;
        objRiskManagementEntity.CreatedBy = Convert.ToInt32(_UserDetails.UserID);
        objRiskManagementEntity.CloseDate = objRiskManagement.CloseDate;
        objRiskManagementEntity.SubDepartmentID = objRiskManagement.SubDepartmentID;
        objRiskManagementEntity.ProcessID = iProcessID;
        objRiskManagementEntity.LocationID = 0;
        objRiskManagementEntity.FacilityID = 0;
        objRiskManagementEntity.LastReviewDate = objRiskManagement.LastReviewDate;
        objRiskManagementEntity.RiskReviewDate = objRiskManagement.RiskReviewDate;
        objRiskManagementEntity.RiskTypeID = objRiskManagement.RiskTypeID;
        objRiskManagementEntity.RiskTreeStructure = objRiskManagement.RiskTreeStructure;
        objRiskManagementEntity.UpdateReason = objRiskManagement.UpdateReason;
        objRiskManagementEntity.UpdatedByID = Convert.ToInt32(_UserDetails.UserID);
        objRiskManagementEntity.RiskCloseStatus = Convert.ToInt32(objRiskManagement.RiskCloseStatus);
        objRiskManagementEntity.ServiceCriticallyValue = Convert.ToString(objRiskManagement.ServiceCriticallyValue);
        objRiskManagementEntity.PlanID = objRiskManagement.PlanID;
        objRiskManagementEntity.IncidentDisplayName = objRiskManagement.IncidentDisplayName;
        objRiskManagementEntity.RiskCloseStatus = objRiskManagement.RiskCloseStatus;

        return objRiskManagementEntity;
    }

    private RiskManagement Build_RiskEntity_RiskSubRegister(RiskManagement objRiskManagement, string strRiskID)
    {
        string strVersion = "0";
        string strUpdated_Date = string.Empty;
        int iUpdated_By = 0;

        if (objRiskManagement.Version != null)
        {
            strVersion = string.IsNullOrEmpty(objRiskManagement.Version) ? "1.0" : objRiskManagement.Version;

            strUpdated_Date = "";
            iUpdated_By = Convert.ToInt32(_UserDetails.UserID);
        }
        else
        {
            strVersion = objRiskManagement.Version == null ? string.Empty : objRiskManagement.Version;
            strUpdated_Date = DateTime.Now.ToString();
            iUpdated_By = Convert.ToInt32(_UserDetails.UserID);
        }

        RiskManagement objRiskManagementEntity = new RiskManagement();
        objRiskManagementEntity.SubRiskRegID = objRiskManagement.SubRiskRegID;
        objRiskManagementEntity.RiskCode = Convert.ToString(strRiskID);
        objRiskManagementEntity.RiskDesCription = objRiskManagement.RiskDesCription;
        objRiskManagementEntity.Impact = Convert.ToString(objRiskManagement.Impact);
        objRiskManagementEntity.LikliHood = objRiskManagement.LikliHood;
        objRiskManagementEntity.ResidualImpact = objRiskManagement.ResidualImpact;
        objRiskManagementEntity.ResidualLikeliHood = objRiskManagement.ResidualLikeliHood;
        objRiskManagementEntity.ResidualRiskRatings = objRiskManagement.ResidualRiskRatings;
        objRiskManagementEntity.TimeLine = objRiskManagement.TimeLine;
        objRiskManagementEntity.Version = "1.0";
        objRiskManagementEntity.UpdatedDate = DateTime.Now;
        objRiskManagementEntity.UpdatedByID = objRiskManagement.UpdatedByID;
        objRiskManagementEntity.Mitigation = "NA";
        objRiskManagementEntity.CotigencyStep = objRiskManagement.CotigencyStep;
        objRiskManagementEntity.CotigencyPlan = objRiskManagement.CotigencyPlan;
        objRiskManagementEntity.RiskRating = objRiskManagement.RiskRating;
        objRiskManagementEntity.RiskValue = objRiskManagement.RiskValue;
        objRiskManagementEntity.NextReviewDate =  Convert.ToDateTime(objRiskManagement.NextReviewDate) == DateTime.MinValue ? DateTime.Now : objRiskManagement.NextReviewDate;
        objRiskManagementEntity.LastReviewDate =  Convert.ToDateTime(objRiskManagement.LastReviewDate) == DateTime.MinValue ? DateTime.Now : objRiskManagement.LastReviewDate;
        objRiskManagementEntity.RiskOwner = Convert.ToString(objRiskManagement.RiskOwnerID);
        objRiskManagementEntity.RiskChampionID = Convert.ToInt32(objRiskManagement.RiskChampionID);
        objRiskManagementEntity.MgmtDecision = Convert.ToString(objRiskManagement.MgmtDecision);
        objRiskManagementEntity.RiskHandleStatus = Convert.ToString(objRiskManagement.RiskHandleStatus);
        objRiskManagementEntity.ProbableRiskScenarios = Convert.ToString(objRiskManagement.ProbableRiskScenarios);
        objRiskManagementEntity.Reminder = Convert.ToString(objRiskManagement.Reminder);
        objRiskManagementEntity.RecurrenceRule = Convert.ToString(objRiskManagement.RecurrenceRule);
        objRiskManagementEntity.CreatedBy = _UserDetails.UserID;
        objRiskManagementEntity.Field1 = Convert.ToString(objRiskManagement.Field1);
        objRiskManagementEntity.Field2 = Convert.ToString(objRiskManagement.Field2);
        objRiskManagementEntity.Field3 = Convert.ToString(objRiskManagement.Field3);
        objRiskManagementEntity.Field4 = Convert.ToString(objRiskManagement.Field4);
        objRiskManagementEntity.RiskName = Convert.ToString(objRiskManagement.RiskName);
        objRiskManagementEntity.UpdateReason = "";
        objRiskManagementEntity.Vulnerability = Convert.ToString(objRiskManagement.Vulnerability);
        objRiskManagementEntity.TreatmentCost = Convert.ToString(objRiskManagement.TreatmentCost);
        objRiskManagementEntity.RiskTargetDate = Convert.ToDateTime(objRiskManagement.RiskTargetDate) == DateTime.MinValue ? DateTime.Now : objRiskManagement.RiskTargetDate;
        objRiskManagementEntity.MDecisionComment = Convert.ToString(objRiskManagement.MDecisionComment);

        return objRiskManagementEntity;
    }

    private int CalculateRiskTrendStatus(int iPrevResiRating, int iCurResiRating, string strRiskID)
    {
        int iRatingValue = 0;
        try
        {
            if (iPrevResiRating > iCurResiRating)
            {
                iRatingValue = (int)BCPEnum.RiskTrendStatus.Downward;
                return iRatingValue;
            }
            else if (iPrevResiRating < iCurResiRating)
            {
                iRatingValue = (int)BCPEnum.RiskTrendStatus.Upward;
                return iRatingValue;
            }
            else if (iPrevResiRating == iCurResiRating)
            {
                iRatingValue = (int)BCPEnum.RiskTrendStatus.NoChange;
                return iRatingValue;
            }
            return iRatingValue;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return 0;
        }
    }
    #endregion

    #region EditRiskAssessmentProcessForm
    [HttpGet]
    public IActionResult EditRiskAssessmentProcessForm(int iRiskID, int iSubRiskId)
    {

        HttpContext.Session.SetString("IRiskID", Convert.ToString(iRiskID));

        RiskManagement objRiskManagement = new RiskManagement();
        try
        {
            List<BCMEntitiesTypeMaster> lstBCMEntitiesTypeMaster = _Utilities.PopulateBCMEntities();
            ViewBag.BCMEntitiesTypeMaster = lstBCMEntitiesTypeMaster;

            List<Disaster> lstDisaster = _Utilities.PopulateDisasterTypeList(0);
            ViewBag.Disaster = lstDisaster;

            List<BusinessProcessInfo> lstBusinessProcessInfo = _Utilities.PopulateBusinessProcessMaster(_UserDetails.OrgID);
            ViewBag.BusinessProcessInfo = lstBusinessProcessInfo;

            List<Disaster> lstIncident = _Utilities.PopulateIncidentByDisasterID(0, 0);
            ViewBag.Incident = lstIncident;

            List<ResourcesInfo> lstRiskOwner = _Utilities.GetResourcesListForDDL(0);
            ViewBag.RiskOwner = lstRiskOwner;

            List<ResourcesInfo> lstRiskChampion = _Utilities.GetResourcesListForDDL(0);
            ViewBag.RiskChampion = lstRiskChampion;

            List<RiskImpactMaster> lstRiskProbabilitymaster = _Utilities.GetAllRiskProbabilitymaster(0);
            ViewBag.RiskProbabilitymaster = lstRiskProbabilitymaster;

            List<RiskImpactMaster> lstAllRiskImpactmaster = _Utilities.GetAllRiskImpactmaster(0);
            ViewBag.AllRiskImpactmaster = lstAllRiskImpactmaster;

            List<RecoveryPlan> lstApprovedRecoveryPlan = _Utilities.PopulateApprovedRecoveryPlan(Convert.ToInt32(_UserDetails.UserID), Convert.ToInt32(_UserDetails.OrgID));
            ViewBag.ApprovedRecoveryPlan = lstApprovedRecoveryPlan;

            List<BusinessProcessInfo> lstBCMEntity = _Utilities.PopulateBCMEntityGetAll();
            ViewBag.BCMEntity = lstBCMEntity;

            ViewBag.RecordID = iRiskID;
            ViewBag.EntityID = Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.RiskManagement).ToString();

            //get tratment plan by subriskid
            DataTable dtTreatmentplan = _ProcessSrv.TreatmentPlanByRiskSubID(iSubRiskId, 1);
            List<RiskTreatmentPlan> lstRiskTreatmentPlan = new List<RiskTreatmentPlan>();
            foreach (DataRow dr in dtTreatmentplan.Rows)
            {
                RiskTreatmentPlan objRiskTreatmentPlan = new RiskTreatmentPlan();
                objRiskTreatmentPlan.Id = Convert.ToInt32(dr["Id"]);
                objRiskTreatmentPlan.PlanName = dr["PlanName"].ToString();
                objRiskTreatmentPlan.Owner = Convert.ToInt32(dr["IsActive"]);
                objRiskTreatmentPlan.IdentifiedDate = Convert.ToDateTime(dr["IdentifiedDate"]).ToString("dd-MM-yyyy");
                objRiskTreatmentPlan.TargetStartDate = Convert.ToDateTime(dr["TargetStartDate"]).ToString("dd-MM-yyyy");
                objRiskTreatmentPlan.TargetEndDate = Convert.ToDateTime(dr["TargetEndDate"]).ToString("dd-MM-yyyy");
                objRiskTreatmentPlan.ActualStartDate = Convert.ToDateTime(dr["ActualStartDate"]).ToString("dd-MM-yyyy");
                objRiskTreatmentPlan.ActualEndDate = Convert.ToDateTime(dr["ActualEndDate"]).ToString("dd-MM-yyyy");
                objRiskTreatmentPlan.PercentageCompletion = Convert.ToInt32(dr["PercentageCompletion"]);
                objRiskTreatmentPlan.risksubId = Convert.ToInt32(dr["risksubId"]);
                objRiskTreatmentPlan.CreatedBy = Convert.ToInt32(dr["CreatedBy"]);
                objRiskTreatmentPlan.UpdatedBy = Convert.ToString(dr["UpdatedBy"]);
                objRiskTreatmentPlan.IsActive = Convert.ToInt32(dr["IsActive"]);
                objRiskTreatmentPlan.IsEffective = dr["IsEffective"].ToString();
                objRiskTreatmentPlan.TreatmentStatus = Convert.ToInt32(dr["TreatmentStatus"].ToString());
                objRiskTreatmentPlan.TreatmentCode = dr["TreatmentCode"].ToString();
                objRiskTreatmentPlan.Remark = dr["Remark"].ToString();
                objRiskTreatmentPlan.OwnerName = dr["OwnerName"].ToString();
                //objRiskTreatmentPlan.RevStartDate = Convert.ToDateTime(dr["RevStartDate"]).Date;

                if (dr["RevStartDate"] == DBNull.Value || string.IsNullOrWhiteSpace(dr["RevStartDate"]?.ToString()))
                {
                    objRiskTreatmentPlan.RevStartDate = DateTime.MinValue; // or DateTime.Now, or any default you prefer
                }
                else
                {
                    objRiskTreatmentPlan.RevStartDate = Convert.ToDateTime(dr["RevStartDate"]).Date;
                }

                if (dr["RevStartDate"] == DBNull.Value || string.IsNullOrWhiteSpace(dr["RevStartDate"]?.ToString()))
                {
                    objRiskTreatmentPlan.RevEndDate = DateTime.MinValue; // or DateTime.Now, or any default you prefer
                }
                else
                {
                    objRiskTreatmentPlan.RevEndDate = Convert.ToDateTime(dr["RevStartDate"]).Date;
                }
                //objRiskTreatmentPlan.RevEndDate = Convert.ToDateTime(dr["RevEndDate"]);
                objRiskTreatmentPlan.ReasonForUpdate = dr["ReasonForUpdate"].ToString();
                objRiskTreatmentPlan.IsEffectivePlan = dr["IsEffectivePlan"].ToString();
                lstRiskTreatmentPlan.Add(objRiskTreatmentPlan);
            }

            ViewBag.RiskTreatmentPlan = lstRiskTreatmentPlan;
            objRiskManagement = _ProcessSrv.getAllBCMRiskList("0", "0", "0", "0", Convert.ToString(iRiskID), "1", "0", "0", "0", string.Empty, string.Empty);
            objRiskManagement.RiskID = iRiskID;
            HttpContext.Session.SetString("SubRiskRegID",objRiskManagement.SubRiskRegID.ToString());
            string subid= HttpContext.Session.GetString("SubRiskRegID");
            objRiskManagement.CloseDate = objRiskManagement.CloseDate == DateTime.MinValue ? DateTime.Now : objRiskManagement.CloseDate;
            List<RecoveryPlan> lstRecoveryPlan = _ProcessSrv.GetAllRecoveyPlanByID(Convert.ToInt32(objRiskManagement.PlanID));
            ViewBag.RecoveryPlan = lstRecoveryPlan;

            List<EntityReview> lstEntityReview = new List<EntityReview>();
            lstEntityReview = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.RiskManagement).ToString(), Convert.ToString(iRiskID));
            ViewBag.EntityReview = lstEntityReview;
            lstEntityReview = lstEntityReview.OrderBy(o => o.Status).Take(3).ToList();
            List<EntityReview> EntityReview = lstEntityReview.Where(x => x.Status.ToString() == "1").ToList();
            if (EntityReview.Count > 0)
            {
                ViewBag.startReview = "1";
            }
            else
            {
                ViewBag.startReview = "0";
            }

            //if (lstEntityReview.Count > 0)
            //{
            //    ViewBag.startReview = "1";
            //}
            //else
            //{
            //    ViewBag.startReview = "0";
            //}
            ViewBag.LoggedInUserId = _UserDetails.UserID;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }


        return View(objRiskManagement);
    }



    [HttpPost]
    public JsonResult EndReviewSection(string EntityID, string RecordID, string NextReviewDate)
    {
        bool Success = false;
        try
        {


            EntityReview objEntityReview = new EntityReview();
            objEntityReview.EntityID = Convert.ToInt32(EntityID);
            objEntityReview.RecordID = Convert.ToInt32(RecordID);
            objEntityReview.NextReviewDate = Convert.ToDateTime(NextReviewDate);
            objEntityReview.ReviewerID = _UserDetails.UserID;

            if (true)
            {
                GetUpdateReviewStatusofRACycle(objEntityReview);
                Success = true;
            }

            bool SuccessUpdated = false;

            if (Success)
            {

                List<EscalationMatrix> lstEscalationMatrix = _ProcessSrv.EscalationMatrixMapping_GetAll(1);

                if (lstEscalationMatrix != null)
                {
                    int iID = string.IsNullOrEmpty(Convert.ToString(EntityID)) ? 0 : Convert.ToInt32(RecordID);

                    var EscDetails = from EscalationMatrix objEsc1 in lstEscalationMatrix
                                     where
                                    Convert.ToInt32(objEsc1.EscMatType) == (int)BCPEnum.EscalationMatrixType.Review
                                     &&
                                    Convert.ToInt32(objEsc1.BCMEntityType) == (string.IsNullOrEmpty(Convert.ToString(EntityID)) ? 0 : Convert.ToInt32(EntityID))
                                     &&
                                    Convert.ToInt32(objEsc1.BCMEntityID) == Convert.ToInt32(RecordID)
                                     select objEsc1;


                    if (EscDetails.Any())
                    {
                        List<EscalationMatrix> lstEscalationMatrixs = EscDetails.ToList();

                        foreach (EscalationMatrix item in lstEscalationMatrixs)
                        {
                            objEntityReview.EscalationExist = "1";
                            objEntityReview.EscalationMatrixID = item.EscMatID;
                            objEntityReview.EscalationMapID = item.EscMapID;
                            objEntityReview.RecordID = Convert.ToInt32(RecordID);
                            objEntityReview.EscalationTime = item.EscTime;
                            objEntityReview.EscalationTimeUnit = item.EscTimeUnit;
                        }

                        if (!string.IsNullOrEmpty(Convert.ToString(objEntityReview.EscalationMapID)))
                        {
                            EscalationMatrix objEscalationMatrix2 = new EscalationMatrix();
                            objEscalationMatrix2.EscMapID = objEntityReview.EscalationMapID;
                            objEscalationMatrix2.UpdatedBy = _UserDetails.UserID;

                            SuccessUpdated = _ProcessSrv.EscalationMatrixMapping_Update_IsReviewed(objEscalationMatrix2);
                        }
                    }
                }

                if ((string.IsNullOrEmpty(objEntityReview.EscalationExist) ? 0 : Convert.ToInt32(objEntityReview.EscalationExist)) == 1)
                {
                    if (SuccessUpdated)
                    {
                        EscalationMatrix EscalationMatrix3 = new EscalationMatrix();

                        EscalationMatrix3.BCMEntityType = string.IsNullOrEmpty(Convert.ToString(EntityID)) ? "0" : Convert.ToString(EntityID);
                        EscalationMatrix3.BCMEntityID = string.IsNullOrEmpty(Convert.ToString(RecordID)) ? 0 : Convert.ToInt32(RecordID);
                        EscalationMatrix3.EscMatID = string.IsNullOrEmpty(Convert.ToString(objEntityReview.EscalationMatrixID)) ? 0 : objEntityReview.EscalationMatrixID;
                        EscalationMatrix3.IsActive = 1;
                        EscalationMatrix3.CreatedBy = _UserDetails.UserID;
                        EscalationMatrix3.EscMatType = ((int)BCPEnum.EscalationMatrixType.Review).ToString();
                        EscalationMatrix3.EscStartDate = Convert.ToDateTime(NextReviewDate);
                        EscalationMatrix3.EscTime = objEntityReview.EscalationTime;
                        EscalationMatrix3.EscTimeUnit = objEntityReview.EscalationTimeUnit;

                        int ires = _ProcessSrv.EscalationMatrixMapping_Save(EscalationMatrix3);

                        List<EscalationLevelConfig> lstEscalationLevelConfig = new List<EscalationLevelConfig>();
                        List<EscalationLevel> lstEscalationLevel = new List<EscalationLevel>();

                        lstEscalationLevelConfig = _ProcessSrv.EscalationLevelConfig_GetAllByMatrixID(string.IsNullOrEmpty(Convert.ToString(objEntityReview.EscalationMatrixID)) ? 0 : Convert.ToInt32(objEntityReview.EscalationMatrixID));
                        lstEscalationLevel = _ProcessSrv.EscalationLevel_GetAllByEscMatID(Convert.ToInt32(string.IsNullOrEmpty(Convert.ToString(objEntityReview.EscalationMatrixID)) ? 0 : Convert.ToInt32(objEntityReview.EscalationMatrixID)));

                        Save_DependentDataOfMatrixProfile(ires, lstEscalationLevelConfig, lstEscalationLevel, Convert.ToDateTime(NextReviewDate));

                        EscalationMatrix objEscalationMatrix4 = new EscalationMatrix();

                        List<EscalationMatrixFyi> lstEscalationMatrixFyi = _ProcessSrv.EscalationMatrixFyi_GetByMapID(Convert.ToInt32(objEntityReview.EscalationMapID));
                        if (lstEscalationMatrixFyi != null)
                        {
                            foreach (EscalationMatrixFyi item in lstEscalationMatrixFyi)
                            {
                                item.CreatedBy = _UserDetails.UserID;
                                item.EscMapID = ires;
                            }

                            bool Successs = _ProcessSrv.EscalationMatFyi_SaveColl(lstEscalationMatrixFyi);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
          
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = Success, message = Success ? "Review Ended." : "Failed to Start Review. Please try again." });
        }
        return Json(Success);
    }

    private void GetUpdateReviewStatusofRACycle(EntityReview entityReview)
    {
        try
        {
            EntityReview objEntityReview = new EntityReview();

            objEntityReview.EntityID = entityReview.EntityID;
            objEntityReview.RecordID = entityReview.RecordID;
            objEntityReview.Status = "2";
            objEntityReview.NextReviewDate = entityReview.NextReviewDate;
            objEntityReview.ChangedBy = _UserDetails.UserID;

            _ProcessSrv.EntityReviewUpdateStatus(objEntityReview);
        }
        catch (Exception ex)
        {
            
        }
    }

    public void Save_DependentDataOfMatrixProfile(int iEscMatMapID, List<EscalationLevelConfig> lstEscalationLevelConfig, List<EscalationLevel> lstEscalationLevel, DateTime dateTime)
    {
        try
        {
            EntityReview objEntityReview = new EntityReview();

            int iLevelsSaved = 0;
            int iLevRes = 0;
            int iLevConRes = 0;

            //Method to Save Levels & resources for Approval & Review
            foreach (EscalationLevelConfig levConfig in lstEscalationLevelConfig)
            {
                levConfig.EscMatMapID = Convert.ToInt32(iEscMatMapID);

                levConfig.EscLevStatus = "1";
                levConfig.CreatedBy = _UserDetails.UserID;
                int count = 0;
                string EscalatedOndate = string.Empty;

                DateTime EscalatedOndate_new = AddtoDate(Convert.ToDateTime(dateTime.ToString()), Convert.ToInt32(levConfig.EscTime), Convert.ToInt32(levConfig.TimeUnit));

                levConfig.LevEscDate = EscalatedOndate_new.ToString();
                levConfig.IsApproved = 1;
                levConfig.IncidentStepID = 0;
                levConfig.IsLevelNotified = 0;
                iLevConRes = _ProcessSrv.EscalationLevConfigResult_Save(levConfig);

                var varEscLevelsList = from EscalationLevel levels in lstEscalationLevel
                                       where Convert.ToInt32(levels.EscLevConfigID) == Convert.ToInt32(levConfig.LevConID)
                                       select levels;

                foreach (EscalationLevel varEscLev in varEscLevelsList)
                {

                    varEscLev.EscLevConfigID = Convert.ToInt32(iLevConRes);
                    varEscLev.CreatedBy = _UserDetails.UserID;
                    varEscLev.IsApproved = 1;

                    if ((Convert.ToString(varEscLev.EntityID) == "1"))
                    {
                        varEscLev.FromTeamID = "-1";
                        iLevRes = _ProcessSrv.EscalationLevelsResult_Save(varEscLev);
                    }
                    else
                    {
                        int iTeamResid = 0;
                        while (count.Equals(0))
                        {
                            varEscLev.FromTeamID = "-1";
                            iTeamResid = Convert.ToInt32(varEscLev.ResourceID);

                            int iLevResTeam = _ProcessSrv.EscalationLevelsResult_Save(varEscLev);
                            count++;
                        }

                        varEscLev.FromTeamID = iTeamResid.ToString();

                        List<EscalationLevel> lstEscalLevel = new List<EscalationLevel>();
                        lstEscalLevel = _ProcessSrv.EscalationLevel_GetAllTeamMembers(iTeamResid);

                        varEscLev.EscLevConfigID = Convert.ToInt32(iLevConRes);
                        varEscLev.CreatedBy = _UserDetails.UserID;

                        foreach (EscalationLevel esclevelteam in lstEscalLevel)
                        {
                            varEscLev.EntityID = 1;
                            varEscLev.ResourceID = esclevelteam.ResourceID;
                            iLevelsSaved = _ProcessSrv.EscalationLevelsResult_Save(varEscLev);
                        }
                    }
                }

            }
        }
        catch (Exception ex)
        {
        
        }
    }

    public DateTime AddtoDate(DateTime dfinalDate, int iEndTime, int EUnit)
    {
        DateTime finalDate = Convert.ToDateTime("01/01/1990 00:00:00");
        if (icount == 0)
        {
            finalDate = dfinalDate;
            iETime = iEndTime;
            iEUnit = EUnit;

            icount++;
        }
        else
        {
            if (iEUnit.Equals(Convert.ToInt32("1")))
            {
                finalDate = dvalue.AddMinutes(Convert.ToInt32(iETime));
            }
            else if (iEUnit.Equals(Convert.ToInt32("2")))
            {
                finalDate = dvalue.AddHours(Convert.ToInt32(iETime));
            }
            else if (iEUnit.Equals("3"))
            {
                finalDate = dvalue.AddDays(Convert.ToInt32(iETime));
            }
        }

        dvalue = finalDate;
        iETime = iEndTime;
        iEUnit = EUnit;
        return finalDate;
    }

    public ActionResult ReviweHistoryDetails(int RecordID, int EntityTypeID)
    {
        List<EntityReview> lstEntityHistory = new List<EntityReview>();
        try
        {
            lstEntityHistory = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(EntityTypeID.ToString(), RecordID.ToString());
        }
        catch (Exception ex)
        {
            //_CvLogger.LogErrorApp(ex);
        }

        return PartialView("ReviewHistoryDetails", lstEntityHistory);
    }

    [HttpPost]
    public JsonResult StartReviewSection(string EntityID, string RecordID, string NextReviewDate)
    {
        EntityReview objEntityReviews = new EntityReview();
        bool isSuccess = false;
        try
        {
            objEntityReviews.EntityID = Convert.ToInt32(EntityID);
            objEntityReviews.RecordID = Convert.ToInt32(RecordID);
            objEntityReviews.NextReviewDate = Convert.ToDateTime(NextReviewDate);
            objEntityReviews.ReviewerID = _UserDetails.UserID;
            isSuccess = _ProcessSrv.EntityReviewSave(objEntityReviews);
        }
        catch (Exception ex)
        {
            //_CvLogger.LogErrorApp(ex);

        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = isSuccess, message = isSuccess ? "Review Started" : "Failed to Start Review. Please try again." });
        }
        return Json(isSuccess);
        //ReviewSection();
    }

    [HttpGet]
    public IActionResult GetTreatmentPlan(int iSubRiskID)
    {
        RiskTreatmentPlan objRiskTreatmentPlan = new RiskTreatmentPlan();

        DataTable dtTreatmentplan = _ProcessSrv.TreatmentPlanByRiskSubID(iSubRiskID, 1);
        List<RiskTreatmentPlan> lstRiskTreatmentPlan = new List<RiskTreatmentPlan>();
        foreach (DataRow dr in dtTreatmentplan.Rows)
        {
            objRiskTreatmentPlan.Id = Convert.ToInt32(dr["Id"]);
            objRiskTreatmentPlan.PlanName = dr["PlanName"].ToString();
            objRiskTreatmentPlan.Owner = Convert.ToInt32(dr["IsActive"]);
            objRiskTreatmentPlan.IdentifiedDate = Convert.ToDateTime(dr["IdentifiedDate"]).ToString("dd-MM-yyyy");
            objRiskTreatmentPlan.TargetStartDate = Convert.ToDateTime(dr["TargetStartDate"]).ToString("dd-MM-yyyy");
            objRiskTreatmentPlan.TargetEndDate = Convert.ToDateTime(dr["TargetEndDate"]).ToString("dd-MM-yyyy");
            objRiskTreatmentPlan.ActualStartDate = Convert.ToDateTime(dr["ActualStartDate"]).ToString("dd-MM-yyyy");
            objRiskTreatmentPlan.ActualEndDate = Convert.ToDateTime(dr["ActualEndDate"]).ToString("dd-MM-yyyy");
            objRiskTreatmentPlan.PercentageCompletion = Convert.ToInt32(dr["PercentageCompletion"]);
            objRiskTreatmentPlan.risksubId = Convert.ToInt32(dr["risksubId"]);
            objRiskTreatmentPlan.CreatedBy = Convert.ToInt32(dr["CreatedBy"]);
            objRiskTreatmentPlan.UpdatedBy = Convert.ToString(dr["UpdatedBy"]);
            objRiskTreatmentPlan.IsActive = Convert.ToInt32(dr["IsActive"]);
            objRiskTreatmentPlan.IsEffective = dr["IsEffective"].ToString();
            objRiskTreatmentPlan.TreatmentStatus = Convert.ToInt32(dr["TreatmentStatus"].ToString());
            objRiskTreatmentPlan.TreatmentCode = dr["TreatmentCode"].ToString();
            objRiskTreatmentPlan.Remark = dr["Remark"].ToString();
            objRiskTreatmentPlan.OwnerName = dr["OwnerName"].ToString();
            objRiskTreatmentPlan.RevStartDate = Convert.ToDateTime(dr["RevStartDate"]).Date;
            objRiskTreatmentPlan.RevEndDate = Convert.ToDateTime(dr["RevEndDate"]);
            objRiskTreatmentPlan.ReasonForUpdate = dr["ReasonForUpdate"].ToString();
            objRiskTreatmentPlan.IsEffectivePlan = dr["IsEffectivePlan"].ToString();
            lstRiskTreatmentPlan.Add(objRiskTreatmentPlan);
        }

        ViewBag.RiskTreatmentPlan = lstRiskTreatmentPlan;
        return View(objRiskTreatmentPlan);
    }

    [HttpGet]
    public IActionResult SaveTreatmentPlanByRiskSubID(int iSubRiskId,int iIsEffective, int iPlanID)
    {
        RiskTreatmentPlan objRiskTreatmentPlan = new RiskTreatmentPlan();

        bool success = _ProcessSrv.DeleteRiskTreatmentMap(iSubRiskId);

        bool mapid = _ProcessSrv.SaveRiskTreatmentMap(iSubRiskId, iIsEffective, iPlanID);

        string? strRiskID = HttpContext.Session.GetString("IRiskID");

        return RedirectToAction("EditRiskAssessmentProcessForm", new
        {
            iRiskID = HttpContext.Session.GetString("riskCode"),
            iSubRiskId = HttpContext.Session.GetString("SubriskCode")
        });
    }


    [HttpGet]
    public IActionResult SaveTreatmentPlan()
    {
        RiskTreatmentPlan objRiskTreatmentPlan = new RiskTreatmentPlan();

        // int Success = _ProcessSrv.RiskTreatmentPlanSave(Entity);
        //if (Success > 0)
        //{
        //    bool MapID = _ProcessSrv.SaveRiskTreatmentMap(Convert.ToInt32(lblRiskSubID.Text), Success, 0);
        //}

        return View(objRiskTreatmentPlan);
        //return PartialView("_RiskTreatmentPlan");
    }

    [HttpPost]
    public IActionResult SaveTreatmentPlanSave(RiskTreatmentPlan objRiskTreatmentPlan)
    {
        int Success = _ProcessSrv.RiskTreatmentPlanSave(objRiskTreatmentPlan);
        if (Success > 0)
        {
            int risksubregisterID = Convert.ToInt32(HttpContext.Session.GetString("SubRiskRegID"));
            bool MapID = _ProcessSrv.SaveRiskTreatmentMap(Convert.ToInt32(risksubregisterID),1, Success );

            ViewBag.RiskChampion = _Utilities.GetResourcesListForDDL(0);

            

        }
        return RedirectToAction("EditRiskAssessmentProcessForm", new
        {
            iRiskID = HttpContext.Session.GetString("riskCode"),
            iSubRiskId = HttpContext.Session.GetString("SubriskCode")
        });




        //return View("EditRiskAssessmentProcessForm", Convert.ToInt32(@HttpContext.Session.GetInt32("riskCode").ToString()), Convert.ToInt32(@HttpContext.Session.GetInt32("SubriskCode").ToString()));





    }

    [HttpGet]
    public IActionResult RecoveryPlan(int id, int iRiskID)
    {
        //RiskManagement objRiskManagement = new RiskManagement();
        try
        {
           // objRiskManagement = _ProcessSrv.getAllBCMRiskList("0", "0", "0", "0", Convert.ToString(iRiskID), "1", "0", "0", "0", string.Empty, string.Empty);

            List<RecoveryPlan> lstRecoveryPlan = _ProcessSrv.GetAllRecoveyPlanByID(id);
            ViewBag.RecoveryPlan = lstRecoveryPlan;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_RiskTreatmentPlan");
    }

    //[HttpGet]
    //public IActionResult GetRiskTreatmentPlan(int iSubRiskId)
    //{
    //    // Fetch the treatment plans for the given sub risk ID
    //    var plans = _ProcessSrv.TreatmentPlanByRiskSubID(iSubRiskId, 1)
    //        .AsEnumerable()
    //        .Select(dr => new RiskTreatmentPlan
    //        {
    //            // Map DataRow to RiskTreatmentPlan properties
    //            Id = Convert.ToInt32(dr["Id"]),
    //            PlanName = dr["PlanName"].ToString(),
    //            // ... map other properties as needed
    //        }).ToList();
    //    ViewBag.RiskChampion = _Utilities.GetResourcesListForDDL(0);
    //    ViewBag.RiskTreatmentPlan = plans;
    //    return PartialView("_RiskTreatmentPlan", iSubRiskId);
    //}


    [HttpPost]
    public IActionResult EditRiskAssessmentProcessForm(RiskManagement objRiskManagement)
    {
        try
        {
            string? strRiskID = HttpContext.Session.GetString("IRiskID");

            if (strRiskID.Contains("-1"))
            {
                RiskManagement objRiskManagementEntity1 = new RiskManagement();
                objRiskManagementEntity1 = Build_RiskEntity_RiskRegister(objRiskManagement);
                objRiskManagementEntity1.RiskTrendStatus = 0;

                if (Convert.ToString(objRiskManagementEntity1.PlanID) == "0")
                {
                    objRiskManagementEntity1.IncidentDisplayName = string.Empty;
                }

                if (objRiskManagementEntity1 != null)
                {
                    objRiskManagementEntity1.RiskTrendStatus = 0;
                    bool iRiskID = _ProcessSrv.RiskAssessmentProcessFormSave(objRiskManagementEntity1, ref strRiskID);
                }
            }
            else
            {
                RiskManagement objRiskManagementEntity2 = new RiskManagement();
                objRiskManagementEntity2 = Build_RiskEntity_RiskRegister(objRiskManagement);
                objRiskManagementEntity2.RiskID = Convert.ToInt32(objRiskManagement.RiskID);

                int iPrevResiRating = 0;
                int iCurResiRating = 0;

                iCurResiRating = Convert.ToInt32(objRiskManagement.ResidualLikeliHood) * Convert.ToInt32(objRiskManagement.ResidualImpact);

                if (Convert.ToBoolean(objRiskManagement.Version) == true)
                {
                    int TrendSts = CalculateRiskTrendStatus(iPrevResiRating, iCurResiRating, Convert.ToString(objRiskManagementEntity2.RiskID));
                    objRiskManagementEntity2.RiskTrendStatus = TrendSts;
                }

                if (objRiskManagementEntity2 != null)
                {
                    bool isRiskAssessmentProcess = false;
                    int iiD = 0;
                    if (Convert.ToString(objRiskManagementEntity2.PlanID) == "0")
                    {
                        objRiskManagementEntity2.IncidentDisplayName = string.Empty;
                    }
                    iiD = _ProcessSrv.RiskAssessmentProcessFormUpdate(objRiskManagementEntity2);
                    if (iiD > 0)
                    { isRiskAssessmentProcess = true; }
                    else
                    { isRiskAssessmentProcess = false; }
                }
            }

            int iIsClosed = Convert.ToBoolean(objRiskManagement.RiskCloseStatus) ? 1 : 0;
            _ProcessSrv.RiskProcessFormUpdateCloseDateByRiskId(Convert.ToString(objRiskManagement.LastReviewDate), strRiskID, iIsClosed);

            RiskManagement objRiskManagementEntity = new RiskManagement();
            objRiskManagementEntity = Build_RiskEntity_RiskSubRegister(objRiskManagement, strRiskID);

            if (objRiskManagementEntity != null)
            {
                bool success = false;
                int iID = 0;
                // Fix for the problematic line causing multiple errors
                // Original line:
                // if (objRiskManagementEntity.Version != "" || objRiskManagementEntity.Version => "1.0")

                // Corrected line:  
                if (Version.TryParse(objRiskManagementEntity.Version, out var ver) && ver > new Version(1, 0))
                //if (objRiskManagementEntity.Version != "" || objRiskManagementEntity.Version => "1.0")
                {
                    iID = _ProcessSrv.RiskAssessmentSubRegisterProcessFormSave_1(objRiskManagementEntity);
                    if (iID > 0)
                    {
                        success = true;

                        DataTable dtTreatmentPlan = _ProcessSrv.TreatmentPlanByRiskSubID(Convert.ToInt32(iID));

                        if (dtTreatmentPlan.Rows.Count >= 0)
                        {
                            foreach (DataRow dr in dtTreatmentPlan.Rows)
                            {
                                int iRiskSubID = Convert.ToInt32(iID);
                                int iPlanID = Convert.ToInt32(dr["ID"]);
                                int iIsEffective = Convert.ToInt32(dr["IsEffectivePlan"]);
                                bool MapID = _ProcessSrv.SaveRiskTreatmentMap(iRiskSubID, iPlanID, iIsEffective);
                            }
                        }
                    }
                }
                else
                {
                    success = _ProcessSrv.RiskAssessmentSubRegisterProcessForm_Update(objRiskManagementEntity);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageRisk");
    }
    #endregion

    #region Delete Risk Assessment
    [HttpGet]
    public IActionResult DeleteAttachedPlanRiskRegister(int iRiskId,string strRiskName)
    {
        RiskManagement objRiskManagement = new RiskManagement();
        try
        {
            if (iRiskId > 0)
            {
                objRiskManagement = _ProcessSrv.getAllBCMRiskList("0", "0", "0", "0", Convert.ToString(iRiskId), "1", "0", "0", "0", string.Empty, string.Empty);                
            }            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        HttpContext.Session.SetInt32("IRiskID", iRiskId);
        return PartialView("_DeleteRiskAssessmentProcess", objRiskManagement);
    }

    [HttpPost]
    public IActionResult DeleteAttachedPlanRiskRegister(RiskManagement objRiskManagement)
    {
        bool Success = false;
        try
        {
            if(objRiskManagement.ID > 0)
            {
                Success = _ProcessSrv.Delete_AttachedPlanRiskRegister(objRiskManagement.ID);
            }            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageRisk");
    }
    #endregion

    #region Send For Approval
    public JsonResult SendForApproval(int iRiskId)
    {
        bool isSucess = false;
        string strAttachment = string.Empty;
        string? strAppLink = "Applink";
        try
        {
            isSucess = CreateAndSendForApproval(Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval), Convert.ToString(iRiskId), strAppLink, Convert.ToString(_UserDetails.OrgID), strAttachment);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return Json(isSucess);
    }
    public bool CreateAndSendForApproval(int strStatusId, string strRiskId, string strAppLink, string strOrgID, string strAttachment, string strRemark = "")
    {
        string? strApproverName = string.Empty;
        string? strBody = string.Empty;
        string? strsubjectline = string.Empty;
        string strcompanyEmail = string.Empty;
        int iiD = 0;

        List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
        try
        {
            lstRiskManagement = _ProcessSrv.getBCMRiskList("0", "0", "0", "0", strRiskId, "1", "0", "0", "0", "0", string.Empty);
            foreach (RiskManagement RiskItem in lstRiskManagement)
            {
                RiskItem.Status = strStatusId;
                RiskItem.RiskID = Convert.ToInt32(strRiskId);
                iiD = _ProcessSrv.RiskAssessmentProcessFormUpdate(RiskItem);

                ResourcesInfo objChampionResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(RiskItem.RiskChampionID));
                ResourcesInfo objOwnerResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(RiskItem.RiskOwnerID));

                if (objOwnerResource != null)
                {
                    strBody = GetMailBody(objChampionResource.ResourceName, strRiskId, Convert.ToString(RiskItem.RiskChampionID), BCPEnum.EntityType.RiskAssessment.ToString(), strStatusId,
                        RiskItem.ProcessName, objOwnerResource.ResourceName, Convert.ToString(RiskItem.RiskOwnerID), strAppLink, strOrgID, strRemark);
                }

                if (strStatusId.Equals((int)BCPEnum.ApprovalType.WaitingforApproval))
                {
                    strsubjectline = " for Approval : ";
                }
                else if (strStatusId.Equals((int)BCPEnum.ApprovalType.Approved))
                {
                    strsubjectline = " Approved : ";
                }
                else if (strStatusId.Equals((int)BCPEnum.ApprovalType.Disapproved))
                {
                    strsubjectline = " Disapproved : ";
                }

                //string Approverid = RiskItem.RiskChampionID.ToString();
                if (strStatusId.Equals((int)BCPEnum.ApprovalType.Approved) || strStatusId.Equals((int)BCPEnum.ApprovalType.Disapproved))
                {
                    strcompanyEmail = objChampionResource.CompanyEmail;
                    strAttachment = string.Empty;
                    _BCMMail.SendMail(strsubjectline + "'" + RiskItem.ProcessName + "'", strBody, strcompanyEmail, "", "", strAttachment, strOrgID);

                }
                else
                {
                    strcompanyEmail = objOwnerResource.CompanyEmail;
                    _BCMMail.SendMail(strsubjectline + "'" + RiskItem.ProcessName + "'", strBody, strcompanyEmail, "", "", strAttachment, strOrgID);

                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return true;
    }
    public string GetMailBody(string strOwnerName, string strRiskId, string strOwnerId, string strRiskAssessmentId, int strStatusId, string strProcessName, string strApproverName, string strApproverId, string strAppLink, string strOrgID, string strRemark)
    {
        string strMailBody = string.Empty;
        string strApp_link = string.Empty;
        try
        {
            strAppLink = strAppLink + @"/BCMRiskAssessment/ApproveRiskAssement.aspx";
            string strApproverlink = strAppLink;
            //string Approverlink = HttpContext.Current.Server.HtmlEncode(AppLink + "?RiskId=" + BCP.Security.CryptographyHelper.BCPEncrypt(RiskId) +
            //    "&UserID=" + BCP.Security.CryptographyHelper.BCPEncrypt(ApproverId) + "&OrgID=" + BCP.Security.CryptographyHelper.BCPEncrypt(OrgID) +
            //    "&Usr_Name=" + BCP.Security.CryptographyHelper.BCPEncrypt(ApproverName) + "&Usr_ID=" + BCP.Security.CryptographyHelper.BCPEncrypt(ApproverId) +
            //    "&App_Link=" + BCP.Security.CryptographyHelper.BCPEncrypt(app_link));

            if (strStatusId.Equals(((int)BCPEnum.ApprovalType.WaitingforApproval)))
            {
                strMailBody = "Dear " + strApproverName + ",<br /><br />Please <a href='" + strApproverlink + "'>Click here</a> to Approve or Disapprove " + "<b>" + "'" + strProcessName + "'" + "</b>" + " Risk." + "<br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
            }
            else if (strStatusId.Equals(((int)BCPEnum.ApprovalType.Approved)))
            {
                strMailBody = "Dear " + strOwnerName + ",<br /><br />" + "<b>" + "'" + strProcessName + "'" + "</b>" + " Risk is Approved by " + "<b>" + strApproverName + "</b>" + "." + "<br /><br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
            }
            else if (strStatusId.Equals(((int)BCPEnum.ApprovalType.Disapproved)))
            {
                strMailBody = "Dear " + strOwnerName + ",<br /><br />" + "<b>" + "'" + strProcessName + "'" + "</b>" + " Risk has been Disapproved by " + "<b>" + strApproverName + "</b>" + ". <br /><br /> Remark - " + strRemark + "<br /><br />Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return strMailBody;

    }
    #endregion

    #region Approval
    public JsonResult Approved(int iRiskId)
    {
        string strAttachment = string.Empty;
        string? strAppLink = "Applink";
        bool bSucess = CreateAndSendForApproval(Convert.ToInt32(BCPEnum.ApprovalType.Approved), Convert.ToString(iRiskId), strAppLink, Convert.ToString(_UserDetails.OrgID), strAttachment);

        List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
        lstRiskManagement = _ProcessSrv.getBCMRiskList("0", "0", "0", "0", Convert.ToString(iRiskId), "1", "0", "0", "0", "0", string.Empty);

        foreach (RiskManagement item in lstRiskManagement)
        {
            AddPCIScore(Convert.ToString(item.ProcessID), ((int)BCPEnum.PCIScore.ProcessRADone).ToString(), "1", Convert.ToString(_UserDetails.OrgID), Convert.ToString(_UserDetails.OrgID));
            AddPCIScoreSummary(Convert.ToString(item.ProcessID), "0", ((int)BCPEnum.PCIScore.ProcessRADone).ToString(), GetPCIScoreWeightage(((int)BCPEnum.PCIScore.ProcessRADone)), GetPCIScoreWeightage(((int)BCPEnum.PCIScore.ProcessRADone)));
        }
        return Json(null);
    }
    public bool AddPCIScore(string strProcessID, string PCIScoreID, string IsActive, string stCreatedBy, string strChangedBy)
    {
        return _ProcessSrv.BusinessProcessPCISaveAndUpdate(new BusinessProcessPCI { ProcessID = strProcessID, IsActive = IsActive, PCIScoreID = PCIScoreID, CreatedBy = stCreatedBy, ChangedBy = strChangedBy });
    }
    public bool AddPCIScoreSummary(string strProcessID, string strRecordID, string strPCIScoreID, string strAssignedWeightage, string strActualWeightage)
    {
        return _ProcessSrv.PCIScoreSummarySave(new BusinessProcessPCI { ProcessID = strProcessID, RecordID = strRecordID, PCIScoreID = strPCIScoreID, AssignedWeightage = strAssignedWeightage, ActualWeightage = strActualWeightage, CreatedBy = "0" });
    }
    public string GetPCIScoreWeightage(int iID)
    {
        BusinessProcessPCI objBusinessProcessPCI = _ProcessSrv.BusinessProcessPCIScoreGetByID(iID);
        return objBusinessProcessPCI.Weightage = (Convert.ToDouble(objBusinessProcessPCI.Weightage) * 100).ToString();
    }
    #endregion

    #region Disapproved
    public JsonResult Disapproved(int iRiskID)
    {
        int action = (int)BCPEnum.ApprovalType.Disapproved;
        bool bSuccess = SaveEntity(iRiskID, action);
        return Json(bSuccess);
    }
    public bool SaveEntity(int iRiskID, int iAction)
    {
        bool bSuccess = false;
        try
        {
            if (iAction == (int)BCPEnum.ApprovalType.Disapproved)
            {
                List<RiskManagement> lstRiskManagement = new List<RiskManagement>();
                lstRiskManagement = _ProcessSrv.getBCMRiskList("0", "0", "0", "0", Convert.ToString(iRiskID), "1", "0", "0", "0", "0", string.Empty);
                foreach (RiskManagement RiskItem in lstRiskManagement)
                {
                    RiskItem.Status = (int)BCPEnum.ApprovalType.Disapproved;
                    RiskItem.RiskID = Convert.ToInt32(iRiskID);
                    int iiD = _ProcessSrv.RiskAssessmentProcessFormUpdate(RiskItem);
                }
                BusinessProcessInfo objProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(iRiskID, 1);
                bSuccess = UpdateProcessStatus(Convert.ToInt32(iRiskID), Convert.ToInt32(BCPEnum.ApprovalType.Disapproved), Convert.ToInt32(BCPEnum.ApprovalType.Disapproved).ToString(), Convert.ToString(objProcessInfo.ProcessOwnerID), Convert.ToString(objProcessInfo.ApproverID), objProcessInfo.ProcessName, objProcessInfo.BCMEntityType);
            }
        }
        catch (Exception ex)
        {
            string a = ex.Message;
            _CVLogger.LogErrorApp(ex);
        }

        return bSuccess;
    }
    public bool UpdateProcessStatus(int iEntityId, int strStatusId, string strAction, string strProcessOwnerID, string strApproverID, string strprocessName, string strEntitytypename)
    {
        bool bSuccess = false;
        try
        {
            BIAApproval objBIAApproval = new BIAApproval();
            objBIAApproval.ProcessID = iEntityId;
            objBIAApproval.IsApproved = strStatusId;
            objBIAApproval.ApproverID = Convert.ToInt32(strApproverID);
            objBIAApproval.UserRole = Convert.ToInt32(_UserDetails.UserRoleID);
            objBIAApproval.ChangedBy = Convert.ToInt32(_UserDetails.UserID);
            bSuccess = _ProcessSrv.BIAApprovalUpdate(objBIAApproval);
            string? strFinalAction = string.Empty;
            if (strAction == "3")
            {
                strFinalAction = " DisApproved - ";
            }
            if (strAction == "2")
            {
                strFinalAction = " Approved - ";
            }

            if (bSuccess)
            {
                BusinessProcessInfo objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(iEntityId, Convert.ToInt32(BCPEnum.EntityType.BusinessProcess));

                string? strOwnerBody = GetOwnerMailBody(objBusinessProcessInfo.ProcessOwner, strprocessName, iEntityId, strAction, strEntitytypename);
                string strAltOwnerBody = GetOwnerMailBody(objBusinessProcessInfo.AltProcessOwner, strprocessName, iEntityId, strAction, strEntitytypename);

                _BCMMail.SendMailForRiskAssessment("Process  " + strFinalAction + ": " + strprocessName, strOwnerBody, objBusinessProcessInfo.OwnerEmail, "", "", "", Convert.ToString(_UserDetails.OrgID), "0", "", Convert.ToString(_UserDetails.UserID), strApproverID);
                _BCMMail.SendMailForRiskAssessment("Process  " + strFinalAction + ": " + strprocessName, strAltOwnerBody, objBusinessProcessInfo.AltProcessOwnerEmail, "", "", "", Convert.ToString(_UserDetails.OrgID), "0", "", Convert.ToString(_UserDetails.UserID), strApproverID);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return bSuccess;
    }
    protected string GetOwnerMailBody(string strResourceName, string strEntityName, int iEntityId, string strAction, string strBCMEntityTypename)
    {
        string strMailBody = string.Empty;
        try
        {
            strMailBody = "Dear " + strResourceName + ",<br /><br />  Process : " + strEntityName + " has been " + ((BCPEnum.ApprovalType)Convert.ToInt32(strAction)) + " by " + _UserDetails.UserName + " . <br /><br /> Remarks - " + "NA" + "   <br /><br /> Thank you.<br /><br /><br /><b>Admin</b><br />Continuity Vault";
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return strMailBody;
    }
    #endregion

    #region Review Section
    [HttpGet]
    public IActionResult ReviewSection(string strRecordID)
    {
        List<EntityReview> lstEntityReview = new List<EntityReview>();

        lstEntityReview = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.RiskManagement).ToString(), strRecordID);
        ViewBag.EntityReview = lstEntityReview;

        ViewBag.RecordID = strRecordID;
        ViewBag.EntityID = Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.RiskManagement).ToString();

        //bind next review date
        var strNextReviewDate = lstEntityReview
            .OrderByDescending(e => e.NextReviewDate)
            .Skip(1)
            .Select(e => e.NextReviewDate)
            .FirstOrDefault();
        if (Convert.ToDateTime(strNextReviewDate) == DateTime.MinValue)
        {
            var strNextReviewDates = lstEntityReview.Where(c => c.ID == c.ID).Select(c => c.NextReviewDate).FirstOrDefault();
            ViewBag.NextReviewDate = DateTime.Now;
        }
        else
        {
            ViewBag.NextReviewDate = strNextReviewDate;
        }

        return PartialView("~/Areas/BCMProcessBIA/Views/BusinessProcessForm/ReviewSection.cshtml", lstEntityReview);
    }
    #endregion

    #region Create new risk version
    [HttpGet]
    public IActionResult CreateNewRiskVersion(int EntityID)
    {
        HttpContext.Session.SetInt32("EntityID", EntityID);
        int ProcessID = 0;
        int IsBCMEntity = (int)BCPEnum.EntityType.RiskAssessment;
        RiskManagement riskManagement = new RiskManagement();
        BusinessProcessInfo objBusProInfo = new BusinessProcessInfo();
        BCMEntityInfo objBCMEntityInfo = new BCMEntityInfo();
        //string? Version = string.Empty; string? LatestVersion = string.Empty;

        if (IsBCMEntity == 0)
        {
            //chkIncludeBIAData.Visible = true;
            objBusProInfo = _ProcessSrv.GetBusinessProcessByProcessId(ProcessID, 1);

            if (objBusProInfo != null)
            {
                riskManagement.Version = string.IsNullOrEmpty(objBusProInfo.ProcessVersion) ? "1.0" : objBusProInfo.ProcessVersion;
                riskManagement.LatestVersion = _ProcessSrv.GetLatestProcessVersionByProcessCode(objBusProInfo.ProcessCode);
                //txtVersion.Text = string.Empty;
                //rbtnMinor.Checked = false;
                //rbtnMajor.Checked = false;
            }
        }
        else if (IsBCMEntity == 1)
        {
            //chkIncludeBIAData.Visible = true;
            objBCMEntityInfo = _ProcessSrv.GetBCMEntityByEntityID(ProcessID);

            if (objBCMEntityInfo != null)
            {
                riskManagement.Version = string.IsNullOrEmpty(objBCMEntityInfo.Version) ? "1.0" : objBCMEntityInfo.Version;
                riskManagement.LatestVersion = _ProcessSrv.GetLatestEntityVersionByEnitityCode(objBCMEntityInfo.BCMEntityCode);
            }
        }
        else if (IsBCMEntity == (int)BCPEnum.EntityType.RiskAssessment)
        {
            List<RiskManagement> riskListCol = new List<RiskManagement>();
            riskListCol = _ProcessSrv.getBCMRiskList("0", "0", "0", "0", EntityID.ToString(), "1", "0", "0", "0", string.Empty, string.Empty);
            foreach (RiskManagement RiskItem in riskListCol)
            {
                if (RiskItem != null)
                {
                    riskManagement.Version = string.IsNullOrEmpty(RiskItem.Version) ? "1.0" : RiskItem.Version;
                    RiskManagement latestversion = _ProcessSrv.GetLatestRiskVersionByRiskCode(RiskItem.RiskCode);
                    riskManagement.LatestVersion = latestversion.LatestVersion;
                    ViewBag.CurrentVersion = riskManagement.Version;
                    ViewBag.LatestVersion = riskManagement.LatestVersion;
                    //txtVersion.Text = string.Empty;
                    //rbtnMinor.Checked = false;
                    //rbtnMajor.Checked = false;
                }
            }

        }
        else if (IsBCMEntity == (int)BCPEnum.EntityType.BCMTraining)
        {
            BCMTrainingMaster TrainingMaster = new BCMTrainingMaster();
            TrainingMaster = _ProcessSrv.BCMTrainingMaster_getByID(Convert.ToInt32(ProcessID));
            riskManagement.Version = string.IsNullOrEmpty(TrainingMaster.Version) ? "1.0" : TrainingMaster.Version;
            riskManagement.LatestVersion = _ProcessSrv.GetLatestTrainingVersionByRiskCode(TrainingMaster.TrainingCode);
            //txtVersion.Text = string.Empty;
            //rbtnMinor.Checked = false;
            //rbtnMajor.Checked = false;
            //chkIncludeBIAData.Visible = false;
            riskManagement.IsBCMEntity = IsBCMEntity;
        }

        return PartialView("_CreateRiskNewVersion", riskManagement);
    }

    [HttpPost]
    public IActionResult CreateNewRiskVersion(RiskManagement riskManagement)
    {
        bool isSuccess = false;

        int? EntityID = HttpContext.Session.GetInt32("EntityID");

        RiskManagement riskManagement1 = new RiskManagement();

        int IsBCMEntity = (int)BCPEnum.EntityType.RiskAssessment;

        if (IsBCMEntity == (int)BCPEnum.EntityType.RiskAssessment)
        {
            isSuccess = RiskAssementVersion(EntityID, riskManagement.Version);
        }
        //Session["ChangedProcessID"] = iProcessID11;
        //Session["VersionChanged"] = txtVersion.Text;

        #region BIA Section Save
        //if (riskManagement == "")
        //{

        //    BIAActivityColl objBIAActivity = new BIAActivityColl();
        //    ProcessBIAPeopleInfoColl objBIAPeople = new ProcessBIAPeopleInfoColl();
        //    ProcessBIAApplicationColl objBIAApplication = new ProcessBIAApplicationColl();

        //    ProcessBIAFacilityColl objBIAFacility = new ProcessBIAFacilityColl();
        //    ProcessBIAThirdPartyColl objBIAThirdParty = new ProcessBIAThirdPartyColl();
        //    ProcessBIAImpactMatrixColl objBIAImpactMatrix = new ProcessBIAImpactMatrixColl();
        //    ProcessBIALegalAndRegulatoryColl objBIALegal = new ProcessBIALegalAndRegulatoryColl();

        //    BIAProfileMasterColl objBIAQualitative = new BIAProfileMasterColl();

        //    BIAProfileMasterColl objBIAQuantitative = new BIAProfileMasterColl();

        //    ApplicationDependencyColl objUpApplicationDependencyList = new ApplicationDependencyColl();
        //    ApplicationDependencyColl objDownApplicationDependencyList = new ApplicationDependencyColl();

        //    BIADefaultSectionInfoColl obBIADefaultSectionColl = new BIADefaultSectionInfoColl();

        //    BIADependentServicesColl objBIADependentServicesColl = new BIADependentServicesColl();
        //    BIADependentEntitiesColl objBIABIADependentEntitiesColl = new BIADependentEntitiesColl();

        //    BIAVitalRecordColl objVitalRecordColl = new BIAVitalRecordColl();


        //    ProcessBIADepOrgStructureColl objProcessBIADepOrgStructureColl = new ProcessBIADepOrgStructureColl();

        //    objBIAActivity = _oProcSrv.ProcessBIAActivity_GetAllByProcessID(iProcID, IsBCMEntity);
        //    objBIAPeople = _oProcSrv.ProcessBIAPeople_GetAllByProcessID(iProcID, IsBCMEntity);
        //    objBIAApplication = _oProcSrv.ProcessBIAApplication_GetAllByProcessID(iProcID, IsBCMEntity);

        //    objBIAFacility = _oProcSrv.ProcessBIAFacility_GetAllByProcessID(iProcID, IsBCMEntity);
        //    objBIAThirdParty = _oProcSrv.ProcessBIAThirdParty_GetAllByProcessID(iProcID, IsBCMEntity);



        //    BIASectionColl objBIASectionsColl = _oProcSrv.GetAllBIASectionsByProcess(Convert.ToInt32(iProcID), 0);

        //    if (objBIASectionsColl != null && objBIASectionsColl.Count > 0)
        //    {
        //        foreach (BIASection item in objBIASectionsColl)
        //        {
        //            if (item.SectionName == "Dependent  Organization Structure")
        //            {
        //                Session["BIAIDForVersion"] = item.BIAID;
        //            }
        //        }
        //    }

        //    objProcessBIADepOrgStructureColl = _ProcessSrv.BIAProcessBIADepOrgStructureByBIAID(Convert.ToInt32(Session["BIAIDForVersion"].ToString()));

        //    objBIALegal = _ProcessSrv.ProcessBIALegalAndRegulatory_GetAllByProcessID(iProcID, IsBCMEntity);


        //    var objBIAQualitativeMatrix = _ProcessSrv.GetBPBIAMatrixByBIAID(GetOldBIAID(0));

        //    var objBIAQuantitativeMatrix = _ProcessSrv.GetBPBIAMatrixByBIAID(GetOldBIAID(1));

        //    objUpApplicationDependencyList = _ProcessSrv.ApplicationDependencyGetByApplicationID(iProcID, 1);
        //    objDownApplicationDependencyList = _ProcessSrv.ApplicationDependencyGetByApplicationID(iProcID, 0);

        //    int DefaultSecBIAID = GetBIAIDForProcess(20);

        //    obBIADefaultSectionColl = _ProcessSrv.BIADefaultGetAllByBIAID(DefaultSecBIAID);

        //    int DepServiceBIAID = GetBIAIDForProcess(21);
        //    objBIADependentServicesColl = _ProcessSrv.BIADependentServices_GetByBIAID(DepServiceBIAID);

        //    int BIAID = GetBIAIDForProcess(22);
        //    objBIABIADependentEntitiesColl = _ProcessSrv.BIADependentEntities_GetByBIAID(BIAID);

        //    int VitalRecordBIAID = GetBIAIDForProcess(19);
        //    objVitalRecordColl = _ProcessSrv.BIAVitalRecordGetAllByBIAID(VitalRecordBIAID);

        //    int varBIAID = 0;

        //    if (objBIAActivity != null)
        //    {
        //        if (objBIAActivity.Count > 0)
        //        {
        //            string Sec = objBIAActivity[0].SectionID;
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (BIAActivityInfo var in objBIAActivity)
        //    {
        //        var.ActivityID = "-1";
        //        var.ChangedBy = _oUser.UserID;
        //        var.BIAID = varBIAID.ToString();
        //        var.RPO = "0";
        //        var.RPOUnit = "0";
        //        var.MAO = "0";
        //        var.MAOUnit = "0";
        //        var.RTO = "0";
        //        var.RTOUnit = "0";
        //        bool varAct = _ProcessSrv.BIAActivitySaveAndUpdate(var);
        //    }

        //    if (objBIAApplication != null)
        //    {
        //        if (objBIAApplication.Count > 0)
        //        {
        //            string Sec = objBIAApplication[0].SectionID;
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (ProcessBIAApplicationInfo var in objBIAApplication)
        //    {
        //        var.ID = "-1";
        //        var.ChangedBy = _oUser.UserID;
        //        var.CreatedBy = _oUser.UserID;
        //        var.BIAID = varBIAID.ToString();
        //        bool varAct = _ProcessSrv.ProcessBIAApplicationSaveandUpdate(var);
        //    }

        //    if (objBIAFacility != null)
        //    {
        //        if (objBIAFacility.Count > 0)
        //        {
        //            string Sec = objBIAFacility[0].SectionID;
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (ProcessBIAFacility var in objBIAFacility)
        //    {
        //        var.ID = "-1";
        //        var.ChangedBy = _oUser.UserID;
        //        var.CreatedBy = _oUser.UserID;
        //        var.BIAID = varBIAID.ToString();

        //        bool varAct = _ProcessSrv.ProcessBIAFacilityInfoSaveandUpdate(var);
        //    }

        //    if (objBIAThirdParty != null)
        //    {
        //        if (objBIAThirdParty.Count > 0)
        //        {
        //            string Sec = objBIAThirdParty[0].SectionID;
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (ProcessBIAThirdParty var in objBIAThirdParty)
        //    {
        //        var.ID = "-1";
        //        var.ChangedBy = _oUser.UserID;
        //        var.CreatedBy = _oUser.UserID;
        //        var.BIAID = varBIAID.ToString();
        //        bool varAct = _ProcessSrv.ProcessBIAThirdPartyInfoSaveandUpdate(var);
        //    }

        //    if (objBIAImpactMatrix != null)
        //    {
        //        if (objBIAImpactMatrix.Count > 0)
        //        {
        //            string Sec = objBIAImpactMatrix[0].SectionID;
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (ProcessBIAImpactMatrix var in objBIAImpactMatrix)
        //    {
        //        var.ID = "-1";
        //        var.ChangedBy = _oUser.UserID;
        //        var.CreatedBy = _oUser.UserID;
        //        var.BIAID = varBIAID.ToString();
        //        bool varAct = _ProcessSrv.ProcessBIAImpactMatrixSaveAndUpdate(var);
        //    }

        //    if (objBIAQualitativeMatrix != null)
        //    {
        //        if (objBIAQualitativeMatrix.Count > 0)
        //        {
        //            string Sec = "7";
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    DataTable ImpactMatrix = new DataTable();
        //    ImpactMatrix = (DataTable)ViewState["BIAProfile"];


        //    if (objBIAQualitativeMatrix != null)
        //    {
        //        BPBIAMatrixColl objBPBIAMatrixColl = new BPBIAMatrixColl();

        //        objBPBIAMatrixColl = _ProcessSrv.GetBPBIAMatrixByBIAID(GetOldBIAID(0));

        //        foreach (BPBIAMatrix objBPBIAMatrix in objBPBIAMatrixColl)
        //        {
        //            BPBIAMatrix objBPBIAMatrixToSave = new BPBIAMatrix();

        //            objBPBIAMatrixToSave.BIAID = varBIAID.ToString();
        //            objBPBIAMatrixToSave.BusinessProcessID = iProcessID11.ToString();
        //            objBPBIAMatrixToSave.ImpactSeverityID = objBPBIAMatrix.ImpactSeverityID.ToString();
        //            objBPBIAMatrixToSave.ImpactTypeID = objBPBIAMatrix.ImpactTypeID;
        //            objBPBIAMatrixToSave.ImpactID = objBPBIAMatrix.ImpactID;
        //            objBPBIAMatrixToSave.UpdatorId = _oUser.UserID;
        //            objBPBIAMatrixToSave.CreatorId = _oUser.UserID;
        //            objBPBIAMatrixToSave.IsQuantitative = "0";

        //            int BFBIAMatrixID = _ProcessSrv.BPBIAMatrixSave(objBPBIAMatrixToSave);

        //            ViewState["BFBIAMatrixID"] = BFBIAMatrixID.ToString();

        //            BPBIAMatrixDetailsColl objBPBIAMatrixDetailsColl = new BPBIAMatrixDetailsColl();

        //            objBPBIAMatrixDetailsColl = _ProcessSrv.GetBPBIAMatrixDetailsByBPMatrixID(Convert.ToInt32(objBPBIAMatrix.ID));

        //            foreach (BPBIAMatrixDetails objBPBIAMatrixDetails in objBPBIAMatrixDetailsColl)
        //            {
        //                BPBIAMatrixDetails objBPBIAMatrixDetailsToSave = new BPBIAMatrixDetails();
        //                objBPBIAMatrixDetailsToSave.BPMatrixID = ViewState["BFBIAMatrixID"].ToString();
        //                objBPBIAMatrixDetailsToSave.Cost = objBPBIAMatrixDetails.Cost;
        //                objBPBIAMatrixDetailsToSave.TimeIntervalID = objBPBIAMatrixDetails.TimeIntervalID;

        //                int BPBIAMatrixDetailsID = _ProcessSrv.BPBIAMatrixDetailsSave(objBPBIAMatrixDetailsToSave);
        //            }
        //        }
        //    }

        //    if (objBIAQuantitativeMatrix != null)
        //    {
        //        if (objBIAQuantitativeMatrix.Count > 0)
        //        {
        //            //string Sec = objBIAProfileMasterColl[0].SectionID;
        //            string Sec = "8";
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    if (objBIAQuantitativeMatrix != null)
        //    {
        //        BPBIAMatrixColl objBPBIAMatrixColl = new BPBIAMatrixColl();

        //        objBPBIAMatrixColl = _ProcessSrv.GetBPBIAMatrixByBIAID(GetOldBIAID(1));

        //        foreach (BPBIAMatrix objBPBIAMatrix in objBPBIAMatrixColl)
        //        {
        //            BPBIAMatrix objBPBIAMatrixToSave = new BPBIAMatrix();

        //            objBPBIAMatrixToSave.BIAID = varBIAID.ToString();
        //            objBPBIAMatrixToSave.BusinessProcessID = iProcessID11.ToString();
        //            objBPBIAMatrixToSave.ImpactSeverityID = objBPBIAMatrix.ImpactSeverityID.ToString();
        //            objBPBIAMatrixToSave.ImpactTypeID = objBPBIAMatrix.ImpactTypeID;
        //            objBPBIAMatrixToSave.ImpactID = objBPBIAMatrix.ImpactID;
        //            objBPBIAMatrixToSave.UpdatorId = _oUser.UserID;
        //            objBPBIAMatrixToSave.CreatorId = _oUser.UserID;
        //            objBPBIAMatrixToSave.IsQuantitative = "1";

        //            int BFBIAMatrixID = _ProcessSrv.BPBIAMatrixSave(objBPBIAMatrixToSave);

        //            ViewState["BFBIAMatrixID"] = BFBIAMatrixID.ToString();

        //            BPBIAMatrixDetailsColl objBPBIAMatrixDetailsColl = new BPBIAMatrixDetailsColl();

        //            objBPBIAMatrixDetailsColl = _oProcSrv.GetBPBIAMatrixDetailsByBPMatrixID(Convert.ToInt32(objBPBIAMatrix.ID));

        //            foreach (BPBIAMatrixDetails objBPBIAMatrixDetails in objBPBIAMatrixDetailsColl)
        //            {
        //                BPBIAMatrixDetails objBPBIAMatrixDetailsToSave = new BPBIAMatrixDetails();
        //                objBPBIAMatrixDetailsToSave.BPMatrixID = ViewState["BFBIAMatrixID"].ToString();
        //                objBPBIAMatrixDetailsToSave.Cost = objBPBIAMatrixDetails.Cost;
        //                objBPBIAMatrixDetailsToSave.TimeIntervalID = objBPBIAMatrixDetails.TimeIntervalID;

        //                int BPBIAMatrixDetailsID = _ProcessSrv.BPBIAMatrixDetailsSave(objBPBIAMatrixDetailsToSave);
        //            }
        //        }
        //    }

        //    if (objUpApplicationDependencyList != null)
        //    {
        //        if (objUpApplicationDependencyList.Count > 0)
        //        {
        //            string Sec = "17";
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (ApplicationDependency objApplicationDependency in objUpApplicationDependencyList)
        //    {
        //        objApplicationDependency.ID = "-1";
        //        objApplicationDependency.ChangedBy = _oUser.UserID;
        //        objApplicationDependency.CreatedBy = _oUser.UserID;
        //        objApplicationDependency.BIAID = varBIAID.ToString();
        //        bool varAct = _ProcessSrv.ApplicationDependencySave(objApplicationDependency) > 0;
        //    }
        //    if (objDownApplicationDependencyList != null)
        //    {
        //        if (objDownApplicationDependencyList.Count > 0)
        //        {
        //            //string Sec = objUpApplicationDependencyList[0].SectionID;
        //            string Sec = "18";
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (ApplicationDependency objApplicationDependency in objDownApplicationDependencyList)
        //    {
        //        objApplicationDependency.ID = "-1";
        //        objApplicationDependency.ChangedBy = _oUser.UserID;
        //        objApplicationDependency.CreatedBy = _oUser.UserID;
        //        objApplicationDependency.BIAID = varBIAID.ToString();
        //        bool varAct = _ProcessSrv.ApplicationDependencySave(objApplicationDependency) > 0;
        //    }

        //    if (obBIADefaultSectionColl != null)
        //    {
        //        if (obBIADefaultSectionColl.Count > 0)
        //        {
        //            string Sec = "20";//obBIADefaultSectionColl[0].SectionID;
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (BIADefaultSection var in obBIADefaultSectionColl)
        //    {
        //        var.ID = "-1";
        //        var.ChangedBy = _oUser.UserID;
        //        var.BIAID = varBIAID.ToString();
        //        bool Success = _ProcessSrv.BIADefaultSaveAndUpdate(var);
        //        //bool varAct = _oProcSrv.BIAActivitySaveAndUpdate(var);
        //    }

        //    if (objBIADependentServicesColl != null)
        //    {
        //        if (objBIADependentServicesColl.Count > 0)
        //        {
        //            string Sec = "21"; //objBIADependentServicesColl[0].SectionID;
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (BIADependentServicesInfo var in objBIADependentServicesColl)
        //    {
        //        var.ID = "-1";
        //        var.ChangedBy = _oUser.UserID;
        //        var.BIAID = varBIAID.ToString();
        //        bool Success = Convert.ToInt32(_ProcessSrv.BIADependent_Services_SaveAndUpdate(var)) > 0;
        //        //bool varAct = _oProcSrv.BIAActivitySaveAndUpdate(var);
        //    }

        //    if (objBIABIADependentEntitiesColl != null)
        //    {
        //        if (objBIABIADependentEntitiesColl.Count > 0)
        //        {
        //            string Sec = "22";// objBIABIADependentEntitiesColl[0].SectionID;
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (BIADependentEntitiesInfo var in objBIABIADependentEntitiesColl)
        //    {
        //        var.ID = "-1";
        //        var.ChangedBy = _oUser.UserID;
        //        var.BIAID = varBIAID.ToString();
        //        bool Success = Convert.ToInt32(_ProcessSrv.BIADependent_Entities_SaveAndUpdate(var)) > 0;
        //        //bool varAct = _oProcSrv.BIAActivitySaveAndUpdate(var);
        //    }
        //    if (objVitalRecordColl != null)
        //    {
        //        if (objVitalRecordColl.Count > 0)
        //        {
        //            string Sec = "19";// objVitalRecordColl[0].SectionID;
        //            varBIAID = GetCurrentProcessBIAID(Sec);
        //        }
        //    }

        //    foreach (BIAVitalRecord var in objVitalRecordColl)
        //    {
        //        var.VitalRecordId = -1;
        //        var.ChangedBy = Convert.ToInt32(_oUser.UserID);
        //        var.BIAID = varBIAID;
        //        bool Success = _ProcessSrv.BIAVitalRecordSaveAndUpdate(var);
        //        //bool varAct = _oProcSrv.BIAActivitySaveAndUpdate(var);
        //    }


        //    // Legal and Regulatory

        //    if (objBIALegal != null)
        //    {
        //        if (objBIALegal.Count > 0)
        //        {
        //            string Sec = objBIALegal[0].SectionID;
        //            varBIAID = GetCurrentBIAID(Sec);
        //        }
        //    }

        //    foreach (ProcessBIALegalAndRegulatory var in objBIALegal)
        //    {
        //        var.ID = "-1";
        //        var.ChangedBy = _oUser.UserID;
        //        var.CreatedBy = _oUser.UserID;
        //        var.BIAID = varBIAID.ToString();

        //        bool Success = _ProcessSrv.processbialegalandregulatorySaveandUpdate(var);
        //    }



        //    // Dependent Org Structure

        //    if (objProcessBIADepOrgStructureColl != null)
        //    {
        //        if (objProcessBIADepOrgStructureColl.Count > 0)
        //        {
        //            string Sec = "23";

        //            varBIAID = GetCurrentBIAID(Sec);
        //        }
        //    }

        //    foreach (ProcessBIADepOrgStructure var in objProcessBIADepOrgStructureColl)
        //    {
        //        var.ID = "-1";
        //        var.BIAID = varBIAID.ToString();
        //        var.CreatedBy = Convert.ToInt32(_oUser.UserID);

        //        bool Success = _ProcessSrv.BIAProcessBIADepOrgStructureSave(var);
        //    }



        //    //for secion iD 33--Work Area Recovery

        //    int WorkAreaRecoveryBIAID = GetBIAIDForProcess(33);

        //    AddBIARecords(WorkAreaRecoveryBIAID);

        //    //for section 2

        //    int HumanBIAID = GetBIAIDForProcess(2);

        //    AddBIARecordsHumanResources(HumanBIAID);

        //}
        #endregion BIA Section Save

        #region Business Parameter Profile Save

        //if (objBusProInfo.BPProfileID > 0)
        //{
        //    if (objBusProInfo.BPProfileID > 0)
        //    {
        //        BusinessProfileParameterColl objBusinessProfileParameterColl = _ProcessSrv.BusinessProfileParameterbyProfID(objBusProInfo.BPProfileID);

        //        if (objBusinessProfileParameterColl != null || objBusinessProfileParameterColl.Count > 0)
        //        {

        //            //      DataTable _dataTable = buildProfileDataTable();

        //            foreach (BusinessProfileParameter item in objBusinessProfileParameterColl)
        //            {
        //                //  _dataTable.Rows.Add(item.ProfileID, item.ParameterID);

        //                BusinessParameterValue objBusinessParameterValue = _ProcessSrv.BusinessParameterValue_ID(Convert.ToInt32(iProcID), Convert.ToInt32(item.ParameterID), objBusProInfo.BPProfileID);

        //                objBusinessParameterValue.ID = -1;
        //                objBusinessParameterValue.ProcessID = iProcessID11;
        //                objBusinessParameterValue.CreatedBy = Convert.ToInt32(_oUser.UserID);
        //                objBusinessParameterValue.UpdatedBy = Convert.ToInt32(_oUser.UserID);


        //                if (objBusinessParameterValue.ParameterID > 0 && objBusinessParameterValue.ProfileID > 0)
        //                {
        //                    int ID = _ProcessSrv.BusinessParameterValue_Save(objBusinessParameterValue);

        //                }

        //            }

        //        }

        //    }
        //}

        #endregion Business Parameter Profile Save

        return RedirectToAction("ManageRisk");
    }


    [HttpGet]
    public IActionResult AttachRecoveryPlan(int RiskID, int PlanID, string IncidentName)
    {
        List<RecoveryPlan> lstRecoveryPlan = _ProcessSrv.GetAllRecoveyPlanByID(Convert.ToInt32(PlanID));
        ViewBag.RecoveryPlan = lstRecoveryPlan;

        return RedirectToAction("ManageRisk"); 
    }


    [HttpPost]
    public IActionResult AttachRecoveryPlan(RiskManagement riskManagement)
    { 
        return RedirectToAction("ManageRisk"); 
    }




    public bool RiskAssementVersion(int? EntityID, string Version)
    {
        bool isSuccess = false;
        try
        {
            string iRiskID = string.Empty;
            List<RiskManagement> riskListCol = new List<RiskManagement>();
            riskListCol = _ProcessSrv.getBCMRiskList("0", "0", "0", "0", Convert.ToString(EntityID), "1", "0", "0", "0", string.Empty, string.Empty);

            foreach (RiskManagement Entity in riskListCol)
            {
                if (Entity != null)
                {
                    Entity.CreatedBy = _UserDetails.UserID;
                    Entity.UpdatedByID = Convert.ToInt32(_UserDetails.UserID);
                    Entity.RiskTrendStatus = 0;
                    Entity.ID = 0;
                    Entity.Status = ((int)BCPEnum.ApprovalType.Initiated);
                    bool res1 = _ProcessSrv.RiskAssessmentProcessFormSave(Entity, ref iRiskID);

                    Entity.RiskOwner = Convert.ToString(Entity.RiskOwnerID);
                    Entity.Version = Version;
                    Entity.RiskCode = iRiskID;

                    int iProcessID11 = _ProcessSrv.RiskAssessmentSubRegisterProcessFormSave_1(Entity);
                    iRiskID = iProcessID11.ToString();
                    isSuccess = true;

                    if (iProcessID11 != 0)
                    {
                        List<RiskManagement> objRiskColl = new List<RiskManagement>();
                        DataTable _dt = _ProcessSrv.TreatmentPlanByRiskSubID(Convert.ToInt32(Entity.SubRiskRegID.ToString()));

                        if (_dt != null)
                        {
                            foreach (DataRow dr in _dt.Rows)
                            {
                                int RiskSubID = iProcessID11;
                                int PlanID = Convert.ToInt32(dr["ID"]);
                                int IsEffective = Convert.ToInt32(dr["IsEffectivePlan"]);
                                bool MapID = _ProcessSrv.SaveRiskTreatmentMap(RiskSubID, PlanID, IsEffective);
                            }
                        }

                        isSuccess = true;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return isSuccess;
    }
    #endregion
}


