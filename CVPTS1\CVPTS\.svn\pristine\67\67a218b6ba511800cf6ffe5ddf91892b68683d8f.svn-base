﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using BCM.Shared;
using System.Text;
using System.Collections.Concurrent;
using BCM.Security.Helper;

//using Microsoft.EntityFrameworkCore.Metadata.Internal;
//using log4net.Core;


namespace BCM.UI.Areas.BCMResourceManagement.Controllers;
[Area("BCMResourceManagement")]
public class UserReminderController : Controller
{
    private ProcessSrv _IProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CvLogger;
    private readonly BCMMail _BCMMail;
    private readonly BCPSms _BCPSms;
    ManageUsersDetails? _UserDetails = new ManageUsersDetails();

    // Static dictionary to track reminder progress
    private static readonly ConcurrentDictionary<string, ReminderProgress> _reminderProgress = new();
    //private readonly ILoggerFactory _LoggerFactory;
    //private readonly ILogger<CVLogger> _Log;
    //private Common _Common;
    public UserReminderController(ProcessSrv iProcessSrv, Utilities Utilities, BCMMail bcmMail, BCPSms bcpSms, CVLogger cvLogger)
    {
        _IProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _BCMMail = bcmMail;
        _BCPSms = bcpSms;
        _CvLogger = cvLogger;
        _UserDetails = _Utilities.LoginUserDetails();
        
    }

    public IActionResult Index()
    {
        return View();
    }

    [HttpGet]
    public IActionResult UserReminder()
    {
        List<ResourcesReminder> ResourceList = new List<ResourcesReminder>();
        try
        {
            // Implement role-based filtering logic
            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole)))
            {
                // Regular user - get data based on their org, unit, and department
                ResourceList = _IProcessSrv.GetRemindersResponseUnitIDDepartmentD(_UserDetails.OrgID, _UserDetails.UnitID, _UserDetails.DepartmentID);
            }
            else
            {
                // Admin users - get data based on selected dropdown values or defaults
                var orgId = _UserDetails.OrgID; // Default to user's org, can be overridden by dropdown selection
                var unitId = _UserDetails.UnitID; // Default to user's unit, can be overridden by dropdown selection
                var departmentId = _UserDetails.DepartmentID; // Default to user's department, can be overridden by dropdown selection

                var ResourceList1 = _IProcessSrv.GetRemindersResponseUnitIDDepartmentD(orgId, unitId, departmentId);

                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    // Super Admin - filter by OrgGroupID
                    var filteredResourceList = ResourceList1.Where(relist => relist.OrgGroupID == _UserDetails.OrgGroupID);

                    if (filteredResourceList != null && filteredResourceList.Any())
                    {
                        ResourceList.AddRange(filteredResourceList);
                    }
                }
                else
                {
                    // Product Admin - get all data without OrgGroupID filtering
                    ResourceList = ResourceList1;
                }
            }

            PopulateDropdowns();
        }
        catch (Exception)
        {
            // _Utilities.CreateCVCoreLogger("UserReminderErrorLog", ex.Message);
        }

        return View(ResourceList);
    }

    [HttpPost]
    public IActionResult GetFilteredReminders(int orgGroupId = 0, int orgId = 0, int unitId = 0, int departmentId = 0)
    {
        List<ResourcesReminder> ResourceList = new List<ResourcesReminder>();
        try
        {
            // Use provided filter values or fall back to user defaults
            var filterOrgId = orgId > 0 ? orgId : _UserDetails.OrgID;
            var filterUnitId = unitId > 0 ? unitId : _UserDetails.UnitID;
            var filterDepartmentId = departmentId > 0 ? departmentId : _UserDetails.DepartmentID;

            // Apply same role-based filtering logic with dropdown filters
            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole)))
            {
                // Regular user - always use their own org/unit/department regardless of dropdown selections
                ResourceList = _IProcessSrv.GetRemindersResponseUnitIDDepartmentD(_UserDetails.OrgID, _UserDetails.UnitID, _UserDetails.DepartmentID);
            }
            else
            {
                // Admin users - use dropdown selections
                var ResourceList1 = _IProcessSrv.GetRemindersResponseUnitIDDepartmentD(filterOrgId, filterUnitId, filterDepartmentId);

                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    // Super Admin - filter by OrgGroupID
                    var filteredResourceList = ResourceList1.Where(relist => relist.OrgGroupID == _UserDetails.OrgGroupID);

                    if (filteredResourceList != null && filteredResourceList.Any())
                    {
                        ResourceList.AddRange(filteredResourceList);
                    }
                }
                else
                {
                    // Product Admin - get all data without OrgGroupID filtering
                    ResourceList = ResourceList1;
                }
            }

            return Json(new { success = true, data = ResourceList });
        }
        catch (Exception ex)
        {
            // _Utilities.CreateCVCoreLogger("UserReminderErrorLog", ex.Message);
            _CvLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "Error occurred while filtering data." });
        }
    }

    public void PopulateDropdowns()
    {
        try
        {
            var orgGroupInfoList = _Utilities.GetOrgGroupList();
            ViewBag.orgGroupInfoList = orgGroupInfoList;

            var orgInfoList = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString());
            ViewBag.orgInfo = orgInfoList;

            var UnitInfoList = _IProcessSrv.GetOrganizationUnitListByOrgID(_UserDetails.OrgID);
            ViewBag.unit = UnitInfoList;

            ViewBag.department = _Utilities.GetDepartmentAllListForDropdown();

            ViewBag.SubDepartment = _Utilities.GetAllSubDepartmentListDropdown();

            List<UserRoleMasterInfo> RoleMasterList = _Utilities.GetUserRoleMasterByOrgID(_UserDetails.OrgID);
            ViewBag.RoleMasterList = RoleMasterList;

            List<TypeMasterInfo> TypeList = _Utilities.GetTypeInfoByEntityID(Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.ResourcePlan));
            ViewBag.Type = TypeList;

            ViewBag.CompanyList = _Utilities.GetCompanyMasterInfoList(_UserDetails.OrgID);
            ViewBag.ResourceDesignation = _Utilities.GetAllResourceDesignationList();
            ViewBag.resourceInfo = _IProcessSrv.GetAllResourcesList();
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
           
        }
    }

    [HttpPost]
    public IActionResult SendReminders([FromBody] SendReminderRequest request)
    {
        try
        {
            if (request == null || request.SelectedUsers == null || !request.SelectedUsers.Any())
            {
                return Json(new { success = false, message = "Please select at least one user to send reminders." });
            }

            // Generate unique job ID for tracking progress
            string jobId = Guid.NewGuid().ToString();

            // Capture base URL before starting background task (HttpContext won't be available in background)
            string baseUrl = $"{Request.Scheme}://{Request.Host}";

            // Initialize progress tracking
            var progress = new ReminderProgress
            {
                JobId = jobId,
                TotalUsers = request.SelectedUsers.Count,
                ProcessedUsers = 0,
                EmailsSent = 0,
                SMSSent = 0,
                IsCompleted = false,
                StartTime = DateTime.Now,
                Status = "Starting..."
            };

            _reminderProgress[jobId] = progress;

            // Start background processing with captured base URL
            Task.Run(() => ProcessRemindersAsync(request, jobId, baseUrl));

            return Json(new {
                success = true,
                jobId = jobId,
                message = "Reminder processing started. You can track progress in real-time.",
                totalUsers = request.SelectedUsers.Count
            });
        }
        catch (Exception ex)
        {
           
            _CvLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "An error occurred while starting reminder process." });
        }
    }

    [HttpGet]
    public IActionResult GetReminderProgress(string jobId)
    {
        if (string.IsNullOrEmpty(jobId) || !_reminderProgress.TryGetValue(jobId, out var progress))
        {
            return Json(new { success = false, message = "Invalid job ID or job not found." });
        }

        return Json(new {
            success = true,
            progress = new {
                jobId = progress.JobId,
                totalUsers = progress.TotalUsers,
                processedUsers = progress.ProcessedUsers,
                emailsSent = progress.EmailsSent,
                smsSent = progress.SMSSent,
                isCompleted = progress.IsCompleted,
                status = progress.Status,
                errors = progress.Errors,
                percentComplete = progress.TotalUsers > 0 ? (int)((double)progress.ProcessedUsers / progress.TotalUsers * 100) : 0
            }
        });
    }

    private async Task ProcessRemindersAsync(SendReminderRequest request, string jobId, string baseUrl)
    {
        var progress = _reminderProgress[jobId];

        // Collections to track successful transactions for logging
        List<int> emailTransactions = new List<int>();
        List<int> smsTransactions = new List<int>();

        try
        {
            progress.Status = "Loading user data...";

            // Apply same role-based filtering logic as in UserReminder method
            List<ResourcesReminder> allUsers = new List<ResourcesReminder>();

            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole) || _Utilities.IsSuperAdmin(_UserDetails.UserRole)))
            {
                // Regular user
                allUsers = _IProcessSrv.GetRemindersResponseUnitIDDepartmentD(_UserDetails.OrgID, _UserDetails.UnitID, _UserDetails.DepartmentID);
            }
            else
            {
                // Admin users
                var allUsersList = _IProcessSrv.GetRemindersResponseUnitIDDepartmentD(_UserDetails.OrgID, _UserDetails.UnitID, _UserDetails.DepartmentID);

                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    // Super Admin - filter by OrgGroupID
                    var filteredUsers = allUsersList.Where(relist => relist.OrgGroupID == _UserDetails.OrgGroupID);

                    if (filteredUsers != null && filteredUsers.Any())
                    {
                        allUsers.AddRange(filteredUsers);
                    }
                }
                else
                {
                    // Product Admin - get all data
                    allUsers = allUsersList;
                }
            }

            progress.Status = "Sending reminders...";

            foreach (var userId in request.SelectedUsers)
            {
                var user = allUsers.FirstOrDefault(u => u.ResourceID == userId);

                if (user != null)
                {
                    progress.Status = $"Processing {user.ResourceName}...";

                    // Send Email Reminder
                    if (request.SendEmail && !string.IsNullOrEmpty(user.CompanyEmail))
                    {
                        try
                        {
                            string emailSubject = "BCM User Validation Reminder";
                            string emailBody = BuildEmailBody(user.ResourceName, request.CustomMessage, userId, baseUrl);

                            bool emailSent = _BCMMail.SendMail(emailSubject, emailBody, user.CompanyEmail, "", "", "",
                                _UserDetails.OrgID.ToString(), "", "", _UserDetails.UserID.ToString(), userId.ToString());

                            if (emailSent)
                            {
                                progress.EmailsSent++;
                                emailTransactions.Add(userId); // Track successful email transaction
                            }
                            else
                            {
                                progress.Errors.Add($"Failed to send email to {user.ResourceName} ({user.CompanyEmail})");
                            }
                        }
                        catch (Exception ex)
                        {
                            progress.Errors.Add($"Email error for {user.ResourceName}: {ex.Message}");
                        }
                    }

                    // Send SMS Reminder
                    if (request.SendSMS && !string.IsNullOrEmpty(user.MobilePhone))
                    {
                        try
                        {
                            string smsMessage = BuildSMSBody(user.ResourceName, request.CustomMessage);

                            bool smsSent = _BCPSms.SMSSend(user.MobilePhone, user.ResourceName, smsMessage,
                                _UserDetails.OrgID, 0, _UserDetails.UserID);

                            if (smsSent)
                            {
                                progress.SMSSent++;
                                smsTransactions.Add(userId); // Track successful SMS transaction
                            }
                            else
                            {
                                progress.Errors.Add($"Failed to send SMS to {user.ResourceName} ({user.MobilePhone})");
                            }
                        }
                        catch (Exception ex)
                        {
                            progress.Errors.Add($"SMS error for {user.ResourceName}: {ex.Message}");
                        }
                    }
                }

                progress.ProcessedUsers++;

                // Small delay to prevent overwhelming the services
                await Task.Delay(100);
            }

            // Log successful transactions to reminder transaction table
            progress.Status = "Logging transactions...";
            await LogReminderTransactions(emailTransactions, smsTransactions);

            progress.IsCompleted = true;
            progress.Status = $"Completed! Emails: {progress.EmailsSent}, SMS: {progress.SMSSent}";

            // Clean up progress after 5 minutes
            _ = Task.Delay(TimeSpan.FromMinutes(5)).ContinueWith(_ =>
            {
                _reminderProgress.TryRemove(jobId, out ReminderProgress removedProgress);
            });
        }
        catch (Exception ex)
        {
            progress.IsCompleted = true;
            progress.Status = "Error occurred during processing";
            progress.Errors.Add($"General error: {ex.Message}");
            _CvLogger.LogErrorApp(ex);
        }
    }

    private string BuildEmailBody(string userName, string customMessage, int resourceId = 0, string baseUrl = "")
    {
        StringBuilder sb = new StringBuilder();
        sb.Append("<HTML><body>");
        sb.Append($"Dear {userName},<br /><br />");
        sb.Append("This is a reminder to validate your BCM user account and ensure your information is up to date.<br /><br />");

        if (!string.IsNullOrEmpty(customMessage))
        {
            sb.Append($"<b>Additional Message:</b><br />{customMessage}<br /><br />");
        }

        sb.Append("Please click the link below to verify your account:<br /><br />");
        sb.Append("<div style=\"background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0;\">");
        sb.Append("<strong>⏰ Important:</strong> This verification link will expire in <strong>5 minutes</strong> for security reasons.");
        sb.Append("</div><br />");

        // Generate encrypted verification link
        if (resourceId > 0)
        {
            if (!string.IsNullOrEmpty(baseUrl))
            {
                string verificationLink = GenerateVerificationLink(resourceId, _UserDetails.OrgID, baseUrl);

                if (!string.IsNullOrEmpty(verificationLink))
                {
                    sb.Append($"<a href=\"{verificationLink}\" style=\"background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;\">🔗 Verify My Account Now</a><br /><br />");
                    sb.Append($"Or copy and paste this link in your browser:<br />");
                    sb.Append($"<a href=\"{verificationLink}\">{verificationLink}</a><br /><br />");
                    sb.Append("<small style=\"color: #666;\">If the link has expired, please request a new reminder email.</small><br /><br />");
                }
                else
                {
                    sb.Append("Please contact your administrator to verify your account.<br /><br />");
                }
            }
            else
            {
                // Fallback when baseUrl is not provided (e.g., for testing or other scenarios)
                sb.Append("Please log in to the BCM system and verify your account details.<br /><br />");
            }
        }

        sb.Append("This is an automated notification, no need to reply.<br /><br />");
        sb.Append("Thank you.<br /><br />");
        sb.Append("<b>Admin</b><br />Continuity Vault");
        sb.Append("</body></HTML>");

        return sb.ToString();
    }

    private string BuildSMSBody(string userName, string customMessage)
    {
        StringBuilder sb = new StringBuilder();
        sb.Append($"BCM Reminder: Please validate your user account. ");

        if (!string.IsNullOrEmpty(customMessage))
        {
            sb.Append($"{customMessage} ");
        }

        sb.Append("Login to BCM system to verify details. -Continuity Vault");

        return sb.ToString();
    }

    private async Task LogReminderTransactions(List<int> emailTransactions, List<int> smsTransactions)
    {
        try
        {
            // Create collection for email transactions
            if (emailTransactions.Any())
            {
                List<ResourcesReminder> emailReminderColl = new List<ResourcesReminder>();

                foreach (int resourceId in emailTransactions)
                {
                    var emailReminder = new ResourcesReminder
                    {
                        ResourceID = resourceId,
                        CommunicationMode = (int)BCPEnum.NotificationType.EMail,
                        InOut_bound = (int)BCPEnum.CommunicationType.Inbound, // Inbound as per reference code
                        SenderID = _UserDetails.UserID
                    };
                    emailReminderColl.Add(emailReminder);
                }

                // Save email transactions
                bool emailResult = _IProcessSrv.ResourcesReminderSave(emailReminderColl);
                if (!emailResult)
                {
                    // Log error if needed
                    // _Utilities.CreateCVCoreLogger("EmailTransactionError", "Failed to save email reminder transactions");
                }
            }

            // Create collection for SMS transactions
            if (smsTransactions.Any())
            {
                List<ResourcesReminder> smsReminderColl = new List<ResourcesReminder>();

                foreach (int resourceId in smsTransactions)
                {
                    var smsReminder = new ResourcesReminder
                    {
                        ResourceID = resourceId,
                        CommunicationMode = (int)BCPEnum.NotificationType.SMS,
                        InOut_bound = (int)BCPEnum.CommunicationType.Inbound, // Inbound as per reference code
                        SenderID = _UserDetails.UserID
                    };
                    smsReminderColl.Add(smsReminder);
                }

                // Save SMS transactions
                bool smsResult = _IProcessSrv.ResourcesReminderSave(smsReminderColl);
                if (!smsResult)
                {
                    // Log error if needed
                    // _Utilities.CreateCVCoreLogger("SMSTransactionError", "Failed to save SMS reminder transactions");
                }
            }
        }
        catch (Exception ex)
        {
            _CvLogger.LogErrorApp(ex);
            // Log error if needed
            // _Utilities.CreateCVCoreLogger("TransactionLogError", ex.Message);
        }
    }

    private string GenerateVerificationLink(int resourceId, int orgId, string baseUrl)
    {
        try
        {
            // Create timestamp for link expiration (current time in UTC)
            string timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();

            // Encrypt parameters including timestamp
            string encryptedResourceId = EncryptUrlParameter(resourceId.ToString());
            string encryptedOrgId = EncryptUrlParameter(orgId.ToString());
            string encryptedTimestamp = EncryptUrlParameter(timestamp);

            return $"{baseUrl}/CVCore/BCMResourceManagement/EmailVerification/VerifyEmail?resourceId={encryptedResourceId}&orgId={encryptedOrgId}&timestamp={encryptedTimestamp}";
        }
        catch
        {
            return string.Empty;
        }
    }

    private string EncryptUrlParameter(string value)
    {
        try
        {
            // Use CryptographyHelper for encryption
            return CryptographyHelper.Encrypt(value);
        }
        catch (Exception ex)
        {
            // Log error if needed
            // _Utilities.CreateCVCoreLogger("EncryptionError", ex.Message);
            _CvLogger.LogErrorApp(ex);
            return value;
        }
    }
}

public class SendReminderRequest
{
    public List<int> SelectedUsers { get; set; } = new List<int>();
    public bool SendEmail { get; set; }
    public bool SendSMS { get; set; }
    public string CustomMessage { get; set; } = "";
}

public class ReminderProgress
{
    public string JobId { get; set; } = "";
    public int TotalUsers { get; set; }
    public int ProcessedUsers { get; set; }
    public int EmailsSent { get; set; }
    public int SMSSent { get; set; }
    public bool IsCompleted { get; set; }
    public DateTime StartTime { get; set; }
    public string Status { get; set; } = "";
    public List<string> Errors { get; set; } = new List<string>();
}
