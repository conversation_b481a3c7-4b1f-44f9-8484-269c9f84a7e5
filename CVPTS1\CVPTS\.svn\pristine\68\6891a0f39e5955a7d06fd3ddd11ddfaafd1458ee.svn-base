@model BCM.BusinessClasses.PerformanceEvaluation

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}
@* <div class="wizard-content"> *@
<form id="addPerformanceEvaluationForm" asp-action="AddPerformanceEvaluation" asp-controller="ManagePerformanceEvaluationSC" class="needs-validation progressive-validation" novalidate>
    <div class="row row-cols-2">
        <div class="col">
            <div class="form-group">
                <label for="EffectiveFromDate" class="form-label">From Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-calendar"></i></span>
                    @* <input class="form-control" type="date" asp-for="EvFromDate" /> *@
                    <input type="date" id="EffectiveFromDate" value="0" name="EvFromDate" asp-for="EvFromDate" class="form-control" required>
                </div>
                <div class="invalid-feedback">Enter From Date</div>
            </div>

        </div>
        <div class="col">
            <div class="form-group">
                <label for="EffectiveToDate" class="form-label">To Date </label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-calendar"></i></span>
                   @*  <input class="form-control" type="date" asp-for="EvToDate" /> *@
                    <input type="date" id="EffectiveToDate" value="0" name="EvToDate" asp-for="EvToDate" class="form-control" required>
                </div>
                <div class="invalid-feedback">Enter To Date</div>
            </div>
        </div>
    </div>
    <h6 class="Sub-Title">Performance Evaluation</h6>
    <div class="row row-cols-2">
        <div class="col">
            <div class="form-group">
                <label for="ddlITDRName" class="form-label">IT DR Objectives</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-objectives"></i></span>
                    <input type="hidden" asp-for="ITDRName" id="ITDRName" />
                    <select class="form-select form-select-sm selectized" id="ddlITDRName" asp-for="MetricID" required>
                        <option value="0" selected>-- ITDRName --</option>
                        @{
                            foreach (var ITDRName in ViewBag.ITDRObj)
                            {
                                <option value="@ITDRName.Value">@ITDRName.Text</option>
                            }
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select IT DR Objectives</div>
            </div>
            <div class="form-group">
                <label for="ddlMeasureKPI" class="form-label">How to measure KPI</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-measure"></i></span>

                    <select class="form-select form-select-sm selectized" id="ddlMeasureKPI" asp-for="MeasurementID" required>
                        <option value="0" selected>-- Measure KPI --</option>

                        @{
                            foreach (var MeasureKPI in ViewBag.ITDR_measureKPI)
                            {
                                <option value="@MeasureKPI.Value">@MeasureKPI.Text</option>
                            }
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select How to measure KPI</div>
            </div>
            <div class="form-group">
                <label for="ResourcesReq" class="form-label">Resources Required and Supporting Material</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-resources"></i></span>
                    <input class="form-control" type="text" id="ResourcesReq" asp-for="ResourcesReq" required />
                </div>
                <div class="invalid-feedback">Enter Resources Required and Supporting Material</div>
            </div>
            <div class="form-group">
                <label for="EffectiveCriteria" class="form-label">Effectiveness criteria</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-criteria"></i></span>
                    <input class="form-control" type="text" placeholder="Enter Effectiveness criteria" id="EffectiveCriteria" asp-for="EffectiveCriteria" required />
                </div>
                <div class="invalid-feedback">Enter Effectiveness criteria</div>
            </div>
            <div class="form-group">
                <label for="ddlRating" class="form-label">Effectiveness Rating</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-rating"></i></span>

                    <select class="form-select form-select-sm selectized" id="ddlRating" asp-for="Rating" required>
                        <option value="0" selected>-- Effective Rating --</option>

                        @{
                            foreach (var EffectiveRating in ViewBag.ITDR_EffRating)
                            {
                                <option value="@EffectiveRating.Value">@EffectiveRating.Text</option>
                            }
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Effectiveness Rating</div>
            </div>
            <div class="form-group">
                <label for="OverallKPI" class="form-label">Overall KPI</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-kpi"></i></span>
                    <input class="form-control" type="text" placeholder="Enter Overall KPI" id="OverallKPI" asp-for="OverallKPI" required />
                </div>
                <div class="invalid-feedback">Enter Overall KPI</div>
            </div>
            <div class="form-group">
                <label for="TargetedDate" class="form-label">Targeted date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-targeted-date"></i></span>
                    @* <input class="form-control" type="date" asp-for="TargetedDate" /> *@
                    <input type="date" id="TargetedDate" value="0" name="TargetedDate" asp-for="TargetedDate" class="form-control" required>
                </div>
                <div class="invalid-feedback">Enter Targeted date</div>
            </div>
            <div class="form-group w-100">
                <label class="form-label">Current risks</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-risks"></i></span>
                    <textarea class="form-control" placeholder="Activity Details" asp-for="CurrentRisk" style="height:0px" required></textarea>
                </div>
                <div class="invalid-feedback">Enter Current risks</div>
            </div>

        </div>
        <div class="col">
            <div class="form-group">
                <label for="ddlMeasuredBy" class="form-label">Objectives to be measured by</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-objectives"></i></span>


                    <select class="form-select form-control selectized" id="ddlMeasuredBy" asp-for="MeasuredBy" required>
                        <option value="0" selected>-- Measured By --</option>
                        @{
                            foreach (var MeasuredBy in ViewBag.ITDR_Objective)
                            {
                                <option value="@MeasuredBy.Value">@MeasuredBy.Text</option>
                            }
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Objectives to be measured by</div>
            </div>

            <div class="form-group">
                <label for="ddlFrequency" class="form-label">When to be measured</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-measure"></i></span>

                    <select class="form-select form-select-sm selectized" id="ddlFrequency" asp-for="FrequencyID" required>
                        <option value="0" selected>-- Frequency --</option>
                        @{
                            foreach (var Frequency in ViewBag.Populat_Frequency)
                            {
                                <option value="@Frequency.Value">@Frequency.Text</option>
                            }
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select When to be measured</div>
            </div>
            <div class="form-group">
                <label for="ddlResource" class="form-label">Responsible parties</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-responsible"></i></span>

                    <select class="form-select form-select-sm selectized" id="ddlResource" asp-for="RespParties" required>
                        <option value="0" selected>-- Responsible Parties --</option>
                        @{
                            foreach (var lstResource in ViewBag.lstResource)
                            {
                                <option value="@lstResource.Value">@lstResource.Text</option>
                            }
                        }
                    </select>

                </div>
                <div class="invalid-feedback">Select Responsible parties</div>
            </div>
            <div class="form-group">
                <label for="RemarkEffLevel" class="form-label">Remarks on Current Effectiveness level</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-remarks"></i></span>
                    <input class="form-control" type="text" placeholder="Enter Remarks on Current Effectiveness level" id="RemarkEffLevel" asp-for="RemarkEffLevel" required />
                </div>
                <div class="invalid-feedback">Enter Remarks on Current Effectiveness level</div>
            </div>
            <div class="form-group">
                <label for="Effectiveness" class="form-label">Effectiveness</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-effectiveness"></i></span>
                    <input class="form-control" type="text" placeholder="Enter Effectiveness" id="Effectiveness" asp-for="Effectiveness" required />
                </div>
                <div class="invalid-feedback">Enter Effectiveness</div>
            </div>
            <div class="form-group">
                <label for="Target" class="form-label">Target</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-target"></i></span>
                    <input class="form-control" type="text" placeholder="Enter Target" id="Target" asp-for="Target" required />
                </div>
                <div class="invalid-feedback">Enter Target</div>
            </div>
            <div class="form-group">
                <label for="ddlCorrectiveAction" class="form-label">Corrective action</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-corrective-action"></i></span>
                    <select class="form-select form-select-sm selectized" id="ddlCorrectiveAction" asp-for="CorrectiveAction" required>
                        <option value="0">NA</option>
                        <option value="1">Yes</option>
                        <option value="2">No</option>

                    </select>
                </div>
                <div class="invalid-feedback">Select Corrective action</div>
            </div>
            <div class="form-group w-100">
                <label class="form-label">Remarks</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-remarks"></i></span>
                    <textarea class="form-control" placeholder="Activity Details" style="height:0px" asp-for="Remarks" required></textarea>
                </div>
                <div class="invalid-feedback">Enter Remarks</div>
            </div>
        </div>
    </div>
    @* </div> *@

    <div class="form-group">
        <div class="modal-footer d-flex justify-content-between">
            <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
            <div>
                <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>




</form>

<script>
    $(document).ready(function () {
        console.log("AddPerformanceEvaluation form ready");

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for AddPerformanceEvaluation form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('addPerformanceEvaluationForm');
                if (!form) {
                    console.error("Form not found with ID: addPerformanceEvaluationForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                const feedbackElements = {};

                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        const message = element.textContent.trim();

                        // Primary key: Use input ID
                        if (input.id) {
                            customMessages[input.id] = message;
                            feedbackElements[input.id] = element;
                            console.log("Stored custom message for ID", input.id, ":", message);
                        }

                        // Fallback: Use asp-for attribute
                        const aspFor = input.getAttribute('asp-for');
                        if (aspFor) {
                            customMessages[aspFor] = message;
                            feedbackElements[aspFor] = element;
                            console.log("Stored custom message for asp-for", aspFor, ":", message);
                        }

                        // Store original message on element for direct access
                        element.originalMessage = message;
                    }
                });

                // Helper function to restore custom message for an input
                function restoreCustomMessage(input) {
                    let message = null;
                    let feedbackElement = null;

                    // Priority 1: Use input ID
                    if (input.id && customMessages[input.id]) {
                        message = customMessages[input.id];
                        feedbackElement = feedbackElements[input.id];
                        console.log("Found custom message by ID", input.id, ":", message);
                    }
                    // Priority 2: Use asp-for attribute
                    else if (input.getAttribute('asp-for') && customMessages[input.getAttribute('asp-for')]) {
                        const aspFor = input.getAttribute('asp-for');
                        message = customMessages[aspFor];
                        feedbackElement = feedbackElements[aspFor];
                        console.log("Found custom message by asp-for", aspFor, ":", message);
                    }
                    // Priority 3: Find feedback element in the same form group
                    else {
                        const formGroup = input.closest('.form-group');
                        feedbackElement = formGroup?.querySelector('.invalid-feedback');
                        if (feedbackElement && feedbackElement.originalMessage) {
                            message = feedbackElement.originalMessage;
                            console.log("Found custom message by form group for", input.id || input.getAttribute('asp-for'), ":", message);
                        }
                    }

                    if (message && feedbackElement) {
                        feedbackElement.textContent = message;
                        // Don't use direct style manipulation - let CSS classes handle display
                        console.log("✅ Restored custom message:", message, "for input:", input.id || input.getAttribute('asp-for'));
                        return true;
                    } else {
                        console.warn("❌ Could not restore custom message for input:", input.id || input.getAttribute('asp-for'));
                        return false;
                    }
                }

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // For select fields, only validate if forced or if they have value "0"
                    if (input.tagName === 'SELECT' && !forceValidation) {
                        const value = input.value;
                        if (value && value !== "0") {
                            // Has valid selection, clear any validation errors
                            input.classList.remove('is-invalid');
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                feedbackElement.hide();
                            }
                            return true;
                        }
                    }

                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        restoreCustomMessage(input);
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        restoreCustomMessage(input);
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);

                    // Validate dates
                    const datesValid = validateDates();

                    console.log("Form validation result:", isValid);
                    console.log("Date validation result:", datesValid);

                    if (!isValid || !datesValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });

            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        // Add event listeners for real-time validation on select fields
        $('#ddlITDRName, #ddlMeasureKPI, #ddlRating, #ddlMeasuredBy, #ddlFrequency, #ddlResource, #ddlCorrectiveAction').on('change', function() {
            var $this = $(this);

            // Clear validation state when user makes a selection
            if ($this.val() && $this.val() !== "0") {
                $this.removeClass('is-invalid');
                var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                if (feedbackElement.length > 0) {
                    feedbackElement.hide();
                }
            }
        });

        // Add date validation for From Date, To Date, and Targeted Date
        function validateDates() {
            var fromDate = $('#EffectiveFromDate').val();
            var toDate = $('#EffectiveToDate').val();
            var targetedDate = $('#TargetedDate').val();
            var currentDate = new Date().toISOString().split('T')[0]; // Get current date in YYYY-MM-DD format
            var isValid = true;

            // Clear previous date validation errors
            clearDateValidationErrors();

            // Validate From Date - cannot be less than current date
            if (fromDate && fromDate < currentDate) {
                showDateValidationError('#EffectiveFromDate', 'From Date cannot be earlier than today');
                isValid = false;
            }

            // Validate To Date - cannot be less than From Date
            if (fromDate && toDate && toDate < fromDate) {
                showDateValidationError('#EffectiveToDate', 'To Date cannot be earlier than From Date');
                isValid = false;
            }

            // Validate Targeted Date - cannot be less than current date
            if (targetedDate && targetedDate < currentDate) {
                showDateValidationError('#TargetedDate', 'Targeted Date cannot be earlier than today');
                isValid = false;
            }

            return isValid;
        }

        // Function to show date validation error
        function showDateValidationError(selector, message) {
            var element = $(selector);
            element.addClass('is-invalid').removeClass('is-valid');

            var formGroup = element.closest('.form-group');
            var feedbackElement = formGroup.find('.invalid-feedback');
            if (feedbackElement.length > 0) {
                feedbackElement.text(message);
                // Use CSS classes and show method for proper display
                feedbackElement.addClass('custom-validation show').show();
            }
        }

        // Function to clear date validation errors
        function clearDateValidationErrors() {
            var dateSelectors = ['#EffectiveFromDate', '#EffectiveToDate', '#TargetedDate'];

            dateSelectors.forEach(function(selector) {
                var element = $(selector);
                var formGroup = element.closest('.form-group');
                var feedbackElement = formGroup.find('.invalid-feedback');

                // Only clear if the current message is a date validation error
                if (feedbackElement.length > 0 && (feedbackElement.text().includes('cannot be earlier') || feedbackElement.text().includes('From Date') || feedbackElement.text().includes('To Date') || feedbackElement.text().includes('Targeted Date'))) {
                    element.removeClass('is-invalid');
                    // Use CSS classes for proper hiding
                    feedbackElement.removeClass('custom-validation show').hide();

                    // Restore original message
                    var originalMessages = {
                        '#EffectiveFromDate': 'Enter From Date',
                        '#EffectiveToDate': 'Enter To Date',
                        '#TargetedDate': 'Enter Targeted date'
                    };

                    if (originalMessages[selector]) {
                        feedbackElement.text(originalMessages[selector]);
                    }
                }
            });
        }

        // Add event listeners for date validation
        $('#EffectiveFromDate, #EffectiveToDate, #TargetedDate').on('change blur', function() {
            var $this = $(this);

            // Clear validation state when user makes a selection
            if ($this.val()) {
                $this.removeClass('is-invalid');
                var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be earlier')) {
                    feedbackElement.hide();
                }
            }

            // Small delay to allow the change to complete, then validate dates
            setTimeout(function() {
                validateDates();
            }, 100);
        });

        // Prevent validation messages from showing on page load
        $(window).on('load', function() {
            // Hide all validation messages that might be showing incorrectly
            $('.invalid-feedback').each(function() {
                var $feedback = $(this);
                var $input = $feedback.closest('.form-group').find('input, select');

                // Only hide if the input is not actually invalid
                if ($input.length > 0 && !$input.hasClass('is-invalid')) {
                    $feedback.hide();
                }
            });
        });

        $(document).on('change','#ddlITDRName',function(){
            var ITDRName = $(this).find('option:selected').text();
                $('#ITDRName').val(ITDRName);
        })
    });
</script>
