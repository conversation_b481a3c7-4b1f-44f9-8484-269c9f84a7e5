﻿@model IEnumerable<BCM.BusinessClasses.BIAVitalRecord>
@* @model BCM.BusinessClasses.BIAVitalRecordsViewModel *@
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor

@{
    ViewBag.Title = "Vital Records and Information";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@{
    var ProcessName = HttpContextAccessor.HttpContext.Session.GetString("ProcessNameWithCode");
    var ProcessVersion = HttpContextAccessor.HttpContext.Session.GetString("ProcessVersion");
}


<div class="Page-Condant card border-0">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        <p style="padding-left:1%" class="fw-bold mb-2">Configure Vital Records and Information for @ProcessName</p>
        <div class="align-items-right" style="padding-right:2%">
           @*  <p class="fw-semibold">Version : @ViewBag.BIASectionVersion.Version</p> *@
            <p class="fw-semibold">Version : @ProcessVersion</p>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-12">                
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <div class="accordion accordion-flush" id="accordionFlushExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                            Instructions and Guidelines
                                        </button>
                                    </h2>
                                    <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                                        <div class="accordion-body">                                          
                                            <div id="editor2" class="content-editable">
                                                @Html.Raw(ViewBag.Description)
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="col-12">
                <div>
                   @*  <p class="fw-semibold">Configure Vital Records and Information for @ProcessName</p> *@
                    <div class="">
                        @foreach (var objQusetions in ViewBag.Questions)
                        {
                            <p class="text-primary d-flex align-items-center gap-1">
                                <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsequestion1" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                                Question: @objQusetions.QuestionDetails
                            </p>
                        }

                        <div class="ps-2 collapse show" id="collapsequestion1">

                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Record</th>
                                        <th>Record Owner</th>
                                        <th>Impact</th>
                                        <th>Primary Storage Location</th>
                                        <th>Secondary Storage Location</th>
                                        <th>Type of Record</th>
                                        <th>Alternate Source</th>
                                       @*  <th>Completion Status</th> *@
                                        <th style="text-align:right !important;">Action</th>
                                    </tr>
                                </thead>
                                <tbody id="tblBody">
                                    @if (Model != null && Model.Count() != 0)
                                    {
                                        int iIndex = 0;
                                        foreach (var item in Model)
                                        {
                                            iIndex++;
                                            <tr>
                                                <td>@iIndex</td>
                                                <td>@item.VitalRecordName</td>
                                                <td>@item.ResourceName</td>
                                                <td>@item.Impact</td>
                                                <td>@item.Facility</td>
                                                <td>
                                                    @item.SecondaryStorageLocation
                                                </td>
                                                <td>@item.TypeOfRecord</td>
                                                <td>@item.NeedBy</td>
                                                @{
                                                    var IsComplete = item.IsComplete == 1 ? "Complete" : "Incomplete";
                                                }
                                               
                                               
                                                @* <td class="text-success">
                                                    <i class="cv-success me-1">@IsComplete</i>

                                                </td> *@

                                                <td style="text-align:right !important;">
                                                    <span class="btn-action btnEdit" type="button" data-id="@item.VitalRecordId" data-bs-toggle="NormalModal"><i class="cv-edit" title="Edit"></i></span>
                                                    <span class="btn-action btnDelete" type="button" data-id="@item.VitalRecordId" data-bs-toggle="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                                                </td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="9" class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <img src="~/img/Isomatric/no_records_found.svg" alt="No Records Found" style="width: 120px; height: auto; margin-bottom: 1rem;">
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <form asp-action="AddUpdateVitalRecords" method="post" id="addUpdateVitalRecords" class="needs-validation progressive-validation" novalidate>
                    <div class="row row-cols-2">
                        @* <div class="col">
                            <div class="form-group">                                                               
                                <label class="form-lable">Version</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-version"></i></span>
                                    <input class="form-control" type="text" name="Version" readonly
                                           value="@ViewBag.BIASectionVersion.Version" />
                                </div>
                            </div>
                        </div> 
                        <div class="col"></div> *@
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Questions</label>
                                @foreach (var objQusetions in ViewBag.Questions)
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" checked="checked" name="QuestionID" id="@objQusetions.ID" value="@objQusetions.ID">
                                        <label class="form-check-label" for="inlineRadio1">@objQusetions.QuestionDetails</label>
                                    </div>
                                }
                            </div>
                        </div>
                        <div class="col"></div>
                        <div class="col">
                            <div class="form-group">
                                <input type="hidden" name="VitalRecordId" id="VitalRecordId" value="" />
                                <label class="form-lable">Vital Record Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-records"></i></span>                                 
                                    <input class="form-control" type="text" name="VitalRecordName" placeholder="Enter Vital Record Name" required pattern="^[A-Za-z\s]+$" title="Record Name must contain only alphabetic characters and spaces." />
                                </div>
                                <div class="invalid-feedback">Enter Vital Record Name</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Description" name="Description" style="height:0px"></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Description</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Record Owner</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-owner"></i></span>
                                    <select class="form-select-sm form-control selectized" name="RecordOwnerID" id="RecordOwnerName" required>
                                        <option value="" disabled selected>Select Record Owner</option>
                                        @foreach (var name in ViewBag.RecordOwnerNames)
                                        {
                                            <option value="@name.ResourceID">@name.ResourceName</option>
                                        }
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select Record Owner</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Type of Record</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-type"></i></span>
                                    <select class="form-select-sm form-control selectized" name="TypeOfRecord" id="TypeOfRecord" required>
                                        <option value="" disabled selected>Select Type of Record</option>
                                        @foreach (var name in ViewBag.TypeOfRecord)
                                        {
                                            <option value="@name.Value">@name.Text</option>
                                        }                                     
                                    </select>                                   
                                </div>
                                <div class="invalid-feedback">Select Type of Record</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Impact</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-impact"></i></span>
                                    <select class="form-select-sm form-control selectized" name="Impact" required>
                                        <option value="" disabled selected>Select Impact</option>
                                        @foreach (var name in ViewBag.Impact)
                                        {
                                            <option value="@name.Value">@name.Text</option>
                                        }                                       
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select Impact</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    Primary Storage Location
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-storage-location"></i></span>
                                    <textarea class="form-control" placeholder="Enter Primary Storage Location" name="Facility" style="height:0px" required></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Primary Storage Location</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    Secondary Storage Location
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-storage-location"></i></span>
                                    <textarea class="form-control" placeholder="Enter Secondary Storage Location" name="SecondaryStorageLocation" style="height:0px" required ></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Secondary Storage Location</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    Alternate Source Method for Generation
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-source"></i></span>
                                    <textarea class="form-control" placeholder="Enter Alternate Source Method for Generation" name="NeedBy" style="height:0px" required></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Alternate Source Method for Generation</div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="text-end me-4 pb-3">
                                <a class="btn btn-sm btn-outline-primary" role="button" formnovalidate asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>

                                <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit">Save</button>
                                <button class="btn btn-sm btn-secondary" id="btnCancel" formnovalidate >Cancel</button>
                               
                                @* <a role="button" class="btn btn-sm btn-primary" asp-action="ManageBusinessProcess" asp-controller="ManageBusinessProcesses" asp-area="BCMProcessBIA" formnovalidate>View All</a> *@
                              
                               
                            </div>
                        </div>
                    </div>
                </form>                
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
            </div>
        </div>
    </div>
</div>


@section Scripts 
{
    <script>
        $(document).ready(function () {

             // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function() {
                        console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function() {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                console.log("Initializing validation for addUpdateVitalRecords form");

                if (window.BCMValidation) {
                    console.log("BCMValidation found, initializing");

                    // Get the form element
                    const form = document.getElementById('addUpdateVitalRecords');
                    if (!form) {
                        console.error("Form not found with ID: addUpdateVitalRecords");
                        return;
                    }

                    // Store the original custom messages from invalid-feedback divs
                    const customMessages = {};
                    form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                        const originalMessage = element.textContent.trim();
                        console.log("Processing invalid-feedback element:", originalMessage);

                        // Find the associated input by looking in the form group
                        const formGroup = element.closest('.form-group');
                        let input = formGroup?.querySelector('input[required], select[required], textarea[required]');

                        // If not found, look for any input in the form group
                        if (!input) {
                            input = formGroup?.querySelector('input, select, textarea');
                        }

                        if (input) {
                            // Store the custom message using multiple keys for reliability
                            const keys = [
                                input.id,
                                input.name,
                                input.getAttribute('asp-for')
                            ].filter(key => key); // Remove null/undefined values

                            keys.forEach(key => {
                                customMessages[key] = originalMessage;
                                console.log("Stored custom message for key", key, ":", originalMessage);
                            });
                        } else {
                            console.log("No input found for invalid-feedback:", originalMessage);
                        }
                    });

                    // Function to restore custom message for an input
                    function restoreCustomMessage(input) {
                        const keys = [
                            input.id,
                            input.name,
                            input.getAttribute('asp-for')
                        ].filter(key => key);

                        for (let key of keys) {
                            if (customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                    return true;
                                }
                            }
                        }
                        return false;
                    }

                    // Function to restore all custom messages after validation
                    function restoreAllCustomMessages() {
                        form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                            // Small delay to ensure the global validation has finished
                            setTimeout(() => {
                                restoreCustomMessage(input);
                            }, 10);
                        });
                    }

                    // Initialize the validation framework
                    window.BCMValidation.init();

                    // Add required field indicators (asterisks)
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for pattern-based inputs
                    window.BCMValidation.addFormatIndicators(form);

                    // Add user interaction validation for inputs
                    const allInputs = form.querySelectorAll('input:not([type="hidden"]), select, textarea');
                    allInputs.forEach(function(input) {
                        // Add blur event listener to mark field as touched and validate
                        input.addEventListener('blur', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                // Mark field as touched and remove validation-pending
                                formGroup.classList.add(window.BCMValidation.classes.fieldTouchedClass);
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            // Validate the input using global validation
                            if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }

                            // Restore custom message after a short delay
                            setTimeout(() => {
                                restoreCustomMessage(this);
                            }, 20);
                        });

                        // Add input event listener for real-time validation (only after field is touched)
                        input.addEventListener('input', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup && formGroup.classList.contains(window.BCMValidation.classes.fieldTouchedClass)) {
                                // Validate the input
                                if (this.hasAttribute('pattern')) {
                                    window.BCMValidation.validatePatternInput(this);
                                } else {
                                    window.BCMValidation.validateInput(this);
                                }

                                // Restore custom message after a short delay
                                setTimeout(() => {
                                    restoreCustomMessage(this);
                                }, 20);
                            }
                        });
                    });

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");

                        // Show all validation messages
                        window.BCMValidation.showAllValidationMessages(form);

                        // Validate the form using global validation
                        const isValid = window.BCMValidation.validateForm(form);

                        // Restore all custom messages after validation
                        restoreAllCustomMessages();

                        console.log("Form validation result:", isValid);

                        if (!isValid) {
                            console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                        }
                    });
                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }

            
            $(document).on('click', '.btnEdit', function () {
                UpdateButtonLabel(); 

                var id = $(this).data('id');
                // Clear validation errors before populating new values
                const form = document.getElementById('addUpdateVitalRecords');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Add validation-pending class to hide messages until user interaction
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }

               
                //$.get('/BCMProcessBIAForms/BIAVitalRecords/GetVitalRecordByID/', { id: id })
                $.get('@Url.Action("GetVitalRecordByID", "BIAVitalRecords")', { id: id })
                    .done(function (data) {
                        if (data) {
                            
                            $('#VitalRecordId').val(data.vitalRecordId);
                            $('input[name="VitalRecordName"]').val(data.vitalRecordName);
                            $('textarea[name="Description"]').val(data.description);
                            $('#RecordOwnerName').val(data.recordOwnerID);
                            $('#TypeOfRecord').val(data.typeOfRecord);
                            $('select[name="Impact"]').val(data.impact);
                            $('textarea[name="Facility"]').val(data.facility);
                            $('textarea[name="SecondaryStorageLocation"]').val(data.secondaryStorageLocation);
                            $('textarea[name="NeedBy"]').val(data.needBy);
                            $('input[name="QuestionID"][value="' + data.questionID + '"]').prop('checked', true);
                            UpdateButtonLabel();
                        }
                    })
                    .fail(function () {
                        console.error('Failed to fetch data.');
                    });                   
            });

         
            $(document).on('click', '.btnDelete', function () {
                var id = $(this).data('id'); 

                
                //$.get('/BCMProcessBIAForms/BIAVitalRecords/DeleteBIAVitalRecord/', { id: id })
                $.get('@Url.Action("DeleteBIAVitalRecord", "BIAVitalRecords")', { id: id })
                    .done(function (data) {
                        $('.modal-body').html(data); 
                        $('#DeleteModal').modal('show'); 
                        $('#modelTitle').text('Delete Vital Records and Information'); 
                    })
                    .fail(function () {
                        console.error('Failed to fetch delete confirmation.');
                    });
            });

            $(document).on('click', '#btnCancel', function (event) {
                event.preventDefault();

                // Clear validation errors when canceling
                const form = document.getElementById('addUpdateVitalRecords');
                if (form && window.BCMValidation) {
                    // Clear validation classes
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    // Hide all validation messages
                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                        feedback.style.display = 'none';
                    });

                    // Reset validation state
                    form.querySelectorAll('.form-group').forEach(function (formGroup) {
                        formGroup.classList.add(window.BCMValidation.classes.validationPendingClass);
                        formGroup.classList.remove(window.BCMValidation.classes.fieldTouchedClass);
                    });
                }

                location.reload();
            });

           
            function UpdateButtonLabel() {
                
                
                var id = $('#VitalRecordId').val();
                if (id && id > 0) {
                    $('#btnSubmit').text('Update');
                } else {
                    $('#btnSubmit').text('Save');
                }
            }

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
        });
    </script>
}
