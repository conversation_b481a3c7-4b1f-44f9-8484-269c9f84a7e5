﻿@model BCM.BusinessClasses.MenuRights
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@* <form asp-action="DeleteSubMenuById" method="post">
    <div>
        <input type="hidden" asp-for="SubMenuID" />
    </div>
    <div class="modal-header d-grid text-center">

        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold">@Model.SubMenuName</span>" ?</span>
    </div>
    <div class="modal-body text-center">
        <img src="~/img/isomatric/delete.svg" width="260" /> 
    </div>
    <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>

</form> *@


<form asp-action="DeleteSubMenuById" method="post">
    <div>
        <input type="hidden" asp-for="@Model.SubMenuID" />
    </div>
    <div class="modal-header p-0">
        <img src="~/img/isomatric/delete.svg" class="delete-img mx-auto" />
    </div>
    <div class="modal-body d-grid px">
        <span class="fw-semibold">Do you really want to delete</span>
        <span>"<span class="text-primary fw-semibold" id="OrgGroupSpan">@Model.SubMenuName</span>" ?</span>
    </div>
    <div class="modal-footer justify-content-center p-0">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary btn-sm">Delete</button>
    </div>
</form>
