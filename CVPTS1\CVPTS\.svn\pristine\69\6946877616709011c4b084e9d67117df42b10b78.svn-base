.table>:not(caption)>*>* {
    background-color: var(--bs-white);
    padding: .6rem .6rem;
}

.table-sm > :not(caption) > * > * {
    padding: .25rem .25rem !important;
}

table.dataTable.table-hover > tbody > tr:hover > * {
    box-shadow: inset 0 0 0 9999px #fff4f9;
}

.table > tbody > tr:nth-of-type(odd) > * {
    --bs-table-color-type: var(--bs-table-striped-color);
    --bs-table-bg-type: rgb(0 0 0 / 5%);
}


/*
td.dataTables_empty {
    background-image: url(../../img/Isomatric/no_records_found.svg);
    background-repeat: no-repeat;
    --bs-table-bg-type: rgb(255 255 255 / 5%) !important;
    height: calc(60vh);
    width: 100%;
    background-position: center;
    border: #ffffff;
    color: white;
}*/

td.dataTables_empty {
    background-image: url('../../img/Isomatric/no_records_found.svg');
    background-repeat: no-repeat;
    background-position: center;
    height: calc(60vh);
    text-indent: -9999px;
    border:none;
}

.table .dataTables_empty {
    box-shadow: none;
    background-color: transparent;
    --bs-table-bg-type: rgb(255 255 255 / 0%);
    border: none;
}

table.dataTable.table-hover > tbody > tr:hover > .dataTables_empty {
    box-shadow: none;
    background-color: transparent;
    --bs-table-bg-type: rgb(255 255 255 / 0%);
}


.Incident-bg .table > tbody > tr:nth-of-type(odd) > * {
    --bs-table-color-type: var(--bs-table-striped-color);
    --bs-table-bg-type: rgb(255 255 255 / 5%) !important;
}


.dropdown-menu .table > tbody > tr:nth-of-type(odd) > * {
    --bs-table-bg-type: rgb(255 255 255 / 5%) !important;
}

.Map-Table > :not(caption) > * > * {
    padding: 3px 6px;
}

.dataTables_filter{
    display: none;
}

.dataTables_scrollBody {
    max-height: calc(100vh - 206px);
    height: calc(100vh - 206px);
}

.dataTables_info{
    padding:0px !important;
}

table.dataTable {
    margin-top: 0px !important;
}

.table-profile{
    width: 22px;
    height: 22px;
    border-radius: 50%;
    margin-right:5px;
}

table thead th {
    font-weight: 500;
    color: #9b227c !important;
}

table.dataTable thead th, table.dataTable thead td, table.dataTable tfoot th, table.dataTable tfoot td {
    font-weight: 500;
    color: var(--bs-primary-bg-subtle);
}


.Workbench-head tr th {
    text-align: center;
}

.SrNo_th {
    width: 40px !important;
}


table tr th:first-child{
    border-radius:var(--bs-border-radius-first-child) ;
}

table tr th:last-child{
    border-radius:var(--bs-border-radius-last-child) ;
}

.truncate {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table .btn-sm {
    --bs-btn-padding-y: 0.1rem;
    --bs-btn-padding-x: 0.4rem;
    --bs-btn-font-size: 0.8rem;
}

.dropdown-menu .table {
    width:max-content;
}
/* Pagination Color */
.pagination {
    --bs-pagination-border-width: none;
    --bs-pagination-bg: transparent;
    --bs-pagination-border-radius: var(--bs-border-radius);
}

.page-link {
    border-radius: 4px !important;
    color: var(--bs-primary);
}

.pagination-sm {
    --bs-pagination-padding-x: 0.5rem;
    --bs-pagination-padding-y: 0.25rem;
    --bs-pagination-font-size: 0.7rem;
    --bs-pagination-border-radius: var(--bs-border-radius);
    gap: 0.5rem;
}

.page-link:hover {
    color: var(--bs-white);
    background: rgb(230, 56, 117);
    background: linear-gradient(114deg, rgba(230, 56, 117, 1) 4%, rgba(50, 2, 132, 1) 100%);
}

.active>.page-link, .page-link.active {
    background: rgb(230, 56, 117);
    background: linear-gradient(114deg, rgba(230, 56, 117, 1) 4%, rgba(50, 2, 132, 1) 100%);
}

/* End Pagination Color */