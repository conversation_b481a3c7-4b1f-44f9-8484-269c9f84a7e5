﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Shared;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata;

namespace BCM.UI.Areas.BCMCompliance.Controllers;
[Area("BCMCompliance")]
public class ReviewTypeMasterFormController : Controller
    {


    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    ManageUsersDetails _UserDetails = new ManageUsersDetails();
    

    public ReviewTypeMasterFormController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger cVLogger)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _UserDetails = _Utilities.LoginUserDetails();
        if (_UserDetails == null)
        {
            RedirectToAction("Login", "Login");
        }
        _CVLogger = cVLogger;
    }

    [HttpGet]
    public IActionResult ReviewTypeMasterForm()
    {
        try
        {
            List<ReviewTypeMaster> lstReviewTypeMaster = _ProcessSrv.GetReviewTypeAll(Convert.ToInt32(_UserDetails.OrgID));
            ViewBag.ReviewTypeMaster = lstReviewTypeMaster;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
    }

    [HttpGet]
    public IActionResult AddReviewTypeMasterForm()
    {
        try
        {
            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddReviewTypeMasterForm", new ReviewTypeMaster());
    }

    [HttpPost]
    public IActionResult AddReviewTypeMasterForm(ReviewTypeMaster objReviewTypeMaster)
    {
        bool bSuccess = false;
        try
        {
            objReviewTypeMaster.ReviewTypeID = "0";//string.IsNullOrEmpty(lblReviewTypeID.Text) == false ? lblReviewTypeID.Text : "0";
            objReviewTypeMaster.IsActive = "1";
            objReviewTypeMaster.OrgID = _UserDetails.OrgID.ToString();
            objReviewTypeMaster.CreatedBy = _UserDetails.UserID.ToString();

            bSuccess = _ProcessSrv.ReviewTypeMaster_Save(objReviewTypeMaster);
        }
        catch(Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objReviewTypeMaster.ReviewType + " Added Successfully" : "Failed To Add Review Type." });
        }
        return RedirectToAction("ReviewTypeMasterForm");
    }


    [HttpGet]
    public IActionResult EditReviewTypeMasterForm(string strReviewTypeID)
    {
        ReviewTypeMaster objReviewTypeMaster = new ReviewTypeMaster();
        try
        {
            if (Convert.ToInt32(Convert.ToInt32(strReviewTypeID)) > 0)
            {
                objReviewTypeMaster = _ProcessSrv.GetReviewType_ByReviewTypeID(strReviewTypeID, _UserDetails.OrgID.ToString());
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_EditReviewTypeMasterForm", objReviewTypeMaster);
    }

    [HttpPost]
    public IActionResult EditReviewTypeMasterForm(ReviewTypeMaster objReviewTypeMaster)
    {
        bool bSuccess = false;
        try
        {
            //objReviewTypeMaster.ReviewTypeID = "0";//string.IsNullOrEmpty(lblReviewTypeID.Text) == false ? lblReviewTypeID.Text : "0";
            objReviewTypeMaster.IsActive = "1";
            objReviewTypeMaster.OrgID = _UserDetails.OrgID.ToString();
            objReviewTypeMaster.CreatedBy = _UserDetails.UserID.ToString();

            bSuccess = _ProcessSrv.ReviewTypeMaster_Save(objReviewTypeMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objReviewTypeMaster.ReviewType + " Update Successfully" : "Failed To Update Review Type." });
        }
        return RedirectToAction("ReviewTypeMasterForm");
    }

    [HttpGet]
    public IActionResult DeleteReviewTypeMasterForm(string strReviewTypeID)
    {
        ReviewTypeMaster objReviewTypeMaster = new ReviewTypeMaster();
        try
        {
            if (Convert.ToInt32(strReviewTypeID) > 0)
            {
                objReviewTypeMaster = _ProcessSrv.GetReviewType_ByReviewTypeID(strReviewTypeID, _UserDetails.OrgID.ToString());
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        // return PartialView("_DeleteReviewTypeMasterForm", objReviewTypeMaster);
        return PartialView("_DeleteReviewTypeMasterForm", objReviewTypeMaster);
    }

    [HttpPost]
    public IActionResult DeleteReviewTypeMasterForm(ReviewTypeMaster objReviewTypeMaster)
    {
        bool bSuccess = false;
        try
        {
            string ReviewTypeID = objReviewTypeMaster.ReviewTypeID;
            bSuccess = _ProcessSrv.DeleteReviewTypeMaster(Convert.ToInt32(ReviewTypeID), Convert.ToInt32(_UserDetails.UserID));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objReviewTypeMaster.ReviewType + " Delete Successfully" : "Failed To Delete Review Type." });
        }
        return RedirectToAction("ReviewTypeMasterForm");
    }
}

