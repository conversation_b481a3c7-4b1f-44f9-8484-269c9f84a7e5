﻿using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using BCM.BusinessClasses;

namespace BCM.UI.Areas.BCMIncidentManagement.Services
{
    public class ApiService
    {
        private readonly HttpClient _HttpClient;
        private readonly IConfiguration _Configuration;
        private readonly ILogger<ApiService> _Logger;
        private readonly string _BaseUrl;
        private readonly CVIncidentManagement _CVIncidentManagement;

        public ApiService(HttpClient HttpClient, IConfiguration Configuration, ILogger<ApiService> Logger, CVIncidentManagement CVIncidentManagement)
        {
            _HttpClient = HttpClient;
            _Configuration = Configuration;
            _Logger = Logger;
            _BaseUrl = _Configuration["ApiSettings:BaseUrl"];
            _CVIncidentManagement = CVIncidentManagement;
        }

        public bool NotifyIncident(NotifyIncident notifyIncident)
        {
            try
            {
                var apiUrl = _BaseUrl + "/api/NotifyIncidentAPI/NotifyIncident";
                var jsonContent = JsonSerializer.Serialize(notifyIncident);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = _HttpClient.PostAsync(apiUrl, content).GetAwaiter().GetResult();
                response.EnsureSuccessStatusCode();

                var responseString = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
                return bool.Parse(responseString);
            }
            catch (Exception ex)
            {
                _Logger.LogError(ex, "Error occurred while notifying incident.");
            }
            return true;
        }

        public bool NotifyIncidentNew(NotifyIncident notifyIncident)
        {
            try
            {
                _CVIncidentManagement.NotifyIncidentByIncidentID(notifyIncident);
            }
            catch (Exception ex)
            {
                _Logger.LogError(ex, "Error occurred while notifying incident.");
            }
            return true;
        }

        public bool UpdateStepStatus(RecoveryTaskStepInfo objStepInfo, int iOrgID)
        {
            try
            {
                var apiUrl = _BaseUrl + "/api/NotifyIncidentAPI/UpdateStepStatus";
                var jsonContent = JsonSerializer.Serialize(objStepInfo);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = _HttpClient.PostAsync(apiUrl, content).GetAwaiter().GetResult();
                response.EnsureSuccessStatusCode();

                var responseString = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
                return bool.Parse(responseString);
            }
            catch (Exception ex)
            {
                _Logger.LogError(ex, "Error occurred while updating step status.");
            }
            return true;
        }
        public bool UpdateStepStatusNew(RecoveryTaskStepInfo objStepInfo, int iOrgID)
        {
            try
            {
                _CVIncidentManagement.UpdateStepStatus(objStepInfo, iOrgID);
            }
            catch (Exception ex)
            {
                _Logger.LogError(ex, "Error occurred while updating step status.");
            }
            return true;
        }
    }
}
