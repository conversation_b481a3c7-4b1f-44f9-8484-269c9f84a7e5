﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.DotNet.Scaffolding.Shared.Messaging;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class DashboardBuilderController : BaseController
{
    private readonly Utilities _Utilities;
    private ProcessSrv _ProcessSrv;
    readonly CVLogger _CVLogger;
    private readonly ILoggerFactory? _LoggerFactory;

    public DashboardBuilderController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult DashboardBuilder()
    {
        return View();
    }


}
