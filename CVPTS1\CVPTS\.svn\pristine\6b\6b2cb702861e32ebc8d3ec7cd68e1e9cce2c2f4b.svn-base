﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Data;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class BIAQuestionsController : BaseController
{

    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    List<ProcessBIAThirdParty> lstProcessBIAThirdParty = new List<ProcessBIAThirdParty>();

    public BIAQuestionsController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;        
        _CVLogger = CVLogger;
    }
    [HttpGet]
    public IActionResult BIAQuestions()
    {
        BindSectionList();
        BindQuestionList();
        return View();
    }

    [HttpGet]
    public IActionResult SaveBIAQuestion(int id)
    {
        BIASurveyQuestion objBIASurveyQuestion = new BIASurveyQuestion();
        try
        {

            if (id > 0)
            {
                List<BIASurveyQuestion> objBIASurveyQuestionColl = _ProcessSrv.GetBIASurveyQuestionList(Convert.ToInt32(_UserDetails.OrgID));
                List<BIASurveyQuestion> obj = objBIASurveyQuestionColl.Where(x => x.ID == id).ToList();

                foreach (BIASurveyQuestion item in obj)
                {
                    objBIASurveyQuestion = item;
                }
                objBIASurveyQuestion.ChangedBy = _UserDetails.UserID;
            }
            else
            {

            }
            List<BIASection> objBIASectionColl = _ProcessSrv.GetBIASurveyQuestionSectionList();

            ViewBag.OrgInfo = new SelectList(objBIASectionColl, "SectionID", "SectionName");
            ViewBag.lstBIASection = objBIASectionColl;
            //PopulateDropDowns();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddQuestion", objBIASurveyQuestion);
    }

    [HttpPost]
    public IActionResult SaveBIAQuestion(BIASurveyQuestion objBIASurveyQuestion)
    {
        try
        {
            _ProcessSrv.BIASurveyQuestionSave(objBIASurveyQuestion);
            //PopulateDropDowns();
            BindSectionList();
            BindQuestionList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("BIAQuestions", new BIASurveyQuestion());
    }

    private void BindSectionList()
    {
        try
        {
            List<BIASection> objBIASectionColl = _ProcessSrv.GetBIASurveyQuestionSectionList();
            ViewBag.OrgInfo = new SelectList(objBIASectionColl, "SectionID", "SectionName");
            ViewBag.lstBIASection = objBIASectionColl;
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
    }

    private void BindQuestionList()
    {
        try
        {
            List<BIASurveyQuestion> objBIASurveyQuestionColl = _ProcessSrv.GetBIASurveyQuestionList(Convert.ToInt32(_UserDetails.OrgID));
            ViewBag.lstBIAQuestion = objBIASurveyQuestionColl;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
    [HttpGet]
    public IActionResult DeleteBIAQuestion(int id)
    {
        BIASurveyQuestion objBIASurveyQuestion = new BIASurveyQuestion();
        List<BIASurveyQuestion> objBIASurveyQuestionColl = _ProcessSrv.GetBIASurveyQuestionList(Convert.ToInt32(_UserDetails.OrgID));
        List<BIASurveyQuestion> obj = objBIASurveyQuestionColl.Where(x => x.ID == id).ToList();

        foreach (BIASurveyQuestion item in obj)
        {
            objBIASurveyQuestion = item;
        }

        return PartialView("_DeleteBIAQuestion", objBIASurveyQuestion);
    }

    [HttpPost]
    public IActionResult DeleteBIAQuestion(BIASurveyQuestion objBIASurveyQuestion)
    {
        try
        {
            _ProcessSrv.BIASurveyQuestionDelete(Convert.ToInt32(string.IsNullOrEmpty(objBIASurveyQuestion.ID.ToString()) ? "0" : objBIASurveyQuestion.ID.ToString()), Convert.ToInt32(_UserDetails.UserID));
            BindSectionList();
            BindQuestionList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("BIAQuestions");
    }
}



