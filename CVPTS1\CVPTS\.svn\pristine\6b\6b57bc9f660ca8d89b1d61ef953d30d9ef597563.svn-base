﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using DevExpress.CodeParser;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Security.Policy;

namespace BCM.UI.Areas.BCMTraining.Controllers;

[Area("BCMTraining")]
public class ManageBCMTrainingFormController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public ManageBCMTrainingFormController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult ManageBCMTrainingForm()
    {
        var lstBCMTrainingMaster = new List<BCMTrainingMaster>();

        try
        {
            PopulateDropdown();
            lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstBCMTrainingMaster);
    }

    [HttpGet]
    public IActionResult AddBCMTrainingMaster()
    {
        try
        {
            PopulateDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_AddBCMTraining", new BCMTrainingMaster());
    }

    [HttpPost]
    public IActionResult AddBCMTrainingMaster(BCMTrainingMaster objBCMTrainingMaster)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.SaveTrainingMaster(objBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }

    [HttpGet]
    public IActionResult GetPublishMasterByTrainingID(int trainingId)
    {
        try
        {
            // Log the received parameter
            //_CVLogger.LogInfoApp($"GetPublishMasterByTrainingID called with trainingId: {trainingId}");

            // Get existing publish data for the training
            BCMTrainingMaster publishData = _ProcessSrv.BCMTrainingMasterPublish_getByID(trainingId);

            if (publishData != null && publishData.PublishID > 0)
            {
                // Return existing publish data
                var responseData = new
                {
                    trainingId = trainingId,
                    publishDate = publishData.PublishDate.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd"),
                    isVendor = publishData.ReleaseToVendore == 1,
                    publishID = publishData.PublishID
                };

                //_CVLogger.LogInfoApp($"Existing publish data found for training {trainingId}: PublishDate={responseData.PublishDate}, IsVendor={responseData.IsVendor}, PublishID={responseData.PublishID}");

                return Json(new
                {
                    success = true,
                    data = responseData,
                    message = "Existing publish data retrieved successfully"
                });
            }
            else
            {
                // Return default data for new publish
                var defaultData = new
                {
                    trainingId = trainingId,
                    publishDate = DateTime.Now.ToString("yyyy-MM-dd"), // Default to today
                    isVendor = false, // Default to false
                    publishID = 0
                };

                //_CVLogger.LogInfoApp($"No existing publish data found for training {trainingId}, returning defaults");

                return Json(new
                {
                    success = true,
                    data = defaultData,
                    message = "Default publish data prepared"
                });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new
            {
                success = false,
                message = "An error occurred while retrieving publish data. Please try again."
            });
        }
    }

    [HttpPost]
    public IActionResult PublishClick(string iId, string date, bool chkVendor)
    {
        bool bSuccess = false;
        string message = "";

        try
        {
            // Log the received parameters
            //_CVLogger.LogInfoApp($"PublishClick called with iId: {iId}, date: {date}, chkVendor: {chkVendor}");

            // TODO: Implement actual publish logic here
            // Example: bSuccess = _ProcessSrv.PublishTraining(Convert.ToInt32(iId), date, chkVendor);

            // For now, simulate success

            BCMTrainingMaster obj = new BCMTrainingMaster();
            BCMTrainingMaster Publish = _ProcessSrv.BCMTrainingMasterPublish_getByID(Convert.ToInt32(iId));

            if (Publish.PublishID == 0)
            {
                obj = _ProcessSrv.BCMTrainingMaster_getByID(Convert.ToInt32(iId));

                Publish.ID = "0";
                Publish.TrainingMasterID = Convert.ToInt32(iId);
                Publish.ReleaseToVendore = chkVendor == true ? 1 : 0;
                Publish.PublishDate = Convert.ToDateTime(date);
                int PublishID = _ProcessSrv.SaveTrainingPublishMaster(Publish);

                List<ResourcesInfo> ResourceInfoColl = new List<ResourcesInfo>();

                if (obj.UnitID == 0)
                {
                    ResourceInfoColl = _ProcessSrv.GetResourceInfoListByOrgID(obj.OrgID);
                }
                else if (obj.DepartmentID == 0)
                {
                    ResourceInfoColl = _ProcessSrv.GetResourcesListByUnitID(obj.UnitID);
                }
                else
                {
                    ResourceInfoColl = _ProcessSrv.GetResourcesListByDepartmentID(obj.DepartmentID);
                }

                //Check Release For Vendore
                if (chkVendor)
                {
                    foreach (ResourcesInfo Resource in ResourceInfoColl)
                    {
                        if (!Resource.VendorID.Equals("0"))
                        {
                            bool sucess = UserExamMaster(Resource.ResourceId.ToString(), PublishID);
                            // string strBody = GetMailBody(Resource.ResourceName , TrainingID.ToString() , Resource.ResourceID , 54 , "0" , obj.TrainingName , Resource.ResourceName , "0");
                            //  BCPMail.SendMail("BCM Training Initiated : " + obj.TrainingName , strBody , Resource.CompanyEmail , "" , "" , "" , Resource.OrgID , "0" , "" , _oUser.UserID , "");

                        }
                    }
                }
                else
                {
                    foreach (ResourcesInfo Resource in ResourceInfoColl)
                    {
                        bool sucess = UserExamMaster(Resource.ResourceId.ToString(), PublishID);
                        // string strBody = GetMailBody(Resource.ResourceName , TrainingID.ToString() , Resource.ResourceID , 54 , "0" , obj.TrainingName , Resource.ResourceName , "0");
                        // BCPMail.SendMail("BCM Training Initiated : " + obj.TrainingName , strBody , Resource.CompanyEmail , "" , "" , "" , Resource.OrgID , "0" , "" , _oUser.UserID , "");
                    }
                }
                bSuccess = true;
                message = "Training published successfully!";
            }
            else
            {
                bSuccess = false;
                message = "Training Already published !";
            }


            //_CVLogger.LogInfoApp($"Training {iId} published successfully on {date}, Vendor: {chkVendor}");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
            message = "An error occurred while publishing the training. Please try again.";
        }

        // Return JSON response for AJAX call
        return Json(new { success = bSuccess, message = message });
    }

    protected JsonResult GetPublishMasterByTrainingID(string iId)
    {
        BCMTrainingMaster Publish = new BCMTrainingMaster();
        try
        {
            Publish = _ProcessSrv.BCMTrainingMasterPublish_getByID(Convert.ToInt32(iId));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

        }

        return Json(Publish);
    }

    protected bool UserExamMaster(string userID, int publishID)
    {
        bool Sucess = false;
        try
        {

            // TrainingID = Utilities.UrlUtility.GetDecrptedIDForURL(Request.QueryString["TrainingID"].ToString());
            int LastInsertedIDofMaster = 0;
            BCMTrainingMaster obj = new BCMTrainingMaster();
            obj.UserID = Convert.ToInt32(userID);

            //obj.TrainingID = Convert.ToInt32(TrainingID);
            obj.ExamStartTime = DateTime.Now;
            obj.ExamEndTime = DateTime.Now;
            obj.Result = "";
            obj.TotalQuestionCount = "";
            obj.AttemptedQuestionCount = "";
            obj.CorrectAttemptedQstnCount = "";
            obj.FailedQstnAttempted = "";
            obj.PublishID = publishID;
            LastInsertedIDofMaster = _ProcessSrv.SaveUserExamMaster(obj);

            if (LastInsertedIDofMaster > 0)
            {
                Sucess = true;
                //ViewState["userMasterID"] = LastInsertedIDofMaster.ToString();
                //   Sucess = SaveUserExamChild(LastInsertedIDofMaster);
            }
            else
            {

            }

            return Sucess;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Sucess;
        }
    }


    [HttpGet]
    public IActionResult EditBCMTrainingMaster(int iId)
    {
        BCMTrainingMaster objBCMTrainingMaster = new BCMTrainingMaster();

        try
        {
            PopulateDropdown();

            objBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getByID(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_EditBCMTraining", objBCMTrainingMaster);
    }

    [HttpPost]
    public IActionResult EditBCMTrainingMaster(BCMTrainingMaster objBCMTrainingMaster)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.SaveTrainingMaster(objBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }


    [HttpGet]
    public IActionResult DeleteBCMTrainingMaster(string iId)
    {
        BCMTrainingMaster objBCMTrainingMaster = new BCMTrainingMaster();

        try
        {
            // Convert string ID to int for the service call
            if (int.TryParse(iId, out int trainingId))
            {
                objBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getByID(trainingId);

                // Ensure both ID properties are set correctly
                objBCMTrainingMaster.ID = iId;
                objBCMTrainingMaster.TrainingMasterID = trainingId;

                //   _CVLogger.LogInfoApp($"Retrieved training for delete: ID={iId}, TrainingMasterID={trainingId}, Name={objBCMTrainingMaster.TrainingName}");
            }
            else
            {
                //     _CVLogger.LogErrorApp($"Invalid training ID format: {iId}");
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteBCMTraining", objBCMTrainingMaster);
    }

    [HttpPost]
    public IActionResult DeleteBCMTrainingMaster(string ID, int TrainingMasterID, string TrainingName, int Status, int OwnerID)
    {
        bool TrainingMasterDelete = false;
        bool bQuestionsDelete = false;
        bool bDeleteOptions = false;
        string message = "";

        try
        {
            // Log received data for debugging
            //_CVLogger.LogInfoApp($"DELETE POST - Received: ID={ID}, TrainingMasterID={TrainingMasterID}, TrainingName={TrainingName}, Status={Status}, OwnerID={OwnerID}");

            int TrainingID = TrainingMasterID;

            // Validate that we have a valid TrainingID
            if (TrainingID <= 0)
            {
                // Try to use ID if TrainingMasterID is not set
                if (int.TryParse(ID, out int parsedId))
                {
                    TrainingID = parsedId;
                    //    _CVLogger.LogInfoApp($"Using parsed ID as TrainingID: {TrainingID}");
                }
                else
                {
                    //   _CVLogger.LogErrorApp($"Invalid TrainingID: TrainingMasterID={TrainingMasterID}, ID={ID}");
                    message = "Invalid training ID. Cannot delete training.";
                    TempData["DeleteMessage"] = message;
                    TempData["DeleteSuccess"] = false;
                    return RedirectToAction("ManageBCMTrainingForm");
                }
            }

            //_CVLogger.LogInfoApp($"Attempting to delete BCM Training with ID: {TrainingID}, Name: {TrainingName}");

            // Delete the training master using the specified method
            TrainingMasterDelete = _ProcessSrv.BCMTrainingMaster_DeleteByID(TrainingID);

            if (TrainingMasterDelete)
            {
                // Delete related questions and options
                List<BCMTrainingMaster> objQuestiondetails = _ProcessSrv.QuestionsDetailsGetByTrainingMasterID(TrainingID);
                bQuestionsDelete = _ProcessSrv.BCMTrainingQuestion_DeleteByTrainingID(TrainingID);

                foreach (BCMTrainingMaster item in objQuestiondetails)
                {
                    bDeleteOptions = _ProcessSrv.BCMTrainingOption_DeleteByQuestionID(Convert.ToInt32(item.ID));
                }

                //_CVLogger.LogInfoApp($"Successfully deleted BCM Training: {TrainingName} (ID: {TrainingID})");
                message = $"Training '{TrainingName}' has been deleted successfully.";
            }
            else
            {
                // _CVLogger.LogErrorApp($"Failed to delete BCM Training: {TrainingName} (ID: {TrainingID})");
                message = $"Failed to delete training '{TrainingName}'. Please try again.";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            message = $"An error occurred while deleting training '{TrainingName}': {ex.Message}";
        }

        // Store message in TempData to show on the redirected page
        TempData["DeleteMessage"] = message;
        TempData["DeleteSuccess"] = TrainingMasterDelete;

        return RedirectToAction("ManageBCMTrainingForm");
    }

    public IActionResult GetDepartmentByID(int iDepartmentId)
    {
        try
        {
            List<BCMTrainingMaster> lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();
            if (iDepartmentId > 0)
            {
                lstBCMTrainingMaster = lstBCMTrainingMaster.Where(x => x.DepartmentID == iDepartmentId).ToList();
                if (lstBCMTrainingMaster == null || !lstBCMTrainingMaster.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterBCMTraining", lstBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }


    public IActionResult GetUnitByID(int iUnitId)
    {
        try
        {
            List<BCMTrainingMaster> lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();
            if (iUnitId > 0)
            {
                lstBCMTrainingMaster = lstBCMTrainingMaster.Where(x => x.UnitID == iUnitId).ToList();
                if (lstBCMTrainingMaster == null || !lstBCMTrainingMaster.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterBCMTraining", lstBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMTrainingForm");
    }


    public void PopulateDropdown()
    {
        try
        {
            ViewBag.DepartmentInfo = new SelectList(_Utilities.GetDepartmentAllListForDropdown(), "DepartmentID", "DepartmentName");
            ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString()), "Id", "OrganizationName");
            ViewBag.OrgUnit = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails.OrgID), "UnitID", "UnitName");
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    /// <summary>
    /// Gets all units for a specific organization
    /// </summary>
    /// <param name="iOrgID">Organization ID</param>
    /// <returns>JSON result with units list</returns>
    [HttpGet]
    public JsonResult GetAllUnits(int iOrgID)
    {
        try
        {
            var unitsList = _Utilities.BindUnit(iOrgID);
            return Json(unitsList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new List<object>());
        }
    }

    /// <summary>
    /// Gets all departments for a specific unit
    /// </summary>
    /// <param name="iUnitID">Unit ID</param>
    /// <returns>JSON result with departments list</returns>
    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID)
    {
        try
        {
            var departmentsList = _Utilities.BindFunction(iUnitID);
            return Json(departmentsList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new List<object>());
        }
    }

    /// <summary>
    /// Gets filtered training data based on organization, unit, and department
    /// </summary>
    /// <param name="orgID">Organization ID</param>
    /// <param name="unitID">Unit ID</param>
    /// <param name="departmentID">Department ID</param>
    /// <returns>Partial view with filtered training data</returns>
    [HttpGet]
    public IActionResult GetFilteredTrainings(string orgID = "0", string unitID = "0", string departmentID = "0")
    {
        var lstBCMTrainingMaster = new List<BCMTrainingMaster>();

        try
        {
            // Get all trainings first
            lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();

            // Apply filters if specified
            if (!string.IsNullOrEmpty(orgID) && orgID != "0")
            {
                if (int.TryParse(orgID, out int orgIdInt))
                {
                    lstBCMTrainingMaster = lstBCMTrainingMaster.Where(t => t.OrgID == orgIdInt).ToList();
                }
            }

            if (!string.IsNullOrEmpty(unitID) && unitID != "0")
            {
                if (int.TryParse(unitID, out int unitIdInt))
                {
                    lstBCMTrainingMaster = lstBCMTrainingMaster.Where(t => t.UnitID == unitIdInt).ToList();
                }
            }

            if (!string.IsNullOrEmpty(departmentID) && departmentID != "0")
            {
                if (int.TryParse(departmentID, out int deptIdInt))
                {
                    lstBCMTrainingMaster = lstBCMTrainingMaster.Where(t => t.DepartmentID == deptIdInt).ToList();
                }
            }

            //_CVLogger.LogInfoApp($"Filtered trainings: OrgID={orgID}, UnitID={unitID}, DeptID={departmentID}, Count={lstBCMTrainingMaster.Count}");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_TrainingTableRows", lstBCMTrainingMaster);
    }
}

