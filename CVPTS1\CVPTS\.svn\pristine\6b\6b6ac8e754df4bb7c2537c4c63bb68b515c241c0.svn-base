﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;

namespace BCM.UI.Areas.BCMProcessBIA.Controllers;
[Area("BCMProcessBIA")]
public class ManageDepartmentBIAController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public ManageDepartmentBIAController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    public IActionResult ManageDepartmentBIA()
    {
        List<DepartmentBIAEntities> lstDepartmentBIAEntities = new List<DepartmentBIAEntities>();
        try
        {
            lstDepartmentBIAEntities = _ProcessSrv.GetDepartmentBIA_All();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstDepartmentBIAEntities);
    }

    [HttpGet]
    public IActionResult ConfigureDepartmentBIA(DepartmentBIAEntities objDepartmentBIAEntities)
    {
        try
        {
            ViewBag.Department = new SelectList(_Utilities.GetDepartmentAllListForDropdown(), "DepartmentID", "DepartmentName");
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
            ViewBag.Facilities = new SelectList(_Utilities.PopulateFacilities(), "FacilityID", "FacilityName");

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(objDepartmentBIAEntities);
    }

    [HttpPost]
    public IActionResult SaveDepartmentSection(DepartmentBIAEntities objDepartmentBIAEntities)
    {
        int iSucess = 0;
        try
        {
            if (objDepartmentBIAEntities != null)
            {
                iSucess = _ProcessSrv.BIADepartmentSaveUpdate(objDepartmentBIAEntities);
                if (iSucess > 0)
                {
                    HttpContext.Session.SetString("deptBIAID", iSucess.ToString());
                    objDepartmentBIAEntities.ID = iSucess;
                    TempData["DeptDetails"] = JsonConvert.SerializeObject(objDepartmentBIAEntities);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ConfigureDepartmentBIA", objDepartmentBIAEntities);
    }

    [HttpPost]
    public IActionResult SaveLocationSection(DepartmentBIAEntities objDepartmentBIAEntities)
    {
        var departmentModel = new DepartmentBIAEntities();
        try
        {
            if (Convert.ToInt32(HttpContext.Session.GetString("deptBIAID")) > 0)
            {
                var departmentJson = TempData["DeptDetails"] as string;
                departmentModel = JsonConvert.DeserializeObject<DepartmentBIAEntities>(departmentJson);
                if (departmentModel != null)
                {
                    departmentModel.ID = Convert.ToInt32(HttpContext.Session.GetString("deptBIAID"));
                    departmentModel.EntityTypeID = (int)BCPEnum.EntityType.DepartmentBIA;
                    //int iPrimaryFacility = 0;
                    bool isPrimaryFacilityDeleted = _ProcessSrv.DepartmentBIAFacility_Delete(departmentModel.ID, departmentModel.EntityTypeID);
                    departmentModel.DepartmentBiaID = departmentModel.ID;
                    departmentModel.PrimarySiteID = objDepartmentBIAEntities.PrimarySiteID;
                    int iPrimaryFacility = _ProcessSrv.DepartmentBIAFacilitySave(departmentModel);

                    //int iSecondaryFacility = 0;
                    bool isSecondaryFacilityDeleted = _ProcessSrv.DepartmentBIAAltFacility_Delete(departmentModel.ID, departmentModel.EntityTypeID);
                    departmentModel.DepartmentBiaID = departmentModel.ID;
                    departmentModel.AlternateSiteID = objDepartmentBIAEntities.AlternateSiteID;
                    int iSecondaryFacility = _ProcessSrv.DepartmentBIAAltFacilitySave(departmentModel);

                    departmentModel.StaffLocation = objDepartmentBIAEntities.StaffLocation;

                }
                int iSucess = _ProcessSrv.BIADepartmentSaveUpdate(departmentModel);
                if (iSucess > 0)
                {
                    TempData["LocationDetails"] = JsonConvert.SerializeObject(departmentModel);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ConfigureDepartmentBIA", departmentModel);
    }

    [HttpPost]
    public IActionResult SaveCustodianSection(DepartmentBIAEntities objDepartmentBIAEntities)
    {
        var departmentModel = new DepartmentBIAEntities();
        try
        {
            if (Convert.ToInt32(HttpContext.Session.GetString("deptBIAID")) > 0)
            {
                var departmentJson = TempData["LocationDetails"] as string;
                departmentModel = JsonConvert.DeserializeObject<DepartmentBIAEntities>(departmentJson);
                if (departmentModel != null)
                {
                    departmentModel.CustodianID = objDepartmentBIAEntities.CustodianID;                    
                    departmentModel.ID = Convert.ToInt32(HttpContext.Session.GetString("deptBIAID"));
                }
                int iSucess = _ProcessSrv.BIADepartmentSaveUpdate(departmentModel);
                if (iSucess > 0)
                {
                    TempData["CustodianDetails"] = JsonConvert.SerializeObject(departmentModel);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ConfigureDepartmentBIA", departmentModel);
    }

    [HttpPost]
    public IActionResult SaveReviewerSection(DepartmentBIAEntities objDepartmentBIAEntities)
    {
        var departmentModel = new DepartmentBIAEntities();
        try
        {
            if (Convert.ToInt32(HttpContext.Session.GetString("deptBIAID")) > 0)
            {
                var departmentJson = TempData["CustodianDetails"] as string;
                departmentModel = JsonConvert.DeserializeObject<DepartmentBIAEntities>(departmentJson);
                if (departmentModel != null)
                {
                    departmentModel.ID = Convert.ToInt32(HttpContext.Session.GetString("deptBIAID"));
                    bool isReviewerDeleted = _ProcessSrv.DepartmentBIAReview_DeleteByDepartmentBIAID(departmentModel.ID);

                    departmentModel.ReviewerID = objDepartmentBIAEntities.ReviewerID;
                    departmentModel.Status = 0;
                    departmentModel.ReviewDate = objDepartmentBIAEntities.ReviewDate;
                    departmentModel.LastReviewDate = objDepartmentBIAEntities.LastReviewDate;
                    int iReviewerDetails = _ProcessSrv.BIADepartmentReviewSaveUpdate(departmentModel);

                    int iSucess = _ProcessSrv.BIADepartmentSaveUpdate(departmentModel);
                    if(iSucess > 0)
                    {
                        TempData["ReviewerDetails"] = JsonConvert.SerializeObject(departmentModel);
                    }
                }                
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ConfigureDepartmentBIA", departmentModel);
    }

    [HttpPost]
    public IActionResult SaveBriefIssueSection(DepartmentBIAEntities objDepartmentBIAEntities)
    {
        var departmentModel = new DepartmentBIAEntities();
        try
        {
            int iSuccess = 0;
            if (Convert.ToInt32(HttpContext.Session.GetString("deptBIAID")) > 0)
            {
                var departmentJson = TempData["ReviewerDetails"] as string;
                departmentModel = JsonConvert.DeserializeObject<DepartmentBIAEntities>(departmentJson);
                if (departmentModel != null)
                {
                    departmentModel.ID = Convert.ToInt32(HttpContext.Session.GetString("deptBIAID"));
                    departmentModel.DetailID = departmentModel.ID;
                    departmentModel.LocationID = departmentModel.LocationID;
                    departmentModel.Issue = objDepartmentBIAEntities.Issue;
                    departmentModel.RevenueLoss = objDepartmentBIAEntities.RevenueLoss;
                    departmentModel.HowsItHandle = objDepartmentBIAEntities.HowsItHandle;
                    departmentModel.Remarks = objDepartmentBIAEntities.Remarks;

                    iSuccess = _ProcessSrv.BIADepartmentDetailsSaveUpdate(departmentModel);
                    TempData["BriefIssueDetails"] = JsonConvert.SerializeObject(departmentModel);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ConfigureDepartmentBIA", departmentModel);
    }
}
