﻿@model BCM.BusinessClasses.ProcessBIA
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@using BCM.BusinessClasses
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using System.Data;

@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    int Srno = 1;
    int iSrnos = 1;

    System.Data.DataTable dtPeopleTimeEdit = ViewBag.DayValues as System.Data.DataTable;

}
<div class="Page-Header d-flex align-items-center justify-content-between mb-3">
    <p class="fw-semibold">Configure Human Resources Requirements for @ViewBag.ProcessName ( @ViewBag.ProcessCode )</p>
    <div class="align-items-right" style="padding-right:2%">
        <p class="fw-semibold" id="iVersion">Version : @ViewBag.ProcessVersion</p>
    </div>
</div>

<div class="Page-Condant card border-0">
    <div class="card-body">
        <div class="row">
            <div class="col-12" style="display:none">
                <p class="fw-semibold mb-2">Instructions and Guidelines</p>
                <div class="accordion accordion-flush" id="accordionFlushExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                Worksheet #4: @ViewBag.QuestionDetails @* Human Resources Requirements *@
                            </button>
                        </h2>
                        <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                            <div class="accordion-body">
                                <p class="mb-2">HR Continuity Planning: The purpose of this section is to collect information to plan and respond to drop in staff levels and cases of manpower shortages (e.g. pandemic planning)</p>
                                <ul class="ps-3">
                                    <li>Minimum possible number of personnel required to execute minimized work – starts with minimum acceptable level and ramps with time</li>
                                    <li>Critical Positions - For the earliest timeframe of HR requirements - indicate the positions required. Identify backups if currently available.</li>
                                    <li>Work Load Transfer - Are there cross trained personnel available in other teams who could be leveraged in case your process does not have minimum personnel? If yes, provide the name of the team.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12">
                <div class="row row-cols-2">
                    <div class="col d-grid align-items-end">
                        <div class="form-group">
                            <label class="form-lable">Questions</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="option1" checked>
                                <label class="form-check-label" for="inlineRadio1">Human Resources Requirements</label>
                            </div>
                        </div>
                    </div>
                    <div class="col" style="display:none">
                        <div class="form-group">
                            <label class="form-lable">Current Actual Manpower</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-manpower"></i></span>
                                <input class="form-control" type="number" value="@ViewBag.ActualCurrentManpower" required pattern="[0-9]+" title="Only Numbers allowed" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <p class="fw-semibold">(A) MINIMUM POSSIBLE NUMBER OF PERSONNEL REQUIRED TO EXECUTE MINIMIZED WORK – STARTS WITH MINIMUM ACCEPTABLE LEVEL AND RAMPS WITH TIME</p>

                    @* <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                @foreach (System.Data.DataColumn column in dtPeopleTimeEdit.Columns)
                                {
                                    <th>@column.ColumnName</th>
                                }
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (DataRow row in dtPeopleTimeEdit.Rows)
                            {
                                <tr>
                                    @foreach (DataColumn column in dtPeopleTimeEdit.Columns)
                                    {
                                        <td>
                                            <div class="input-group">
                                                <input class="form-control px-0" type="number" value="@row[column].ToString()" placeholder="Enter Day" />
                                            </div>
                                        </td>
                                    }
                                </tr>
                            }
                        </tbody>
                    </table> *@
                </div>
            </div>

            <div class="col-12">
                <p class="fw-semibold">(B) INDICATE THE POSITIONS REQUIRED. IDENTIFY BACKUPS IF CURRENTLY AVAILABLE.</p>
                <p class="text-primary d-flex align-items-center gap-1">
                    <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsequestion1" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                    Question: @ViewBag.QuestionDetails ? @* What are the Vital Records involved in this process? *@
                </p>
                <div class="ps-2 collapse show" id="collapsequestion1">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Primary</th>
                                <th>Assigned Backup</th>
                                <th>Completion Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.ProcessBIAPeopleInfo != null)
                            {
                                foreach (ProcessBIAPeopleInfo item in Model.ProcessBIAPeopleInfo)
                                {
                                    <tr>
                                        <td>@Srno</td>
                                        <td>
                                            <div class="d-flex">
                                                <div class="User-icon">
                                                    <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg" />
                                                </div>
                                                <div>
                                                    <ul class="ps-0 mb-0">
                                                        <li class="list-group-item fw-semibold">@item.OwnerName</li>
                                                        <li class="list-group-item" style="display:none">@item.OwnerEmail</li>
                                                        <li class="list-group-item" style="display:none">@item.OwnerMobile</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex">
                                                <div class="User-icon">
                                                    <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg" />
                                                </div>
                                                <div>
                                                    <ul class="ps-0 mb-0">
                                                        <li class="list-group-item fw-semibold">@item.AltOwnerName</li>
                                                        <li class="list-group-item" style="display:none">@item.AltOwnerEmail</li>
                                                        <li class="list-group-item" style="display:none">@item.AltOwnerMobile</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @(item.IsComplete == 1 ? "completed" : "pending")
                                        </td>

                                        <td>
                                            <div class="d-flex align-items-center gap-2">
                                                <div class="dropdown dropstart">
                                                    <span class="btn-action" role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false"><i class="cv-activity-details" title="View Details"></i></span>
                                                    <div class="dropdown-menu border-0">
                                                        <table class="table mb-0 table-borderless">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="table table-sm mb-0 table-borderless">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <th class="fw-semibold text-primary" colspan="3">Owner Details</th>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="fw-semibold"><i class="cv-user"></i></td>
                                                                                    <td> : </td>
                                                                                    <td>
                                                                                        @{
                                                                                            if (string.IsNullOrEmpty(item.OwnerName))
                                                                                            {
                                                                                                <span><i class="cv-na" title="Not Available"></i></span>
                                                                                            }
                                                                                            else
                                                                                            {
                                                                                                @item.OwnerName
                                                                                            }
                                                                                        }
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                                                    <td>:</td>
                                                                                    <td>@item.OwnerEmail</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                                                    <td>:</td>
                                                                                    <td>@item.OwnerMobile</td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <table class="table table-sm mb-0 table-borderless">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <th class="fw-semibold text-primary" colspan="3">Alt Owner Details</th>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="fw-semibold"><i class="cv-user"></i></td>
                                                                                    <td> : </td>
                                                                                    <td>
                                                                                        @{
                                                                                            if (string.IsNullOrEmpty(item.AltOwnerName))
                                                                                            {
                                                                                                <span><i class="cv-na" title="Not Available"></i></span>
                                                                                            }
                                                                                            else
                                                                                            {
                                                                                                @item.AltOwnerName
                                                                                            }
                                                                                        }
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                                                    <td>:</td>
                                                                                    <td>@item.AltOwnerEmail</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                                                    <td>:</td>
                                                                                    <td>@item.AltOwnerMobile</td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <span class="btn-action btnEdit" @ViewBag.ButtonAccess.btnUpdate type="button" data-id="@item.ID"><i class="cv-edit" title="Edit"></i></span>
                                                <span class="btn-action btnDelete" @ViewBag.ButtonAccess.btnUpdate type="button" data-id="@item.ID"><i class="cv-delete text-danger" title="Delete"></i></span>
                                            </div>
                                        </td>
                                    </tr>
                                    Srno++;
                                }
                            }
                        </tbody>
                    </table>
                </div>
                <form asp-action="AddOrEditBIAPeople" method="post" id="addOrEditBIAPeople" class="needs-validation" novalidate>
                    <div class="row row-cols-2">
                        <div class="col">
                            <div class="form-group">
                                <input type="hidden" value="" name="ID" id="PeopleId" />
                                <label class="form-lable">Primary</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-primary"></i></span>
                                    <select class="form-select-sm form-control selectized" name="OwnerID" id="ResourceID" required>
                                        <option value="">-- Select Primary --</option>
                                        @* @{
                                            foreach (var item in ViewBag.Resource)
                                            {
                                                <option value="@item.Value">@item.Text</option>
                                            }
                                        } *@
                                    </select>
                                </div>
                                <div class="invalid-feedback">Please select a Primary resource</div>
                            </div>
                        </div>

                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Assigned Backup </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-backup"></i></span>
                                    <select class="form-select-sm form-control selectized" name="AltResourceID" id="AltResourceID" required>
                                        <option value="">-- Select Assigned Backup --</option>
                                        @* @{
                                            foreach (var item in ViewBag.Resource)
                                            {
                                                <option value="@item.Value">@item.Text</option>
                                            }
                                        } *@
                                    </select>
                                </div>
                                <div class="invalid-feedback">Please select an Assigned Backup resource</div>
                            </div>
                        </div>
                        <div class="col-12 mb-2 text-end">
                            <button type="button" class="btn btn-sm btn-secondary" id="btnCancelPeople">Cancel</button>
                            <button type="submit" class="btn btn-sm btn-primary" id="btnSubmitPeople" @ViewBag.ButtonAccess.btnUpdate>Save</button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="col-12">
                <p class="fw-semibold">
                    (C) WORK LOAD TRANSFER - ARE THERE CROSS TRAINED PERSONNEL AVAILABLE IN OTHER TEAMS WHO COULD BE LEVERAGED IN CASE YOUR PROCESS DOES NOT HAVE MINIMUM PERSONNEL? IF YES, PROVIDE THE NAME OF THE TEAM.
                </p>
                <p class="text-primary d-flex align-items-center gap-1">
                    <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsequestion2" aria-expanded="false" aria-controls="collapseExample">
                        <i class="cv-minus align-middle"></i>
                    </span> Question: @ViewBag.QuestionDetails ?
                </p>
                <div class="ps-2 collapse show" id="collapsequestion2">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Activity</th>
                                <th>Potential Team</th>
                                <th>Completion Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (BCM.BusinessClasses.ProcessBIAPeopleActivity item in Model.ProcessBIAPeopleActivity)
                            {
                                <tr>
                                    <td>@iSrnos</td>
                                    <td>
                                        <div class="d-flex">
                                            @item.Activity
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            @item.PotentialTeam
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            @(item.IsComplete == 1 ? "completed" : "pending")
                                        </div>
                                    </td>

                                    <td>
                                        <span class="btn btn-sm" id="btnEditActivity" @ViewBag.ButtonAccess.btnUpdate type="button" data-id="@item.Id"><i class="cv-edit" title="Edit"></i></span>
                                        <span class="btn-action btnDeleteActivity" @ViewBag.ButtonAccess.btnDelete type="button" data-id="@item.Id"><i class="cv-delete text-danger" title="Delete"></i></span>
                                    </td>
                                </tr>
                                iSrnos++;
                            }
                        </tbody>
                    </table>
                </div>
                <form asp-action="UpdatePeopleInfo" method="post" id="addOrEditBIAActivity" class="needs-validation" novalidate>
                    <div class="row row-cols-2">
                        <div class="col">
                            <div class="form-group">
                                <input type="hidden" value="" name="Id" id="ActivityId" />
                                <label class="form-lable">Activity</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-activity"></i></span>
                                    <textarea class="form-control" rows="1" name="Activity" id="activity" required></textarea>
                                </div>
                                <div class="invalid-feedback">Please enter an Activity</div>
                                <div class="text-end text-secondary">
                                    <span id="charCount">10000</span> characters left.
                                </div>
                            </div>
                        </div>

                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Potential Team</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-team-size"></i></span>
                                    <textarea class="form-control" rows="1" name="PotentialTeam" id="potentialteam" required></textarea>
                                </div>
                                <div class="invalid-feedback">Please enter a Potential Team</div>
                                <div class="text-end text-secondary">
                                    <span id="charCounts">10000</span> characters left.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 mb-2 text-end">
                        <a class="btn btn-sm btn-outline-primary" role="button" asp-action="PerformProcessBIA"
                           asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA"
                           asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>
                        <button type="submit" class="btn btn-sm btn-primary" id="btnSubmitActivity" @ViewBag.ButtonAccess.btnUpdate>Save</button>
                        <button type="button" class="btn btn-sm btn-secondary" id="btnCancelActivity">Cancel</button>

                    </div>
                </form>
                <div class="col-12 mb-2 text-end">
                    @* <button class="btn btn-sm btn-outline-primary">Back</button> *@

                </div>
            </div>
        </div>
        <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-body text-center" id="DeleteBody">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
@section Scripts {
    <script>

        $(document).ready(function () {

            // $("#btnUpdateBIAPeople").hide();
            // $("#btnUpdateActivity").hide();

            //get all primary resources
            // if ($('#ResourceID')[0].selectize) {
            //     $('#ResourceID')[0].selectize.destroy();
            // }

            // $.get('@Url.Action("PopulatePrimarylist", "BIAPeople")', function (data) {
            //     var $resourceSelect = $('#ResourceID');

            //     // Clear and re-add default option
            //     $resourceSelect.empty();
            //     $resourceSelect.append('<option selected disabled value="">-- Select Primary --</option>');

            //     // Add new options from data
            //     $.each(data, function (index, item) {
            //         $resourceSelect.append('<option value="' + item.resourceId + '">' + item.resourceName + '</option>');
            //     });

            //     // Initialize Selectize
            //     $resourceSelect.selectize({
            //         sortField: 'text',
            //         placeholder: '-- Select Primary --',
            //         allowEmptyOption: true
            //     });
            // });

            $.get('@Url.Action("PopulatePrimarylist", "BIAPeople")', function (data) {
                    var ResourceID = $('#ResourceID');
                    ResourceID.empty();
                    ResourceID.append('<option selected disabled value="">-- Select Primary --</option>');
                    $.each(data, function (index, item) {
                        ResourceID.append('<option value="' + item.resourceId + '">' + item.resourceName + '</option>');
                    });
                });

            $.get('@Url.Action("PopulatePrimarylist", "BIAPeople")', function (data) {
                    var AltResourceID = $('#AltResourceID');
                    AltResourceID.empty();
                    AltResourceID.append('<option disabled selected value="">-- Select Alt Owner --</option>');
                    $.each(data, function (index, item) {
                        AltResourceID.append('<option value="' + item.resourceId + '">' + item.resourceName + '</option>');
                    });
                });

            // Update button labels
            function UpdatePeopleButtonLabel() {
                    var id = $('#PeopleId').val();
                    if (id && id > 0) {
                        $('#btnSubmitPeople').text('Update');
                    } else {
                        $('#btnSubmitPeople').text('Save');
                    }
                }

            function UpdateActivityButtonLabel() {
                    var id = $('#ActivityId').val();
                    if (id && id > 0) {
                        $('#btnSubmitActivity').text('Update');
                    } else {
                        $('#btnSubmitActivity').text('Save');
                    }
                }

            UpdatePeopleButtonLabel();
            UpdateActivityButtonLabel();

            // Edit People functionality (for form-based approach)
            $('.btnEdit').click(function () {
                    var iID = $(this).data('id');

                    // Form-based edit
                    $.ajax({
                        url: '@Url.Action("EditBIAPeople", "BIAPeople")',
                        type: 'GET',
                        data: { iID: iID },
                        success: function (data) {
                            $('#btnSubmitPeople').text('Update');
                            $('select[name="OwnerID"]').val(data.resourceID);
                            $('select[name="AltResourceID"]').val(data.altResourceID);
                            $('#PeopleId').val(iID);
                            // UpdatePeopleButtonLabel();
                        },
                        error: function () {
                            console.log('Error loading people data');
                        }
                    });

                    // Also populate for AJAX-based approach (backward compatibility)
                    $.get('@Url.Action("GetPrimaryOwnerByiPeopleID", "BIAPeople")', { iPeopleID: iID }, function (data) {
                        var selectedResourceID = data.length > 0 ? data[0].resourceID : null;
                        var selectedAlResourceID = data.length > 0 ? data[0].altResourceID : null;
                        if (selectedResourceID) {
                            $("#ResourceID").val(selectedResourceID);
                            $("#AltResourceID").val(selectedAlResourceID);
                        }
                    });
                });

            // Edit Activity functionality (for form-based approach)
            $('#btnEditActivity').click(function () {
                    var iID = $(this).data('id');
                    $.ajax({
                        url: '@Url.Action("EditBIAActivity", "BIAPeople")',
                        type: 'GET',
                        data: { iID: iID },
                        success: function (data) {
                            console.log(data);
                            $('textarea[name="Activity"]').val(data.activity);
                            $('textarea[name="PotentialTeam"]').val(data.potentialTeam);
                            $('#ActivityId').val(iID);
                            UpdateActivityButtonLabel();
                        },
                        error: function () {
                            console.log('Error loading activity data');
                        }
                    });

                    // Also populate for AJAX-based approach (backward compatibility)
                    $.get('@Url.Action("GetAllProcessBIAPeopleActivity", "BIAPeople")', { iPeopleActivityID: iID }, function (data) {
                        $("#activity").val(data.activity);
                        $("#potentialteam").val(data.potentialTeam);
                    });
                });

            // AJAX-based update for Primary Owner (original functionality)
            $(document).on('click', '#btnUpdateBIAPeople', function () {
                    var resourceID = $('#ResourceID').val();
                    var altResourceID = $('#AltResourceID').val();

                    if (!resourceID || resourceID === '') {
                        alert('Please select a Primary resource.');
                        return;
                    }

                    if (!altResourceID || altResourceID === '') {
                        alert('Please select an Assigned Backup resource.');
                        return;
                    }

                    var objdata = {
                        OwnerID: resourceID,
                        AltResourceID: altResourceID,
                        Version: "1"
                    };

                    $.ajax({
                        type: "POST",
                        url: '@Url.Action("UpdatePrimaryOwnerByPeopleID", "BIAPeople")',
                        data: JSON.stringify(objdata),
                        contentType: 'application/json',
                        dataType: 'JSON',
                        success: function (response) {
                            if (response && response.success) {
                                $('#ResourceID').val('');
                                $('#AltResourceID').val('');
                                window.location.reload();
                            } else {
                                alert('Update failed. Please try again.');
                            }
                        },
                        error: function (xhr, status, error) {
                            console.log('Error occurred during update:', error);
                            alert('An error occurred while updating. Please try again.');
                        }
                    });
                });

            // AJAX-based update for Activity (original functionality)
            $(document).on('click', '#btnUpdateActivity', function () {
                    var activity = $('#activity').val();
                    var potentialTeam = $('#potentialteam').val();

                    if (!activity || activity.trim() === '') {
                        alert('Please enter an Activity.');
                        return;
                    }

                    if (!potentialTeam || potentialTeam.trim() === '') {
                        alert('Please enter a Potential Team.');
                        return;
                    }

                    var objdata = {
                        Activity: activity.trim(),
                        PotentialTeam: potentialTeam.trim(),
                    };

                    $.ajax({
                        type: "POST",
                        url: '@Url.Action("UpdatePeopleInfo", "BIAPeople")',
                        data: JSON.stringify(objdata),
                        contentType: 'application/json',
                        dataType: 'JSON',
                        success: function (response) {
                            if (response && response.success) {
                                $('#activity').val('');
                                $('#potentialteam').val('');
                                $('#charCount').text('10000');
                                $('#charCounts').text('10000');
                                window.location.reload();
                            } else {
                                alert('Update failed. Please try again.');
                            }
                        },
                        error: function (xhr, status, error) {
                            console.log('Error occurred during activity update:', error);
                            alert('An error occurred while updating activity. Please try again.');
                        }
                    });
                });

            // Cancel buttons
            $('#btnCancelPeople').click(function (event) {
                    event.preventDefault();
                    location.reload();
                });

            $('#btnCancelActivity').click(function (event) {
                    event.preventDefault();
                    location.reload();
                });

            // Character count for textareas
            $('#activity').on('input', function () {
                    var remaining = 10000 - $(this).val().length;
                    $('#charCount').text(remaining);
                });

            $('#potentialteam').on('input', function () {
                    var remaining = 10000 - $(this).val().length;
                    $('#charCounts').text(remaining);
                });

            $(document).on('click','.btnDelete',function(){
                    var id = $(this).data('id');
                    $.get('@Url.Action("BIAPeopleDeleteByID", "BIAPeople")', { id: id }, function (data) {
                        $('#DeleteBody').html(data);
                        $('#DeleteModal').modal('show');
                    });
                })

            $(document).on('click','.btnDeleteActivity',function(){
                    var id = $(this).data('id');
                    $.get('@Url.Action("DeleteBIAPeopleActivity", "BIAPeople")', { id: id }, function (data) {
                        $('#DeleteBody').html(data);
                        $('#DeleteModal').modal('show');
                    });
                })

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

        });
    </script>
}
