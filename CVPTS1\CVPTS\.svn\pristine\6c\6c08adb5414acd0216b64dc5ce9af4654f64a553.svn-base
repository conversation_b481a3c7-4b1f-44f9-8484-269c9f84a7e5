﻿@{
    ViewData["Title"] = "Data Set";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .dataset-editor {
        /*  height: 130px;
                                            overflow: auto; */
        border: 1px solid #C3C3C3;
        border-radius: 10px;
    }



    .list-card {
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
        padding: 15px;
    }

    .list-header {
        font-weight: 600;
        margin-bottom: 10px;
        text-align: left;
    }

    .search-box {
        margin-bottom: 10px;
    }

    .list-box {
        list-style: none;
        padding: 0;
        margin: 0;
        min-height: 280px;
        max-height: 280px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 6px;
    }

        .list-box li {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

            .list-box li:last-child {
                border-bottom: none;
            }

            .list-box li:hover {
                background-color: #f1f1f1;
            }

            .list-box li.selected {
                background-color: #0d6efd;
                color: white;
            }

    .transfer-buttons {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 10px;
        margin-top: 60px;
    }

    .btn-icon {
        font-size: 20px;
    }
</style>

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Dataset </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" id="createDataset" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal">
            <i class="cv-Plus" title="Create New"></i>Data Set
        </button>
    </div>
</div>

<div class="Page-Condant card border-0">
    <table id="datasetTable" class="table table-hover" style="width:100%">
        <thead>
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Description</th>
                <th style="max-width:60%;width:60%">Stored Query</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>
</div>




<div class="modal fade" id="AddModal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Dataset Configuration</h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Dataset Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-name"></i></span>
                                <input type="text" id="datasetName"class="form-control" placeholder="Enter Dataset Name" name="database" value="">
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input type="text" id="datasetDesc"class="form-control" placeholder="Enter Description" name="Description" value="">
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Schema Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-workflow-dashline"></i></span>
                                <select id="schemaName" class="form-select form-control selectized">
                                    <option value="" selected disabled>Select schemaName</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Table Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-calculated"></i></span>
                                <select id="tableName" name="tableName" class="form-select form-control">
                                    <option value="" selected disabled>Select Table Name</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row dual-list-container">
                    <label class="form-label">Column Name</label>
                    <div class="col-5">
                        <div class="input-group mb-2">
                            <span class="input-group-text">
                                <i class="cv-search"></i>
                            </span>
                            <input type="text" id="searchLeft" class="form-control" placeholder="Search columns...">
                        </div>
                        <ul id="leftList" class="list-box"></ul>
                    </div>

                    <div class="col-2">
                        <!-- Buttons -->
                        <div class="transfer-buttons">
                            <button class="btn btn-outline-secondary btn-icon" id="datasetColumnName_rightAll" onclick="moveAll('left', 'right')" title="Move all right"><i class="cv-left-double-arrow fs-5"></i></button>
                            <button class="btn btn-outline-secondary btn-icon" id="datasetColumnName_rightSelected" onclick="moveSelected('left', 'right')" title="Move selected right"><i class="cv-right-arrow fs-5"></i></button>
                            <button class="btn btn-outline-secondary btn-icon" id="datasetColumnName_leftSelected" onclick="moveSelected('right', 'left')" title="Move selected left"><i class="cv-left-arrow fs-5"></i></button>
                            <button class="btn btn-outline-secondary btn-icon" id="datasetColumnName_leftAll" onclick="moveAll('right', 'left')" title="Move all left"><i class="cv-right-double-arrow fs-5"></i></button>
                        </div>
                    </div>
                    <div class="col-5">

                        <div class="input-group mb-2">
                            <span class="input-group-text"><i class="cv-search"></i></span>
                            <input type="text" id="searchRight" class="form-control" placeholder="Search...">
                        </div>
                        <ul id="rightList" class="list-box"></ul>
                    </div>
                    <div class="col-12">
                        <div class="form-group field-touched mt-3">
                            <label class="form-label">Stored Query</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <textarea type="text" id="datasetStoredQuery" class="form-control" placeholder="query" name="" value="" style="height:0px;max-height:100px;overflow:auto"></textarea>
                                <span id="btnRunQuery" class="input-group-text"><button class="btn btn-sm btn-primary ms-2">Run</button></span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary">Cancel</button>
                <button id="btnDatasetSave"class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>
</div>
<!-- Dataset Query List Modal -->
<div class="modal fade" id="QueryListModal" tabindex="-1" aria-labelledby="QueryListModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <form class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-configure-dataset"></i><span>DataSet Query List</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="overflow-y: hidden;">
                <div style="overflow:auto;height: 500px;">
                    <table class="table table-hover dataTable no-footer" style="width:100%;" id="datasetQueryTable">
                        <thead class="position-sticky top-0 z-3">
                            <tr id="tablerow">
                                <!-- dynamically filled -->
                            </tr>
                        </thead>
                        <tbody id="tableData">
                            <!-- dynamically filled -->
                        </tbody>
                    </table>
                    <div id="datasetWrapper"></div>
                </div>
            </div>
        </form>
    </div>
</div>


<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="~/js/dataset/dataset.js"></script>
<script src="~/js/common/common.js"></script>
 
<script>
    // document.addEventListener("DOMContentLoaded", function () {
    //   const tbody = document.getElementById("log-table-body");

    //   for (let i = 1; i <= 10; i++) {
    //     const tr = document.createElement("tr");

    //     tr.innerHTML = `
    //       <td>${i}</td>
    //       <td>Oracle Log Datase ${i}</td>
    //       <td>Oracle Log Dataset ${i}</td>
    //       <td title="Select * from [WAPT].[dbo].[oracle_monitor_logs_${i}]"><span class="d-inline-block text-truncate" style="max-width:60%;width:60%">Select * from [WAPT].[dbo].[oracle_monitor_logs_${i}] </span></td>
    //       <td>
    //         <div class="d-flex align-items-center gap-2">
    //           <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="#Modal" data-id="${i}">
    //             <i class="cv-edit" title="Edit"></i>
    //           </span>
    //           <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="#Modal" data-id="${i}">
    //             <i class="cv-delete text-danger" title="Delete"></i>
    //           </span>
    //         </div>
    //       </td>
    //     `;

    //     tbody.appendChild(tr);
    //   }
    // });


       // Select toggle
    document.querySelectorAll('.list-box').forEach(list => {
      list.addEventListener('click', (e) => {
        if (e.target.tagName === 'LI') {
          e.target.classList.toggle('selected');
        }
      });
    });

    // Move selected items
    function moveSelected(fromId, toId) {
      const from = document.getElementById(fromId + 'List');
      const to = document.getElementById(toId + 'List');
      [...from.querySelectorAll('li.selected')].forEach(li => {
        li.classList.remove('selected');
        to.appendChild(li);
      });
    }

    // Move all items
    function moveAll(fromId, toId) {
      const from = document.getElementById(fromId + 'List');
      const to = document.getElementById(toId + 'List');
      [...from.querySelectorAll('li')].forEach(li => {
        li.classList.remove('selected');
        to.appendChild(li);
      });
    }

    // Filter items
    function filterList(inputId, listId) {
      const query = document.getElementById(inputId).value.toLowerCase();
      const listItems = document.getElementById(listId).getElementsByTagName('li');
      for (let li of listItems) {
        li.style.display = li.textContent.toLowerCase().includes(query) ? '' : 'none';
      }
    }

    document.getElementById('searchLeft').addEventListener('input', () => filterList('searchLeft', 'leftList'));
    document.getElementById('searchRight').addEventListener('input', () => filterList('searchRight', 'rightList'));

    // Drag and drop between lists
    let dragged;

    document.querySelectorAll('.list-box li').forEach(item => {
      item.addEventListener('dragstart', e => {
        dragged = e.target;
        e.dataTransfer.effectAllowed = 'move';
      });
    });

    document.querySelectorAll('.list-box').forEach(list => {
      list.addEventListener('dragover', e => e.preventDefault());
      list.addEventListener('drop', e => {
        e.preventDefault();
        if (dragged && dragged !== e.target && e.currentTarget !== dragged.parentNode) {
          e.currentTarget.appendChild(dragged);
          dragged.classList.remove('selected');
        }
      });
    });


</script>
