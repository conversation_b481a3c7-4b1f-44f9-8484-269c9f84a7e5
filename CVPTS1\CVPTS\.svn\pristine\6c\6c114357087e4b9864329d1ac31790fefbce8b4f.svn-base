﻿@model BCM.BusinessClasses.RecoveryPlan
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<form>
<div class="row row-cols-2">
    <div class="col">
        <div>
            <input type="hidden" id="ID" name="ID" asp-for="ID" />
        </div>
        <div class="form-group">
            <label class="form-label">Plan Name</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-name"></i></span>
                <input id="PlanName" name="PlanName" type="text" class="form-control" placeholder="Enter Plan Name" asp-for="PlanName" />
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Organization Name</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-organization"></i></span>
                <select id="OrgName" name="OrgName" class="form-select form-select-sm" asp-for="OrgID">
                    <option selected value="0">Organization Name</option>
                    @foreach (var objOrg in ViewBag.OrgInfo)
                    {
                        <option value="@objOrg.Value">@objOrg.Text</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Department</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-department"></i></span>
                <select id="DepartName" name="DepartName" class="form-select form-select-sm" asp-for="DepartmentID">
                    <option selected value="0">All Departments</option>
                    @foreach (var objDepartment in ViewBag.DepartmentInfo)
                    {
                        <option value="@objDepartment.Value">@objDepartment.Text</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Plan Owner</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-owner"></i></span>
                <select id="PlanOwner" name="PlanOwner" class="form-select form-select-sm" asp-for="PlanOwnerID">
                    <option selected value="0">Plan Owner</option>
                    @foreach (var objPlanOwner in ViewBag.ResourcesInfo)
                    {
                        <option value="@objPlanOwner.Value">@objPlanOwner.Text</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Estimated Time</label>
            <div class="row">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                        <input type="text" id="EstimatedTime" name="EstimatedTime" class="form-control" placeholder="Max Interval" asp-for="EstimatedRecoveryTime">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <select class="form-select-sm" id="EstimatedTimeUnit" name="EstimatedTimeUnit" asp-for="EstimatedRecoveryTimeUnit">
                            <option value="0" selected>Min</option>
                            <option value="1">Hour</option>
                            <option value="2">Day</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Last Review Date</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-calendar"></i></span>
                <input id="LstReviewDate" name="LstReviewDate" type="date" class="form-control" asp-for="LastReviewDate" />
            </div>
        </div>
    </div>
    <div class="col">
        <div class="form-group">
            <label class="form-label">Plan Code  </label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-name"></i></span>
                <input id="PlanCode" name="PlanCode" type="text" class="form-control" placeholder="Enter Plan Code" asp-for="PlanCode" />
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Unit</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-unit"></i></span>
                <select id="UnitName" name="UnitName" class="form-select form-select-sm" asp-for="UnitID">
                    <option selected value="0">Units</option>
                    @foreach (var objUnit in ViewBag.OrgUnit)
                    {
                        <option value="@objUnit.Value">@objUnit.Text</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Sub Department</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                <select id="SubDepartment" name="SubDepartment" class="form-select form-select-sm" asp-for="SubfunctionID">
                    <option selected value="0">Sub Departments</option>
                    @foreach (var objSubDepartment in ViewBag.SubFunction)
                    {
                        <option value="@objSubDepartment.Value">@objSubDepartment.Text</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Plan Approver</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-develop-Plan"></i></span>
                <select id="ApproverName" name="ApproverName" class="form-select form-select-sm" asp-for="PlanApproverID">
                    <option selected value="0">Plan Approver</option>
                    @foreach (var objPlanApprover in ViewBag.ResourcesInfo)
                    {
                        <option value="@objPlanApprover.Value">@objPlanApprover.Text</option>
                    }
                </select>
            </div>
        </div>


        <div class="form-group">
            <label class="form-label">Comments</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-comment"></i></span>
                <input id="Description" name="Description" type="text" class="form-control" placeholder="Enter Comments" asp-for="Comments" />
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Next Review Date</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-calendar"></i></span>
                <input id="NxtReviewDate" name="NxtReviewDate" type="date" class="form-control" asp-for="PlanReviewDate" />
            </div>
        </div>
    </div>
</div>
</form>


<script src="~/lib/jquery/jquery.min.js"></script>
<script src="~/lib/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.min.js"></script>
<script src="~/lib/datatables/js/jquery.datatables.min.js"></script>
<script src="~/lib/datatables/js/datatables.bootstrap5.min.js"></script>
<script src="~/lib/selectize/selectize.min.js"></script>
<script src="~/js/managerecoveryplan.js"></script>


<script src="~/js/password_toggle.js"></script>


<script>

    $(document).ready(function () {
        $('a').click(function (arrayOfValues) {
           // debugger;
            var href = $(this).attr('href');

            if (href == "#finish") {
                var getValueOrDefault = function (selector, defaultValue) {
                    var value = $(selector).val();
                    return value ? value : defaultValue;
                };


                var objdata = {
                    PlanName: $('#PlanName').val(),
                    ID: getValueOrDefault('#ID', 0),
                    OrgID: getValueOrDefault('#OrgName', 0),
                    DepartmentID: getValueOrDefault('#DepartName', 0),
                    SubFunctionID: getValueOrDefault('#SubDepartment', 0),
                    UnitID: getValueOrDefault('#UnitName', 0),
                    PlanOwnerID: getValueOrDefault('#PlanOwner', 0),
                    PlanApproverID: getValueOrDefault('#ApproverName', 0),
                    EstimatedRecoveryTime: $('#EstimatedTime').val(),
                    EstimatedRecoveryTimeUnit: $('#EstimatedTimeUnit').val(),
                    Comments: $('#Description').val(),
                    PlanReviewDate: $('#NxtReviewDate').val()
                };

                $.ajax({
                    type: "POST",
                    url: '/BCMFunctionRecoveryPlan/FunctionalRecoveryPlanForm/AddFunctionalRecoveryPlan/',
                    data: JSON.stringify(objdata),
                    contentType: 'application/json', 
                    dataType: 'JSON',
                    success: function (response) {
                        debugger;
                        if (response.success) {
                            // Handle success
                        } else {
                            // Handle failure
                        }
                    },
                    error: function (data) {
                        console.log('Error:', data);
                    }
                });
            }
        });
    });

</script>
