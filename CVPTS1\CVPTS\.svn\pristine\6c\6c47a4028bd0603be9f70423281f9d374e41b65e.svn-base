﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Text;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using BCM.Shared;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using static BCM.Shared.BCPEnum;
using Microsoft.AspNetCore.Components.Routing;
using System.Data;
using Microsoft.AspNetCore.Mvc;

using Microsoft.AspNetCore.Hosting.Server;
using Ubiety.Dns.Core.Records.NotUsed;
using BCM.Security.Helper;
//using log4net.Repository.Hierarchy;

namespace BCM.Shared
{
    [Serializable()]
    public class Utilities
    {
        ProcessSrv _ProcessSrv { get; set; }
        public VaultSettings _objVaultSettings = null;
        private readonly IHttpContextAccessor _HttpContextAccessor;
        private readonly CVLogger _CVLogger;
        ConfigSettings? _ConfigSettings;
        ManageUsersDetails _UserDetails;
        private int IsUnderBCM = 1;

        private readonly IConfiguration _configuration;
        public Utilities(ProcessSrv ProcessSrv, IHttpContextAccessor HttpContextAccessor, CVLogger logger)
        {
            _ProcessSrv = ProcessSrv;
            _HttpContextAccessor = HttpContextAccessor;
            _CVLogger = logger;
            _configuration = (new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json").Build());
            _UserDetails = LoginUserDetails();
        }
        public bool IsProductAdmin(string strUserRole)
        {
            bool bSuccess = false;
            string ProductAdminRole = _configuration.GetSection("UserRole")["ProductAdminRole"].ToString();

           // string ConnectionString = CryptographyHelper.Decrypt("wxYcunG9J8QexXLiehwNLNMnI84MGlJpqSldb40v/h5e1xGauI5v6Il1nxYiUYUI1i7ZRnikI3KugvdzhEkPu+YrywJFybsRkBpR6hH9B15W4/iPw7q4m+DELpZegad7HtKKxlYv/NJk0g5djM8y6WkpcUhwlJ7AM1YnU/yiDqE=");
            string ConnectionString1 = CryptographyHelper.Encrypt("Data Source=**************;Initial Catalog=CVRoot_Core;User ID=sa;Password=*********;TrustServerCertificate=True;");
           string pass = CryptographyHelper.Decrypt("QyJUS3CHAsvDBZsVQuTRlZB76ZMBcnmNrDQWHr1TrnE=");
            //string Pass = CryptographyHelper.Encrypt("*********");
            string ConnectionString2 = CryptographyHelper.Decrypt(_configuration.GetSection("Connectionstrings")["MsSql"].ToString());


            if (strUserRole == ProductAdminRole)
            {
                bSuccess = true;
            }
            return bSuccess;
        }

        public bool IsSuperAdmin(string strUserRole)
        {
            bool bSuccess = false;
            string SuperAdminRole = _configuration.GetSection("UserRole")["SuperAdminRole"].ToString();

            if (strUserRole == SuperAdminRole)
            {
                bSuccess = true;
            }
            return bSuccess;
        }

        public ManageUsersDetails? LoginUserDetails()
        {
            ManageUsersDetails? objUserDetails = null;
            try
            {
                var HttpContext = _HttpContextAccessor.HttpContext;
                if (HttpContext != null)
                {
                    var LoginUserDetailsJSON = HttpContext.Session.GetString("LoginUserDetails");

                    if (LoginUserDetailsJSON != null)
                    {
                        objUserDetails = new ManageUsersDetails();
                        objUserDetails = JsonConvert.DeserializeObject<ManageUsersDetails>(LoginUserDetailsJSON);
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }

            return objUserDetails;
        }

        public bool EnsureUserLoggedIn(ManageUsersDetails objManageUsersDetails)
        {
            bool bIsLoggedIn = true;
            try
            {
                if (objManageUsersDetails == null)
                {
                    return bIsLoggedIn = false;
                }
                return bIsLoggedIn;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return bIsLoggedIn = false;
            }
        }

        #region PopulateDropdowns
        public List<OrgInfo> BindOrg(int iOrgGroupID)
        {
            List<OrgInfo> OrgGroupList = new List<OrgInfo>();
            if (IsProductAdmin(_UserDetails.UserRole))
            {
                OrgGroupList = _ProcessSrv.GetOrganizationMasterList();
            }
            else if (IsSuperAdmin(_UserDetails.UserRole))
            {
                if (Convert.ToInt32(iOrgGroupID) > 0)
                {
                    OrgGroupList = _ProcessSrv.GetOrganizationByOrgGroupId(iOrgGroupID);
                }
                else
                {
                    OrgGroupList = _ProcessSrv.GetOrganizationMasterList();
                }
            }
            else
            {
                OrgGroupList = _ProcessSrv.GetOrganizationMasterListbyOrgLevelAccess(_UserDetails.UserRoleID.ToString(), _UserDetails.OrgGroupID.ToString(), _UserDetails.UserID.ToString());
            }
            return OrgGroupList;
        }

        public List<OrgUnit> BindUnit(int iOrgID)
        {
            List<OrgUnit> OrgUnitList = new List<OrgUnit>();
            if (IsProductAdmin(_UserDetails.UserRole))
            {
                OrgUnitList = _ProcessSrv.GetOrganizationUnitList_New(Convert.ToInt32(iOrgID));
            }
            else if (IsSuperAdmin(_UserDetails.UserRole))
            {
                if (_UserDetails.OrgID > 0)
                {
                    OrgUnitList = _ProcessSrv.GetOrganizationUnitList_New(Convert.ToInt32(iOrgID));
                }
                else
                {
                    OrgUnitList = _ProcessSrv.PopulateUnitsByOrgGroupID(_UserDetails.OrgGroupID.ToString());
                }
            }
            else
            {
                OrgUnitList = _ProcessSrv.GetUnitsListByOrgIDByOrgAcessLevel(iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.OrgGroupID.ToString(), _UserDetails.UserID.ToString());
            }

            return OrgUnitList;
        }

        public List<DepartmentInfo> BindFunction(int iUnitID)
        {
            List<DepartmentInfo> DepartmentList = new List<DepartmentInfo>();
            if (IsProductAdmin(_UserDetails.UserRole))
            {
                if (_UserDetails.UserID == 0)
                    DepartmentList = _ProcessSrv.GetDepartmentList(_UserDetails.UserRoleID.ToString(), Convert.ToInt32(_UserDetails.OrgID));
                else
                    DepartmentList = _ProcessSrv.GetDepartmentByUnitId(iUnitID);

            }
            else if (IsSuperAdmin(_UserDetails.UserRole))
            {
                if (iUnitID > 0)
                {
                    DepartmentList = _ProcessSrv.GetDepartmentByUnitId(iUnitID);
                }
                else
                {
                    DepartmentList = _ProcessSrv.PopulateDepartmentByOrgGroup(Convert.ToInt32(_UserDetails.OrgGroupID));
                }
            }
            else
            {
                DepartmentList = _ProcessSrv.GetDepartmentByUnitIdByOrgLevelAccess(iUnitID, _UserDetails.UserRoleID.ToString(), _UserDetails.OrgGroupID.ToString(), _UserDetails.UserID.ToString());
            }
            return DepartmentList;
        }

        public List<SubFunction> BindSubFunction(int iFunctionID)
        {
            List<SubFunction> subFunctionList = new List<SubFunction>();
            if (IsProductAdmin(_UserDetails.UserRole))
            {
                if (iFunctionID != 0)
                    subFunctionList = _ProcessSrv.GetSubFunctionListByFunctionID(iFunctionID.ToString());
                else
                    subFunctionList = _ProcessSrv.GetSubFunctionList_New(iFunctionID);
            }
            else if (IsSuperAdmin(_UserDetails.UserRole))
            {
                if (iFunctionID > 0)
                {
                    subFunctionList = _ProcessSrv.GetSubFunctionListByFunctionID(iFunctionID.ToString());
                }
                else
                {
                    subFunctionList = _ProcessSrv.GetSubFunctionList_ByOrgGroupID(_UserDetails.OrgGroupID);
                }

            }
            else
            {
                subFunctionList = _ProcessSrv.GetSubFunctionListByFunctionIDByorgLevelAccess(iFunctionID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.OrgGroupID.ToString(), _UserDetails.UserID.ToString());
            }

            return subFunctionList;
        }

        #endregion


        #region Incident

        public string GetNotificationType(string strNotificationId)
        {
            string strNotificationType = string.Empty;

            try
            {
                switch (strNotificationId)
                {
                    case var _ when strNotificationId == ((int)BCPEnum.IncidentNotfnType.Live).ToString():
                        strNotificationType = BCPEnum.IncidentNotfnType.Live.ToString();
                        break;

                    case var _ when strNotificationId == ((int)BCPEnum.IncidentNotfnType.Drill).ToString():
                        strNotificationType = BCPEnum.IncidentNotfnType.Drill.ToString();
                        break;

                    case var _ when strNotificationId == ((int)BCPEnum.IncidentNotfnType.TableTop).ToString():
                        strNotificationType = BCPEnum.IncidentNotfnType.TableTop.ToString();
                        break;

                    default:
                        // Optional: Handle invalid input
                        strNotificationType = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }

            return strNotificationType;
        }

        public string GetIncidentStatus(string strStatusId)
        {
            string strStatus = string.Empty;
            try
            {
                switch (strStatusId)
                {
                    case var _ when strStatusId == ((int)BCPEnum.IncidentStatus.InActive).ToString():
                        strStatus = BCPEnum.IncidentStatus.InActive.ToString();
                        break;

                    case var _ when strStatusId == ((int)BCPEnum.IncidentStatus.InProgress).ToString():
                        strStatus = BCPEnum.IncidentStatus.InProgress.ToString();
                        break;

                    case var _ when strStatusId == ((int)BCPEnum.IncidentStatus.Completed).ToString():
                        strStatus = BCPEnum.IncidentStatus.Completed.ToString();
                        break;

                    case var _ when strStatusId == ((int)BCPEnum.IncidentStatus.InComplete).ToString():
                        strStatus = BCPEnum.IncidentStatus.InComplete.ToString();
                        break;

                    case var _ when strStatusId == ((int)BCPEnum.IncidentStatus.TaskAssigned).ToString():
                        strStatus = BCPEnum.IncidentStatus.TaskAssigned.ToString();
                        break;

                    default:
                        strStatus = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return strStatus;
        }

        #endregion        


        #region Language Culture
        public List<LangaugeMaster> GetAllLanguage()//New
        {
            List<LangaugeMaster> lstLangaugeMaster = new List<LangaugeMaster>();
            try
            {

                lstLangaugeMaster = _ProcessSrv.LangaugeMasterGetAll();

                //var lstItemApp = new (lstLangaugeMaster.Contains.LanguageName + " ( " + lstLangaugeMaster.UICulture + " )", lstLangaugeMaster.Culture + "," + lstLangaugeMaster.UICulture);

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstLangaugeMaster;

        }
        #endregion

        #region Calendar Activity Type DropDown
        public List<AddCalendarActivity> PopulateCalendarActivityType()
        {
            List<AddCalendarActivity> lstCalendarActivityType = new List<AddCalendarActivity>();
            try
            {
                lstCalendarActivityType = _ProcessSrv.GetActivityAll();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstCalendarActivityType;
        }

        public List<CalendarStatusEnums> PopulateCalederStatus()
        {
            List<CalendarStatusEnums> lstCalenderStatus = new List<CalendarStatusEnums>();
            try
            {
                string[] enumCalenderStatus = Enum.GetNames(typeof(BCPEnum.CalenderStatus));
                foreach (string item in enumCalenderStatus)
                {
                    int value = (int)Enum.Parse(typeof(BCPEnum.CalenderStatus), item);
                    lstCalenderStatus.Add(new CalendarStatusEnums { StatusID = value, StatusName = item });
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstCalenderStatus;
        }
        #endregion

        public List<MonthsEnums> PopulateMonths()
        {
            List<MonthsEnums> lstMonths = new List<MonthsEnums>();
            try
            {
                string[] enumMonths = Enum.GetNames(typeof(BCPEnum.Months));
                foreach (string item in enumMonths)
                {
                    int value = (int)Enum.Parse(typeof(BCPEnum.Months), item);
                    lstMonths.Add(new MonthsEnums { MonthId = value, MonthName = item });
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstMonths;
        }

        public List<ReviewReportEnums> PopulateReportStatus()
        {
            List<ReviewReportEnums> lstStatus = new List<ReviewReportEnums>();
            try
            {
                string[] enumStatus = Enum.GetNames(typeof(BCPEnum.ReviewReportStatus));
                foreach (string item in enumStatus)
                {
                    int value = (int)Enum.Parse(typeof(BCPEnum.ReviewReportStatus), item);
                    lstStatus.Add(new ReviewReportEnums { StatusId = value, StatusName = item });
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstStatus;
        }

        #region NotoficatioAsEnum
        public List<NotificationAsEnums> PolulateNotificationAs()
        {
            List<NotificationAsEnums> lstNotificationAs = new List<NotificationAsEnums>();
            try
            {
                string[] enumNotificationAs = Enum.GetNames(typeof(BCPEnum.NotificationAs));
                foreach (string item in enumNotificationAs)
                {
                    int value = (int)Enum.Parse(typeof(BCPEnum.NotificationAs), item);
                    lstNotificationAs.Add(new NotificationAsEnums { NotificationAsID = value, NotificationAsName = item });
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstNotificationAs;
        }

        public List<StepStatusEnums> PopulateStepStatus()
        {
            List<StepStatusEnums> lstSteps = new List<StepStatusEnums>();
            try
            {
                string[] enumSteps = Enum.GetNames(typeof(BCPEnum.StepStatus));
                foreach (string item in enumSteps)
                {
                    int value = (int)Enum.Parse(typeof(BCPEnum.StepStatus), item);
                    lstSteps.Add(new StepStatusEnums { StatusId=value, StatusName=item });
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstSteps;
        }
        #endregion

        #region NotifyUserValidation
        public bool ValidateSelectedResourceOrTeams(int usersFYA, int usersFYI, int teamsFYA, int teamsFYI)
        {
            bool bIsValid = false;
            try
            {
                if (usersFYA != 0 || usersFYI != 0 || usersFYI != 0 || teamsFYI != 0)
                {
                    bIsValid = true;
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return bIsValid;
        }
        #endregion

        #region

        public static string GetFormattedRPO(int Minutes)
        {

            //var mth = Math.Floor((decimal)Minutes / (1440*30));
            //var d = Math.Floor((decimal)Minutes / 1440);
            //var h = Math.Floor((Minutes - d * 1440) / 60);
            //var m = Minutes - (d * 1440) - (h * 60);
            var mth = Math.Floor((decimal)Minutes / (1440 * 30));
            var d = Math.Floor((Minutes - mth * 1440 * 30) / 1440);
            var h = Math.Floor((Minutes - (d * 1440 + mth * 1440 * 30)) / 60);
            var m = Minutes - (d * 1440 + mth * 1440 * 30 + h * 60);

            var timeFormat = "";
            if (mth > 0)
                timeFormat = timeFormat + mth + " Month(s)";
            if (d > 0)
                timeFormat = timeFormat + d + " Day(s)";
            if (h > 0)
                timeFormat = timeFormat + h + " Hour(s)";
            if (m > 0)
                timeFormat = timeFormat + m + " Minute(s)";

            if (string.IsNullOrEmpty(timeFormat))
                timeFormat = "NA";
            return timeFormat;
        }

        public static string GetFormattedRTO(int durationInMinute)
        {
            string Days = string.Empty;
            string Hours = string.Empty;
            string Mins = string.Empty;
            try
            {

                if (durationInMinute > 0)
                {
                    TimeSpan timeSpan = TimeSpan.FromMinutes(durationInMinute);

                    Days = timeSpan.Days >= 1 ? timeSpan.Days + "Day(s)" : string.Empty;
                    Hours = timeSpan.Hours >= 1 ? timeSpan.Hours + "Hour(s)" : string.Empty;
                    Mins = timeSpan.Minutes >= 1 ? timeSpan.Minutes + "Min(s)" : string.Empty;
                }
            }
            catch (Exception)
            {
                return "NA";
            }
            return Days + Hours + Mins;
        }


        #endregion

        #region EntityID wise Entity's

        public List<BusinessProcessInfo> GetAllProcessListForDropdown(int iOrgID)
        {
            try
            {
                return _ProcessSrv.GetBusinessProcessMasterList(iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return new List<BusinessProcessInfo>();
            }
        }

        public List<LocationMaster> GetAllLocationListForDropDown(int iOrgID)
        {
            try
            {
                return _ProcessSrv.GetLocationMasterList(iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return new List<LocationMaster>();
            }
        }
        public List<ResourcesInfo> GetAllPeopleListForDropDown(int iOrgID)
        {
            try
            {
                return _ProcessSrv.GetResourcesListForDDL(iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return new List<ResourcesInfo>();
            }
        }
        public List<CompanyMasterInfo> GetAllThirdPartyListForDropDown(int iOrgID)
        {
            try
            {
                return _ProcessSrv.GetCompanyList(iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return new List<CompanyMasterInfo>();
            }
        }
        public List<Applications> GetAllApplicationListForDropDown(int iOrgID)
        {
            try
            {
                return _ProcessSrv.GetApplicationList(iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return new List<Applications>();
            }
        }

        #endregion

        #region Org Structure

        public List<OrgGroup> GetOrgGroupList()
        {
            List<OrgGroup> orgGroups = new List<OrgGroup>();
            try
            {
                orgGroups = _ProcessSrv.GetOrgGroupList();
            }
            catch (Exception ex)
            {

                _CVLogger.LogErrorUtilities(ex);
            }
            return orgGroups;
        }

        public List<OrgGroup> GetAlOrgGroupList()
        {
            List<OrgGroup> orgGroups = new List<OrgGroup>();
            try
            {
                orgGroups = _ProcessSrv.GetAllOrgGroupList();
            }
            catch (Exception ex)
            {

                _CVLogger.LogErrorUtilities(ex);
            }
            return orgGroups;
        }

        public List<OrgInfo?> PupulateOrganisation(string OrgGroupID = "0", string UserRoleID = "0")
        {
            List<OrgInfo> OrganizationInfoList = new List<OrgInfo>();
            try
            {
                if (IsProductAdmin(_UserDetails.UserRole.ToString()))
                {
                    OrganizationInfoList = _ProcessSrv.GetOrganizationMasterList();
                }
                else if (IsSuperAdmin(_UserDetails.UserRole.ToString()))
                {
                    OrganizationInfoList = _ProcessSrv.GetOrganizationMasterList_ByOrgGroupID(OrgGroupID);
                }
                else
                {
                    OrganizationInfoList = _ProcessSrv.GetOrganizationMasterListbyOrgLevelAccess(UserRoleID, OrgGroupID, _UserDetails.UserID.ToString());

                }
                //OrganizationInfoList = _ProcessSrv.GetOrganizationMasterList_ByOrgGroupID(OrgGroupID);


                //OrganizationInfoList.Add(new OrgInfo { Id = "0", OrganizationName = "-- All Organizations --" });
                // return OrganizationInfoList;
            }
            catch (Exception ex)
            {
                //OrganizationInfoList = null;
                _CVLogger.LogErrorUtilities(ex);
            }
            return OrganizationInfoList;
        }

        public List<OrgUnit?> PupulateUnit(string OrgGroupID = "0", string OrgID = "0", string UserRoleID = "0")
        {
            List<OrgUnit?> orgUnitsList = new List<OrgUnit?>();
            try
            {
                if (IsProductAdmin(_UserDetails.UserRole.ToString()))
                {
                    orgUnitsList = _ProcessSrv.GetOrganizationUnitList_New(Convert.ToInt32(OrgID));
                }
                else if (IsSuperAdmin(_UserDetails.UserRole.ToString()))
                {
                    orgUnitsList = _ProcessSrv.PopulateUnitsByOrgGroupID(OrgGroupID);
                }
                else
                {
                    orgUnitsList = _ProcessSrv.GetUnitsListByOrgIDByOrgAcessLevel(OrgID, UserRoleID, OrgGroupID, _UserDetails.UserID.ToString());

                }

            }
            catch (Exception ex)
            {
                //OrganizationInfoList = null;
                _CVLogger.LogErrorUtilities(ex);
            }
            return orgUnitsList;
        }

        public List<DepartmentInfo?> PupulateDepartment(string strOrgGroupID = "0", string strOrgID = "0", string strUserRoleID = "0", string strUnitID = "0")
        {
            List<DepartmentInfo?> DepartmentInfoList = new List<DepartmentInfo?>();
            try
            {
                if (IsProductAdmin(_UserDetails.UserRole.ToString()))
                {
                    if (strUnitID != "0")
                    {
                        DepartmentInfoList = GetDepartmentMasterAll();
                    }
                    else
                    {
                        DepartmentInfoList = _ProcessSrv.GetDepartmentByUnitId(Convert.ToInt32(strUnitID));
                    }

                }
                else if (IsSuperAdmin(_UserDetails.UserRole.ToString()))
                {
                    if (strUnitID != "0")
                    {
                        DepartmentInfoList = _ProcessSrv.GetDepartmentByUnitId(Convert.ToInt32(strUnitID));
                    }
                    else
                    {
                        DepartmentInfoList = _ProcessSrv.PopulateDepartmentByOrgGroup(Convert.ToInt32(strOrgGroupID));
                    }
                }
                else
                {
                    DepartmentInfoList = _ProcessSrv.GetDepartmentByUnitIdByOrgLevelAccess(Convert.ToInt32(strUnitID), strUserRoleID, strOrgGroupID, _UserDetails.UserID.ToString());
                }

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return DepartmentInfoList;
        }

        public List<SubFunction?> PupulateSubDepartment(string strOrgGroupID = "0", string strOrgID = "0", string strUserRoleID = "0", string strDepartment = "0")
        {
            List<SubFunction?> SubDepartmentInfoList = new List<SubFunction?>();
            try
            {

                if (IsProductAdmin(_UserDetails.UserRole.ToString()))
                {
                    SubDepartmentInfoList = _ProcessSrv.GetSubFunctionListByFunctionID(strDepartment);
                }
                else if (IsSuperAdmin(_UserDetails.UserRole.ToString()))
                {
                    if (Convert.ToInt32(strDepartment) > 0)
                    {
                        SubDepartmentInfoList = _ProcessSrv.GetSubFunctionListByFunctionID(strDepartment);
                    }
                    else
                    {
                        SubDepartmentInfoList = _ProcessSrv.GetSubFunctionList_ByOrgGroupID(Convert.ToInt32(strOrgGroupID));
                    }
                }
                else
                {
                    SubDepartmentInfoList = _ProcessSrv.GetSubFunctionListByFunctionIDByorgLevelAccess(strDepartment.ToString(), strUserRoleID, strOrgGroupID, _UserDetails.UserID.ToString());

                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return SubDepartmentInfoList;
        }


        public List<CompanyMasterInfo> BindCompany()
        {
            List < CompanyMasterInfo > lstCompanyMaster = new List<CompanyMasterInfo> ();
            if (IsProductAdmin(_UserDetails.UserRole.ToString()))
            {
                lstCompanyMaster = _ProcessSrv.GetCompanyList(_UserDetails.OrgID);
            }
            else if (IsSuperAdmin(_UserDetails.UserRole.ToString()))
            {
                if (_UserDetails.OrgID > 0)
                {
                    lstCompanyMaster = _ProcessSrv.GetCompanyList(_UserDetails.OrgID);
                }
                else
                {
                    lstCompanyMaster = _ProcessSrv.GetCompanyList(0);
                    lstCompanyMaster = lstCompanyMaster.Where(x => x.OrgGroupID == _UserDetails.OrgGroupID).ToList();
                    //Utilities.Utilities.PopulateCompanyByOrgGroupID(rdCompany, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_oUser.OrgGroupID));
                }
            }
            else
            {
                lstCompanyMaster = _ProcessSrv.GetCompanyList(_UserDetails.OrgID);
                lstCompanyMaster = lstCompanyMaster.Where(x => x.OrgGroupID == _UserDetails.OrgGroupID).ToList();
            }
            return lstCompanyMaster;
        }





        public List<OrgInfo?> GetOrganizationListByOrgGroupID_ForDropdown(string OrgGroupID = "0")
        {
            List<OrgInfo> OrganizationInfoList = new List<OrgInfo>();
            try
            {
                //OrganizationInfoList = _ProcessSrv.GetOrganizationMasterList_ByOrgGroupID(OrgGroupID);
                OrganizationInfoList = _ProcessSrv.GetOrganizationMasterList();

                //OrganizationInfoList.Add(new OrgInfo { Id = "0", OrganizationName = "-- All Organizations --" });
                // return OrganizationInfoList;
            }
            catch (Exception ex)
            {
                //OrganizationInfoList = null;
                _CVLogger.LogErrorUtilities(ex);
            }
            return OrganizationInfoList;
        }

        public List<OrgUnit> GetUnitListByOrgID(string RoleID = "0", int OrgID = 0)
        {
            List<OrgUnit> UnitList = new List<OrgUnit>();

            UnitList = _ProcessSrv.GetOrganizationUnitList(RoleID, OrgID);


            return UnitList;
        }

        //Unit List by orgID
        public List<OrgUnit> GetUnitListByOrgID(int OrgID = 0)
        {
            List<OrgUnit> UnitList = _ProcessSrv.GetOrganizationUnitList_New(OrgID);
            return UnitList;
        }

        public List<BCMEntitiesTypeMaster> PopulateBCMEntities()
        {
            List<BCMEntitiesTypeMaster> lstBCMEntitiesTypeMasterColl = new List<BCMEntitiesTypeMaster>();
            try
            {
                lstBCMEntitiesTypeMasterColl = _ProcessSrv.BCMEntitiesTypeMasterGetAll();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBCMEntitiesTypeMasterColl;
        }

        public List<BusinessProcessInfo> PopulateBusinessProcessMaster(int OrgID)
        {
            List<BusinessProcessInfo> objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(OrgID));
            return objBusinessProcessInfo;
        }

        public List<Facility> PopulateFacilitiesByUnitIdOrgid(int iOrgId, int iUnitID, string OrgGroupID)
        {
            List<Facility> objFacility = _ProcessSrv.GetAllFacilitieslistByUnitIDOrgId(iOrgId, iUnitID, OrgGroupID);
            return objFacility;
        }

        public List<Facility> PopulateFacilities()
        {
            List<Facility> objFacility = new List<Facility>();
            try
            {
                objFacility = _ProcessSrv.GetFacilitiesList();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return objFacility;
        }

        public List<OtherBCMEntities> PopulateOtherBCMEntities(int iOrgId, int iUnitID, int iFunctionID, int iSubfunId)
        {
            List<OtherBCMEntities> objOtherBCMEntities = _ProcessSrv.GetOtherBCMEntities(iOrgId, iUnitID, iFunctionID, iSubfunId);
            return objOtherBCMEntities;
        }


        public List<DepartmentInfo> GetDepartmentAllListForDropdown()
        {
            List<DepartmentInfo> DepartmentInfoList = new List<DepartmentInfo>();
            try
            {
                DepartmentInfoList = _ProcessSrv.GetAllDepartment();
                //DepartmentInfoList = _ProcessSrv.GetDepartmentByUnitId();
                //DepartmentInfoList.Add(new DepartmentInfo { DepartmentID = 0, DepartmentName = "-- All Departments --" });
                return DepartmentInfoList;
            }
            catch (Exception ex)
            {
                DepartmentInfoList.Add(new DepartmentInfo { DepartmentID = 0, DepartmentName = "-- All Departments --" });
                _CVLogger.LogErrorUtilities(ex);
            }
            return DepartmentInfoList;
        }



        public List<MenuRights> GetMenu()
        {
            List<MenuRights> lstMenuRights = new List<MenuRights>();
            try
            {
                lstMenuRights = _ProcessSrv.GetMenuName();
                return lstMenuRights;
            }
            catch (Exception ex)
            {
                lstMenuRights.Add(new MenuRights { MenuID = 0, MenuName = "-- All Menu --" });
                _CVLogger.LogErrorUtilities(ex);
            }

            return lstMenuRights;
        }


        public List<MenuRights> GetSubMenu()
        {
            List<MenuRights> lstSubMenuRights = new List<MenuRights>();
            try
            {
                lstSubMenuRights = _ProcessSrv.GetSubMenuMasterList();
                return lstSubMenuRights;
            }
            catch (Exception ex)
            {
                lstSubMenuRights.Add(new MenuRights { ID = 0, SubMenuName = "-- All SubMenu --" });
                _CVLogger.LogErrorUtilities(ex);
            }

            return lstSubMenuRights;
        }



        public List<DepartmentInfo> GetDepartmentMasterAll()
        {
            List<DepartmentInfo> DepartmentInfoList = new List<DepartmentInfo>();
            try
            {
                DepartmentInfoList = _ProcessSrv.GetDepartmentMasterAll();
                //DepartmentInfoList = _ProcessSrv.GetDepartmentByUnitId();
                //DepartmentInfoList.Add(new DepartmentInfo { DepartmentID = 0, DepartmentName = "-- All Departments --" });
                return DepartmentInfoList;
            }
            catch (Exception ex)
            {
                //DepartmentInfoList.Add(new DepartmentInfo { DepartmentID = 0, DepartmentName = "-- All Departments --" });
                _CVLogger.LogErrorUtilities(ex);
            }
            return DepartmentInfoList;
        }


        public List<SubFunction> GetAllSubDepartmentListDropdown()
        {
            List<SubFunction> SubDepartmentList = new List<SubFunction>();
            try
            {
                SubDepartmentList = _ProcessSrv.GetSubFunctionList_New();
            }
            catch (Exception ex)
            {
                SubDepartmentList.Add(new SubFunction { SubFunctionID = "0", SubFunctionName = "-- All SubDepartments --" });
                _CVLogger.LogErrorUtilities(ex);
            }


            return SubDepartmentList;
        }

        public List<DepartmentInfo> GetDepartmentByUnitId(int iUnitID)
        {
            List<DepartmentInfo> lstDepartmentInfo = new List<DepartmentInfo>();
            try
            {
                lstDepartmentInfo = _ProcessSrv.GetDepartmentByUnitId(iUnitID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstDepartmentInfo;
        }

        public List<Facility> GetFacilitieslistByUnitID(int iUnitid, int iOrgID, int iOrgGroupID = 0)
        {
            List<Facility> lstFacility = new List<Facility>();
            try
            {
                lstFacility = _ProcessSrv.GetFacilitieslistByUnitID(iUnitid, iOrgID, iOrgGroupID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstFacility;
        }

        public List<BusinessProcessInfo> GetAllProcessNames(int iOrgID, int iUnitID, int iDepartID = 0, int iSubDepartID = 0, int iEntityTypeID = 0)
        {
            List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
            try
            {
                lstBusinessProcessInfo = _ProcessSrv.GetAllProcessNames(iOrgID, iUnitID, iDepartID, iSubDepartID, iEntityTypeID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcessInfo;
        }

        #endregion

        #region BCM Facilities
        public List<Facility> GetFacilityListByUnitID(int UnitID)
        {
            List<Facility> FacilityList = new List<Facility>();
            try
            {
                FacilityList = _ProcessSrv.GetFacilitiesList(1);
                if (FacilityList.Count > 0)
                    return FacilityList;
            }
            catch (Exception ex)
            {
                FacilityList.Add(new Facility { FacilityID = 0, FacilityName = "-- All Facilities --" });
                _CVLogger.LogErrorUtilities(ex);
            }

            return FacilityList;
        }



        public List<Facility> PopulatFacilityType()
        {
            List<Facility> lstFacilityType = new List<Facility>();

            string[] enumNames = Enum.GetNames(typeof(BCPEnum.FacilityType));

            foreach (string item in enumNames)
            {
                int value = (int)Enum.Parse(typeof(BCPEnum.FacilityType), item);
                lstFacilityType.Add(new Facility { FacilityID = value, FacilityType = item });
            }

            return lstFacilityType;
        }
        #endregion
        public static string GetYesNo(int ID)
        {
            string Text = "No";
            if (ID == 1)
                Text = "Yes";

            return Text;
        }

        public static string isCriticalCss(int ID)
        {
            string Text = "text-success";
            if (ID == 1)
            {
                Text = "text-danger";
            }

            return Text;
        }
        #region BCM Resources
        public List<ResourcesInfo> GetAllResourceList()
        {
            List<ResourcesInfo> objresource = new List<ResourcesInfo>();
            try
            {
                objresource = _ProcessSrv.GetAllResourcesList().OrderBy(x => x.ResourceName).ToList();
            }
            catch (Exception ex)
            {
                //objresource.Add(new ResourcesInfo {  ResourceID= 0, ResourceName = "-- All ResourceName --" });
                _CVLogger.LogErrorUtilities(ex);
            }
            return objresource;
        }

        public List<ResourcesInfo> GetResources(int iOrgID)
        {
            List<ResourcesInfo> lstResourcesInfo = new List<ResourcesInfo>();
            try
            {
                if (IsProductAdmin(_UserDetails.UserRole))
                {
                    lstResourcesInfo = _ProcessSrv.GetResourcesList(iOrgID).OrderBy(x=> x.ResourceName).ToList();
                }
                else if (IsSuperAdmin(_UserDetails.UserRole))
                {
                    if (iOrgID > 0)
                    {
                        lstResourcesInfo = _ProcessSrv.GetResourceInfoListByOrgID(iOrgID).OrderBy(x => x.ResourceName).ToList();
                    }
                    else
                    {
                        lstResourcesInfo = _ProcessSrv.GetResourcesListByOrgGrpID(_UserDetails.OrgGroupID).OrderBy(x => x.ResourceName).ToList();
                    }
                }
                else
                {
                    lstResourcesInfo = _ProcessSrv.GetResourcesList(iOrgID).OrderBy(x => x.ResourceName).ToList();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return lstResourcesInfo;
        }

        public List<ResourceDesignation> GetAllResourceDesignationList()
        {
            List<ResourceDesignation> objresource = new List<ResourceDesignation>();
            try
            {
                objresource = _ProcessSrv.GetAllResourceDesignation();
            }
            catch (Exception ex)
            {
                //objresource.Add(new ResourcesInfo {  ResourceID= 0, ResourceName = "-- All ResourceName --" });
                _CVLogger.LogErrorUtilities(ex);
            }
            return objresource;
        }


        public List<ResourcesInfo> GetResourceInfoListByOrgID(int OrgID = 0)
        {
            List<ResourcesInfo> objresource = new List<ResourcesInfo>();
            try
            {
                objresource = _ProcessSrv.GetResourceInfoListByOrgID(OrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return objresource;
        }

        public List<UserRoleMasterInfo> GetUserRoleMasterByOrgID(int iOrgID)
        {
            List<UserRoleMasterInfo> userGroupList;

            try
            {
                userGroupList = _ProcessSrv.GetUserRoleMaster(iOrgID);

            }
            catch (Exception ex)
            {
                userGroupList = new List<UserRoleMasterInfo>();
                _CVLogger.LogErrorUtilities(ex);
            }
            return userGroupList;
        }

        public List<TypeMasterInfo> GetTypeInfoByEntityID(int iEntityID)
        {
            List<TypeMasterInfo> typeMasterList = new List<TypeMasterInfo>();
            try
            {
                typeMasterList = _ProcessSrv.GetTypeInfoByEntityID(iEntityID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return typeMasterList;
        }


        #endregion

        #region CompanyMaster
        public List<CompanyMasterInfo> GetCompanyMasterInfoList(int iOrgID)
        {
            List<CompanyMasterInfo> companyMasterInfos = new List<CompanyMasterInfo>();
            try
            {
                companyMasterInfos = _ProcessSrv.GetCompanyList(iOrgID);
            }
            catch (Exception)
            {

                throw;
            }

            return companyMasterInfos;
        }
        #endregion

        #region CheckFileTypeandSize

        public bool CheckFileTypeandSize(string strExtention, int iFileSize, int iOrgID)
        {
            bool bIsValidFile = false;
            int iAllowedFileSize;
            string[] validFileTypes;
            try
            {
                VaultSettings objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(iOrgID);
                if (objVaultSettings != null)
                {
                    string strAllowedExt = objVaultSettings.AllowedExtension;
                    if (string.IsNullOrEmpty(strAllowedExt))
                    {
                        bIsValidFile = true;
                    }
                    else
                    {
                        validFileTypes = strAllowedExt.Split(",");
                        iAllowedFileSize = Convert.ToInt32(objVaultSettings.FileSize);

                        if (iFileSize <= ((iAllowedFileSize * 1024) * 1024))
                        {
                            int iPosition = Array.IndexOf(validFileTypes, strExtention.ToLower());
                            if (iPosition > -1)
                                bIsValidFile = true;
                        }
                    }
                }
                return bIsValidFile;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return bIsValidFile;
            }
        }

        #endregion

        #region ConvertToBlob

        //public byte[] ConvertToBlob(IFormFile file)
        //{
        //    try
        //    {
        //        using (BinaryReader reader = new BinaryReader(file.OpenReadStream()))
        //        {
        //            return reader.ReadBytes((int)file.Length);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        _CVLogger.LogErrorApp(ex); // Log the error
        //        return null; // Return null if an exception occurs
        //    }
        //}

        public byte[] ConvertToBlob(IFormFile file)
        {
            try
            {
                using (BinaryReader reader = new BinaryReader(file.OpenReadStream()))
                {
                    return reader.ReadBytes((int)file.Length);
                }

                //using (BinaryReader reader = new BinaryReader(file.OpenReadStream()))
                //{
                //    byte[] blob = reader.ReadBytes((int)file.Length);
                //    // Debugging statement to verify the blob content
                //    Console.WriteLine($"Blob length: {blob.Length}");
                //    return blob;                    
                //}
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex); // Log the error
                return null; // Return null if an exception occurs
            }
        }


        public void ConvertBlobToFile(byte[] blobData, string outputPath)
        {
            try
            {
                using (var fileStream = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
                {
                    fileStream.Write(blobData, 0, blobData.Length);
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex); // Log the error
            }
        }


        #endregion

        public VaultSettings? VaultConfigurations
        {
            get
            {
                ManageUsersDetails? ObjUser = new ManageUsersDetails(); ;


                var HttpContext = _HttpContextAccessor.HttpContext;
                if (HttpContext != null)
                {
                    var LoginUserDetailsJSON = HttpContext.Session.GetString("LoginUserDetails");

                    if (LoginUserDetailsJSON != null)
                    {
                        ObjUser = JsonConvert.DeserializeObject<ManageUsersDetails>(LoginUserDetailsJSON);
                    }
                }

                int orgID = 0;

                if (ObjUser != null)
                {
                    orgID = Convert.ToInt32(ObjUser.OrgID) == 0 ? 0 : Convert.ToInt32(ObjUser.OrgID);
                }

                if (Convert.ToInt32(ObjUser.OrgID) > 0)
                {
                    _objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt16(ObjUser.OrgID));
                }
                return _objVaultSettings;
            }
        }

        public List<TimeUnit> PopulateStepTimeUnit()
        {
            string[] enumNames = Enum.GetNames(typeof(BCPEnum.StepTimeUnit));

            List<TimeUnit> lstTimeUnit = new List<TimeUnit>();

            //lstType.Items.Add(new ListItem("-- Select --", "-1"));
            foreach (string item in enumNames)
            {
                //get the enum item value
                int value = (int)Enum.Parse(typeof(BCPEnum.StepTimeUnit), item);
                //string Name  = BCPEnum.StepTimeUnit.ToString();

                lstTimeUnit.Add(new TimeUnit { ID = value.ToString(), Name = item });
            }
            return lstTimeUnit;
        }


        public List<Impact> PopulateBiaSectionImpactStatus()
        {

            string[] enumNames = Enum.GetNames(typeof(BCPEnum.BiaSectionImpact));

            List<Impact> lstImpact = new List<Impact>();
            foreach (string item in enumNames)
            {
                int value = (int)Enum.Parse(typeof(BCPEnum.BiaSectionImpact), item);

                lstImpact.Add(new Impact { ImpactID = value, ImpactName = item });
            }

            return lstImpact;
        }


        public bool CheckFileType(string extension, int OrgID)
        {
            string[] validFileTypes;
            bool isValidFile = false;
            //BindAllowedExtension();
            VaultSettings objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(OrgID);
            if (objVaultSettings != null)
            {
                string AllowedExt = objVaultSettings.AllowedExtension.ToLower();
                if (string.IsNullOrEmpty(AllowedExt))
                    isValidFile = false;
                else
                {
                    validFileTypes = AllowedExt.Split(',');
                    int position = Array.IndexOf(validFileTypes, extension.ToLower());

                    if (position > -1)
                        isValidFile = true;
                }
            }


            return isValidFile;
        }

        public bool CheckFileSize(int filesize, int OrgID)
        {
            int AllowedFileSize;
            bool isValidFile = false;

            //BindAllowedExtension();

            VaultSettings objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(OrgID);
            if (objVaultSettings != null)
            {
                AllowedFileSize = Convert.ToInt32(objVaultSettings.FileSize);
                if (filesize <= ((AllowedFileSize * 1024) * 1024))
                {
                    isValidFile = true;
                }
            }
            return isValidFile;
        }
        public List<BusinessParameterProfiles> GetBusinessParameterProfileList(int iOrgID)
        {

            List<BusinessParameterProfiles> lstBusinessParameterProfile = _ProcessSrv.BusinessParameterProfile_byOrgID(iOrgID);

            return lstBusinessParameterProfile;
        }

        public DateTime GetTimeUnit(string strTimeTaken, string strTimeOut)
        {
            string strTimeUnit = string.Empty;
            string strDateFormat = _configuration["ConfigurationValue:DateFormat"].ToString();
            string strDateTimeFormat = _configuration["ConfigurationValue:DateTimeFormat"].ToString();
            try
            {
                strTimeOut = strTimeOut == null ? "1" : strTimeOut;

                //if (strTimeOut == null)
                //{
                //    strTimeOut = "1";
                //}

                if (strTimeOut == "1")
                    strTimeUnit = DateTime.Now.AddMinutes(Convert.ToDouble(strTimeTaken)).ToString(strDateTimeFormat);
                else if (strTimeOut == "2")
                    strTimeUnit = DateTime.Now.AddHours(Convert.ToDouble(strTimeTaken)).ToString(strDateTimeFormat);
                else if (strTimeOut == "3")
                    strTimeUnit = DateTime.Now.AddDays(Convert.ToDouble(strTimeTaken)).ToString(strDateTimeFormat);
                else if (strTimeOut == "4")
                    strTimeUnit = DateTime.Now.AddMonths(Convert.ToInt32(strTimeTaken)).ToString(strDateTimeFormat);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            DateTime dtDateTime = Convert.ToDateTime(strTimeUnit);
            return dtDateTime;
        }

        public int RiskProfileID
        {
            get
            {
                var HttpContext = _HttpContextAccessor.HttpContext;
                if (HttpContext.Session.GetString("LoginUserDetails") != null)
                {
                    ManageUsersDetails objManageUsersDetails;
                    var LoginUserDetailsJSON = HttpContext.Session.GetString("LoginUserDetails");
                    objManageUsersDetails = JsonConvert.DeserializeObject<ManageUsersDetails>(LoginUserDetailsJSON);
                    return _ProcessSrv.GetDefaultProfile(Convert.ToInt32((objManageUsersDetails.OrgID)));
                }
                else
                {
                    return 0;
                }


            }
        }


        public List<ImpactType> BindImpactType()
        {
            List<ImpactType> lstImpactType = new List<ImpactType>();
            try
            {
                lstImpactType = _ProcessSrv.GetImpactTypeList();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }

            return lstImpactType;
        }


        public List<BIASection> BindBIASurveySection(int iOrgID = 0)
        {
            List<BIASection> lstBIASection = new List<BIASection>();
            try
            {
                lstBIASection = _ProcessSrv.GetBIASurveySectionList(iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }

            return lstBIASection;
        }


        public List<Impact> BindImpactList()
        {
            List<Impact> lstImpact = new List<Impact>();
            try
            {
                lstImpact = _ProcessSrv.GetImpactList();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }

            return lstImpact;
        }





        public List<BIATimeInterval> GetBIATimeInterval(int OrgID)
        {
            List<BIATimeInterval> lstTimeInterval = new List<BIATimeInterval>();
            try
            {
                lstTimeInterval = _ProcessSrv.GetBIATimeIntervalListAll(OrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }

            return lstTimeInterval;
        }




        public List<RiskImpactMaster> PopulateRiskSeverity(int iOrgID)
        {
            List<RiskImpactMaster> lstRiskImpactMaster = new List<RiskImpactMaster>();
            try
            {
                int iDefaultProfileID = RiskProfileID;
                lstRiskImpactMaster = _ProcessSrv.GetAllRiskSeverityDetails(iOrgID, iDefaultProfileID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstRiskImpactMaster;
        }

        public List<BusinessProcessInfo> PopulateBusinessProcessMasterNew(int iOrgID)
        {
            List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
            try
            {
                lstBusinessProcessInfo = _ProcessSrv.GetBusinessProcessMasterList(iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcessInfo;
        }

        public List<BusinessProcessInfo> FilterListForOwner(List<BusinessProcessInfo> lstBusinessProcess, int OrgID = 0, int UnitID = 0, int DepartmentID = 0, int SubDepartmentID = 0, int BCMEntityTypeID = 0, int IsUnderBCM = -1)
        {
            try
            {
                int UserID = _UserDetails.UserID;
                List<OrgRoleRights> dtRoleRights1 = _ProcessSrv.GetMasterPageAccessListByRoleID(_UserDetails.UserRoleID.ToString(), _UserDetails.UserID.ToString(), _UserDetails.OrgID.ToString());

                dtRoleRights1 = dtRoleRights1.Where(x => x.OrgID == _UserDetails.OrgID && x.RoleRightsSubEntityID == (int)BCPEnum.RoleRightsSubEntityID.MasterScreensPrivileges
                                               && x.MasterEntryTypeID == (int)BCPEnum.MasterPageType.OverAllAccess
                                               && x.ParentPrivilegeID == (int)BCPEnum.ParentPrivilegeID.View
                                               && x.PrivilegeID == (int)BCPEnum.PrivilegeID.View).ToList();

                List<BusinessProcessInfo> strIfOrgHead = lstBusinessProcess.Where(x => x.OrgID == _UserDetails.OrgID || x.ProcessOwnerID == UserID || x.AltProcessOwnerID == UserID || x.ApproverID == UserID || x.CreatedBy == UserID).ToList();
                List<BusinessProcessInfo> strIfUnitHead = lstBusinessProcess.Where(x => x.UnitHeadID == UserID || x.AltUnitHeadId == UserID || x.UnitBCPCorID == UserID || x.AltBCPCorID == UserID || x.ProcessOwnerID == UserID || x.AltProcessOwnerID == UserID || x.ApproverID == UserID || x.CreatedBy == UserID).ToList();
                List<BusinessProcessInfo> strIfFunctionHead = lstBusinessProcess.Where(x => x.DepartmentHeadID == UserID || x.AltDepartmentHeadID == UserID || x.ProcessOwnerID == UserID || x.AltProcessOwnerID == UserID || x.ApproverID == UserID || x.CreatedBy == UserID).ToList();
                List<BusinessProcessInfo> strIfSubFunctionHead = lstBusinessProcess.Where(x => x.SubFunOwnerId == UserID || x.AltSubFunOwnerId == UserID || x.ProcessOwnerID == UserID || x.AltProcessOwnerID == UserID || x.ApproverID == UserID || x.CreatedBy == UserID).ToList();
                List<BusinessProcessInfo> strIfNot = lstBusinessProcess.Where(x => x.ProcessOwnerID == UserID || x.AltProcessOwnerID == UserID || x.ApproverID == UserID || x.CreatedBy == UserID).ToList();

                //if (lstBusinessProcess.All(strIfNot.Contains))
                //    lstBusinessProcess = IsUnitHead(UserID.ToString()) == true ? strIfUnitHead : strIfNot;
                
                
                //lstBusinessProcess = IsOrgHead(UserID.ToString()) == true ? strIfOrgHead : strIfNot;

                if (dtRoleRights1.Count > 0)
                {
                    strIfNot.Clear();
                    strIfUnitHead.Clear();
                    strIfFunctionHead.Clear();
                    strIfSubFunctionHead.Clear();
                }

                if (IsOrgHead(UserID.ToString(),_UserDetails.OrgID.ToString()))
                {
                    lstBusinessProcess = strIfOrgHead;
                }
                else
                {
                    if (IsUnitHead(UserID.ToString()))
                    {
                        lstBusinessProcess = strIfUnitHead;
                    }
                    else
                    {
                        if (IsFunctionHead(UserID.ToString()))
                        {
                            lstBusinessProcess = strIfFunctionHead;
                        }
                        else
                        {
                            if (IsSubFunctionHead(UserID.ToString()))
                            {
                                lstBusinessProcess = strIfSubFunctionHead;
                            }
                            else
                            {
                                lstBusinessProcess = strIfNot;
                            }
                        }    
                    }
                }
                
                
                
                //if (lstBusinessProcess.whe(strIfOrgHead.Contains))
                //    lstBusinessProcess = IsOrgHead(UserID.ToString()) == true ? strIfOrgHead : strIfNot;

                //if (lstBusinessProcess.All(strIfUnitHead.Contains))
                //    lstBusinessProcess = IsUnitHead(UserID.ToString()) == true ? strIfUnitHead : strIfNot;

                //if (lstBusinessProcess.All(strIfFunctionHead.Contains))
                //    lstBusinessProcess = IsFunctionHead(UserID.ToString()) == true ? strIfFunctionHead : strIfNot;

                //if (lstBusinessProcess.All(strIfSubFunctionHead.Contains))
                //    lstBusinessProcess = IsSubFunctionHead(UserID.ToString()) == true ? strIfSubFunctionHead : strIfNot;


                List<OrgRoleRights> dtRoleRights = new List<OrgRoleRights>();
                dtRoleRights = _ProcessSrv.GetOrganizationalAccessListByRoleAndUserID(Convert.ToInt32(_UserDetails.UserRoleID), Convert.ToInt32(_UserDetails.UserID), Convert.ToInt32(_UserDetails.OrgID));

                dtRoleRights = dtRoleRights.Where(x => x.RecordID != 0).ToList();

                if (dtRoleRights.Count > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => dtRoleRights.Any(y => y.RecordID == x.RecordID && y.EntityTypeID == x.EntityTypeID)).ToList();

                }


                if (OrgID > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID == OrgID).ToList();
                }
                if (UnitID > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > 0 && x.UnitID == UnitID).ToList();
                }
                if (DepartmentID > 0)
                {
                    if (UnitID == 0)
                    {
                        lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID > 0 && x.DepartmentID == DepartmentID).ToList();
                    }
                    else
                    {
                        lstBusinessProcess = lstBusinessProcess.Where(x => x.UnitID == UnitID && x.DepartmentID == DepartmentID).ToList();
                    }
                }
                if (SubDepartmentID > 0)
                {
                    if (DepartmentID == 0 && UnitID == 0)
                    {
                        lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID > 0 && x.DepartmentID > 0 && x.SubfunctionID == SubDepartmentID).ToList();
                    }
                    else
                    {
                        lstBusinessProcess = lstBusinessProcess.Where(x => x.UnitID == UnitID && x.DepartmentID == DepartmentID && x.SubfunctionID == SubDepartmentID).ToList();
                    }
                }
                if (BCMEntityTypeID > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.EntityTypeID == BCMEntityTypeID).ToList();
                }
                if (BCMEntityTypeID == -1)
                {
                    //lstBusinessProcess = lstBusinessProcess.Where(x => x.EntityTypeID  BCMEntityTypeID).ToList();
                }
                if (IsUnderBCM > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.ProcessID > 0).ToList();
                }
                if (IsUnderBCM == 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.ProcessID == 0).ToList();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcess;
        }

        public List<BusinessProcessInfo> GetBusinessProcess(List<BusinessProcessInfo> lstBusinessProcess, int OrgID = 0, int UnitID = 0, int DepartmentID = 0, int SubDepartmentID = 0, int IsUnderBCM = 0)
        {
            try
            {
                if (IsProductAdmin(_UserDetails.UserRole) || IsSuperAdmin(_UserDetails.UserRole))
                {
                    if (IsUnderBCM == -1)
                    {
                        if (OrgID > 0)
                        {
                            lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID == OrgID).ToList();
                        }
                        if (UnitID > 0)
                        {
                            lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > 0 && x.UnitID == UnitID).ToList();
                        }
                        if (DepartmentID > 0)
                        {
                            if (UnitID == 0)
                            {
                                lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID > 0 && x.DepartmentID == DepartmentID).ToList();
                            }
                            else
                            {
                                lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID == UnitID && x.DepartmentID == DepartmentID).ToList();
                            }
                        }
                        if (SubDepartmentID > 0)
                        {
                            if (DepartmentID == 0 && UnitID == 0)
                            {
                                lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID > 0 && x.DepartmentID > 0 && x.SubfunctionID == SubDepartmentID).ToList();
                            }
                            else
                            {
                                lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID == UnitID && x.DepartmentID == DepartmentID && x.SubfunctionID == SubDepartmentID).ToList();
                            }
                        }

                    }
                    else
                    {
                        if (OrgID > 0)
                        {
                            lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID == OrgID && x.ProcessCode != string.Empty).ToList();
                        }
                        if (UnitID > 0)
                        {
                            lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > 0 && x.UnitID == UnitID && x.ProcessCode != string.Empty).ToList();
                        }
                        if (DepartmentID > 0)
                        {
                            if (UnitID == 0)
                            {
                                lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID > 0 && x.DepartmentID == DepartmentID).ToList();
                            }
                            else
                            {
                                lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID == UnitID && x.DepartmentID == DepartmentID).ToList();
                            }
                            //lstBusinesProcess = lstBusinesProcess.Where(x => x.OrgID == OrgID && x.UnitID == UnitID && x.DepartmentID == DepartmentID && x.IsBCMEntity == IsUnderBCM).ToList();
                        }
                        if (SubDepartmentID > 0)
                        {
                            if (DepartmentID == 0 && UnitID == 0)
                            {
                                lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID > 0 && x.DepartmentID > 0 && x.SubfunctionID == SubDepartmentID).ToList();
                            }
                            else
                            {
                                lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgID > OrgID && x.UnitID == UnitID && x.DepartmentID == DepartmentID && x.SubfunctionID == SubDepartmentID).ToList();
                            }
                            //lstBusinesProcess = lstBusinesProcess.Where(x => x.OrgID == OrgID && x.UnitID == UnitID && x.DepartmentID == DepartmentID && x.SubfunctionID == SubDepartmentID && x.IsBCMEntity == IsUnderBCM).ToList();

                        }

                        if (IsUnderBCM == 1)
                        {
                            lstBusinessProcess = lstBusinessProcess.Where(x => x.ProcessID > 0).ToList();
                        }
                        else
                        {
                            lstBusinessProcess = lstBusinessProcess.Where(x => x.ProcessID == 0).ToList();
                        }

                    }
                }
                else
                {
                    lstBusinessProcess = FilterListForOwner(lstBusinessProcess, OrgID, UnitID, DepartmentID, SubDepartmentID);
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcess;
        }

        public List<BusinessProcessInfo> FilterListByOrgGroupID(List<BusinessProcessInfo> lstBusinessProcess, int OrgGroupID = 0)
        {
            try
            {
                if (OrgGroupID > 0)
                {
                    lstBusinessProcess = lstBusinessProcess.Where(x => x.OrgGroupID == OrgGroupID || x.OrgGroupID == 0).ToList();
                }
                else
                {
                    lstBusinessProcess = new List<BusinessProcessInfo>();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcess;
        }

        public List<BusinessProcessInfo> FilterListByOrgID(List<BusinessProcessInfo> lstBusinessProcess, int OrgGroupID = 0, int OrgID = 0, int UserRoleID = 0)
        {
            List<OrgInfo> orgcoll = new List<OrgInfo>();
            try
            {
                orgcoll = _ProcessSrv.GetOrgByRole(OrgID.ToString(), UserRoleID.ToString(), OrgGroupID.ToString(), _UserDetails.UserID.ToString());
                if (lstBusinessProcess.Count > 0 && orgcoll.Count > 0)
                {
                    var OrgId = orgcoll.Select(i => i.Id).ToHashSet();
                    lstBusinessProcess = lstBusinessProcess.Where(x => OrgId.Contains(x.OrgID.ToString())).ToList();
                }
                else
                {
                    lstBusinessProcess = new List<BusinessProcessInfo>();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcess;
        }

        public List<BusinessProcessInfo> FilterListByRoleID(List<BusinessProcessInfo> lstBusinessProcess, int OrgGroupID = 0, int OrgID = 0, int UserRoleID = 0)
        {
            try
            {
                if (lstBusinessProcess != null || lstBusinessProcess.Count > 0)
                {
                    lstBusinessProcess = FilterListByUnitID(lstBusinessProcess, OrgGroupID, OrgID, UserRoleID);
                    lstBusinessProcess = FilterListByDeptID(lstBusinessProcess, OrgGroupID, OrgID, UserRoleID);
                    lstBusinessProcess = FilterListBySubDeptID(lstBusinessProcess, OrgGroupID, OrgID, UserRoleID);
                }
                else
                {
                    lstBusinessProcess = new List<BusinessProcessInfo>();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcess;
        }

        public List<BusinessProcessInfo> FilterListByUnitID(List<BusinessProcessInfo> lstBusinessProcess, int OrgGroupID = 0, int OrgID = 0, int UserRoleID = 0)
        {
            try
            {
                List<OrgUnit> lstUnit = _ProcessSrv.GetUnitsByRole(OrgID.ToString(), UserRoleID.ToString(), OrgGroupID.ToString(), _UserDetails.UserID.ToString());
                if (lstBusinessProcess != null)
                {
                    var UnitIDs = lstUnit.Select(i => i.UnitID).ToHashSet();
                    lstBusinessProcess = lstBusinessProcess.Where(x => UnitIDs.Contains(x.UnitID) || x.AltProcessOwnerID == _UserDetails.UserID || x.ProcessOwnerID == _UserDetails.UserID ||x.ApproverID == _UserDetails.UserID).ToList();
                }
                else
                {
                    lstBusinessProcess = new List<BusinessProcessInfo>();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcess;
        }

        public List<BusinessProcessInfo> FilterListByDeptID(List<BusinessProcessInfo> lstBusinessProcess, int OrgGroupID = 0, int OrgID = 0, int UserRoleID = 0)
        {
            try
            {
                if (lstBusinessProcess != null || lstBusinessProcess.Count > 0)
                {
                    List<DepartmentInfo> lstDepartment = _ProcessSrv.GetDepartmentsByRole(OrgID.ToString(), UserRoleID.ToString(), OrgGroupID.ToString(), _UserDetails.UserID.ToString());
                    var lstDepartmentID = lstDepartment.Select(i => i.DepartmentID).ToHashSet();
                    lstBusinessProcess = lstBusinessProcess.Where(x => lstDepartmentID.Contains(x.DepartmentID) || x.AltProcessOwnerID == _UserDetails.UserID || x.ProcessOwnerID == _UserDetails.UserID ||x.ApproverID == _UserDetails.UserID).ToList();
                }
                else
                {
                    lstBusinessProcess = new List<BusinessProcessInfo>();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcess;
        }

        public List<BusinessProcessInfo> FilterListBySubDeptID(List<BusinessProcessInfo> lstBusinessProcess, int OrgGroupID = 0, int OrgID = 0, int UserRoleID = 0)
        {
            try
            {
                List<SubFunction> lstUnit = _ProcessSrv.GetSubDepartmentsByRole(OrgID.ToString(), UserRoleID.ToString(), OrgGroupID.ToString(), _UserDetails.UserID.ToString());
                if (lstBusinessProcess != null)
                {
                    var UnitIDs = lstUnit.Select(i => i.SubFunctionID).ToHashSet();
                    lstBusinessProcess = lstBusinessProcess.Where(x => UnitIDs.Contains(x.SubfunctionID.ToString())|| x.AltProcessOwnerID == _UserDetails.UserID || x.ProcessOwnerID == _UserDetails.UserID ||x.ApproverID == _UserDetails.UserID).ToList();
                }
                else
                {
                    lstBusinessProcess = new List<BusinessProcessInfo>();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcess;
        }
        public List<RTOEnums> PopulateRTO()
        {
            List<RTOEnums> lstRTOEnums = new List<RTOEnums>();
            try
            {
                string[] enumNames = Enum.GetNames(typeof(BCPEnum.RTO));
                foreach (string item in enumNames)
                {
                    int value = (int)Enum.Parse(typeof(BCPEnum.RTO), item);

                    lstRTOEnums.Add(new RTOEnums { RTOID = value, RTOName = item });
                }

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstRTOEnums;
        }


        public List<FrequencyEnums> PopulateFrequency()
        {
            List<FrequencyEnums> lsFrequencyEnums = new List<FrequencyEnums>();
            try
            {
                string[] enumNames = Enum.GetNames(typeof(BCPEnum.Frequency));
                foreach (string item in enumNames)
                {
                    int value = (int)Enum.Parse(typeof(BCPEnum.Frequency), item);

                    lsFrequencyEnums.Add(new FrequencyEnums { ID = value, FrequencyName = item });
                }

            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lsFrequencyEnums;
        }


        public String PopulateFrequency_ByID(int ID)
        {
            string lsFrequencyEnums = string.Empty;
            try
            {
                string[] enumNames = Enum.GetNames(typeof(BCPEnum.Frequency));
                foreach (string item in enumNames)
                {
                    int value = (int)Enum.Parse(typeof(BCPEnum.Frequency), item);
                    if (ID == value)
                    {
                        lsFrequencyEnums = Enum.Parse(typeof(BCPEnum.Frequency), item).ToString();
                    }
                }

            }
            catch (Exception ex)
            {
                //_CVLogger.LogErrorUtilities(ex);
            }
            return lsFrequencyEnums;
        }
        public List<ImpactSeverity> PopulateBIAImpactSeverityType()
        {
            List<ImpactSeverity> lstImpactSeverity = new List<ImpactSeverity>();
            try
            {
                lstImpactSeverity = _ProcessSrv.GetImpactSeverityList();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstImpactSeverity;
        }

        public List<CompanyMasterInfo> PopulateCompany(int iOrgID)
        {

            List<CompanyMasterInfo> lstGetAllCompany = new List<CompanyMasterInfo>();
            try
            {
                lstGetAllCompany = _ProcessSrv.GetCompanyList(iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstGetAllCompany;
        }

        public List<ImpactRatings> PopulateImpactRating()
        {
            List<ImpactRatings> lstImpactRatings = new List<ImpactRatings>();
            try
            {
                string[] strImpactRating = Enum.GetNames(typeof(BCPEnum.ImpactRating));
                foreach (var objImpactRatings in strImpactRating)
                {
                    int value = (int)Enum.Parse(typeof(BCPEnum.ImpactRating), objImpactRatings);
                    lstImpactRatings.Add(new ImpactRatings { ImpactRatingID = value, ImpactRatingName = objImpactRatings });
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstImpactRatings;
        }

        public string GetFormattedRTO_New(int iDurationInMinute)
        {
            string strFormatedRTO = string.Empty;
            try
            {
                if (iDurationInMinute > 0)
                {
                    TimeSpan timeSpan = TimeSpan.FromMinutes(iDurationInMinute);
                    string strDays = timeSpan.Days >= 1 ? timeSpan.Days + "Day(s)" : string.Empty;
                    string strHours = timeSpan.Hours >= 1 ? timeSpan.Hours + "Hour(s)" : string.Empty;
                    string strMins = timeSpan.Minutes >= 1 ? timeSpan.Minutes + "Min(s)" : string.Empty;
                    strFormatedRTO = strDays + strHours + strMins;
                }
                else
                    strFormatedRTO = "NA";
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return strFormatedRTO;
        }

        public bool IsProcessCritical(int iRTO, int OrgID)
        {
            int iConfiguredRTO = 0;
            try
            {
                TimeSpan timeSpan = TimeSpan.FromMinutes(iRTO);
                RTOMTRConfigurations objRTOMTR = _ProcessSrv.GetRtoMtrByOrgID(OrgID.ToString());
                if (objRTOMTR != null)
                {
                    iConfiguredRTO = Convert.ToInt32(objRTOMTR.ConfiguredRTO);
                }
                return timeSpan.TotalHours <= iConfiguredRTO && timeSpan.TotalHours > 0;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return false;
            }
        }

        public string GetIsCriticalStatus(string strIsCritical)
        {
            string strStatus = string.Empty;
            try
            {
                if (strIsCritical.Equals(((int)BCPEnum.IsCritical.Yes).ToString()) || strIsCritical.Equals("Yes"))
                {
                    strStatus = BCPEnum.IsCritical.Yes.ToString();
                }
                if (strIsCritical.Equals(((int)BCPEnum.IsCritical.No).ToString()) || strIsCritical.Equals("No"))
                {
                    strStatus = BCPEnum.IsCritical.No.ToString();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return strStatus;
        }

        public static string GetIsCriticalStatusNew(string strIsCritical)
        {
            string strStatus = string.Empty;
            try
            {
                if (strIsCritical.Equals(((int)BCPEnum.IsCritical.Yes).ToString()) || strIsCritical.Equals("Yes"))
                {
                    strStatus = BCPEnum.IsCritical.Yes.ToString();
                }
                if (strIsCritical.Equals(((int)BCPEnum.IsCritical.No).ToString()) || strIsCritical.Equals("No"))
                {
                    strStatus = BCPEnum.IsCritical.No.ToString();
                }
            }
            catch (Exception)
            {
                strStatus = "NA";
            }
            return strStatus;
        }

        public static string ApprovalStatus(int iStatusID)
        {
            string strStatus = string.Empty;
            try
            {
                switch ((BCPEnum.ApprovalType)iStatusID)
                {
                    case BCPEnum.ApprovalType.Initiated:
                        strStatus = "Initiated";
                        break;
                    case BCPEnum.ApprovalType.WaitingforApproval:
                        strStatus = "Waiting for Approval";
                        break;
                    case BCPEnum.ApprovalType.Approved:
                        strStatus = "Approved";
                        break;
                    case BCPEnum.ApprovalType.Disapproved:
                        strStatus = "Rejected";
                        break;
                    case BCPEnum.ApprovalType.error:
                        strStatus = "Error";
                        break;
                    case BCPEnum.ApprovalType.InActive:
                        strStatus = "InActive";
                        break;
                    default:
                        strStatus = "Unknown";
                        break;
                }
            }
            catch (Exception)
            {

                //_CVLogger.LogErrorUtilities(ex);
                strStatus = "NA";
            }
            return strStatus;
        }

        public static string ApprovalStatusWiseClass(int iStatusID)
        {
            string strStatus = string.Empty;
            try
            {
                switch ((BCPEnum.ApprovalType)iStatusID)
                {
                    case BCPEnum.ApprovalType.Initiated:
                        strStatus = "cv-initiated";
                        break;
                    case BCPEnum.ApprovalType.WaitingforApproval:
                        strStatus = "cv-waiting";
                        break;
                    case BCPEnum.ApprovalType.Approved:
                        strStatus = "cv-success";
                        break;
                    case BCPEnum.ApprovalType.Disapproved:
                        strStatus = "cv-error";
                        break;
                    case BCPEnum.ApprovalType.error:
                        strStatus = "";
                        break;
                    case BCPEnum.ApprovalType.InActive:
                        strStatus = "";
                        break;
                    default:
                        strStatus = "Unknown";
                        break;
                }
            }
            catch (Exception)
            {
                //_CVLogger.LogErrorUtilities(ex);
                strStatus = "";
            }
            return strStatus;
        }

        public static string ApprovalStatusWiseTextClass(int iStatusID)
        {
            string strStatus = string.Empty;
            try
            {
                switch ((BCPEnum.ApprovalType)iStatusID)
                {
                    case BCPEnum.ApprovalType.Initiated:
                        strStatus = "text-info";
                        break;
                    case BCPEnum.ApprovalType.WaitingforApproval:
                        strStatus = "text-warning";
                        break;
                    case BCPEnum.ApprovalType.Approved:
                        strStatus = "text-success";
                        break;
                    case BCPEnum.ApprovalType.Disapproved:
                        strStatus = "text-danger";
                        break;
                    case BCPEnum.ApprovalType.error:
                        strStatus = "";
                        break;
                    case BCPEnum.ApprovalType.InActive:
                        strStatus = "";
                        break;
                    default:
                        strStatus = "Unknown";
                        break;
                }
            }
            catch (Exception)
            {
                //_CVLogger.LogErrorUtilities(ex);
                strStatus = "";
            }
            return strStatus;
        }

        public bool AddPCIScore(string strProcessID, string strPCIScoreID, string strIsActive, string strCreatedBy, string strChangedBy)
        {
            return _ProcessSrv.BusinessProcessPCISaveAndUpdate(new BusinessProcessPCI
            {
                ProcessID = strProcessID,
                IsActive = strIsActive,
                PCIScoreID = strPCIScoreID,
                CreatedBy = strCreatedBy,
                ChangedBy = strChangedBy
            });
        }

        public bool AddPCIScoreSummary(string strProcessID, string strRecordID, string strPCIScoreID, string strAssignedWeightage, string strActualWeightage)
        {
            return _ProcessSrv.PCIScoreSummarySave(new BusinessProcessPCI
            {
                ProcessID = strProcessID,
                RecordID = strRecordID,
                PCIScoreID = strPCIScoreID,
                AssignedWeightage = strAssignedWeightage,
                ActualWeightage = strActualWeightage,
                CreatedBy = "0"
            });
        }

        public string GetPCIScoreWeightage(int iID)
        {
            BusinessProcessPCI objBusinessProcessPCI = _ProcessSrv.BusinessProcessPCIScoreGetByID(iID);
            return objBusinessProcessPCI.Weightage = (Convert.ToDouble(objBusinessProcessPCI.Weightage) * 100).ToString();
        }

        public void AddLog(int iUserId, string strModule, string strEntity, string strEntitytype, string strActionType, int iProcessId,
            string strVersion, int iChangedBy, int iApplicationId)
        {
            //string pageUrl = string.Empty;
            //if (HttpContext.Current != null)
            //{
            //    pageUrl = HttpContext.Current.Request.Url.ToString();
            //}
            var auditLog = new AuditLogsInfo
            {
                UserID = iUserId,
                Module = strModule,
                EntityName = strEntity,
                EntityType = strEntitytype,
                ProcessID = iProcessId,
                Version = strVersion,
                Action = strActionType.ToString(),
                ChangedBy = iChangedBy,
                ApplicationId = iApplicationId
            };
            // AddAuditLogsInDatabase(auditLog);
        }

        public string GetCompliantEntitiesCount(int OrgID, int EntityTypeID, int IsCritical, int TotalOrCompliant, double OrgPCIThreshold = 90.00)
        {
            int Count = 0;
            List<BusinessProcessInfo> objBusinessProcessColl = _ProcessSrv.GetBusinessProcessListByEntityType(EntityTypeID, OrgID);

            try
            {
                if (objBusinessProcessColl != null)
                {

                    if (objBusinessProcessColl.Count > 0)
                    {

                        DataTable dtAllProcess = new DataTable();

                        dtAllProcess.Columns.Add("ProcessID");
                        dtAllProcess.Columns.Add("ProcessCode");
                        dtAllProcess.Columns.Add("ProcessName");
                        dtAllProcess.Columns.Add("OrgID");
                        dtAllProcess.Columns.Add("UnitID");
                        dtAllProcess.Columns.Add("DepartmentID");
                        dtAllProcess.Columns.Add("SubfunctionID");
                        dtAllProcess.Columns.Add("RTO");
                        dtAllProcess.Columns.Add("OwnerRTO");
                        dtAllProcess.Columns.Add("OwnerID");
                        dtAllProcess.Columns.Add("AltOwnerID");
                        dtAllProcess.Columns.Add("Status");
                        dtAllProcess.Columns.Add("Priority");
                        dtAllProcess.Columns.Add("ProcessType");
                        dtAllProcess.Columns.Add("ProcessDescription");
                        dtAllProcess.Columns.Add("MinimumResource");
                        dtAllProcess.Columns.Add("Comments");
                        dtAllProcess.Columns.Add("IsActive");
                        dtAllProcess.Columns.Add("CreateDate");
                        dtAllProcess.Columns.Add("UpdateDate");
                        dtAllProcess.Columns.Add("IsCritical");
                        dtAllProcess.Columns.Add("ProcessReviewDate");
                        dtAllProcess.Columns.Add("LastReviewDate");
                        dtAllProcess.Columns.Add("RecordID");
                        dtAllProcess.Columns.Add("EntityTypeID");
                        dtAllProcess.Columns.Add("RTOText");

                        dtAllProcess.Columns.Add("ApproverID");
                        dtAllProcess.Columns.Add("UnitHeadID");
                        dtAllProcess.Columns.Add("AltUnitHeadId");
                        dtAllProcess.Columns.Add("UnitBCPCorID");
                        dtAllProcess.Columns.Add("AltBCPCorID");
                        dtAllProcess.Columns.Add("CreatedBy");
                        dtAllProcess.Columns.Add("IsEffective");

                        dtAllProcess.Columns.Add("OrgHeadId");

                        dtAllProcess.Columns.Add("DepartmentHeadID");
                        dtAllProcess.Columns.Add("AltDepartmentHeadID");
                        dtAllProcess.Columns.Add("SubFunOwnerId");
                        dtAllProcess.Columns.Add("AltSubFunOwnerId");


                        foreach (BusinessProcessInfo objBusinessProcess in objBusinessProcessColl)
                        {
                            dtAllProcess.Rows.Add(objBusinessProcess.ProcessID, objBusinessProcess.ProcessCode, objBusinessProcess.ProcessName, objBusinessProcess.OrgID,
                                objBusinessProcess.UnitID, objBusinessProcess.DepartmentID, objBusinessProcess.SubfunctionID,
                                objBusinessProcess.RTO, objBusinessProcess.OwnerRTO, objBusinessProcess.ProcessOwner, objBusinessProcess.AltProcessOwner,
                                objBusinessProcess.Status, objBusinessProcess.Priority, objBusinessProcess.ProcessType, objBusinessProcess.ProcessDescription,
                                objBusinessProcess.MinimumResource, objBusinessProcess.Comments, objBusinessProcess.IsActive, objBusinessProcess.CreateDate,
                                objBusinessProcess.UpdateDate, objBusinessProcess.IsCritical, objBusinessProcess.ProcessReviewDate, objBusinessProcess.LastReviewDate,
                                objBusinessProcess.RecordID, objBusinessProcess.EntityTypeID, objBusinessProcess.RTOText, objBusinessProcess.ApproverID,
                                objBusinessProcess.UnitHeadID, objBusinessProcess.AltUnitHeadId, objBusinessProcess.UnitBCPCorID,
                                objBusinessProcess.AltBCPCorID, objBusinessProcess.CreatedBy, objBusinessProcess.IsEffective, objBusinessProcess.OrgHeadId,
                                objBusinessProcess.DepartmentHeadID, objBusinessProcess.AltDepartmentHeadID, objBusinessProcess.SubFunOwnerId,
                                objBusinessProcess.AltSubFunOwnerId);
                        }

                        //  dtAllProcess = GetBusinessProcess(IsUnderBCM);

                        dtAllProcess = GetBusinessEntity(dtAllProcess, EntityTypeID);

                        dtAllProcess = GetBusinessProcessByIsUnderBCM(dtAllProcess);


                        if (!IsProductAdmin(_UserDetails.UserRole))
                        {
                            if (IsSuperAdmin(_UserDetails.UserRoleID.ToString()))
                            {
                                dtAllProcess = FilterDataTableByOrgGroupID(dtAllProcess, _UserDetails.OrgGroupID.ToString());
                            }
                            else
                            {
                                dtAllProcess = FilterDataTableByOrgID(dtAllProcess, _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.OrgGroupID.ToString());
                                dtAllProcess = FilterGridDataByRoles(dtAllProcess, _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.OrgGroupID.ToString());
                            }//, "UnitID", "DepartmentID", "SubfunctionID"
                        }

                        DataTable dtAllProcess1 = new DataTable();

                        if (dtAllProcess != null)
                        {
                            dtAllProcess1 = (from DataRow dr in dtAllProcess.Rows
                                             where dr["IsEffective"].ToString() == "1"
                                             select dr).CopyToDataTable();
                        }


                        objBusinessProcessColl = new List<BusinessProcessInfo>();

                        if (dtAllProcess1 != null)
                        {
                            foreach (DataRow dr in dtAllProcess1.Rows)
                            {
                                var objBusiness = new BusinessProcessInfo();
                                objBusiness.ProcessID = Convert.ToInt32(dr["ProcessID"]);
                                objBusiness.ProcessCode = (dr["ProcessCode"]).ToString();
                                objBusiness.ProcessName = dr["ProcessName"].ToString();
                                objBusiness.OrgID = Convert.IsDBNull(dr["OrgID"]) ? 0 : Convert.ToInt32(dr["OrgID"]);
                                objBusiness.UnitID = Convert.IsDBNull(dr["UnitID"]) ? 0 : Convert.ToInt32(dr["UnitID"]);
                                objBusiness.DepartmentID = Convert.IsDBNull(dr["DepartmentID"]) ? 0 : Convert.ToInt32(dr["DepartmentID"]);
                                objBusiness.SubfunctionID = Convert.IsDBNull(dr["SubFunctionID"]) ? 0 : Convert.ToInt32(dr["SubFunctionID"]);
                                objBusiness.RTO = dr["RTO"].ToString();
                                objBusiness.OwnerRTO = dr["OwnerRTO"].ToString();
                                objBusiness.ProcessOwner = dr["OwnerID"].ToString();
                                objBusiness.AltProcessOwner = Convert.IsDBNull(dr["AltOwnerID"]) ? "0" : Convert.ToInt32(dr["AltOwnerID"]).ToString();
                                objBusiness.Status = Convert.IsDBNull(dr["Status"]) ? 0 : Convert.ToInt32(dr["Status"]);
                                //objBusiness.Priority = dr["Priority"].ToString();
                                //objBusiness.ProcessType = Convert.IsDBNull(dr["TypeId"]) ? "0" : Convert.ToInt32(dr["TypeId"]).ToString();
                                //objBusiness.ProcessDescription = dr["ProcessDescription"].ToString();
                                //objBusiness.MinimumResource = dr["MinimumResource"].ToString();
                                //objBusiness.Comments = dr["Comments"].ToString();
                                objBusiness.IsActive = Convert.IsDBNull(dr["IsActive"]) ? 0 : Convert.ToInt32(dr["IsActive"]);
                                objBusiness.CreateDate = Convert.ToDateTime(dr["CreateDate"].ToString());
                                //objBusiness.UpdateDate = dr["UpdateDate"].ToString();
                                objBusiness.IsCritical = Convert.IsDBNull(dr["IsCritical"]) ? 0 : Convert.ToInt32(dr["IsCritical"]);
                                objBusiness.ProcessReviewDate = Convert.ToDateTime(dr["ProcessReviewDate"].ToString());
                                objBusiness.LastReviewDate = Convert.ToDateTime(dr["LastReviewDate"].ToString());
                                objBusiness.RecordID = Convert.IsDBNull(dr["RecordID"]) ? 0 : Convert.ToInt32(dr["RecordID"]);
                                objBusiness.EntityTypeID = Convert.IsDBNull(dr["EntityTypeID"]) ? 0 : Convert.ToInt32(dr["EntityTypeID"]);
                                objBusiness.RTOText = dr["RTOText"].ToString();


                                objBusinessProcessColl.Add(objBusiness);
                            }
                        }
                        else
                        {
                            objBusinessProcessColl = new List<BusinessProcessInfo>();
                        }
                    }
                    else
                    {
                        objBusinessProcessColl = new List<BusinessProcessInfo>();
                    }
                }
                else
                {
                    objBusinessProcessColl = new List<BusinessProcessInfo>();
                }

                //var objBusinessProcessCollLevel = (BusinessProcessColl)null;
                if (objBusinessProcessColl == null) return "0";

                double PCIScore = 0.0;

                if (TotalOrCompliant == 0)
                {
                    if (IsCritical == 1)
                    {
                        foreach (BusinessProcessInfo objBusinessProcess in objBusinessProcessColl)
                        {
                            if (objBusinessProcess.RTOText == "1")
                            {
                                PCIScore = _ProcessSrv.GetPCIScoreByProcessID(Convert.ToInt32(objBusinessProcess.ProcessID));
                                if (PCIScore > OrgPCIThreshold)
                                    Count++;
                            }
                        }
                    }
                    else
                    {
                        foreach (BusinessProcessInfo objBusinessProcess in objBusinessProcessColl)
                        {

                            PCIScore = _ProcessSrv.GetPCIScoreByProcessID(Convert.ToInt32(objBusinessProcess.ProcessID));
                            if (PCIScore > OrgPCIThreshold)
                                Count++;
                        }
                    }
                }
                else
                {
                    if (IsCritical == 1)
                    {
                        Count = (from BusinessProcessInfo objBusinessProcess in objBusinessProcessColl
                                 where objBusinessProcess.RTOText == "1"
                                 select objBusinessProcess).Count();
                    }
                    else
                        Count = objBusinessProcessColl.Count;
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return "0";
            }
            
            return Count.ToString();

        }


        public string GetAveragePCIScore(int OrgID, int Level, int LevelID, int EntityTypeID, int IsCritical)
        {
            double AveragePCIScore = 0.0;
            try
            {
                ManageUsersDetails _oUser = LoginUserDetails();


                List<BusinessProcessInfo> objBusinessProcessColl = _ProcessSrv.GetBusinessProcessListByEntityType(EntityTypeID, OrgID);

                if (objBusinessProcessColl != null)
                {
                    if (objBusinessProcessColl.Count > 0)
                    {

                        DataTable dtAllProcess = new DataTable();

                        dtAllProcess.Columns.Add("ProcessID");
                        dtAllProcess.Columns.Add("ProcessCode");
                        dtAllProcess.Columns.Add("ProcessName");
                        dtAllProcess.Columns.Add("OrgID");
                        dtAllProcess.Columns.Add("UnitID");
                        dtAllProcess.Columns.Add("DepartmentID");
                        dtAllProcess.Columns.Add("SubfunctionID");
                        dtAllProcess.Columns.Add("RTO");
                        dtAllProcess.Columns.Add("OwnerRTO");
                        dtAllProcess.Columns.Add("OwnerID");
                        dtAllProcess.Columns.Add("AltOwnerID");
                        dtAllProcess.Columns.Add("Status");
                        dtAllProcess.Columns.Add("Priority");
                        dtAllProcess.Columns.Add("ProcessType");
                        dtAllProcess.Columns.Add("ProcessDescription");
                        dtAllProcess.Columns.Add("MinimumResource");
                        dtAllProcess.Columns.Add("Comments");
                        dtAllProcess.Columns.Add("IsActive");
                        dtAllProcess.Columns.Add("CreateDate");
                        dtAllProcess.Columns.Add("UpdateDate");
                        dtAllProcess.Columns.Add("IsCritical");
                        dtAllProcess.Columns.Add("ProcessReviewDate");
                        dtAllProcess.Columns.Add("LastReviewDate");
                        dtAllProcess.Columns.Add("RecordID");
                        dtAllProcess.Columns.Add("EntityTypeID");
                        dtAllProcess.Columns.Add("RTOText");

                        dtAllProcess.Columns.Add("ApproverID");
                        dtAllProcess.Columns.Add("UnitHeadID");
                        dtAllProcess.Columns.Add("AltUnitHeadId");
                        dtAllProcess.Columns.Add("UnitBCPCorID");
                        dtAllProcess.Columns.Add("AltBCPCorID");
                        dtAllProcess.Columns.Add("CreatedBy");
                        dtAllProcess.Columns.Add("IsEffective");

                        dtAllProcess.Columns.Add("OrgHeadId");

                        dtAllProcess.Columns.Add("DepartmentHeadID");
                        dtAllProcess.Columns.Add("AltDepartmentHeadID");
                        dtAllProcess.Columns.Add("SubFunOwnerId");
                        dtAllProcess.Columns.Add("AltSubFunOwnerId");


                        foreach (BusinessProcessInfo objBusinessProcess in objBusinessProcessColl)
                        {
                            dtAllProcess.Rows.Add(objBusinessProcess.ProcessID, objBusinessProcess.ProcessCode, objBusinessProcess.ProcessName, objBusinessProcess.OrgID,
                                objBusinessProcess.UnitID, objBusinessProcess.DepartmentID, objBusinessProcess.SubfunctionID,
                                objBusinessProcess.RTO, objBusinessProcess.OwnerRTO, objBusinessProcess.ProcessOwner, objBusinessProcess.AltProcessOwner,
                                objBusinessProcess.Status, objBusinessProcess.Priority, objBusinessProcess.ProcessType, objBusinessProcess.ProcessDescription,
                                objBusinessProcess.MinimumResource, objBusinessProcess.Comments, objBusinessProcess.IsActive, objBusinessProcess.CreateDate,
                                objBusinessProcess.UpdateDate, objBusinessProcess.IsCritical, objBusinessProcess.ProcessReviewDate, objBusinessProcess.LastReviewDate,
                                objBusinessProcess.RecordID, objBusinessProcess.EntityTypeID, objBusinessProcess.RTOText, objBusinessProcess.ApproverID,
                                objBusinessProcess.UnitHeadID, objBusinessProcess.AltUnitHeadId, objBusinessProcess.UnitBCPCorID,
                                objBusinessProcess.AltBCPCorID, objBusinessProcess.CreatedBy, objBusinessProcess.IsEffective, objBusinessProcess.OrgHeadId,
                                objBusinessProcess.DepartmentHeadID, objBusinessProcess.AltDepartmentHeadID, objBusinessProcess.SubFunOwnerId,
                                objBusinessProcess.AltSubFunOwnerId);
                        }

                        //  dtAllProcess = GetBusinessProcess(IsUnderBCM);

                        dtAllProcess = GetBusinessEntity(dtAllProcess, EntityTypeID);

                        dtAllProcess = GetBusinessProcessByIsUnderBCM(dtAllProcess);

                        if (!IsProductAdmin(_UserDetails.UserRole))
                        {
                            if (IsSuperAdmin(_UserDetails.UserRole))
                            {
                                dtAllProcess = FilterDataTableByOrgGroupID(dtAllProcess, _UserDetails.OrgGroupID.ToString());
                            }
                            else
                            {
                                dtAllProcess = FilterDataTableByOrgID(dtAllProcess, _UserDetails.OrgID.ToString(), _oUser.UserRoleID.ToString(), _oUser.OrgGroupID.ToString());
                                dtAllProcess = FilterGridDataByRoles(dtAllProcess, _oUser.OrgID.ToString(), _oUser.UserRoleID.ToString(), _oUser.OrgGroupID.ToString());
                            }//, "UnitID", "DepartmentID", "SubfunctionID"
                        }

                        DataTable dtAllProcess1 = new DataTable();

                        if (dtAllProcess != null)
                        {
                            dtAllProcess1 = (from DataRow dr in dtAllProcess.Rows
                                             where dr["IsEffective"].ToString() == "1"
                                             select dr).CopyToDataTable();
                        }


                        objBusinessProcessColl = new List<BusinessProcessInfo>();

                        if (dtAllProcess1 != null)
                        {
                            foreach (DataRow dr in dtAllProcess1.Rows)
                            {
                                var objBusiness = new BusinessProcessInfo();
                                objBusiness.ProcessID = Convert.ToInt32(dr["ProcessID"]);
                                objBusiness.ProcessCode = (dr["ProcessCode"]).ToString();
                                objBusiness.ProcessName = dr["ProcessName"].ToString();
                                objBusiness.OrgID = Convert.IsDBNull(dr["OrgID"]) ? 0 : Convert.ToInt32(dr["OrgID"]);
                                objBusiness.UnitID = Convert.IsDBNull(dr["UnitID"]) ? 0 : Convert.ToInt32(dr["UnitID"]);
                                objBusiness.DepartmentID = Convert.IsDBNull(dr["DepartmentID"]) ? 0 : Convert.ToInt32(dr["DepartmentID"]);
                                objBusiness.SubfunctionID = Convert.ToInt32(dr["SubFunctionID"].ToString());
                                objBusiness.RTO = dr["RTO"].ToString();
                                objBusiness.OwnerRTO = dr["OwnerRTO"].ToString();
                                objBusiness.ProcessOwner = dr["OwnerID"].ToString();
                                objBusiness.AltProcessOwner = Convert.IsDBNull(dr["AltOwnerID"]) ? "0" : Convert.ToInt32(dr["AltOwnerID"]).ToString();
                                objBusiness.Status = Convert.IsDBNull(dr["Status"]) ? 0 : Convert.ToInt32(dr["Status"]);
                                //objBusiness.Priority = dr["Priority"].ToString();
                                //objBusiness.ProcessType = Convert.IsDBNull(dr["TypeId"]) ? "0" : Convert.ToInt32(dr["TypeId"]).ToString();
                                //objBusiness.ProcessDescription = dr["ProcessDescription"].ToString();
                                //objBusiness.MinimumResource = dr["MinimumResource"].ToString();
                                //objBusiness.Comments = dr["Comments"].ToString();
                                objBusiness.IsActive = Convert.IsDBNull(dr["IsActive"]) ? 0 : Convert.ToInt32(dr["IsActive"]);
                                objBusiness.CreateDate = Convert.ToDateTime(dr["CreateDate"].ToString());
                                //objBusiness.UpdateDate = dr["UpdateDate"].ToString();
                                objBusiness.IsCritical = Convert.ToInt32(dr["IsCritical"].ToString());
                                objBusiness.ProcessReviewDate = Convert.IsDBNull(dr["ProcessReviewDate"]) ? null : Convert.ToDateTime(dr["ProcessReviewDate"]);
                                objBusiness.LastReviewDate = Convert.IsDBNull(dr["LastReviewDate"]) ? null : Convert.ToDateTime(dr["LastReviewDate"]);
                                objBusiness.RecordID = Convert.IsDBNull(dr["RecordID"]) ? 0 : Convert.ToInt32(dr["RecordID"]);
                                objBusiness.EntityTypeID = Convert.IsDBNull(dr["EntityTypeID"]) ? 0 : Convert.ToInt32(dr["EntityTypeID"]);
                                objBusiness.RTOText = dr["RTOText"].ToString();


                                objBusinessProcessColl.Add(objBusiness);
                            }
                        }
                        else
                        {
                            objBusinessProcessColl = new List<BusinessProcessInfo>();
                        }
                    }
                    else
                    {
                        objBusinessProcessColl = new List<BusinessProcessInfo>();
                    }
                }
                else
                {
                    objBusinessProcessColl = new List<BusinessProcessInfo>();
                }


                if (objBusinessProcessColl != null)
                {

                    if (IsCritical == 1)
                    {
                        var objBusinessProcessCollCritical = from BusinessProcessInfo objBusinessProcessInfo in objBusinessProcessColl
                                                             where objBusinessProcessInfo.IsCritical.ToString() == "1"
                                                             select objBusinessProcessInfo;
                        List<BusinessProcessInfo> objBusinessProcessCollCrit = new List<BusinessProcessInfo>();
                        foreach (BusinessProcessInfo objBusinessProcessInfo in objBusinessProcessCollCritical)
                        {
                            objBusinessProcessCollCrit.Add(objBusinessProcessInfo);
                        }
                        objBusinessProcessColl = objBusinessProcessCollCrit;
                    }

                }

                if (objBusinessProcessColl == null) return "0";
                int Count = 0;
                double TotalPCIScore = 0.0;
                
                if (Level == 1)
                {
                    foreach (BusinessProcessInfo objBusinessProcess in objBusinessProcessColl)
                    {
                        Count++;
                        TotalPCIScore = TotalPCIScore + _ProcessSrv.GetPCIScoreByProcessID(Convert.ToInt32(objBusinessProcess.ProcessID));
                    }
                }
                if (Level == 2)
                {
                    var objBusinessProcessCollUnitLevel = from BusinessProcessInfo objBusinessProcessInfo in objBusinessProcessColl
                                                          where objBusinessProcessInfo.UnitID.ToString() == LevelID.ToString()
                                                          select objBusinessProcessInfo;

                    foreach (BusinessProcessInfo objBusinessProcess in objBusinessProcessCollUnitLevel)
                    {
                        Count++;
                        TotalPCIScore = TotalPCIScore + _ProcessSrv.GetPCIScoreByProcessID(Convert.ToInt32(objBusinessProcess.ProcessID));
                    }
                }
                else if (Level == 3)
                {
                    var objBusinessProcessCollDeptLevel = from BusinessProcessInfo objBusinessProcessInfo in objBusinessProcessColl
                                                          where objBusinessProcessInfo.DepartmentID.ToString() == LevelID.ToString()
                                                          select objBusinessProcessInfo;

                    foreach (BusinessProcessInfo objBusinessProcess in objBusinessProcessCollDeptLevel)
                    {
                        Count++;
                        TotalPCIScore = TotalPCIScore + _ProcessSrv.GetPCIScoreByProcessID(Convert.ToInt32(objBusinessProcess.ProcessID));
                    }
                }
                if (Level == 4)
                {
                    var objBusinessProcessCollSubDeptLevel = from BusinessProcessInfo objBusinessProcessInfo in objBusinessProcessColl
                                                             where objBusinessProcessInfo.SubfunctionID.ToString() == LevelID.ToString()
                                                             select objBusinessProcessInfo;

                    foreach (BusinessProcessInfo objBusinessProcess in objBusinessProcessCollSubDeptLevel)
                    {
                        Count++;
                        TotalPCIScore = TotalPCIScore + _ProcessSrv.GetPCIScoreByProcessID(Convert.ToInt32(objBusinessProcess.ProcessID));
                    }
                }


                if (Count > 0 && TotalPCIScore > 0)
                    AveragePCIScore = Math.Round((TotalPCIScore / Count), 2);

            }
            catch (Exception)
            {

                throw;
            }
           
            
            return AveragePCIScore.ToString();


        }

        public List<BusinessProcessInfo> FilterListByOrgID(List<BusinessProcessInfo> BusinessProcessList, string OrgID, string RoleID, string OrgGroupID)
        {
            try
            {
                List<OrgInfo> orgcoll = new List<OrgInfo>();
                string strUserID = _UserDetails.UserID.ToString();
                if (BusinessProcessList.Count > 0)
                {
                    orgcoll = _ProcessSrv.GetOrgByRole(OrgID, RoleID, OrgGroupID, strUserID);
                    BusinessProcessList = (from BusinessProcess in BusinessProcessList
                                           where orgcoll.Any(order => BusinessProcess.OrgID.ToString() == order.Id)
                                           select BusinessProcess).ToList();
                }
            }
            catch (Exception)
            {


            }

            return BusinessProcessList;
        }
        public DataTable FilterDataTableByOrgID(DataTable dt, string OrgID, string RoleID, string OrgGroupID)
        {
            string OrgID_Name = "OrgID";


            List<OrgInfo> orgcoll = new List<OrgInfo>();
            DataView dv = new DataView();
            string filterstring = string.Empty;
            string strUserID = _UserDetails.UserID.ToString();
            // dt = FilterDataTableByUnitID(dt, "UnitID");

            if (dt != null)
            {
                if (dt.Rows.Count > 0)
                {
                    orgcoll = _ProcessSrv.GetOrgByRole(OrgID, RoleID, OrgGroupID, strUserID);
                    if (orgcoll != null)
                    {
                        if (orgcoll.Count > 0)
                        {
                            foreach (OrgInfo obj in orgcoll)
                            {

                                filterstring = filterstring + OrgID_Name.ToString() + "='" + obj.Id + "' Or ";
                            }
                            filterstring = filterstring + OrgID_Name.ToString() + "=null Or " + OrgID_Name.ToString() + "='0'";
                            //filterstring = filterstring + OrgID_Name.ToString() + "=null Or " + OrgID_Name.ToString() + "='" + string.Empty + "'";
                            // filterstring = filterstring.Remove(filterstring.Length - 4, 4);




                            /*  dr = dt.Select(filterstring);
                              if (dr.Length > 0)
                              {
                                  dt = dr.CopyToDataTable();
                              }
                              else
                                  dt = null;
                             */
                            dv = dt.DefaultView;
                            dv.RowFilter = filterstring.ToString();
                            dt = dv.ToTable();
                        }
                        else
                            filterstring = filterstring + OrgID_Name.ToString() + "='" + OrgID.ToString() + "'";

                        dv = dt.DefaultView;
                        dv.RowFilter = filterstring.ToString();
                        dt = dv.ToTable();
                        // dt = null;
                    }
                    else
                        dt = null;

                }
                else
                    dt = null;
            }
            else
                dt = null;
            //dr = dt.Select(filterstring);
            //if (dr.Length > 0)
            //{
            //    dt = dr.CopyToDataTable();
            //}
            filterstring = string.Empty;
            return dt;
        }


        public static DataTable FilterDataTableByOrgGroupID(DataTable dt, string OrgGroupID)
        {
            try
            {
                string OrgGroupID_Name = "OrgGroupID";

                string filterstring = string.Empty;
                DataView dv = new DataView();

                if (dt != null)
                {
                    if (dt.Rows.Count > 0)
                    {
                        filterstring = filterstring + OrgGroupID_Name.ToString() + "='" + OrgGroupID + "' Or ";

                        filterstring = filterstring + OrgGroupID_Name.ToString() + "=null Or " + OrgGroupID_Name.ToString() + "='0'";

                        /* dr = dt.Select(filterstring);
                             if (dr.Length > 0)
                                 {
                                     dt = dr.CopyToDataTable();
                                 }
                                 else
                                     dt = null;  */
                        dv = dt.DefaultView;
                        dv.RowFilter = filterstring.ToString();
                        dt = dv.ToTable();
                    }
                    else
                        dt = null;
                }
                else
                    dt = null;

                filterstring = string.Empty;
                return dt;
            }
            catch (Exception)
            {
                return null;
            }
        }
        public DataTable FilterDataTableByUnitID(DataTable dt, string OrgID, string RoleID, string OrgGroupID)
        {
            string UnitID = "UnitID";
            List<OrgUnit> orgUnitcoll = new List<OrgUnit>();
            string filterstring = string.Empty;
            DataView dv = new DataView();
            // dt = FilterDataTableByUnitID(dt, "UnitID");
            string strUserID = _UserDetails.UserID.ToString();
            if (dt != null)
            {
                if (dt.Rows.Count > 0)
                {
                    orgUnitcoll = _ProcessSrv.GetUnitsByRole(OrgID, RoleID, OrgGroupID, strUserID);
                    if (orgUnitcoll != null)
                    {
                        if (orgUnitcoll.Count > 0)
                        {
                            foreach (OrgUnit obj in orgUnitcoll)
                            {


                                filterstring = filterstring + UnitID.ToString() + "='" + obj.UnitID + "' Or ";
                            }

                            //filterstring = filterstring + UnitID.ToString() + "=null Or " + UnitID.ToString() + "='0'Or " + UnitID.ToString() + "='" + string.Empty + "'";
                            //filterstring = filterstring + UnitID.ToString() + "=null Or " + UnitID.ToString() + "='0'";
                            if (orgUnitcoll.Count == 1)
                            {
                                filterstring = filterstring + UnitID.ToString() + "=null ";
                            }
                            else
                            {
                                filterstring = filterstring + UnitID.ToString() + "=null Or " + UnitID.ToString() + "='0'";
                            }

                            // filterstring = filterstring.Remove(filterstring.Length - 4, 4);

                            /* dr = dt.Select(filterstring);
                             if (dr.Length > 0)
                             {
                                 dt = dr.CopyToDataTable();
                             }
                             else
                                 dt = null;*/
                            dv = dt.DefaultView;
                            dv.RowFilter = filterstring.ToString();
                            dt = dv.ToTable();

                        }
                        else
                        {

                            filterstring = " OrgID='" + OrgID + "' and UnitID=0    ";
                            /*  dr = dt.Select(filterstring);
                              if (dr.Length > 0)
                              {
                                  dt = dr.CopyToDataTable();
                              }
                              else
                                  dt = null;*/
                            dv = dt.DefaultView;
                            dv.RowFilter = filterstring.ToString();
                            dt = dv.ToTable();
                        }
                    }
                    else
                    {

                        filterstring = " OrgID='" + OrgID + "' and UnitID=0   ";
                        /* dr = dt.Select(filterstring);
                         if (dr.Length > 0)
                         {
                             dt = dr.CopyToDataTable();
                         }
                         else
                             dt = null;*/
                        dv = dt.DefaultView;
                        dv.RowFilter = filterstring.ToString();
                        dt = dv.ToTable();
                    }
                }
                else
                    dt = null;
            }
            else
                dt = null;
            //dr = dt.Select(filterstring);
            //if (dr.Length > 0)
            //{
            //    dt = dr.CopyToDataTable();
            //}
            filterstring = string.Empty;
            return dt;
        }

        public DataTable FilterDataTableByDeptID(DataTable dt, string OrgID, string RoleID, string OrgGroupID)
        {
            string DeptID = "DepartmentID";


            List<DepartmentInfo> orgDeptcoll = new List<DepartmentInfo>();
            string filterstring = string.Empty;
            DataView dv = new DataView();

            if (dt != null)
            {
                if (dt.Rows.Count > 0)
                {

                    orgDeptcoll = _ProcessSrv.GetDepartmentsByRole(OrgID, RoleID, OrgGroupID, _UserDetails.UserID.ToString());
                    if (orgDeptcoll != null)
                    {
                        if (orgDeptcoll.Count > 0)
                        {
                            foreach (DepartmentInfo obj in orgDeptcoll)
                            {

                                filterstring = filterstring + DeptID.ToString() + "='" + obj.DepartmentID + "' Or ";
                            }
                            //filterstring = filterstring + DeptID.ToString() + "=null Or " + DeptID.ToString() + "='0' Or " + DeptID.ToString() + "='" + string.Empty + "'";
                            //filterstring = filterstring + DeptID.ToString() + "=null Or " + DeptID.ToString() + "='0'";
                            if (orgDeptcoll.Count == 1)
                            {
                                filterstring = filterstring + DeptID.ToString() + "=null ";
                            }
                            else
                            {
                                filterstring = filterstring + DeptID.ToString() + "=null Or " + DeptID.ToString() + "='0'";
                            }
                            // filterstring = filterstring + DeptID.ToString() + "=null ";
                            dv = dt.DefaultView;
                            dv.RowFilter = filterstring.ToString();
                            dt = dv.ToTable();

                        }
                        else
                        {
                            filterstring = "  DepartmentID =0  ";
                            dv = dt.DefaultView;
                            dv.RowFilter = filterstring.ToString();
                            dt = dv.ToTable();

                        }

                    }
                    else
                        dt = null;
                }
                else
                    dt = null;
            }
            else
                dt = null;

            filterstring = string.Empty;
            return dt;
        }
        public DataTable FilterDataTableBySubDeptID(DataTable dt, string OrgID, string RoleID, string OrgGroupID)
        {

            string SubDeptID = "SubfunctionID";

            DataRow[] dr;
            List<SubFunction> orgSubDeptcoll = new List<SubFunction>();
            string filterstring = string.Empty;

            // dt = FilterDataTableByUnitID(dt, "UnitID");
            if (dt != null)
            {
                if (dt.Rows.Count > 0)
                {

                    orgSubDeptcoll = _ProcessSrv.GetSubDepartmentsByRole(OrgID, RoleID, OrgGroupID, _UserDetails.UserID.ToString());
                    if (orgSubDeptcoll != null)
                    {
                        if (orgSubDeptcoll.Count > 0)
                        {
                            foreach (SubFunction obj in orgSubDeptcoll)
                            {
                                filterstring = filterstring + SubDeptID.ToString() + "='" + obj.SubFunctionID + "' Or ";
                            }
                            //filterstring = filterstring + SubDeptID.ToString() + "=null Or " + SubDeptID.ToString() + "='0' Or " + SubDeptID.ToString() + "='" + string.Empty + "'";
                            //

                            if (orgSubDeptcoll.Count == 1)
                            {
                                filterstring = filterstring + SubDeptID.ToString() + "=null ";
                            }
                            else
                            {
                                filterstring = filterstring + SubDeptID.ToString() + "=null Or " + SubDeptID.ToString() + "='0'";
                            }
                            //filterstring = filterstring.Remove(filterstring.Length - 4, 4);

                            dr = dt.Select(filterstring);
                            if (dr.Length > 0)
                            {
                                dt = dr.CopyToDataTable();
                            }
                            else
                                dt = null;
                        }
                        else
                        {

                            filterstring = "  SubFunctionID = '0'  ";
                            dr = dt.Select(filterstring);
                            if (dr.Length > 0)
                            {
                                dt = dr.CopyToDataTable();
                            }

                        }

                    }
                    else
                        dt = null;
                }
                else
                    dt = null;
            }
            else
                dt = null;

            filterstring = string.Empty;
            return dt;
        }

        public DataTable FilterGridDataByRoles(DataTable dt, string OrgID, string RoleID, string OrgGroupID)//, string UnitID_ColumnName, string DeptID_ColumnName, string SubDeptID_ColumnName
        {
            dt = FilterDataTableByUnitID(dt, OrgID, RoleID, OrgGroupID);
            dt = FilterDataTableByDeptID(dt, OrgID, RoleID, OrgGroupID);
            dt = FilterDataTableBySubDeptID(dt, OrgID, RoleID, OrgGroupID);
            return dt;
        }

        public DataTable GetBusinessProcessByIsUnderBCM(DataTable dtAllProcess)
        {
            DataTable dtSelected = dtAllProcess.Clone();
            if (IsUnderBCM == 1)
            {
                DataRow[] drAll = dtAllProcess.Select(" ProcessID > 0 ");
                foreach (var item in drAll)
                {
                    dtSelected.ImportRow(item);
                }
                return dtSelected;
            }
            else if (IsUnderBCM == 0)
            {
                DataRow[] drAll = dtAllProcess.Select(" ProcessID = 0 ");
                foreach (var item in drAll)
                {
                    dtSelected.ImportRow(item);
                }
                return dtSelected;

            }
            else
                return dtAllProcess;
        }

        private DataTable GetBusinessEntity(DataTable dtAllProcess, int EntityTypeID)
        {
            ManageUsersDetails _oUser = LoginUserDetails();
            DataTable dtAllProcess1 = new DataTable();
            string FilterString = string.Empty;
            if (IsProductAdmin(_oUser.UserRoleID.ToString()) || IsSuperAdmin(_oUser.UserRoleID.ToString()))
            {
                //FilterString = GetFilterString1(IsUnderBCM);
            }
            else
            {
                FilterString = GetFilterStringForOwner(0, 0, 0, 0, EntityTypeID, 1);
            }

            dtAllProcess1 = dtAllProcess.Clone();

            DataRow[] drAll = FilterString == string.Empty ? dtAllProcess.Select() : dtAllProcess.Select(FilterString);

            if (drAll != null)
            {
                foreach (DataRow dr in drAll)
                {
                    dtAllProcess1.ImportRow(dr);
                }
            }
            return dtAllProcess1;
        }

        public string GetFilterStringForOwner(int OrgID = 0, int ddlBusinessUnit = 0, int ddlDepartment = 0, int ddlSubDepartment = 0, int BCMEntityTypeID = 0, int IsUnderBCM = -1)   //rohini
        {


            //Boolean ProductAdmin = false;
            string strUserID = _UserDetails.UserID.ToString();

            if (_UserDetails.UserRole.Equals("*") || _UserDetails.UserRole.Equals("**"))
            {
                // ProductAdmin = true;
            }


            DataTable dtRoleRights1 = _ProcessSrv.GetMasterPageAccessByRoleID(_UserDetails.UserRoleID.ToString(), _UserDetails.UserID.ToString(), _UserDetails.OrgID.ToString());
            var table = (from DataRow dr in dtRoleRights1.Rows
                         where dr["OrgID"].ToString() == _UserDetails.OrgID.ToString()
                          && Convert.ToInt32(dr["RoleRightsSubEntityID"]) == Convert.ToInt32(BCPEnum.RoleRightsSubEntityID.MasterScreensPrivileges)
                          && Convert.ToInt32(dr["masterentrytypeID"]) == Convert.ToInt32(BCPEnum.MasterPageType.OverAllAccess)
                         && Convert.ToInt32(dr["ParentPrivilegeID"]) == Convert.ToInt32(BCPEnum.ParentPrivilegeID.View)
                         && Convert.ToInt32(dr["PrivilegeID"]) == Convert.ToInt32(BCPEnum.PrivilegeID.View)
                         select dr);

            DataTable dtt = table.Any() ? table.CopyToDataTable() : null;

            string strAccessFilterString = string.Empty;
            string strFilterString = string.Empty;
            string strUserFilterString = string.Empty;
            string strMainFilterString = string.Empty;
            string strIfNot = string.Empty;


            string strIfOrgHead = " ( OrgHeadID = '" + strUserID + "'  OR OwnerID = '" + strUserID + "' OR  AltOwnerID = '" + strUserID + "' OR ApproverID='" + strUserID + "' OR CreatedBy = '" + strUserID + "') ";
            string strIfUnitHead = " ( UnitHeadID = '" + strUserID + "' OR  AltUnitHeadID = '" + strUserID + "'  OR UnitBCPCorID = '" + strUserID + "' OR AltBCPCorID = '" + strUserID + "' OR OwnerID = '" + strUserID + "' OR  AltOwnerID = '" + strUserID + "' OR ApproverID='" + strUserID + "' OR CreatedBy = '" + strUserID + "') ";
            string strIfFunctionHead = " ( DepartmentHeadID = '" + strUserID + "' OR AltDepartmentHeadID = '" + strUserID + "'  OR OwnerID = '" + strUserID + "' OR  AltOwnerID = '" + strUserID + "'  OR ApproverID='" + strUserID + "' OR CreatedBy = '" + strUserID + "') ";
            string strIfSubFunctionHead = " ( SubFunOwnerId = '" + strUserID + "' OR AltSubFunOwnerId = '" + strUserID + "'  OR OwnerID = '" + strUserID + "' OR  AltOwnerID = '" + strUserID + "'  OR ApproverID='" + strUserID + "' OR CreatedBy = '" + strUserID + "') ";
            strIfNot = " ( OwnerID = '" + strUserID + "' OR AltOwnerID = '" + strUserID + "' OR ApproverID = '" + strUserID + "' OR CreatedBy = '" + strUserID + "') ";

            strFilterString = IsOrgHead(strUserID,_UserDetails.OrgID.ToString()) == true ? strIfOrgHead : strIfNot;
            if (dtt != null && dtt.Rows.Count > 0)
            {
                strFilterString = string.Empty;
                strIfNot = string.Empty;
                strIfUnitHead = string.Empty;
                strIfFunctionHead = string.Empty;
                strIfSubFunctionHead = string.Empty;
            }

            if (strFilterString == strIfNot)
                strFilterString = IsUnitHead(strUserID) == true ? strIfUnitHead : strIfNot;

            if (strFilterString == strIfNot)
                strFilterString = IsFunctionHead(strUserID) == true ? strIfFunctionHead : strIfNot;

            if (strFilterString == strIfNot)
                strFilterString = IsSubFunctionHead(strUserID) == true ? strIfSubFunctionHead : strIfNot;


            DataTable dtRoleRights = new DataTable();
            dtRoleRights = _ProcessSrv.GetOrganizationalAccessByRoleAndUserID(Convert.ToInt32(_UserDetails.UserRoleID), Convert.ToInt32(_UserDetails.UserID), Convert.ToInt32(_UserDetails.OrgID));

            foreach (DataRow dr in dtRoleRights.Rows)
            {
                if (!string.IsNullOrEmpty(dr["RecordID"].ToString()))
                {

                    if (dr["RecordID"].ToString() != "0")
                    {
                        if (!string.IsNullOrEmpty(strFilterString))
                        {
                            strFilterString = strFilterString + " OR ";
                        }

                        strFilterString = strFilterString + " ( RecordID = " + dr["RecordID"].ToString() + " AND EntityTypeID=" + dr["EntitytypeID"].ToString() + ")";
                    }
                }
            }
            if (OrgID != 0)
            {
                if (ddlBusinessUnit > 0)
                {
                    if (strFilterString == string.Empty)
                    {
                        strFilterString = "1=1 ";
                    }
                    strFilterString = strFilterString + " and UnitID = " + ddlBusinessUnit;
                    if (ddlDepartment > 0)
                    {
                        strFilterString = strFilterString + " And ";
                        strFilterString = strFilterString + "DepartmentID = '" + ddlDepartment + "'";
                        if (ddlSubDepartment > 0)
                        {
                            strFilterString = strFilterString + " And ";
                            strFilterString = strFilterString + "SubFunctionID = " + ddlSubDepartment;
                        }
                    }
                    else
                    {
                        if (ddlSubDepartment > 0)
                        {
                            strFilterString = strFilterString + " And ";
                            strFilterString = strFilterString + " SubFunctionID = " + ddlSubDepartment;
                        }
                    }
                }
                else
                {
                    if (ddlDepartment > 0)
                    {
                        strFilterString = strFilterString + " and DepartmentID = '" + ddlDepartment + "'";
                        if (ddlSubDepartment > 0)
                        {
                            strFilterString = strFilterString + " And ";
                            strFilterString = strFilterString + "SubFunctionID = " + ddlSubDepartment;
                        }
                    }
                }
            }

            if (BCMEntityTypeID > 0)
            {
                strFilterString = !string.IsNullOrEmpty(strFilterString) ? strFilterString + " AND EntityTypeID ='" + BCMEntityTypeID + "'  " : "EntityTypeID ='" + BCMEntityTypeID + "'  ";
            }
            if (BCMEntityTypeID == -1)
            {
                if (!string.IsNullOrEmpty(strFilterString))
                {
                    strFilterString = "(" + strFilterString + ")";
                    strFilterString = !string.IsNullOrEmpty(strFilterString) ? strFilterString + " AND   EntityTypeID <> '" + Convert.ToInt32(BCPEnum.EntityType.BusinessProcess) + "' AND   EntityTypeID <> '" + Convert.ToInt32(BCPEnum.EntityType.Application) + "' " : strFilterString + " EntityTypeID <> '" + Convert.ToInt32(BCPEnum.EntityType.BusinessProcess) + "' AND   EntityTypeID <> '" + Convert.ToInt32(BCPEnum.EntityType.Application) + "' ";
                }

            }
            if (BCMEntityTypeID == 0)
            {


            }
            if (IsUnderBCM > 0)
            {
                strFilterString = !string.IsNullOrEmpty(strFilterString) ? strFilterString + " AND ProcessID > '0'" : "ProcessID > '0'";
            }
            if (IsUnderBCM == 0)
            {
                strFilterString = !string.IsNullOrEmpty(strFilterString) ? strFilterString + " AND ProcessID ='" + IsUnderBCM + "'  " : "ProcessID ='" + IsUnderBCM + "'  ";
            }
            if (string.IsNullOrEmpty(strFilterString))
                strMainFilterString = strUserFilterString;
            else
                strMainFilterString = strFilterString;

            return strMainFilterString;

        }

        public bool IsOrgHead(string LoginUserID,string strOrgID)
        {

            var objOrgInfo = _ProcessSrv.GetOrganizationMasterList();

            if (objOrgInfo != null)
            {
                foreach (OrgInfo objOrg in objOrgInfo)
                {
                    if (objOrg.Id == strOrgID)
                    {
                        if ((LoginUserID.Equals(objOrg.OrgHeadID)))
                        {
                            return true;
                        }
                    }
                    
                }
            }
            return false;
        }

        public bool IsUnitHead(string LoginUserID)
        {

            var objOrgUnitInfo = _ProcessSrv.GetOrganizationUnitList(_UserDetails.UserRoleID.ToString(), Convert.ToInt32(_UserDetails.OrgID));


            if (objOrgUnitInfo != null)
            {
                foreach (OrgUnit objOrgUnit in objOrgUnitInfo)
                {
                    if ((LoginUserID.Equals(objOrgUnit.UnitHeadID)) || (LoginUserID.Equals(objOrgUnit.AltUnitHeadID)) || (LoginUserID.Equals(objOrgUnit.UnitBCPCorID)) || (LoginUserID.Equals(objOrgUnit.AltBCPCorID)))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public bool IsFunctionHead(string LoginUserID)
        {

            var objFunctionInfo = _ProcessSrv.GetDepartmentList(_UserDetails.UserRoleID.ToString(), Convert.ToInt32(_UserDetails.OrgID));

            if (objFunctionInfo != null)
            {
                foreach (DepartmentInfo objFunction in objFunctionInfo)
                {
                    if ((LoginUserID.Equals(objFunction.DeptHeadID)) || (LoginUserID.Equals(objFunction.DeptAltHeadID)))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public bool IsSubFunctionHead(string LoginUserID)
        {

            //var objSubFunctionInfo = _oProcSrv.GetSubFunctionList(Utilities.SessionLoginUser.UserRoleID, Convert.ToInt32(Utilities.SessionLoginUser.OrgID));

            var objSubFunctionInfo = _ProcessSrv.GetSubFunctionList_New(Convert.ToInt32(_UserDetails.OrgID));

            if (objSubFunctionInfo != null)
            {
                foreach (SubFunction objSUbFunction in objSubFunctionInfo)
                {
                    if ((LoginUserID.Equals(objSUbFunction.OwnerId)) || (LoginUserID.Equals(objSUbFunction.AlternateOwnerId)))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        public ButtonAcces ShowButtonsByAccess(string strStatusId, string strApproverId, string strOwnerID, string strAltOwnerID,
            string strUserId, int buttonStatus = 0)
        {

            ButtonAcces ButtonAccess = new ButtonAcces();

            try
            {

                DataTable dtRoleRights = new DataTable();
                DataTable dtt = new DataTable();
                strStatusId = string.IsNullOrEmpty(strStatusId) == true ? "0" : strStatusId;

                if (_UserDetails != null)
                {
                    dtRoleRights = _ProcessSrv.GetMasterPageAccessByRoleID(_UserDetails.UserRoleID.ToString(), _UserDetails.UserID.ToString(), _UserDetails.OrgID.ToString());

                    // filter Data according PagetypeID and CanSendForApproval
                    var table = (from DataRow dr in dtRoleRights.Rows
                                 where dr["OrgID"].ToString() == _UserDetails.OrgID.ToString()
                                  && Convert.ToInt32(dr["RoleRightsSubEntityID"]) == Convert.ToInt32(BCPEnum.RoleRightsSubEntityID.MasterScreensPrivileges)
                                  && Convert.ToInt32(dr["masterentrytypeID"]) == Convert.ToInt32(BCPEnum.MasterPageType.OverAllAccess)
                                 && Convert.ToInt32(dr["ParentPrivilegeID"]) == Convert.ToInt32(BCPEnum.ParentPrivilegeID.View)
                                 select dr);

                    dtt = table.Any() ? table.CopyToDataTable() : null;
                    if (dtt != null && dtt.Rows.Count > 0)
                    {

                        //  Approve Access is true or false
                        var tempTable = (from DataRow dr in table where Convert.ToInt32(dr["PrivilegeID"]) == Convert.ToInt32(BCPEnum.PrivilegeID.Approve) select dr);
                        DataTable DT = tempTable.Any() ? tempTable.CopyToDataTable() : null;
                        if (DT != null && DT.Rows.Count > 0 && Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval))
                        {
                            ButtonAccess.btnApprove = "";
                            ButtonAccess.btnImgApprove = "";
                        }
                        else
                        {
                            if (DT != null && DT.Rows.Count > 0)
                            {
                                ButtonAccess.btnApprove = "style=display:none";
                                ButtonAccess.btnImgApprove = "style=display:none";
                            }
                            else
                            {
                                ButtonAccess.btnApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";  //btnDisApprove =
                                ButtonAccess.btnImgApprove = Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) && strApproverId.Equals(strUserId) == true ? "" : "style=display:none"; 
                            }
                        }

                        // Delete Access is true or flase
                        tempTable = (from DataRow dr in table where Convert.ToInt32(dr["PrivilegeID"]) == Convert.ToInt32(BCPEnum.PrivilegeID.Delete) select dr);
                        DT = tempTable.Any() ? tempTable.CopyToDataTable() : null;
                        if ((DT != null && DT.Rows.Count > 0) && (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) ||
                            Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated)))
                        {
                            ButtonAccess.btnImgDelete = "";
                        }
                        else
                        {
                            if (DT != null && DT.Rows.Count > 0)
                            {
                                ButtonAccess.btnImgDelete = "style=display:none";
                            }
                            else
                            {
                                ButtonAccess.btnImgDelete = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated))) && (strOwnerID == strUserId || strAltOwnerID == strUserId || _UserDetails.UserRole == "BCM Team")) == true ? "" : "style=display:none";

                            }
                        }

                        //  btnupdate Access is true or false
                        tempTable = (from DataRow dr in table where Convert.ToInt32(dr["PrivilegeID"]) == buttonStatus select dr);
                        DT = tempTable.Any() ? tempTable.CopyToDataTable() : null;
                        if ((DT != null && DT.Rows.Count > 0) && (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) ||
                            Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated)))
                        {
                            ButtonAccess.btnUpdate = "";
                        }
                        else
                        {
                            if (DT != null && DT.Rows.Count > 0)
                            {
                                ButtonAccess.btnUpdate = "style=display:none";
                            }
                            else
                            {
                                //if (strOwnerID == null || strAltOwnerID == null)
                                //{
                                //    btnUpdate = Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) || Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated);
                                //}
                                //else
                                //{
                                ButtonAccess.btnUpdate = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated))) && (strOwnerID == strUserId || strAltOwnerID == strUserId || _UserDetails.UserRole == "BCM Team")) == true ? "" : "style=display:none";
                                //}
                            }
                        }

                        //  Disapprove Access is true or false
                        tempTable = (from DataRow dr in table where Convert.ToInt32(dr["PrivilegeID"]) == Convert.ToInt32(BCPEnum.PrivilegeID.Disapprove) select dr);
                        DT = tempTable.Any() ? tempTable.CopyToDataTable() : null;
                        if (DT != null && DT.Rows.Count > 0 && Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval))
                        {
                            ButtonAccess.btnDisApprove = "";
                            ButtonAccess.btnImgDisApprove = "";
                        }
                        else
                        {
                            if (DT != null && DT.Rows.Count > 0)
                            {
                                ButtonAccess.btnDisApprove = "style=display:none";
                                ButtonAccess.btnImgDisApprove = "style=display:none"; ;
                            }
                            else
                            {
                                ButtonAccess.btnDisApprove = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval)) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";
                                ButtonAccess.btnImgDisApprove = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved))) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";
                            }
                        }

                        //  SendForApprove Access is true or false
                        tempTable = (from DataRow dr in table where Convert.ToInt32(dr["PrivilegeID"]) == Convert.ToInt32(BCPEnum.PrivilegeID.SendForApprove) select dr);
                        DT = tempTable.Any() ? tempTable.CopyToDataTable() : null;
                        if (DT != null && DT.Rows.Count > 0 &&
                           (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated) ||
                           Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved)) && (strOwnerID != "0" && strOwnerID != null))
                        {
                            ButtonAccess.btnSendForApproval = "";
                        }
                        else
                        {
                            if (DT != null && DT.Rows.Count > 0)
                            {
                                ButtonAccess.btnSendForApproval = "style=display:none";
                            }
                            else
                            {
                                ButtonAccess.btnSendForApproval = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated) || Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved))
                                    && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none"; ;//Only owner & Alt owner can send for approval
                            }
                        }

                        //  CreateNewVersion Access is true or false
                        tempTable = (from DataRow dr in table where Convert.ToInt32(dr["PrivilegeID"]) == Convert.ToInt32(BCPEnum.PrivilegeID.CreateNewVersion) select dr);
                        DT = tempTable.Any() ? tempTable.CopyToDataTable() : null;
                        if (DT != null && DT.Rows.Count > 0 && Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved))
                        {
                            ButtonAccess.btnCreateVersion = "";
                        }
                        else
                        {
                            if (DT != null && DT.Rows.Count > 0)
                            {
                                ButtonAccess.btnCreateVersion = "style=display:none";
                            }
                            else
                            {
                                ButtonAccess.btnCreateVersion = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved) && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none";//Only owner & Alt owner can send for CreateNew version
                            }
                        }
                    }
                    else
                    {

                        if (strOwnerID != string.Empty && strOwnerID != "0" && strOwnerID != null)
                        {
                            ButtonAccess.btnSendForApproval = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated) && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none";//Only owner & Alt owner can send for approval

                            ButtonAccess.btnReSendApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none";//Only owner can send for approval
                            //btnUpdate = Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) || Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated);
                            //if (_UserDetails.UserRole != "*")

                            ButtonAccess.btnApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";  //btnDisApprove =
                            ButtonAccess.btnDisApprove = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval)) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";

                            ButtonAccess.btnCreateVersion = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved) && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none";//Only owner & Alt owner can send for CreateNew version
                            ButtonAccess.btnImgApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";  //btnDisApprove =
                            ButtonAccess.btnImgDisApprove = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved))) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";
                            ButtonAccess.btnUpdate = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated))) && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none";

                            ButtonAccess.btnImgApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";
                            //btnImgDisApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved))) && strApproverId.Equals(strUserId);
                            ButtonAccess.btnImgDelete = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated))) && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none";
                        }
                        else
                        {
                            ButtonAccess.btnSendForApproval = "style=display:none";
                            ButtonAccess.btnReSendApprove = "style=display:none";
                            //ButtonAccess.btnUpdate = "style=display:none";
                            ButtonAccess.btnApprove = "style=display:none";
                            ButtonAccess.btnDisApprove = "style=display:none";
                            ButtonAccess.btnCreateVersion = "style=display:none";

                        }
                    }
                }
                else
                {

                    if (strOwnerID != string.Empty && strOwnerID != "0" && strOwnerID != null)
                    {
                        ButtonAccess.btnSendForApproval = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated) && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none";//Only owner & Alt owner can send for approval

                        ButtonAccess.btnReSendApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none"; //Only owner can send for approval
                        //btnUpdate = Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) || Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated);
                        //if (_UserDetails.UserRole != "*")

                        ButtonAccess.btnApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";  //btnDisApprove =
                        ButtonAccess.btnDisApprove = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved))) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";

                        ButtonAccess.btnCreateVersion = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved) && (strOwnerID == strUserId || strAltOwnerID == strUserId)) == true ? "" : "style=display:none";//Only owner & Alt owner can send for CreateNew version
                        ButtonAccess.btnImgApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none"; ;  //btnDisApprove =
                        ButtonAccess.btnImgDisApprove = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved))) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";
                        ButtonAccess.btnUpdate = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Disapproved) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Initiated))) && (strOwnerID == strUserId || strAltOwnerID == strUserId || _UserDetails.UserRole == "BCM Team")) == true ? "" : "style=display:none";

                        ButtonAccess.btnImgApprove = (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) && strApproverId.Equals(strUserId)) == true ? "" : "style=display:none";
                        ButtonAccess.btnImgDisApprove = ((Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.WaitingforApproval) || (Convert.ToInt32(strStatusId) == Convert.ToInt32(BCPEnum.ApprovalType.Approved)) && strApproverId.Equals(strUserId))) == true ? "" : "style=display:none";

                    }
                    else
                    {
                        ButtonAccess.btnSendForApproval = "style=display:none";

                        ButtonAccess.btnReSendApprove = "style=display:none";
                        ButtonAccess.btnUpdate = "";
                        ButtonAccess.btnApprove = "style=display:none";
                        ButtonAccess.btnDisApprove = "style=display:none";

                        ButtonAccess.btnCreateVersion = "style=display:none";

                    }
                }




                return ButtonAccess;
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
                return ButtonAccess;
            }
        }

        #region for mobile
        public ManageUsersDetails? LoginResourceSessionDetails()
        {
            ManageUsersDetails? objUserDetails = null;
            try
            {
                var HttpContext = _HttpContextAccessor.HttpContext;
                if (HttpContext != null)
                {
                    var LoginUserDetailsJSON = HttpContext.Session.GetString("LoginResourceDetails");

                    if (LoginUserDetailsJSON != null)
                    {
                        objUserDetails = new ManageUsersDetails();
                        objUserDetails = JsonConvert.DeserializeObject<ManageUsersDetails>(LoginUserDetailsJSON);
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }

            return objUserDetails;
        }

        public int DefaultRiskProfileID
        {
            get
            {
                var HttpContext = _HttpContextAccessor.HttpContext;
                if (HttpContext.Session.GetString("LoginResourceDetails") != null)
                {
                    ManageUsersDetails objManageUsersDetails;
                    var LoginUserDetailsJSON = HttpContext.Session.GetString("LoginResourceDetails");
                    objManageUsersDetails = JsonConvert.DeserializeObject<ManageUsersDetails>(LoginUserDetailsJSON);
                    return _ProcessSrv.GetDefaultProfile(Convert.ToInt32((objManageUsersDetails.OrgID)));
                }
                else
                {
                    return 0;
                }


            }
        }

        public static object BIASurveyUtility { get; set; }

        public List<Disaster> PopulateDisasterTypeList(int iDefaultProfileID)
        {
            List<Disaster> lstDisaster = new List<Disaster>();
            try
            {
                lstDisaster = _ProcessSrv.GetDisasterTypeList(iDefaultProfileID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstDisaster;
        }

        public List<Disaster?> PopulateDisasterTypeList_New()
        {
            List<Disaster> lstDisaster = new List<Disaster>();
            try
            {

                lstDisaster = _ProcessSrv.GetDisasterTypeList_New();

            }
            catch (Exception ex)
            {
                //OrganizationInfoList = null;
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstDisaster;
        }

        public List<Disaster> PopulateIncidentByDisasterID(int iDisasterId, int IncidentID)
        {
            List<Disaster> lstDisaster = new List<Disaster>();
            try
            {
                lstDisaster = _ProcessSrv.GetIncidentTypesByDisasterID(iDisasterId, IncidentID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstDisaster;
        }

        public List<ResourcesInfo> GetResourcesListForDDL(int iOrgID = 0)
        {
            List<ResourcesInfo> lstResourcesInfo = new List<ResourcesInfo>();
            try
            {
                lstResourcesInfo = _ProcessSrv.GetResourcesListForDDL(iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstResourcesInfo;
        }

        public List<RiskImpactMaster> GetAllRiskProbabilitymaster(int iOrgID)
        {
            var ImpactmasterList = new List<RiskImpactMaster>();
            try
            {
                int iDefaultProfileID = RiskProfileID;

                ImpactmasterList = _ProcessSrv.GetAllRiskProbabilitymaster(iOrgID, iDefaultProfileID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return ImpactmasterList;
        }

        public List<RiskImpactMaster> GetAllRiskImpactmaster(int iOrgID)
        {
            var ImpactmasterList = new List<RiskImpactMaster>();

            try
            {
                int iDefaultProfileID = RiskProfileID;
                ImpactmasterList = _ProcessSrv.GetAllRiskImpactmaster(iOrgID, iDefaultProfileID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return ImpactmasterList;
        }

        public List<RecoveryPlan> PopulateApprovedRecoveryPlan(int iLoginUser, int iOrgID = 0)
        {
            List<RecoveryPlan> lstRecoveryPlan = new List<RecoveryPlan>();
            try
            {
                lstRecoveryPlan = _ProcessSrv.GetRecoveryPlansList(iLoginUser, iOrgID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstRecoveryPlan;
        }

        #region RISK Assessment
        public DataTable GetFilteredBIASurveyDataTable(string FilterExpression, int iOrgID = 0, int iEntityTypeID = 0)
        {
            DataTable dtAllProcess = new DataTable();
            DataTable drAll = new DataTable();
            dtAllProcess = GetBIASurveyDataTableSchema();

            if (string.IsNullOrEmpty(FilterExpression))
            {
                drAll = GetAllBIASurveyDataTable(iOrgID, iEntityTypeID);//.Select();
            }
            else
            {
                drAll = GetAllBIASurveyDataTable(iOrgID, iEntityTypeID);//.Select(FilterExpression);
            }


            if (drAll != null)
            {
                foreach (DataRow dr in drAll.Rows)
                {
                    dtAllProcess.ImportRow(dr);
                }
            }
            return dtAllProcess;
        }

        public DataTable GetBIASurveyDataTableSchema()
        {
            DataTable dtBIASurveyData = new DataTable();
            dtBIASurveyData.Columns.Add("OrgID");
            dtBIASurveyData.Columns.Add("OrgName");
            dtBIASurveyData.Columns.Add("UnitID");
            dtBIASurveyData.Columns.Add("UnitName");
            dtBIASurveyData.Columns.Add("UnitHeadID");
            dtBIASurveyData.Columns.Add("UnitBCPCorID");
            dtBIASurveyData.Columns.Add("AltBCPCorID");
            dtBIASurveyData.Columns.Add("DepartmentID");
            dtBIASurveyData.Columns.Add("DepartmentName");
            dtBIASurveyData.Columns.Add("DepartmentHeadID");
            dtBIASurveyData.Columns.Add("AltDepartmentHeadID");
            dtBIASurveyData.Columns.Add("SubfunctionID");
            dtBIASurveyData.Columns.Add("SubFunctionName");
            dtBIASurveyData.Columns.Add("ProcessID");
            dtBIASurveyData.Columns.Add("ProcessID_Encrypted");
            dtBIASurveyData.Columns.Add("ProcessName");
            dtBIASurveyData.Columns.Add("OwnerID");
            dtBIASurveyData.Columns.Add("OwnerName");
            dtBIASurveyData.Columns.Add("OwnerMobile");
            dtBIASurveyData.Columns.Add("OwnerEmail");
            dtBIASurveyData.Columns.Add("AltOwnerID");
            dtBIASurveyData.Columns.Add("AltOwnerName");
            dtBIASurveyData.Columns.Add("AltOwnerMobile");
            dtBIASurveyData.Columns.Add("AltOwnerEmail");
            dtBIASurveyData.Columns.Add("ApproverID");

            dtBIASurveyData.Columns.Add("Comment");
            dtBIASurveyData.Columns.Add("RTO");
            dtBIASurveyData.Columns.Add("RTOText");
            dtBIASurveyData.Columns.Add("CategoryRange");
            dtBIASurveyData.Columns.Add("RPO");
            dtBIASurveyData.Columns.Add("OwnerRTO");
            dtBIASurveyData.Columns.Add("MTR");
            dtBIASurveyData.Columns.Add("OwnerMTR");
            dtBIASurveyData.Columns.Add("IsCritical");
            dtBIASurveyData.Columns.Add("ApproverName");
            dtBIASurveyData.Columns.Add("IsActive");
            dtBIASurveyData.Columns.Add("ImageAltText");
            dtBIASurveyData.Columns.Add("ImageStatus");
            dtBIASurveyData.Columns.Add("StatusID");

            dtBIASurveyData.Columns.Add("ProcessCode");
            dtBIASurveyData.Columns.Add("Version");
            dtBIASurveyData.Columns.Add("IsEffective");

            dtBIASurveyData.Columns.Add("ImageCompanyEmail");
            dtBIASurveyData.Columns.Add("ImageCompanyEmailToolTip");
            dtBIASurveyData.Columns.Add("ImageMobileNo");
            dtBIASurveyData.Columns.Add("ImageMobileNoToolTip");

            dtBIASurveyData.Columns.Add("SubFunOwnerId");
            dtBIASurveyData.Columns.Add("AltSubFunOwnerId");
            dtBIASurveyData.Columns.Add("OrgHeadId");
            dtBIASurveyData.Columns.Add("AltUnitHeadID");
            dtBIASurveyData.Columns.Add("ReviewDate");
            dtBIASurveyData.Columns.Add("LastReviewDate");
            dtBIASurveyData.Columns.Add("ProfileID");
            dtBIASurveyData.Columns.Add("Reminder");
            dtBIASurveyData.Columns.Add("RecurrenceRule");
            dtBIASurveyData.Columns.Add("RecurrenceParentID");
            dtBIASurveyData.Columns.Add("EntityTypeID");
            dtBIASurveyData.Columns.Add("RecordID");
            dtBIASurveyData.Columns.Add("EntityTypeName");
            dtBIASurveyData.Columns.Add("OrgGroupID");
            dtBIASurveyData.Columns.Add("CreateDate");
            dtBIASurveyData.Columns.Add("CreatedBy");
            dtBIASurveyData.Columns.Add("ProductKeyEntity");


            return dtBIASurveyData;
        }

        public DataTable GetAllBIASurveyDataTable(int iOrgID = 0, int iEntityTypeId = 0)
        {
            List<BusinessProcessInfo> objBIASurveyORGColl = new List<BusinessProcessInfo>();
            DataTable dtBIASurveyData = new DataTable();
            DataRow dr = null;
            string? productKeyEntity = string.Empty;
            dtBIASurveyData = GetBIASurveyDataTableSchema().Clone();

            if (iOrgID > 0)
            {
                OrgInfo orgDetails = _ProcessSrv.GetOrganizationMasterByOrgId(iOrgID.ToString());
                productKeyEntity = orgDetails.ProductKeyEntity;

                if (productKeyEntity == "1")
                {
                    objBIASurveyORGColl = _ProcessSrv.GetBIASurveyListOrgUnitLevel(iOrgID, iEntityTypeId);
                }
                else if (productKeyEntity == "2")
                {
                    objBIASurveyORGColl = _ProcessSrv.GetBIASurveyListOrgUnitLevelForResource(iOrgID, iEntityTypeId);
                }
            }
            else
            {
                objBIASurveyORGColl = _ProcessSrv.GetBIASurveyListOrgUnitLevel(iOrgID, iEntityTypeId);
            }

            foreach (BusinessProcessInfo objBIASurveyORG in objBIASurveyORGColl)
            {
                dr = dtBIASurveyData.NewRow();
                dr["OrgID"] = string.IsNullOrEmpty(Convert.ToString(objBIASurveyORG.OrgID)) ? "0" : objBIASurveyORG.OrgID;
                dr["OrgName"] = objBIASurveyORG.OrgName;
                dr["UnitID"] = string.IsNullOrEmpty(Convert.ToString(objBIASurveyORG.UnitID)) ? "0" : objBIASurveyORG.UnitID;
                dr["UnitName"] = objBIASurveyORG.UnitName;
                dr["UnitHeadID"] = objBIASurveyORG.UnitHeadID;
                dr["UnitBCPCorID"] = objBIASurveyORG.UnitBCPCorID;
                dr["AltBCPCorID"] = objBIASurveyORG.AltBCPCorID;
                dr["DepartmentID"] = string.IsNullOrEmpty(Convert.ToString(objBIASurveyORG.DepartmentID)) ? "0" : objBIASurveyORG.DepartmentID;
                dr["DepartmentName"] = objBIASurveyORG.DepartmentName;
                dr["DepartmentHeadID"] = objBIASurveyORG.DepartmentHeadID;
                dr["AltDepartmentHeadID"] = objBIASurveyORG.AltDepartmentHeadID;
                dr["SubfunctionID"] = string.IsNullOrEmpty(Convert.ToString(objBIASurveyORG.SubfunctionID)) ? "0" : objBIASurveyORG.SubfunctionID;
                dr["SubFunctionName"] = objBIASurveyORG.SubFunctionName;
                dr["ProcessID"] = string.IsNullOrEmpty(Convert.ToString(objBIASurveyORG.ProcessID)) ? "0" : objBIASurveyORG.ProcessID;
                dr["ProcessID_Encrypted"] = objBIASurveyORG.ProcessID_Encrypted;
                dr["ProcessName"] = objBIASurveyORG.ProcessName;
                dr["OwnerID"] = objBIASurveyORG.ProcessOwnerID;
                dr["OwnerName"] = objBIASurveyORG.ProcessOwner;
                dr["OwnerMobile"] = objBIASurveyORG.ProcessOwnerMobile;
                dr["OwnerEmail"] = objBIASurveyORG.OwnerEmail;
                dr["AltOwnerID"] = objBIASurveyORG.AltProcessOwnerID;
                dr["AltOwnerName"] = objBIASurveyORG.AltProcessOwner;
                dr["AltOwnerMobile"] = objBIASurveyORG.AltProcessOwnerMobile;
                dr["AltOwnerEmail"] = objBIASurveyORG.AltProcessOwnerEmail;
                dr["RTO"] = objBIASurveyORG.RTO;
                dr["RTOText"] = objBIASurveyORG.RTOText;
                dr["CategoryRange"] = objBIASurveyORG.CategoryRange;
                dr["RPO"] = objBIASurveyORG.RPO;
                dr["OwnerRTO"] = objBIASurveyORG.OwnerRTO;
                dr["MTR"] = objBIASurveyORG.MTR;
                dr["OwnerMTR"] = objBIASurveyORG.OwnerMTR;
                dr["Comment"] = objBIASurveyORG.Comment;
                dr["ApproverName"] = objBIASurveyORG.ApproverName;
                dr["IsCritical"] = objBIASurveyORG.IsCritical;
                dr["IsActive"] = objBIASurveyORG.ProcessIsActive;
                dr["StatusID"] = objBIASurveyORG.Status;
                dr["ProcessCode"] = objBIASurveyORG.ProcessCode;
                dr["Version"] = objBIASurveyORG.ProcessVersion;
                dr["IsEffective"] = objBIASurveyORG.IsEffective;
                dr["ApproverID"] = objBIASurveyORG.ApproverID;
                dr["ImageMobileNo"] = objBIASurveyORG.ActivationModeMobile;
                dr["ImageMobileNoToolTip"] = objBIASurveyORG.MobileVerifiedTooltip;
                dr["ImageCompanyEmail"] = objBIASurveyORG.ActivationModeEmail;
                dr["ImageCompanyEmailToolTip"] = objBIASurveyORG.EmailVerifiedTooltip;
                dr["OrgHeadId"] = objBIASurveyORG.OrgHeadId;
                dr["AltUnitHeadID"] = objBIASurveyORG.AltUnitHeadId;
                dr["ImageMobileNo"] = objBIASurveyORG.ActivationModeMobile;
                dr["ImageMobileNoToolTip"] = objBIASurveyORG.MobileVerifiedTooltip;
                dr["ImageCompanyEmail"] = objBIASurveyORG.ActivationModeEmail;
                dr["ImageCompanyEmailToolTip"] = objBIASurveyORG.EmailVerifiedTooltip;
                dr["LastReviewDate"] = objBIASurveyORG.LastReviewDate;
                dr["ReviewDate"] = objBIASurveyORG.ReviewDate;
                dr["ProfileID"] = objBIASurveyORG.ProfileID;
                dr["Reminder"] = objBIASurveyORG.Reminder;
                dr["RecurrenceRule"] = objBIASurveyORG.RecurrenceRule;
                dr["RecurrenceParentID"] = objBIASurveyORG.RecurrenceParentID;
                dr["EntityTypeID"] = objBIASurveyORG.EntityTypeID;
                dr["RecordID"] = objBIASurveyORG.RecordID;
                dr["EntityTypeName"] = objBIASurveyORG.BCMEntityType;
                dr["OrgGroupID"] = objBIASurveyORG.OrgGroupID;
                dr["CreateDate"] = objBIASurveyORG.CreateDate;
                dr["CreatedBy"] = objBIASurveyORG.CreatedBy;
                dr["ProductKeyEntity"] = productKeyEntity;
                dtBIASurveyData.Rows.Add(dr);
            }
            return dtBIASurveyData;
        }

        public List<BCMEntitiesTypeMaster> PopulateBCMEntitiesTypeMaster_ByEntityTypeID(int iEntityTypeID)
        {
            List<BCMEntitiesTypeMaster> lstBCMEntitiesTypeMasterColl = new List<BCMEntitiesTypeMaster>();
            try
            {
                lstBCMEntitiesTypeMasterColl = _ProcessSrv.BCMEntitiesTypeMaster_ByEntityTypeID(iEntityTypeID);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBCMEntitiesTypeMasterColl;
        }

        public List<ResourcesInfo> PopulateGetPeoplesByEntityTypeId(int EntityTypeId)
        {
            List<ResourcesInfo> lstCompanyMasterInfo = _ProcessSrv.GetPeoplesByEntityTypeId(EntityTypeId);
            return lstCompanyMasterInfo;
        }
        public List<CompanyMasterInfo> PopulateGetCompanyMasterInfoByEntityTypeId(int EntityTypeId)
        {
            List<CompanyMasterInfo> lstCompanyMasterInfo = _ProcessSrv.GetCompanyMasterInfoByEntityTypeId(EntityTypeId);
            return lstCompanyMasterInfo;
        }
        public List<ITServiceMaster> PopulateGetITServiceMasterByEntityTypeId(int EntityId)
        {
            List<ITServiceMaster> lstITServiceMaster = _ProcessSrv.GetAllITServiceMasterByEntityTypeId(EntityId);
            return lstITServiceMaster;
        }
        public List<LocationMaster> PopulateGetLocationMasterList_EntityId(int EntityId)
        {
            List<LocationMaster> objFacility = _ProcessSrv.GetLocationMasterListByEntityId(EntityId);
            return objFacility;
        }
        public List<BusinessProcessInfo> PopulateApplicationByEntityTypeId(int EntityTyepId)
        {
            List<BusinessProcessInfo> lstBusinessProcessInfo = _ProcessSrv.GetApplicationByEntityTypeId(EntityTyepId);
            return lstBusinessProcessInfo;
        }
        public List<Facility> PopulateGetAllFacilities_OrgId(int iOrgId)
        {
            List<Facility> objFacility = _ProcessSrv.GetAllFacilities_OrgId(iOrgId);
            return objFacility;
        }
        public List<BusinessProcessInfo> PopulateBCMEntityByEntityId(int ByEntityId)
        {
            List<BusinessProcessInfo> objBusinessProcessInfo = _ProcessSrv.GetBCMEntityByEntityId(Convert.ToInt32(ByEntityId));
            return objBusinessProcessInfo;
        }

        public List<BusinessProcessInfo> PopulateBCMEntityGetAll()
        {
            List<BusinessProcessInfo> objBusinessProcessInfo = _ProcessSrv.GetAllBCMEntity();
            return objBusinessProcessInfo;
        }

        #endregion

        #endregion
    }

    public class BIASurveyUtility
    {
        ProcessSrv _ProcessSrv { get; set; }
        public VaultSettings _objVaultSettings = null;
        private readonly IHttpContextAccessor _HttpContextAccessor;
        private readonly CVLogger _CVLogger;
        ConfigSettings? _ConfigSettings;

        private readonly IConfiguration _configuration;
        public BIASurveyUtility(ProcessSrv ProcessSrv, IHttpContextAccessor HttpContextAccessor, CVLogger logger)
        {
            _ProcessSrv = ProcessSrv;
            _HttpContextAccessor = HttpContextAccessor;
            _CVLogger = logger;
            _configuration = (new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json").Build()); ;
        }

        public List<BusinessProcessInfo> GetBIASurveyDataTable(int iOrgID = 0, int iEntityTypeId = 0)
        {
            List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
            string strProductKeyEntity = string.Empty;
            try
            {
                if (iOrgID > 0)
                {
                    OrgInfo objOrgInfo = _ProcessSrv.GetOrganizationMasterByOrgId(iOrgID.ToString());
                    strProductKeyEntity = objOrgInfo.ProductKeyEntity;

                    if (strProductKeyEntity == "1")
                    {
                        lstBusinessProcessInfo = _ProcessSrv.GetBIASurveyListOrgUnitLevel(iOrgID, iEntityTypeId);
                    }
                    else if (strProductKeyEntity == "2")
                    {
                        lstBusinessProcessInfo = _ProcessSrv.GetBIASurveyListOrgUnitLevelForResource(iOrgID, iEntityTypeId);
                    }
                }
                else
                {
                    lstBusinessProcessInfo = _ProcessSrv.GetBIASurveyListOrgUnitLevel(iOrgID, iEntityTypeId);
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcessInfo;
        }

        public List<BusinessProcessInfo> GetFilteredBIASurveyDataTable(string strFilterExpression, int iOrgID = 0, int iEntityTypeID = 0)
        {
            List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
            try
            {
                if (string.IsNullOrEmpty(strFilterExpression))
                {
                    lstBusinessProcessInfo = GetBIASurveyDataTable(iOrgID, iEntityTypeID);
                }
                else
                {
                    lstBusinessProcessInfo = GetBIASurveyDataTable(iOrgID, iEntityTypeID);
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorUtilities(ex);
            }
            return lstBusinessProcessInfo;
        }
    }


}

