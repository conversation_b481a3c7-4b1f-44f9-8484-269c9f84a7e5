﻿@model IEnumerable<BCM.BusinessClasses.BusinessProcessInfo>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using BCM.Shared

@{
    int iIndex = 1;
    string RPOText = string.Empty;
}

@if (ViewBag.Application != null && ((IEnumerable<dynamic>)ViewBag.Application).Any())
{
    @foreach (var item in ViewBag.Application)
{
    <tr>
        <td>@iIndex</td>
        <td>
            <div class="d-grid">
                <ul class="ps-0 mb-0">
                    @{
                        if (!string.IsNullOrEmpty(item.ProcessName))
                        {
                            // <li class="list-group-item"><a class="text-primary" href="#" data-bs-toggle="modal" data-bs-target="#SummaryModal"> @item.ProcessName</a></li>
                            <li class="list-group-item">@item.ProcessName</li>
                        }
                        else
                        {
                            <span class="text-danger"><i class="cv-na" title="Not Available"></i></span>
                        }
                    }
                    <li class="list-group-item" style="display:none">@item.ProcessName</li>
                    <li class="list-group-item" style="display:none"><span class="text-secondary">Version</span> :  @item.ProcessVersion</li>
                    @if (Convert.ToString(@item.ProcessCode) == "" || Convert.ToString(@item.ProcessCode) == null)
                    {
                        <li class="list-group-item text-secondary" style="display:none">Is Under BCM Scope: <span class="text-warning">No</span></li>
                    }
                    else
                    {
                        <li class="list-group-item text-secondary" style="display:none">Is Under BCM Scope: <span class="text-success">Yes</span></li>
                    }
                </ul>
            </div>
        </td>
        <td>
            <table>
                <tbody>
                    <tr>
                        @* <td class="fw-semibold"><i class="cv-user"></i></td>
                        <td> : </td> *@
                        <td>@item.ProcessOwner</td>
                    </tr>
                    <tr style="display:none">
                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                        <td>:</td>
                        <td>@item.OwnerEmail</td>
                    </tr>
                    <tr style="display:none">
                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                        <td>:</td>
                        <td>@item.ProcessOwnerMobile</td>
                    </tr>
                </tbody>
            </table>
        </td>
        <td>
            <table>
                <tbody>
                    <tr title="Unit">
                        @* <td class="fw-semibold"><i class="cv-unit"></i> </td>
                        <td>:</td> *@
                        <td>@item.UnitName</td>
                    </tr>
                </tbody>
            </table>
        </td>
        <td>
            <table>
                <tbody>
                    <tr title="Department">
                        @* <td class="fw-semibold"><i class="cv-department"></i> </td>
                        <td>:</td> *@
                        <td>@item.DepartmentName</td>
                    </tr>
                </tbody>
            </table>
        </td>
        <td>
            <table>
                <tbody>
                    <tr title="Sub Department">
                        @* <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                        <td>:</td> *@
                        <td>@item.SubFunctionName</td>
                    </tr>
                </tbody>
            </table>
        </td>
        <td style="display:none">
            <table>
                <tbody>
                    <tr title="Organization">
                        <td class="fw-semibold"><i class="cv-company"></i></td>
                        <td> : </td>
                        <td>
                            @item.OrgName
                        </td>
                    </tr>
                    <tr title="Unit">
                        <td class="fw-semibold"><i class="cv-unit"></i> </td>
                        <td>:</td>
                        <td>@item.UnitName</td>
                    </tr>
                    <tr title="Department">
                        <td class="fw-semibold"><i class="cv-department"></i> </td>
                        <td>:</td>
                        <td>@item.DepartmentName</td>
                    </tr>
                    <tr title="Sub Department">
                        <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                        <td>:</td>
                        <td>@item.SubFunctionName</td>
                    </tr>
                </tbody>
            </table>
        </td>
        <td>
            <table>
                <tbody>
                    <tr>
                        @* <td><i class="cv-RTO me-1"></i>User RTO</td>
                        <td> : </td> *@
                        <td>
                            @{
                                if (item.OwnerRTO == "0" || item.OwnerRTO == null)
                                {
                                    <span class="text-danger"><i class="cv-na" title="Not Available"></i></span>
                                }
                                else
                                {
                                     var RTO = @Utilities.GetFormattedRTO(Convert.ToInt32(item.OwnerRTO));
                                     <span>@RTO</span>
                                }
                            }
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td><i class="cv-user me-1"></i>User MAO</td>
                        <td> : </td>
                        <td>@item.OwnerMTR</td>
                    </tr>
                    <tr style="display:none">
                        <td><i class="cv-calculated me-1"></i>Calculated RTO</td>
                        <td> : </td>
                        <td>@item.RTO</td>
                    </tr>
                    <tr style="display:none">
                        <td><i class="cv-calculated me-1"></i>Calculated MAO</td>
                        <td> : </td>
                        <td>@item.MTR</td>
                    </tr>
                    <tr style="display:none">
                        <td><i class="cv-rpo me-1"></i>RPO</td>
                        <td> : </td>
                        <td>@item.RPO</td>
                    </tr>
                </tbody>
            </table>
        </td>
        @if (Convert.ToString(@item.IsCritical) == "0")
        {
            <td class="text-success">
                No
            </td>
        }
        else
        {
            <td class="text-danger">
                Yes
            </td>
        }
        <td>
            @{
                int statusId = Convert.ToInt32(item.Status);
            }
            <span class="d-flex align-items-center @BCM.Shared.Utilities.ApprovalStatusWiseTextClass(statusId)">
                <i class="@BCM.Shared.Utilities.ApprovalStatusWiseClass(statusId) me-2"></i>
                @BCM.Shared.Utilities.ApprovalStatus(statusId)
            </span>
        </td>
        @* <td>
            <a class="btn btn-sm btn-primary accept-policy px-2" asp-action="BusinessProcessForm" asp-area="BCMProcessBIA" asp-controller="BusinessProcessForm" asp-route-strEntityTypeID="@BCM.Security.Helper.CryptographyHelper.Encrypt(Convert.ToString((int)BCPEnum.EntityType.Application))" asp-route-strRecordID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.RecordID.ToString())" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.ProcessID.ToString())">
                <i class="cv-page-name align-middle ViewBIA" type="button" title="View BIA"></i>
            </a>
        </td> *@
        <td>
            <div class="d-flex align-items-center gap-2">
                <a class="text-dark" asp-action="BusinessProcessForm" asp-area="BCMProcessBIA" asp-controller="BusinessProcessForm" asp-route-strEntityTypeID="@BCM.Security.Helper.CryptographyHelper.Encrypt(Convert.ToString((int)BCPEnum.EntityType.Application))" asp-route-strRecordID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.RecordID.ToString())" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.ProcessID.ToString())">
                    <i class="cv-page-name align-middle ViewBIA" type="button" title="View BIA"></i>
                </a>
                <div class="dropdown dropstart">
                    <span class="btn-action" role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false"><i class="cv-activity-details" title="View Details"></i></span>
                    <div class="dropdown-menu border-0">
                        @* <h6 class="dropdown-header fw-semibold text-dark pb-0">Ptech Pune LTD</h6> *@
                        <table class="table mb-0 table-borderless">
                            <tbody>
                                <tr>
                                    <td>
                                        <table class="table table-sm mb-0 table-borderless">
                                            <tbody>
                                                <tr>
                                                    <th class="fw-semibold text-primary" colspan="3">Application Details</th>
                                                </tr>
                                                <tr>
                                                    <td><i class="cv-process-code" title="Code"></i></td>
                                                    <td> : </td>
                                                    <td>
                                                        @if (string.IsNullOrEmpty(item.ProcessCode))
                                                        {
                                                            <span><i class="cv-na text-danger" title="Not Available"></i></span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-warning">  (@item.ProcessCode )</span>
                                                        }
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><i class="cv-version" title="Version"></i></td>
                                                    <td>:</td>
                                                    <td>
                                                        @if (string.IsNullOrEmpty(item.ProcessVersion) || item.ProcessVersion == "0")
                                                        {
                                                            <span><i class="cv-na text-danger" title="Not Available"></i></span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-warning">  (@item.ProcessVersion)</span>
                                                        }
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><i class="cv-bcm-scope" title="Is Under BCM Scope"></i></td>
                                                    <td>:</td>
                                                    <td>
                                                        @if (string.IsNullOrEmpty(item.ProcessCode))
                                                        {
                                                            <span class="text-success fw-semibold">
                                                                Yes
                                                            </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-danger fw-semibold">
                                                                No
                                                            </span>
                                                        }
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td>
                                        <table class="table table-sm mb-0 table-borderless">
                                            <tbody>
                                                <tr>
                                                    <th class="fw-semibold text-primary" colspan="3">Owner Details</th>
                                                </tr>
                                                <tr>
                                                    <td class="fw-semibold"><i class="cv-user"></i></td>
                                                    <td> : </td>
                                                    <td>
                                                        @{
                                                            if (string.IsNullOrEmpty(item.ProcessOwner))
                                                            {
                                                                <span><i class="cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                @item.ProcessOwner
                                                            }
                                                        }
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                    <td>:</td>
                                                    <td>
                                                        @{
                                                            if (string.IsNullOrEmpty(item.OwnerEmail))
                                                            {
                                                                <span><i class="cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                @item.OwnerEmail
                                                            }
                                                        }
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                    <td>:</td>
                                                    <td>
                                                        @{
                                                            if (string.IsNullOrEmpty(item.ProcessOwnerMobile) || item.ProcessOwnerMobile == "0")
                                                            {
                                                                <span><i class="cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                @item.ProcessOwnerMobile
                                                            }
                                                        }
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table class="table table-sm mb-0 table-borderless">
                                            <tbody>
                                                <tr>
                                                    <th class="fw-semibold text-primary" colspan="3">Approver Details</th>
                                                </tr>
                                                <tr>
                                                    <td class="fw-semibold"><i class="cv-user"></i></td>
                                                    <td> : </td>
                                                    <td>
                                                        @{
                                                            if (string.IsNullOrEmpty(item.ApproverName))
                                                            {
                                                                <span><i class="cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                @item.ApproverName
                                                            }
                                                        }
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                    <td>:</td>
                                                    <td>
                                                        @{
                                                            if (string.IsNullOrEmpty(item.ApproverEmail))
                                                            {
                                                                <span><i class="cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                @item.ApproverEmail
                                                            }
                                                        }
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                    <td>:</td>
                                                    <td>
                                                        @{
                                                            if (string.IsNullOrEmpty(item.ApproverMobile) || item.ApproverMobile == "0")
                                                            {
                                                                <span><i class="cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                @item.ApproverMobile
                                                            }
                                                        }
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td>
                                        <table class="table table-sm mb-0 table-borderless">
                                            <tbody>
                                                <tr>
                                                    <th class="fw-semibold text-primary" colspan="3">RTO/MAO/RPO</th>
                                                </tr>
                                                <tr title="RTO">
                                                    <td class="text-secondary"><i class="cv-RTO me-1"></i> RTO </td>
                                                    <td> : </td>
                                                    <td>
                                                        @{
                                                            if (item.OwnerRTO == "0" || string.IsNullOrEmpty(item.OwnerRTO))
                                                            {
                                                                <span class="text-danger"><i class="cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                var ownerRTO = @Utilities.GetFormattedRTO(Convert.ToInt32(item.OwnerRTO));
                                                                <sapn>@ownerRTO</sapn>
                                                            }
                                                        }
                                                    </td>
                                                </tr>
                                                <tr title="Calculated MAO">
                                                    <td class="text-secondary"><i class="cv-RTO me-1"></i>MAO </td>
                                                    <td>:</td>
                                                    <td>
                                                        @{
                                                            if (item.MTR == "0" || string.IsNullOrEmpty(item.MTR))
                                                            {
                                                                <span class="text-danger"><i class="cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                               var MTR =   @Utilities.GetFormattedRTO(Convert.ToInt32(item.MTR));
                                                               <span>@MTR</span>
                                                            }
                                                        }
                                                    </td>
                                                </tr>
                                                <tr title="RPO">
                                                    <td class="text-secondary"><i class="cv-rpo me-1"></i>RPO </td>
                                                    <td>:</td>
                                                    <td>
                                                        @{
                                                            if (item.RPO == "0" || string.IsNullOrEmpty(item.RPO))
                                                            {
                                                                <span class="text-danger"><i class="cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                               var RPO= @Utilities.GetFormattedRTO(Convert.ToInt32(item.RPO));
                                                               <span>@RPO</span>
                                                            }
                                                        }
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <span class="btn-action btnEdit" type="button" id="btnEdit" data-id="@item.RecordID"><i class="cv-edit me-1" title="Edit"></i></span>
                <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-id="@item.RecordID" data-Text="@item.ProcessName" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
            </div>
        </td>
    </tr>
    iIndex++;
    }
}
else
{
    <tr>
        <td colspan="10" class="text-center py-4">
            <div class="d-flex flex-column align-items-center">
                <img src="~/img/Isomatric/no_records_found.svg" alt="No Records Found" style="width: 120px; height: auto; margin-bottom: 1rem;">
            </div>
        </td>
    </tr>
}

@* @{
    int iIndex = 0;
    foreach (var item in Model)
    {
        iIndex++;
        <tr>
            <td>@iIndex</td>
            <td>
                <div class="d-grid">
                    <ul class="ps-0 mb-0">
                        <li class="list-group-item fw-semibold"><a class="text-primary" href="#" data-bs-toggle="modal" data-bs-target="#SummaryModal"> @item.ProcessCode</a></li>
                        <li class="list-group-item fw-semibold">@item.ProcessName</li>
                        <li class="list-group-item"><span class="text-info">Version</span> :  @item.ProcessVersion</li>

                        @if (Convert.ToString(@item.ProcessCode) == "" || Convert.ToString(@item.ProcessCode) == null)
                        {
                            <li class="list-group-item">Is Under BCM Scope: <span class="text-warning"> No</span></li>
                        }
                        else
                        {
                            <li class="list-group-item">Is Under BCM Scope: <span class="text-success">Yes</span></li>
                        }
                    </ul>
                </div>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr>
                            <td class="fw-semibold"><i class="cv-user"></i></td>
                            <td> : </td>
                            <td>@item.ProcessOwner</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                            <td>:</td>
                            <td><a class="text-primary" href="#">@item.OwnerEmail</a></td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                            <td>:</td>
                            <td>@item.ProcessOwnerMobile</td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr title="Organization">
                            <td class="fw-semibold"><i class="cv-company"></i></td>
                            <td> : </td>
                            <td>
                                @item.OrgName
                            </td>
                        </tr>
                        <tr title="Unit">
                            <td class="fw-semibold"><i class="cv-unit"></i> </td>
                            <td>:</td>
                            <td>@item.UnitName</td>
                        </tr>
                        <tr title="Department">
                            <td class="fw-semibold"><i class="cv-department"></i> </td>
                            <td>:</td>
                            <td>@item.DepartmentName</td>
                        </tr>
                        <tr title="Sub Department">
                            <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                            <td>:</td>
                            <td>@item.SubFunctionName</td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr>
                            <td><i class="cv-user me-1"></i>User RTO</td>
                            <td> : </td>
                            <td>@item.OwnerRTO</td>
                        </tr>
                        <tr>
                            <td><i class="cv-user me-1"></i>User MAO</td>
                            <td> : </td>
                            <td>@item.OwnerMTR</td>
                        </tr>
                        <tr>
                            <td><i class="cv-calculated me-1"></i>Calculated RTO</td>
                            <td> : </td>
                            <td>@item.RTO</td>
                        </tr>
                        <tr>
                            <td><i class="cv-calculated me-1"></i>Calculated MAO</td>
                            <td> : </td>
                            <td>@item.MTR</td>
                        </tr>
                        <tr>
                            <td><i class="cv-rpo me-1"></i>RPO</td>
                            <td> : </td>
                            <td>@item.RPO</td>
                        </tr>
                    </tbody>
                </table>
            </td>
            @if (Convert.ToString(@item.IsCritical) == "0")
            {
                <td class="text-success">
                    No
                </td>
            }
            else
            {
                <td class="text-danger">
                    Yes
                </td>
            }
            @if (Convert.ToString(@item.Status) == "0")
            {
                <td class="text-info">
                    <i class="cv-initiated me-1"></i>
                    Initiated
                </td>
            }
            @if (Convert.ToString(@item.Status) == "1")
            {
                <td class="text-warning">
                    <i class="cv-waiting me-1"></i>
                    WaitingForApproval
                </td>
            }
            @if (Convert.ToString(@item.Status) == "2")
            {
                <td class="text-success">
                    <i class="cv-success me-1"></i>
                    Approved
                </td>

            }
            @if (Convert.ToString(@item.Status) == "3")
            {
                <td class="text-danger">
                    <i class="cv-error me-1"></i>
                    DisApproved
                </td>
            }
            <td>
                <span class="btn-action"><i class="cv-view fs-6"></i></span>
            </td>
            <td>
                <span class="btn-action btnEdit" type="button" id="btnEdit" data-id="@item.RecordID"><i class="cv-edit me-1"></i></span>
                <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-id="@item.RecordID" data-Text="@item.ProcessName" data-bs-target="#DeleteModal"><i class="cv-delete text-danger"></i></span>
            </td>
        </tr>
    }
} *@