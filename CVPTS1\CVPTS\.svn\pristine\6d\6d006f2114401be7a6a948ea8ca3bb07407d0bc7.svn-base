﻿using BCM.UI.Areas.BCMReports.ReportModels.HRReport;
using BCM.UI.Controllers.PreBuildReport;
using DevExpress.Utils;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;

namespace BCM.UI.Areas.BCMReports.ReportTemplate;

public partial class HRReport : DevExpress.XtraReports.UI.XtraReport
{
    public static List<HRReportModel> HRReportModels = new List<HRReportModel>();
    public HRReport(string hrreportdata)
    {
        InitializeComponent();

        HRReportModels.Clear();

        if (!string.IsNullOrEmpty(hrreportdata))
        {
            var reportModels = JsonConvert.DeserializeObject<List<HRReportModel>>(hrreportdata);
            if (reportModels != null && reportModels.Count > 0)
            {
                _dayOne.Text = reportModels.FirstOrDefault(x => x.Time == "Day1")?.Value.ToString() ?? "0";
                _dayThree.Text = reportModels.FirstOrDefault(x => x.Time == "Day3")?.Value.ToString() ?? "0";
                _daySeven.Text = reportModels.FirstOrDefault(x => x.Time == "Day7")?.Value.ToString() ?? "0";
                _dayFourteen.Text = reportModels.FirstOrDefault(x => x.Time == "Day14")?.Value.ToString() ?? "0";
                _dayThirty.Text = reportModels.FirstOrDefault(x => x.Time == "Day30")?.Value.ToString() ?? "0";
                _dayBeyond.Text = reportModels.FirstOrDefault(x => x.Time == "Beyond")?.Value.ToString() ?? "0";

                HRReportModels = reportModels.DistinctBy(x => new { x.ASGName, x.DepartmentName, x.SubDepartmentName, x.ProcessName }).ToList();

                foreach (var items in HRReportModels)
                {
                    items.Day1 = Convert.ToInt32(_dayOne.Text);
                    items.Day3 = Convert.ToInt32(_dayThree.Text);
                    items.Day7 = Convert.ToInt32(_daySeven.Text);
                    items.Day14 = Convert.ToInt32(_dayFourteen.Text);
                    items.Day30 = Convert.ToInt32(_dayThirty.Text);
                    items.Beyond = Convert.ToInt32(_dayBeyond.Text);
                }
            }
        }
        this.DetailReport.DataSource = HRReportModels;

        lblUserName.Text = PreBuildReportController.ReportGeneratedBy;

        this.DisplayName = "HR Report_" + DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss tt");
    }
    private void HrReport_SplineChart_BeforePrint(object sender, EventArgs e)
    {
        try
        {
            Int64 lblDayOne = 0, lblDayThree = 0, lblDaySeven = 0, lblDayFourteen = 0, lblDayThirty = 0, lblDayBeyond = 0;

            lblDayOne = Convert.ToInt64(_dayOne.Text); lblDayThree = Convert.ToInt64(_dayThree.Text);
            lblDaySeven = Convert.ToInt64(_daySeven.Text); lblDayFourteen = Convert.ToInt64(_dayFourteen.Text);
            lblDayThirty = Convert.ToInt64(_dayThirty.Text); lblDayBeyond = Convert.ToInt64(_dayBeyond.Text);

            Series series = new Series("Progress", ViewType.Spline);
            _HrReport_Chart.Series.Clear();
            _HrReport_Chart.Series.Add(series);
            _HrReport_Chart.Borders = DevExpress.XtraPrinting.BorderSide.None;
            _HrReport_Chart.Legend.Visibility = DefaultBoolean.False;

            series.DataSource = CreateSplineChartData(lblDayOne, lblDayThree, lblDaySeven, lblDayFourteen, lblDayThirty, lblDayBeyond);
            series.ArgumentScaleType = ScaleType.Qualitative;
            series.ArgumentDataMember = "Days";
            series.ValueDataMembers.AddRange(new string[] { "Total" });
            series.LabelsVisibility = DefaultBoolean.False;

            SplineSeriesView view = (SplineSeriesView)series.View;
            view.LineStyle.Thickness = 1;
            view.MarkerVisibility = DefaultBoolean.True;
            view.LineMarkerOptions.Kind = MarkerKind.Circle;
            view.LineMarkerOptions.Size = 8;
            view.Color = Color.FromArgb(234, 234, 234);

            XYDiagram diagram = (XYDiagram)_HrReport_Chart.Diagram;
            diagram.Rotated = true;
            diagram.AxisY.Label.Angle = 0;
            diagram.AxisX.Label.TextColor = Color.FromArgb(26, 26, 26);
            diagram.AxisY.Label.TextColor = Color.FromArgb(26, 26, 26);

            diagram.AxisX.GridLines.Visible = false;
            diagram.AxisY.GridLines.Visible = false;
            diagram.DefaultPane.BorderVisible = false;
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    private DataTable CreateSplineChartData(Int64 lblDayOne, Int64 lblDayThree, Int64 lblDaySeven, Int64 lblDayFourteen, Int64 lblDayThirty, Int64 lblDayBeyond)
    {
        DataTable table = new DataTable("SplineChartData");

        table.Columns.Add("Days", typeof(string));
        table.Columns.Add("Total", typeof(Int64));

        table.Rows.Add("1", lblDayOne);
        table.Rows.Add("3", lblDayThree);
        table.Rows.Add("7", lblDaySeven);
        table.Rows.Add("14", lblDayFourteen);
        table.Rows.Add("30", lblDayThirty);
        table.Rows.Add("~30", lblDayBeyond);

        return table;
    }
    private void HrReport_SplineChart_Color_BeforePrint(object sender, CustomDrawSeriesPointEventArgs e)
    {
        switch (e.SeriesPoint.Argument)
        {
            case "1":
                e.SeriesDrawOptions.Color = Color.FromArgb(46, 134, 235);
                break;
            case "3":
                e.SeriesDrawOptions.Color = Color.FromArgb(0, 24, 143);
                break;
            case "7":
                e.SeriesDrawOptions.Color = Color.FromArgb(81, 212, 50);
                break;
            case "14":
                e.SeriesDrawOptions.Color = Color.FromArgb(249, 225, 6);
                break;
            case "30":
                e.SeriesDrawOptions.Color = Color.FromArgb(249, 138, 61);
                break;
            case "~30":
                e.SeriesDrawOptions.Color = Color.FromArgb(234, 47, 78);
                break;
        }
    }
}