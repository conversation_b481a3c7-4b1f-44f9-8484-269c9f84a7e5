﻿class WidgetManager {
    constructor() {
        this.widgetCounter = 0;
        this.selectedWidget = null;
        this.amCharts = {}; // Store amCharts instances
        this.intervals = {}; // Store intervals for dynamic widgets
        this.grid = null; // Gridstack instance
    }

    // Initialize Gridstack
    initializeGrid() {
        this.grid = GridStack.init({
            cellHeight: 70,
            margin: 8,
            minRow: 1,
            animate: true,
            float: false,
            removable: false,
            resizable: {
                handles: 'se'
            },
            draggable: {
                handle: '.widget-header'
            },
          
            marginUnit: 'px',
            column: 12,
            maxRow: 0, 
             disableOneColumnMode: false,
            oneColumnModeDomSort: true,          
            alwaysShowResizeHandle: false
        });

        this.setupGridEvents();
    }

    // Setup Gridstack events
    setupGridEvents() {
        this.grid.on('change', (event, items) => {
            // Auto-save after changes
            setTimeout(() => window.DashboardStorage.saveState(), 500);
        });

        this.grid.on('dragstart', (event, el) => {
            window.DashboardStorage.pushUndoState();
        });

        this.grid.on('dragstop', (event, el) => {
            // Reinitialize amCharts after drag
            const $widget = $(el).find('.dashboard-widget');
            const type = $widget.data('widget-type');
            if (type && type.startsWith('amchart-')) {
                setTimeout(() => {
                    this.reinitializeAmChart($widget, type);
                }, 200);
            }
        });
    }

    // Create a new widget
    createWidget(type, title, id, query, Xaxis, Yaxis,tableArray, gridOptions = null, dataIndex = null ) {
        const widgetId = id;
        const defaultGridOptions = gridOptions || this.getDefaultGridSize(type);

     

        // Save state for undo
        if (window.DashboardStorage && typeof window.DashboardStorage.pushUndoState === 'function') {
            window.DashboardStorage.pushUndoState();
        } else {
            console.warn('DashboardStorage.pushUndoState not available');
        }

        // Get widget content first
        const contentHtml = this.getWidgetContent(type, dataIndex);
      
        if (!contentHtml || contentHtml.trim() === '') {
            console.error('Widget content is empty for type:', type);
            return null;
        }

        const tableArrayAttr = Array.isArray(tableArray) && tableArray.length > 0
            ? `data-widgettableArray='${encodeURIComponent(JSON.stringify(tableArray))}'`
            : "";

        const queryAttr = query ? `data-widgetquery="${query}"` : "";
        const xAxisAttr = Xaxis ? `data-widgetXaxis="${Xaxis}"` : "";
        const yAxisAttr = Yaxis ? `data-widgetYaxis="${Yaxis}"` : "";
        const typeAttr = type ? `data-widget-type="${type}"` : "";

        const widgetContent = $(`
   
        <div class="dashboard-widget bcm-box card" ${tableArrayAttr}  ${queryAttr}  ${xAxisAttr}  ${yAxisAttr}  ${typeAttr}>
            <div class="card-header" style="border-bottom: none !important;">
                <div class="bcm-title" id="headerName">${title}</div>
            </div>
            <div class="card-body">
                <div id="BodyContainer">
                    ${contentHtml}
                </div>
            </div>
            <div class="widget-toolbar text-end p-2">
                <div class="btn-group btn-group-sm" role="group">                
                    <button type="button" class="btn btn-outline-danger widget-delete" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
  
`);


        // Add widget to grid
        const gridItem = this.grid.addWidget(widgetContent[0], {
            id: widgetId,
            query: query,
            x: defaultGridOptions.x,
            y: defaultGridOptions.y,
            w: defaultGridOptions.w,
            h: defaultGridOptions.h
        });

        const $gridItem = $(gridItem);
        let $widget = $gridItem.find('.dashboard-widget');

        if ($widget.length === 0) {
            $widget = $gridItem.children('.dashboard-widget');
        }

        if ($widget.length === 0) {
            $widget = $gridItem.hasClass('dashboard-widget') ? $gridItem : $gridItem.find('[data-widget-type]');
        }

        if (type.startsWith('amchart-')) {
            // Use requestAnimationFrame to ensure DOM is fully rendered
            requestAnimationFrame(() => {
                setTimeout(() => {
                    if ($widget.length > 0) {
                        this.initializeWidget($widget, type, dataIndex);
                    } else {
                        console.error('Widget element not found for initialization');
                    }
                }, 100);
            });
        } else {
            // Initialize other widgets immediately
            if ($widget.length > 0) {
                this.initializeWidget($widget, type, dataIndex);
            } else {
                console.error('Widget element not found for initialization');
            }
        }

        console.log('=== CREATE WIDGET END ===');
        console.log('Returning widget:', $widget.length > 0 ? $widget[0] : 'null');
        return $widget;
    }

    // Get default grid size for widget type
    getDefaultGridSize(type) {
        const sizes = {
            'kpi': { x: 0, y: 0, w: 3, h: 3 },
            'table': { x: 0, y: 0, w: 8, h: 4 },
            'text': { x: 0, y: 0, w: 6, h: 3 },
            'progress': { x: 0, y: 0, w: 4, h: 4 },
            'amchart-line': { x: 0, y: 0, w: 6, h: 4 },
            'amchart-column': { x: 0, y: 0, w: 6, h: 4 },
            'amchart-pie': { x: 0, y: 0, w: 4, h: 4 },
            'amchart-radar': { x: 0, y: 0, w: 4, h: 4 },
            'amchart-area': { x: 0, y: 0, w: 6, h: 4 },
            'amchart-scatter': { x: 0, y: 0, w: 5, h: 4 },
            'weather': { x: 0, y: 0, w: 4, h: 5 },
            'calendar': { x: 0, y: 0, w: 5, h: 6 },
            'clock': { x: 0, y: 0, w: 3, h: 3 },
            'gauge': { x: 0, y: 0, w: 4, h: 4 },
            'map': { x: 0, y: 0, w: 6, h: 5 },
            'news': { x: 0, y: 0, w: 5, h: 6 },
            'social': { x: 0, y: 0, w: 4, h: 5 }
        };
        return sizes[type] || { x: 0, y: 0, w: 4, h: 3 };
    }

    // Get widget content based on type
    getWidgetContent(type, dataIndex) {
        console.log('Creating widget content for type:', type, 'dataIndex:', dataIndex);
        let content = '';
        switch (type) {
            case 'table':
                content = this.createTableContent(queryData, tableArray);
                break;
            case 'amchart-line':
            case 'amchart-column':
            case 'amchart-pie':
            case 'amchart-radar':
            case 'amchart-area':
            case 'amchart-scatter':
                content = this.createAmChartContent(type);
                break;            
            default:
                content = '<p>Unknown widget type: ' + type + '</p>';
                break;
        }

        console.log('Generated content length:', content.length);
        return content;
    }

    

 createTableContent(data,tableArrays) {
    if (!data || data.length === 0) {
        return `<div class="text-center p-3 text-muted">No data available.</div>`;
    }
     const newTableResult = data[0].map(item => {
         const newObj = {};
         tableArrays.forEach(map => {
             newObj[map.name] = item[map.value];
         });
         return newObj;
     });

    let tableHTML = `
        <div class="table-widget">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
    `;

    // Create dynamic headers from first row's keys
     Object.keys(newTableResult[0]).forEach(key => {
        tableHTML += `<th>${key}</th>`;
    });
    tableHTML += `
                        </tr>
                    </thead>
                    <tbody>
    `;
     newTableResult.forEach(row => {
        tableHTML += `<tr>`;
        Object.values(row).forEach(value => {
            tableHTML += `<td>${value}</td>`;
        });
        tableHTML += `</tr>`;
    });

    tableHTML += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    return tableHTML;
}

    createAmChartContent(type) {
        const chartId = `amchart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const content = `<div class="amchart-container" id="${chartId}" style="width: 100%; height: 100%; min-height: 250px;"></div>`;
        console.log('Created amChart content:', content);
        return content;
    }

    // Initialize widget functionality
    initializeWidget($widget, type, dataIndex) {
        const widgetId = $widget.closest('.grid-stack-item').attr('gs-id') || $widget.attr('id');

        if (type.startsWith('amchart-')) {

            this.waitForWidgetToBeReady($widget, type, dataIndex);
        } 

        $widget.on('click', (e) => {
            e.stopPropagation();
            this.selectWidget(widgetId);
        });

        
        $widget.find('.widget-delete').on('click', (e) => {
            e.stopPropagation();
            this.deleteWidget(widgetId);
        });
    }

    // Wait for widget to be ready for chart initialization (legacy method)
    waitForWidgetReady($widget, callback, maxAttempts = 20) {
        let attempts = 0;

        const checkReady = () => {
            attempts++;
            const container = $widget.find('.amchart-container')[0];

            if (!container) {
                console.error('amChart container not found');
                return;
            }

            // Check if container is visible and has dimensions
            const isVisible = container.offsetWidth > 0 && container.offsetHeight > 0;
            const isInDOM = document.contains(container);

            console.log(`Attempt ${attempts}: Container visible: ${isVisible}, In DOM: ${isInDOM}, Dimensions: ${container.offsetWidth}x${container.offsetHeight}`);

            if (isVisible && isInDOM) {
                console.log('Widget ready, initializing chart...');
                callback();
            } else if (attempts < maxAttempts) {
                setTimeout(checkReady, 100);
            } else {
                console.error('Widget failed to become ready after', maxAttempts, 'attempts');
                // Try to force initialization anyway
                callback();
            }
        };

        // Start checking after a small delay
        setTimeout(checkReady, 50);
    }

    // Wait for dynamically added widget to be ready
    waitForWidgetToBeReady($widget, type, dataIndex, maxAttempts = 30) {
        let attempts = 0;

        const checkAndInitialize = () => {
            attempts++;
            console.log(`Checking widget readiness, attempt ${attempts}...`);

            // Check if widget exists and is in DOM
            if (!$widget || $widget.length === 0) {
                console.error('Widget element not found');
                return;
            }

            const widgetElement = $widget[0];
            if (!widgetElement) {
                console.error('Widget DOM element is null');
                return;
            }

            const isInDOM = document.contains(widgetElement);
            const hasSize = widgetElement.offsetWidth > 0 && widgetElement.offsetHeight > 0;

            console.log(`Widget in DOM: ${isInDOM}, Has size: ${hasSize} (${widgetElement.offsetWidth}x${widgetElement.offsetHeight})`);

            // Check if container exists
            let $container = $widget.find('.amchart-container');
            if ($container.length === 0) {
                console.warn('amChart container missing, recreating...');
                const amChartContent = this.createAmChartContent(type);
                $widget.find('.widget-content').html(amChartContent);
                $container = $widget.find('.amchart-container');
            }

            const containerElement = $container[0];
            const containerHasSize = containerElement && containerElement.offsetWidth > 0 && containerElement.offsetHeight > 0;

            console.log(`Container exists: ${!!containerElement}, Container has size: ${containerHasSize}`);

            if (isInDOM && hasSize && containerElement && containerHasSize) {
                console.log('Widget is ready, initializing chart...');
                this.initializeAmChart($widget, type, dataIndex);
            } else if (attempts < maxAttempts) {
                setTimeout(checkAndInitialize, 100);
            } else {
                console.error('Widget failed to become ready after', maxAttempts, 'attempts, forcing initialization...');
                // Force initialization anyway
                this.initializeAmChart($widget, type, dataIndex);
            }
        };


        checkAndInitialize();
    }



    // Initialize amChart
    initializeAmChart($widget, type, dataIndex, retryCount = 0) {
       
        let $container = $widget.find('.amchart-container');    

        this.continueAmChartInit($widget, type, dataIndex, retryCount, $container[0]);
    }

    continueAmChartInit($widget, type, dataIndex, retryCount, container) {

        if (this.amCharts[container.id]) {
            console.log('Disposing existing chart:', container.id);
            this.amCharts[container.id].root.dispose();
            delete this.amCharts[container.id];
        }

        const chartType = type.replace('amchart-', '');

        try {
            // Ensure container has dimensions
            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn('Container has no dimensions, setting defaults and forcing layout');
                container.style.width = '100%';
                container.style.height = '300px';
                container.style.minHeight = '250px';
                container.style.display = 'block';

                // Force a reflow
                container.offsetHeight;

                // Wait a bit more for layout to settle
                setTimeout(() => {
                    this.initializeAmChart($widget, type, dataIndex, retryCount + 1);
                }, 200);
                return;
            }


      
            switch (chartType) {
                case 'line':
                    chart = renderChart(chartType, container.id, queryData[0], widgettitle,Xaxis, Yaxis);
                    break;
                case 'column':
                    chart = this.createAmColumnChart(root);
                    break;
                case 'pie':
                    chart = renderChart(chartType, container.id, queryData[0], widgettitle, Xaxis, Yaxis);
                    break;
                case 'radar':
                    chart = this.createAmRadarChart(root);
                    break;
                case 'area':
                    chart = this.createAmAreaChart(root);
                    break;
                case 'scatter':
                    chart = this.createAmScatterChart(root);
                    break;
                default:
                    console.error('Unknown chart type:', chartType);
                    return;
            }

            this.amCharts[container.id] = { root, chart, type: chartType };
            console.log('amChart initialized successfully:', container.id);

        } catch (error) {
                              
        }
    }

    
    initializeAllAmCharts() {
        $('.dashboard-widget[data-widget-type^="amchart-"]').each((index, element) => {
            const $widget = $(element);
            const type = $widget.data('widget-type');
            setTimeout(() => {
                this.initializeAmChart($widget, type, null);
            }, index * 200); // Stagger initialization
        });
    }


    // Reinitialize amChart after drag (fixes drag and drop issue)
    reinitializeAmChart($widget, type) {
        const container = $widget.find('.amchart-container')[0];
        if (!container) return;

        setTimeout(() => {
            this.initializeAmChart($widget, type, null);
        }, 100);
    }

    // Select widget
    selectWidget(widgetId) {
        $('.dashboard-widget').removeClass('selected');
        const $gridItem = $(`.grid-stack-item[gs-id="${widgetId}"]`);
        $gridItem.find('.dashboard-widget').addClass('selected');
        this.selectedWidget = widgetId;
    }



    deleteWidget(widgetId) {
        if (confirm('Are you sure you want to delete this widget?')) {
            window.DashboardStorage.pushUndoState();

            const $gridItem = $(`.grid-stack-item[gs-id="${widgetId}"]`);
            const $widget = $gridItem.find('.dashboard-widget');



            // Cleanup amCharts
            const amChartContainer = $widget.find('.amchart-container')[0];
            if (amChartContainer && this.amCharts[amChartContainer.id]) {
                this.amCharts[amChartContainer.id].root.dispose();
                delete this.amCharts[amChartContainer.id];
            }

            // Cleanup intervals (for clock widgets)
            const clockElement = $widget.find('[id^="clock-time-"]');
            if (clockElement.length > 0) {
                const clockId = clockElement.attr('id');
                if (this.intervals[clockId]) {
                    clearInterval(this.intervals[clockId]);
                    delete this.intervals[clockId];
                }
            }

            // Remove from grid
            this.grid.removeWidget($gridItem[0]);
            window.DashboardStorage.showNotification('Widget deleted', 'success');
        }
    }


    // Clear all widgets
    clearDashboard() {
        if (confirm('Are you sure you want to clear all widgets?')) {
            window.DashboardStorage.pushUndoState();

            // Destroy all amCharts
            Object.values(this.amCharts).forEach(chartData => {
                if (chartData.root) {
                    chartData.root.dispose();
                }
            });
            this.amCharts = {};

            // Clear all intervals
            Object.values(this.intervals).forEach(interval => clearInterval(interval));
            this.intervals = {};

            // Clear grid
            this.grid.removeAll();

            window.DashboardStorage.showNotification('Dashboard cleared', 'success');
        }
    }
}


// Initialize widget manager
window.WidgetManager = new WidgetManager();


