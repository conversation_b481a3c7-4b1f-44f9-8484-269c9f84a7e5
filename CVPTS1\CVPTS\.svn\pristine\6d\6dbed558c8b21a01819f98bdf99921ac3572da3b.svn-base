﻿@{
    ViewBag.Title = "RssFeedMaster";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .rss_feed_master .card {
        /* background-image: radial-gradient(at top left,#629a92 36%,#02d2a0 67%, #fff 11%); */
        background-image: radial-gradient(ellipse farthest-corner at 0 140%, #F8F8F8 0%, #F8F8F8 70%, #FDFDFD 70%);
        border-radius: 10px;
        width: 100%;
        height: 100%;
    }

        .rss_feed_master .card .card-header {
            background-color: transparent;
        }
</style>

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Rss Feed Master</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

      
        <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"> <i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant  border-0">
    <div class="card-body rss_feed_master">
        <div class="row g-4">
            <div class="col-4 d-grid">
                <div class="card shadow-sm">
                    <div class="card-header bg-transparent border-0 d-flex align-items-center justify-content-between">
                        <span>
                            <img src="/img/rss_feed_master/company_logo/bbc_news.png" class="img-fluid" />
                        </span> 
                        <span>
                                <span type="button"><i class="cv-edit" title="Edit"></i></span>
                                <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                            
                        </span>
                    </div>
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <div>
                            <p class="mb-0 fw-semibold">BBC NEWS - Americas : World Edition</p>
                            <span><span class="fw-semibold me-1">URL:</span><a href="#">https://www.bbc.com/news/live/world-us-canada-69069142</a></span>
                        </div>
                        <div>
                            <img src="/img/rss_feed_master/status_ok.png" class="img-fluid" />
                        </div>
                       
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card shadow-sm">
                    <div class="card-header border-0 d-flex align-items-center justify-content-between">
                        <span>
                            <img src="/img/rss_feed_master/company_logo/simple_cast.png" class="img-fluid" />
                        </span>
                        <span>
                            <span type="button"><i class="cv-edit" title="Edit"></i></span>
                            <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                        </span>
                       
                    </div>
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <div>
                        <p class="mb-0 fw-semibold">Simplecast.com</p>
                        <span><span class="fw-semibold me-1">URL:</span><a href="#">https://feeds.simplecast.com/54nAGcll</a></span>
                        </div>
                        <div>
                            <span>
                                <img src="/img/rss_feed_master/status_ok.png" class="img-fluid" />
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card shadow-sm">
                    <div class="card-header border-0 d-flex align-items-center justify-content-between">
                        <span>
                            <img src="/img/rss_feed_master/company_logo/spotify.png" class="img-fluid" />
                        </span> <span>
                            <span type="button"><i class="cv-edit" title="Edit"></i></span>
                            <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span> 
                        </span>
                    </div>
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <div>
                            <p class="mb-0 fw-semibold">Spotify - Podcast list</p>
                            <span><span class="fw-semibold me-1">URL:</span><a href="#">https://www.https//open.spotify.com/</a></span>
                        </div>
                       <div>
                            <img src="/img/rss_feed_master/status_no.png" class="img-fluid" />
                       </div>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card shadow-sm">
                    <div class="card-header border-0 d-flex align-items-center justify-content-between">
                        <span>
                            <img src="/img/rss_feed_master/company_logo/amazon.png" class="img-fluid" />
                        </span> <span>
                            <span type="button"><i class="cv-edit" title="Edit"></i></span>
                            <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                           
                        </span>
                    </div>
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <div>
                            <p class="mb-0 fw-semibold">Amazon - India Edition</p>
                            <span><span class="fw-semibold me-1">URL:</span><a href="#">https://www.amazon.in/s?srs=17478432031</a></span>
                        </div>
                        <div> <img src="/img/rss_feed_master/status_ok.png" class="img-fluid" /></div>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card shadow-sm">
                    <div class="card-header border-0 d-flex align-items-center justify-content-between">
                        <span>
                            <img src="/img/rss_feed_master/company_logo/intel.png" class="img-fluid" />
                        </span> <span>
                            <span type="button"><i class="cv-edit" title="Edit"></i></span>
                            <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                            
                        </span>
                    </div>
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <div>
                            <p class="mb-0 fw-semibold">Intel Computex 2024</p>
                            <span>
                                <span class="fw-semibold me-1">URL:</span><a href="#">
                                    https://www.intel.com/us/en/newsroom/news/computex-2024-keynote-gs.ag2a89
                                </a>
                            </span>
                        </div>
                        <div><img src="/img/rss_feed_master/status_ok.png" class="img-fluid" /></div>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card shadow-sm">
                    <div class="card-header border-0 d-flex align-items-center justify-content-between">
                        <span>
                            <img src="/img/rss_feed_master/company_logo/simple_cast.png" class="img-fluid" />
                        </span> <span>
                            <span type="button"><i class="cv-edit" title="Edit"></i></span>
                            <span type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                           
                        </span>
                    </div>
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <div>
                            <p class="mb-0 fw-semibold">Simplecast.com</p>
                            <span><span class="fw-semibold me-1">URL:</span><a href="#">https://feeds.simplecast.com/54nAGcll</a></span>
                        </div>
                        <div> <img src="/img/rss_feed_master/status_ok.png" class="img-fluid" /></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Rss Feed Master Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-6">
                        <div class="form-group">
                            <label class="form-label">Organization</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-organization"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Natural Disaster</option>
                                    <option value="value">System Failure</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group w-100">
                            <label class="form-label">Is Active</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-parameter"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Yes</option>
                                    <option value="value">No</option>
                                </select>
                            </div>
                        </div>

                    </div>
                    <div class="col-6">
                        <div class="form-group w-100">
                            <label class="form-label">Title</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Select Title</option>
                                    <option value="value">Eng</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group w-100">
                            <label class="form-label">Upload Logo</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-upload"></i></span>
                                <input type="file" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Feed URL</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-url"></i></span>
                            <input class="form-control" type="text" />
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-between">
                        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                        <div>
                            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary btn-sm">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--End Configuration Modal -->

</div>
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/delete.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->