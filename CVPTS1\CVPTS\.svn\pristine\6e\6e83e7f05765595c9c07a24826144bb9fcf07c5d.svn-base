﻿@model IEnumerable<BCM.BusinessClasses.BCMTrainingMaster>
@{
    ViewData["Title"] = "ManageBCMTrainingForm";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int iIndex = 1;
}

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header ">

    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">Manage BCM Training</h6>
        <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
            <!-- Filter Dropdown -->
            <div class="dropdown">
                <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown"
                    aria-expanded="false" data-bs-auto-close="outside">
                    <i class="cv-filter align-middle" title="View Filter"></i>
                </button>
                <form class="dropdown-menu p-3 border-0" style="width:18rem;">
                    <div class="mb-3">
                        <label class="form-label">Organizations</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                            <select class="form-select form-control selectized" autocomplete="off" id="ddlOrganization"
                                aria-label="Default select example"
                                asp-items="@(new SelectList(ViewBag.OrgInfo, "Value", "Text"))">
                                <option selected value="0">-- All Organizations --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Units</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                            <select class="form-select form-control selectized" autocomplete="off" id="ddlUnit"
                                aria-label="Default select example">
                                <option selected value="0">-- All Units --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-department"></i></span>
                            <select class="form-select form-control selectized" autocomplete="off" id="ddlDepartment"
                                aria-label="Default select example">
                                <option selected value="0">-- All Departments --</option>
                            </select>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary flex-fill"
                            onclick="clearFilters()">
                            <i class="cv-refresh"></i> Clear
                        </button>
                        <button type="button" class="btn btn-sm btn-primary flex-fill" onclick="applyFilters()">
                            <i class="cv-check"></i> Apply
                        </button>
                    </div>
                </form>
            </div>

            <div class="input-group" style="width: 250px;">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control form-control-sm"
                    placeholder="Search trainings...">
            </div>

            <a class="btn btn-sm btn-primary" asp-action="BCMTrainingMasterConfiguration"
                asp-controller="BCMTrainingMasterConfiguration"
                asp-route-iID="@BCM.Security.Helper.CryptographyHelper.Encrypt("0")">
                <i class="cv-plus me-1"></i>Create
            </a>
            @*  <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="Modal"><i class="cv-Plus" title="Create New"></i>Create</button> *@
        </div>
    </div>

</div>

@* Display delete success/error messages *@
@if (TempData["DeleteMessage"] != null)
{
    <div class="alert @(TempData["DeleteSuccess"] != null && (bool)TempData["DeleteSuccess"] ? "alert-success" : "alert-danger") alert-dismissible fade show"
        role="alert">
        <i
            class="@(TempData["DeleteSuccess"] != null && (bool)TempData["DeleteSuccess"] ? "cv-check-circle" : "cv-exclamation-triangle") me-2"></i>
        @TempData["DeleteMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Training Name</th>
                <th>Owner Details</th>
                <th>Approver Details</th>
                <th>Organization</th>
                <th>Unit</th>
                <th>Department</th>
                <th>Status</th>
                <th class="text-end">Action</th>
            </tr>
        </thead>
        <tbody id="tblBody">
            @if (Model != null)
            {
                @foreach (var objBCMTrainingMaster in Model)
                {
                    <tr>
                        <td>
                            @iIndex
                        </td>
                        <td>
                            <div class="d-grid">
                                @* <span class="fw-semibold text-warning">@objBCMTrainingMaster.TrainingCode</span> *@
                                <input type="hidden" id="TrainingID" asp-for="@objBCMTrainingMaster.ID" />
                                <span class="fw-semibold">@objBCMTrainingMaster.TrainingName ( @objBCMTrainingMaster.Version
                                    )</span>
                                @* <span>Version  : <span class="text-info"> @objBCMTrainingMaster.Version</span></span> *@
                            </div>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    <tr>
                                        <td>@objBCMTrainingMaster.OwnerName</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    <tr>
                                        <td>@objBCMTrainingMaster.ApproverName</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>

                        <td>
                            @objBCMTrainingMaster.OrgName
                        </td>
                        <td>
                            @objBCMTrainingMaster.UnitName
                        </td>
                        <td>
                            @objBCMTrainingMaster.DepartmentName
                        </td>
                        <td>
                            @{
                                int statusId = Convert.ToInt32(@objBCMTrainingMaster.Status);
                            }
                            <span class="d-flex align-items-center @BCM.Shared.Utilities.ApprovalStatusWiseTextClass(statusId)">
                                <i class="@BCM.Shared.Utilities.ApprovalStatusWiseClass(statusId) me-2"></i>
                                @BCM.Shared.Utilities.ApprovalStatus(statusId)
                            </span>

                        </td>
                        <td class="text-end">
                            @if (@objBCMTrainingMaster.Status == 2)
                            {
                                <span class="btn-action publish" type="button" id="imgbuttonPublish" data-bs-toggle="modal"
                                    data-bs-target="Modal" data-id="@objBCMTrainingMaster.ID">
                                    <i class="cv-page-name align-middle ViewBIA" title="Publish"></i>
                                </span>
                            }

                            <a class="text-dark" asp-action="BCMTrainingMasterConfiguration"
                                asp-controller="BCMTrainingMasterConfiguration"
                                asp-route-iID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBCMTrainingMaster.ID.ToString())">
                                <i class="cv-edit" title="Edit"></i>
                            </a>
                            @* <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@objBCMTrainingMaster.ID"><i class="cv-edit" title="Edit"></i></span>
                             *@<span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="Modal"
                                data-id="@objBCMTrainingMaster.ID"><i class="cv-delete text-danger" title="Delete"></i></span>
                        </td>
                    </tr>
                    iIndex++;
                }
            }
        </tbody>
    </table>
</div>

<div class="modal fade" id="Modal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle"></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0">

                <table class="w-100">
                    <tbody>
                        <tr>
                            <th>
                                Publish Date :

                            </th>
                            <td>
                                <div class="form-group">
                                    <div class="input-group">
                                        <input type="date" class="form-control Publishdate" id="Publishdate" required />
                                    </div>
                                    <div class="invalid-feedback">Select Date</div>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <th>
                                Publish For Vendor :
                            </th>
                            <td>
                                <div class="form-group">
                                    <div>
                                        <input class="form-check-input chkVendor" type="checkbox" name="chkVendor" id="chkVendor">
                                    </div>
                                    <div class="invalid-feedback">check</div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <span class="fst-italic d-flex align-items-center text-secondary"><i
                        class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                <div>
                    @* <button type="button" class="btn btn-secondary btn-sm me-1" >Close</button> *@
                    <button type="button" class="btn btn-sm btn-primary" id="btnPublish">Publish</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <style>
        /* Modal adjustments for date picker */
        #Modal .modal-dialog {
            overflow: visible !important;
            margin: 1.75rem auto;
            min-height: calc(100vh - 3.5rem);
            display: flex;
            align-items: flex-start;
        }

        #Modal .modal-content {
            overflow: visible !important;
            width: 100%;
            min-height: auto;
            flex: none;
        }

        #Modal .modal-body {
            overflow: visible !important;
            padding-bottom: 250px;
            /* Extra space for calendar */
        }

        /* Date input styling */
        #Modal .modal-body input[type="date"] {
            z-index: 1060;
            position: relative;
        }

        /* Ensure modal can expand when calendar opens */
        #Modal.modal.show {
            overflow-y: auto;
            padding-right: 0 !important;
        }

        #Modal .modal-dialog.modal-dialog-scrollable {
            max-height: none !important;
        }

        #Modal .modal-dialog.modal-dialog-scrollable .modal-content {
            max-height: none !important;
            overflow: visible !important;
        }

        #Modal .modal-dialog.modal-dialog-scrollable .modal-body {
            overflow: visible !important;
            max-height: none !important;
        }

        /* Responsive adjustments */
    </style>
    <script>
        var currentTrainingId = null; // Store the current training ID - global scope

        $(document).ready(function () {

            // Handle publish button click from table rows
            $(document).on('click', '#btnPublish', function () {
                currentTrainingId = $(this).data('id');
                console.log('Publish clicked for training ID:', currentTrainingId);

                // Set default date to today
                var today = new Date().toISOString().split('T')[0];
                $('#Publishdate').val(today);

                // Clear vendor checkbox
                $('#chkVendor').prop('checked', false);

                // Set modal title
                $('#modaltitle').text('Publish Training');
            });

            // Handle specific imgbuttonPublish click to load existing data
            $(document).on('click', '#imgbuttonPublish', function (e) {
                e.preventDefault();
                e.stopPropagation();

                currentTrainingId = $(this).data('id');
                console.log('imgbuttonPublish clicked for training ID:', currentTrainingId);

                // Set modal title
                $('#modaltitle').text('Publish Training');

                // Show loading state in modal
                $('#Publishdate').val('');
                $('#chkVendor').prop('checked', false);

                // Call controller method to get existing publish data
                $.ajax({
                    url: '@Url.Action("GetPublishMasterByTrainingID", "ManageBCMTrainingForm")',
                    type: 'GET',
                    data: { trainingId: currentTrainingId },
                    success: function (response) {

                        alert(response.data.publishDate);
                        console.log('Publish data response:', response);
                        console.log('Response data object:', response.data);
                        
                        if (response.success && response.data) {
                            // Debug: Check if elements exist
                            console.log('Publishdate element exists:', $('.Publishdate').length > 0);
                            console.log('chkVendor element exists:', $('.chkVendor').length > 0);

                            // Debug: Log the values being set
                            console.log('Setting PublishDate to:', response.data.publishDate);
                            console.log('Setting IsVendor to:', response.data.isVendor);

                            // Populate modal with existing data
                            $('#Publishdate').val(response.data.publishDate);
                            $('#chkVendor').prop('checked', response.data.isVendor);

                            // Debug: Verify values were set
                            console.log('Publishdate value after setting:', $('.Publishdate').val());
                            console.log('chkVendor checked after setting:', $('.chkVendor').is(':checked'));

                            console.log('Modal populated with existing data:', response.data);
                        } else {
                            // Set default values if no existing data
                            var today = new Date().toISOString().split('T')[0];
                            $('#Publishdate').val(today);
                            $('#chkVendor').prop('checked', false);

                            console.log('No existing data found, using defaults');
                            console.log('Response was:', response);
                        }

                        // Show the modal
                        $('#Modal').modal('show');
                    },
                    error: function (xhr, status, error) {
                        console.error('Error loading publish data:', error);

                        // Set default values on error
                        var today = new Date().toISOString().split('T')[0];
                        $('#Publishdate').val(today);
                        $('#chkVendor').prop('checked', false);

                        // Show the modal anyway
                        $('#Modal').modal('show');

                        alert('Could not load existing publish data. Using default values.');
                    }
                });
            });

            // Handle publish button click in modal
            $('#btnPublish').click(function (e) {
                e.preventDefault();

                // Get form values
                var publishDate = $('#Publishdate').val();
                var isVendor = $('#chkVendor').is(':checked');

                // Validate required fields
                if (!publishDate) {
                    alert('Please select a publish date.');
                    $('#Publishdate').focus();
                    return false;
                }

                if (!currentTrainingId) {
                    alert('Training ID is missing. Please try again.');
                    return false;
                }

                // Prepare data for submission
                var publishData = {
                    iId: currentTrainingId,
                    date: publishDate,
                    chkVendor: isVendor
                };

                console.log('Publishing training with data:', publishData);

                // Show loading state
                var $btn = $(this);
                var originalText = $btn.text();
                $btn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i>Publishing...');

                // Make AJAX call to publish training
                $.ajax({
                    url: '@Url.Action("PublishClick", "ManageBCMTrainingForm")',
                    type: 'POST',
                    data: publishData,
                    success: function (response) {
                        console.log('Publish response:', response);

                        if (response.success) {
                            // Show success message
                            alert('Training published successfully!');

                            // Close modal
                            $('#Modal').modal('hide');

                            // Reload page to reflect changes
                            location.reload();
                        } else {
                            // Show error message
                            alert(response.message || 'Failed to publish training. Please try again.');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Error publishing training:', error);
                        alert('An error occurred while publishing the training. Please try again.');
                    },
                    complete: function () {
                        // Reset button state
                        $btn.prop('disabled', false).text(originalText);
                    }
                });

                return false; // Prevent default form submission
            });

            $('#btnCreate').click(function () {
                $.get('@Url.Action("AddBCMTrainingMaster", "ManageBCMTrainingForm")', function (data) {
                    $('.modal-body').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('BCM Training Program Configuration');
                });
            });

            // $(document).on('click', '#btnPublish', function () {
            //     var iId = $('#TrainingID').value;
            //     alert(iId);

            //     var date = $('#Publishdate').value;
            //     $.get('@Url.Action("publishClick", "ManageBCMTrainingForm")', { iId: iId, date: date }, function (data) {
            //     });
            // })

            $(document).on('click', '.btnEdit', function () {
                var iId = $(this).data('id');
                $.get('@Url.Action("EditBCMTrainingMaster", "ManageBCMTrainingForm")', { iId: iId }, function (data) {
                    $('.modal-body').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Update BCM Training Program');
                });
            })

            $(document).on('click', '.btnDelete', function () {
                var iId = $(this).data('id');
                $.get('@Url.Action("DeleteBCMTrainingMaster", "ManageBCMTrainingForm")', { iId: iId }, function (data) {
                    $('#deleteBody').html(data);
                    $('#DeleteModal').modal('show');
                    $('#modaltitle').text('Delete BCM Training Program');
                });
            })

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        location.reload();
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

            // Initialize tooltips on page load
            $('[title]').tooltip();

            // Initialize cascading dropdowns based on ManageBusinessProcess pattern
            setupCascadingDropdowns();

            // Initialize event handlers for existing content
            reinitializeEventHandlers();
        });

        // Setup cascading dropdowns based on ManageBusinessProcess pattern
        function setupCascadingDropdowns() {
            console.log('Setting up cascading dropdowns');

            // Organization dropdown change handler
            $('#ddlOrganization').change(function () {
                var ddlOrganizationVal = $(this).val();
                console.log('Organization changed to:', ddlOrganizationVal);

                // Clear dependent dropdowns
                if ($('#ddlUnit')[0] && $('#ddlUnit')[0].selectize) {
                    let unitSelectize = $('#ddlUnit')[0].selectize;
                    unitSelectize.clear();
                    unitSelectize.clearOptions();
                    unitSelectize.addOption({ value: "0", text: "-- All Units --" });
                    unitSelectize.setValue("0");
                }

                if ($('#ddlDepartment')[0] && $('#ddlDepartment')[0].selectize) {
                    let deptSelectize = $('#ddlDepartment')[0].selectize;
                    deptSelectize.clear();
                    deptSelectize.clearOptions();
                    deptSelectize.addOption({ value: "0", text: "-- All Departments --" });
                    deptSelectize.setValue("0");
                }

                // Load units if organization is selected
                if (ddlOrganizationVal && ddlOrganizationVal != "0") {
                    $.ajax({
                        url: '@Url.Action("GetAllUnits", "ManageBCMTrainingForm")',
                        type: 'GET',
                        data: { iOrgID: ddlOrganizationVal },
                        success: function (response) {
                            console.log('Units loaded for org:', ddlOrganizationVal, response);
                            let selectizeInstance = $('#ddlUnit')[0].selectize;
                            selectizeInstance.clear();
                            selectizeInstance.clearOptions();
                            selectizeInstance.addOption({ value: "0", text: "-- All Units --" });
                            selectizeInstance.setValue("0");

                            response && response.forEach(({ unitID, unitName }) => {
                                if (unitID && unitName) {
                                    selectizeInstance.addOption({ value: unitID, text: unitName });
                                }
                            });
                        },
                        error: function (xhr, status, error) {
                            console.error('Error loading units:', error);
                        }
                    });
                }

                // Apply filters after organization change
                applyFilters();
            });

            // Unit dropdown change handler
            $('#ddlUnit').change(function () {
                var ddlUnitVal = $(this).val();
                console.log('Unit changed to:', ddlUnitVal);

                // Clear department dropdown
                if ($('#ddlDepartment')[0] && $('#ddlDepartment')[0].selectize) {
                    let deptSelectize = $('#ddlDepartment')[0].selectize;
                    deptSelectize.clear();
                    deptSelectize.clearOptions();
                    deptSelectize.addOption({ value: "0", text: "-- All Departments --" });
                    deptSelectize.setValue("0");
                }

                // Load departments if unit is selected
                if (ddlUnitVal && ddlUnitVal != "0") {
                    $.ajax({
                        url: '@Url.Action("GetAllDepartments", "ManageBCMTrainingForm")',
                        type: 'GET',
                        data: { iUnitID: ddlUnitVal },
                        success: function (response) {
                            console.log('Departments loaded for unit:', ddlUnitVal, response);
                            let selectizeInstance = $('#ddlDepartment')[0].selectize;
                            selectizeInstance.clear();
                            selectizeInstance.clearOptions();
                            selectizeInstance.addOption({ value: "0", text: "-- All Departments --" });
                            selectizeInstance.setValue("0");

                            response && response.forEach(({ departmentID, departmentName }) => {
                                if (departmentID && departmentName) {
                                    selectizeInstance.addOption({ value: departmentID, text: departmentName });
                                }
                            });
                        },
                        error: function (xhr, status, error) {
                            console.error('Error loading departments:', error);
                        }
                    });
                }

                // Apply filters after unit change
                applyFilters();
            });

            // Department dropdown change handler
            $('#ddlDepartment').change(function () {
                console.log('Department changed to:', $(this).val());
                // Apply filters after department change
                applyFilters();
            });
        }



        // Function to apply filters
        function applyFilters() {
            try {
                var orgID = $('#ddlOrganization').val() || "0";
                var unitID = $('#ddlUnit').val() || "0";
                var deptID = $('#ddlDepartment').val() || "0";

                console.log('Applying filters:', { orgID: orgID, unitID: unitID, deptID: deptID });

                // Call the filtered data endpoint
                $.ajax({
                    url: '@Url.Action("GetFilteredTrainings", "ManageBCMTrainingForm")',
                    type: 'GET',
                    data: {
                        orgID: orgID,
                        unitID: unitID,
                        departmentID: deptID
                    },
                    beforeSend: function () {
                        console.log('Loading filtered trainings...');
                        // Show loading indicator
                        $('#example tbody').html('<tr><td colspan="8" class="text-center"><i class="cv-spinner"></i> Loading...</td></tr>');
                    },
                    success: function (data) {
                        console.log('Filtered trainings loaded');
                        $('#example tbody').html(data);

                        // Reinitialize event handlers for the new content
                        reinitializeEventHandlers();
                    },
                    error: function (xhr, status, error) {
                        console.error('Error loading filtered trainings:', error);
                        $('#example tbody').html('<tr><td colspan="8" class="text-center text-danger">Error loading data. Please try again.</td></tr>');
                    }
                });
            } catch (error) {
                console.error('Error in applyFilters:', error);
            }
        }

        // Function to reinitialize event handlers after AJAX content update
        function reinitializeEventHandlers() {
            try {
                console.log('Reinitializing event handlers for filtered content');

                // Reinitialize tooltips for all elements with title attribute
                $('[title]').tooltip('dispose'); // Remove existing tooltips
                $('[title]').tooltip(); // Reinitialize tooltips
                console.log('Tooltips reinitialized');

                // Reinitialize edit button handlers
                $('.btnEdit').off('click').on('click', function () {
                    var trainingId = $(this).data('id');
                    var encryptedId = $(this).data('encrypted-id');
                    console.log('Edit clicked for training ID:', trainingId, 'Encrypted ID:', encryptedId);

                    // Navigate to edit page
                    var editUrl = '@Url.Action("BCMTrainingMasterConfiguration", "BCMTrainingMasterConfiguration", new { area = "BCMTraining" })' + '?iID=' + encodeURIComponent(encryptedId);
                    console.log('Navigating to:', editUrl);
                    window.location.href = editUrl;
                });

                // Reinitialize delete button handlers
                $('.btnDelete').off('click').on('click', function () {
                    var trainingId = $(this).data('id');
                    console.log('Delete clicked for training ID:', trainingId);

                    // Call the existing delete modal function
                    if (typeof openDeleteModal === 'function') {
                        openDeleteModal(trainingId);
                    } else {
                        // Fallback: trigger the modal directly
                        $('#Modal').modal('show');
                        // Load delete content
                        $.get('@Url.Action("DeleteBCMTrainingMaster", "ManageBCMTrainingForm")', { iId: trainingId }, function (data) {
                            $('#Modal .modal-content').html(data);
                        });
                    }
                });

                // Reinitialize publish button handlers
                $('.publish').off('click').on('click', function () {
                    var trainingId = $(this).data('id');
                    console.log('Publish clicked for training ID:', trainingId);

                    // Set the current training ID
                    currentTrainingId = trainingId;

                    // Set default date to today
                    var today = new Date().toISOString().split('T')[0];
                    $('#Publishdate').val(today);

                    // Clear vendor checkbox
                    $('#chkVendor').prop('checked', false);

                    // Set modal title
                    $('#modaltitle').text('Publish Training');

                    // Show the modal
                    $('#Modal').modal('show');
                });

                // Reinitialize imgbuttonPublish handlers for filtered content
                $('#imgbuttonPublish').off('click').on('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var trainingId = $(this).data('id');
                    console.log('imgbuttonPublish clicked for training ID:', trainingId);

                    // Set the current training ID
                    currentTrainingId = trainingId;

                    // Set modal title
                    $('#modaltitle').text('Publish Training');

                    // Show loading state in modal
                    $('#Publishdate').val('');
                    $('#chkVendor').prop('checked', false);

                    // Call controller method to get existing publish data
                    $.ajax({
                        url: '@Url.Action("GetPublishMasterByTrainingID", "ManageBCMTrainingForm")',
                        type: 'GET',
                        data: { trainingId: trainingId },
                        success: function (response) {
                            console.log('Publish data response:', response);

                            if (response.success && response.data) {
                                // Populate modal with existing data
                                alert(response.data.publishDate);
                                $('#Publishdate').val(response.data.publishDate);
                                $('#chkVendor').prop('checked', response.data.isVendor);

                                console.log('Modal populated with existing data:', response.data);
                            } else {
                                // Set default values if no existing data
                                var today = new Date().toISOString().split('T')[0];
                                $('#Publishdate').val(today);
                                $('#chkVendor').prop('checked', false);

                                console.log('No existing data found, using defaults');
                            }

                            // Show the modal
                            $('#Modal').modal('show');
                        },
                        error: function (xhr, status, error) {
                            console.error('Error loading publish data:', error);

                            // Set default values on error
                            var today = new Date().toISOString().split('T')[0];
                            $('#Publishdate').val(today);
                            $('#chkVendor').prop('checked', false);

                            // Show the modal anyway
                            $('#Modal').modal('show');

                            alert('Could not load existing publish data. Using default values.');
                        }
                    });
                });

                // Reinitialize any other UI components that might be needed
                // Initialize DataTable features if needed
                if (typeof $.fn.DataTable !== 'undefined') {
                    // Reinitialize DataTable features for new rows
                    $('#example').DataTable().draw(false);
                }

                console.log('Event handlers and UI components reinitialized successfully');
            } catch (error) {
                console.error('Error reinitializing event handlers:', error);
            }
        }

        // Function to clear all filters
        function clearFilters() {
            try {
                console.log('Clearing all filters');

                // Reset organization dropdown
                if ($('#ddlOrganization')[0] && $('#ddlOrganization')[0].selectize) {
                    $('#ddlOrganization')[0].selectize.setValue("0");
                }

                // Reset unit dropdown
                if ($('#ddlUnit')[0] && $('#ddlUnit')[0].selectize) {
                    let unitSelectize = $('#ddlUnit')[0].selectize;
                    unitSelectize.clear();
                    unitSelectize.clearOptions();
                    unitSelectize.addOption({ value: "0", text: "-- All Units --" });
                    unitSelectize.setValue("0");
                }

                // Reset department dropdown
                if ($('#ddlDepartment')[0] && $('#ddlDepartment')[0].selectize) {
                    let deptSelectize = $('#ddlDepartment')[0].selectize;
                    deptSelectize.clear();
                    deptSelectize.clearOptions();
                    deptSelectize.addOption({ value: "0", text: "-- All Departments --" });
                    deptSelectize.setValue("0");
                }

                // Apply filters to show all data
                applyFilters();
            } catch (error) {
                console.error('Error in clearFilters:', error);
            }
        }

    </script>
}
