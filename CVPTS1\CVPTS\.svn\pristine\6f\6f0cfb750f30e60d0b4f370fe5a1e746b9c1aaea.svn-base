﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Security.Helper;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Areas.BCMConfiguration.Controllers
{
    [Area("BCMConfiguration")]
    public class ConfigSettingApiController : BaseController
    {
        private ProcessSrv _ProcessSrv;
        readonly Utilities _Utilities;
        readonly CVLogger _CVLogger;
        private readonly ILoggerFactory? _LoggerFactory;
        private int iPlanID = 0;

        public ConfigSettingApiController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
        {
            _ProcessSrv = ProcessSrv;
            _Utilities = Utilities;
            _CVLogger = CVLogger;
        }

        public IActionResult ConfigSettingApi()
        {
            List<APIConfiguration> lstApi = new List<APIConfiguration>();
            try
            {
                lstApi = _ProcessSrv.GetAPIDetails_OrgUnitLevel(_UserDetails.OrgID);
                ViewBag.ApiList = lstApi;
                return View();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
                //return NotFound("No Records Found.");
                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        public IActionResult AddConfigApi()
        {
            RecoveryPlan objRecoveryPlan = new RecoveryPlan();

            try
            {
                PopulateDropDown();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return PartialView("_AddApi", new RecoveryPlan());
        }

        [HttpGet]
        public IActionResult EditConfigApi(int iApiId)
        {
            APIConfiguration objWorkflowAPIInfo = new APIConfiguration();
            try
            {
                PopulateDropDown();
                objWorkflowAPIInfo = _ProcessSrv.GetAPIDetails_ById(Convert.ToInt32(iApiId));
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return PartialView("_EditApi", objWorkflowAPIInfo);
        }

        [HttpPost]
        public string SaveWorkflowActionAPI([FromBody] APIConfiguration objWorkflowAPIInfo)
        {
            int iSuccess = 0;
            string strPlanId = HttpContext.Session.GetString("Planid");

            objWorkflowAPIInfo.CreatedBy = _UserDetails.UserID.ToString();
            objWorkflowAPIInfo.IsActive = true;


            iSuccess = _ProcessSrv.WorkflowActionAPI_Save(objWorkflowAPIInfo);

            return iSuccess.ToString();
        }

        [HttpGet]
        public IActionResult DeleteConfigApi(string iApiId)
        {
            APIConfiguration objWorkflowAPIInfo = new APIConfiguration();
            try
            {
                if (Convert.ToInt32(iApiId) > 0)
                {
                    objWorkflowAPIInfo = _ProcessSrv.GetAPIDetails_ById(Convert.ToInt32(iApiId));
                    PopulateDropDown();
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return PartialView("_DeleteApi", objWorkflowAPIInfo);
        }

        [HttpPost]
        public IActionResult DeleteConfigApi(APIConfiguration objWorkflowAPIInfo)
        {
            bool success = false;
            try
            {
                success = _ProcessSrv.ApiConfigDelete(Convert.ToInt32(objWorkflowAPIInfo.ID));
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
            return RedirectToAction("ConfigSettingApi");
        }

        public void PopulateDropDown(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
        {
            try
            {
                //ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());
                ViewBag.OrgInfo = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());

                //ViewBag.Unit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);
                ViewBag.Unit = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString()); ;

                ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

                ViewBag.ResourceList = _Utilities.GetAllResourceList();

                ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }
        }
    }
}