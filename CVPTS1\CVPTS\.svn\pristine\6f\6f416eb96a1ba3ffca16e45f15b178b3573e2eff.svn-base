@model BCM.UI.Areas.BCMReports.ReportModels.IncidentSummaryReport.IncidentSummaryReportModel

<div class="container-fluid p-0">
    <!-- Main Report Card -->
    <div class="card card-custom gutter-b">
        <div class="card-header">
            <h6 class="Page-Title mb-0"><i class="cv-report me-2"></i>Incident Summary Report</h6>
        </div>
        <div class="card-body">
            <!-- Incident Overview Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="Sub-Title text-primary border-bottom pb-2 mb-3">Incident Overview</h6>
                </div>
                <div class="col-md-6">
                    <div class="table-responsive">
                        <table class="table table-borderless table-sm">
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-muted" style="width: 40%;">Event Name:</td>
                                    <td>@Model.EventName</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-muted">Incident Code:</td>
                                    <td>@Model.IncidentCode</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-muted">Plan Name:</td>
                                    <td>@Model.PlanName</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="table-responsive">
                        <table class="table table-borderless table-sm">
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-muted" style="width: 40%;">Notification Time:</td>
                                    <td>@Model.NotificationTime</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-muted">Notifier Name:</td>
                                    <td>@Model.NotifierName</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-muted">Notified As:</td>
                                    <td>@Model.NotifiedAs</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Overall Summary Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="Sub-Title text-primary border-bottom pb-2 mb-3">Overall Summary</h6>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card card-design-none">
                        <div class="card-body text-center p-3">
                            <div class="icon-circle bg-primary text-white mb-2 mx-auto">
                                <i class="cv-clock"></i>
                            </div>
                            <h6 class="Sub-Title mb-1">Est. Completion Time</h6>
                            <p class="fw-bold text-primary mb-0">@Model.OverAllEstCompletionTime</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card card-design-none">
                        <div class="card-body text-center p-3">
                            <div class="icon-circle bg-success text-white mb-2 mx-auto">
                                <i class="cv-check"></i>
                            </div>
                            <h6 class="Sub-Title mb-1">Actual Completion Time</h6>
                            <p class="fw-bold text-success mb-0">@Model.OverAllActualCompletionTime</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card card-design-none">
                        <div class="card-body text-center p-3">
                            <div class="icon-circle bg-warning text-white mb-2 mx-auto">
                                <i class="cv-time"></i>
                            </div>
                            <h6 class="Sub-Title mb-1">Time Deviation</h6>
                            <p class="fw-bold text-warning mb-0">@Model.OverAllPlannedVsActualTimeDeviation</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card card-design-none">
                        <div class="card-body text-center p-3">
                            <div class="icon-circle bg-info text-white mb-2 mx-auto">
                                <i class="cv-chart"></i>
                            </div>
                            <h6 class="Sub-Title mb-1">Time Variation</h6>
                            <p class="fw-bold text-info mb-0">@Model.OverAllPlannedVsActualTimeVariation</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Steps Section -->
            @if (Model.IncidentSummaryReportVms != null && Model.IncidentSummaryReportVms.Any())
            {
                <div class="row">
                    <div class="col-12">
                        <h6 class="Sub-Title text-primary border-bottom pb-2 mb-3">Step Details</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover align-middle">
                                <thead class="table-primary">
                                    <tr>
                                        <th class="fw-semibold">Step Name</th>
                                        <th class="fw-semibold">Description</th>
                                        <th class="fw-semibold">Step Owner</th>
                                        <th class="fw-semibold">Notification Time</th>
                                        <th class="fw-semibold">Actual Time</th>
                                        <th class="fw-semibold">Time Taken</th>
                                        <th class="fw-semibold">Actual Completion</th>
                                        <th class="fw-semibold">Deviation</th>
                                        <th class="fw-semibold">Variation</th>
                                        <th class="fw-semibold">Remarks</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var step in Model.IncidentSummaryReportVms)
                                    {
                                        <tr>
                                            <td class="fw-medium">@step.StepName</td>
                                            <td>@step.StepDescription</td>
                                            <td class="text-muted">@step.StepOwnerName</td>
                                            <td>@step.NotificationTime</td>
                                            <td>@step.ActualTime</td>
                                            <td class="text-primary fw-medium">@step.TimeTaken</td>
                                            <td class="text-success fw-medium">@step.ActualCompletionTime</td>
                                            <td class="text-warning fw-medium">@step.PlanVsActualTimeDeviation</td>
                                            <td class="text-info fw-medium">@step.PlanVsActualTimeVariation</td>
                                            <td>@step.Remarks</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="alert alert-info border-0">
                    <div class="d-flex align-items-center">
                        <i class="cv-info me-2 fs-5"></i>
                        <span>No step details available for this incident.</span>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<style>
    /* Incident Summary Report Modal Styles */
    .incident-summary-modal .table th {
        font-size: var(--bs-body-font-size);
        white-space: nowrap;
        border-bottom: 2px solid var(--bs-primary);
    }

    .incident-summary-modal .table td {
        font-size: var(--bs-body-font-size);
        vertical-align: middle;
        border-bottom: 1px solid #f0f0f0;
    }

    .incident-summary-modal .table-primary th {
        background-color: var(--bs-primary);
        color: white;
        border-color: var(--bs-primary);
    }

    .incident-summary-modal .icon-circle {
        width: 35px;
        height: 35px;
    }

    .incident-summary-modal .card-design-none {
        transition: transform 0.2s ease-in-out;
    }

    .incident-summary-modal .card-design-none:hover {
        transform: translateY(-2px);
    }

    .incident-summary-modal .Sub-Title {
        font-size: var(--bs-sub-title);
        font-weight: 600;
    }

    .incident-summary-modal .Page-Title {
        font-size: var(--bs-page-title-font-size);
        font-weight: 600;
    }

    .incident-summary-modal .table-responsive {
        border-radius: var(--bs-border-radius);
        overflow: hidden;
    }

    .incident-summary-modal .alert {
        border-radius: var(--bs-border-radius);
        background-color: #f8f9fa;
    }
</style>
