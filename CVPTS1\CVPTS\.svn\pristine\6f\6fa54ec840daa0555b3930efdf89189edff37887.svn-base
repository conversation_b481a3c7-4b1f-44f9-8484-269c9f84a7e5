﻿@model BCM.BusinessClasses.KPIMeasurementMasters
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@* <div class="wizard-content"> *@
<form asp-action="AddKPIMeasurementMaster" asp-controller="KPIMeasurementMaster">
    <div class="row row-cols-2">
        <div class="col">

            <div class="form-group">
                <label class="form-label">Objective Measured By</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-measure"></i></span>
                    <input type="text" class="form-control" asp-for="Objective" placeholder="Enter Objective Measured By" required />
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Effectiveness Criteria</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-criteria"></i></span>
                    <textarea class="form-control" placeholder="Enter Effectiveness Criteria" asp-for="Effectiveness" style="height:0px" required></textarea>
                </div>
            </div>

        </div>
        <div class="col">
            <div class="form-group">
                <label class="form-label">Target</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-target"></i></span>
                    <input type="text" class="form-control" placeholder="Enter Target" asp-for="Target" required />
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Effectiveness Rating</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-rating"></i></span>
                    <textarea class="form-control" placeholder="Enter Effectiveness Rating" asp-for="EffectivenessRating" style="height:0px" required></textarea>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm" id="btnSave">Save</button>
        </div>
    </div>
</form>

@* </div> *@