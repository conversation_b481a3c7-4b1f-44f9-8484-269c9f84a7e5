﻿@model IEnumerable<BCM.BusinessClasses.BusinessProcessInfo>
@using BCM.Shared
@{
    ViewBag.Title = "Manage Vendor";
    Layout = "~/Views/Shared/_Layout.cshtml";
    //int iIndex = 1;
    string UserOrgID = ViewBag.SelectedOrgID;
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header ">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">Vendor</h6>
        <div class="d-flex gap-3 justify-content-end align-items-end">
            <div class="vendor-section my-0">
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle primary"><i class="cv-current-BCM-entites fs-6"></i></span><span>Total Vendors</span><span class="count-primary">@ViewBag.TotalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle warning"><i class="cv-warning fs-6"></i></span><span>Critical Vendors</span><span class="count-warning">@ViewBag.CriticalCount</span>
                </div>
                <div class="d-flex align-items-center gap-2 fs-14">
                    <span class="vendor-circle success"><i class="cv-success1 fs-6"></i></span><span>Non Critical Vendors</span><span class="count-success">@ViewBag.NonCriticalCount</span>
                </div>
            </div>
            <div class="mt-2" style="display:none">
                <div class="form-check form-check-inline me-2">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="-1" checked>
                    <label class="form-check-label" for="inlineRadio1">All Vendors</label>
                </div>
                <div class="form-check form-check-inline me-2">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="1">
                    <label class="form-check-label" for="inlineRadio2">Under BCM Scope</label>
                </div>
                <div class="form-check form-check-inline me-2">
                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="0">
                    <label class="form-check-label" for="inlineRadio3">Not Under BCM Scope</label>
                </div>
            </div>
            <div class="input-group Search-Input">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control" placeholder="Search">
            </div>
            <div class="dropdown">
                <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                    <i class="cv-filter align-middle" title="View Filter"></i>
                </button>
                <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                    <div class="mb-3">
                        <label>Organizations</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                            <select class="form-select form-control selectized" autocomplete="off" id="ddlOrganization" asp-for=@UserOrgID aria-label="Default select example" asp-items="@(new SelectList(ViewBag.OrgInfo,"Id","OrganizationName"))">
                                <option selected value="0">-- Select Organizations --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Units</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                            <select class="form-control form-select selectized" autocomplete="off" id="ddlUnit" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Unit,"UnitID","UnitName"))">
                                <option selected value="0">-- Select Units --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-department"></i></span>
                            <select class="form-control form-select selectized" autocomplete="off" id="ddlDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Department,"DepartmentID","DepartmentName"))">
                                <option selected value="0">-- select Departments --</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label>Sub Departments</label>
                        <div class="input-group">
                            <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
                            <select class="form-control form-select selectized" autocomplete="off" id="ddlSubDepartment" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.SubDepartment,"SubFunctionID","SubFunctionName"))">
                                <option selected value="0">-- Select SubDepartments --</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-end">
                        @* <button type="button" id="btnSearch" class="btn btn-sm btn-primary">Search</button> *@
                    </div>
                </form>
            </div>

            <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</button>
        </div>
    </div>
</div>

<div class="Page-Condant border-0" id="FacilityList">
    @await Html.PartialAsync("_FilteredVendor")
</div>

@* Configure Vendor Modal Start *@
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title"> Vendor Configuration</h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-data">
            </div>
        </div>
    </div>
</div>
@* Configure Vendor Modal End *@



<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->
@section Scripts {
    <script>
        $(document).ready(function ()
        {

             $(document).on("click", ".Closebtn", function(){
                  location.reload();
             });
            // Function to initialize or reinitialize DataTable
            function initializeDataTable() {
                // Check if DataTable is already initialized
                if ($.fn.DataTable.isDataTable('#example')) {
                    // Destroy the existing DataTable
                    $('#example').DataTable().destroy();
                }

                // Initialize DataTable if the table exists
                if ($('#example').length > 0) {
                    $('#example').DataTable({
                        language: {
                            paginate: {
                                next: '<i class="cv-right-arrow"></i>',
                                previous: '<i class="cv-left-arrow"></i>'
                            }
                        },
                        dom: '<"pull-left"B><"pull-right"f>rt<"row px-2 pb-2 align-items-center g-0"<"col"l><"col text-center"i><"col"p>>',
                        scrollY: true,
                        deferRender: true,
                        scroller: true,
                    });
                }
            }


            // Initialize DataTable on page load
            //initializeDataTable();

            // Handle modal buttons
            $('#btnCreate').click(function () {
                $.get('@Url.Action("AddBCMVendor", "ManageVendor")', function (data) {
                    $('.modal-data').html(data);
                    $('#CreateModal').modal('show');
                });
            });

            $(document).on('click', '.btnDelete', function () {
                var id = $(this).data('id');
                var EntityTypeID = $(this).data('EntityTypeID');
                var Text = $(this).data('Text');

                $.get('@Url.Action("DeleteBCMVendor", "ManageVendor")',{ id: id }, function (data) {
                    $('#DeleteBody').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

            $(document).on('click', '.btnEdit', function () {
                var id = $(this).data('id');
                var EntityTypeID = $(this).data('EntityTypeID');
                var Text = $(this).data('Text');
                $.get('@Url.Action("EditBCMVendor", "ManageVendor")',{ id: id }, function (data) {
                    $('.modal-data').html(data);
                    $('#CreateModal').modal('show');
                });
            });

            // Handle organization dropdown change
            $('#ddlOrganization').change(function () {
                var ddlOrganizationVal = $('#ddlOrganization').val();
                 var ddlUnitnVal = $('#ddlUnit').val();

                if (ddlOrganizationVal) {
                    $.ajax({
                        url: '@Url.Action("GetAllUnits", "ManageVendor")',
                        type: 'GET',
                        data: { iOrgID: ddlOrganizationVal },
                        success: function (response) {
                            let selectizeInstance = $('#ddlUnit')[0].selectize;
                            selectizeInstance.clear();
                            selectizeInstance.clearOptions();
                            selectizeInstance.addOption({ value: "0", text: "-- All Units --" });
                            selectizeInstance.addItem("0");

                            response && response.forEach(({ unitID, unitName }) => {
                                 if (unitID && unitName) {
                                     selectizeInstance.addOption({ value: unitID, text: unitName });
                                 }
                            });

                        },
                        // success: function (data) {
                        //     var ddlUnit = $('#ddlUnit');
                        //     ddlUnit.empty();
                        //     ddlUnit.append('<option value="0">-- All Units --</option>');
                        //     $.each(data, function (index, item) {
                        //         ddlUnit.append('<option value="' + item.unitID + '">' + item.unitName + '</option>')
                        //     });
                        // },
                        error: function(Error) {
                            console.log(Error, "Error Block");
                        }
                    });
                }
            });

            // Handle unit dropdown change
            $('#ddlUnit').change(function () {
                var iUnitID = $(this).val();
                 var ddlUnitnVal = $('#ddlUnit').val();
                 var ddlDepartmentVal = $('#ddlDepartment').val();
                 var ddlOrganizationVal = $('#ddlOrganization').val();
                 var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                 var iIsUnderBCM = -1;
                if (iUnitID) {
                    $.ajax({
                        url: '@Url.Action("GetAllDepartments", "ManageVendor")',
                        type: 'GET',
                        data: { iUnitID: iUnitID },
                        success: function (response) {
                            let selectizeInstance = $('#ddlDepartment')[0].selectize;
                            selectizeInstance.clear();
                            selectizeInstance.clearOptions();
                            selectizeInstance.addOption({ value: "0", text: "-- All Departments --" });
                            selectizeInstance.addItem("0");

                            response && response.forEach(({ departmentID, departmentName }) => {
                                 if (departmentID && departmentName) {
                                     selectizeInstance.addOption({ value: departmentID, text: departmentName });
                                 }
                            });

                        }
                        // success: function (data) {
                        //     var ddlDepartment = $('#ddlDepartment');
                        //     ddlDepartment.empty();
                        //     ddlDepartment.append('<option value="0">-- All Departments --</option>');
                        //     $.each(data, function (index, item) {
                        //         ddlDepartment.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                        //     });
                        // }
                    });
                    $.get('@Url.Action("GetFileredVendor", "ManageVendor")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
                     var tableData = $('#FacilityList');
                        var tableData = $('#FacilityList');
                        tableData.empty();
                        $('#FacilityList').html(data);
                });
                }
            });

            // Handle department dropdown change
            $('#ddlDepartment').change(function () {
                var iDepartmentID = $(this).val();
                var ddlUnitnVal = $('#ddlUnit').val();
                var ddlOrganizationVal = $('#ddlOrganization').val();
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                var iIsUnderBCM = -1;
                if (iDepartmentID) {
                    $.ajax({
                        url: '@Url.Action("GetAllSubDepartments", "ManageVendor")',
                        type: 'GET',
                        data: { iDepartmentID: iDepartmentID },
                        success: function (response) {
                            let selectizeInstance = $('#ddlSubDepartment')[0].selectize;
                            selectizeInstance.clear();
                            selectizeInstance.clearOptions();
                            selectizeInstance.addOption({ value: "0", text: "-- All SubDepartments --" });
                            selectizeInstance.addItem("0");

                            response && response.forEach(({ subFunctionID, subFunctionName }) => {
                                 if (subFunctionID && subFunctionName) {
                                     selectizeInstance.addOption({ value: subFunctionID, text: subFunctionName });
                                 }
                            });

                        }
                        // success: function (data) {
                        //     var ddlSubDepartment = $('#ddlSubDepartment');
                        //     ddlSubDepartment.empty();
                        //     ddlSubDepartment.append('<option value="0">-- All SubDepartments --</option>');
                        //     $.each(data, function (index, item) {
                        //         ddlSubDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>')
                        //     });
                        // }
                    });
                }
                $.get('@Url.Action("GetFileredVendor", "ManageVendor")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: iDepartmentID, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
                     var tableData = $('#FacilityList');
                        var tableData = $('#FacilityList');
                        tableData.empty();
                        $('#FacilityList').html(data);
                });
            });

            $('#ddlSubDepartment').change(function () {
                var iDepartmentID = $('#ddlDepartment').val();
                var ddlUnitnVal = $('#ddlUnit').val();
                var ddlOrganizationVal = $('#ddlOrganization').val();
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val();
                var iIsUnderBCM = -1;

                $.get('@Url.Action("GetFileredVendor", "ManageVendor")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: iDepartmentID, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
                     var tableData = $('#FacilityList');
                        var tableData = $('#FacilityList');
                        tableData.empty();
                        $('#FacilityList').html(data);
                });
             });

            $('#inlineRadio1,#inlineRadio2,#inlineRadio3').change (function () {
                var iIsUnderBCM;
                var iDepartmentID = $('#ddlDepartment').val();
                var ddlUnitnVal = $('#ddlUnit').val();
                var ddlOrganizationVal = $('#ddlOrganization').val();
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val();

                if ($("#inlineRadio1").prop("checked")) {

                    iIsUnderBCM = $('#inlineRadio1').val();
                }
                else if ($("#inlineRadio2").prop("checked")) {

                    iIsUnderBCM = $('#inlineRadio2').val();
                }
                else if ($("#inlineRadio3").prop("checked")) {

                    iIsUnderBCM = $('#inlineRadio3').val();
                }
                // alert(Radio + " " + ddlOrganizationVal + " " + ddlUnitnVal + " " + ddlDepartmentVal + " " + ddlSubDepartmentVal);
                //$.get('/BCMProcessBIA/ManageBusinessProcesses/GetFileredProcess', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: ddlDepartmentVal, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
                $.get('@Url.Action("GetFileredVendor", "ManageVendor")', { OrgID: ddlOrganizationVal, UnitID: ddlUnitnVal, DepartmentID: iDepartmentID, SubDepartmentID: ddlSubDepartmentVal, IsUnderBCM: iIsUnderBCM}, function (data) {
                     var tableData = $('#FacilityList');
                        var tableData = $('#FacilityList');
                        tableData.empty();
                        $('#FacilityList').html(data);
                });
            });

            // Handle filter search
            $('#btnSearch').click(function () {
                var ddlOrganizationVal = $('#ddlOrganization').val() || 0;
                var ddlUnitVal = $('#ddlUnit').val() || 0;
                var ddlDepartmentVal = $('#ddlDepartment').val() || 0;
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val() || 0;

                // Show loading indicator
                $('#FacilityList').html('<div class="text-center p-5"><i class="cv-loading fs-1 d-block mb-3"></i>Loading...</div>');

                $.ajax({
                    url: '@Url.Action("GetFileredVendor", "ManageVendor")',
                    type: 'GET',
                    data: {
                        OrgID: ddlOrganizationVal,
                        UnitID: ddlUnitVal,
                        DepartmentID: ddlDepartmentVal,
                        SubDepartmentID: ddlSubDepartmentVal
                    },
                    success: function(data) {
                        // Update content
                        $('#FacilityList').html(data);

                        // Initialize DataTable after content update
                       
                    },
                    error: function(xhr, status, error) {
                        $('#FacilityList').html('<div class="alert alert-danger">Error loading filtered results. Please try again.</div>');
                    }
                });
            });

            // Enhanced search input handling with debounce
            var searchTimer;
            $('#search-inp').on('input', function() {
                // Get the search text
                var searchText = $(this).val();

                // Clear any existing timer to debounce the search
                clearTimeout(searchTimer);

                // Set a timer to delay the search until the user stops typing
                searchTimer = setTimeout(function() {
                    // Perform the search with the current text
                    performSearch(searchText);
                }, 300); // 300ms delay for debouncing
            });

            // Handle search input clearing (when user deletes all text)
            $('#search-inp').on('keyup', function(e) {
                // Check if the input is empty (user cleared it) or if backspace/delete was pressed
                if ($(this).val() === '') {
                    // Clear any existing timer
                    clearTimeout(searchTimer);
                    // Load all data immediately
                    performSearch('');
                }
            });

            // Function to perform search with proper error handling
            function performSearch(searchText) {
                // Get filter values
                var ddlOrganizationVal = $('#ddlOrganization').val() || 0;
                var ddlUnitVal = $('#ddlUnit').val() || 0;
                var ddlDepartmentVal = $('#ddlDepartment').val() || 0;
                var ddlSubDepartmentVal = $('#ddlSubDepartment').val() || 0;

                // Show loading indicator
                $('#FacilityList').html('<div class="text-center p-5"><i class="cv-loading fs-1 d-block mb-3"></i>Loading...</div>');

                // Make AJAX request
                $.ajax({
                    url: '@Url.Action("GetSearchVendor", "ManageVendor")',
                    type: 'GET',
                    data: {
                        textSearch: searchText,
                        OrgID: ddlOrganizationVal,
                        UnitID: ddlUnitVal,
                        DepartmentID: ddlDepartmentVal,
                        SubDepartmentID: ddlSubDepartmentVal
                    },
                    success: function(data) {
                        // Update content
                        $('#FacilityList').html(data);

                        // Check if DataTable exists and initialize it
                        if ($('#example').length > 0) {
                            // Initialize DataTable after content update with a delay to ensure DOM is ready
                           
                        }
                    },
                    error: function(xhr, status, error) {
                        // Show error message
                        $('#FacilityList').html(
                            '<div class="alert alert-danger mt-3">' +
                            '<strong>Error:</strong> Unable to retrieve search results. ' +
                            'Please try again.' +
                            '</div>'
                        );
                    },
                    // Add timeout to prevent hanging requests
                    timeout: 10000 // 10 seconds timeout
                });
            }

            // Also handle Enter key press for immediate search
            $('#search-inp').keypress(function(e) {
                if (e.which === 13) { // Enter key
                    e.preventDefault();
                    // Clear any existing timer
                    clearTimeout(searchTimer);
                    // Perform search immediately
                    performSearch($(this).val());
                }
            });
        });
    </script>
}
