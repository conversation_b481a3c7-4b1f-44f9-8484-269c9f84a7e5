﻿@model IEnumerable<BCM.BusinessClasses.BusinessProcessInfo>
@using BCM.Shared
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    int iIndex = 0;
}
@{
    if (Model != null && Model.Any())
    {
        @foreach (var objBusiProcInfo in Model)
    {
        iIndex++;
        <tr>
            <td>@iIndex</td>
            <td>
                <div class="d-grid">
                    <span class="fw-semibold text-warning" style="display:none;">@objBusiProcInfo.ProcessCode</span>
                    <span style="display:none;"><b>Entity Type : </b>@objBusiProcInfo.BCMEntityType</span>
                    <span title="Entity Name">@objBusiProcInfo.ProcessName</span>
                    <span style="display:none;"><b>Version  : </b><span class="text-info">@objBusiProcInfo.Version</span></span>
                    <span style="display:none;">
                        <b>Is Under BCM Scope :</b>

                        @if (!string.IsNullOrEmpty(@objBusiProcInfo.ProcessCode))
                        {
                                <span class="text-danger fw-semibold">Yes</span>
                        }
                        else
                        {
                                <span class="text-success fw-semibold">No</span>
                        }
                    </span>
                </div>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr title="Owner">
                            <td style="display:none" class="fw-semibold"><i class="cv-user"></i></td>
                            <td style="display:none"> : </td>
                            <td>
                                    @if (@objBusiProcInfo.ProcessOwner == "" || @objBusiProcInfo.ProcessOwner == null)
                                    {
                                        <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                    }
                                    else
                                    {
                                        @objBusiProcInfo.ProcessOwner
                                    }
                            </td>
                        </tr>
                        <tr style="display:none;">
                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                            <td>:</td>
                            <td><a class="text-primary" href="#">@objBusiProcInfo.OwnerEmail</a></td>
                        </tr>
                        <tr style="display:none;">
                            <td style="display:none" class="fw-semibold"><i class="cv-phone"></i></td>
                            <td style="display:none">:</td>
                            <td>@objBusiProcInfo.ProcessOwnerMobile</td>
                        </tr>
                    </tbody>
                </table>
            </td>
            @*<td >
                <table>
                    <tbody>
                        <tr>
                            <td class="fw-semibold"><i class="cv-company"></i></td>
                            <td> : </td>
                            <td>
                                @objBusiProcInfo.OrgName
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-unit"></i> </td>
                            <td>:</td>
                            <td>@objBusiProcInfo.UnitName</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-department"></i> </td>
                            <td>:</td>
                            <td>@objBusiProcInfo.DepartmentName</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                            <td>:</td>
                            <td>@objBusiProcInfo.SubFunctionName</td>
                        </tr>
                    </tbody>
                </table>
            </td>*@
            <td>
                <table>
                    <tbody>                       
                    <tr title="Unit">
                            <td style="display:none" class="fw-semibold"><i class="cv-unit"></i> </td>
                            <td style="display:none">:</td>
                            <td>@objBusiProcInfo.UnitName</td>
                        </tr>                                               
                    </tbody>
                </table>
            </td>
            <td>
                <table>
                    <tbody>                                               
                        <tr title="Department">
                            <td style="display:none" class="fw-semibold"><i class="cv-department"></i> </td>
                            <td style="display:none">:</td>
                            <td>
                                @{
                                    if (string.IsNullOrEmpty(@objBusiProcInfo.SubFunctionName))
                                    {
                                        <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                    }
                                    else
                                    {
                                        @objBusiProcInfo.DepartmentName
                                    }
                                }
                            </td>
                        </tr>                        
                    </tbody>
                </table>
            </td>
            <td>
                <table>
                    <tbody>                                                                    
                        <tr title="Sub Department">
                            
                            <td style="display:none;" class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                            <td style="display:none;">:</td>
                            <td>
                                @{
                                    if (string.IsNullOrEmpty(@objBusiProcInfo.SubFunctionName))                        
                                    {
                                        <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                    }
                                    else
                                    {
                                        @objBusiProcInfo.SubFunctionName
                                    }
                                }
                                
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr title="RTO" style="display:none;">
                            <td>Owner RTO </td>
                            <td> : </td>
                            <td>
                                @Utilities.GetFormattedRTO(Convert.ToInt32(@objBusiProcInfo.OwnerRTO))
                            </td>
                        </tr>
                        <tr title="MAO" style="display:none;">
                            <td>Owner MTR </td>
                            <td>:</td>
                            <td> @Utilities.GetFormattedRTO(Convert.ToInt32(@objBusiProcInfo.OwnerMTR))</td>
                        </tr>
                        <tr title="Calculated RTO">
                            @*<td>RTO </td>*@
                            <td style="display:none" class="fw-semibold"><i class="cv-RTO me-1"></i></td>
                            <td style="display:none">:</td>                            
                            <td>
                                @{
                                    if (objBusiProcInfo.OwnerRTO == "0" || objBusiProcInfo.OwnerRTO == null)
                                    {
                                        <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                    }
                                    else
                                    {
                                          var RTO =   Utilities.GetFormattedRTO(Convert.ToInt32(@objBusiProcInfo.OwnerRTO));
                                            <span>@RTO</span>
                                    }
                                }                               
                            </td>
                        </tr>
                        <tr title="Calculated MAO" style="display:none;">
                            <td>MAO </td>
                            <td>:</td>
                            <td>@Utilities.GetFormattedRTO(Convert.ToInt32(@objBusiProcInfo.MTR))</td>
                        </tr>
                        <tr title="RPO" style="display:none;">
                            <td>RPO </td>
                            <td>:</td>
                            <td>@Utilities.GetFormattedRPO(Convert.ToInt32(@objBusiProcInfo.RPO))</td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                @if (@objBusiProcInfo.IsCritical == 1)
                {
                      <span class ="text-danger fw-semibold">Yes</span>
                }
                else
                {
                      <span class="text-success fw-semibold" >No</span>
                }
            </td>
            <td>
                @{
                    int statusId = Convert.ToInt32(@objBusiProcInfo.Status);
                }
                <span class="d-flex align-items-center @BCM.Shared.Utilities.ApprovalStatusWiseTextClass(statusId)">
                    <i class="@BCM.Shared.Utilities.ApprovalStatusWiseClass(statusId) me-2"></i>
                    @BCM.Shared.Utilities.ApprovalStatus(statusId)
                </span>                
            </td>          
            <td class="text-center">

                <div class="d-flex align-items-center gap-2">
                    
                    <div class="btn-action">
                        @if (@objBusiProcInfo.ProcessCode == string.Empty)
                        {
                            <a class="text-dark" asp-action="BusinessProcessForm" asp-area="BCMProcessBIA" asp-controller="BusinessProcessForm" asp-route-strEntityTypeID="@BCM.Security.Helper.CryptographyHelper.Encrypt(objBusiProcInfo.EntityTypeID.ToString())" asp-route-strRecordID="@BCM.Security.Helper.CryptographyHelper.Encrypt(objBusiProcInfo.RecordID.ToString())" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(objBusiProcInfo.ProcessID.ToString())">
                                <i class="cv-page-name align-middle ViewBIA" type="button" title="View BIA"></i>
                            </a>
                        }
                        else
                        {
                            <a class="text-dark" asp-action="PerformProcessBIA" asp-area="BCMProcessBIA" asp-controller="PerformProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(objBusiProcInfo.ProcessID.ToString())">
                                <i class="cv-page-name align-middle ViewBIA" type="button" title="View BIA"></i>
                            </a>
                        }
                    </div>
                    
                    <div class="dropdown dropstart">
                        <span class="btn-action" role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false" title="View Details"><i class="cv-activity-details" title="View Details"></i></span>
                        <div class="dropdown-menu border-0">
                            <h6 class="dropdown-header fw-semibold  text-primary pb-0">BCM Entity Details</h6>
                            <table class="table mb-0 table-borderless">
                                <tbody>
                                    <tr>
                                        <td>
                                            <table>
                                                <tbody class="table table-sm mb-0 table-borderless">
                                                    <tr>
                                                        <td class ="fw-semibold">Entity Code </td>
                                                        <td>:</td>
                                                        <td>
                                                            @if (@objBusiProcInfo.ProcessCode == "" || @objBusiProcInfo.ProcessCode == null)
                                                            {
                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                <span class="fw-semibold text-warning">  ( @objBusiProcInfo.ProcessCode )</span>
                                                            }
                                                        </td>

                                                    </tr>
                                                    <tr>
                                                        <td class="fw-semibold">Entity Type</td>
                                                        <td>:</td>
                                                        <td>
                                                            @if (@objBusiProcInfo.BCMEntityType == "" || @objBusiProcInfo.BCMEntityType == null)
                                                            {
                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                @objBusiProcInfo.BCMEntityType
                                                            }
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-semibold">Entity Name</td>
                                                        <td>:</td>
                                                        <td>
                                                            @if (@objBusiProcInfo.ProcessName == "" || @objBusiProcInfo.ProcessName == null)
                                                            {
                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                @objBusiProcInfo.ProcessName
                                                            }
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-semibold">Version </td>
                                                        <td>:</td>
                                                        <td>
                                                            @if (@objBusiProcInfo.Version == "" || @objBusiProcInfo.Version == null)
                                                            {
                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                            }
                                                            else
                                                            {
                                                                <span class="fw-semibold text-warning">  ( @objBusiProcInfo.Version )</span>
                                                            }
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-semibold">Is Under BCM Scope</td>
                                                        <td>:</td>
                                                        <td>
                                                            @if (@objBusiProcInfo.ProcessCode != "")
                                                            {
                                                                <span class="text-success fw-semibold">
                                                                    Yes
                                                                </span>
                                                            }
                                                            else
                                                            {
                                                                <span class="text-danger fw-semibold">
                                                                    No
                                                                </span>
                                                            }
                                                        </td>
                                                    </tr>

                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <table class="table table-sm mb-0 table-borderless">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="table table-sm mb-0 table-borderless">
                                                                <tbody>
                                                                    <tr>
                                                                        <th class="fw-semibold text-primary" colspan="3">Owner Detail</th>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                                                        <td> : </td>
                                                                        <td>
                                                                            @if (@objBusiProcInfo.ProcessOwner == "" || @objBusiProcInfo.ProcessOwner == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @objBusiProcInfo.ProcessOwner
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @if (@objBusiProcInfo.OwnerEmail == "" || @objBusiProcInfo.OwnerEmail == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @objBusiProcInfo.OwnerEmail
                                                                            }
                                                                        </td>
                                                                    </tr>

                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @if (@objBusiProcInfo.ProcessOwnerMobile == "" || @objBusiProcInfo.ProcessOwnerMobile == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @objBusiProcInfo.ProcessOwnerMobile
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                        <td>
                                                            <table class="table table-sm mb-0 table-borderless">
                                                                <tbody>
                                                                    <tr>
                                                                        <th class="fw-semibold text-primary" colspan="3">Approver Detail</th>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                                                        <td> : </td>
                                                                        <td>
                                                                            @if (@objBusiProcInfo.ApproverName == "" || @objBusiProcInfo.ApproverName == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @objBusiProcInfo.ApproverName
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                    <tr>

                                                                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @if (@objBusiProcInfo.ApproverEmail == "" || @objBusiProcInfo.ApproverEmail == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @objBusiProcInfo.ApproverEmail
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                                        <td>:</td>
                                                                        <td>

                                                                            @if (@objBusiProcInfo.ApproverMobile == "" || @objBusiProcInfo.ApproverMobile == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @objBusiProcInfo.ApproverMobile
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>

                                                </tbody>
                                            </table>
                                        </td>

                                    </tr>
                                    <tr>
                                        <td>
                                            <table class="table table-sm mb-0 table-borderless">
                                                <tbody>
                                                    <tr>
                                                        <th class="fw-semibold text-primary" colspan="3">RTO/MAO/RPO</th>
                                                    </tr>
                                                    <tr title="RTO">
                                                        <td class="text-secondary"><i class="cv-RTO me-1"></i> RTO </td>
                                                        <td> : </td>
                                                        <td>
                                                            @{
                                                                if (objBusiProcInfo.OwnerRTO == "0" || objBusiProcInfo.OwnerRTO == null)
                                                                {
                                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                }
                                                                else
                                                                {
                                                                    var rt = Utilities.GetFormattedRTO(Convert.ToInt32(objBusiProcInfo.OwnerRTO));
                                                                    <span>@rt</span>
                                                                }
                                                            }

                                                        </td>
                                                    </tr>


                                                    <tr title="Calculated MAO">
                                                        <td class="text-secondary"><i class="cv-RTO me-1"></i>MAO </td>
                                                        <td>:</td>
                                                        <td>
                                                            @{
                                                                if (objBusiProcInfo.MTR == "0" || objBusiProcInfo.MTR == null)
                                                                {
                                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                }
                                                                else
                                                                {
                                                                    var MAO =Utilities.GetFormattedRTO(Convert.ToInt32(@objBusiProcInfo.MTR));
                                                                    <span>@MAO</span>
                                                                }
                                                            }
                                                        </td>
                                                    </tr>
                                                    <tr title="RPO">
                                                        <td class="text-secondary"><i class="cv-rpo me-1"></i>RPO </td>
                                                        <td>:</td>
                                                        <td>
                                                            @{
                                                                var a = objBusiProcInfo.RPO;
                                                                if (objBusiProcInfo.RPO == "0" || objBusiProcInfo.RPO == null)
                                                                {
                                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                }
                                                                else
                                                                {
                                                                    Utilities.GetFormattedRPO(Convert.ToInt32(objBusiProcInfo.RPO));
                                                                    var RPO = Utilities.GetFormattedRPO(Convert.ToInt32(@objBusiProcInfo.RPO));
                                                                    <span class="text-danger fw-semibold">
                                                                        @RPO
                                                                    </span>
                                                                }
                                                            }
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>


                    <span class="btn-action btnEdit" id="btnEdit" type="button" data-Entity-id="@objBusiProcInfo.EntityTypeID" data-record-id="@objBusiProcInfo.RecordID" title="Edit"><i class="cv-edit me-1"></i></span>


                    <span class="btn-action btnDelete" id="btnDelete" type="button" data-bs-toggle="modal" data-id="@objBusiProcInfo.ProcessID" data-Text="@objBusiProcInfo.ProcessName" data-Entity-id="@objBusiProcInfo.EntityTypeID" data-bs-target="#DeleteModal" title="Delete"><i class="cv-delete text-danger"></i></span>

                </div>


            </td>
           
        </tr>
        }
    }
    else
    {
        <tr>
            <td colspan="10" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <img src="~/img/Isomatric/no_records_found.svg" alt="No Records Found" style="width: 120px; height: auto; margin-bottom: 1rem;">                                       
                </div>
            </td>
        </tr>
        // <tr>
        //     <td colspan="10" class="text-center py-4">
        //         <div class="d-flex flex-column align-items-center">
        //             <i class="no_record_found" style="font-size: 3rem;"></i>
        //             <span><i class="no_record_found" title="Not Available"></i></span>
        //             <h5 class="text-muted mb-1">No Records Found</h5>
        //             <p class="text-muted mb-0">No BCM Entity data available to display.</p>
        //         </div>
        //     </td>
        // </tr>
    }
}