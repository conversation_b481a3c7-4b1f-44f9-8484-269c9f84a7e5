﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Security.Helper;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc;
using System.Security.Cryptography;
using System;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMTraining.Controllers;

[Area("BCMTraining")]
public class AddQuestionsAndOptionsController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public AddQuestionsAndOptionsController(ProcessSrv iProcessSrv, Utilities Utilities, CVLogger cVLogger, 
        IWebHostEnvironment Environment, BCMMail BCMMail) : base(Utilities)
    {
        _ProcessSrv = iProcessSrv;
        _Utilities = Utilities;
        _CVLogger = cVLogger;
    }

    public IActionResult QuestionsAndOptions(string iID)
    {
        try
        {
            BCMTrainingMaster objBCMTrainingMaster = new BCMTrainingMaster();

            iID = CryptographyHelper.Decrypt(iID.ToString());
            ViewBag.TrainingMasterID = iID;

            HttpContext.Session.SetString("TrainingMasterID", iID.ToString());
            objBCMTrainingMaster.TrainingMasterID = Convert.ToInt32(iID);
            List<BCMTrainingMaster> objQuestionsDetailsColl = _ProcessSrv.QuestionsDetailsGetByTrainingMasterID(Convert.ToInt32(iID));

            // Debug: Log the questions data
            if (objQuestionsDetailsColl != null && objQuestionsDetailsColl.Count > 0)
            {
                // _CVLogger.LogInfoApp($"Found {objQuestionsDetailsColl.Count} questions for TrainingMasterID: {iID}");
                foreach (var question in objQuestionsDetailsColl)
                {
                    //  _CVLogger.LogInfoApp($"Question: ID={question.ID}, QuestionText='{question.QuestionText}', TrainingMasterID={question.TrainingMasterID}");
                }
            }
            else
            {
                //_CVLogger.LogInfoApp($"No questions found for TrainingMasterID: {iID}");
            }

            // Ensure ViewBag.Questions is never null
            ViewBag.Questions = objQuestionsDetailsColl ?? new List<BCMTrainingMaster>();

            // Initialize ButtonAccess to prevent RuntimeBinderException
            ViewBag.ButtonAccess = new { btnUpdate = "" };
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            // Initialize empty list in case of exception
            ViewBag.Questions = new List<BCMTrainingMaster>();
            ViewBag.TrainingMasterID = "0";
            // Initialize ButtonAccess to prevent RuntimeBinderException
            ViewBag.ButtonAccess = new { btnUpdate = "" };
        }
        return View();
    }

    [HttpPost]
    public IActionResult AddQuestion(string ID, int TrainingMasterID, string QuestionText)
    {
        bool bSuccess = false;
        string message = "Question saved successfully.";

        try
        {
            // If TrainingMasterID is not provided in the parameter, try to get it from session as fallback
            if (TrainingMasterID <= 0)
            {
                var sessionTrainingMasterID = HttpContext.Session.GetString("TrainingMasterID");
                if (!string.IsNullOrEmpty(sessionTrainingMasterID))
                {
                    TrainingMasterID = Convert.ToInt32(sessionTrainingMasterID);
                }
            }

            // Validate input parameters
            if (string.IsNullOrEmpty(QuestionText))
            {
                return Json(new { success = false, message = "Question text is required." });
            }

            if (TrainingMasterID <= 0)
            {
                return Json(new { success = false, message = "Training Master ID is required." });
            }

            // Create BCMTrainingMaster object
            var objquestiondetails = new BCMTrainingMaster
            {
                ID = string.IsNullOrEmpty(ID) ? "0" : ID,
                TrainingMasterID = TrainingMasterID,
                QuestionText = QuestionText.Trim()
            };

            // Set user details
            if (_UserDetails != null)
            {
                objquestiondetails.CreatedBy = _UserDetails.UserID;
                objquestiondetails.UpdatedBy = _UserDetails.UserID;
            }

            // Save the question
            int success = _ProcessSrv.QuestionsDetailsSaveandUpdate(objquestiondetails);
            bSuccess = success > 0;

            if (!bSuccess)
            {
                message = "Failed to save question. Please try again.";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            message = $"An error occurred while saving the question: {ex.Message}";
            bSuccess = false;
        }

        return Json(new { success = bSuccess, message = message });
    }

    [HttpPost]
    public IActionResult AddQuestionModel(BCMTrainingMaster objquestiondetails)
    {
        bool bSuccess = false;
        string message = "Question saved successfully.";

        try
        {
            // Validate input
            if (objquestiondetails == null)
            {
                return Json(new { success = false, message = "Invalid question data provided." });
            }

            if (string.IsNullOrEmpty(objquestiondetails.QuestionText))
            {
                return Json(new { success = false, message = "Question text is required." });
            }

            if (objquestiondetails.TrainingMasterID <= 0)
            {
                return Json(new { success = false, message = "Training Master ID is required." });
            }

            // Set default values if not provided
            if (string.IsNullOrEmpty(objquestiondetails.ID))
            {
                objquestiondetails.ID = "0"; // New question
            }

            // Set user details
            if (_UserDetails != null)
            {
                objquestiondetails.CreatedBy = _UserDetails.UserID;
                objquestiondetails.UpdatedBy = _UserDetails.UserID;
            }

            // Save the question
            int success = _ProcessSrv.QuestionsDetailsSaveandUpdate(objquestiondetails);
            bSuccess = success > 0;

            if (!bSuccess)
            {
                message = "Failed to save question. Please try again.";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            message = $"An error occurred while saving the question: {ex.Message}";
            bSuccess = false;
        }

        return Json(new { success = bSuccess, message = message });
    }

    [HttpGet]
    public IActionResult GetOptionsByQuestionId(string questionId, int trainingMasterID)
    {
        try
        {
            // Validate input parameters
            if (string.IsNullOrEmpty(questionId))
            {
                return Json(new { success = false, message = "Question ID is required." });
            }

            if (trainingMasterID <= 0)
            {
                return Json(new { success = false, message = "Training Master ID is required." });
            }

            // Validate questionId is a valid integer
            if (!int.TryParse(questionId, out int questionIdInt))
            {
                return Json(new { success = false, message = "Invalid Question ID format." });
            }

            // Get options for the specific question using the service
            List<BCMTrainingMaster> objOptionsDetailsColl = null;

            try
            {
                //_CVLogger.LogInfoApp($"Calling OptionsDetailsGetByQuestionMasterID with questionId: {questionId}");
                objOptionsDetailsColl = _ProcessSrv.OptionsDetailsGetByQuestionMasterID(Convert.ToInt32(questionId));
                //_CVLogger.LogInfoApp($"Service returned {objOptionsDetailsColl?.Count ?? 0} options");
            }
            catch (Exception serviceEx)
            {
                //_CVLogger.LogErrorApp($"Service call failed: {serviceEx.Message}");
                _CVLogger.LogErrorApp(serviceEx);
                return Json(new { success = false, message = $"Error retrieving options from database: {serviceEx.Message}" });
            }

            // Check if service returned null
            if (objOptionsDetailsColl == null)
            {
                objOptionsDetailsColl = new List<BCMTrainingMaster>();
            }

            // Log options count for debugging
            //_CVLogger.LogInfoApp($"Found {objOptionsDetailsColl.Count} options for questionId: {questionId}");

            // Transform the options data for the frontend
            var options = objOptionsDetailsColl.Select(option => new
            {
                ID = option.ID ?? "",
                OptionText = option.OptionText, // Use helper method to get correct option text
                IsCorrect = option.IsAnswer == 1,
                QuestionID = option.QuestionID ?? questionId,
                TrainingMasterID = option.TrainingMasterID,
                CreatedBy = option.CreatedBy,
                UpdatedBy = option.UpdatedBy
            }).ToList();




            return Json(new
            {
                success = true,
                data = options,
                message = options.Count > 0 ? $"{options.Count} option(s) loaded successfully." : "No options found for this question.",
                count = options.Count,
                questionId = questionId,
                trainingMasterID = trainingMasterID
            });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new
            {
                success = false,
                message = "An error occurred while loading options.",
                error = ex.Message, // Include error details for debugging
                questionId = questionId,
                trainingMasterID = trainingMasterID
            });
        }
    }

    [HttpPost]
    public IActionResult AddOption(string ID, string OptionText, bool IsCorrect, string QuestionID, int TrainingMasterID)
    {
        bool bSuccess = false;
        string message = "Option saved successfully.";
        string optionId = "";

        try
        {
            // Validate input parameters
            if (string.IsNullOrEmpty(OptionText))
            {
                return Json(new { success = false, message = "Option text is required." });
            }

            if (string.IsNullOrEmpty(QuestionID))
            {
                return Json(new { success = false, message = "Question ID is required." });
            }

            if (TrainingMasterID <= 0)
            {
                return Json(new { success = false, message = "Training Master ID is required." });
            }

            // Create option object using BCMTrainingMaster
            var optionDetails = new BCMTrainingMaster
            {
                ID = string.IsNullOrEmpty(ID) ? "0" : ID,
                OptionText = OptionText.Trim(),
                IsAnswer = IsCorrect == true ? 1 : 0,
                QuestionID = QuestionID,
                QuestionmasterID = Convert.ToInt32(QuestionID),
                TrainingMasterID = TrainingMasterID
            };

            // Set user details
            if (_UserDetails != null)
            {
                optionDetails.CreatedBy = _UserDetails.UserID;
                optionDetails.UpdatedBy = _UserDetails.UserID;
            }

            // Save the option using the service layer
            int success = _ProcessSrv.OptionsDetailsSaveandUpdate(optionDetails);
            bSuccess = success > 0;

            if (bSuccess)
            {
                optionId = ID == "0" ? success.ToString() : ID;
            }

            if (!bSuccess)
            {
                message = "Failed to save option. Please try again.";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            message = $"An error occurred while saving the option: {ex.Message}";
            bSuccess = false;
        }

        return Json(new { success = bSuccess, message = message, optionId = optionId });
    }

    /// <summary>
    /// Helper method to extract option text from BCMTrainingMaster object
    /// Tries multiple properties to find the actual option text
    /// </summary>
    private string GetOptionText(BCMTrainingMaster option)
    {
        // Try different possible properties that might contain option text
        if (!string.IsNullOrEmpty(option.OptionText))
            return option.OptionText;

        if (!string.IsNullOrEmpty(option.QuestionText))
            return option.QuestionText;

        // Try other possible property names that might contain option text
        // Add more properties here if needed based on your model structure

        return "No option text available";
    }
}

