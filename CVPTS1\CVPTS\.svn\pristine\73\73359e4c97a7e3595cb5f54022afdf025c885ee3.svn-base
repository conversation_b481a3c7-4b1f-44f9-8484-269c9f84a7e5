﻿@model IEnumerable<BCM.BusinessClasses.OrgGroup>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    if (Model != null && Model.Any())
    {
        int Index = 0;
        foreach (var item in Model)
        {
            Index++;
            <tr>
                <td>@Index</td>
                <td>@item.OrganizationGroupName</td>
                <td>
                    <table>
                        <tbody>
                            <tr>
                                @*<td class="fw-semibold"><i class="cv-corporate-address"></i></td>
                            <td> : </td>*@
                                <td>@item.CompanyAddress</td>
                            </tr>
                            <tr style="display:none;">
                                <td class="fw-semibold"><i class="cv-phone"></i></td>
                                <td>:</td>
                                <td>@item.Mobile</td>
                            </tr>
                            <tr style="display:none;">
                                <td class="fw-semibold"><i class="cv-fax"></i></td>
                                <td>:</td>
                                <td>@item.Fax</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>@item.LegalEntity</td>
                <td>
                    <div class="d-flex">

                        <div>
                            <ul class="ps-0 mb-0">
                                <li class="list-group-item">@item.SPOCName</li>
                                <li class="list-group-item" style="display:none;"><i class="cv-user"></i>:@item.SPOCName</li>
                                <li class="list-group-item" style="display:none;"><i class="cv-mail"></i>:@item.SPOCEmail</li>
                                <li class="list-group-item" style="display:none;"><i class="cv-phone"></i>:@item.SPOCMobile</li>
                            </ul>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center gap-2">
                        <div class="dropdown dropstart">
                            <span class="btn-action" role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false"><i class="cv-activity-details" title="View Details"></i></span>
                            <div class="dropdown-menu border-0">
                                @* <h6 class="dropdown-header fw-semibold text-dark pb-0">Ptech Pune LTD</h6> *@
                                <table class="table mb-0 table-borderless">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <table class="table table-sm mb-0 table-borderless">
                                                    <tbody>
                                                        <tr>
                                                            <th class="fw-semibold text-primary" colspan="3">Org Group Detail</th>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-organization-group-name"></i></td>
                                                            <td> : </td>
                                                            <td>@item.CompanyAddress</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                            <td>:</td>
                                                            <td>@item.Mobile</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                            <td>:</td>
                                                            <td>@item.Fax</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                            <td>
                                                <table class="table table-sm mb-0 table-borderless">
                                                    <tbody>
                                                        <tr>
                                                            <th class="fw-semibold text-primary" colspan="3">SPOC Detail</th>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-user"></i></td>
                                                            <td> : </td>
                                                            <td>@item.SPOCName</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                            <td>:</td>
                                                            <td>@item.SPOCEmail</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                            <td>:</td>
                                                            <td>@item.SPOCMobile</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@item.OrgGroupID"><i class="cv-edit" title="Edit"></i></span>
                        <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="DeleteModal" data-id="@item.OrgGroupID"><i class="cv-delete text-danger" title="Delete"></i></span>
                    </div>

                </td>
            </tr>
        }
    }
    else
    {
        <tr>
            <td colspan="6" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <i class="cv-no-data fs-1 text-muted mb-2"></i>
                    <h5 class="text-muted mb-1">No Records Found</h5>
                    <p class="text-muted mb-0">No organization group data available to display.</p>
                </div>
            </td>
        </tr>
    }
}
