﻿@model IEnumerable<BCM.BusinessClasses.ResourcesInfo>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="g-2 row row-cols-xl-4 row-cols-4 p-3" style="overflow-y: auto;height: 150px;">
    @{
        foreach (var BCMTeamsAndResources in Model)
        {
                <div class="col">
                    <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
                        <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                            <span>
                                <input class="form-check myChkbox" type="checkbox" id="<EMAIL>" value="@BCMTeamsAndResources.ResourceId" data-resourceId="@BCMTeamsAndResources.ResourceId" />
                            </span>
                            <span class="">@BCMTeamsAndResources.ResourceName</span>
                        </span>
                    </div>
                </div>
        }
    }
</div>

<script>
    $(document).ready(function () {
        debugger;
        const checkedItems = localStorage.getItem("checkedItems");  
        const checkboxName = localStorage.getItem("checkboxName");
        const checkedItemsArray = JSON.parse(checkedItems);

        const checkboxes = document.querySelectorAll('input.myChkbox');
        checkboxes.forEach(function (chkbox) {
            var resId = chkbox.getAttribute('data-resourceId');
            chkbox.name = checkboxName;

            if (checkedItemsArray.includes(chkbox.value) && chkbox.name == checkboxName) {
                console.log('checking ', chkbox.value);
                chkbox.checked = true;
            }
            else {
                console.log('not checking ', chkbox.value)
            }
        })
    })
</script>