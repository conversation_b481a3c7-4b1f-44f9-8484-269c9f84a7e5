﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Security.Helper;
using BCM.Shared;
using BCM.UI.Areas.BCMIncidentManagement.Services;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BCM.UI.Areas.BCMIncidentManagement.Controllers;
[Area("BCMIncidentManagement")]
public class UpdateTaskStepsController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    private readonly ApiService _ApiService;
    public UpdateTaskStepsController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, ApiService ApiService) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _ApiService = ApiService;
    }

    public override void OnActionExecuting(ActionExecutingContext context)
    {
        try
        {
            // Check if user has valid session
            var userDetails = _Utilities.LoginUserDetails();
            if (userDetails == null)
            {
                // For email links, redirect to login with return URL
                if (context.HttpContext.Request.Method == "GET" && 
                    context.HttpContext.Request.Query.ContainsKey("IncidentStepID") &&
                    context.HttpContext.Request.Query.ContainsKey("UserID") &&
                    context.HttpContext.Request.Query.ContainsKey("OrgID"))
                {
                    var returnUrl = context.HttpContext.Request.Path + context.HttpContext.Request.QueryString;
                    context.Result = new RedirectToActionResult("Login", "Login", 
                        new { area = "", returnUrl = returnUrl });
                    return;
                }
                
                // For other requests, use base controller logic
                base.OnActionExecuting(context);
                return;
            }
            
            // User is logged in, proceed normally
            base.OnActionExecuting(context);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            // Set a result to avoid unhandled exceptions
            context.Result = new RedirectToActionResult("Login", "Login", new { area = "" });
        }
    }

    public IActionResult UpdateTaskSteps(string IncidentStepID, string UserID, string OrgID)
    {
        RecoveryTaskStepInfo objTaskInfo = new RecoveryTaskStepInfo();
        try
        {
            //int iIncidentStepID = Convert.ToInt32(CryptographyHelper.Decrypt(IncidentStepID));
            //int iUserID = Convert.ToInt32(CryptographyHelper.Decrypt(UserID));
            //int iOrgID = Convert.ToInt32(CryptographyHelper.Decrypt(OrgID));
            
            int iIncidentStepID = string.IsNullOrEmpty(IncidentStepID) ? 0:  Convert.ToInt32(IncidentStepID);
            int iUserID = string.IsNullOrEmpty(UserID) ? 0 : Convert.ToInt32(UserID);
            int iOrgID = string.IsNullOrEmpty(OrgID) ? 0 : Convert.ToInt32(OrgID);

            HttpContext.Session.SetInt32("iIncidentStepID", iIncidentStepID);
            HttpContext.Session.SetInt32("iUserID", iUserID);
            HttpContext.Session.SetInt32("iOrgID", iOrgID);

            objTaskInfo = GetRecoveryStepsData(iIncidentStepID);
            BindStatus();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(objTaskInfo);
    }

    [HttpPost]
    public IActionResult UpdateTaskSteps(RecoveryTaskStepInfo objTaskSteps)
    {
        int iErrorCount = 0;
        int iStepNotSelected = 0;
        try
        {

            RecoveryTaskStepInfo objRecoveryTaskStep = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(Convert.ToInt32(objTaskSteps.IncidentStepID));
            if (!objRecoveryTaskStep.StepStatus.Equals(((int)BCPEnum.StepStatus.Completed).ToString()))
            {
                objRecoveryTaskStep.StepStatus=objTaskSteps.StepStatus;
                if (objRecoveryTaskStep.StepStatus.Equals(((int)BCPEnum.StepStatus.Completed).ToString()) ||
                                objRecoveryTaskStep.StepStatus.Equals(((int)BCPEnum.StepStatus.Failed).ToString()))
                {
                    objRecoveryTaskStep.NotificationSentTime = string.Empty;
                    objRecoveryTaskStep.CompletionTime = DateTime.Now.ToString();
                }
                else
                {
                    if (!string.IsNullOrEmpty(objRecoveryTaskStep.CompletionTime))
                    {
                        objRecoveryTaskStep.CompletionTime = objRecoveryTaskStep.CompletionTime;
                    }
                    else
                    {
                        objRecoveryTaskStep.CompletionTime = string.Empty;
                    }
                }

                objRecoveryTaskStep.UpdatedBy = ((int)BCPEnum.NotificationType.EMail).ToString();
                objRecoveryTaskStep.Remarks="";
                objRecoveryTaskStep.ChangedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                if (objRecoveryTaskStep.ExecutedBy == "0")
                {
                    objRecoveryTaskStep.ExecutedBy = AcknowledgeUser(objRecoveryTaskStep);
                }

                //if (!_ApiService.UpdateStepStatus(objRecoveryTaskStep, Convert.ToInt32(objRecoveryTaskStep.OrgID)))
                if (!_ApiService.UpdateStepStatusNew(objRecoveryTaskStep, Convert.ToInt32(objRecoveryTaskStep.OrgID)))
                {
                    iErrorCount+=1;
                    return Json(new { success = false, message = "Error occoured while updating status..." });
                }
                return Json(new { success = true, message = "Step Updated Successfully..." });
            }
            else
                iStepNotSelected +=1;
            return Json(new { success = false, message = "Step status is match,Please select other status..." });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "ex" });
        }
    }

    private RecoveryTaskStepInfo GetRecoveryStepsData(int iIncidentStepID)
    {
        try
        {
            return _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(iIncidentStepID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return new RecoveryTaskStepInfo();
        }
    }

    private void BindStatus()
    {
        try
        {
            ViewBag.StepStaus = new SelectList(_Utilities.PopulateStepStatus(), "StatusId", "StatusName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private string AcknowledgeUser(RecoveryTaskStepInfo objRecoveryTaskStep)
    {
        string ValidMessageBody = string.Empty;
        string ExecutedBy = "0";

        try
        {
            if (objRecoveryTaskStep.StepOwnerID.Equals(HttpContext.Session.GetInt32("iUserID").ToString()))
            {
                if (_ProcessSrv.ResourceMaster_UpdateActiveUser(HttpContext.Session.GetInt32("iUserID").ToString(), HttpContext.Session.GetInt32("iIncidentStepID") ?? 0))
                {
                    ValidMessageBody =  objRecoveryTaskStep.StepOwnerName + " has taken the responsibilty of performing Task" + objRecoveryTaskStep.StepName;
                    ExecutedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                    //BCPSms.SMSSend(objRecoveryTaskStep.AltStepOwnerMobileNo, objRecoveryTaskStep.AltStepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);
                    ExecutedBy = objRecoveryTaskStep.StepOwnerID;
                }
            }
            else if (objRecoveryTaskStep.AltStepOwnerID.Equals(HttpContext.Session.GetInt32("iUserID").ToString()))
            {
                if (_ProcessSrv.ResourceMaster_UpdateActiveUser(HttpContext.Session.GetInt32("iUserID").ToString(), HttpContext.Session.GetInt32("iIncidentStepID") ?? 0))
                {
                    ValidMessageBody = objRecoveryTaskStep.AltStepOwnerName + " has taken the responsibilty of performing Task" + objRecoveryTaskStep.StepName; //"Dear " + objRecoveryTaskStep.StepOwnerName + ", This is to inform you that " +
                    ExecutedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                    //BCPSms.SMSSend(objRecoveryTaskStep.StepOwnerMobileNo, objRecoveryTaskStep.StepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);
                    ExecutedBy = objRecoveryTaskStep.AltStepOwnerID;
                }
            }
            else if (objRecoveryTaskStep.ReassignedTo.Equals(HttpContext.Session.GetInt32("iUserID").ToString()))
            {
                if (_ProcessSrv.ResourceMaster_UpdateActiveUser(objRecoveryTaskStep.ReassignedTo, HttpContext.Session.GetInt32("iIncidentStepID") ?? 0))
                {
                    ValidMessageBody = objRecoveryTaskStep.ReassignedOwnerName + " has taken the responsibilty of performing Task" + objRecoveryTaskStep.StepName;//"Dear " + objRecoveryTaskStep.StepOwnerName + ", This is to inform you that " +

                    //BCPSms.SMSSend(objRecoveryTaskStep.StepOwnerMobileNo, objRecoveryTaskStep.StepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);

                    ValidMessageBody = objRecoveryTaskStep.ReassignedOwnerName + " has taken the responsibilty of performing Task" 
                        + objRecoveryTaskStep.StepName; //"Dear " + objRecoveryTaskStep.AltStepOwnerName + ", This is to inform you that " +
                   //BCPSms.SMSSend(objRecoveryTaskStep.AltStepOwnerMobileNo, objRecoveryTaskStep.AltStepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);

                    ExecutedBy = objRecoveryTaskStep.ReassignedTo;
                }
            }
            else
            {

                RecoveryTaskStepInfo obj = new RecoveryTaskStepInfo();
                obj.StepID = objRecoveryTaskStep.IncidentStepID;
                obj.PlanID = objRecoveryTaskStep.PlanID;
                obj.AltStepOwnerID = HttpContext.Session.GetInt32("iUserID").ToString();
                bool Success = _ProcessSrv.RecoveryTaskStepInfoUpdate_AltOwner(obj);
                if (Success)
                {
                    if (_ProcessSrv.ResourceMaster_UpdateActiveUser(HttpContext.Session.GetInt32("iUserID").ToString(), Convert.ToInt32(objRecoveryTaskStep.IncidentStepID)))
                    {
                        //bool notify = NotifyReassignedOwner(Convert.ToInt32(ResponseID), Convert.ToInt32(objTaskStepInfo.IncidentID));
                        //ValidMessageBody = "Dear " + objTaskStepInfo.StepOwnerName + ", " + Convert.ToString(_DataTable.Rows[0]["ResourceName"]) + " has taken the responsibilty of performing Task" + objTaskStepInfo.StepName;

                        //BCPSms.SMSSend(objTaskStepInfo.StepOwnerMobileNo, ValidMessageBody);

                        //ValidMessageBody = "Dear " + objTaskStepInfo.AltStepOwnerName + ", " + Convert.ToString(_DataTable.Rows[0]["ResourceName"]) + " has taken the responsibilty of performing Task" + objTaskStepInfo.StepName;
                        //BCPSms.SMSSend(objTaskStepInfo.ReassignedOwnerMobileNo, ValidMessageBody);
                        RecoveryTaskStepInfo objrecstep = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(HttpContext.Session.GetInt32("iIncidentStepID") ?? 0);
                        ValidMessageBody =  objrecstep.ReassignedOwnerName + " has taken the responsibilty of performing Task" + objrecstep.StepName;

                        //BCPSms.SMSSend(objrecstep.StepOwnerMobileNo, objrecstep.StepOwnerName, ValidMessageBody, objrecstep.OrgID);

                        ValidMessageBody =  objrecstep.ReassignedOwnerName + " has taken the responsibilty of performing Task" + objrecstep.StepName;
                        //BCPSms.SMSSend(objrecstep.AltStepOwnerMobileNo, objrecstep.AltStepOwnerName, ValidMessageBody, objrecstep.OrgID);

                        ExecutedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return ExecutedBy;
    }
}

