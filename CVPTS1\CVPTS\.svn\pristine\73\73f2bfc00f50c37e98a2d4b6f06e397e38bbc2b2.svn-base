﻿namespace BCM.UI.Areas.BCMReports.ReportModels.KPIReport;

public class KPIReportModel
{
    public int OrganizationId { get; set; }
    public int StatusId { get; set; }
    public int MetricId { get; set; }
    public int MeasurementId { get; set; }
    public int ModeId { get; set; }
    public int AnalysisId { get; set; }
    public int FrequencyId { get; set; }
    public string? ResourcesRequired { get; set; }
    public string? ResponsibleParties { get; set; }
    public string? EffectiveCriteria { get; set; }
    public string? RemarkEffectivenessLevel { get; set; }
    public double EffectiveRating { get; set; }
    public string? Effectiveness { get; set; }
    public int OverallKPI { get; set; }
    public string? Target { get; set; }
    public string? CorrectiveAction { get; set; }
    public string? CurrentRisk { get; set; }
    public string? Remarks { get; set; }
    public string? ITDRName { get; set; }
    public string? Objective { get; set; }
    public string? MeasureKPI { get; set; }
    public string? Rating { get; set; }
    public DateTime TargetedDate { get; set; }
    public string? MeasuredByName { get; set; }
}
