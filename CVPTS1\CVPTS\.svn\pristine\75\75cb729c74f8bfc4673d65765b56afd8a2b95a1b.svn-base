﻿
@{
    ViewBag.Title = "CheckListGroups";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">CheckList Groups </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
    <div class="row" style="width:80%">
         <div class="col">
                <div class="input-group">
                    <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                    <select class="form-select form-select-sm" aria-label="Default select example">
                        <option selected>Checklist Name</option>
                        <option value="1">list1</option>
                        <option value="2">list2</option>
                        <option value="3">list3</option>
                    </select>
                </div>
            </div>
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                <select class="form-select form-select-sm" aria-label="Default select example">
                    <option selected>All Organizations</option>
                    <option value="1">PTS</option>
                    <option value="2">TCS</option>
                    <option value="3">Continuity Vault</option>
                </select>
            </div>
        </div>
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
            <div class="col-auto p-0">
                <button type="button" class="btn icon-btn btn-primary btn-sm">Update</button>
            </div>
            <div class="col-auto">
                <button type="button" class="btn icon-btn btn-secondary btn-sm">Cancel</button>
            </div>           
        </div>      
    </div>
</div>
<div class="Page-Condant border-0 card">
    <div class="card-body" style="height:calc(100vh - 110px);overflow:auto;overflow-x:hidden">
        <h6 class="Sub-Title">Add Checklist Items</h6>
        <div class="my-3">
            <div class="border-bottom border-secondary-subtle pb-2">
                <div class="d-flex align-items-center justify-content-between ">
                    <span class="d-flex align-items-center gap-5">
                    <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                    <label class="form-check-label" for="flexCheckDefault">
                    01
                    </label>
                    </div>
                        <span class="fw-semibold">Determine recovery objectives (RTO & RPO)</span>
                    </span>
                    <span>
                        <span><i class="cv-edit me-1" role="button"></i><i role="button" class="cv-delete text-danger" data-bs-toggle="modal" data-bs-target="#DeleteModal"></i></span>
                    </span>
                </div>
                
            </div>
            <div class="border-bottom border-secondary-subtle pb-2 my-2">
                <div class="d-flex align-items-center justify-content-between ">
                    <span class="d-flex align-items-center gap-5">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                            <label class="form-check-label" for="flexCheckDefault">
                                02
                            </label>
                        </div>
                        <span class="fw-semibold">Identify stakeholders (such as engineers, technical support, and executives)</span>
                    </span>
                    <span>
                        <span><i class="cv-edit" title="Edit"></i><i class="cv-delete text-danger" title="Delete"></i></span>
                    </span>
                </div>
                
            </div>
            <div class="border-bottom border-secondary-subtle pb-2 my-2">
                <div class="d-flex align-items-center justify-content-between ">
                    <span class="d-flex align-items-center gap-5">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                            <label class="form-check-label" for="flexCheckDefault">
                                03
                            </label>
                        </div>
                        <span class="fw-semibold">Establish communication channels also set up dedicated communication channels and hubs</span>
                    </span>
                    <span>
                        <span><i class="cv-edit" title="Edit"></i><i class="cv-delete text-danger" title="Delete"></i></span>
                    </span>
                </div>
                
            </div>
        </div>
       
        <div class="my-3">
            <div class="border-bottom border-secondary-subtle pb-2">
                <div class="d-flex align-items-center justify-content-between ">
                    <span class="d-flex align-items-center gap-5">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                            <label class="form-check-label" for="flexCheckDefault">
                                04
                            </label>
                        </div>
                        <span class="fw-semibold">Collect all infrastructure documentation (with functioning devices and their configurations), the entire setup of systems and their usage (operating system (OS) and configuration, applications running,</span>
                    </span>
                    <span>
                        <span><i class="cv-edit" title="Edit"></i><i class="cv-delete text-danger" title="Delete"></i></span>
                    </span>
                </div>
                
            </div>
            <div class="border-bottom border-secondary-subtle pb-2 my-2">
                <div class="d-flex align-items-center justify-content-between ">
                    <span class="d-flex align-items-center gap-5">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                            <label class="form-check-label" for="flexCheckDefault">
                                05
                            </label>
                        </div>
                        <span class="fw-semibold">Choose the right technology</span>
                    </span>
                    <span>
                        <span><i class="cv-edit" title="Edit"></i><i class="cv-delete text-danger" title="Delete"></i></span>
                    </span>
                </div>
                
            </div>
            <div class="border-bottom border-secondary-subtle pb-2 my-2">
                <div class="d-flex align-items-center justify-content-between ">
                    <span class="d-flex align-items-center gap-5">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                            <label class="form-check-label" for="flexCheckDefault">
                                06
                            </label>
                        </div>
                        <span class="fw-semibold">
                            <div class="form-group">
                                
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Description" style="height:0px;width: 800px;"></textarea>
                                </div>
                            </div>
                        </span>
                    </span>
                    <span>
                        <span><i class="cv-edit" title="Edit"></i><i class="cv-delete text-danger" title="Delete"></i></span>
                    </span>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/delete.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->