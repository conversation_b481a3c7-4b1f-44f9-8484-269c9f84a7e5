﻿@model BCM.BusinessClasses.ManageUsersDetails

@{
    ViewBag.Title = "Login";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<section class="Login">
    <div class="container-fluid">
        <div class="row Login-bg vh-100">
            <div class="col-6 d-grid LoginLeft-bg align-items-end p-4">
                <img class="" src="~/img/Logo/Logo-Login.svg" height="80"/>

            </div>
            <div class="col-6 LoginRight-bg d-flex align-items-center justify-content-center">
                <div class="row justify-content-center h-100 align-items-end">
                    <div class="col-9">
                        <div class="text-center">
                            <img class="mb-4" src="~/img/Logo/Perpetuutit-Logo.svg" width="230" />
                        </div>
                        <form asp-action="Login">
                            <div class="mb-3" id="chkUserType">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="UserRole" 
                                           id="inlineRadio1" value="*" asp-for="UserRole">
                                    <label class="form-check-label" for="inlineRadio1">Product Admin</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="UserRole"
                                           id="inlineRadio2" value="**" asp-for="UserRole">
                                    <label class="form-check-label" for="inlineRadio2">Super Admin</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="UserRole"
                                           id="inlineRadio3" value="U" asp-for="UserRole" checked="checked">
                                    <label class="form-check-label" for="inlineRadio3">BCM User</label>
                                </div>
                            </div>
                            <div class="mb-3" id="UserNameContent">
                                <label class="form-label">User Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-user"></i></span>
                                    <input type="text" class="form-control" autocomplete="off" placeholder="Login Name" id="LoginName" asp-for="LoginName" tabindex="1">
                                </div>
                            </div>@* value="david.brown" *@
                            <div class="mb-3" id="PasswordContent">
                                <label class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-password"></i></span>
                                    <input type="password" class="form-control" autocomplete="off" placeholder="Enter Password" id="Password" asp-for="Password" tabindex="2">
                                </div>
                                <a href="@Url.Action("ForgetPassword", "ForgetPassword", new { area = "BCMAdministration" })">
                                    <div class="small text-end text-primary" tabindex="6">Forgot Password?</div>
                                   </a>
                                @* <div class="small text-end text-primary">Forgot Password?</div> *@@* value="admin@123" *@
                            </div>
                            <div class="mb-3" id="OrgCodeContent">
                                <label class="form-label">Organization Login Code</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-login-code"></i></span>
                                    <input type="password" class="form-control" autocomplete="off" placeholder="Organization Code" id="OrgCode" asp-for="OrganizationCode" tabindex="3">
                                </div>
                            </div>
                            <div class="mb-3" id="OrgGroupContent" style="display:none">
                                <label class="form-label">Organization Group Code</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-login-code"></i></span>
                                    <input type="password" class="form-control" autocomplete="off" placeholder="Organization Group Code" id="OrgCode" tabindex="4" asp-for="OrgGroupCode">
                                </div>
                            </div>
                            <div class="mb-3" id="DomainContent" style="display:none">
                                <label class="form-label">Domain</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-login-code"></i></span>
                                    <select autocomplete="off" disabled class="form-control" asp-for=DomainName id="ddlDomain" aria-label="Default select example">
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" asp-for="IsAdLogin"
                                           id="chkADLogin" tabindex="5">
                                    <label class="form-check-label" for="inlineRadio1">Login By Active Directory</label>
                                </div>
                            </div>
                            <div class="mb-3" style="display:none">
                                <label class="form-label">Captcha</label>
                                <div class="input-group captch_box">
                                    <span class="input-group-text" id="refresh" role="button"><i class="cv-description"></i></span>
                                    <input type="text" class="form-control refresh_button" name="captchaText" placeholder="Enter captcha text" autocomplete="off">
                                    <input type="hidden" name="captchaValue" id="captchaValue">
                                    <span class="input-group-text bg-primary text-white" id="textBox"><span id="captcha">captcha text</span></span>
                                    <span class="input-group-text" id="refresh" role="button"><i class="cv-refresh"></i></span>
                                </div>
                            </div>
                            <div class="d-grid col-6 mx-auto">
                                <button class="btn btn-primary btn-lg rounded-2" id="btnLogin" type="submit" tabindex="6">Login</button>
                                <button class="btn btn-primary btn-lg rounded-2" id="btnNext" style="display:none" type="button">Next</button>
                            </div>
                        </form>
                    </div>
                    <div class="col-12 text-center mb-3">
                        <small>Continuity Vault | © <script>document.write(new Date().getFullYear())</script> Perpetuuiti - All Rights Reserved.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>




<!-- Toast Containers for Login Messages -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <!-- Error Toast -->
    <div id="errorToast" class="toast bg-danger text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex" data-bs-theme="dark">
            <div class="toast-body">
                <div class="d-flex align-items-center gap-2">
                    <span><i class="cv-error align-middle fs-5"></i></span>
                    <span id="errorToastMessage">
                        Login Failed!
                    </span>
                </div>
            </div>
            <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>

    <!-- Success Toast -->
    <div id="successToast" class="toast bg-success text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex" data-bs-theme="dark">
            <div class="toast-body">
                <div class="d-flex align-items-center gap-2">
                    <span><i class="cv-success align-middle fs-5"></i></span>
                    <span id="successToastMessage">
                        Success!
                    </span>
                </div>
            </div>
            <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Function to show error toast
        function showErrorToast(message) {
            const errorToastElement = document.getElementById('errorToast');
            const errorMessageElement = document.getElementById('errorToastMessage');
            errorMessageElement.textContent = message;

            const errorToast = new bootstrap.Toast(errorToastElement, {
                delay: 5000 // Show for 5 seconds
            });
            errorToast.show();
        }

        // Function to show success toast
        function showSuccessToast(message) {
            const successToastElement = document.getElementById('successToast');
            const successMessageElement = document.getElementById('successToastMessage');
            successMessageElement.textContent = message;

            const successToast = new bootstrap.Toast(successToastElement, {
                delay: 4000 // Show for 4 seconds
            });
            successToast.show();
        }

        $(document).ready(function () {
            // Show error toast if there's an error message from server
            @if (ViewBag.ErrorMessage != null)
            {
                <text>
                showErrorToast('@Html.Raw(ViewBag.ErrorMessage.ToString().Replace("'", "\\'"))');
                </text>
            }

            @if (TempData["SessionExpMessage"] != null)
            {
                <text>
                    showErrorToast('@Html.Raw(TempData["SessionExpMessage"].ToString().Replace("'", "\\'"))');
                </text>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <text>
                showErrorToast('@Html.Raw(TempData["ErrorMessage"].ToString().Replace("'", "\\'"))');
                </text>
            }

            // Show success toast if there's a success message from server
            @if (ViewBag.SuccessMessage != null)
            {
                <text>
                showSuccessToast('@Html.Raw(ViewBag.SuccessMessage.ToString().Replace("'", "\\'"))');
                </text>
            }

            @if (TempData["SuccessMessage"] != null)
            {
                <text>
                showSuccessToast('@Html.Raw(TempData["SuccessMessage"].ToString().Replace("'", "\\'"))');
                </text>
            }

            // Existing login form functionality
            window.addEventListener('DOMContentLoaded', function () {
                    var captchaValue = document.getElementById('captcha').textContent.trim();
                    document.getElementById('captchaValue').value = captchaValue;
                });

                $('#inlineRadio1').change (function () {
                    $('#OrgGroupContent').show();
                    $('#OrgCodeContent').hide();
                });

                $('#inlineRadio2').change (function () {
                    $('#OrgGroupContent').show();
                    $('#OrgCodeContent').hide();
                });

                $('#inlineRadio3').change (function () {
                    $('#OrgGroupContent').hide();
                    $('#OrgCodeContent').show();
                });
                 $('#chkADLogin').change(function () 
                 {
                     $.ajax({
                        url: '@Url.Action("GetActiveDirectoryUser", "Login")',
                        type: 'GET',
                        data: { OrgCode: OrgCode
                        },
                        success: function (data) {
                            if (data) {
                                $('#DomainContent').show();
                                var ddlUnit = $('#ddlDomain');
                                    ddlUnit.empty();
                                    $.each(data, function (index, item) {
                                        ddlUnit.append('<option value="' + item.processName + '">' + item.processName + '</option>')
                                    });

                            } else {
                                // If there's an error, uncheck the box and show the error
                                $('#chkADLogin').prop('checked', false);
                                alert(result.message || 'Could not find Active Directory user.');
                            }
                            $('#LoginName').val('');
                            $('#Password').val('');
                            $('#UserNameContent').show();
                            $('#PasswordContent').show();
                            $('#btnLogin').show();
                            $('#btnNext').hide();

                        },
                        error: function (error) {
                            $('#chkADLogin').prop('checked', false);
                            alert('An error occurred while retrieving Active Directory user information.');
                            console.error('Error:', error);
                        }
                    });


                        // if ($(this).prop('checked') == true) {
                        //     // Clear the form fields first
                        //     $('#UserNameContent').hide();
                        //     $('#PasswordContent').hide();
                        //     $('#chkUserType').hide();
                        //     $('#btnLogin').hide();
                        //     $('#btnNext').show();
                        //     $('#OrgGroupContent').show();
                        //     $('#OrgCodeContent').hide();
                        // } else {

                        //     // When unchecked, clear the form or reset to default values
                        //     $('#UserNameContent').show();
                        //     $('#PasswordContent').show();
                        //     $('#btnLogin').show();
                        //     $('#btnNext').hide();
                        //     $('#chkUserType').show();
                        //     $('#DomainContent').hide();
                        //     $('#OrgGroupContent').Hide();
                        //      $('#OrgCodeContent').show();
                        // }
                });

                    $('#btnNext').click(function(){


                        var OrgCode = $('#OrgCode').val();
                    //Call the controller to get AD user info
                    $.ajax({
                        url: '@Url.Action("GetActiveDirectoryUser", "Login")',
                        type: 'GET',
                        data: { OrgCode: OrgCode
                        },
                        success: function (data) {
                            if (data) {
                                $('#DomainContent').show();
                                var ddlUnit = $('#ddlDomain');
                                    ddlUnit.empty();
                                    $.each(data, function (index, item) {
                                        ddlUnit.append('<option value="' + item.processName + '">' + item.processName + '</option>')
                                    });

                            } else {
                                // If there's an error, uncheck the box and show the error
                                $('#chkADLogin').prop('checked', false);
                                alert(result.message || 'Could not find Active Directory user.');
                            }
                            $('#LoginName').val('');
                            $('#Password').val('');
                            $('#UserNameContent').show();
                            $('#PasswordContent').show();
                            $('#btnLogin').show();
                            $('#btnNext').hide();

                        },
                        error: function (error) {
                            $('#chkADLogin').prop('checked', false);
                            alert('An error occurred while retrieving Active Directory user information.');
                            console.error('Error:', error);
                        }
                    });
                    });
        }); // End of document.ready
    </script>
}
