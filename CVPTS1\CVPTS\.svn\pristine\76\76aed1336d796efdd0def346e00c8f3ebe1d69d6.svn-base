﻿@model IEnumerable<BCM.BusinessClasses.BusinessProcessInfo>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using BCM.Shared

@{
    int iIndex = 1;
    string RPOText = string.Empty;
}

@* <hr /> *@
<div class="card border-0">
    <div class="card-body p-0">
        <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
            <thead>
                <tr>
                    <th class="SrNo_th">#</th>
                    <th>Name</th>
                    <th>Owner&nbsp;Details </th>
                    <th>Unit</th>
                    <th>Department</th>
                    <th>Sub Department</th>
                    @* <th tyle="display:none">Org&nbsp;Level </th> *@
                    @*  <th>RTO/MAO/RPO</th> *@
                    <th>RTO</th>
                    <th class="text-center">IsCritical</th>
                    <th class="text-center">Status</th>
                    @*  <th>View&nbsp;BIA</th> *@
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @if (ViewBag.Vendor != null && ((IEnumerable<BCM.BusinessClasses.BusinessProcessInfo>)ViewBag.Vendor).Any())
                {
                    @foreach (var item in ViewBag.Vendor)
                    {
                        <tr>
                            <td>@iIndex</td>
                            <td>
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item fw-semibold" style="display:none"><span role="button" data-bs-toggle="modal" data-bs-target="#SummaryModal"> @item.ProcessCode</span></li>
                                    <li class="list-group-item ">@item.ProcessName</li>

                                    @if (Convert.ToString(@item.ProcessCode) == "" || Convert.ToString(@item.ProcessCode) == null)
                                    {
                                        <li class="list-group-item text-secondary" style="display:none">Is Under BCM Scope: <span class="text-warning"> No</span></li>
                                    }
                                    else
                                    {
                                        <li class="list-group-item text-secondary" style="display:none">Is Under BCM Scope: <span class="text-success">Yes</span></li>
                                    }
                                </ul>
                            </td>
                            <td>
                                <table>
                                    <tbody>
                                        <tr>
                                            <td class="fw-semibold"><i class="cv-user" style="display:none"></i></td>
                                            <td style="display:none"> : </td>
                                            <td>
                                                @if (@item.ProcessOwner == "" || @item.ProcessOwner == null)
                                                {
                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                }
                                                else
                                                {
                                                    @item.ProcessOwner
                                                }
                                            </td>
                                        </tr>
                                        <tr style="display:none">
                                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                                            <td>:</td>
                                            <td>@item.OwnerEmail</td>
                                        </tr>
                                        <tr style="display:none">
                                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                                            <td>:</td>
                                            <td>@item.ProcessOwnerMobile</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tbody>
                                        <tr title="Unit">
                                            <td class="fw-semibold"><i class="cv-unit" style="display:none"></i> </td>
                                            <td style="display:none">:</td>
                                            <td>
                                                @if (@item.UnitName == "" || @item.UnitName == null)
                                                {
                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                }
                                                else
                                                {
                                                    @item.UnitName
                                                }
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tbody>
                                        <tr title="Department">
                                            <td class="fw-semibold"><i class="cv-department" style="display:none"></i> </td>
                                            <td style="display:none">:</td>
                                            <td>
                                                @if (@item.DepartmentName == "" || @item.DepartmentName == null)
                                                {
                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                }
                                                else
                                                {
                                                    @item.DepartmentName
                                                }
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tbody>
                                        <tr title="Sub Department">
                                            <td class="fw-semibold"><i class="cv-subdepartment" style="display:none"></i> </td>
                                            <td style="display:none">:</td>
                                            <td>
                                                @if (@item.SubFunctionName == "" || @item.SubFunctionName == null)
                                                {
                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                }
                                                else
                                                {
                                                    @item.SubFunctionName
                                                }
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            @* <td style="display:none">
                            <ul class="ps-0 mb-0">
                                <li class="list-group-item"><i class="cv-organization me-1"></i>@item.OrgName</li>
                                <li class="list-group-item"><i class="cv-unit me-1"></i>@item.UnitName</li>
                                <li class="list-group-item"><i class="cv-department me-1"></i>@item.DepartmentName</li>
                                <li class="list-group-item"><i class="cv-subdepartment me-1"></i>@item.SubFunctionName</li>
                            </ul>
                        </td> *@
                            <td>
                                <table>
                                    <tbody>
                                        <tr style="display:none;">
                                            <td class="text-secondary"><i class="cv-user me-1"></i>User RTO</td>
                                            <td> : </td>
                                            <td>@item.OwnerRTO</td>
                                        </tr>
                                        <tr style="display:none;">
                                            <td class="text-secondary"><i class="cv-user me-1"></i>User MAO</td>
                                            <td> : </td>
                                            <td>@item.OwnerMTR</td>
                                        </tr>
                                        @*  <tr>
                                        <td class="text-secondary"><i class="cv-calculated me-1"></i>Calculated RTO</td>
                                        <td> : </td>
                                        <td>@item.RTO</td>
                                    </tr> *@

                                        <tr title="Calculated RTO">
                                            <td><i class="cv-RTO me-1" style="display:none"></i>@*RTO*@ </td>
                                            <td style="display:none"> : </td>
                                            <td>
                                                @{
                                                    if (item.OwnerRTO == "0" || item.OwnerRTO == null)
                                                    {
                                                        <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                    }
                                                    else
                                                    {
                                                        var RTO = Utilities.GetFormattedRTO(Convert.ToInt32(@item.OwnerRTO));
                                                        <span>@RTO</span>
                                                    }
                                                }
                                            </td>
                                        </tr>

                                        <tr style="display:none;">
                                            <td class="text-secondary"><i class="cv-calculated me-1"></i>Calculated MAO</td>
                                            <td> : </td>
                                            <td>@item.MTR</td>
                                        </tr>
                                        <tr style="display:none;">
                                            <td class="text-secondary"><i class="cv-rpo me-1"></i>RPO</td>
                                            <td> : </td>
                                            <td>@item.RPO</td>
                                        </tr>
                                    </tbody>
                                </table>

                            </td>

                            @if (Convert.ToString(@item.IsCritical) == "0")
                            {
                                <td class="text-success">
                                    No
                                </td>
                            }
                            else
                            {
                                <td class="text-danger">
                                    Yes
                                </td>
                            }

                            <td>
                                @{
                                    int statusId = Convert.ToInt32(@item.Status);
                                }
                                <span class="d-flex align-items-center @BCM.Shared.Utilities.ApprovalStatusWiseTextClass(statusId)">
                                    <i class="@BCM.Shared.Utilities.ApprovalStatusWiseClass(statusId) me-2"></i>
                                    @BCM.Shared.Utilities.ApprovalStatus(statusId)
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="d-flex align-items-center gap-2">

                                    <div class="btn-action">
                                        @if (@item.ProcessCode == string.Empty)
                                        {
                                            <a class="text-dark" asp-action="BusinessProcessForm" asp-area="BCMProcessBIA" asp-controller="BusinessProcessForm" asp-route-strEntityTypeID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.EntityTypeID.ToString())" asp-route-strRecordID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.RecordID.ToString())" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.ProcessID.ToString())">
                                                @* <span class="cv-page-name fs-6 ViewBIA" type="button"></span> *@
                                                <i class="cv-page-name align-middle ViewBIA" type="button" title="View BIA"></i>
                                            </a>
                                        }
                                        else
                                        {
                                            <a class="text-dark" asp-action="PerformProcessBIA" asp-area="BCMProcessBIA" asp-controller="PerformProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.ProcessID.ToString())">
                                                @*  <span class="cv-page-name fs-6 ViewBIA" type="button"></span> *@
                                                <i class="cv-page-name align-middle ViewBIA" type="button" title="View BIA"></i>
                                            </a>
                                        }
                                    </div>

                                    <div class="dropdown dropstart">
                                        <span class="btn-action" role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false"><i class="cv-activity-details" title="View Details"></i></span>
                                        <div class="dropdown-menu border-0">
                                            @*  <h6 class="dropdown-header fw-semibold text-dark pb-0">Ptech Pune LTD</h6> *@
                                            <table class="table mb-0 table-borderless">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="table table-sm mb-0 table-borderless">
                                                                <tbody>
                                                                    <tr>
                                                                        <th class="fw-semibold text-primary" colspan="3">Vendor Details</th>
                                                                    </tr>
                                                                    <tr>
                                                                        <td><i class="cv-process-code" title="Code"></i></td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @if (@item.ProcessCode == "" || @item.ProcessCode == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                <span class="fw-semibold text-warning">  ( @item.ProcessCode )</span>
                                                                            }
                                                                        </td>

                                                                    </tr>
                                                                    <tr>
                                                                        <td><i class="cv-version" title="Version"></i></td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @if (@item.Version == "" || @item.Version == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                <span class="fw-semibold text-warning">  ( @item.Version )</span>
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td><i class="cv-bcm-scope" title="Is Under BCM Scope"></i></td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @if (@item.ProcessCode != "")
                                                                            {
                                                                                <span class="text-success fw-semibold">
                                                                                    Yes
                                                                                </span>
                                                                            }
                                                                            else
                                                                            {
                                                                                <span class="text-danger fw-semibold">
                                                                                    No
                                                                                </span>
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>

                                                        <td>
                                                            <table class="table table-sm mb-0 table-borderless">
                                                                <tbody>
                                                                    <tr>
                                                                        <th class="fw-semibold text-primary" colspan="3">Owner Detail</th>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                                                        <td> : </td>
                                                                        <td>
                                                                            @if (@item.ProcessOwner == "" || @item.ProcessOwner == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @item.ProcessOwner
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @if (@item.OwnerEmail == "" || @item.OwnerEmail == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @item.OwnerEmail
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @if (@item.ProcessOwnerMobile == "" || @item.ProcessOwnerMobile == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @item.ProcessOwnerMobile
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <td>
                                                            <table class="table table-sm mb-0 table-borderless">
                                                                <tbody>
                                                                    <tr>
                                                                        <th class="fw-semibold text-primary" colspan="3">Approver Detail</th>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-user"></i></td>
                                                                        <td> : </td>
                                                                        <td>
                                                                            @if (@item.ApproverName == "" || @item.ApproverName == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @item.ApproverName
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                    <tr>

                                                                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @if (@item.ApproverEmail == "" || @item.ApproverEmail == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @item.ApproverEmail
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                                        <td>:</td>
                                                                        <td>

                                                                            @if (@item.ApproverMobile == "" || @item.ApproverMobile == null)
                                                                            {
                                                                                <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @item.ApproverMobile
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>

                                                        <td>
                                                            <table class="table table-sm mb-0 table-borderless">
                                                                <tbody>
                                                                    <tr>
                                                                        <th class="fw-semibold text-primary" colspan="3">RTO/MAO/RPO</th>
                                                                    </tr>
                                                                    <tr title="RTO">
                                                                        <td class="text-secondary"><i class="cv-RTO me-1"></i> RTO </td>
                                                                        <td> : </td>
                                                                        <td>
                                                                            @{
                                                                                if (item.OwnerRTO == "0" || item.OwnerRTO == null)
                                                                                {
                                                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                                }
                                                                                else
                                                                                {
                                                                                    var RTO = Utilities.GetFormattedRTO(Convert.ToInt32(item.OwnerRTO));
                                                                                    <span>@RTO</span>
                                                                                    // class= "text-danger fw-semibold"
                                                                                }
                                                                            }

                                                                        </td>
                                                                    </tr>
                                                                    <tr title="Calculated MAO">
                                                                        <td class="text-secondary"><i class="cv-RTO me-1"></i>MAO </td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @{
                                                                                if (item.MTR == "0" || item.MTR == null)
                                                                                {
                                                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                                }
                                                                                else
                                                                                {
                                                                                    var MAO = Utilities.GetFormattedRTO(Convert.ToInt32(@item.MTR));
                                                                                    <span>@MAO</span>
                                                                                    //class="text-danger fw-semibold"
                                                                                }
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                    <tr title="RPO">
                                                                        <td class="text-secondary"><i class="cv-rpo me-1"></i>RPO </td>
                                                                        <td>:</td>
                                                                        <td>
                                                                            @{
                                                                                var a = item.RPO;
                                                                                if (item.RPO == "0" || item.RPO == null)
                                                                                {
                                                                                    <span><i class="text-danger cv-na" title="Not Available"></i></span>
                                                                                }
                                                                                else
                                                                                {
                                                                                    Utilities.GetFormattedRPO(Convert.ToInt32(item.RPO));
                                                                                    var RPO = Utilities.GetFormattedRPO(Convert.ToInt32(@item.RPO));
                                                                                    <span>
                                                                                        @RPO
                                                                                    </span>
                                                                                    //class= "text-danger fw-semibold"
                                                                                }
                                                                            }
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>



                                                </tbody>
                                            </table>
                                        </div>
                                    </div>



                                    <span class="btn-action btnEdit" id="btnEdit" type="button" data-id="@item.RecordID"><i class="cv-edit me-1" title="Edit"></i></span>
                                    <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-id="@item.RecordID" data-Text="@item.ProcessName"><i class="cv-delete text-danger" title="Delete"></i></span>
                                    @* <a href="#" data-bs-toggle="offcanvas" data-bs-target="#offcanvasExample" role="button" aria-controls="offcanvasExample"><i class="btn btn-outline-primary btn-sm cv-view me-1"></i></a> *@
                                </div>
                            </td>

                        </tr>
                        iIndex++;
                    }

                }
                else
                {
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <div class="d-flex flex-column align-items-center">
                                <img src="~/img/Isomatric/no_records_found.svg" alt="No Records Found" style="width: 120px; height: auto; margin-bottom: 1rem;">
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
@* @{
    int iIndex = 0;
    string RPOText = string.Empty;
    foreach (var item in Model)
    {
        iIndex++;

        <tr>
            <td>@iIndex</td>
            <td>
                <ul class="ps-0 mb-0">
                    <li class="list-group-item fw-semibold"><a class="text-primary" href="#" data-bs-toggle="modal" data-bs-target="#SummaryModal"> @item.ProcessCode</a></li>
                    <li class="list-group-item fw-semibold">@item.ProcessName</li>
                    @if (Convert.ToString(@item.ProcessCode) == "" || Convert.ToString(@item.ProcessCode) == null)
                    {
                        <li class="list-group-item">Is Under BCM Scope: <span class="text-warning"> No</span></li>
                    }
                    else
                    {
                        <li class="list-group-item">Is Under BCM Scope: <span class="text-success">Yes</span></li>
                    }
                </ul>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr>
                            <td class="fw-semibold"><i class="cv-user"></i></td>
                            <td> : </td>
                            <td>@item.ProcessOwner</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                            <td>:</td>
                            <td><a class="text-primary" href="#">@item.OwnerEmail</a></td>
                        </tr>
                        <tr>
                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                            <td>:</td>
                            <td>@item.ProcessOwnerMobile</td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                <ul class="ps-0 mb-0">
                    <li class="list-group-item"><i class="cv-organization me-1"></i>@item.OrgName</li>
                    <li class="list-group-item"><i class="cv-unit me-1"></i>@item.UnitName</li>
                    <li class="list-group-item"><i class="cv-department me-1">@item.DepartmentName</i></li>
                    <li class="list-group-item"><i class="cv-subdepartment me-1">@item.SubFunctionName</i></li>
                </ul>
            </td>
            <td>
                <table>
                    <tbody>
                        <tr>
                            <td><i class="cv-user me-1"></i>User RTO</td>
                            <td> : </td>
                            <td>@item.OwnerRTO</td>
                        </tr>
                        <tr>
                            <td><i class="cv-user me-1"></i>User MAO</td>
                            <td> : </td>
                            <td>@item.OwnerMTR</td>
                        </tr>
                        <tr>
                            <td><i class="cv-calculated me-1"></i>Calculated RTO</td>
                            <td> : </td>
                            <td>@item.RTO</td>
                        </tr>
                        <tr>
                            <td><i class="cv-calculated me-1"></i>Calculated MAO</td>
                            <td> : </td>
                            <td>@item.MTR</td>
                        </tr>
                        <tr>
                            <td><i class="cv-rpo me-1"></i>RPO</td>
                            <td> : </td>
                            <td>@item.RPO</td>
                        </tr>
                    </tbody>
                </table>
            </td>

            @if (Convert.ToString(@item.IsCritical) == "0")
            {
                <td class="text-success">
                    No
                </td>
            }
            else
            {
                <td class="text-danger">
                    Yes
                </td>
            }


            <td>
                @if (Convert.ToString(@item.Status) == "0")
                {
                    <p class="text-info">
                        <i class="cv-initiated me-1"></i>
                        Initiated
                    </p>
                }
                @if (Convert.ToString(@item.Status) == "1")
                {
                    <p class="text-warning">
                        <i class="cv-waiting me-1"></i>
                        WaitingForApproval
                    </p>
                }
                @if (Convert.ToString(@item.Status) == "2")
                {
                    <p class="text-success">
                        <i class="cv-success me-1"></i>
                        Approved
                    </p>

                }
                @if (Convert.ToString(@item.Status) == "3")
                {
                    <p class="text-danger">
                        <i class="cv-error me-1"></i>
                        DisApproved
                    </p>
                }
            </td>
            <td>
                <a asp-action="BusinessProcessForm" asp-area="BCMProcessBIA" asp-controller="BusinessProcessForm" asp-route-strEntityTypeID="@BCM.Security.Helper.CryptographyHelper.Encrypt(Convert.ToString((int)BCPEnum.EntityType.ThirdParty))" asp-route-strRecordID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.RecordID.ToString())" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.ProcessID.ToString())">
                    <span class="cv-view fs-6 ViewBIA" type="button"></span>
                </a>
            </td>
            <td>
                <span class="btn-action btnEdit" id="btnEdit" type="button" data-id="@item.RecordID"><i class="cv-edit" title="Edit"></i></span>
                <span class="btn-action btnDelete" id="btnDelete" type="button" data-bs-toggle="modal" data-id="@item.RecordID" data-Text="@item.ProcessName" data-EntityID="@item.EntityTypeID" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
            </td>
        </tr>
    }
} *@