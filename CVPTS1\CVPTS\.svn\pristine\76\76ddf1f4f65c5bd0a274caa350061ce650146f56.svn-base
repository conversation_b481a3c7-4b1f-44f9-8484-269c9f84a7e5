﻿@model BCM.BusinessClasses.ResourcesInfo
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="wizard-content">
    <form id="editResourceForm" class="tab-wizard wizard-circle wizard clearfix example-form needs-validation progressive-validation" novalidate>
        <h6>
            <span class="step"><i class="cv-edit"></i></span>
            <span class="step_title">Configure Resource</span>
        </h6>
        <section>
            <div class="row row-cols-2">
                <div class="col">
                    <div class="form-group">
                        <label class="form-label required-field">Org Group</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization-group-name"></i></span>
                            <select class="form-select form-control" id="OrgGroupID" name="OrgGroupID" asp-for="OrgGroupID" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.orgGroupInfoList,"OrgGroupID","OrganizationGroupName"))" required>
                                <option selected disabled value="">Select Organization Group</option>
                            </select>
                        </div>
                        <div class="invalid-feedback">Select Org Group</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label required-field">Organization</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-organization"></i></span>
                            <select class="form-select form-control ddlOrganization" id="OrgID" name="OrgID" asp-for="OrgID" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.orgInfo,"Id","OrganizationName"))" required>
                                <option selected disabled value="">Select Organization</option>
                            </select>
                        </div>
                        <div class="invalid-feedback">Select Organization</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Unit</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-unit"></i></span>
                            <select class="form-select form-control ddlUnit" id="UnitID" name="UnitID" asp-for="UnitID" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.unit,"UnitID","UnitName"))">
                                <option selected disabled value="">-- All Units --</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Department</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-department"></i></span>
                            <select class="form-select form-control ddlDepartment" id="DepartmentID" name="DepartmentID" asp-for="DepartmentID" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.department,"DepartmentID","DepartmentName"))">
                                <option selected disabled value="">-- All Departments --</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label class="form-label">SubDepartment</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                            <select class="form-select form-control ddlSubDepartment" id="SubFunctionID" name="SubFunctionID" asp-for="SubFunctionID" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.SubDepartment,"SubFunctionID","SubFunctionName"))">
                                <option selected disabled value="">-- All SubDepartments --</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Resource Type</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-type"></i></span>
                            <select class="form-select form-control" id="ResourceTypeID" name="ResourceTypeID" asp-for="ResourceTypeID" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Type,"TypeID","TypeName"))" required>
                                <option selected disabled value="">-- All Types --</option>
                            </select>
                        </div>
                        <div class="invalid-feedback">Select Resource Type</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Company Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-company"></i></span>
                            <select class="form-select form-control" autocomplete="off" id="VendorID" name="VendorID" asp-for="VendorID" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.CompanyList,"CompanyID","CompanyName"))">
                                <option selected disabled value="">Select Companies</option>
                            </select>
                        </div>
                        <div class="invalid-feedback">Select Company Name</div>
                    </div>
                </div>
            </div>
        </section>
        <h6>
            <span class="step"><i class="cv-user"></i></span>
            <span class="step_title">User Information</span>
        </h6>
        <section>
            <div class="row row-cols-2">
                <div class="col">
                    <div class="form-group">
                        <label class="form-label required-field">Title</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-name"></i></span>
                            <select class="form-select form-select-sm" id="ResourceTitle" name="ResourceTitle" asp-for="ResourceTitle" required>
                                <option selected disabled value="">Select Title</option>
                                <option value="1">Mr</option>
                                <option value="2">Mrs</option>
                                <option value="3">MS</option>
                            </select>
                        </div>
                        <div class="invalid-feedback">Select Title</div>
                    </div>
                    <div class="form-group">
                        <label for="validationCustom01" class="form-label">Middle Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-name"></i></span>
                            <input type="text" id="MName" asp-for="MName" name="MName" class="form-control" placeholder="Enter Middle Name">
                        </div>
                        <div class="invalid-feedback">Enter Middle Name</div>
                    </div>
                    <div class="form-group">
                        <label for="validationCustom01" class="form-label required-field">City</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-corporate-address"></i></span>
                            <input type="text" id="City" name="City" asp-for="City" class="form-control" placeholder="Enter City" required pattern="[A-Za-z\s]+" title="Please enter alphabetic characters only.">
                        </div>
                        <div class="invalid-feedback">Enter City</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Designation </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-corporate-address"></i></span>
                            <select class="form-select form-control" id="DesignationId" name="DesignationId" asp-for="DesignationId" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ResourceDesignation,"ID","Designation"))">
                                <option selected value="0">-- All Designations --</option>
                            </select>
                            <button type="button" class="btn btn-outline-primary input-group-text" onclick="openDesignationModal()" title="Add/Manage Designations" style="border-left: 0;">
                                <i class="fa fa-plus" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="validationCustom01" class="form-label required-field">First Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-name"></i></span>
                            <input type="text" class="form-control" id="FName" asp-for="FName" name="FName" required pattern="[A-Za-z\s]+" title="Please enter alphabetic characters only." placeholder="Enter First Name">
                        </div>
                        <div class="invalid-feedback">Enter First Name</div>
                    </div>
                    <div class="form-group">
                        <label for="validationCustom01" class="form-label required-field">Last Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-name"></i></span>
                            <input type="text" class="form-control" id="LName" asp-for="LName" name="LName" placeholder="Enter Last Name" required pattern="[A-Za-z\s]+" title="Please enter alphabetic characters only.">
                        </div>
                        <div class="invalid-feedback">Enter Last Name</div>
                    </div>
                    <div class="form-group">
                        <label for="validationCustom01" class="form-label required-field">State</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-corporate-address"></i></span>
                            <input type="text" class="form-control" id="State" asp-for="State" name="State" placeholder="Enter State" required pattern="[A-Za-z\s]+" title="Please enter alphabetic characters only.">
                        </div>
                        <div class="invalid-feedback">Enter State</div>
                    </div>
                    <div class="form-group">
                        <label for="validationCustom01" class="form-label">Pin code</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-login-code"></i></span>
                            <input type="text" class="form-control" id="PinCode" asp-for="PinCode" name="PinCode" placeholder="Enter Pin code" pattern="\d{6}" title="Please enter a valid 6-digit pin code.">
                        </div>
                        <div class="invalid-feedback">Enter Pin code</div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="validationCustom01" class="form-label">Description</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-description"></i></span>
                        <input type="text" class="form-control" id="Comments" asp-for="Comments" name="Comments" placeholder="Enter Description">
                    </div>
                    <div class="invalid-feedback">Enter Description</div>
                </div>
            </div>
        </section>
        <h6>
            <span class="step"><i class="cv-info"></i></span>
            <span class="step_title">Contact Information</span>
        </h6>
        <section>
            <div class="row row-cols-2">
                <div class="col">
                    <div class="form-group">
                        <label class="form-label required-field">Business Phone</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-phone"></i></span>
                            <input type="tel" class="form-control" id="BusinessPhone" asp-for="BusinessPhone" name="BusinessPhone" placeholder="Enter Business Phone" required pattern="\d{10}" title="Please enter a valid 10-digit phone number.">
                        </div>
                        <div class="invalid-feedback">Enter Business Phone</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label required-field">Mobile Phone</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-phone"></i></span>
                            <input type="tel" class="form-control" id="MobilePhone" asp-for="MobilePhone" name="MobilePhone" placeholder="Enter Mobile Phone" required pattern="\d{10}" title="Please enter a valid 10-digit mobile number.">
                        </div>
                        <div class="invalid-feedback">Enter Mobile Phone</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label required-field">Company Mail</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-mail"></i></span>
                            <input type="email" class="form-control" id="CompanyEmail" asp-for="CompanyEmail" name="CompanyEmail" placeholder="Enter Company Mail" required>
                        </div>
                        <div class="invalid-feedback">Enter Company Mail</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Fax</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-fax"></i></span>
                            <input type="tel" class="form-control" id="Fax" asp-for="Fax" name="Fax" placeholder="Enter Fax" pattern="\d{10,15}" title="Please enter a valid fax number (10 to 15 digits).">
                        </div>
                        <div class="invalid-feedback">Enter Fax</div>
                    </div>
                    <div class="form-group" style="display:none">
                        <label class="form-label required-field">Effective Start Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-review-date"></i></span>
                            <input type="date" id="EffectiveStartDate" asp-for="EffectiveStartDate" name="EffectiveStartDate" class="form-control" required>
                        </div>
                        <div class="invalid-feedback">Effective Start Date</div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label class="form-label">Home Phone</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-phone"></i></span>
                            <input type="tel" class="form-control" id="HomePhone" asp-for="HomePhone" name="HomePhone" placeholder="Enter Home Phone" pattern="\d{10}" title="Please enter a valid 10-digit phone number.">
                        </div>
                        <div class="invalid-feedback">Enter Home Phone</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Other Phone</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-phone"></i></span>
                            <input type="tel" class="form-control" id="OtherPhone" asp-for="OtherPhone" name="OtherPhone" placeholder="Enter Other Phone" pattern="\d{10}" title="Please enter a valid 10-digit phone number.">
                        </div>
                        <div class="invalid-feedback">Enter Other Phone</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Other Email</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-mail"></i></span>
                            <input type="email" class="form-control" id="OtherEmail" asp-for="OtherEmail" name="OtherEmail" placeholder="Enter Other Email">
                        </div>
                        <div class="invalid-feedback">Enter Other Email</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Address</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-corporate-address"></i></span>
                            <input type="text" class="form-control" id="Address" asp-for="Address" name="Address" placeholder="Enter Address">
                        </div>
                        <div class="invalid-feedback">Enter Address</div>
                    </div>
                    <div class="form-group" style="display:none">
                        <label class="form-label">Effective End Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cv-review-date"></i></span>
                            <input type="date" class="form-control" id="EffectiveEndDate" name="EffectiveEndDate" asp-for="EffectiveEndDate" required>
                        </div>
                        <div class="invalid-feedback">Effective End Date</div>
                    </div>
                </div>
            </div>
        </section>
        <h6>
            <span class="step"><i class="cv-description"></i></span>
            <span class="step_title">User Role Section</span>
        </h6>
        <section>
            <div style="height:calc(100vh - 320px);overflow:auto">

                <table class="table table-striped table-sm">
                    <thead class="position-sticky top-0 z-3">
                        <tr>

                            <th scope="col">User Role</th>
                            <th scope="col">
                                Select Role
                            </th>
                        </tr>
                    </thead>

                    <tbody>

                        @foreach (BCM.BusinessClasses.MenuRoleRights item in ViewBag.Roleslist)
                        {
                            bool roles = false;

                            // First try to check Model.role (List<int>)
                            if (Model.role != null && Model.role.Any())
                            {
                                roles = Model.role.Contains(item.RoleID);
                            }
                            // Fallback: check Model.UserRole (string) if Model.role is empty
                            else if (!string.IsNullOrEmpty(Model.UserRole))
                            {
                                try
                                {
                                    var userRoleIds = Model.UserRole.Split(',')
                                    .Where(r => !string.IsNullOrWhiteSpace(r))
                                    .Select(r => Convert.ToInt32(r.Trim()))
                                    .ToList();
                                    roles = userRoleIds.Contains(item.RoleID);
                                }
                                catch
                                {
                                    roles = false;
                                }
                            }

                            <input type="hidden" id="<EMAIL>" value="@item.RoleID" />

                            <tr>
                                <td>@item.RoleName</td>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input" name="role" value="@item.RoleID" id="<EMAIL>" type="radio" @(roles ? "checked" : "") required>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
                <div class="invalid-feedback">Please Select Role</div>
            </div>
            <div class="form-group">
                <label class="form-label">Upload Photo</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-upload"></i></span>
                    <input type="file" class="form-control" id="">
                </div>
                <div class="invalid-feedback">Upload Photo</div>
            </div>
        </section>
    </form>
</div>

<!-- Designation Management Modal -->
<div class="modal fade" id="designationModal" tabindex="-1" aria-labelledby="designationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>


<!-- Scripts are already loaded in the main layout, no need to reload them here -->

<script>
    // Function to initialize validation - will be called from main page after modal loads
    window.initializeAddBCMResourceValidation = function() {
        console.log("initializeAddBCMResourceValidation called!");
        console.log("Form exists at start:", !!document.getElementById('editResourceForm'));

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("initializeValidation called");

            if (window.BCMValidation) {
                console.log("BCMValidation is available");

                // Get the form element
                const form = document.getElementById('editResourceForm');
                console.log("Form element found:", !!form);

                if (!form) {
                    console.error("Form not found with ID: editResourceForm");
                    return;
                }

                console.log("Form found, proceeding with validation setup");

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                            console.log("Input details - ID:", input.id, "Name:", input.name, "asp-for:", input.getAttribute('asp-for'), "pattern:", input.pattern);
                        }
                    }
                });



                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateEmail function to show proper email validation messages
                const originalValidateEmail = window.BCMValidation.validateEmail;
                window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                    console.log("validateEmail called for:", input.id || input.name, "value:", input.value);

                    // Get the result from the original function
                    const result = originalValidateEmail(input, forceValidation);
                    console.log("Email validation result:", result);

                    // If email validation fails, show appropriate message
                    if (!result) {
                        const formGroup = input.closest('.form-group');
                        const feedbackElement = formGroup?.querySelector('.invalid-feedback');

                        if (feedbackElement) {
                            console.log("Current feedback text before email restore:", feedbackElement.textContent);

                            // For empty required email fields, show custom message
                            if (input.value === '' && input.hasAttribute('required')) {
                                const key = input.id || input.name || input.getAttribute('asp-for');
                                if (key && customMessages[key]) {
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom required message for email", key, ":", customMessages[key]);
                                }
                            }
                            // For invalid email format, show specific email format error message
                            else if (input.value !== '') {
                                feedbackElement.textContent = "Please enter a valid email address (e.g., <EMAIL>)";
                                feedbackElement.style.display = 'block';
                                console.log("Set email format validation message");
                            }

                            console.log("Feedback text after email restore:", feedbackElement.textContent);
                        }
                    }

                    return result;
                };

                // Simple override for fields with both pattern and custom validation
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                    // For fields with custom validation active, skip pattern validation
                    if ((input.id === 'MobilePhone' || input.id === 'CompanyEmail') &&
                        input.hasAttribute('data-custom-validation-active')) {
                        return true; // Skip pattern validation when custom validation is handling it
                    }
                    return originalValidatePatternInput(input, forceValidation);
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom form message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);



                // Add user interaction validation for all required fields
                const requiredFields = form.querySelectorAll('[required]');
                console.log("Found", requiredFields.length, "required fields");

                requiredFields.forEach(function(field, index) {
                    console.log("Setting up event listeners for field", index + 1, ":", field.id || field.name, "type:", field.type);

                    // Add input event listener for real-time validation
                    field.addEventListener('input', function() {
                        debugger;
                        console.log("INPUT EVENT triggered for:", this.id || this.name, "value:", this.value);
                        window.BCMValidation.validateInput(this);
                    });

                    // Add blur event listener for validation when field loses focus
                    field.addEventListener('blur', function() {
                        console.log("BLUR EVENT triggered for:", this.id || this.name, "value:", this.value);
                        window.BCMValidation.validateInput(this);
                    });
                });

                // Add change event listeners for select elements
                const selectFields = form.querySelectorAll('select[required]');
                console.log("Found", selectFields.length, "required select fields");

                selectFields.forEach(function(select, index) {
                    console.log("Setting up change listener for select", index + 1, ":", select.id || select.name);

                    // Add change event listener for select fields
                    select.addEventListener('change', function() {
                        debugger;
                        console.log("CHANGE EVENT triggered for:", this.id || this.name, "value:", this.value);
                        window.BCMValidation.validateInput(this);
                    });
                });

                // Add pattern validation for fields with pattern attributes
                const patternFields = form.querySelectorAll('input[pattern]');
                console.log("Found", patternFields.length, "fields with pattern validation");

                patternFields.forEach(function(field, index) {
                    console.log("Setting up pattern validation for field", index + 1, ":", field.id || field.name, "pattern:", field.pattern);

                    // Add input event listener for real-time pattern validation
                    field.addEventListener('input', function() {
                        console.log("PATTERN INPUT EVENT triggered for:", this.id || this.name, "value:", this.value, "pattern:", this.pattern);
                        window.BCMValidation.validatePatternInput(this);
                    });

                    // Add blur event listener for pattern validation when field loses focus
                    field.addEventListener('blur', function() {
                        console.log("PATTERN BLUR EVENT triggered for:", this.id || this.name, "value:", this.value, "pattern:", this.pattern);
                        window.BCMValidation.validatePatternInput(this);
                    });
                });

                // Add email validation for email fields
                const emailFields = form.querySelectorAll('input[type="email"]');
                console.log("Found", emailFields.length, "email fields");

                emailFields.forEach(function(field, index) {
                    console.log("Setting up email validation for field", index + 1, ":", field.id || field.name);

                    // Add input event listener for real-time email validation
                    field.addEventListener('input', function() {
                        console.log("EMAIL INPUT EVENT triggered for:", this.id || this.name, "value:", this.value);

                        // For company email, use simple validation approach
                        if (this.id === 'CompanyEmail') {
                            validateCompanyEmailExists(this);
                        } else {
                            window.BCMValidation.validateEmail(this);
                        }
                    });

                    // Add blur event listener for email validation when field loses focus
                    field.addEventListener('blur', function() {
                        console.log(" EMAIL BLUR EVENT triggered for:", this.id || this.name, "value:", this.value);

                        // For company email, use simple validation approach
                        if (this.id === 'CompanyEmail') {
                            validateCompanyEmailExists(this);
                        } else {
                            window.BCMValidation.validateEmail(this);
                        }
                    });
                });

                // Add mobile phone validation for mobile field
                const mobileField = form.querySelector('#MobilePhone');
                if (mobileField) {
                    console.log("Setting up mobile phone validation for field:", mobileField.id);

                    // Add input event listener for real-time mobile validation
                    mobileField.addEventListener('input', function() {
                        console.log("MOBILE INPUT EVENT triggered for:", this.id, "value:", this.value);
                        // Use simple validation approach that handles both pattern and custom validation
                        validateMobilePhoneExists(this);
                    });

                    // Add blur event listener for mobile validation when field loses focus
                    mobileField.addEventListener('blur', function() {
                        console.log("MOBILE BLUR EVENT triggered for:", this.id, "value:", this.value);
                        // Use simple validation approach that handles both pattern and custom validation
                        validateMobilePhoneExists(this);
                    });
                }



                // Add special handling for radio button groups (role selection)
                const radioGroups = form.querySelectorAll('input[type="radio"][name="role"]');
                radioGroups.forEach(function(radio) {
                    radio.addEventListener('change', function() {
                        // Hide validation message when a role is selected
                        const section = this.closest('section');
                        if (section) {
                            const feedbackElement = section.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                feedbackElement.style.display = 'none';
                            }
                        }
                    });
                });

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function (event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    if (!isValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        // AJAX validation functions
        let emailValidationTimeout;
        let mobileValidationTimeout;

        function validateCompanyEmailExists(input) {
            const email = input.value.trim();
            const emailInput = $(input);
            const emailFeedback = emailInput.closest('.form-group').find('.invalid-feedback');

            // Clear previous timeout
            clearTimeout(emailValidationTimeout);

            // If empty, let the framework handle it
            if (email === '') {
                emailInput.removeAttr('data-custom-validation-active');
                emailFeedback.removeClass('custom-validation show').hide();
                return;
            }

            // Check email format first
            const emailPattern = /^[a-zA-Z0-9._%+\-]+@@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,63}$/;
            if (!emailPattern.test(email)) {
                // Email format invalid - let framework handle it, hide custom message
                emailInput.removeAttr('data-custom-validation-active');
                emailFeedback.removeClass('custom-validation show').hide();
                return;
            }

            // Email format valid - check if exists (with debounce)
            emailValidationTimeout = setTimeout(function() {
                $.ajax({
                    url: '@Url.Action("CheckCompanyEmailExists", "ManageBCMResource")',
                    type: 'GET',
                    data: {
                        companyEmail: email,
                        resourceId: 0
                    },
                    success: function(response) {
                        if (response.exists) {
                            // Email exists - show custom message, hide email format validation
                            emailInput.attr('data-custom-validation-active', 'true');
                            emailInput.addClass('is-invalid');
                            emailFeedback.addClass('custom-validation show').text('This Company Email already exists. Please use another email address.').show();
                        } else {
                            // Email available - clear custom validation
                            emailInput.removeAttr('data-custom-validation-active');
                            emailInput.removeClass('is-invalid');
                            emailFeedback.removeClass('custom-validation show').hide();
                        }
                    },
                    error: function() {
                        // Clear validation on error
                        emailInput.removeAttr('data-custom-validation-active');
                        emailInput.removeClass('is-invalid');
                        emailFeedback.removeClass('custom-validation show').hide();
                    }
                });
            }, 500);
        }

        function validateMobilePhoneExists(input) {
            const mobile = input.value.trim();
            const mobileInput = $(input);
            const mobileFeedback = mobileInput.closest('.form-group').find('.invalid-feedback');

            // Clear previous timeout
            clearTimeout(mobileValidationTimeout);

            // If empty, let the framework handle it
            if (mobile === '') {
                mobileInput.removeAttr('data-custom-validation-active');
                mobileFeedback.removeClass('custom-validation show').hide();
                return;
            }

            // Check pattern first
            const pattern = /^\d{10}$/;
            if (!pattern.test(mobile)) {
                // Pattern invalid - let framework handle it, hide custom message
                mobileInput.removeAttr('data-custom-validation-active');
                mobileFeedback.removeClass('custom-validation show').hide();
                return;
            }

            // Pattern valid - check if mobile exists (with debounce)
            mobileValidationTimeout = setTimeout(function() {
                $.ajax({
                    url: '@Url.Action("CheckMobilePhoneExists", "ManageBCMResource")',
                    type: 'GET',
                    data: {
                        mobilePhone: mobile,
                        resourceId: 0
                    },
                    success: function(response) {
                        if (response.exists) {
                            // Mobile exists - show custom message, hide pattern validation
                            mobileInput.attr('data-custom-validation-active', 'true');
                            mobileInput.addClass('is-invalid');
                            mobileFeedback.addClass('custom-validation show').text('This Mobile Phone number already exists. Please use another number.').show();
                        } else {
                            // Mobile available - clear custom validation
                            mobileInput.removeAttr('data-custom-validation-active');
                            mobileInput.removeClass('is-invalid');
                            mobileFeedback.removeClass('custom-validation show').hide();
                        }
                    },
                    error: function() {
                        // Clear validation on error
                        mobileInput.removeAttr('data-custom-validation-active');
                        mobileInput.removeClass('is-invalid');
                        mobileFeedback.removeClass('custom-validation show').hide();
                    }
                });
            }, 500);
        }

        // Add a test function to manually check if event listeners are working
        window.testEventListeners = function() {
            console.log("Testing event listeners...");
            const form = document.getElementById('editResourceForm');
            if (!form) {
                console.error("Form not found!");
                return;
            }

            const requiredFields = form.querySelectorAll('[required]');
            console.log("Found", requiredFields.length, "required fields");

            if (requiredFields.length > 0) {
                const firstField = requiredFields[0];
                console.log("Testing first field:", firstField.id || firstField.name);

                // Simulate input event
                firstField.focus();
                firstField.value = "test";
                firstField.dispatchEvent(new Event('input', { bubbles: true }));

                console.log("Input event dispatched");
            }
        };

        // Add a test function specifically for pattern validation
        window.testPatternValidation = function() {
            console.log("Testing pattern validation...");
            const form = document.getElementById('editResourceForm');
            if (!form) {
                console.error("Form not found!");
                return;
            }

            const patternFields = form.querySelectorAll('input[pattern]');
            console.log("Found", patternFields.length, "pattern fields");

            if (patternFields.length > 0) {
                const firstPatternField = patternFields[0];
                console.log("Testing pattern field:", firstPatternField.id || firstPatternField.name, "pattern:", firstPatternField.pattern);

                // Check what custom message should be displayed
                const formGroup = firstPatternField.closest('.form-group');
                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                if (feedbackElement) {
                    console.log("Expected custom message:", feedbackElement.originalMessage || feedbackElement.textContent);
                }

                // Test with invalid input first
                firstPatternField.focus();
                firstPatternField.value = "123"; // Invalid for alphabetic pattern
                firstPatternField.dispatchEvent(new Event('input', { bubbles: true }));

                console.log("Invalid pattern input dispatched");

                // Check what message is actually displayed
                setTimeout(() => {
                    if (feedbackElement) {
                        console.log("Actual message displayed:", feedbackElement.textContent);
                        console.log("Message visible:", feedbackElement.style.display !== 'none');
                    }

                    // Then test with valid input
                    firstPatternField.value = "Valid Text"; // Valid for alphabetic pattern
                    firstPatternField.dispatchEvent(new Event('input', { bubbles: true }));
                    console.log("Valid pattern input dispatched");
                }, 500);
            }
        };

        // Add a test function specifically for email validation
        window.testEmailValidation = function() {
            console.log("Testing email validation...");
            const form = document.getElementById('editResourceForm');
            if (!form) {
                console.error("Form not found!");
                return;
            }

            const emailFields = form.querySelectorAll('input[type="email"]');
            console.log("Found", emailFields.length, "email fields");

            if (emailFields.length > 0) {
                const firstEmailField = emailFields[0];
                console.log("Testing email field:", firstEmailField.id || firstEmailField.name, "required:", firstEmailField.hasAttribute('required'));

                // Check what custom message should be displayed
                const formGroup = firstEmailField.closest('.form-group');
                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                if (feedbackElement) {
                    console.log("Expected custom message:", feedbackElement.textContent);
                }

                // Test with invalid email format first
                firstEmailField.focus();
                firstEmailField.value = "invalid-email"; // Invalid email format
                firstEmailField.dispatchEvent(new Event('input', { bubbles: true }));

                console.log("Invalid email format input dispatched");

                // Check what message is actually displayed
                setTimeout(() => {
                    if (feedbackElement) {
                        console.log("Actual message displayed:", feedbackElement.textContent);
                        console.log("Message visible:", feedbackElement.style.display !== 'none');
                    }

                    // Then test with valid email
                    setTimeout(() => {
                        firstEmailField.value = "<EMAIL>"; // Valid email
                        firstEmailField.dispatchEvent(new Event('input', { bubbles: true }));
                        console.log("Valid email input dispatched");
                    }, 1000);
                }, 500);
            }
        };

        // Set default dates
        var today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format
        $('#EffectiveStartDate').val(today); // Set Effective Start Date to today

        var futureDate = new Date();
        futureDate.setFullYear(futureDate.getFullYear() + 10); // Add 10 years
        var formattedFutureDate = futureDate.toISOString().split('T')[0]; // Format YYYY-MM-DD
        $('#EffectiveEndDate').val(formattedFutureDate); // Set Effective End Date

        // Function to validate current step
        function validateCurrentStep(currentIndex) {
            console.log("Validating step", currentIndex);
            var isValid = true;
            var currentSection = $('.wizard-content section').eq(currentIndex);

            // Find all required fields in current step
            var requiredFields = currentSection.find('input[required], select[required]');
            console.log("Found", requiredFields.length, "required fields in current step");

            requiredFields.each(function() {
                console.log("Validating field:", this.id || this.name, "value:", this.value);

                // Use the correct validation function
                var fieldValid = window.BCMValidation.validateInput(this, true);
                console.log("Field validation result:", fieldValid);

                if (!fieldValid) {
                    isValid = false;
                }
            });

            // Special validation for role selection in step 4 (index 3)
            if (currentIndex === 3) {
                var roleSelected = $('input[name="role"]:checked').length > 0;
                console.log("Role selected:", roleSelected);

                if (!roleSelected) {
                    // Show role validation message
                    var roleSection = $('input[name="role"]').closest('section');
                    var roleFeedback = roleSection.find('.invalid-feedback');
                    if (roleFeedback.length === 0) {
                        // Create feedback element if it doesn't exist
                        roleFeedback = $('<div class="invalid-feedback">Please select a Role</div>');
                        roleSection.append(roleFeedback);
                    }
                    roleFeedback.text('Please select a Role').show();
                    isValid = false;
                } else {
                    // Hide role validation message
                    var roleSection = $('input[name="role"]').closest('section');
                    var roleFeedback = roleSection.find('.invalid-feedback');
                    roleFeedback.hide();
                }
            }

            console.log("Step validation result:", isValid);
            return isValid;
        }

        // Handle step navigation with validation
        $('a').click(function (arrayOfValues) {
            var href = $(this).attr('href');

            if (href == "#finish") {
                // Final validation before submission
                if (!validateCurrentStep(3)) { // Validate final step
                    return false;
                }

                // Perform final form validation
                const form = document.getElementById('editResourceForm');
                if (!window.BCMValidation.validateForm(form)) {
                    console.log("Final form validation failed");
                    return false;
                }

                // Check for AJAX validation errors (email and mobile existence)
                const companyEmailField = form.querySelector('#CompanyEmail');
                const mobilePhoneField = form.querySelector('#MobilePhone');

                if (companyEmailField && companyEmailField.classList.contains('is-invalid')) {
                    console.log("Company email validation failed - email already exists");
                    alert("Please fix the Company Email validation error before submitting.");
                    companyEmailField.focus();
                    return false;
                }

                if (mobilePhoneField && mobilePhoneField.classList.contains('is-invalid')) {
                    console.log("Mobile phone validation failed - mobile already exists");
                    alert("Please fix the Mobile Phone validation error before submitting.");
                    mobilePhoneField.focus();
                    return false;
                }

                console.log("Final form validation passed");

                //code for getting checklist
                var CheckBoxValues= [];
                $("input[name='role']:checked").each(function () {
                    CheckBoxValues.push($(this).val());
                })
                const selectedRole = $("input[name='role']:checked").val();
                //var UserRole = $("input[name='UserRole']:checked").val();
                debugger;
                console.log("Preparing data for submission...");

                // Helper function to safely parse integer values
                function safeParseInt(value) {
                    const parsed = parseInt(value);
                    return isNaN(parsed) ? 0 : parsed;
                }

                // Helper function to safely get string values
                function safeGetValue(selector) {
                    const value = $(selector).val();
                    return value || '';
                }

                var objdata = {
                    // Primary identifiers
                    Id: 0, // For new resource
                    ResourceId: safeParseInt($('#ResourceId').val()),

                    // Organization structure
                    OrgID: safeParseInt($('#OrgID').val()),
                    OrgGroupID: safeParseInt($('#OrgGroupID').val()),
                    EntityId: safeParseInt($('#UnitID').val()),
                    UnitID: safeParseInt($('#UnitID').val()), // Both EntityId and UnitID needed
                    DepartmentID: safeParseInt($('#DepartmentID').val()),
                    SubFunctionID: safeParseInt($('#SubFunctionID').val()),

                    // Resource details
                    ResourceName: safeGetValue('#FName') + " " + safeGetValue('#LName'),
                    FName: safeGetValue('#FName'),
                    MName: safeGetValue('#MName'),
                    LName: safeGetValue('#LName'),
                    ResourceTitle: safeGetValue('#ResourceTitle'),
                    ResourceTypeID: safeParseInt($('#ResourceTypeID').val()),
                    DesignationId: safeParseInt($('#DesignationId').val()),

                    // Contact information
                    BusinessPhone: safeGetValue('#BusinessPhone'),
                    HomePhone: safeGetValue('#HomePhone'),
                    MobilePhone: safeGetValue('#MobilePhone'),
                    Fax: safeGetValue('#Fax'),
                    OtherPhone: safeGetValue('#OtherPhone'),
                    CompanyEmail: safeGetValue('#CompanyEmail'),
                    OtherEmail: safeGetValue('#OtherEmail'),

                    // Address information
                    Address: safeGetValue('#Address'),
                    City: safeGetValue('#City'),
                    State: safeGetValue('#State'),
                    PinCode: safeGetValue('#PinCode'),

                    // Company and vendor
                    CompanyID: safeParseInt($('#VendorID').val()), // Note: VendorID maps to CompanyID
                    VendorID: safeParseInt($('#VendorID').val()),

                    // Dates (ensure proper format)
                    EffectiveStartDate: $('#EffectiveStartDate').val() || new Date().toISOString().split('T')[0],
                    EffectiveEndDate: $('#EffectiveEndDate').val() || new Date(new Date().setFullYear(new Date().getFullYear() + 10)).toISOString().split('T')[0],

                    // Role and permissions
                    role: selectedRole ? [parseInt(selectedRole)] : [],

                    // Additional fields
                    Description: safeGetValue('#Description'),
                    Comments: safeGetValue('#Comments'),                                     
                };

                console.log("🔧 Prepared data object:", objdata);


                    $.ajax({
                        type: "POST",
                        url: '@Url.Action("SaveBCMResource", "ManageBCMResource")',
                        data: JSON.stringify(objdata),
                        contentType: 'application/json',
                        dataType: 'JSON',
                        success: function (response) {
                            // debugger;
                            // if (response.success) {
                            //     alert("Resource Saved successfully!");
                            //     $('#NormalModal').modal('hide');
                            //     location.reload();
                            // }
                            // else {
                            //      alert(response.message);
                            // }
                            if (response.success)
                            {
                                $('#NormalModal').modal('hide');
                                $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                                const toastElement = $('#liveToast');
                                toastElement.removeClass('bg-success bg-warning bg-danger');
                                toastElement.addClass('bg-success');
                                const toastLiveExample = document.getElementById('liveToast');
                                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                                toastBootstrap.show();
                                setTimeout(function () {
                                    location.reload();
                                }, 3000);
                            } 
                            else
                            {
                                $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                                const toastElement = $('#liveToast');
                                toastElement.removeClass('bg-success bg-warning bg-danger');
                                toastElement.addClass('bg-danger');
                                const toastLiveExample = document.getElementById('liveToast');
                                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                                toastBootstrap.show();
                            }
                        },
                        error: function (data) {
                            console.log('error is invoked');
                        }
                    });
                }
            });

            // Cascading Dropdown Implementation
            // Organization change event - populate Units
            $(document).on("change", "#OrgID", function () {
                var iOrgID = $(this).val();
                console.log("Organization changed to:", iOrgID);

                if (iOrgID && iOrgID !== "" && iOrgID !== "0") {
                    $.ajax({
                        url: '@Url.Action("GetAllUnits", "ManageBCMResource")',
                        type: 'GET',
                        data: { iOrgID: iOrgID },
                        success: function (data) {
                            console.log("Units data received:", data);
                            var ddlUnit = $('.ddlUnit');
                            ddlUnit.empty();
                            ddlUnit.append('<option value="0">-- All Units --</option>');
                            $.each(data, function (index, item) {
                                ddlUnit.append('<option value="' + item.unitID + '">' + item.unitName + '</option>');
                            });

                            // Clear dependent dropdowns
                            $('.ddlDepartment').empty().append('<option value="0">-- All Departments --</option>');
                            $('.ddlSubDepartment').empty().append('<option value="0">-- All SubDepartments --</option>');
                        },
                        error: function (xhr, status, error) {
                            console.log('Error loading units:', error);
                            console.error(xhr.status);
                            console.error(xhr.responseText);
                        }
                    });
                } else {
                    // Clear all dependent dropdowns when no organization is selected
                    $('.ddlUnit').empty().append('<option value="0">-- All Units --</option>');
                    $('.ddlDepartment').empty().append('<option value="0">-- All Departments --</option>');
                    $('.ddlSubDepartment').empty().append('<option value="0">-- All SubDepartments --</option>');
                }
            });

            // Unit change event - populate Departments
            $(document).on("change", ".ddlUnit", function () {
                var iUnitID = $(this).val();
                console.log("Unit changed to:", iUnitID);

                if (iUnitID && iUnitID !== "" && iUnitID !== "0") {
                    $.ajax({
                        url: '@Url.Action("GetAllDepartments", "ManageBCMResource")',
                        type: 'GET',
                        data: { iUnitID: iUnitID },
                        success: function (data) {
                            console.log("Departments data received:", data);
                            var ddlDepartment = $('.ddlDepartment');
                            ddlDepartment.empty();
                            ddlDepartment.append('<option value="0">-- All Departments --</option>');
                            $.each(data, function (index, item) {
                                ddlDepartment.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>');
                            });

                            // Clear dependent dropdown
                            $('.ddlSubDepartment').empty().append('<option value="0">-- All SubDepartments --</option>');
                        },
                        error: function (xhr, status, error) {
                            console.log('Error loading departments:', error);
                            console.error(xhr.status);
                            console.error(xhr.responseText);
                        }
                    });
                } else {
                    // Clear dependent dropdowns when no unit is selected
                    $('.ddlDepartment').empty().append('<option value="0">-- All Departments --</option>');
                    $('.ddlSubDepartment').empty().append('<option value="0">-- All SubDepartments --</option>');
                }
            });

            // Department change event - populate SubDepartments
            $(document).on("change", ".ddlDepartment", function () {
                var iDepartmentID = $(this).val();
                console.log("Department changed to:", iDepartmentID);

                if (iDepartmentID && iDepartmentID !== "" && iDepartmentID !== "0") {
                    $.ajax({
                        url: '@Url.Action("GetAllSubDepartments", "ManageBCMResource")',
                        type: 'GET',
                        data: { iDepartmentID: iDepartmentID },
                        success: function (data) {
                            console.log("SubDepartments data received:", data);
                            var ddlSubDepartment = $('.ddlSubDepartment');
                            ddlSubDepartment.empty();
                            ddlSubDepartment.append('<option value="0">-- All SubDepartments --</option>');
                            $.each(data, function (index, item) {
                                ddlSubDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>');
                            });
                        },
                        error: function (xhr, status, error) {
                            console.log('Error loading sub departments:', error);
                            console.error(xhr.status);
                            console.error(xhr.responseText);
                        }
                    });
                } else {
                    // Clear dependent dropdown when no department is selected
                    $('.ddlSubDepartment').empty().append('<option value="0">-- All SubDepartments --</option>');
                }
            });
    }; // End of initializeAddBCMResourceValidation function

    // Fallback: Try to initialize when document is ready (in case the main function isn't called)
    $(document).ready(function() {
        console.log("Document ready - checking if form exists...");

        // Wait a bit for the form to be loaded if it's in a modal
        setTimeout(function() {
            const form = document.getElementById('editResourceForm');
            if (form && typeof window.initializeAddBCMResourceValidation === 'function') {
                console.log("Form found in document ready, initializing validation...");
                window.initializeAddBCMResourceValidation();
            } else {
                console.log("Form not found in document ready or function not available");
            }
        }, 500);
    });

    // Function to open designation management modal
    function openDesignationModal() {
        console.log('Opening designation modal...');
        $.get('@Url.Action("ViewDesignations", "ManageBCMResource")', function(data) {
            $('#designationModal .modal-content').html(data);
            $('#designationModal').modal('show');
        }).fail(function(xhr, status, error) {
            console.error('Error loading designations:', error);
            alert('Error loading designations. Please try again.');
        });
    }

    // Function to refresh designation dropdown (called from modal)
    function refreshDesignationDropdown() {
        console.log('Refreshing designation dropdown...');
        $.get('@Url.Action("GetDesignationDropdownData", "ManageBCMResource")', function(data) {
            var dropdown = $('#DesignationId');
            var currentValue = dropdown.val();
            dropdown.empty();
            dropdown.append('<option value="0">-- All Designations --</option>');

            $.each(data, function(index, item) {
                dropdown.append('<option value="' + item.value + '">' + item.text + '</option>');
            });

            // Restore previous selection if it still exists
            if (currentValue && dropdown.find('option[value="' + currentValue + '"]').length > 0) {
                dropdown.val(currentValue);
            }
            console.log('Designation dropdown refreshed with', data.length, 'items');
        }).fail(function(xhr, status, error) {
            console.error('Error refreshing designation dropdown:', error);
        });

        $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#NormalModal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        //Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });
    }
</script>


