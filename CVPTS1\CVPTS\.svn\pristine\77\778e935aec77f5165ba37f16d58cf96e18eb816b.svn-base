﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BCM.UI.Areas.OrgStructure.Controllers;

[Area("BCMOrgStructure")]
public class OrganizationController : BaseController
{

    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public OrganizationController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult ManageOrganization()
    {
        List<OrgInfo> lstOrgInfo = new List<OrgInfo>();

        try
        {
            PopulateDropdown();
            lstOrgInfo = _ProcessSrv.GetOrganizationMasterList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View(lstOrgInfo);
    }


    [HttpGet]
    public IActionResult AddOrg()
    {
        OrgInfoAndAttachments OrgInfoAndAttachments = new OrgInfoAndAttachments();
        try
        {
            OrgInfoAndAttachments.OrgInfo = new OrgInfo();
            OrgInfoAndAttachments.Attachments = new Attachments();
            PopulateDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_AddOrganization", OrgInfoAndAttachments);
    }

    [HttpPost]
    public IActionResult AddOrg(OrgInfoAndAttachments objOrgInfo)
    {
        bool bSuccess = false;
        int iOrgID = 0;
        bool IsOrgNameExist = false;

        // Debug: Log what we received
        _CVLogger.LogErrorApp(new Exception($"AddOrg POST called. OrgInfo null: {objOrgInfo?.OrgInfo == null}, Attachments null: {objOrgInfo?.Attachments == null}"));
        _CVLogger.LogErrorApp(new Exception($"Request.Form.Files.Count: {Request.Form.Files.Count}"));
        foreach (var file in Request.Form.Files)
        {
            _CVLogger.LogErrorApp(new Exception($"Form file: {file.Name} = {file.FileName} ({file.Length} bytes)"));
        }

        try
        {
            if (!_ProcessSrv.IsExistByName(objOrgInfo.OrgInfo.OrganizationName.ToLower(), Convert.ToInt32(objOrgInfo.OrgInfo.Id), Convert.ToInt32(objOrgInfo.OrgInfo.OrgGroupID)))
            {

                iOrgID = _ProcessSrv.OrganizationMasterSave(objOrgInfo.OrgInfo);
                bSuccess = iOrgID > 0 ? true : false;
                IsOrgNameExist = true;

                // Handle logo file upload - check multiple sources
                IFormFile logoFile = null;

                // First, try to get from the bound model
                if (objOrgInfo.Attachments?.AttachementFile != null)
                {
                    logoFile = objOrgInfo.Attachments.AttachementFile;
                }
                // If not found, try to get directly from Request.Form.Files
                else if (Request.Form.Files.Count > 0)
                {
                    // Try common file input names
                    logoFile = Request.Form.Files["Attachments.AttachementFile"] ??
                              Request.Form.Files["AttachementFile"] ??
                              Request.Form.Files["logoFile"] ??
                              Request.Form.Files.FirstOrDefault();
                }

                // Save organization logo if file is found
                if (logoFile != null && logoFile.Length > 0)
                {
                    // Create Attachments object if it doesn't exist
                    if (objOrgInfo.Attachments == null)
                    {
                        objOrgInfo.Attachments = new Attachments();
                    }
                    objOrgInfo.Attachments.AttachementFile = logoFile;

                    SaveOrganizationLogo(objOrgInfo.Attachments, iOrgID);
                }
                else
                {
                    // Log for debugging - no file found
                    _CVLogger.LogErrorApp(new Exception($"No logo file found for organization ID: {iOrgID}. Request.Form.Files.Count: {Request.Form.Files.Count}"));
                }
                #region "OrgLevelUser"
                List<OrgInfo> objOrgColl = new List<OrgInfo>(); OrgInfo objOrgItem = new OrgInfo();

                List<OrgUnit> objUnitColl = new List<OrgUnit>(); OrgUnit objUnitItem = new OrgUnit();
                List<DepartmentInfo> objDepartmentColl = new List<DepartmentInfo>(); DepartmentInfo objDeptItem = new DepartmentInfo();
                List<SubFunction> objSubfunctionColl = new List<SubFunction>(); SubFunction objSubFuItem = new SubFunction();

                objUnitColl = _ProcessSrv.GetOrganizationUnitListByOrgID(Convert.ToInt32(iOrgID.ToString()));
                if (objUnitColl != null && objUnitColl.Count > 0)
                {
                    foreach (OrgUnit objOrgUnit in objUnitColl)
                    {


                        objDepartmentColl = _ProcessSrv.GetDepartmentByUnitId(Convert.ToInt32(objOrgUnit.UnitID));
                        if (objDepartmentColl != null && objDepartmentColl.Count > 0)
                        {
                            foreach (DepartmentInfo objDepartment in objDepartmentColl)
                            {


                                objSubfunctionColl = _ProcessSrv.GetSubFunctionListByFunctionID(objDepartment.DepartmentID.ToString());
                                if (objSubfunctionColl != null && objSubfunctionColl.Count > 0)
                                {
                                    foreach (SubFunction objSubfunction in objSubfunctionColl)
                                    {
                                        List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                                        OrgRoleRights objrights = new OrgRoleRights();

                                        objrights.UserID = Convert.ToInt32(objOrgInfo.OrgInfo.OrgHeadID);//Enter access for  OrgHead
                                        objrights.CreatedBy = _UserDetails.UserID;
                                        objrights.UpdatedBy = _UserDetails.UserID;
                                        objrights.OrgGroupID = (objOrgInfo.OrgInfo.OrgGroupID.Equals(null) || objOrgInfo.OrgInfo.OrgGroupID.ToString() == "") ? 0 : Convert.ToInt32(objOrgInfo.OrgInfo.OrgGroupID);
                                        objrights.OrgID = (iOrgID.Equals(null) || iOrgID.ToString() == "") ? 0 : iOrgID;
                                        objrights.UnitID = (objOrgUnit.UnitID.Equals(null) || objOrgUnit.UnitID.ToString() == "") ? 0 : objOrgUnit.UnitID;
                                        objrights.DeptID = (objDepartment.DepartmentID.Equals(null) || objDepartment.DepartmentID.ToString() == "") ? 0 : objDepartment.DepartmentID;
                                        objrights.SubDeptID = (objSubfunction.SubFunctionID.Equals(null) || objSubfunction.SubFunctionID == "") ? 0 : Convert.ToInt32(objSubfunction.SubFunctionID);

                                        objrightscoll.Add(objrights);
                                        int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                                    }
                                }
                                else
                                {
                                    OrgRoleRights objrights = new OrgRoleRights();
                                    List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();

                                    objrights.UserID = Convert.ToInt32(objOrgInfo.OrgInfo.OrgHeadID);//Enter access for  OrgHead
                                    objrights.CreatedBy = _UserDetails.UserID;
                                    objrights.UpdatedBy = _UserDetails.UserID;
                                    objrights.OrgGroupID = (objOrgInfo.OrgInfo.OrgGroupID.Equals(null) || objOrgInfo.OrgInfo.OrgGroupID.ToString() == "") ? 0 : Convert.ToInt32(objOrgInfo.OrgInfo.OrgGroupID);
                                    objrights.OrgID = (iOrgID.ToString().Equals(null) || iOrgID.ToString() == "") ? 0 : iOrgID;
                                    objrights.UnitID = (objOrgUnit.UnitID.Equals(null) || objOrgUnit.UnitID.ToString() == "") ? 0 : objOrgUnit.UnitID;
                                    objrights.DeptID = (objDepartment.DepartmentID.Equals(null) || objDepartment.DepartmentID.ToString() == "") ? 0 : objDepartment.DepartmentID;
                                    objrightscoll.Add(objrights);
                                    int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                                }
                            }
                        }
                        else
                        {
                            OrgRoleRights objrights = new OrgRoleRights();
                            List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();

                            objrights.UserID = Convert.ToInt32(objOrgInfo.OrgInfo.OrgHeadID);//Enter access for  OrgHead
                            objrights.CreatedBy = _UserDetails.UserID;
                            objrights.UpdatedBy = _UserDetails.UserID;
                            objrights.OrgGroupID = (objOrgInfo.OrgInfo.OrgGroupID.Equals(null) || objOrgInfo.OrgInfo.OrgGroupID.ToString() == "") ? 0 : Convert.ToInt32(objOrgInfo.OrgInfo.OrgGroupID);
                            objrights.OrgID = (iOrgID.ToString().Equals(null) || iOrgID.ToString() == "") ? 0 : iOrgID;
                            objrights.UnitID = (objOrgUnit.UnitID.Equals(null) || objOrgUnit.UnitID.ToString() == "") ? 0 : objOrgUnit.UnitID;
                            objrightscoll.Add(objrights);
                            int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                        }
                    }
                }
                else
                {
                    OrgRoleRights objrights = new OrgRoleRights();
                    List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                    objrights.UserID = Convert.ToInt32(objOrgInfo.OrgInfo.OrgHeadID);//Enter access for  OrgHead
                    objrights.CreatedBy = _UserDetails.UserID;
                    objrights.UpdatedBy = _UserDetails.UserID;
                    objrights.OrgGroupID = (objOrgInfo.OrgInfo.OrgGroupID.Equals(null) || objOrgInfo.OrgInfo.OrgGroupID.ToString() == "") ? 0 : Convert.ToInt32(objOrgInfo.OrgInfo.OrgGroupID);
                    objrights.OrgID = (iOrgID.ToString().Equals(null) || iOrgID.ToString() == "") ? 0 : iOrgID;

                    objrightscoll.Add(objrights);
                    int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                }
                #endregion
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objOrgInfo.OrgInfo.OrganizationName + " Added Successfully" : "Failed To Add Organization." });
        }
        if (IsOrgNameExist)
        {
            return RedirectToAction("ManageOrganization");
        }
        else
        {
            return RedirectToAction("_AddOrganization", objOrgInfo);
        }
    }

    [HttpGet]
    public JsonResult IsOrgNameExist(string iOrgGroupID = "0", string OrgName = "")
    {
        bool IsExist = false;
        try
        {
            if (OrgName != string.Empty)
            {
                IsExist = _ProcessSrv.IsExistByName(OrgName.ToLower(), Convert.ToInt32("0"), Convert.ToInt32(iOrgGroupID));
            }
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
        return Json(IsExist);
    }


    [HttpGet]
    public IActionResult EditOrg(int iId)
    {
        var objOrgInfo = new OrgInfoAndAttachments();

        try
        {
            PopulateDropdown();
            objOrgInfo.OrgInfo = _ProcessSrv.GetOrganizationMasterByOrgId(iId.ToString());

            //objOrgInfo.Attachments attachList = _ProcessSrv.GetOrgLogoAttachmentListByID(Convert.ToInt32(BCPEnum.EntityType.Organization), Convert.ToInt32(iId));
            objOrgInfo.Attachments = new Attachments(); // Initialize Attachments object
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_EditOrganization", objOrgInfo);
    }

    [HttpPost]
    public IActionResult EditOrg(OrgInfoAndAttachments objOrgInfo)
    {
        bool bSuccess = false;
        int iOrgID = Convert.ToInt32(objOrgInfo.OrgInfo.Id);
        try
        {
            bSuccess = _ProcessSrv.OrganizationMasterUpdate(objOrgInfo.OrgInfo, _UserDetails.UserID);

            // Handle logo file upload - check multiple sources
            IFormFile logoFile = null;

            // First, try to get from the bound model
            if (objOrgInfo.Attachments?.AttachementFile != null)
            {
                logoFile = objOrgInfo.Attachments.AttachementFile;
            }
            // If not found, try to get directly from Request.Form.Files
            else if (Request.Form.Files.Count > 0)
            {
                // Try common file input names
                logoFile = Request.Form.Files["Attachments.AttachementFile"] ??
                          Request.Form.Files["AttachementFile"] ??
                          Request.Form.Files["logoFile"] ??
                          Request.Form.Files.FirstOrDefault();
            }

            // Save organization logo if file is found
            if (logoFile != null && logoFile.Length > 0)
            {
                // Create Attachments object if it doesn't exist
                if (objOrgInfo.Attachments == null)
                {
                    objOrgInfo.Attachments = new Attachments();
                }
                objOrgInfo.Attachments.AttachementFile = logoFile;

                SaveOrganizationLogo(objOrgInfo.Attachments, iOrgID);
            }
            #region "OrgLevelUser"
            List<OrgInfo> objOrgColl = new List<OrgInfo>(); OrgInfo objOrgItem = new OrgInfo();

            List<OrgUnit> objUnitColl = new List<OrgUnit>(); OrgUnit objUnitItem = new OrgUnit();
            List<DepartmentInfo> objDepartmentColl = new List<DepartmentInfo>(); DepartmentInfo objDeptItem = new DepartmentInfo();
            List<SubFunction> objSubfunctionColl = new List<SubFunction>(); SubFunction objSubFuItem = new SubFunction();

            objUnitColl = _ProcessSrv.GetOrganizationUnitListByOrgID(Convert.ToInt32(iOrgID.ToString()));
            if (objUnitColl != null && objUnitColl.Count > 0)
            {
                foreach (OrgUnit objOrgUnit in objUnitColl)
                {


                    objDepartmentColl = _ProcessSrv.GetDepartmentByUnitId(Convert.ToInt32(objOrgUnit.UnitID));
                    if (objDepartmentColl != null && objDepartmentColl.Count > 0)
                    {
                        foreach (DepartmentInfo objDepartment in objDepartmentColl)
                        {


                            objSubfunctionColl = _ProcessSrv.GetSubFunctionListByFunctionID(objDepartment.DepartmentID.ToString());
                            if (objSubfunctionColl != null && objSubfunctionColl.Count > 0)
                            {
                                foreach (SubFunction objSubfunction in objSubfunctionColl)
                                {
                                    List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                                    OrgRoleRights objrights = new OrgRoleRights();

                                    objrights.UserID = Convert.ToInt32(objOrgInfo.OrgInfo.OrgHeadID);//Enter access for  OrgHead
                                    objrights.CreatedBy = _UserDetails.UserID;
                                    objrights.UpdatedBy = _UserDetails.UserID;
                                    objrights.OrgGroupID = (objOrgInfo.OrgInfo.OrgGroupID.Equals(null) || objOrgInfo.OrgInfo.OrgGroupID.ToString() == "") ? 0 : Convert.ToInt32(objOrgInfo.OrgInfo.OrgGroupID);
                                    objrights.OrgID = (iOrgID.Equals(null) || iOrgID.ToString() == "") ? 0 : iOrgID;
                                    objrights.UnitID = (objOrgUnit.UnitID.Equals(null) || objOrgUnit.UnitID.ToString() == "") ? 0 : objOrgUnit.UnitID;
                                    objrights.DeptID = (objDepartment.DepartmentID.Equals(null) || objDepartment.DepartmentID.ToString() == "") ? 0 : objDepartment.DepartmentID;
                                    objrights.SubDeptID = (objSubfunction.SubFunctionID.Equals(null) || objSubfunction.SubFunctionID == "") ? 0 : Convert.ToInt32(objSubfunction.SubFunctionID);

                                    objrightscoll.Add(objrights);
                                    int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                                }
                            }
                            else
                            {
                                OrgRoleRights objrights = new OrgRoleRights();
                                List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();

                                objrights.UserID = Convert.ToInt32(objOrgInfo.OrgInfo.OrgHeadID);//Enter access for  OrgHead
                                objrights.CreatedBy = _UserDetails.UserID;
                                objrights.UpdatedBy = _UserDetails.UserID;
                                objrights.OrgGroupID = (objOrgInfo.OrgInfo.OrgGroupID.Equals(null) || objOrgInfo.OrgInfo.OrgGroupID.ToString() == "") ? 0 : Convert.ToInt32(objOrgInfo.OrgInfo.OrgGroupID);
                                objrights.OrgID = (iOrgID.ToString().Equals(null) || iOrgID.ToString() == "") ? 0 : iOrgID;
                                objrights.UnitID = (objOrgUnit.UnitID.Equals(null) || objOrgUnit.UnitID.ToString() == "") ? 0 : objOrgUnit.UnitID;
                                objrights.DeptID = (objDepartment.DepartmentID.Equals(null) || objDepartment.DepartmentID.ToString() == "") ? 0 : objDepartment.DepartmentID;
                                objrightscoll.Add(objrights);
                                int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                            }
                        }
                    }
                    else
                    {
                        OrgRoleRights objrights = new OrgRoleRights();
                        List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();

                        objrights.UserID = Convert.ToInt32(objOrgInfo.OrgInfo.OrgHeadID);//Enter access for  OrgHead
                        objrights.CreatedBy = _UserDetails.UserID;
                        objrights.UpdatedBy = _UserDetails.UserID;
                        objrights.OrgGroupID = (objOrgInfo.OrgInfo.OrgGroupID.Equals(null) || objOrgInfo.OrgInfo.OrgGroupID.ToString() == "") ? 0 : Convert.ToInt32(objOrgInfo.OrgInfo.OrgGroupID);
                        objrights.OrgID = (iOrgID.ToString().Equals(null) || iOrgID.ToString() == "") ? 0 : iOrgID;
                        objrights.UnitID = (objOrgUnit.UnitID.Equals(null) || objOrgUnit.UnitID.ToString() == "") ? 0 : objOrgUnit.UnitID;
                        objrightscoll.Add(objrights);
                        int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                    }
                }
            }
            else
            {
                OrgRoleRights objrights = new OrgRoleRights();
                List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                objrights.UserID = Convert.ToInt32(objOrgInfo.OrgInfo.OrgHeadID);//Enter access for  OrgHead
                objrights.CreatedBy = _UserDetails.UserID;
                objrights.UpdatedBy = _UserDetails.UserID;
                objrights.OrgGroupID = (objOrgInfo.OrgInfo.OrgGroupID.Equals(null) || objOrgInfo.OrgInfo.OrgGroupID.ToString() == "") ? 0 : Convert.ToInt32(objOrgInfo.OrgInfo.OrgGroupID);
                objrights.OrgID = (iOrgID.ToString().Equals(null) || iOrgID.ToString() == "") ? 0 : iOrgID;

                objrightscoll.Add(objrights);
                int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
            }
            #endregion
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objOrgInfo.OrgInfo.OrganizationName + " Updated Successfully" : "Failed To Update Organization." });
        }

        return RedirectToAction("ManageOrganization");
    }


    [HttpGet]
    public IActionResult DeleteOrg(int iId)
    {
        var objOrgInfo = new OrgInfo();

        try
        {
            objOrgInfo = _ProcessSrv.GetOrganizationMasterByOrgId(iId.ToString());
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteOrganization", objOrgInfo);
    }

    [HttpPost]
    public IActionResult DeleteOrg(OrgInfo objOrgInfo)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.OrganizationmasterDelete(Convert.ToInt32(objOrgInfo.Id), _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objOrgInfo.OrganizationName + " Deleted Successfully" : "Failed To Delete Organization." });
        }

        return RedirectToAction("ManageOrganization");
    }

    [HttpGet]
    public IActionResult GetResourceDetails(int iId)
    {
        try
        {
            var objResourcesInfo = _ProcessSrv.GetResourcesByResourceID(iId);

            if (objResourcesInfo != null)
            {
                return Json(new { mail = objResourcesInfo.CompanyEmail, mobile = objResourcesInfo.MobilePhone });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return Json(null);
    }

    public IActionResult GetOrganizationByID(int iOrgId)
    {
        try
        {
            List<OrgInfo> lstOrgInfo = _ProcessSrv.GetOrganizationMasterList();
            if (iOrgId > 0)
            {
                lstOrgInfo = lstOrgInfo.Where(x => Convert.ToInt32(x.Id) == iOrgId).ToList();
                if (lstOrgInfo == null || !lstOrgInfo.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterOrganization", lstOrgInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageOrganization");
    }

    public void PopulateDropdown()
    {
        try
        {
            ViewBag.OrgInfo = new SelectList(_Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString()), "Id", "OrganizationName");
            ViewBag.OrgGroup = new SelectList(_Utilities.GetOrgGroupList(), "OrgGroupID", "OrganizationGroupName");
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    /// <summary>
    /// Saves organization logo file to database and file system without deleting the file
    /// </summary>
    /// <param name="attachments">Attachment object containing the logo file</param>
    /// <param name="orgID">Organization ID</param>
    private void SaveOrganizationLogo(Attachments attachments, int orgID)
    {
        try
        {
            if (attachments?.AttachementFile != null)
            {
                var file = attachments.AttachementFile;

                // Validate file type and size
                string ext = System.IO.Path.GetExtension(file.FileName);
                string ext1 = ext.TrimStart('.');
                int iFileLengthInKB = Convert.ToInt32(file.Length);

                if (_Utilities.CheckFileType(ext1, Convert.ToInt32(_UserDetails.OrgID)) &&
                    _Utilities.CheckFileSize(iFileLengthInKB, Convert.ToInt32(_UserDetails.OrgID)))
                {
                    // Build attachment entity
                    attachments = BuildLogoAttachmentEntity(attachments, orgID);

                    // Convert file to blob for database storage
                    byte[] blob = _Utilities.ConvertToBlob(file);
                    if (blob != null)
                    {
                        attachments.AttachmentObj = (object)blob;

                        // Save to database using CVaultAttachmentSave method
                        bool iSuccess = _ProcessSrv.CVaultAttachmentSave(attachments);

                        if (iSuccess)
                        {
                            // Also save file to file system (without deleting)
                            var uploadsFolder = Path.Combine("Attachments", "OrganizationLogos");
                            if (!Directory.Exists(uploadsFolder))
                            {
                                Directory.CreateDirectory(uploadsFolder);
                            }

                            // Create unique filename to avoid conflicts
                            string fileName = $"Org_{orgID}_{DateTime.Now:yyyyMMdd_HHmmss}_{file.FileName}";
                            var filePath = Path.Combine(uploadsFolder, fileName);

                            // Save file to disk
                            using (var stream = new FileStream(filePath, FileMode.Create))
                            {
                                file.CopyToAsync(stream).Wait();
                            }

                            // Log success (using LogErrorApp as info logging method not available)
                            // _CVLogger.LogErrorApp(new Exception($"Organization logo saved successfully for Org ID: {orgID}, File: {fileName}"));
                        }
                        else
                        {
                            _CVLogger.LogErrorApp(new Exception($"Failed to save organization logo to database for Org ID: {orgID}"));
                        }
                    }
                    else
                    {
                        _CVLogger.LogErrorApp(new Exception($"Failed to convert logo file to blob for Org ID: {orgID}"));
                    }
                }
                else
                {
                    _CVLogger.LogErrorApp(new Exception($"Invalid file type or size for organization logo. Org ID: {orgID}, File: {file.FileName}"));
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    /// <summary>
    /// Builds attachment entity for organization logo
    /// </summary>
    /// <param name="attachments">Attachment object</param>
    /// <param name="orgID">Organization ID</param>
    /// <returns>Built attachment entity</returns>
    private Attachments BuildLogoAttachmentEntity(Attachments attachments, int orgID)
    {
        try
        {
            if (attachments?.AttachementFile != null)
            {
                string ext = Path.GetExtension(attachments.AttachementFile.FileName);
                string ext1 = ext.TrimStart('.');
                int iFileLengthInKB = Convert.ToInt32(attachments.AttachementFile.Length);

                // Generate unique identifier
                byte[] GuidSize = new byte[16];
                Random rd = new Random();
                rd.NextBytes(GuidSize);
                Guid guid = new Guid(GuidSize);

                // Set attachment properties
                attachments.EntityId = Convert.ToInt32(BCPEnum.EntityType.Organization); // Organization entity type
                attachments.RecordId = orgID; // Reference to organization ID
                attachments.UserId = _UserDetails.UserID;
                attachments.AttchmentName = attachments.AttachementFile.FileName;
                attachments.FileSize = iFileLengthInKB;
                attachments.MimeType = ext1;
                attachments.GUID = guid.ToString();
                attachments.CreatedBy = _UserDetails.UserID;
                attachments.UpdatedBy = _UserDetails.UserID;
                attachments.CreatedDate = DateTime.Now;
                attachments.UpdatedDate = DateTime.Now;
                attachments.Description = $"Organization Logo for {orgID}";
                attachments.Status = "1"; // Active status

                // Set attachment type for organization logo (using OtherBCMDocument as closest match)
                attachments.AttachmentType = Convert.ToString((int)BCPEnum.AttachmentType.OtherBCMDocument);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return attachments;
    }
}

