﻿
@{
    ViewBag.Title = "ViewDependentEntities";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">View Dependent Services</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-organization"></i></span>
            <select class="form-select form-control" aria-label="Default select example">
                <option selected>All Organizations</option>
                <option value="1">PTS</option>
                <option value="2">TCS</option>
                <option value="3">Continuity Vault</option>
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-unit"></i></span>
            <select class="form-select form-control" id="unitlist" aria-label="Default select example">
                <option selected>All Units</option>
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-department"></i></span>
            <select class="form-select form-control" id="departmentlist" aria-label="Default select example">
                <option selected>All Departments</option>
            </select>
        </div>
        <div class="input-group w-30">
            <span class="input-group-text py-1"><i class="cv-subdepartment"></i></span>
            <select class="form-select form-control" id="departmentlist" aria-label="Default select example">
                <option selected>All Sub Departments</option>
            </select>
        </div>
        <div class="input-group ">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>

        @*    <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>
<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th>Sl No.</th>
                <th>Dependent ProcessName</th>
                <th>Dependent ProcessRTO</th>
                <th>Process Name</th>
                <th>RTO</th>
                <th>RPO</th>
                <th>Process Owner</th>
                <th>OrgLevel</th>
                <th>IsCritical</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>1</td>
                <td>Tenders & Auctions Committees</td>
                <td>1Day(s)</td>
                <td>Community Engagement</td>
                <td>1Day(s)</td>
                <td>Neeraj Sahu</td>
                <td>Selvam C P</td>
                <td>
                    <table>
                        <tbody>
                            <tr title="Org Name">
                                <td><i class="cv-organization"></i></td>
                                <td> : </td>
                                <td>Perpetuuiti</td>
                            </tr>
                            <tr title="Unit ">
                                <td><i class="cv-unit"></i></td>
                                <td>:</td>
                                <td>Finance affairs</td>
                            </tr>
                            <tr title="Department">
                                <td><i class="cv-department"></i></td>
                                <td>:</td>
                                <td>Product Development</td>
                            </tr>
                            <tr title="Sub Department">
                                <td><i class="cv-subdepartment"></i></td>
                                <td>:</td>
                                <td>SG OfficeA</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <span class="text-danger">No </span>
                </td>
                <td>
                    <span class="text-success">Approved </span>
                </td>
            </tr>
        </tbody>
    </table>
</div>

