﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata;
using System.Xml;

namespace BCM.UI.Areas.BCMStrategy.Controllers;
[Area("BCMStrategy")]
public class ManageBCMStrategyController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    int iEntityTypeID = 0;

    public ManageBCMStrategyController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        iEntityTypeID = Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.BCMStrategy);
        _CVLogger = CVLogger;
    }

    public IActionResult Index()
    {
        return View();
    }

    [HttpGet]
    public IActionResult ManageBCMStrategy()
    {
        try
        {
            PopulateDropDown();

            List<BCM.BusinessClasses.BCMStrategy> lstStrategy = _ProcessSrv.BCMStrategy_GetAll(Convert.ToInt32(_UserDetails.OrgID));            

            ViewBag.Strategy = lstStrategy;
            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View();
    }

    public void PopulateDropDown(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());

            ViewBag.Unit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);

            ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

            ViewBag.ResourceList = _Utilities.GetAllResourceList();

            ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();

            ViewBag.Entity = _ProcessSrv.GetAllCVaultEntitiesGetAll();

            ViewBag.EntityType = _Utilities.PopulateBCMEntities();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public string CheckEntityType(int Id)
    {
        string strEntityType = ((BCM.Shared.BCPEnum.EntityType)Id).ToString();

        return strEntityType;
    }
}

