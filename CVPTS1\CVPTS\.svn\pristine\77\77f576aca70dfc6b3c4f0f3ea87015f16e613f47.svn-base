﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.Json;
using System.Xml.Linq;

namespace BCM.UI.Areas.BCMProcessBIAForms.Controllers;
[Area("BCMProcessBIAForms")]
public class QuantitativeBIAMatrixController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    int iBIAID = 0;
    int iProcessID = 0;
    int iSectionID = 0;
    int iIsBCMEntity = 0;
    int iBpProfileID = 0;
    List<BIAProfileMaster> lstBIAProfileMaster = new List<BIAProfileMaster>();

    public QuantitativeBIAMatrixController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult QuantitativeBIAMatrix(string strSectionID, string strProcessID, string strIsBCMEntity, string strBIAID)
    {
        try
        {
            #region Page_Init Methods

            iBIAID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strBIAID));
            iProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
            iSectionID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strSectionID));
            iIsBCMEntity = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strIsBCMEntity));

            BusinessProcessInfo objBusinessProcess = new BusinessProcessInfo();
            objBusinessProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(iProcessID.ToString() == "" ? "0" : iProcessID), (int)BCPEnum.EntityType.BusinessProcess);
            ViewBag.ProcessName = objBusinessProcess.ProcessName;
            ViewBag.ProcessCode = objBusinessProcess.ProcessCode;
            ViewBag.ProcessVersion = objBusinessProcess.ProcessVersion;
            if (iIsBCMEntity == 0)
            {
                BusinessProcessInfo objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(iProcessID, 1);
                iBpProfileID = objBusinessProcessInfo.ProfileID;
            }

            if (iIsBCMEntity == 1)
            {
                BCMEntityInfo objBCMEntityInfo = _ProcessSrv.GetBCMEntityByEntityID(iProcessID);
                iBpProfileID = Convert.ToInt32(objBCMEntityInfo.ProfileID);
            }

            HttpContext.Session.SetString("SectionID", iSectionID.ToString());
            HttpContext.Session.SetString("IsBCMEntity", iIsBCMEntity.ToString());
            HttpContext.Session.SetString("BIAID", iBIAID.ToString());

            ViewBag.ProcessNameWithCode = HttpContext.Session.GetString("ProcessNameWithCode") == null ? "" : HttpContext.Session.GetString("ProcessNameWithCode");

            ViewBag.ImpactSeverity = new SelectList(_Utilities.PopulateBIAImpactSeverityType(), "ImpSeverityID", "ImpSeverityValue");

            GetBIAProfileDetails_ForMatrix(iProcessID, iBpProfileID);
            GenerateDataGridView();
            GetCurrentBIAID();

            #endregion

            #region Page_Load Methods

            ViewBag.ImpactSeverityId = GetHighestSeverity();
            AssignDataToMatrix();

            ViewBag.ButtonAccess = _Utilities.ShowButtonsByAccess((objBusinessProcess.Status).ToString(), objBusinessProcess.ApproverID.ToString(), objBusinessProcess.ProcessOwnerID.ToString(),
                                                        objBusinessProcess.AltProcessOwnerID.ToString(), _UserDetails.UserID.ToString(),
                                                        Convert.ToInt32(BCPEnum.PrivilegeID.Modify));

            #endregion
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
    }

    private void AssignDataToMatrix()
    {
        Dictionary<(int rowId, int colId), string> textBoxValues = new Dictionary<(int rowId, int colId), string>();
        try
        {
            List<Impact> lstImpact = _ProcessSrv.BIAProfileImpactTypesByProfID(iBpProfileID);
            var timeIntervalIds = lstBIAProfileMaster.DistinctBy(x => x.TimeIntervalID).Select(u => u.TimeIntervalID).ToArray();
            foreach (Impact objImpact in lstImpact)
            {
                for (int i = 0; i < timeIntervalIds.Length; i++)
                {
                    if (objImpact.ImpactID > 0 && timeIntervalIds[i] > 0 && ViewBag.ImpactSeverityId > 0)
                    {
                        string strCost = GetRTOData(objImpact.ImpactID, timeIntervalIds[i], ViewBag.ImpactSeverityId);
                        if (string.IsNullOrEmpty(strCost))
                        {
                            strCost = "0";
                        }
                        textBoxValues.Add((objImpact.ImpactID, timeIntervalIds[i]), strCost);
                    }
                }
            }
            ViewBag.TextBoxValues = textBoxValues;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private string GetRTOData(int iImpactID, int iRTOID, int iSeverityID)
    {
        string strCost = string.Empty;
        try
        {
            BIAProfileMaster newBIAProfileMasterList = new BIAProfileMaster();
            if (lstBIAProfileMaster.Count > 0)
            {
                newBIAProfileMasterList = lstBIAProfileMaster.Where(x => x.ImpactID == iImpactID && x.TimeIntervalID == iRTOID && x.ImpactSeverityID == iSeverityID).FirstOrDefault();
                if (newBIAProfileMasterList != null)
                {
                    strCost = newBIAProfileMasterList.Cost;
                    strCost = string.IsNullOrEmpty(strCost) ? "0" : strCost;
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return strCost;
    }

    public int GetHighestSeverity()
    {
        int iHighestSeverity = 1;
        try
        {
            if (lstBIAProfileMaster.Count == 0)
            {
                iIsBCMEntity = Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity"));

                if (iIsBCMEntity == 0)
                {
                    BusinessProcessInfo objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), 1);
                    iBpProfileID = objBusinessProcessInfo.ProfileID;
                }

                if (iIsBCMEntity == 1)
                {
                    BCMEntityInfo objBCMEntityInfo = _ProcessSrv.GetBCMEntityByEntityID(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")));
                    iBpProfileID = Convert.ToInt32(objBCMEntityInfo.ProfileID);
                }
                lstBIAProfileMaster = _ProcessSrv.GetBIAProfileImpactMatrixByProfID(iBpProfileID, Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), 0, Convert.ToInt32(HttpContext.Session.GetString("BIAID")));
            }
            List<BIAProfileMaster> lstBIAProfileMaster_1 = lstBIAProfileMaster.Where(x => x.ImpactSeverityID == 1).ToList();

            if (lstBIAProfileMaster_1 != null && lstBIAProfileMaster_1.Count > 0)
                iHighestSeverity = 1;

            List<BIAProfileMaster> lstBIAProfileMaster_2 = lstBIAProfileMaster.Where(x => x.ImpactSeverityID == 2).ToList();

            if (lstBIAProfileMaster_2 != null && lstBIAProfileMaster_2.Count > 0)
                iHighestSeverity = 2;

            List<BIAProfileMaster> lstBIAProfileMaster_3 = lstBIAProfileMaster.Where(x => x.ImpactSeverityID == 3).ToList();

            if (lstBIAProfileMaster_3 != null && lstBIAProfileMaster_3.Count > 0)
                iHighestSeverity = 3;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return iHighestSeverity;
    }

    [HttpPost]
    public IActionResult QuantitativeBIAMatrix([FromBody] JsonElement objJsonData)
    {
        try
        {
            GetDataFromGridToCollection(objJsonData);

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("QuantitativeBIAMatrix", new
        {
            strSectionID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID")),
            strProcessID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID")),
            strIsBCMEntity = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity")),
            strBIAID = BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID")),
        });
    }

    [HttpGet]
    public void GetDataFromGridToCollection(JsonElement objJsonData)
    {
        //int iSaveRecCount = 0;
        try
        {
            //string strRowCount = objJsonData.GetProperty("iRowCount").GetString();
            string strDDLImpactSeverity = objJsonData.GetProperty("iDDLImpactSeverity").GetString();
            ViewBag.ImpactSeverityId = Convert.ToInt32(strDDLImpactSeverity);
            List<List<int>> parentArrayList = new List<List<int>>();
            foreach (JsonElement objParentArrayList in objJsonData.GetProperty("strRowWiseValues").EnumerateArray())
            {
                List<int> childArray = new List<int>();
                foreach (JsonElement objChildArray in objParentArrayList.EnumerateArray())
                {
                    childArray.Add(objChildArray.GetInt32());
                }
                parentArrayList.Add(childArray);
            }

            List<int> deserializedTimeIntervalIds = new List<int>();
            foreach (JsonElement objTimeIntervalIds in objJsonData.GetProperty("strTimeIntevalIds").EnumerateArray())
            {
                deserializedTimeIntervalIds.Add(objTimeIntervalIds.GetInt32());
            }

            bool bRes = _ProcessSrv.BPBIAMAtrixDeleteByBPIdImpactSevID(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), Convert.ToInt32(strDDLImpactSeverity), 0, Convert.ToInt32(HttpContext.Session.GetString("BIAID")));

            if (iIsBCMEntity == 0)
            {
                BusinessProcessInfo objBusinessProcessInfo = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")), 1);
                iBpProfileID = objBusinessProcessInfo.ProfileID;
            }

            if (iIsBCMEntity == 1)
            {
                BCMEntityInfo objBCMEntityInfo = _ProcessSrv.GetBCMEntityByEntityID(Convert.ToInt32(HttpContext.Session.GetString("ProcessID")));
                iBpProfileID = Convert.ToInt32(objBCMEntityInfo.ProfileID);
            }
            List<Impact> lstImpact = _ProcessSrv.BIAProfileImpactTypesByProfID(iBpProfileID);
            BPBIAMatrix objBPBIAMatrix = new BPBIAMatrix();

            int iCount = 0;
            int iBPBIAMatrixDetailsID = 0;
            int BFBIAMatrixID = 0;
            foreach (Impact objImpact in lstImpact)
            {

                objBPBIAMatrix.ImpactID = objImpact.ImpactID;
                objBPBIAMatrix.BusinessProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
                objBPBIAMatrix.BIAID = GetCurrentBIAID();
                objBPBIAMatrix.ImpactSeverityID = Convert.ToInt32(strDDLImpactSeverity);
                objBPBIAMatrix.ImpactTypeID = objImpact.ImpactTypeID;
                objBPBIAMatrix.UpdatorId = _UserDetails.UserID;
                objBPBIAMatrix.CreatorId = _UserDetails.UserID;
                objBPBIAMatrix.IsQuantitative = 0;

                BFBIAMatrixID = _ProcessSrv.BPBIAMatrixSave(objBPBIAMatrix);

                List<int> childArray = parentArrayList[iCount];
                
                for (int i = 0; i < childArray.Count; i++)
                {
                    if (childArray[i] > 0)
                    {
                        BPBIAMatrixDetails objBPBIAMatrixDetails = new BPBIAMatrixDetails();

                        objBPBIAMatrixDetails.BPMatrixID = BFBIAMatrixID;
                        objBPBIAMatrixDetails.Cost = childArray[i];
                        objBPBIAMatrixDetails.TimeIntervalID = deserializedTimeIntervalIds[i];

                        iBPBIAMatrixDetailsID = _ProcessSrv.BPBIAMatrixDetailsSave(objBPBIAMatrixDetails);
                    }                                        
                }
                iCount++;
            }
            if (iBPBIAMatrixDetailsID > 0 && BFBIAMatrixID > 0)
            {
                UpdateBusinessProcessBIAByBIAID();
                AssignDataToMatrix();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    protected void UpdateBusinessProcessBIAByBIAID()
    {
        try
        {
            _ProcessSrv.ProcessBIAUpdateByBIAID(Convert.ToInt32(HttpContext.Session.GetString("BIAID")), _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private void GetBIAProfileDetails_ForMatrix(int iBPID, int iProfileID)
    {
        try
        {
            lstBIAProfileMaster = _ProcessSrv.GetBIAProfileImpactMatrixByProfID(iProfileID, iBPID, 0, Convert.ToInt32(HttpContext.Session.GetString("BIAID")));
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private void GenerateDataGridView()
    {
        try
        {
            ViewBag.ColumnHeaders = lstBIAProfileMaster.DistinctBy(x => x.TimeIntervalID).Select(u => new { u.TimeIntervalID, u.TimeIntervalText }).ToList();
            GetImpactByProfileID(iBpProfileID);
            //PopulateImpactRating();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private void GetImpactByProfileID(int iBpProfileID)
    {
        try
        {
            List<Impact> lstImpact = _ProcessSrv.BIAProfileImpactTypesByProfID(iBpProfileID);
            ViewBag.ImpactTypeName = lstImpact.DistinctBy(x => x.ImpactTypeID).ToList();
            ViewBag.ImpactName = lstImpact;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private int GetCurrentBIAID()
    {
        BIASection objBIASection = new BIASection();
        int iBIAIDForQualitativeMatrix = 0;
        try
        {
            iBIAIDForQualitativeMatrix = Convert.ToInt32(HttpContext.Session.GetString("BIAID"));
            if (iBIAIDForQualitativeMatrix == 0)
            {
                objBIASection.Version = "1.0";
                objBIASection.VersionChangeDescription = "";
                objBIASection.ApprovalStatus = ((int)BCPEnum.ApprovalType.Initiated).ToString();
                objBIASection.ProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
                objBIASection.SectionID = Convert.ToInt32(HttpContext.Session.GetString("SectionID"));
                objBIASection.IsEffective = 1;
                objBIASection.CreatedBy = _UserDetails.UserID;
                objBIASection.ChangedBy = _UserDetails.UserID;
                objBIASection.IsBCMEntity = Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity"));

                iBIAIDForQualitativeMatrix = _ProcessSrv.ProcessBIASectionSave(objBIASection);

                HttpContext.Session.SetString("BIAID", iBIAIDForQualitativeMatrix.ToString());
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return iBIAIDForQualitativeMatrix;
    }
}

