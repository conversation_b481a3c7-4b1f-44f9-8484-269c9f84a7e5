﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;

namespace BCM.UI.Areas.OrgStructure.Controllers;
[Area("BCMOrgStructure")]
public class DepartmentController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;   

    public DepartmentController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult ManageDepartment()
    {
        List<DepartmentInfo> lstDepartmentInfo = new List<DepartmentInfo>();
        try
        {
            PopulateDropdown();
            lstDepartmentInfo = _ProcessSrv.GetAllDepartment();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View(lstDepartmentInfo);
    }

    [HttpGet]
    public IActionResult AddDepartment()
    {
        try
        {
            PopulateDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_AddDepartment", new DepartmentInfo());
    }

    [HttpPost]
    public IActionResult AddDepartment(DepartmentInfo objDepartmentInfo)
    {
        int bSuccess = 0;
        bool success = false;
        try
        {
            bSuccess = _ProcessSrv.DepartmentMasterSave(objDepartmentInfo);
            success = bSuccess> 0 ? true : false;
            _Utilities.AddLog(_UserDetails.UserID, "", "Department ", "Business function Details '" + objDepartmentInfo.DepartmentName + "'", BCPEnum.ActionType.Saved.ToString(), 0, "0", _UserDetails.UserID, 0); _Utilities.AddLog(_UserDetails.UserID, "", "Department", "Department Details", BCPEnum.ActionType.Saved.ToString(), 0, "0", _UserDetails.UserID, 0);

            #region "DepartmentLevelUser"
            List<DepartmentInfo> objDepartmentColl = new List<DepartmentInfo>(); DepartmentInfo objDeptItem = new DepartmentInfo();
            List<SubFunction> objSubfunctionColl = new List<SubFunction>(); SubFunction objSubFuItem = new SubFunction();


            objSubfunctionColl = _ProcessSrv.GetSubFunctionListByFunctionID(bSuccess.ToString());
            if (objSubfunctionColl != null && objSubfunctionColl.Count > 0)
            {
                foreach (SubFunction objSubfunction in objSubfunctionColl)
                {
                    List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                    OrgRoleRights objrights = new OrgRoleRights();

                    objrights.UserID = objDepartmentInfo.DeptHeadID;//Enter access for dept Head 
                    objrights.CreatedBy = _UserDetails.UserID;
                    objrights.UpdatedBy = _UserDetails.UserID;
                    objrights.OrgGroupID = (_UserDetails.OrgGroupID.Equals(null) || _UserDetails.OrgGroupID.ToString() == "") ? 0 : _UserDetails.OrgGroupID;
                    objrights.OrgID = (_UserDetails.OrgID.Equals(null) || _UserDetails.OrgID.ToString() == "") ? 0 : _UserDetails.OrgID;
                    objrights.UnitID = (objDepartmentInfo.UnitID.Equals(null)) ? 0 : objDepartmentInfo.UnitID;
                    objrights.DeptID = (bSuccess.Equals(null)) ? 0 : bSuccess;
                    objrights.SubDeptID = (objSubfunction.SubFunctionID.Equals(null) || objSubfunction.SubFunctionID == "") ? 0 : Convert.ToInt32(objSubfunction.SubFunctionID);

                    objrightscoll.Add(objrights);
                    int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                    objrights.UserID = objDepartmentInfo.DeptHeadID;//Enter access for ddlAlternateOwner
                    ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                    objrights.UserID = _UserDetails.UserID;//Enter access for CreatedBy
                    ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                }
            }
            else
            {
                List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                OrgRoleRights objrights = new OrgRoleRights();

                objrights.UserID = objDepartmentInfo.DeptHeadID;//Enter access for dept Head 
                objrights.CreatedBy = _UserDetails.UserID;
                objrights.UpdatedBy = _UserDetails.UserID;
                objrights.OrgGroupID = (_UserDetails.OrgGroupID.Equals(null) || _UserDetails.OrgGroupID.ToString() == "") ? 0 : _UserDetails.OrgGroupID;
                objrights.OrgID = (_UserDetails.OrgID.Equals(null) || _UserDetails.OrgID.ToString() == "") ? 0 : _UserDetails.OrgID;
                objrights.UnitID = (objDepartmentInfo.UnitID.Equals(null)) ? 0 : objDepartmentInfo.UnitID;
                objrights.DeptID = (bSuccess.Equals(null)) ? 0 : bSuccess;

                objrightscoll.Add(objrights);
                int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                objrights.UserID = objDepartmentInfo.DeptAltHeadID;//Enter access for ddlAlternateOwner
                ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                objrights.UserID = _UserDetails.UserID;//Enter access for CreatedBy
                ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
            }

            #endregion
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = success, message = success ? objDepartmentInfo.DepartmentName + " Added Successfully" : "Failed To Add Department." });
        }

        return RedirectToAction("ManageDepartment");
    }

    private bool UpdateResource(string strResourceID, string strEmail, string strMobile)
    {
        bool Success = false;
        ResourcesInfo objResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(strResourceID));
        if (objResource != null)
        {
            objResource.CompanyEmail = strEmail;
            objResource.MobilePhone = strMobile;

            Success = _ProcessSrv.ResourcesUpdate(objResource);
        }
        return Success;
    }


    [HttpGet]
    public IActionResult EditDepartment(int iId)
    {
        var objDepartmentInfo = new DepartmentInfo();
        int bSuccess = iId;
        try
        {
            PopulateDropdown();
            objDepartmentInfo = _ProcessSrv.GetDepartmentBydeptId(iId);

            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        
        return PartialView("_EditDepartment", objDepartmentInfo);
    }

    [HttpPost]
    public IActionResult EditDepartment(DepartmentInfo objDepartmentInfo)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.DepartmentMasterUpdate(objDepartmentInfo);
            if (bSuccess)
            {
                #region "DepartmentLevelUser"
                List<DepartmentInfo> objDepartmentColl = new List<DepartmentInfo>(); DepartmentInfo objDeptItem = new DepartmentInfo();
                List<SubFunction> objSubfunctionColl = new List<SubFunction>(); SubFunction objSubFuItem = new SubFunction();


                objSubfunctionColl = _ProcessSrv.GetSubFunctionListByFunctionID(objDepartmentInfo.DepartmentID.ToString());
                if (objSubfunctionColl != null && objSubfunctionColl.Count > 0)
                {
                    foreach (SubFunction objSubfunction in objSubfunctionColl)
                    {
                        List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                        OrgRoleRights objrights = new OrgRoleRights();

                        objrights.UserID = objDepartmentInfo.DeptHeadID;//Enter access for dept Head 
                        objrights.CreatedBy = _UserDetails.UserID;
                        objrights.UpdatedBy = _UserDetails.UserID;
                        objrights.OrgGroupID = (_UserDetails.OrgGroupID.Equals(null) || _UserDetails.OrgGroupID.ToString() == "") ? 0 : _UserDetails.OrgGroupID;
                        objrights.OrgID = (_UserDetails.OrgID.Equals(null) || _UserDetails.OrgID.ToString() == "") ? 0 : _UserDetails.OrgID;
                        objrights.UnitID = (objDepartmentInfo.UnitID.Equals(null)) ? 0 : objDepartmentInfo.UnitID;
                        objrights.DeptID = (objDepartmentInfo.DepartmentID.ToString().Equals(null)) ? 0 : objDepartmentInfo.DepartmentID;
                        objrights.SubDeptID = (objSubfunction.SubFunctionID.Equals(null) || objSubfunction.SubFunctionID == "") ? 0 : Convert.ToInt32(objSubfunction.SubFunctionID);

                        objrightscoll.Add(objrights);
                        int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                        objrights.UserID = objDepartmentInfo.DeptHeadID;//Enter access for ddlAlternateOwner
                        ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                        objrights.UserID = _UserDetails.UserID;//Enter access for CreatedBy
                        ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                    }
                }
                else
                {
                    List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                    OrgRoleRights objrights = new OrgRoleRights();

                    objrights.UserID = objDepartmentInfo.DeptHeadID;//Enter access for dept Head 
                    objrights.CreatedBy = _UserDetails.UserID;
                    objrights.UpdatedBy = _UserDetails.UserID;
                    objrights.OrgGroupID = (_UserDetails.OrgGroupID.Equals(null) || _UserDetails.OrgGroupID.ToString() == "") ? 0 : _UserDetails.OrgGroupID;
                    objrights.OrgID = (_UserDetails.OrgID.Equals(null) || _UserDetails.OrgID.ToString() == "") ? 0 : _UserDetails.OrgID;
                    objrights.UnitID = (objDepartmentInfo.UnitID.Equals(null)) ? 0 : objDepartmentInfo.UnitID;
                    objrights.DeptID = (objDepartmentInfo.DepartmentID.ToString().Equals(null)) ? 0 : objDepartmentInfo.DepartmentID;

                    objrightscoll.Add(objrights);
                    int ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                    objrights.UserID = objDepartmentInfo.DeptAltHeadID;//Enter access for ddlAlternateOwner
                    ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                    objrights.UserID = _UserDetails.UserID;//Enter access for CreatedBy
                    ID = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                }

                #endregion
                bool Success = UpdateResource(objDepartmentInfo.DeptHeadID.ToString(), objDepartmentInfo.HeadEmail, objDepartmentInfo.HeadMobilePhone);
                bool Success1 = UpdateResource(objDepartmentInfo.AltDepartmentHead, objDepartmentInfo.AltHeadEmail, objDepartmentInfo.AltHeadMobilePhone);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objDepartmentInfo.DepartmentName + " Updated Successfully" : "Failed To Updated Department." });
        }
        return RedirectToAction("ManageDepartment");
    }


    [HttpGet]
    public IActionResult DeleteDepartment(int iId)
    {
        var objDepartmentInfo = new DepartmentInfo();

        try
        {
            objDepartmentInfo = _ProcessSrv.GetDepartmentBydeptId(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteDepartment", objDepartmentInfo);
    }

    [HttpPost]
    public IActionResult DeleteDepartment(DepartmentInfo ObjDepartmentInfo)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.DepartmentDeleteBydeptID(ObjDepartmentInfo.DepartmentID, _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? ObjDepartmentInfo.DepartmentName + " Deleted Successfully" : "Failed To Delete Department." });
        }
        return RedirectToAction("ManageDepartment");
    }

    [HttpGet]
    public IActionResult GetResourceDetails(int iId)
    {
        try
        {
            var objResourcesInfo = _ProcessSrv.GetResourcesByResourceID(iId);
            if (objResourcesInfo != null)
            {
                return Json(new { mail = objResourcesInfo.CompanyEmail, mobile = objResourcesInfo.MobilePhone });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return Json(null);
    }

    public IActionResult GetDepartmentByID(int iDepartmentId)
    {
        try
        {
            List<DepartmentInfo> lstDepartmentInfo = _ProcessSrv.GetAllDepartment();
            if (iDepartmentId > 0)
            {
                lstDepartmentInfo = lstDepartmentInfo.Where(x => x.DepartmentID == iDepartmentId).ToList();
                //if (lstDepartmentInfo == null || !lstDepartmentInfo.Any())
                //{
                //    return NotFound("No Records Found.");
                //}
            }
            return PartialView("_FilterDepartment", lstDepartmentInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageDepartment");
    }

    [HttpGet]
    public JsonResult GetDepartmentsByUnitID(int iOrgID,int iUnitID)
    {
        try
        {
           // var DepartmentList = _ProcessSrv.GetDepartmentByUnitId(unitID);
            var DepartmentList = _Utilities.PupulateDepartment( _UserDetails.OrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString());

            return Json(DepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    public IActionResult GetUnitByID(int iUnitId)
    {
        try
        {
            List<DepartmentInfo> lstDepartmentInfo = _ProcessSrv.GetAllDepartment();
            if (iUnitId > 0)
            {
                lstDepartmentInfo = lstDepartmentInfo.Where(x => x.UnitID == iUnitId).ToList();
                //if (lstDepartmentInfo == null || !lstDepartmentInfo.Any())
                //{
                //    return NotFound("No Records Found.");
                //}
            }
            return PartialView("_FilterDepartment", lstDepartmentInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageDepartment");
    }

    public void PopulateDropdown()
    {
        try
        {

            ViewBag.DepartmentInfo = new SelectList(_Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString()), "DepartmentID", "DepartmentName");
            ViewBag.OrgInfo = new SelectList(_Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString()), "Id", "OrganizationName");
            ViewBag.selectedOrgID = _UserDetails.OrgID;
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
            ViewBag.OrgUnit = new SelectList(_Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(), _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString()), "UnitID", "UnitName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    [HttpGet]
    public JsonResult GetAllUnits(int iOrgID)
    {
        try
        {
            var objDepartmentList = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());

            //var objDepartmentList = _Utilities.BindUnit(iOrgID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }
}

