﻿@{
    ViewBag.Title = "ManageBCMStrategy";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int iIndex = 1;
}

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Manage BCM Strategy</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

        <div class="input-group">
            <span class="input-group-text py-1"><i class="cv-entity-type"></i></span>
            <select class="form-select form-control" autocomplete="off" id="ddlEntityType" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.EntityType,"BCMEntityID","BCMEntityName"))">
                <option selected value="0">-- Select Entity Type --</option>
            </select>
        </div>


        <div class="input-group">
            <span class="input-group-text py-1"><i class="cv-entity"></i></span>
            <select class="form-select form-control" autocomplete="off" id="ddlEntity" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.Entity,"EntityID","EntityName"))">
                <option selected value="0">-- Select Entity --</option>
            </select>
        </div>


        <div class="input-group Search-Input1">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <a asp-controller="Unit" asp-action="AddUnit" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</a>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>
<div class="Page-Condant card border-0">

    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Strategy</th>
                <th>Entity Type</th>
                <th>Org Level</th>
                <th>Owner</th>
                <th>Approval</th>
                <th>Status</th>
                <th>Review&nbsp;Date</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in ViewBag.Strategy)
            {
                var strEntityType = ((BCM.Shared.BCPEnum.EntityType)Convert.ToInt32(item.EntityTypeID)).ToString();

                <tr>
                    <td>@iIndex</td>
                    <td>
                        <ul class="ps-0 mb-0">
                            <li class="list-group-item fw-semibold text-primary"> @item.StratCode</li>
                            <li class="list-group-item ">@item.StrategyName</li>
                        </ul>

                    </td>
                    <td>
                        <ul class="ps-0 mb-0">
                            <li class="list-group-item">
                                <i class="cv-business-process me-1"></i> @strEntityType
                            </li>
                            
                            <li class="list-group-item "><i class="cv-business-process me-1"></i>@item.EntityName</li>
                        </ul>
                    </td>
                    <td>
                        <ul class="ps-0 mb-0">
                            <li class="list-group-item" title="Organization"><i class="cv-organization me-1"></i>@item.OrganizationName</li>
                            <li class="list-group-item" title="Unit"><i class="cv-unit me-1"></i>@item.UnitName</li>
                            <li class="list-group-item" title="Department"><i class="cv-department me-1"></i>@item.DepartmentName</li>
                            <li class="list-group-item" title="Sub Department"><i class="cv-subdepartment me-1"></i>@item.SubfunctionName</li>

                        </ul>
                    </td>

                    <td>
                        <div class="d-flex">
                            @* <div class="User-icon">
                                <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                            </div> *@
                            <div>
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item fw-semibold"><i class="cv-user"></i>:@item.OwnerName</li>
                                    <li class="list-group-item"><a class="text-primary" href="#"><i class="cv-mail"></i>:@item.OwnerEmail</a></li>
                                    <li class="list-group-item"><i class="cv-phone"></i>:@item.OwnerMobile</li>

                                </ul>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex">
                           @*  <div class="User-icon">
                                <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                            </div> *@
                            <div>
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item fw-semibold"><i class="cv-user"></i>:@item.ApproverName</li>
                                    <li class="list-group-item"><a class="text-primary" href="#"><i class="cv-mail"></i>:@item.ApproverEmail</a></li>
                                    <li class="list-group-item"><i class="cv-phone"></i>:@item.ApproverMobile</li>

                                </ul>
                            </div>
                        </div>
                    </td>

                    @if (Convert.ToString(@item.Status) == "0")
                    {
                        <td>
                            <span class="badge bg-info-subtle text-info py-2 fs-14 px-2 fw-normal">
                                <i class="cv-initiated me-1"></i>
                                Initiated
                            </span>
                        </td>
                    }
                    @if (Convert.ToString(@item.Status) == "1")
                    {
                        <td>
                            <span class="badge bg-warning-subtle text-warning py-2 fs-14 px-2 fw-normal">
                                <i class="cv-waiting me-1"></i>
                                WaitingForApprove
                            </span>
                        </td>
                    }
                    @if (Convert.ToString(@item.Status) == "2")
                    {
                        <td>
                            <span class="badge success-light-bg py-2 fs-14 px-2 fw-normal">
                                <i class="cv-success me-1"></i>
                                Approved
                            </span>
                        </td>
                    }
                    @if (Convert.ToString(@item.Status) == "3")
                    {
                        <td>
                            <span class="badge danger-light-bg py-2 fs-14 px-2 fw-normal">
                                <i class="cv-error me-1"></i>
                                DisApproved
                            </span>
                        </td>
                    }
                    <td>@item.NextReviewDate.ToString("dd/MM/yyyy")</td>
                    <td>
                        <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                        <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                    </td>
                </tr>
                iIndex++;
            }
        </tbody>
    </table>

    <!-- Configuration Modal -->
    <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">BCM Strategy Configuration</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pt-0">
                    <div class="tab-design ">
                        <ul class="nav nav-tabs gap-3 " id="myTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active ps-0" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane" type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">
                                    Configure BCM Strategy
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane" type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false" tabindex="-1">
                                    Define BCM Strategy Options
                                </button>
                            </li>

                        </ul>
                    </div>

                    <div class="tab-content mt-3" id="myTabContent">
                        <div class="tab-pane fade active show" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">
                            <section>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="form-label">Organization</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-organization"></i></span>
                                                <select class="form-select form-select-sm" required>
                                                    <option value="value">Perpetuuiti</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Department</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-department"></i></span>
                                                <select class="form-select form-select-sm" required>
                                                    <option value="value">All Department</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Entity Type</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-entity-type"></i></span>
                                                <select class="form-select form-select-sm" required>
                                                    <option value="value">All Entities</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">BCM Strategy Name</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-name"></i></span>
                                                <input type="text" class="form-control" placeholder="Enter BCM Strategy Name" required />
                                            </div>
                                        </div>




                                    </div>
                                    <div class="col-6">

                                        <div class="form-group">
                                            <label class="form-label">Unit</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-unit"></i></span>
                                                <select class="form-select form-select-sm">
                                                    <option value="value">Units</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Sub Department</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                                                <select class="form-select form-select-sm">
                                                    <option value="value">Sub Department</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Entity</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-entity"></i></span>
                                                <select class="form-select form-select-sm">
                                                    <option value="value">Select Entities</option>
                                                </select>
                                            </div>
                                        </div>




                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="form-label">Strategy Owner</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-owner"></i></span>
                                                <select class="form-select form-select-sm">
                                                    <option value="value">Select Strategy Owner</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="form-label">Plan Approver</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-develop-Plan"></i></span>
                                                <select class="form-select form-select-sm">
                                                    <option value="value">Plan Approver</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Description</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-description"></i></span>
                                            <input type="text" class="form-control" placeholder="Enter Description" required />
                                        </div>
                                    </div>
                                    <h6 class="Sub-Title">Review Section</h6>
                                    <div class="col">
                                        <div class="form-group">
                                            <label class="form-label">Last Review Date</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-calendar"></i></span>
                                                <input type="date" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="form-group">
                                            <label class="form-label">Next Review Date</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cv-calendar"></i></span>
                                                <input type="date" class="form-control" required/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                        <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">
                            <div class="row mt-3">
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Strategy Options
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-organization"></i></span>
                                            <select class="form-select form-select-sm">
                                                <option value="value">Select Options</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Linking RTO Range From
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-organization"></i></span>
                                            <input type="time" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Linking RTO Range To

                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-organization"></i></span>
                                            <input type="time" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Feasibility Rating ( 60% )
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-rating"></i></span>
                                            <select class="form-select form-select-sm">
                                                <option value="value">Low (1)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Investment Rating ( 20% )
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-rating"></i></span>
                                            <select class="form-select form-select-sm">
                                                <option value="value">Low (1)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Maintainability Rating ( 20% )
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-rating"></i></span>
                                            <select class="form-select form-select-sm">
                                                <option value="value">Low (1)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 d-flex justify-content-end">
                                    <button class="btn btn-sm btn-primary">Add Options & Rating</button>
                                </div>
                                <div class="my-2">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th class="SrNo_th">Sr. No.</th>
                                                <th>Select</th>
                                                <th>
                                                    Option Name
                                                </th>
                                                <th>Linking Range</th>
                                                <th>Feasibility Rating</th>
                                                <th>Investment Rating</th>
                                                <th>Maintainability Rating</th>
                                                <th>Score</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>1</td>
                                                <td><input type="checkbox" class="form-check" /></td>
                                                <td>Hired Hot Site</td>
                                                <td>2 Hour TO 4 Hour </td>
                                                <td>Medium</td>
                                                <td>Medium</td>
                                                <td>Low</td>
                                                <td>1.8</td>
                                                <td>
                                                    <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                                                    <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                                                </td>

                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                                <div class="my-2">
                                    <h6 class="Sub-Title">Preferred Option(s)</h6>
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th class="SrNo_th">Sr. No.</th>

                                                <th>
                                                    Option Name
                                                </th>
                                                <th>Linking Range</th>
                                                <th>Feasibility Rating</th>
                                                <th>Investment Rating</th>
                                                <th>Maintainability Rating</th>
                                                <th>Score</th>

                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="7">
                                                    <div class="text-center">
                                                        <img src="/img/Isomatric/no_records_to_display.svg" class="img-fluid" style="width:15%" />
                                                    </div>
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-12 d-flex justify-content-end">
                                    <button class="btn btn-sm btn-primary">Add / Edit Strategy Summary</button>
                                </div>
                            </div>

                        </div>
                    </div>



                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--End Configuration Modal -->

    <!-- Delete Modal -->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header d-grid text-center">
                    <span class="fw-semibold">Do you really want to delete</span>
                    <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
                </div>
                <div class="modal-body text-center">
                    <img src="~/img/isomatric/delete.svg" width="260" />
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                    <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End Delete Modal -->
</div>
