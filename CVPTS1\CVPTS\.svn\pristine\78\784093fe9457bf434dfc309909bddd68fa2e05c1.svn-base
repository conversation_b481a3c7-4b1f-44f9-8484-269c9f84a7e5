﻿@model IEnumerable<BCM.BusinessClasses.ProcessBIAFacility>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@using BCM.Shared;
@{
    ViewBag.Title = "Building and Workspace Requirements";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@{
    var ProcessName = HttpContextAccessor.HttpContext.Session.GetString("ProcessNameWithCode");
    var ProcessVersion = HttpContextAccessor.HttpContext.Session.GetString("ProcessVersion");
}
@* <div class="Page-Header d-flex align-items-center justify-content-between mb-3">
    <h6 class="Page-Title">Building and Workspace Requirements</h6>
</div> *@

<div class="Page-Condant card border-0">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        <p style="padding-left:1%" class="fw-bold mb-2">Building and Workspace Requirements for @ViewBag.ProcessName ( @ViewBag.ProcessCode )</p>
        <div class="align-items-right" style="padding-right:2%">
            @* <p class="fw-semibold" id="iVersion">Version : @ViewBag.ProcessVersion</p> *@
            <p class="fw-semibold" id="iVersion">Version : @ProcessVersion</p>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-12">
                <div class="accordion accordion-flush" id="accordionFlushExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button  btn-primary text-white rounded" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="true" aria-controls="flush-collapseOne">
                                Instructions and Guidelines
                            </button>
                        </h2>
                        <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                            <div class="accordion-body">
                                @*  <textarea name="content" disabled contenteditable="false" id="editor2">@ViewBag.Description</textarea >*@
                             @*    <div id="editor2" class="content-editable"> *@
                                    @Html.Raw(ViewBag.Description)
                           @*      </div> *@
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">
                <div>                    
                    <div class="">
                        @foreach (var objQusetions in ViewBag.Questions)
                        {
                            <p class="text-primary d-flex align-items-center gap-1">
                                <span class="d-flex align-items-center justify-content-center rounded-circle toggle-password collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsequestion1" aria-expanded="false" aria-controls="collapseExample"><i class="cv-minus align-middle"></i></span>
                                Question: @objQusetions.QuestionDetails
                            </p>
                        }
                        <div class="ps-2 collapse show" id="collapsequestion1">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Facility Name</th>
                                        <th>Facility Address</th>
                                        <th>Type</th>
                                        <th>Site In charge</th>
                                        <th>Location</th>
                                       @*  <th>Completion Status</th> *@
                                        <th style="text-align:right !important;">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model != null)
                                    {
                                        int iIndex = 0;
                                        foreach (var objQusetions in ViewBag.Questions)
                                        {
                                            @* <tr data-bs-toggle="collapse" data-bs-target="#collapseQuestion" aria-expanded="false" aria-controls="collapseQuestion" role="button">
                                                <td class="bg-secondary-subtle"><i class="cv-down-arrow ms-2"></i></td>
                                                <td class="bg-secondary-subtle" colspan="10">Question: @objQusetions.QuestionDetails</td>
                                            </tr> *@

                                            foreach (var item in Model)
                                            {
                                                string CompletionStatus = item.IsComplete == 0 ? "Incomplete" : "Complete";
                                                string strFacType = string.Empty;

                                                @if (Convert.ToInt32(item.FacilityType) == (int)BCPEnum.FacilityType.People_are_located_here)
                                                {
                                                    strFacType = "People_are_located_here";

                                                }
                                                else if (Convert.ToInt32(item.FacilityType) == (int)BCPEnum.FacilityType.Application_hosted)
                                                {
                                                    strFacType = "Application_hosted";

                                                }
                                                else if (Convert.ToInt32(item.FacilityType) == (int)BCPEnum.FacilityType.Partner_Or_ThirdParty_facility)
                                                {
                                                    strFacType = "Partner_Or_ThirdParty_facility";
                                                }

                                                if (objQusetions.ID == item.QuestionID)
                                                {
                                                    iIndex++;
                                                    <tr>
                                                        <td>
                                                            @iIndex
                                                        </td>
                                                        <td>
                                                            @item.FacilityName
                                                        </td>
                                                        <td>
                                                            @item.FacilityAddress
                                                            <input type="hidden" id="FacilityAddress" value="@item.FacilityAddress" />
                                                        </td>
                                                        <td>
                                                            @strFacType
                                                        </td>
                                                        <td>
                                                            @item.FacilityManagerName
                                                        </td>
                                                        <td>@item.LocationName</td>
                                                        @* <td>@CompletionStatus</td> *@
                                                        <td style="text-align:right !important;">
                                                            <span role="button"><i class="cv-edit me-1 btnEdit" @ViewBag.ButtonAccess.btnUpdate data-id="@item.ID"></i></span>
                                                            <span class="btn-action btnDelete" type="button" @ViewBag.ButtonAccess.btnImgDelete data-id="@item.ID" data-bs-toggle="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                                                        </td>
                                                    </tr>
                                                }
                                            }
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <form asp-action="AddOrEditBIAFacility" method="post" id="addOrEditBIAFacility" class="needs-validation progressive-validation" novalidate>
                    <div class="row row-cols-2">
                        <div class="col">
                            @* <div class="form-group">
                                <label class="form-lable">Version</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-version"></i></span>
                                    <input class="form-control" type="text" readonly value="1.0" />
                                   
                                </div>
                            </div> *@
                            <div class="form-group">
                                <label class="form-lable">Questions</label>
                                @foreach (var objQusetions in ViewBag.Questions)
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" checked="checked" name="QuestionID" id="@objQusetions.ID" value="@objQusetions.ID">
                                        <label class="form-check-label" for="inlineRadio1">@objQusetions.QuestionDetails</label>
                                    </div>
                                }
                            </div>
                            <div class="form-group">
                                <input type="hidden" value="" name="ID" id="Id" />
                                <label class="form-lable">Facility Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-facility-name"></i></span>
                                    <select class="form-select-sm form-control selectized" id="ddlFacility" name="FacilityID" required>
                                        <option selected disabled value="0">-- Select Facility Name --</option>
                                        @foreach (var objFacility in ViewBag.Facility)
                                        {
                                            <option value="@objFacility.Value">@objFacility.Text</option>
                                        }
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select Facility Name</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Facility Address</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-facility-address"></i></span>
                                    <textarea class="form-control" placeholder="Enter Facility Address" style="height:0px" id="FacilityAddress" name="FacilityAddress"></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Facility Address</div>
                                @* <div class="text-end text-secondary">10000 characters left.</div> *@
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Total Platform HC by Location</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-platform-location"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter Total Platform HC by Location" name="TotalPlatformByHCLoacation" required pattern="[0-9]+" title="Only Numbers allowed" />
                                </div>
                                <div class="invalid-feedback">Enter Total Platform HC by Location</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Current process SLA</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-process-name"></i></span>
                                    <input type="text" class="form-control" placeholder="Enter Current process SLA" name="CurrentProcessSLA" />
                                </div>
                                <div class="invalid-feedback">Enter Current process SLA</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Number of resources that can be moved to DR site</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-resources"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter DR site" name="ResourceMovedToDRSite" pattern="[0-9]+" title="Only Numbers allowed" />
                                </div>
                                <div class="invalid-feedback">Enter Number of resources that can be moved to DR site</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Number of resources that can be flown to other EGS locations</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-resources"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter EGS locations" name="ResourceMovedToEGSLocations" pattern="[0-9]+" title="Only Numbers allowed" />
                                </div>
                                <div class="invalid-feedback">Enter Number of resources that can be flown to other EGS locations</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Impact</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-impact"></i></span>
                                    <select class="form-select-sm form-control selectized" name="Impact" required>
                                        <option selected disabled value="">-- Select Impact --</option>
                                        @foreach (var objImpact in ViewBag.Impact)
                                        {
                                            <option value="@objImpact.Value">@objImpact.Text</option>
                                        }
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select Impact</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Type</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-facility-name"></i></span>
                                    <select class="form-select-sm form-control selectized" name="FacilityType" required>
                                        <option selected disabled value="">-- Select Facility Type --</option>
                                        @foreach (var objFacilityType in ViewBag.FacilityType)
                                        {
                                            <option value="@objFacilityType.Value">@objFacilityType.Text</option>
                                        }
                                    </select>
                                </div>
                                <div class="invalid-feedback">Select Type</div>
                            </div>

                            <div class="form-group">
                                <label class="form-lable">Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Description" style="height:0px" name="iBIAFindings"></textarea>
                                </div>
                                <div class="invalid-feedback">Enter Description</div>
                                @* <div class="text-end text-secondary">10000 characters left.</div> *@
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Team Distribution</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-team-distribution"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter Team distribution" name="TeamDistribution" pattern="[0-9]+" title="Only Numbers allowed" />
                                </div>
                                <div class="invalid-feedback">Enter No Of Team Distribution</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Number of team members having the ability to connect from home</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-team-size"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter Connect from home" name="MemberConnectFromHome" pattern="[0-9]+" title="Only Numbers allowed" />
                                </div>
                                <div class="invalid-feedback">Enter Number of team members having the ability to connect from home</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Number of resources that can moved to another location</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-resources"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter Another location" name="ResourceMovedToAnotherLocation" pattern="[0-9]+" title="Only Numbers allowed" />
                                </div>
                                <div class="invalid-feedback">Enter Number of resources that can moved to another location</div>
                            </div>
                            <div class="form-group">
                                <label class="form-lable">Increasing shift time at DR Site</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-resources"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter Increasing shift time at DR Site" name="IncreasingShiftTiming" pattern="[0-9]+" title="Only Numbers allowed" />
                                </div>
                                <div class="invalid-feedback">Enter Increasing shift time at DR Site</div>
                            </div>
                        </div>
                        <div class="col-12 text-end">
                            <a class="btn btn-sm btn-outline-primary" role="button" asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>
                            @* <a role="button" class="btn btn-sm btn-primary" asp-action="ManageBusinessProcess" asp-controller="ManageBusinessProcesses" asp-area="BCMProcessBIA" formnovalidate>View All</a> *@
                            <button type="submit" @ViewBag.ButtonAccess.btnUpdate class="btn btn-sm btn-primary" id="btnSubmit">Save</button>
                            <button class="btn btn-sm btn-secondary" id="btnCancel" formnovalidate>Cancel</button>
                

                        </div>
                    </div>
                </form>
                <form asp-action="AddOrEditBIADependentEntities"  method="post">
                    <div style="display:none;" class="row row-cols-3">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">From</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-clock"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter Form Count" name="From_Shift" />
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">To</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-clock"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter To Count" name="To_Shift" />
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Resource Count</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-resources"></i></span>
                                    <input type="number" class="form-control" placeholder="Enter Resource Count" name="ResourceCount" pattern="[0-9]+" title="Only Numbers allowed" />
                                    <span class="input-group-text">
                                        <button type="submit" id="btnCreate" style="display:none;" class="btn icon-btn btn-primary btn-sm"><i class="cv-Plus p-0"></i></button>                                       
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
             // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function() {
                        console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function() {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                console.log("Initializing validation for addOrEditBIAFacility form");

                if (window.BCMValidation) {
                    console.log("BCMValidation found, initializing");

                    // Get the form element
                    const form = document.getElementById('addOrEditBIAFacility');
                    if (!form) {
                        console.error("Form not found with ID: addOrEditBIAFacility");
                        return;
                    }

                    // Store the original content of all invalid-feedback divs
                    const customMessages = {};
                    form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                        // Find the associated input
                        const formGroup = element.closest('.form-group');
                        const input = formGroup?.querySelector('input, select, textarea');
                        if (input) {
                            // Store the custom message using the input's ID or name as the key
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key) {
                                customMessages[key] = element.textContent.trim();
                                console.log("Stored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    // Override the validateInput function to preserve custom messages
                    const originalValidateInput = window.BCMValidation.validateInput;
                    window.BCMValidation.validateInput = function(input, forceValidation = false) {
                        // Get the result from the original function
                        const result = originalValidateInput(input, forceValidation);

                        // If the input is invalid, restore the custom message
                        if (!result) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        }

                        return result;
                    };

                    // // Override the validateEmail function similarly
                    // const originalValidateEmail = window.BCMValidation.validateEmail;
                    // window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                    //     // Get the result from the original function
                    //     const result = originalValidateEmail(input, forceValidation);

                    //     // If the input is invalid, restore the custom message
                    //     if (!result) {
                    //         const key = input.id || input.name || input.getAttribute('asp-for');
                    //         if (key && customMessages[key]) {
                    //             const formGroup = input.closest('.form-group');
                    //             const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                    //             if (feedbackElement) {
                    //                 // Restore the custom message
                    //                 feedbackElement.textContent = customMessages[key];
                    //                 feedbackElement.style.display = 'block';
                    //                 console.log("Restored custom message for", key, ":", customMessages[key]);
                    //             }
                    //         }
                    //     }

                    //     return result;
                    // };

                    // Override the validatePatternInput function similarly
                    const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                    window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                        // Get the result from the original function
                        const result = originalValidatePatternInput(input, forceValidation);

                        // If the input is invalid, restore the custom message
                        if (!result) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        }

                        return result;
                    };

                    // Override the validateForm function to restore all custom messages after validation
                    const originalValidateForm = window.BCMValidation.validateForm;
                    window.BCMValidation.validateForm = function(form) {
                        // Get the result from the original function
                        const result = originalValidateForm(form);

                        // Restore all custom messages for invalid inputs
                        form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        });

                        return result;
                    };

                    // Initialize the validation framework
                    window.BCMValidation.init();

                    // Add required field indicators (asterisks)
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for pattern-based inputs
                    window.BCMValidation.addFormatIndicators(form);

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");

                        // Show all validation messages
                        window.BCMValidation.showAllValidationMessages(form);

                        // Validate the form
                        const isValid = window.BCMValidation.validateForm(form);
                        console.log("Form validation result:", isValid);

                        if (!isValid) {

                            console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                        }
                    });
                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }

            UpdateButtonLabel();

            $('.btnEdit').click(function () {
                var iID = $(this).data('id');
                // Clear validation errors before populating new values
                const form = document.getElementById('addOrEditBIAFacility');
                if (form) {
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function (input) {
                        input.classList.remove(window.BCMValidation.classes.invalidClass);
                    });

                    form.querySelectorAll('.invalid-feedback').forEach(function (feedback) {
                       // feedback.textContent = '';
                        feedback.style.display = 'none';
                    });
                }

                $.ajax({
                    url: '@Url.Action("EditBIAFacility", "BIAFacility")',
                    type: 'GET',
                    data: { iID: iID },
                    success: function (data) {

                        console.log(data);
                        $('select[name="FacilityID"]').val(data.facilityID);
                        $('textarea[name="FacilityAddress"]').val(data.facilityAddress);
                        $('input[name="TotalPlatformByHCLoacation"]').val(data.totalPlatformByHCLoacation);
                        $('input[name="CurrentProcessSLA"]').val(data.currentProcessSLA);
                        $('input[name="ResourceMovedToDRSite"]').val(data.resourceMovedToDRSite);
                        $('input[name="ResourceMovedToEGSLocations"]').val(data.resourceMovedToEGSLocations);
                        $('select[name="Impact"]').val(data.impact);
                        $('select[name="FacilityType"]').val(data.facilityType);                     
                        $('input[name="TeamDistribution"]').val(data.teamDistribution);
                        $('input[name="MemberConnectFromHome"]').val(data.memberConnectFromHome);
                        $('input[name="ResourceMovedToAnotherLocation"]').val(data.resourceMovedToAnotherLocation);
                        $('input[name="IncreasingShiftTiming"]').val(data.increasingShiftTiming);
                        $('input[name="QuestionID"][value="' + data.questionID + '"]').prop('checked', true);
                        $('#Id').val(data.id);
                        $('textarea[name="iBIAFindings"]').val(data.iBIAFindings);

                        UpdateButtonLabel();
                        if (window.BCMValidation && typeof window.BCMValidation.init === 'function') {
                                window.BCMValidation.init();
                        }
                    },
                    error: function () {
                        console.log('Error');
                    }
                });
            });

            $(document).on('click', '.btnDelete', function () {
                var iID = $(this).data('id');
                //$.get('/BCMProcessBIAForms/BIAFacility/DeleteProcessBIAFacility/', { iID: iID }, function (data) {
                $.get('@Url.Action("DeleteProcessBIAFacility", "BIAFacility")', { iID: iID }, function (data) {
                    $('#DeleteBody').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

            $(document).on('click', '#btnCancel', function (event) {
                event.preventDefault();
                location.reload();
            });

            $(document).on('change', '#ddlFacility', function () {

                 var iID = $(this).val();


                  $.ajax({
                    url: '@Url.Action("GetFacilities_ByID", "BIAFacility")',
                    type: 'GET',
                    data: { iID: iID },
                    success: function (data) {

                        console.log(data);                       
                        $('textarea[name="FacilityAddress"]').val(data.facilityAddress);                       
                        //UpdateButtonLabel();
                        if (window.BCMValidation && typeof window.BCMValidation.init === 'function') {
                                window.BCMValidation.init();
                        }
                    },
                    error: function () {
                        console.log('Error');
                    }
                });

                // $.get('@Url.Action("GetFacilities_ByID", "BIAFacility")', { iID: iID }, function (data) {
                //      success: function (data) {
                //          alert(data);
                //    $("#FacilityAddress").val(data.facilityAddress);
                  
                //     }

                // });
            });

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('delete')) {
                                toastElement.addClass('bg-danger');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        // Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

        });

        
        function UpdateButtonLabel() {
            var id = $('input[name="ID"]').val();
            if (id > 0) {
                $('#btnSubmit').text('Update');
            } else {
                $('#btnSubmit').text('Save');
            }
        }
    </script>
}