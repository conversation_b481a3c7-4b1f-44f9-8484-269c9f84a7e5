﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BCM.Shared
{
    public class BCPIncidentNotification
    {
        private readonly ProcessSrv _ProcessSrv;
        readonly CVLogger _CVLogger;
        public BCPIncidentNotification(ProcessSrv ProcessSrv, CVLogger CVLogger)
        {
            _ProcessSrv = ProcessSrv;
            _CVLogger = CVLogger;
        }
        public string GetEmailBodyforNotification(string strUsername, string strSMSBody, string strLink, string strRespText, string strLink2, string strRespText2)
        {
            string strMessage = string.Empty;
            StringBuilder sb = new StringBuilder();
            try
            {                
                sb.Append("Dear ");
                sb.Append(strUsername);
                sb.Append(",<br/><br/>");
                sb.Append(" This is to inform you that ");
                sb.Append(strSMSBody);
                if (!string.IsNullOrEmpty(strLink))
                {
                    sb.Append("<br /><br />Please");
                    sb.Append(" <a href='" + strLink);
                    sb.Append("'>Click here</a> " + strRespText + ". <br />");
                }
                if (!string.IsNullOrEmpty(strLink2))
                {
                    sb.Append("<br /><br />Please    ");
                    sb.Append(" <a href='" + strLink2);
                    sb.Append("'>Click here</a> " + strRespText2 + ". <br /><br />");
                }
                sb.Append(" <br/><br /> <br /> <br />Thank you.<br /><br /><b>Admin</b><br />Continuity Vault");                
            }
            catch (Exception e)
            {
                //sb = null;
                strMessage = e.ToString();
            }
            return sb.ToString();
        }

        public int AddIncidentNotification(string strIncidentID, string strIncidentStepID, string strStepID,
            string strNotifiedBy, string strNotifiedTo, string strSubject, string strBody, string strCommunicationID, string strTeamID,
            string strStatus, string strInBound, string StepExecutionStatus = "")
        {

            try
            {
                IncidentNotificationHistory obj = new IncidentNotificationHistory();

                obj.IncidentID = Convert.ToInt32(strIncidentID);
                obj.IncidentStepID = Convert.ToInt32(strIncidentStepID);
                obj.StepID = Convert.ToInt32(strStepID);
                obj.CommunicationID = Convert.ToInt32(strCommunicationID);
                obj.NotifiedBy = Convert.ToInt32(strNotifiedBy);
                obj.Subject = strSubject;
                obj.Body = strBody;
                obj.TeamID = Convert.ToInt32(strTeamID);
                obj.NotifiedTo = strNotifiedTo;
                obj.Status = strStatus;
                obj.IsInbound = Convert.ToInt32(strInBound);
                obj.StepExecutionStatus = StepExecutionStatus;

                return _ProcessSrv.IncidentNotificationHistorySave(obj);
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApi(ex);
                return 0;
            }
        }
    }
}
