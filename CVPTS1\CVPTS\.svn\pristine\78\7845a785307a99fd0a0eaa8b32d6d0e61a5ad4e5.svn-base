@{
    ViewData["Title"] = "Training Question Paper";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Ensure jQuery is loaded before our scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"
        integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="
        crossorigin="anonymous"></script>

<div class="Page-Header">
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">@ViewBag.TrainingName - Question Paper</h6>
        <div class="d-flex align-items-center">
            <div id="timerDisplay" class="badge bg-warning text-dark fs-6 me-3">
                <i class="fas fa-clock me-1"></i>
                <span id="timeRemaining">00:00:00</span>
            </div>
            <div class="badge bg-info text-white fs-6">
                Question <span id="currentQuestionNumber">@(ViewBag.CurrentQuestionIndex ?? 1)</span> of <span id="totalQuestions">@(ViewBag.TotalQuestions ?? 1)</span>
            </div>
        </div>
    </div>
</div>

<div class="Page-Content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <!-- Question Content Container -->
                        <div id="questionContainer">
                            <!-- Loading indicator -->
                            <div id="loadingIndicator" class="text-center py-5" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2 text-muted">Loading question...</p>
                            </div>

                            <!-- Question Content -->
                            <div id="questionContent">
                                @if (ViewBag.ShowError == true)
                                {
                                    <div class="alert alert-warning text-center">
                                        <i class="fas fa-exclamation-triangle fs-1 text-warning mb-3"></i>
                                        <h5>@ViewBag.ErrorMessage</h5>
                                        <p class="text-muted">Please contact your administrator or try again later.</p>
                                        <a href="@Url.Action("TrainingandExamination", "TrainingandExamination")" class="btn btn-primary">
                                            <i class="fas fa-arrow-left me-1"></i>Back to Training List
                                        </a>
                                    </div>
                                }
                                else
                                {
                                    <div class="question-header mb-4">
                                        <h5 class="question-title mb-3">
                                            <span class="badge bg-primary me-2">Q<span id="questionIndex">@(ViewBag.CurrentQuestionIndex ?? 1)</span></span>
                                            <span id="questionText">@(ViewBag.QuestionText ?? "Loading question...")</span>
                                        </h5>
                                    </div>

                                    <!-- Options Container -->
                                    <div id="optionsContainer" class="options-list">
                                        @if (ViewBag.QuestionOptions != null)
                                        {
                                            @foreach (var option in ViewBag.QuestionOptions)
                                            {
                                                bool isSelected = ViewBag.SelectedAnswers != null && ViewBag.SelectedAnswers.Contains(option.OptionID.ToString());
                                                bool isDisabled = ViewBag.Watch == 1;

                                                <div class="form-check mb-3 option-item">
                                                    <input class="form-check-input" type="radio"
                                                           name="<EMAIL>"
                                                           value="@option.OptionID"
                                                           id="option_@<EMAIL>"
                                                           @(isSelected ? "checked" : "")
                                                           @(isDisabled ? "disabled" : "")>
                                                    <label class="form-check-label @(isDisabled ? "text-muted" : "")"
                                                           for="option_@<EMAIL>">
                                                        @option.OptionText
                                                    </label>
                                                </div>
                                            }
                                        }
                                        else
                                        {
                                            <div class="text-center py-4 text-muted">
                                                <i class="fas fa-question-circle fs-2 mb-3"></i>
                                                <p>No options available for this question.</p>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Navigation Buttons -->
                        <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                            <!-- Left side: Previous button -->
                            <div>
                                <button type="button" class="btn btn-outline-secondary" id="btnPrevious" style="display: none;">
                                    <i class="fas fa-chevron-left me-1"></i>Previous
                                </button>
                            </div>

                            <!-- Right side: Next, Finish, and View Results buttons - always positioned on the right -->
                            <div class="btn-group ms-auto">
                                <button type="button" class="btn btn-primary" id="btnNext">
                                    Next<i class="fas fa-chevron-right ms-1"></i>
                                </button>
                                <button type="button" class="btn btn-success" id="btnFinish" style="display: none;">
                                    <i class="fas fa-check me-1"></i>Finish Exam
                                </button>
                                <button type="button" class="btn btn-info" id="btnViewResults" style="display: none;">
                                    <i class="fas fa-chart-bar me-1"></i>View Results
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Container -->
<div id="notificationContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
</div>

<!-- Modals -->
<!-- Time Up Modal -->
<div class="modal fade" id="timeUpModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <i class="fas fa-hourglass-end fs-1 text-warning mb-3"></i>
                <h5>Your exam time has expired</h5>
                <p class="text-muted mb-0">The exam has been automatically submitted.</p>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary" onclick="viewResults()">
                    View Results
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Exam Completed Modal -->
<div class="modal fade" id="examCompletedModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <i class="fas fa-trophy fs-1 text-success mb-3"></i>
                <h5>Congratulations!</h5>
                <p class="text-muted mb-0">You have successfully completed the training exam.</p>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success" onclick="viewResults()">
                    View Results
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .option-item {
        padding: 12px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.2s ease;
        background-color: #fff;
    }
    
    .option-item:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    
    .option-item input[type="radio"]:checked + label {
        color: #007bff;
        font-weight: 500;
    }
    
    .question-title {
        color: #2c3e50;
        line-height: 1.6;
    }
    
    #timerDisplay {
        font-family: 'Courier New', monospace;
        font-size: 1.1rem;
        padding: 8px 12px;
    }
    
    .btn-group .btn {
        margin-left: 5px;
    }
    
    .btn-group .btn:first-child {
        margin-left: 0;
    }
</style>

<script>
    // Global variables
    var currentQuestionIndex = @(ViewBag.CurrentQuestionIndex ?? 1);
    var totalQuestions = @(ViewBag.TotalQuestions ?? 1);
    var trainingId = @(ViewBag.TrainingId ?? 0);
    var currentQuestionId = @(ViewBag.CurrentQuestionId ?? 0);
    var publishId = @(ViewBag.PublishID ?? 0);
    var isWatchMode = @(ViewBag.Watch ?? 0) == 1;
    var timeRemaining = @(ViewBag.TimeRemaining ?? 3600);
    var timerInterval;

    // Function to wait for jQuery to be available
    function waitForJQuery(callback) {
        if (typeof jQuery !== 'undefined') {
            console.log('jQuery is available');
            callback();
        } else {
            console.log('Waiting for jQuery...');
            setTimeout(function() {
                waitForJQuery(callback);
            }, 100);
        }
    }

    // Initialize when jQuery is ready
    waitForJQuery(function() {
        console.log('jQuery is loaded successfully');
        initializeTrainingQuestionPaper();
    });

    function initializeTrainingQuestionPaper() {

    $(document).ready(function() {
        try {
            console.log('TrainingQuestionPaper initialized', {
                currentQuestionIndex: currentQuestionIndex,
                totalQuestions: totalQuestions,
                trainingId: trainingId,
                currentQuestionId: currentQuestionId,
                publishId: publishId,
                isWatchMode: isWatchMode
            });

            // Validate required data
            if (!trainingId || trainingId === 0) {
                console.error('Error: Training ID not found. Please refresh the page.');
                return;
            }

            // Initialize timer
            if (!isWatchMode && timeRemaining > 0) {
                startTimer();
            } else {
                updateTimerDisplay();
                if (isWatchMode) {
                    $('#timerDisplay').append(' <span class="badge bg-secondary ms-1">FROZEN</span>');
                }
            }

            // Initialize navigation buttons
            updateNavigationButtons();

            // Bind events
            bindEvents();

            // Show initial question info
            console.log('Question paper loaded successfully. Use arrow keys to navigate.');
        } catch (error) {
            console.error('Error in document ready:', error);
            alert('Error initializing page: ' + error.message);
        }
    });

    function bindEvents() {
        // Previous button
        $('#btnPrevious').on('click', function() {
            console.log('Previous button clicked', {
                isWatchMode: isWatchMode,
                currentQuestionIndex: currentQuestionIndex
            });

            try {
                if (currentQuestionIndex > 1) {
                    if (isWatchMode) {
                        // In watch mode, just navigate without saving
                        console.log('Watch mode: Loading previous question');
                        loadQuestion(currentQuestionIndex - 1, false);
                    } else {
                        // In normal mode, save current answer first, then navigate
                        console.log('Normal mode: Saving current answer before going to previous question');
                        saveAndNavigate('previous', currentQuestionIndex - 1);
                    }
                } else {
                    console.log('Already on first question');
                }
            } catch (error) {
                console.error('Error in previous button click:', error);
                alert('Error navigating to previous question: ' + error.message);
            }
        });

        // Next button
        $('#btnNext').on('click', function() {
            console.log('Next button clicked', {
                isWatchMode: isWatchMode,
                currentQuestionIndex: currentQuestionIndex,
                totalQuestions: totalQuestions
            });

            try {
                if (isWatchMode) {
                    // In watch mode, just navigate
                    if (currentQuestionIndex < totalQuestions) {
                        console.log('Watch mode: Loading next question');
                        loadQuestion(currentQuestionIndex + 1, false);
                    } else {
                        console.log('Watch mode: Already on last question');
                    }
                } else {
                    // In normal mode, save answer first
                    console.log('Normal mode: Saving and navigating');
                    saveAndNavigate('next');
                }
            } catch (error) {
                console.error('Error in next button click:', error);
                alert('Error navigating to next question: ' + error.message);
            }
        });

        // Finish button
        $('#btnFinish').on('click', function() {
            if (confirm('Are you sure you want to finish the exam?')) {
                finishExam();
            }
        });

        // View Results button
        $('#btnViewResults').on('click', function() {
            viewResults();
        });

        // Auto-save on option change (only in normal mode)
        if (!isWatchMode) {
            $(document).on('change', 'input[type="radio"]', function() {
                autoSaveAnswer();
            });
        }
    }

    // Load question by index via AJAX
    function loadQuestion(questionIndex, saveFirst = false) {
        console.log('Loading question', questionIndex, 'saveFirst:', saveFirst);

        try {
            if (saveFirst && !isWatchMode) {
                // Save current answer first, then load new question
                console.log('Saving first, then loading question');
                saveAndNavigate('navigate', questionIndex);
                return;
            }

            // Show loading indicator
            console.log('Showing loading indicator');
            showLoading();
        } catch (error) {
            console.error('Error in loadQuestion:', error);
            alert('Error loading question: ' + error.message);
            return;
        }

        $.ajax({
            url: '@Url.Action("GetQuestionData", "TrainingandExamination")',
            type: 'GET',
            data: {
                TrainingID: trainingId,
                questionIndex: questionIndex,
                watch: isWatchMode ? 1 : 0,
                PublishID: publishId
            },
            success: function(response) {
                console.log('Question data received:', response);

                if (response.success) {
                    // Update the question display with the received data
                    updateQuestionDisplay({
                        questionIndex: response.questionIndex,
                        questionId: response.questionId,
                        questionText: response.questionText,
                        options: response.options.map(function(opt) {
                            return {
                                id: opt.optionId,
                                text: opt.optionText,
                                isSelected: opt.isSelected,
                                isDisabled: isWatchMode
                            };
                        })
                    });
                } else {
                    console.error(response.message || 'Error loading question');
                }

                hideLoading();
            },
            error: function(xhr, status, error) {
                console.error('Error loading question:', error);
                hideLoading();
            }
        });
    }

    // Update question display with new data
    function updateQuestionDisplay(data) {
        console.log('Updating question display:', data);

        // Update global variables
        currentQuestionIndex = data.questionIndex;
        currentQuestionId = data.questionId;

        // Update question text
        $('#questionText').text(data.questionText);
        $('#questionIndex').text(data.questionIndex);
        $('#currentQuestionNumber').text(data.questionIndex);

        // Update options
        var optionsHtml = '';
        data.options.forEach(function(option) {
            var checkedAttr = option.isSelected ? 'checked' : '';
            var disabledAttr = option.isDisabled ? 'disabled' : '';
            var labelClass = option.isDisabled ? 'text-muted' : '';

            optionsHtml += `
                <div class="form-check mb-3 option-item">
                    <input class="form-check-input" type="radio"
                           name="question_${data.questionId}"
                           value="${option.id}"
                           id="option_${data.questionId}_${option.id}"
                           ${checkedAttr} ${disabledAttr}>
                    <label class="form-check-label ${labelClass}"
                           for="option_${data.questionId}_${option.id}">
                        ${option.text}
                    </label>
                </div>
            `;
        });

        $('#optionsContainer').html(optionsHtml);

        // Update navigation buttons
        updateNavigationButtons();

        // Scroll to top
        $('html, body').animate({ scrollTop: 0 }, 300);
    }

    // Update navigation buttons based on current question
    function updateNavigationButtons() {
        console.log('Updating navigation buttons:', {
            currentQuestionIndex: currentQuestionIndex,
            totalQuestions: totalQuestions,
            isWatchMode: isWatchMode
        });

        var $btnPrevious = $('#btnPrevious');
        var $btnNext = $('#btnNext');
        var $btnFinish = $('#btnFinish');
        var $btnViewResults = $('#btnViewResults');

        // Previous button
        if (currentQuestionIndex > 1) {
            $btnPrevious.show();
            console.log('Previous button: shown');
        } else {
            $btnPrevious.hide();
            console.log('Previous button: hidden');
        }

        if (isWatchMode) {
            // Watch mode - always show Next button, never show Finish
            $btnNext.show();
            $btnFinish.hide();
            $btnViewResults.hide();
            console.log('Watch mode: Next shown, Finish hidden, ViewResults hidden');
        } else {
            // Normal mode - show Next or Finish based on question
            if (currentQuestionIndex < totalQuestions) {
                $btnNext.show();
                $btnFinish.show(); // Show both for user choice
                console.log('Normal mode (not last question): Next shown, Finish shown');
            } else {
                $btnNext.hide();
                $btnFinish.show();
                console.log('Normal mode (last question): Next hidden, Finish shown');
            }
            $btnViewResults.hide();
        }
    }

    // Show loading indicator
    function showLoading() {
        console.log('showLoading called');
        try {
            $('#questionContent').hide();
            $('#loadingIndicator').show();
            console.log('Loading indicator shown');
        } catch (error) {
            console.error('Error in showLoading:', error);
        }
    }

    // Hide loading indicator
    function hideLoading() {
        console.log('hideLoading called');
        try {
            $('#loadingIndicator').hide();
            $('#questionContent').show();
            console.log('Loading indicator hidden');
        } catch (error) {
            console.error('Error in hideLoading:', error);
        }
    }

    // Save answer and navigate
    function saveAndNavigate(direction, targetIndex = null) {
        if (isWatchMode) {
            console.log('Watch mode - skipping save');
            if (targetIndex) {
                loadQuestion(targetIndex, false);
            } else if (direction === 'next' && currentQuestionIndex < totalQuestions) {
                loadQuestion(currentQuestionIndex + 1, false);
            } else if (direction === 'previous' && currentQuestionIndex > 1) {
                loadQuestion(currentQuestionIndex - 1, false);
            }
            return;
        }

        // Get selected answer
        var selectedOptionId = 0;
        var checkedRadio = $('input[type="radio"]:checked');
        if (checkedRadio.length > 0) {
            selectedOptionId = parseInt(checkedRadio.val()) || 0;
        }

        console.log('Saving answer:', {
            questionId: currentQuestionId,
            selectedOptionId: selectedOptionId,
            direction: direction
        });

        // Log saving action
        console.log('Saving answer...');

        $.ajax({
            url: '@Url.Action("SaveAnswer", "TrainingandExamination")',
            type: 'POST',
            data: {
                questionId: currentQuestionId,
                selectedOptionId: selectedOptionId,
                direction: direction,
                trainingId: trainingId,
                PublishID: publishId,
                currentQuestionIndex: currentQuestionIndex
            },
            success: function(response) {
                console.log('SaveAnswer success response:', response);
                if (response.success) {
                    console.log('Answer saved successfully!');

                    // Navigate based on direction
                    if (targetIndex) {
                        console.log('Navigating to target index:', targetIndex);
                        loadQuestion(targetIndex, false);
                    } else if (direction === 'next' && currentQuestionIndex < totalQuestions) {
                        console.log('Navigating to next question:', currentQuestionIndex + 1);
                        loadQuestion(currentQuestionIndex + 1, false);
                    } else if (direction === 'previous' && currentQuestionIndex > 1) {
                        console.log('Navigating to previous question:', currentQuestionIndex - 1);
                        loadQuestion(currentQuestionIndex - 1, false);
                    } else if (response.redirectUrl) {
                        console.log('Redirecting to:', response.redirectUrl);
                        setTimeout(function() {
                            window.location.href = response.redirectUrl;
                        }, 500);
                    } else {
                        console.log('No navigation action taken. Direction:', direction, 'currentQuestionIndex:', currentQuestionIndex, 'totalQuestions:', totalQuestions);
                    }
                } else {
                    console.error('SaveAnswer failed:', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error saving answer:', error);
            }
        });
    }

    // Auto-save answer (silent save)
    function autoSaveAnswer() {
        if (isWatchMode) return;

        var selectedOptionId = 0;
        var checkedRadio = $('input[type="radio"]:checked');
        if (checkedRadio.length > 0) {
            selectedOptionId = parseInt(checkedRadio.val()) || 0;
        }

        if (selectedOptionId === 0) return;

        $.ajax({
            url: '@Url.Action("AutoSaveAnswer", "TrainingandExamination")',
            type: 'POST',
            data: {
                questionId: currentQuestionId,
                selectedOptionId: selectedOptionId,
                trainingId: trainingId,
                PublishID: publishId
            },
            success: function(response) {
                // Silent save - no notification
                console.log('Auto-save successful');
            },
            error: function() {
                console.log('Auto-save failed');
            }
        });
    }

    // Save details and redirect to view result page
    function saveDetailsAndViewResult() {
        console.log('saveDetailsAndViewResult called');

        if (isWatchMode) {
            console.log('Watch mode - redirecting directly to results');
            window.location.href = '@Url.Action("ViewResults", "TrainingandExamination", new { Area = "BCMTraining" })?trainingId=' + trainingId + '&publishid=' + publishId;
            return;
        }

        // Get selected answer for current question (same logic as SaveAnswer method)
        var selectedOptionId = 0;

        // Try multiple selectors to find the checked radio button
        var checkedRadio = $('input[name="question_' + currentQuestionId + '"]:checked');
        if (checkedRadio.length === 0) {
            checkedRadio = $('input[type="radio"]:checked');
        }
        if (checkedRadio.length === 0) {
            checkedRadio = $('.form-check-input:checked');
        }

        if (checkedRadio.length > 0) {
            selectedOptionId = parseInt(checkedRadio.val()) || 0;
            console.log('Found checked radio button for save details:', {
                value: checkedRadio.val(),
                selectedOptionId: selectedOptionId,
                questionId: currentQuestionId
            });
        } else {
            console.warn('No radio button selected for question:', currentQuestionId);
        }

        console.log('Saving details with data:', {
            questionId: currentQuestionId,
            selectedOptionId: selectedOptionId,
            trainingId: trainingId,
            publishId: publishId,
            currentQuestionIndex: currentQuestionIndex
        });

        console.log('Saving current answer and redirecting to results...');

        // Save the current answer using SaveAnswer method (same as finish exam)
        $.ajax({
            url: '@Url.Action("SaveAnswer", "TrainingandExamination")',
            type: 'POST',
            data: {
                questionId: currentQuestionId,
                selectedOptionId: selectedOptionId,
                direction: 'savedetails',
                trainingId: trainingId,
                PublishID: publishId,
                currentQuestionIndex: currentQuestionIndex
            },
            success: function(response) {
                console.log('SaveAnswer response for save details:', response);
                if (response.success) {
                    console.log('Answer saved successfully! Redirecting to results...');
                    // Redirect to ViewResults page after successful save
                    setTimeout(function() {
                        window.location.href = '@Url.Action("ViewResults", "TrainingandExamination", new { Area = "BCMTraining" })?trainingId=' + trainingId + '&publishid=' + publishId;
                    }, 1000);
                } else {
                    console.error(response.message || 'Failed to save answer');
                    // Still redirect to results even if save fails
                    setTimeout(function() {
                        window.location.href = '@Url.Action("ViewResults", "TrainingandExamination", new { Area = "BCMTraining" })?trainingId=' + trainingId + '&publishid=' + publishId;
                    }, 2000);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error saving answer for save details:', error);
                console.warn('Warning: Could not save current answer, but redirecting to results...');
                // Still redirect to results even if save fails
                setTimeout(function() {
                    window.location.href = '@Url.Action("ViewResults", "TrainingandExamination", new { Area = "BCMTraining" })?trainingId=' + trainingId + '&publishid=' + publishId;
                }, 2000);
            }
        });
    }

    // Finish exam
    function finishExam() {
        console.log('finishExam called');

        if (isWatchMode) {
            console.log('Watch mode - redirecting to results without saving');
            viewResults();
            return;
        }

        // Get selected answer for current question
        var selectedOptionId = 0;

        // Try multiple selectors to find the checked radio button
        var checkedRadio = $('input[name="question_' + currentQuestionId + '"]:checked');
        if (checkedRadio.length === 0) {
            checkedRadio = $('input[type="radio"]:checked');
        }
        if (checkedRadio.length === 0) {
            checkedRadio = $('.form-check-input:checked');
        }
        if (checkedRadio.length > 0) {
            selectedOptionId = parseInt(checkedRadio.val()) || 0;
            console.log('Found checked radio button:', {
                selector: checkedRadio.length > 0 ? 'found' : 'not found',
                value: checkedRadio.val(),
                selectedOptionId: selectedOptionId,
                questionId: currentQuestionId
            });
        } else {
            console.warn('No radio button selected for question:', currentQuestionId);
        }

        console.log('Finishing exam with data:', {
            questionId: currentQuestionId,
            selectedOptionId: selectedOptionId,
            trainingId: trainingId,
            publishId: publishId,
            currentQuestionIndex: currentQuestionIndex
        });

        console.log('Saving final answer and finishing exam...');

        // First save the current answer using SaveAnswer
        $.ajax({
            url: '@Url.Action("SaveAnswer", "TrainingandExamination")',
            type: 'POST',
            data: {
                questionId: currentQuestionId,
                selectedOptionId: selectedOptionId,
                direction: 'finish',
                trainingId: trainingId,
                PublishID: publishId,
                currentQuestionIndex: currentQuestionIndex,
                watch: 0
            },
            success: function(response) {
                console.log('SaveAnswer response:', response);
                if (response.success) {
                    // After saving answer, call FinishExam
                    callFinishExam(currentQuestionId);
                } else {
                    console.error(response.message || 'Failed to save final answer');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error saving final answer:', error);
                // Even if save fails, try to finish exam
                console.warn('Warning: Could not save final answer, but finishing exam...');
                callFinishExam(currentQuestionId);
            }
        });
    }

    // Call FinishExam after saving answer
    function callFinishExam(questionId) {

        var selectedOptionId = 0;
        console.log('Calling FinishExam endpoint with questionId:', questionId);

        // Use passed questionId or fallback to currentQuestionId
        var finalQuestionId = questionId || currentQuestionId || 0;

        var checkedRadio = $('input[name="question_' + currentQuestionId + '"]:checked');
        if (checkedRadio.length === 0) {
            checkedRadio = $('input[type="radio"]:checked');
        }
        if (checkedRadio.length === 0) {
            checkedRadio = $('.form-check-input:checked');
        }
        if (checkedRadio.length > 0) {
            selectedOptionId = parseInt(checkedRadio.val()) || 0;
            console.log('Found checked radio button:', {
                selector: checkedRadio.length > 0 ? 'found' : 'not found',
                value: checkedRadio.val(),
                selectedOptionId: selectedOptionId,
                questionId: currentQuestionId
            });
        } else {
            console.warn('No radio button selected for question:', currentQuestionId);
        }


        $.ajax({
            url: '@Url.Action("FinishExam", "TrainingandExamination")',
            type: 'POST',
            data: {
                trainingId: trainingId,
                questionId: finalQuestionId,
                selectedOptionId: selectedOptionId, // Already saved in previous call
                PublishID: publishId
            },
            success: function(response) {
                console.log('FinishExam response:', response);
                if (response.success) {
                    console.log('Exam completed successfully!');
                    showExamCompletedModal();
                } else {
                    console.error(response.message || 'Failed to finish exam');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error finishing exam:', error);
            }
        });
    }



    // Show exam completed modal
    function showExamCompletedModal() {
        $('#examCompletedModal').modal('show');
    }

    // Show time up modal
    function showTimeUpModal() {
        $('#timeUpModal').modal('show');
    }

    // Timer functions
    function startTimer() {
        if (timerInterval) {
            clearInterval(timerInterval);
        }

        timerInterval = setInterval(function() {
            timeRemaining--;
            updateTimerDisplay();

            if (timeRemaining <= 0) {
                clearInterval(timerInterval);
                showTimeUpModal();
                finishExam();
            }
        }, 1000);
    }

    function updateTimerDisplay() {
        var hours = Math.floor(timeRemaining / 3600);
        var minutes = Math.floor((timeRemaining % 3600) / 60);
        var seconds = timeRemaining % 60;

        var timeString = String(hours).padStart(2, '0') + ':' +
                        String(minutes).padStart(2, '0') + ':' +
                        String(seconds).padStart(2, '0');

        $('#timeRemaining').text(timeString);
    }



    // Cleanup on page unload
    $(window).on('beforeunload', function() {
        if (timerInterval) {
            clearInterval(timerInterval);
        }
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        if (e.ctrlKey) return; // Ignore if Ctrl is pressed

        switch(e.key) {
            case 'ArrowLeft':
                if (currentQuestionIndex > 1) {
                    e.preventDefault();
                    $('#btnPrevious').click();
                }
                break;
            case 'ArrowRight':
                if (currentQuestionIndex < totalQuestions) {
                    e.preventDefault();
                    $('#btnNext').click();
                }
                break;
            case 'Enter':
                if (e.target.type !== 'radio') {
                    e.preventDefault();
                    if (currentQuestionIndex < totalQuestions) {
                        $('#btnNext').click();
                    } else {
                        $('#btnFinish').click();
                    }
                }
                break;
        }
    });

    // Handle radio button selection with keyboard
    $(document).on('keydown', 'input[type="radio"]', function(e) {
        var $radios = $('input[name="' + this.name + '"]');
        var currentIndex = $radios.index(this);

        switch(e.key) {
            case 'ArrowUp':
            case 'ArrowLeft':
                e.preventDefault();
                if (currentIndex > 0) {
                    $radios.eq(currentIndex - 1).focus().click();
                }
                break;
            case 'ArrowDown':
            case 'ArrowRight':
                e.preventDefault();
                if (currentIndex < $radios.length - 1) {
                    $radios.eq(currentIndex + 1).focus().click();
                }
                break;
        }
    });

    } // End of initializeTrainingQuestionPaper function

    // Global functions that need to be accessible from HTML onclick events

    // View results - Global function
    function viewResults() {
        console.log('viewResults called');

        if (isWatchMode) {
            // In watch mode, go directly to results without saving
            console.log('Watch mode - going directly to results');
            window.location.href = '@Url.Action("ViewResults", "TrainingandExamination", new { Area = "BCMTraining" })?trainingId=' + trainingId + '&publishid=' + publishId;
            return;
        }

        // In normal mode, redirect directly to results page
        console.log('Normal mode - redirecting directly to results');
        window.location.href = '@Url.Action("ViewResults", "TrainingandExamination", new { Area = "BCMTraining" })?trainingId=' + trainingId + '&publishid=' + publishId;
    }



</script>

@section Scripts {
    <!-- Ensure jQuery is loaded -->
    <script>
        if (typeof jQuery === 'undefined') {
            document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
        }
    </script>

    <!-- Fallback for Bootstrap if needed -->
    <script>
        if (typeof bootstrap === 'undefined') {
            document.write('<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"><\/script>');
        }
    </script>
}
