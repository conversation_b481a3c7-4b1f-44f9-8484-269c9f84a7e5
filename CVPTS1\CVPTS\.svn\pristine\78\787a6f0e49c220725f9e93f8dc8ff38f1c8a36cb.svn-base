﻿@{
    ViewBag.Title = "ApplicationBIA";
    Layout = "~/Views/Shared/_Layout.cshtml";

}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Manage Applications</h6>
    <div class="row" style="width:80%">
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-organization-group-name"></i></span>
                <select class="form-select form-select-sm" aria-label="Default select example">
                    <option selected>All Organizations</option>
                    <option value="1">PTS</option>
                    <option value="2">TCS</option>
                    <option value="3">Continuity Vault</option>
                </select>
            </div>
        </div>
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-organization-group-name"></i></span>
                <select class="form-select form-select-sm" aria-label="Default select example">
                    <option selected>All Units</option>
                    <option value="1">PTS</option>
                    <option value="2">TCS</option>
                    <option value="3">Continuity Vault</option>
                </select>
            </div>
        </div>
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-organization-group-name"></i></span>
                <select class="form-select form-select-sm" aria-label="Default select example">
                    <option selected>All Departments</option>
                    <option value="1">PTS</option>
                    <option value="2">TCS</option>
                    <option value="3">Continuity Vault</option>
                </select>
            </div>
        </div>
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-organization-group-name"></i></span>
                <select class="form-select form-select-sm" aria-label="Default select example">
                    <option selected>All SubDepartments</option>
                    <option value="1">PTS</option>
                    <option value="2">TCS</option>
                    <option value="3">Continuity Vault</option>
                </select>
            </div>
        </div>
        <div class="col">
            <div class="input-group">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control" placeholder="Search">
            </div>
        </div>
        <div class="col-auto">
            <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#NormalModal"><i class="cv-Plus" title="Create New"></i>Create</button>
        </div>
    </div>
</div>

<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">Sr. No.</th>
                <th>Application Name</th>
                <th>Owner Details</th>
                <th>Org Level</th>
                <th>RTO / MAO / RPO</th>
                <th>IsCritical</th>
                <th>Status</th>
                <th>View BIA</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>1</td>
                <td>
                    Is Under BCM Scope: <span class="text-danger">No</span>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr>
                                <td class="fw-semibold"><i class="cv-user"></i></td>
                                <td> : </td>
                                <td>Kishan Kadam</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold"><i class="cv-mail"></i></td>
                                <td>:</td>
                                <td><a class="text-primary" href="#"><EMAIL></a></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold"><i class="cv-phone"></i></td>
                                <td>:</td>
                                <td>9021693184</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr title="Organization">
                                <td class="fw-semibold"><i class="cv-company"></i></td>
                                <td> : </td>
                                <td>
                                    Perpetuuiti
                                </td>
                            </tr>
                            <tr title="Unit">
                                <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                <td>:</td>
                                <td>Tournament Affairs</td>
                            </tr>
                            <tr title="Department">
                                <td class="fw-semibold"><i class="cv-department"></i> </td>
                                <td>:</td>
                                <td>Programme Management Office</td>
                            </tr>
                            <tr title="Sub Department">
                                <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                <td>:</td>
                                <td>Secretary General</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    <table>
                        <tbody>
                            <tr title="RTO">
                                <td class="fw-semibold"><i class="cv-RTO"></i></td>
                                <td> : </td>
                                <td>
                                    NA
                                </td>
                            </tr>
                            <tr title="MAO">
                                <td class="fw-semibold"><i class="cv-user"></i> </td>
                                <td>:</td>
                                <td>NA</td>
                            </tr>
                            <tr title="Calculated RTO">
                                <td class="fw-semibold"><i class="cv-RTO"></i></td>
                                <td> : </td>
                                <td>
                                    NA
                                </td>
                            </tr>
                            <tr title="Calculated MAO">
                                <td class="fw-semibold"><i class="cv-user"></i> </td>
                                <td>:</td>
                                <td>Na</td>
                            </tr>
                            <tr title="RPO">
                                <td class="fw-semibold"><i class="cv-rpo"></i></td>
                                <td>:</td>
                                <td>NA</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td>
                    No
                </td>
                <td>
                    NA
                </td>
                <td>
                    <span class="btn-action"><i class="cv-page-name" type="button" title="View BIA"></i></span>
                </td>
                <td>
                    <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                    <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Configuration Modal -->
<div class="modal fade" id="NormalModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Application BIA Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-2">
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Organization</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-organization"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">All Organization Groups</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Department</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-department"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Department</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Application Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-Application-name"></i></span>
                                <input class="form-control" type="text" placeholder="Enter Application Name" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input class="form-control" type="text" placeholder="Enter Description" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label class="form-label">Unit</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-unit"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">All SubDepartment</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">SubDepartment</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Select Type</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Application Owner</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-Application-owner"></i></span>
                                <select class="form-select form-select-sm">
                                    <option value="value">Select Company Name</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-sm btn-primary">Save</button>
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header d-grid text-center">
                <span class="fw-semibold">Do you really want to delete</span>
                <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
            </div>
            <div class="modal-body text-center">
                <img src="~/img/isomatric/delete.svg" width="260" />
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->