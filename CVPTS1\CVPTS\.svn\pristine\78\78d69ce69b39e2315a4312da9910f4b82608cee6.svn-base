﻿/* Dashboard Builder Styles */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Fix dropdown visibility issues */
.navbar .dropdown-menu {
    z-index: 9999 !important;
    position: absolute !important;
    display: none;
    min-width: 250px;
    background-color: white !important;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.175);
    top: 100% !important;
    left: 0 !important;
    right: auto !important;
    transform: none !important;
    margin-top: 0.125rem !important;
    max-height: 400px;
    overflow-y: auto;
}

    .navbar .dropdown-menu.show {
        display: block !important;
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
    }

.navbar .dropdown-toggle::after {
    margin-left: 0.5em;
}

/* Ensure dropdown items are visible */
.dropdown-item {
    display: block !important;
    width: 100%;
    padding: 0.375rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

    .dropdown-item:hover,
    .dropdown-item:focus {
        background-color: #f8f9fa;
        color: #1e2125;
    }

.dropdown-header {
    display: block;
    padding: 0.5rem 1rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    color: #6c757d;
    white-space: nowrap;
}

/* Additional dropdown fixes */
.navbar .nav-item.dropdown {
    position: relative;
}

.navbar .dropdown-menu {
    top: 100% !important;
    left: 0 !important;
    transform: none !important;
    margin-top: 0.125rem;
}

/* Ensure dropdown is above all other elements */
.navbar {
   /* z-index: 1030;*/
    overflow: visible !important;
}

    .navbar .container-fluid {
        overflow: visible !important;
    }

    .navbar .dropdown {
        z-index: 1031;
        position: relative !important;
    }

    .navbar .dropdown-menu {
        z-index: 1032 !important;
    }

/* Force dropdown visibility when Bootstrap shows it */
.dropdown-menu[data-bs-popper] {
    z-index: 1032 !important;
    position: absolute !important;
}

/* Override any conflicting styles */
.navbar .dropdown-menu li {
    list-style: none;
}

.navbar .dropdown-menu a {
    cursor: pointer;
}

/* Simplified dropdown styles that work reliably */
.dropdown-menu {
    display: none !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    z-index: 9999 !important;
    min-width: 250px !important;
    background-color: white !important;
    border: 2px solid #007bff !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.175) !important;
    list-style: none !important;
    padding: 0.5rem 0 !important;
    margin: 0.125rem 0 0 0 !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

    .dropdown-menu.show {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Ensure dropdown items are properly styled and visible */
    .dropdown-menu li {
        list-style: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .dropdown-menu .dropdown-item {
        display: block !important;
        width: 100% !important;
        padding: 0.375rem 1rem !important;
        clear: both !important;
        font-weight: 400 !important;
        color: #212529 !important;
        text-align: inherit !important;
        text-decoration: none !important;
        white-space: nowrap !important;
        background-color: transparent !important;
        border: 0 !important;
        cursor: pointer !important;
        line-height: 1.5 !important;
    }

        .dropdown-menu .dropdown-item:hover,
        .dropdown-menu .dropdown-item:focus {
            background-color: #f8f9fa !important;
            color: #1e2125 !important;
            text-decoration: none !important;
        }

    .dropdown-menu .dropdown-header {
        display: block !important;
        padding: 0.5rem 1rem !important;
        margin-bottom: 0 !important;
        font-size: 0.875rem !important;
        color: #6c757d !important;
        white-space: nowrap !important;
        font-weight: 600 !important;
    }

    .dropdown-menu .dropdown-divider {
        height: 0 !important;
        margin: 0.5rem 0 !important;
        overflow: hidden !important;
        border-top: 1px solid #dee2e6 !important;
    }

    /* Force visibility of icons in dropdown items */
    .dropdown-menu .dropdown-item i {
        display: inline-block !important;
        margin-right: 0.5rem !important;
        color: #6c757d !important;
    }

/* Custom dropdown menu styles */
.custom-dropdown-menu {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.5;
}

    .custom-dropdown-menu .dropdown-section {
        padding: 4px 0;
    }

    .custom-dropdown-menu .dropdown-header {
        padding: 8px 16px 4px 16px;
        font-weight: 600;
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e9ecef;
        margin-bottom: 4px;
    }

    .custom-dropdown-menu .dropdown-item {
        display: block;
        padding: 8px 16px;
        color: #212529;
        text-decoration: none;
        cursor: pointer;
        transition: background-color 0.15s ease-in-out;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
    }

        .custom-dropdown-menu .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #1e2125;
        }

        .custom-dropdown-menu .dropdown-item:active {
            background-color: #e9ecef;
        }

    .custom-dropdown-menu .dropdown-divider {
        height: 1px;
        background-color: #dee2e6;
        margin: 8px 0;
        border: none;
    }

/* Animation for dropdown */
.custom-dropdown-menu {
    animation: dropdownFadeIn 0.15s ease-out;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ensure dropdown button works */
.dropdown-toggle {
    background-color: transparent !important;
    border-color: rgba(255,255,255,0.5) !important;
    color: white !important;
}

    .dropdown-toggle:hover {
        background-color: rgba(255,255,255,0.1) !important;
        border-color: white !important;
    }

/* Force dropdown positioning */
.nav-item.dropdown {
    position: relative !important;
    overflow: visible !important;
}

    .nav-item.dropdown .dropdown-menu {
        position: absolute !important;
        top: calc(100% + 2px) !important;
        left: 0 !important;
        right: auto !important;
        transform: none !important;
        margin: 0 !important;
        clip: auto !important;
        clip-path: none !important;
    }

/* Gridstack Container */
.grid-stack {
    min-height: calc(100vh - 120px);
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f8f9fa 75%), linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Gridstack Item Styling */
.grid-stack-item {
    border-radius: 8px;
    overflow: visible;
    /* Ensure consistent spacing */
    box-sizing: border-box;
}

.grid-stack-item-content {
    border-radius: 8px;
    overflow: hidden;
    /* Ensure the content fills the item with proper spacing */
    margin: 0;
    padding: 0;
    /* Add subtle shadow for better visual separation */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

    .grid-stack-item-content:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

/* Prevent horizontal scroll and ensure responsive layout */
html, body {
    overflow-x: hidden;
    max-width: 100vw;
}

.container-fluid {
   /* padding: 10px;*/
    max-width: 100vw;
    overflow-x: hidden;
    box-sizing: border-box;
}

/* Special handling for navbar container */
.navbar .container-fluid {
    overflow: visible !important;
    position: relative;
}

/* Ensure navbar doesn't clip dropdowns */
.navbar {
    overflow: visible !important;
    position: relative;
}

/* Ensure proper spacing between widgets without horizontal scroll */
.grid-stack {
    margin: 0;
    padding: 10px;
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    /* Remove default GridStack margins that cause overflow */
}

    /* Reset all GridStack item margins to prevent overflow */
    .grid-stack > .grid-stack-item {
        margin: 0 !important;
    }

.grid-stack-item {
    box-sizing: border-box;
    /* Remove all margins that cause horizontal scroll */
    margin: 0 !important;
}

/* Use padding instead of margins for spacing - simpler approach */
.grid-stack-item-content {
    margin: 8px;
    box-sizing: border-box;
}

/* Add spacing using border-box approach */
.grid-stack-item {
    padding: 8px;
    box-sizing: border-box;
}

/* Adjust widget to account for padding */
.dashboard-widget {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

/* Widget Styles */
.dashboard-widget {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    width: 100%;
    position: relative;
    /* Ensure widget takes full space within grid item */
    box-sizing: border-box;
}

    .dashboard-widget:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        border-color: #007bff;
    }

    .dashboard-widget.selected {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
    }

/* Gridstack dragging styles */
.grid-stack-item.ui-draggable-dragging .dashboard-widget {
    transform: rotate(2deg);
    opacity: 0.8;
}

/* Widget Header */
.widget-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 15px;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    cursor: move;
}

    .widget-header h5 {
        margin: 0;
        font-size: 14px;
    }

/* Widget Content */
.widget-content {
    padding: 15px;
    height: calc(100% - 50px);
    overflow: hidden; /* Default no scroll */
}

/* Table widget specific - only table should scroll */
.dashboard-widget[data-widget-type="table"] .widget-content {
    padding: 0; /* Remove padding for table content */
    overflow: hidden;
}

.table-widget {
    height: 100%;
    overflow-y: auto;
    overflow-x: auto;
    padding: 10px;
}

    .table-widget .table-responsive {
        height: 100%;
        overflow-y: auto;
    }

/* Widget Toolbar */
.widget-toolbar {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dashboard-widget:hover .widget-toolbar {
    opacity: 1;
}

.widget-toolbar .btn {
    padding: 2px 6px;
    font-size: 10px;
    margin-left: 2px;
}

/* Preview Mode */
.preview-mode .widget-toolbar {
    display: none !important;
}

.preview-mode .dashboard-widget {
    cursor: default !important;
}

.preview-mode .widget-header {
    cursor: default !important;
}

.preview-mode .grid-stack {
    pointer-events: none;
}

.preview-mode .grid-stack-item {
    pointer-events: auto;
}

/* KPI Widget Styles */
.kpi-widget {
    text-align: center;
    padding: 20px;
}

.kpi-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #007bff;
    margin: 10px 0;
}

.kpi-label {
    font-size: 1rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.kpi-icon {
    font-size: 3rem;
    color: #28a745;
    margin-bottom: 10px;
}

.kpi-change {
    font-size: 0.9rem;
    margin-top: 10px;
}

    .kpi-change.positive {
        color: #28a745;
    }

    .kpi-change.negative {
        color: #dc3545;
    }

/* Chart Container */
.chart-container {
    position: relative;
    height: 100%;
    width: 100%;
    min-height: 200px;
}

    .chart-container canvas {
        max-width: 100%;
        max-height: 100%;
    }

/* Table Widget - Updated */
.table-widget table {
    font-size: 0.9rem;
    margin: 0;
}

/* Text Widget */
.text-widget {
    height: 100%;
}

    .text-widget h3 {
        color: #495057;
        margin-bottom: 15px;
    }

/* Progress Widget */
.progress-widget {
    padding: 20px;
}

.progress-item {
    margin-bottom: 20px;
}

    .progress-item:last-child {
        margin-bottom: 0;
    }

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Gridstack Resize Handle Styling */
.grid-stack-item .ui-resizable-se {
    background: #007bff;
    opacity: 0;
    transition: opacity 0.3s ease;
    width: 12px;
    height: 12px;
    border-radius: 0 0 6px 0;
}

.grid-stack-item:hover .ui-resizable-se {
    opacity: 0.7;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-nav {
        flex-direction: column;
        gap: 10px;
    }

    .dashboard-widget {
        min-width: 150px;
        min-height: 120px;
    }

    .kpi-value {
        font-size: 2rem;
    }

    .kpi-icon {
        font-size: 2rem;
    }
}

/* Animation Classes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.widget-fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Weather Widget Styles */
.weather-widget {
    text-align: center;
    padding: 15px;
}

.weather-header h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.weather-main {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.weather-temp {
    font-size: 2.5rem;
    font-weight: bold;
    color: #007bff;
}

.weather-icon i {
    font-size: 2rem;
    color: #ffc107;
}

.weather-condition {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 15px;
}

.weather-details {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
}

.weather-detail {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: #6c757d;
}

.weather-forecast {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.forecast-day {
    text-align: center;
    flex: 1;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;
}

.forecast-day-name {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.forecast-temps {
    font-size: 0.8rem;
    margin-top: 5px;
}

/* Calendar Widget Styles */
.calendar-widget {
    padding: 10px;
}

.calendar-header h4 {
    text-align: center;
    margin: 0 0 15px 0;
    color: #495057;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 15px;
}

.calendar-day-header {
    text-align: center;
    font-weight: 600;
    font-size: 0.8rem;
    padding: 5px;
    background: #e9ecef;
    color: #495057;
}

.calendar-day {
    text-align: center;
    padding: 8px 4px;
    font-size: 0.8rem;
    cursor: pointer;
    border-radius: 4px;
}

    .calendar-day.current-month {
        background: #f8f9fa;
        color: #495057;
    }

    .calendar-day.other-month {
        color: #adb5bd;
    }

    .calendar-day.today {
        background: #007bff;
        color: white;
        font-weight: bold;
    }

.calendar-events h5 {
    font-size: 0.9rem;
    margin-bottom: 10px;
    color: #495057;
}

.calendar-event {
    display: flex;
    gap: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.8rem;
}

.event-time {
    color: #6c757d;
    min-width: 60px;
}

.event-title {
    color: #495057;
    font-weight: 500;
}

/* Clock Widget Styles */
.clock-widget {
    text-align: center;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.clock-time {
    font-size: 2.5rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 10px;
    font-family: 'Courier New', monospace;
}

.clock-date {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.clock-timezone {
    color: #adb5bd;
}

/* Gauge Widget Styles */
.gauge-widget {
    padding: 15px;
}

.gauge-item {
    margin-bottom: 20px;
}

    .gauge-item:last-child {
        margin-bottom: 0;
    }

.gauge-label {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.gauge-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.gauge-bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.gauge-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.gauge-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    min-width: 40px;
}

/* Map Widget Styles */
.map-widget {
    height: 100%;
}

.map-container {
    height: 100%;
    background: #f8f9fa;
    border-radius: 6px;
    overflow: hidden;
}

.map-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
}

    .map-placeholder i {
        font-size: 3rem;
        margin-bottom: 10px;
    }

.map-locations {
    padding: 15px;
    height: 100%;
    overflow-y: auto;
}

.map-marker {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
}

    .map-marker i {
        color: #dc3545;
        margin-top: 2px;
    }

.marker-info strong {
    display: block;
    color: #495057;
    margin-bottom: 5px;
}

.marker-info p {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
}

/* News Widget Styles */
.news-widget {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.news-header {
    padding: 15px 15px 0 15px;
}

    .news-header h4 {
        margin: 0;
        color: #495057;
    }

.news-list {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

.news-item {
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

    .news-item:last-child {
        border-bottom: none;
    }

.news-category {
    font-size: 0.8rem;
    color: #007bff;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.news-title {
    font-size: 1rem;
    margin: 0 0 8px 0;
    color: #495057;
    line-height: 1.3;
}

.news-summary {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.news-time {
    font-size: 0.8rem;
    color: #adb5bd;
}

/* Social Media Widget Styles */
.social-widget {
    padding: 15px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.social-header h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.social-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.social-stat {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

.social-posts {
    flex: 1;
    overflow-y: auto;
}

    .social-posts h5 {
        font-size: 1rem;
        margin: 0 0 15px 0;
        color: #495057;
    }

.social-post {
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

    .social-post:last-child {
        border-bottom: none;
    }

.post-platform {
    font-size: 0.8rem;
    color: #007bff;
    font-weight: 600;
    margin-bottom: 5px;
}

.post-content {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 8px;
    line-height: 1.4;
}

.post-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

    .post-stats span {
        color: #6c757d;
    }

.post-time {
    color: #adb5bd;
}

/* amChart Container */
.amchart-container {
    width: 100%;
    height: 100%;
    min-height: 250px;
    position: relative;
    overflow: hidden;
}

/* Ensure amChart containers in widgets have proper sizing */
.widget-content .amchart-container {
    height: calc(100% - 20px);
    min-height: 200px;
    margin: 10px;
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    .dashboard-container {
        background-color: #2d2d2d;
    }

    .dashboard-widget {
        background: #3a3a3a;
        border-color: #555;
        color: #ffffff;
    }

    .widget-content {
        color: #ffffff;
    }
}
