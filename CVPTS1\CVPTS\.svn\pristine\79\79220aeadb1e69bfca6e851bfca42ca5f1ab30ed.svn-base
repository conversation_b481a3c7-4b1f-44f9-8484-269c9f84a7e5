﻿namespace BCM.UI.Areas.BCMReports.ReportModels.ProcessRecovery_Report
{
    public class ProcessRecoveryPriorityReportVm
    {
        public string? LoginName { get; set; }
        public List<ProcessRecoveryPriorityReportList> ProcessRecoveryPriorityReportList { get; set; }
        public List<CategoryMasterlist> CategoryMastersList { get; set; }
    }
    public class ProcessRecoveryPriorityReportList
    {
        public int OwnerID { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerPhone { get; set; }
        public string? OwnerEmail { get; set; }
        public string? ProcessName { get; set; }
        public int ProcessID { get; set; }
        public string? Category { get; set; }
        public string? CategoryStatus { get; set; }
        public int StatusID { get; set; }
        public string RTO { get; set; }
        public string RTOText { get; set; }
        public string? OrganizationName { get; set; }
        public string? UnitName { get; set; }
        public string? DepartmentName { get; set; }
        public string? SubDepartmentName { get; set; }


    }
    public class CategoryMasterlist
    {
        public int Id { get; set; }
        public string? CategoryName { get; set; }
        public string? Description { get; set; }
        public string? CategoryRange { get; set; }
       
    }
}
