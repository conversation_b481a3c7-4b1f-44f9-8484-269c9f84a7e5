﻿@model BCM.BusinessClasses.BCMTeamsAndResources
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    var selectedOrgID = ViewBag.selectedOrgID;
}

<form asp-action="NotifyClick" method="post" id="notifyForm" enctype="multipart/form-data">
    <div class="modal-body pt-0">
        <div class="row row-cols-2 align-items-center">
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Org Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select class="form-select form-select-sm" autocomplete="off" aria-label="Default select example" name="ddlOrg">
                            <option value="0">-- All Organizations --</option>
                            @foreach (var organization in ViewBag.OrgInfo)
                            {
                                <!option value="@organization.Value" @(organization.Value == selectedOrgID.ToString() ? "selected=\"selected\"" : "")>@organization.Text</!option>
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Notification Type</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-notfication"></i></span>
                        <select class="form-select form-select-sm" id="ddlNotification" required>
                            <option value="0">-- Select --</option>
                            <option value="1">Alert Notification</option>
                            <option value="2">General Notification</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Unit Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-unit"></i></span>
                        <select id="ddlUnit" class="form-select form-control" autocomplete="off" aria-label="Default select example">
                            <option selected disabled value="">-- All Units --</option>
                            @foreach (var unit in ViewBag.UnitList)
                            {
                                <option value="@unit.Value">@unit.Text</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Incident to Notify</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-notify-incident"></i></span>
                        <select class="form-select form-select-sm" id="ddlincident">
                            @* required *@
                            <option selected disabled value="">-- Select Incident --</option>
                            @foreach (var incident in ViewBag.IncidentManagement)
                            {
                                <option value="@incident.Value">@incident.Text</option>
                            }
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="form-group">
                    <label class="form-label">Team Notification Subject</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-notfication"></i></span>
                        <textarea class="form-control" placeholder="Team Notification Subject" style="height:0px" id="txtSubject" required></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Team Notification Message</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-notify-team"></i></span>
                        <textarea class="form-control" placeholder="Team Notification Message" style="height:0px" id="txtMsg" required></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group d-flex align-items-center">
                        <span class="form-label mb-0"><i class="cv-user me-1"></i>User Response Required</span>
                        <input type="checkbox" class="form-check  ms-2" name="chkResponse" id="chkResponse" onclick="enableDropDown(this)" /> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="form-label mb-0" hidden="hidden" id="responeseMsg">Please reply RESP Yxxx for Yes and RESP Nxxx for No to @@@@@@@@@@.</span>
                    </div>
                </div>
                <div class="row align-items-center">
                    <label class="form-label">Accept User Response for</label>
                    <div class="col-6">
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-user"></i></span>
                                <input type="number" class="form-control" disabled="disabled" id="txtTimeTaken" name="id" required />
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-clock"></i></span>
                                <select class="form-select form-select-sm" disabled="disabled" id="ddlTimeOut" name="ddlTimeOut" required>
                                    <option selected disabled value="">Select</option>
                                    @foreach (var objTimeOut in ViewBag.TimeUnit)
                                    {
                                        <option value="@objTimeOut.Value">@objTimeOut.Text</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-file-size"></i></span>
                        <input type="file" class="form-control" id="txtAttachment" name="File" />
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex align-items-center justify-content-between gap-3 mb-2">
                        <div class="d-flex align-items-center gap-3">
                            <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                                <i class="cv-Plus align-middle"></i>
                            </span>
                            <h6 class="mb-0">Notify Users (as FYA)</h6>
                        </div>
                        <div class="input-group Search-Input" style="width:400px !important;">
                            <span class="input-group-text py-1"><i class="cv-search"></i></span>
                            <input data-search="usersFYADiv" type="text" class="form-control searchId" placeholder="Search">
                        </div>
                    </div>
                    <div class="collapse" id="collapseExample">
                        <div class="g-2 row row-cols-xl-4 row-cols-4 p-3" style="overflow-y: auto;height: 150px;" id="usersFYADiv">
                            @await Html.PartialAsync("_FilteredFYAUsers", Model.ResourceList)
                        </div>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex align-items-center justify-content-between gap-3 mb-2">
                        <div class="d-flex align-items-center gap-3">
                            <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#collapseUsersFYI" aria-expanded="false" aria-controls="collapseExample">
                                <i class="cv-Plus align-middle"></i>
                            </span>
                            <h6 class="mb-0">Notify Users (as FYI)</h6>
                        </div>
                        <div class="input-group Search-Input" style="width:400px !important;">
                            <span class="input-group-text py-1"><i class="cv-search"></i></span>
                            <input data-search="usersFYIDiv" type="text" class="form-control searchId" placeholder="Search">
                        </div>
                    </div>
                    <div class="collapse" id="collapseUsersFYI">
                        <div class="g-2 row row-cols-xl-4 row-cols-4 p-3" style="overflow-y: auto;height: 150px;" id="usersFYIDiv">
                            @await Html.PartialAsync("_FilteredFYIUsers", Model.ResourceList)
                        </div>
                    </div>
                </div>
                <div class="mb-2 mt-3">
                    <div class="d-flex align-items-center gap-3 mb-2">
                        <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#collapseTeamsFYA" aria-expanded="false" aria-controls="collapseExample">
                            <i class="cv-Plus align-middle"></i>
                        </span>
                        <h6 class="mb-0">
                            Notify Teams (as FYA)
                        </h6>
                    </div>
                    <div class="g-2 row row-cols-xl-4 row-cols-4 collapse p-3" id="collapseTeamsFYA">
                        @{
                            foreach (var BCMTeamsAndResources in Model.BCMGroupList)
                            {
                                <div class="col">
                                    <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
                                        <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                                            <span>
                                                <input class="form-check" type="checkbox" id="<EMAIL>" value="@BCMTeamsAndResources.GroupID"
                                                       name="teamsFYA" />
                                            </span>
                                            <span class="">@BCMTeamsAndResources.GroupName</span>
                                        </span>
                                    </div>
                                </div>
                            }
                        }
                    </div>
                </div>
                <div class="mb-2 mt-3">
                    <div class="d-flex align-items-center gap-3 mb-2">
                        <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#collapseTeamsFYI" aria-expanded="false" aria-controls="collapseExample">
                            <i class="cv-Plus align-middle"></i>
                        </span>
                        <h6 class="mb-0">
                            Notify Teams (as FYI)
                        </h6>
                    </div>
                    <div class="g-2 row row-cols-xl-4 row-cols-4 collapse p-3" id="collapseTeamsFYI">
                        @{
                            foreach (var BCMTeamsAndResources in Model.BCMGroupList)
                            {
                                <div class="col">
                                    <div class="d-flex px-2 py-1 rounded-1 justify-content-between align-items-center border">
                                        <span class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                                            <span>
                                                <input class="form-check" type="checkbox" id="<EMAIL>" value="@BCMTeamsAndResources.GroupID"
                                                       name="teamsFYI" />
                                            </span>
                                            <span class="">@BCMTeamsAndResources.GroupName</span>
                                        </span>
                                    </div>
                                </div>
                            }
                        }
                    </div>
                    <div class="mb-2">
                        <div class="form-group">
                            <label class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-team-size"></i></span>
                                <input class="form-control" type="password" id="txtPassword" name="txtPassword" value="" placeholder="Password" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div id="successMessage" class="alert alert-success d-none"></div>
                <div id="errorMessage" class="alert alert-danger d-none"></div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm me-1">Notify</button>
        </div>
    </div>
</form>

<script>

    $(document).ready(function () {

        $('#ddlUnit').change(function () {
            var UnitID = $(this).val();
            if (UnitID) {
                $.ajax({
                    url: '@Url.Action("GetIncidentData", "BCMGroupsNotification")',
                    type: 'GET',
                    data: { iUnitID: UnitID },
                    success: function (data) {
                        var incident = $('#ddlincident');
                        incident.empty();
                        incident.append('<option value="0">-- Select Incident --</option>');
                        $.each(data, function (index, item) {
                            incident.append('<option value="' + item.id + '">' + item.eventName + '</option>')
                        })
                    },
                    error: function (error) {
                        console.log("Error : ", error);
                    }
                });
            }
        });

        $('#ddlincident').change(function(){
            var selectedText = $('#ddlincident option:selected').text();
            $('#txtMsg').val(selectedText);
        })

        getFYACheckboxesStates();

        getFYICheckboxesStates();

        document.addEventListener('change', function (event) {
            if (event.target.matches('input.fyaUser')) {
                updateFYACheckboxesStates(event.target);
            }
            else if (event.target.matches('input.fyiUser')) {
                updateFYICheckboxesStates(event.target);
            }
        });

        $(document).on('change', '.searchId', function () {
            const strSearchId = $(this).data('search');
            searchUsers(strSearchId, $(this).val());
        })

        $(document).on('submit', '#notifyForm', function (e) {
            e.preventDefault();

            var chkResponse = document.getElementById('chkResponse');
            var usersFYA = $('#usersFYADiv input[name="usersFYA"]:checked').map(function () {
                return parseInt($(this).val(), 10);
            }).get();

            var usersFYI = $('#usersFYIDiv input[name="usersFYI"]:checked').map(function () {
                return parseInt($(this).val(), 10);
            }).get();

            var teamsFYA = $('input[name="teamsFYA"]:checked').map(function () {
                return parseInt($(this).val(), 10);
            }).get();

            var teamsFYI = $('input[name="teamsFYI"]:checked').map(function () {
                return parseInt($(this).val(), 10);
            }).get();

            var iChkResponse = chkResponse.checked ? 1 : 0;

            var formData = new FormData();
            var fileInput = document.getElementById('txtAttachment');
            if (fileInput.files.length > 0) {
                formData.append('file', fileInput.files[0]);
            }

            // Append the users and teams as separate fields, not as JSON strings
            usersFYA.forEach(function (user) {
                formData.append('UsersFYA', user);
            });
            usersFYI.forEach(function (user) {
                formData.append('UsersFYI', user);
            });
            teamsFYA.forEach(function (team) {
                formData.append('TeamsFYA', team);
            });
            teamsFYI.forEach(function (team) {
                formData.append('TeamsFYI', team);
            });

            formData.append('iChkResponse', iChkResponse);
            formData.append('txtMsg', $('#txtMsg').val().trim());
            formData.append('txtSubject', $('#txtSubject').val().trim());
            formData.append('ddlNotification', $('#ddlNotification').val());
            formData.append('ddlTimeOut', $('#ddlTimeOut').val());
            formData.append('txtTimeTaken', $('#txtTimeTaken').val());
            formData.append('txtPassword', $('#txtPassword').val());

            $.ajax({
                url: '@Url.Action("NotifyClick", "BCMGroupsNotification", new { area = "BCMTeams" })',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.success)
                    {
                        $('#NotifyModal').modal('hide');
                        $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                        const toastElement = $('#liveToast');
                        toastElement.removeClass('bg-success bg-warning bg-danger');
                        toastElement.addClass('bg-success');
                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();
                    }
                    else
                    {
                        $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                        const toastElement = $('#liveToast');
                        toastElement.removeClass('bg-success bg-warning bg-danger');
                        toastElement.addClass('bg-danger');
                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();
                    }
                },
                error: function (xhr, status, error) {
                    $('#errorMessage').text("An error occurred. Please try again.").removeClass('d-none');
                    console.error("Error details:", xhr.responseText);
                }
            });
        });


    });

    function searchUsers(strSearchId, strUserName) {
        $.ajax({
            url: '@Url.Action("SearchUsers", "BCMGroupsNotification")',
            type: 'GET',
            data: {
                strUserName: strUserName,
                strSearchId: strSearchId
            },
            success: function (response) {
                $('#' + strSearchId).html(response);
                if (strSearchId == 'usersFYADiv') {
                    getFYACheckboxesStates();
                } else if (strSearchId == 'usersFYIDiv') {
                    getFYICheckboxesStates();
                }
            },
            error: function (xhr, status, error) {
                console.log(error);
                console.error(xhr.status);
                console.error(xhr.responseText);
            }
        });
    }

    function updateFYACheckboxesStates(fyaCheckbox) {
        let fyaCheckboxesState = JSON.parse(localStorage.getItem('fyaCheckboxesState')) || {};
        fyaCheckboxesState[fyaCheckbox.value] = fyaCheckbox.checked;
        localStorage.setItem('fyaCheckboxesState', JSON.stringify(fyaCheckboxesState));
    }

    function updateFYICheckboxesStates(fyiCheckbox) {
        let fyiCheckboxesState = JSON.parse(localStorage.getItem('fyiCheckboxesState')) || {};
        fyiCheckboxesState[fyiCheckbox.value] = fyiCheckbox.checked;
        localStorage.setItem('fyiCheckboxesState', JSON.stringify(fyiCheckboxesState));
    }

    function getFYACheckboxesStates() {
        let fyaCheckboxesStates = JSON.parse(localStorage.getItem('fyaCheckboxesState')) || {};
        document.querySelectorAll('input.fyaUser').forEach(function (fyaChkbox) {
            if (fyaCheckboxesStates.hasOwnProperty(fyaChkbox.value)) {
                fyaChkbox.checked = fyaCheckboxesStates[fyaChkbox.value];
            }
        });
    }

    function getFYICheckboxesStates() {
        let fyiCheckboxesStates = JSON.parse(localStorage.getItem('fyiCheckboxesState')) || {};
        document.querySelectorAll('input.fyiUser').forEach(function (fyiChkbox) {
            if (fyiCheckboxesStates.hasOwnProperty(fyiChkbox.value)) {
                fyiChkbox.checked = fyiCheckboxesStates[fyiChkbox.value];
            }
        });
    }

    function enableDropDown(chkResponse) {
        var ddlResponseHours = document.getElementById('ddlTimeOut');
        var txtTimeTaken = document.getElementById('txtTimeTaken');
        ddlResponseHours.disabled = chkResponse.checked ? false : true;
        txtTimeTaken.disabled = chkResponse.checked ? false : true;
        responeseMsg.hidden = chkResponse.checked ? false : true;
        if (!ddlResponseHours.disabled) {
            ddlResponseHours.focus();
        }
    }

</script>