﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Oracle.ManagedDataAccess.Types;
using System.Security.Cryptography;
using System.Text;

namespace BCM.UI.Areas.BCMCalendar.Controllers;
[Area("BCMCalendar")]
public class BCMCalenderConfigurationController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    ManageUsersDetails? _UserDetails = new ManageUsersDetails();
    readonly CVLogger _CVLogger;
    private readonly BCMMail _BCMMail;
    private readonly IWebHostEnvironment _Environment;

    int iNotifyUsersAsFYACheckedCount = 0;
    string strNotifyUsersAsFYA = string.Empty;
    int iNotifyUsersAsFYICheckedCount = 0;
    string strNotifyUsersAsFYI = string.Empty;

    int iNotifyTeamsAsFYACheckedCount = 0;
    string strNotifyTeamsAsFYA = string.Empty;
    int iNotifyTeamsAsFYICheckedCount = 0;
    string strNotifyTeamsAsFYI = string.Empty;

    int iCalendarID = 0;
    public BCMCalenderConfigurationController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, BCMMail BCMMail, IWebHostEnvironment Environment) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _BCMMail = BCMMail;
        _Environment = Environment;
    }

    public void BindDropDowns()
    {
        try
        {
            ViewBag.selectedOrgID = _UserDetails.OrgID;
            ViewBag.OrgInfo = new SelectList(_Utilities.BindOrg(_UserDetails.OrgID), "Id", "OrganizationName");
            ViewBag.UnitList = new SelectList(_Utilities.BindUnit(_UserDetails.OrgID), "UnitID", "UnitName");
            ViewBag.ActivityType = new SelectList(_Utilities.PopulateCalendarActivityType(), "TypeID", "ActivityType");
            ViewBag.CalendarStatus = new SelectList(_Utilities.PopulateCalederStatus(), "StatusID", "StatusName");
            ViewBag.BCMGroupList = _ProcessSrv.GetBCMGroupAllList();
            ViewBag.ResourceList = _Utilities.GetAllResourceList();
            ViewBag.Organizer = new SelectList(ViewBag.ResourceList, "ResourceId", "ResourceName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    [HttpGet]
    public IActionResult BCMCalenderConfiguration(int iCalID)
    {
        AddCalendarActivity objCalendarActivity = new AddCalendarActivity();
        try
        {
            ViewBag.userID = _UserDetails.UserID;
            BindDropDowns();
            if (iCalID > 0)
            {
                objCalendarActivity = BCMCalendarInfoByID(iCalID);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (iCalID == 0)
        {
            return PartialView("_AddBCMCalendar",new AddCalendarActivity());
        }
        else
        {
            return PartialView("_AddBCMCalendar", objCalendarActivity);
        }
    }

    [HttpPost]
    private int AttachmentSave(IFormFile file, int iCalID)
    {
        int iResult = 0;
        bool iSuccess = false;
        Attachments objAttachments = new Attachments();
        try
        {
            string fileExtension = Path.GetExtension(file.FileName);
            string strExtention = fileExtension.TrimStart('.');
            var iFileLengthInKB = file.Length;

            if (_Utilities.CheckFileTypeandSize(strExtention, Convert.ToInt32(iFileLengthInKB), _UserDetails.OrgID))
            {
                byte[] GuidSize = new byte[16];
                Random rd = new Random();
                rd.NextBytes(GuidSize);
                System.Guid guid = new Guid(GuidSize);

                objAttachments.EntityId = (int)BCPEnum.EntityType.BCMCalender;
                objAttachments.RecordId = iCalID;
                objAttachments.AttchmentName = file.FileName;
                objAttachments.Description = "";
                objAttachments.FileSize = Convert.ToInt32(iFileLengthInKB);
                objAttachments.MimeType = strExtention;
                objAttachments.GUID = guid.ToString();
                objAttachments.CreatedBy = _UserDetails.UserID;

                byte[] blob = _Utilities.ConvertToBlob(file);
                if (blob != null)
                {
                    objAttachments.AttachmentObj = (object)blob;

                    iSuccess = _ProcessSrv.CVaultAttachmentSave(objAttachments);

                    if (iSuccess)
                    {
                        var uploadsFolder = Path.Combine("Attachments", "BCMCalendarAttachments");
                        if (!Directory.Exists(uploadsFolder))
                        {
                            Directory.CreateDirectory(uploadsFolder);
                        }

                        var filePath = Path.Combine(uploadsFolder, file.FileName);

                        using (var stream = new FileStream(filePath, FileMode.Create))
                        {
                            file.CopyToAsync(stream).Wait();
                        }
                        iResult = 1; // File conversion save successfully in db and solution
                    }
                }
                else
                {
                    iResult = 4; // Error occurred during file conversion
                }
            }
            else
            {
                iResult = 2; // Invalid file type or size                
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            iResult = 3; // Error occurred during file upload
        }
        return iResult;
    }


    [HttpPost]
    public IActionResult BCMCalenderConfiguration([FromForm] AddCalendarActivity objAddCalendarActivity)
    {
        int iFileUploadResult = 0;
        try
        {
            DateTime yesterday = DateTime.Today.AddDays(-1);

            if (objAddCalendarActivity.ScheduledStartDate.Date <= yesterday)
            {
                return Json(new { success = false, message = "Start date must be greater than yesterday's date..." });
            }

            if (objAddCalendarActivity.ScheduledENDDate.Date <= objAddCalendarActivity.ScheduledStartDate.Date)
            {
                return Json(new { success = false, message = "End date must be greater than Start date..." });
            }

            ViewBag.usersFYA = objAddCalendarActivity.UsersFYA;
            ViewBag.usersFYI = objAddCalendarActivity.UsersFYI;
            ViewBag.teamsFYA = objAddCalendarActivity.TeamsFYA;
            ViewBag.teamsFYI = objAddCalendarActivity.TeamsFYI;

            bool isUpdate = objAddCalendarActivity.Id != 0;
            if (isUpdate)
            {
                if (UpdateCalendarInfo(objAddCalendarActivity))
                {
                    if (CalenderDetails_DeleteByCalenderID(objAddCalendarActivity.Id))
                    {
                        SaveCalendarDetails();
                        if (iCalendarID > 0 && objAddCalendarActivity.File != null && objAddCalendarActivity.File.Length > 0)
                        {
                            iFileUploadResult = AttachmentSave(objAddCalendarActivity.File, iCalendarID);
                        }
                        iFileUploadResult = 1;
                    }
                    else
                    {
                        return Json(new { success = false, message = "Error occurred while updating calendar data." });
                    }
                }
                else
                {
                    return Json(new { success = false, message = "Error occurred while updating calendar data." });
                }
            }
            else
            {
                if (SaveCalendarInfo(objAddCalendarActivity))
                {
                    SaveCalendarDetails();
                    if (iCalendarID > 0 && objAddCalendarActivity.File != null && objAddCalendarActivity.File.Length > 0)
                    {
                        iFileUploadResult = AttachmentSave(objAddCalendarActivity.File, iCalendarID);
                    }
                }
                else
                {
                    return Json(new { success = false, message = "Error occurred while saving calendar data." });
                }
            }

            if (iFileUploadResult == 1)
            {
                return Json(new { success = true, message = "BCM Activity Save Successfully..." });
            }
            else if (iFileUploadResult == 2)
            {
                return Json(new { success = false, message = "Invalid file type or size." });
            }
            else if (iFileUploadResult == 3)
            {
                return Json(new { success = false, message = "Error occurred while uploading the file." });
            }
            else if (iFileUploadResult == 4)
            {
                return Json(new { success = false, message = "Error occurred during file conversion." });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "An error occurred while processing your request." });
        }

        return Json(new { success = false, message = "Unknown error occurred." });
    }

    //[HttpPost]
    //public IActionResult BCMCalenderConfiguration([FromBody] AddCalendarActivity objAddCalendarActivity)
    //{
    //    try
    //    {
    //        //bool isSaveCalInfo = false;
    //        DateTime yesterday = DateTime.Today.AddDays(-1);
    //        if (objAddCalendarActivity.ScheduledStartDate.Date <= yesterday)
    //        {
    //            return Json(new { success = false, message = "Start date must be greater than today's date..." });
    //        }
    //        if (objAddCalendarActivity.ScheduledENDDate.Date <= objAddCalendarActivity.ScheduledStartDate.Date)
    //        {
    //            return Json(new { success = false, message = "End date must be greater than Start date..." });
    //        }

    //        ViewBag.usersFYA = objAddCalendarActivity.UsersFYA;
    //        ViewBag.usersFYI = objAddCalendarActivity.UsersFYI;
    //        ViewBag.teamsFYA = objAddCalendarActivity.TeamsFYA;
    //        ViewBag.teamsFYI = objAddCalendarActivity.TeamsFYI;

    //        if (objAddCalendarActivity.Id != 0)
    //        {
    //            if (UpdateCalendarInfo(objAddCalendarActivity))
    //            {
    //                CalenderDetails_DeleteByCalenderID(objAddCalendarActivity.Id);
    //                SaveCalendarDetails();
    //            }
    //        }
    //        else
    //        {
    //            if (SaveCalendarInfo(objAddCalendarActivity))
    //            {
    //                SaveCalendarDetails();
    //            }
    //        }
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }
    //    return RedirectToAction("ManageBCMCalender", "ManageBCMCalender");
    //}

    public AddCalendarActivity BCMCalendarInfoByID(int iCalID)
    {
        AddCalendarActivity objCalendarActivity = new AddCalendarActivity();
        //Byte[] bytes;
        try
        {
            if (iCalID > 0)
            {
                List<BCMCalenderDetailss> lstBCMCalenderDetailss = _ProcessSrv.GetCalenderDetails_ByCalenderID(iCalID);

                ViewBag.chkUserFYI = (from item in lstBCMCalenderDetailss
                                      where item.UserId > 0 && item.NotificationTypeId == BCPEnum.NotificationAs.FYI.ToString()
                                      select item).ToList();

                string oldUserFYI = string.Empty;
                foreach(BCMCalenderDetailss item in ViewBag.chkUserFYI)
                {
                    oldUserFYI += item.UserName + ", ";
                }
                HttpContext.Session.SetString("oldUserFYI", oldUserFYI);

                ViewBag.chkUserFYA = (from item in lstBCMCalenderDetailss
                                      where item.UserId > 0 && item.NotificationTypeId == BCPEnum.NotificationAs.FYA.ToString()
                                      select item).ToList();

                string oldUserFYA = string.Empty;
                foreach (BCMCalenderDetailss item in ViewBag.chkUserFYA)
                {
                    oldUserFYA += item.UserName + ", ";
                }                
                HttpContext.Session.SetString("oldUserFYA", oldUserFYA);

                ViewBag.chkTeamFYI = (from item in lstBCMCalenderDetailss
                                      where item.TeamId > 0 && item.NotificationTypeId == BCPEnum.NotificationAs.FYI.ToString()
                                      select item).ToList();

                string oldTeamFYI = string.Empty;
                foreach (BCMCalenderDetailss item in ViewBag.chkTeamFYI)
                {
                    oldTeamFYI += item.TeamName + ", ";
                }                
                HttpContext.Session.SetString("oldTeamFYI", oldTeamFYI);

                ViewBag.chkTeamFYA = (from item in lstBCMCalenderDetailss
                                      where item.TeamId > 0 && item.NotificationTypeId == BCPEnum.NotificationAs.FYA.ToString()
                                      select item).ToList();

                string oldTeamFYA = string.Empty;
                foreach (BCMCalenderDetailss item in ViewBag.chkTeamFYA)
                {
                    oldTeamFYA += item.TeamName + ", ";
                }                
                HttpContext.Session.SetString("oldTeamFYA", oldTeamFYA);

                objCalendarActivity = _ProcessSrv.GetAddCalendarActivityDetialByID(iCalID);

                Attachments objAttachment = _ProcessSrv.GetOrgLogoAttachmentListByID((int)BCPEnum.EntityType.BCMCalender, iCalID);

                if (objAttachment != null)
                {
                    if (objAttachment.AttchmentName != null)
                    {
                        objCalendarActivity.AttachmentName = objAttachment.AttchmentName;
                        objCalendarActivity.AttachmentID = objAttachment.AttachmentId;
                    }
                }

                if (objAttachment.AttachmentObj != null)
                {
                    byte[] blobBytes = (Byte[])(objAttachment.AttachmentObj);
                    var uploads = Path.Combine(_Environment.WebRootPath, "Attachments/BCMCalendarAttachments");
                    if (!Directory.Exists(uploads))
                        Directory.CreateDirectory(uploads);
                    //var fileDownloadPath = Path.Combine(uploads, ViewBag.lblUpload.ToString());
                    //if (System.IO.File.Exists(fileDownloadPath))
                    //    System.IO.File.Delete(fileDownloadPath);
                    //using (var fs = new FileStream(fileDownloadPath, FileMode.OpenOrCreate, FileAccess.Write))
                    //{
                    //    fs.Write(blobBytes, 0, blobBytes.Length);
                    //}
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return objCalendarActivity;
    }    

    private bool SaveCalendarInfo(AddCalendarActivity objAddCalendarActivity)
    {
        bool bSuccess = false;

        try
        {
            ViewBag.ActivityName = objAddCalendarActivity.ActivityName;
            objAddCalendarActivity.ActualStartDate = objAddCalendarActivity.ScheduledStartDate;
            objAddCalendarActivity.ActualEndDate = objAddCalendarActivity.ScheduledENDDate;
            objAddCalendarActivity.Remarks = "";
            objAddCalendarActivity.ChangedBy = _UserDetails.UserID;
            objAddCalendarActivity.Reminder = GetReminderString(objAddCalendarActivity.ScheduledStartDate);
            objAddCalendarActivity.RecurrenceRule = GetRecurrenceRuleString(objAddCalendarActivity.ScheduledStartDate.ToString(), objAddCalendarActivity.ScheduledENDDate.ToString());

            iCalendarID = _ProcessSrv.AddCalendarActivitySave(objAddCalendarActivity);

            if (iCalendarID > 0)
            {
                bSuccess = true;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return bSuccess;
    }

    private bool UpdateCalendarInfo(AddCalendarActivity objAddCalendarActivity)
    {
        //bool bSuccess = false;
        bool isUpdated = false;
        try
        {
            ViewBag.ActivityName = objAddCalendarActivity.ActivityName;            
            objAddCalendarActivity.ActualStartDate = objAddCalendarActivity.ScheduledStartDate;
            objAddCalendarActivity.ActualEndDate = objAddCalendarActivity.ScheduledENDDate;
            objAddCalendarActivity.Remarks = "";
            objAddCalendarActivity.ChangedBy = _UserDetails.UserID;
            objAddCalendarActivity.Reminder = GetReminderString(objAddCalendarActivity.ScheduledStartDate);
            objAddCalendarActivity.RecurrenceRule = GetRecurrenceRuleString(objAddCalendarActivity.ScheduledStartDate.ToString(), objAddCalendarActivity.ScheduledENDDate.ToString());

            isUpdated = _ProcessSrv.AddCalendarActivityUpdate(objAddCalendarActivity);

            if (isUpdated)
            {
                iCalendarID = objAddCalendarActivity.Id;
                if (objAddCalendarActivity.File != null)
                {
                    _ProcessSrv.AttachmentDeleteByEntityIDandRecordID((int)BCPEnum.EntityType.BCMCalender, objAddCalendarActivity.Id);
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return isUpdated;
    }    

    private bool CalenderDetails_DeleteByCalenderID(int iCalID)
    {
        try
        {
            return _ProcessSrv.BCMCalenderDetails_DeleteByCalenderID(iCalID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return false; ;
        }
    }

    private string GetReminderString(DateTime StartDate)
    {
        try
        {
            if (StartDate != null)
            {
                StringBuilder sbReminder = new StringBuilder();
                sbReminder.Append("BEGIN:VALARM");
                sbReminder.Append("TRIGGER:-PT10M");
                sbReminder.Append(" X-TELERIK-UID:" + GetUniqueKey());
                sbReminder.Append("END:VALARM");
                return sbReminder.ToString();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return string.Empty;
    }

    private string GetUniqueKey()
    {
        try
        {
            int maxSize = 10;
            char[] chars = new char[62];
            string a;
            a = "**********";         //bcdefghijklmnopqrstuvwxyABCDEFGHIJKLMNOPQRSTUVWXYZ
            chars = a.ToCharArray();
            int size = maxSize;
            byte[] data = new byte[1];
            RNGCryptoServiceProvider crypto = new RNGCryptoServiceProvider();
            crypto.GetNonZeroBytes(data);
            size = maxSize;
            data = new byte[size];
            crypto.GetNonZeroBytes(data);
            StringBuilder result = new StringBuilder(size);
            foreach (byte b in data)
            { result.Append(chars[b % (chars.Length)]); }
            //<span class="code-keyword">return result.ToString();
            return result.ToString();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return string.Empty;
        }
    }

    private string GetRecurrenceRuleString(string StartDate, string EndDAte)
    {
        try
        {
            if (StartDate != string.Empty && EndDAte != string.Empty)
            {
                StringBuilder sbRec = new StringBuilder();
                //DateTime DtStartDate = DateTime.ParseExact(StartDate, CVGlobal.DateTimeFormat, CultureInfo.InvariantCulture);
                //DateTime DtEndDate = DateTime.ParseExact(EndDAte, CVGlobal.DateTimeFormat, CultureInfo.InvariantCulture);
                sbRec.Append("DTSTART:" + Convert.ToDateTime(StartDate).ToString("yyyyMMdd") + "T" + Convert.ToDateTime(StartDate).ToString("HHmmss") + "Z ");
                sbRec.Append("DTEND:" + Convert.ToDateTime(EndDAte).ToString("yyyyMMdd") + "T" + Convert.ToDateTime(EndDAte).ToString("HHmmss") + "Z ");
                sbRec.Append("RRULE:FREQ=HOURLY;COUNT=10;INTERVAL=10");
                return sbRec.ToString();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        // string abc = "DTSTART:20150718T000000Z DTEND:20150718T010000Z RRULE:FREQ=WEEKLY;INTERVAL=1;BYDAY=MO,TU,FR,SA ";
        return string.Empty;
    }

    private void SaveCalendarDetails()
    {
        try
        {
            foreach (var objUserFYA in ViewBag.usersFYA)
            {
                iNotifyUsersAsFYACheckedCount = 1;
                CalendarDetailsSave(0, objUserFYA, BCPEnum.NotificationAs.FYA.ToString());
                ResourcesInfo objRes = _ProcessSrv.GetResourceNameByID(objUserFYA);
                strNotifyUsersAsFYA += objRes.ResourceName + ", ";
            }

            foreach (var objUsersFYI in ViewBag.usersFYI)
            {
                iNotifyUsersAsFYICheckedCount = 1;
                CalendarDetailsSave(0, objUsersFYI, BCPEnum.NotificationAs.FYI.ToString());
                ResourcesInfo objRes = _ProcessSrv.GetResourceNameByID(objUsersFYI);
                strNotifyUsersAsFYI += objRes.ResourceName + ", ";
            }

            foreach (var objTeamsFYA in ViewBag.teamsFYA)
            {
                iNotifyTeamsAsFYACheckedCount = 1;
                CalendarDetailsSave(objTeamsFYA, 0, BCPEnum.NotificationAs.FYA.ToString());
                BCMGroupInfo objGroupInfo = _ProcessSrv.GetBCMGroupByID(objTeamsFYA);
                strNotifyTeamsAsFYA += objGroupInfo.GroupName + ", ";
            }

            foreach (var objTeamsFYI in ViewBag.teamsFYI)
            {
                iNotifyTeamsAsFYICheckedCount = 1;
                CalendarDetailsSave(objTeamsFYI, 0, BCPEnum.NotificationAs.FYI.ToString());
                BCMGroupInfo objGroupInfo = _ProcessSrv.GetBCMGroupByID(objTeamsFYI);
                strNotifyTeamsAsFYI += objGroupInfo.GroupName + ", ";
            }

            AuditLogs();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private bool CalendarDetailsSave(int iTeamID, int iUserID, string strNotificationType)
    {
        BCMCalenderDetailss objCalDetails = new BCMCalenderDetailss();
        try
        {
            if (iCalendarID > 0)
            {
                objCalDetails.TeamId = iTeamID;
                objCalDetails.UserId = iUserID;
                objCalDetails.NotificationTypeId = strNotificationType;
                objCalDetails.CalenderId = iCalendarID;
                return _ProcessSrv.CalenderDetailsSave(objCalDetails);
            }
            return false;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return false;
        }
    }

    public void AuditLogs()
    {
        try
        {
            strNotifyUsersAsFYA = strNotifyUsersAsFYA.TrimEnd(' ', ',');
            strNotifyUsersAsFYI = strNotifyUsersAsFYI.TrimEnd(' ', ',');
            strNotifyTeamsAsFYA = strNotifyTeamsAsFYA.TrimEnd(' ', ',');
            strNotifyTeamsAsFYI = strNotifyTeamsAsFYI.TrimEnd(' ', ',');



            string strOldUserFYI = string.Empty;
            string OldUserFYI = HttpContext.Session.GetString("oldUserFYI");
            if (!string.IsNullOrEmpty(OldUserFYI))
            {
                strOldUserFYI = OldUserFYI.TrimEnd(' ', ',');
            }

            
            string strOldUserFYA = string.Empty;
            string OldUserFYA = HttpContext.Session.GetString("oldUserFYA");
            if (!string.IsNullOrEmpty(OldUserFYA))
            {
                strOldUserFYA = OldUserFYA.TrimEnd(' ', ',');
            }

            string strOldTeamFYI = string.Empty;
            string OldTeamFYI = HttpContext.Session.GetString("oldTeamFYI");
            if (!string.IsNullOrEmpty(OldTeamFYI))
            {
                strOldTeamFYI = OldTeamFYI.TrimEnd(' ', ',');
            }

            string strOldTeamFYA = string.Empty;
            string OldTeamFYA = HttpContext.Session.GetString("oldTeamFYA");
            if (!string.IsNullOrEmpty(OldTeamFYA))
            {
                strOldTeamFYA = OldTeamFYA.TrimEnd(' ', ',');
            }

            if (!string.IsNullOrEmpty(strOldUserFYA))
            {
                AuditLogs_DetailsForSave(strOldUserFYA, strNotifyUsersAsFYA, "Update", "Notify Users (as FYA)");
            }
            else if (!string.IsNullOrEmpty(strNotifyUsersAsFYA))
            {
                AuditLogs_DetailsForSave("", strNotifyUsersAsFYA, "Save", "Notify Users (as FYA)");
            }

            if (!string.IsNullOrEmpty(strOldUserFYI))
            {
                AuditLogs_DetailsForSave(strOldUserFYI, strNotifyUsersAsFYI, "Update", "Notify Users (as FYI)");
            }
            else if (!string.IsNullOrEmpty(strNotifyUsersAsFYI))
            {
                AuditLogs_DetailsForSave("", strNotifyUsersAsFYI, "Save", "Notify Users (as FYI)");
            }

            if (!string.IsNullOrEmpty(strOldTeamFYI))
            {
                AuditLogs_DetailsForSave(strOldTeamFYI, strNotifyTeamsAsFYA, "Update", "Notify Teams (as FYA)");
            }
            else if (!string.IsNullOrEmpty(strNotifyTeamsAsFYA))
            {
                AuditLogs_DetailsForSave("", strNotifyTeamsAsFYA, "Save", "Notify Teams (as FYA)");
            }

            if (!string.IsNullOrEmpty(strOldTeamFYI))
            {
                AuditLogs_DetailsForSave(strOldTeamFYI, strNotifyTeamsAsFYI, "Update", "Notify Teams (as FYI)");
            }
            else if (!string.IsNullOrEmpty(strNotifyTeamsAsFYI))
            {
                AuditLogs_DetailsForSave("", strNotifyTeamsAsFYI, "Save", "Notify Teams (as FYI)");
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public void AuditLogs_DetailsForSave(string strOldValue, string strNewValue, string strOperation, string strDescription)
    {
        try
        {
            string strActivityName = ViewBag.ActivityName;
            List<UserActivitiesInfo> lstUserActs = new List<UserActivitiesInfo>();
            lstUserActs = _ProcessSrv.UserActivities_GetBySessionActive(Convert.ToInt32(_UserDetails.UserID), 1);

            int iIdValue = 0;
            string strIPAddress = string.Empty;

            if (lstUserActs.Count > 0)
            {
                foreach (UserActivitiesInfo objUserAct in lstUserActs)
                {
                    iIdValue = objUserAct.ID;
                    strIPAddress = objUserAct.IPAddress;
                }
            }

            if (strOperation == "Save")
            {
                Auditlog_Process_Details objAuditlog = new Auditlog_Process_Details();

                objAuditlog.ModuleID = 66;
                objAuditlog.RecordID = iCalendarID;
                objAuditlog.ActionID = "I";
                objAuditlog.CreatedBy = _UserDetails.UserID;
                objAuditlog.RecordName = strActivityName;
                objAuditlog.IPAddress = strIPAddress;
                objAuditlog.IDValue = iIdValue;

                int iAuditlog = _ProcessSrv.SaveAuditlog_Process(objAuditlog);

                Auditlog_Process_Details objAuditlogDetails = new Auditlog_Process_Details();

                objAuditlogDetails.DetailsID = iAuditlog;
                objAuditlogDetails.ChangeDescription = strDescription;
                objAuditlogDetails.PreviousValue = strOldValue;
                objAuditlogDetails.NewValue = strNewValue;

                int iAuditlogDetails = _ProcessSrv.SaveAuditlog_Process_Details(objAuditlogDetails);
            }
            if (strOperation == "Update")
            {
                Auditlog_Process_Details objAuditlog = new Auditlog_Process_Details();

                objAuditlog.ModuleID = 66;
                objAuditlog.RecordID = iCalendarID;
                objAuditlog.ActionID = "U";
                objAuditlog.CreatedBy = _UserDetails.UserID;
                objAuditlog.RecordName = strActivityName;
                objAuditlog.IPAddress = strIPAddress;
                objAuditlog.IDValue = iIdValue;

                int iAuditlog = _ProcessSrv.SaveAuditlog_Process(objAuditlog);

                Auditlog_Process_Details objAuditlogDetails = new Auditlog_Process_Details();

                objAuditlogDetails.DetailsID = iAuditlog;
                objAuditlogDetails.ChangeDescription = strDescription;
                objAuditlogDetails.PreviousValue = strOldValue;
                objAuditlogDetails.NewValue = strNewValue;

                int iAuditlogDetails = _ProcessSrv.SaveAuditlog_Process_Details(objAuditlogDetails);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    [HttpPost]
    public IActionResult SendNotificationsClick(AddCalendarActivity objAddCalendarActivity)
    {
        bool bSuccess = false;
        try
        {
            DateTime yesterday = DateTime.Today.AddDays(-1);

            if (objAddCalendarActivity.ScheduledStartDate.Date <= yesterday)
            {
                return Json(new { success = false, message = "Start date must be greater than yesterday's date..." });
            }

            if (objAddCalendarActivity.ScheduledENDDate.Date <= objAddCalendarActivity.ScheduledStartDate.Date)
            {
                return Json(new { success = false, message = "End date must be greater than Start date..." });
            }

            if (!_Utilities.ValidateSelectedResourceOrTeams(objAddCalendarActivity.UsersFYA.Count, objAddCalendarActivity.UsersFYI.Count,
                objAddCalendarActivity.TeamsFYA.Count, objAddCalendarActivity.TeamsFYI.Count))
            {
                return Json(new { success = false, message = "Please select at least one USER or TEAM..." });
            }

            bSuccess = SendNotifications(objAddCalendarActivity);

            if (bSuccess)
            {
                return Json(new { success = true, message = "Notified successfully..." });
            }
            return Json(new { success = false, message = ViewBag.Error });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            bSuccess = false;
            return Json(new { success = false, message = "An error occurred while processing your request." });
        }
    }

    private bool SendNotifications(AddCalendarActivity objAddCalendarActivity)
    {
        bool bSuccess = false;
        try
        {
            List<AddCalendarActivity> lstCalendarActivityType = _ProcessSrv.GetActivityAll();
            var objActivityType = lstCalendarActivityType.Where(x => x.TypeID == Convert.ToInt16(objAddCalendarActivity.ActivityType)).Select(y => y.ActivityType).FirstOrDefault();
            string strEmailID = string.Empty;
            string strMobileNo = string.Empty;
            int i = 0;
            //string strAttachment = string.Empty;

            string strSubject = "Calendar Activity: " + objAddCalendarActivity.ActivityName;
            string strLocation = "Pune";
            string strMailBody = "<html><body>" + "This is ICO file " + " <br/>" + " </body></html>";
            strMailBody = objAddCalendarActivity.ActivityName;
            strSubject = strMailBody.Replace("<html><body>", "");
            string strDesc = "To add this appointment to your calendar, please open and save the attached file." +
                            " </body></html>";
            strDesc = strDesc.Replace("<br/>", @"\n");
            strDesc = strDesc.Replace("<br/><br/>", @"\n");
            strDesc = strDesc.Replace("</body></html>", "");

            string[] contents = {   "BEGIN:VCALENDAR",
                                    "PRODID:-//Flo Inc.//FloSoft//EN",
                                    "BEGIN:VEVENT",
                                    "DTSTART:" + Convert.ToDateTime(objAddCalendarActivity.ScheduledStartDate).ToUniversalTime().ToString("yyyyMMdd\\THHmmss\\Z"),
                                    "DTEND:" + Convert.ToDateTime(objAddCalendarActivity.ScheduledENDDate).ToUniversalTime().ToString("yyyyMMdd\\THHmmss\\Z"),
                                    "LOCATION:" + strLocation,
                                    "DESCRIPTION;ENCODING=ESCAPED-CHAR:" + strDesc,
                                    "SUMMARY:" + strSubject,
                                    "PRIORITY:3",
                                    "END:VEVENT",
                                    "END:VCALENDAR"
                                };

            #region Bind Attachment
            
            string[] strAttachment = new string[2];
            if (objAddCalendarActivity.AttachmentName != null)
            {
                string basePath = Path.Combine(_Environment.ContentRootPath, "Attachments", "BCMCalendarAttachments");
                string strFileName2 = Path.Combine(basePath, objAddCalendarActivity.AttachmentName);
                strAttachment[1] = strFileName2;
            }

            #endregion

            BCMGroupNotification bcmGrpNotfn = new BCMGroupNotification();
            List<BCMCalenderDetailss> lstBCMCalenderDetailss = _ProcessSrv.GetCalenderDetails_ByCalenderID(objAddCalendarActivity.Id);

            foreach (BCMCalenderDetailss objCalDetails in lstBCMCalenderDetailss)
            {
                if (objCalDetails.UserId != 0 && objCalDetails.TeamId == 0)
                {
                    strSubject = "Calendar Activity: " + objCalDetails.NotificationTypeId + "-" + objAddCalendarActivity.ActivityName;
                    SendMailForBCM(bcmGrpNotfn, objCalDetails.NotificationTypeId, strSubject, strMailBody, false, ref strEmailID,
                        ref strMobileNo, ref i, objCalDetails.UserId, strAttachment, objActivityType, objAddCalendarActivity);
                }

                if (objCalDetails.UserId == 0 && objCalDetails.TeamId != 0)
                {
                    List<ResourcesInfo> lstResInfo = _ProcessSrv.GetResourceInfo_ForResourceID_ByGroupMapID(objCalDetails.TeamId.ToString());
                    strSubject = "Calendar Activity: " + objCalDetails.NotificationTypeId + "-" + objCalDetails.TeamName  + "-" + objAddCalendarActivity.ActivityName;
                    foreach (ResourcesInfo objRes in lstResInfo)
                    {
                        SendMailForBCM(bcmGrpNotfn, objCalDetails.NotificationTypeId, strSubject, strMailBody, false, ref strEmailID,
                        ref strMobileNo, ref i, objRes.ResourceId, strAttachment, objActivityType, objAddCalendarActivity);
                    }
                }
            }

            //HashSet<string> sentEmails = new HashSet<string>();

            //foreach (BCMCalenderDetailss objCalDetails in lstBCMCalenderDetailss)
            //{
            //    if (objCalDetails.UserId != 0 && objCalDetails.TeamId == 0)
            //    {
            //        strSubject = "Calendar Activity: " + objCalDetails.NotificationTypeId + "-" + objAddCalendarActivity.ActivityName;
            //        if (!sentEmails.Contains(strEmailID))
            //        {
            //            SendMailForBCM(bcmGrpNotfn, objCalDetails.NotificationTypeId, strSubject, strMailBody, false, ref strEmailID,
            //                ref strMobileNo, ref i, objCalDetails.UserId, strAttachment, objActivityType, objAddCalendarActivity);
            //            sentEmails.Add(strEmailID);
            //        }
            //    }

            //    if (objCalDetails.UserId == 0 && objCalDetails.TeamId != 0)
            //    {
            //        List<ResourcesInfo> lstResInfo = _ProcessSrv.GetResourceInfo_ForResourceID_ByGroupMapID(objCalDetails.TeamId.ToString());
            //        strSubject = "Calendar Activity: " + objCalDetails.NotificationTypeId + "-" + objCalDetails.TeamName + "-" + objAddCalendarActivity.ActivityName;
            //        foreach (ResourcesInfo objRes in lstResInfo)
            //        {
            //            if (!sentEmails.Contains(strEmailID))
            //            {
            //                SendMailForBCM(bcmGrpNotfn, objCalDetails.NotificationTypeId, strSubject, strMailBody, false, ref strEmailID,
            //                ref strMobileNo, ref i, objRes.ResourceId, strAttachment, objActivityType, objAddCalendarActivity);
            //                sentEmails.Add(strEmailID);
            //            }
            //        }
            //    }
            //}

            return true;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            ViewBag.Error = "Error occurred while sending notifications.";
            return bSuccess;
        }
    }

    private bool SendMailForBCM(BCMGroupNotification bcmGrpNotfn, string strNotificationType, string strSubject, string strMailBody, bool mgtGrp, ref string emailID, ref string mobileNo,
         ref int i, int iResID, string[] objAttchment,string strActivityType, AddCalendarActivity objAddCalendarActivity)
    {
        bool bSuccess = false;
        bool bStatus = false;
        try
        {
            if (iResID > 0)
            {
                ResourcesInfo objRes = _ProcessSrv.GetResourcesByResourceID(iResID);
                
                bcmGrpNotfn.ID = 0;
                bcmGrpNotfn.SentSuccess = "-1";
                bcmGrpNotfn.resourceID = iResID;
                bcmGrpNotfn.CommunicationMode = (int)BCPEnum.NotificationType.EMail;

                int iRecordID = 0;
                string strDetails = string.Empty;
                string strAppUrl = $"{this.Request.Scheme}://{this.Request.Host}{this.Request.PathBase}";

                string strLinkPage = $"{strAppUrl}/BCMTeams/BCMTeamResponse/BCMTeamResponse";
                string strLink1 = $"{strLinkPage}?iNotificationID={iRecordID}&iUserID={iResID}&strRESP=Y";
                string strLink2 = $"{strLinkPage}?iNotificationID={iRecordID}&iUserID={iResID}&strRESP=N";

                //strMailBody = "Dear " + objRes.ResourceName + ",<br/><br/>This is inform you that your " + strActivityType + " has been scheduled for <b>'" 
                //    + objAddCalendarActivity.ActivityName
                //                + "'</b> on <b>'" + objAddCalendarActivity.ScheduledStartDate + " ' to '" + objAddCalendarActivity.ScheduledENDDate + 
                //                "'</b> And you are a part of this " + strActivityType + ". <br/>"
                //                + "<br/><br/><br/> <b>" + strActivityType + " description:</b><br/>" + objAddCalendarActivity.ActivityDetails + 
                //                "<br/><br/><br/><b>Admin</b>,<br/>Continuity Vault";

                strMailBody= "Dear " + objRes.ResourceName + ",<br/><br/>This is inform you that your " + strActivityType + " has been scheduled for <b>'"
                    + objAddCalendarActivity.ActivityName + "'</b> on <b>'" + objAddCalendarActivity.ScheduledStartDate + "' to '" +
                    objAddCalendarActivity.ScheduledENDDate + "'</b> And you are a part of this " + strActivityType + ". <br/>" + "<br/><br/><br/> <b>" +
                    strActivityType + " description:</b><br/>" + objAddCalendarActivity.ActivityDetails +
                    "<br/><br/><br/><b>Admin</b>,<br/>Continuity Vault";

                bcmGrpNotfn.SentSuccess = (_BCMMail.SendMail(strSubject, strMailBody, new string[] { objRes.CompanyEmail },
                    new string[] { string.Empty }, new string[] { string.Empty }, objAttchment, _UserDetails.OrgID.ToString(),null)) ? "1" : "0";

                //bcmGrpNotfn.SentSuccess = (_BCMMail.SendMail(strSubject, strMailBody, objRes.CompanyEmail, string.Empty, string.Empty, objAttchment,
                //    _UserDetails.OrgID.ToString(),"","",_UserDetails.UserID.ToString())) ? "1" : "0";

                bSuccess = bcmGrpNotfn.SentSuccess == "1" ? true : false;
                bStatus = NotifiedStatusUpdate(objAddCalendarActivity.Id,(int)BCPEnum.DrillStatus.Scheduled);
            }
            return bSuccess;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            ViewBag.Error = "Error occurred while sending Email notification.";
            return bSuccess;
        }
    }

    private bool NotifiedStatusUpdate(int iCalID,int iDrillStatus)
    {
        try
        {
            AddCalendarActivity objAddCalendarActivity = new AddCalendarActivity();
            objAddCalendarActivity.Id = iCalID;
            objAddCalendarActivity.Status = iDrillStatus.ToString();
            return _ProcessSrv.AddCalendarActivityDetails_StatusUpdate(objAddCalendarActivity);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            ViewBag.Error = "Error occurred while updating the status of the calendar activity.";
            return false;
        }
    }

    [HttpGet]
    public IActionResult DeleteBCMCalendar(int iCalID)
    {
        AddCalendarActivity objCalDetails = new AddCalendarActivity();
        try
        {
            if (iCalID > 0)
            {
                objCalDetails = _ProcessSrv.GetAddCalendarActivityDetialByID(iCalID);
            }
            return PartialView("_DeleteBCMCalendar", objCalDetails);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMCalender", "ManageBCMCalender");
    }

    [HttpPost]
    public IActionResult DeleteBCMCalendar(AddCalendarActivity objCalDetails)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.AddCalendarActivityDeleteByID(objCalDetails.Id, _UserDetails.UserID);
            if (bSuccess)
            {
                return RedirectToAction("ManageBCMCalender", "ManageBCMCalender");
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_DeleteBCMCalendar", objCalDetails);
    }
}