﻿/* Form Input & Select Style */
.form-group {
    position: relative;
    margin-bottom: 1rem !important;
}

.input-group {
    border-bottom: var(--bs-border-width) solid var(--bs-border-color);
    background-color: transparent;
}

    .input-group .form-control {
        border: none;
    }

    .input-group .form-select {
        border: none;
    }

    .input-group .input-group-text {
        border: none;
        background-color: transparent;
        padding: 0.375rem .0rem;
    }


    .input-group:focus {
        color: var(--bs-body-color);
        background-color: transparent;
        border-bottom: 1px solid var(--bs-primary);
    }

    .input-group:focus-within {
        color: var(--bs-body-color);
        background-color: transparent;
        border-bottom: 1px solid var(--bs-primary);
    }

.input-group-text:focus-within {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-primary);
    border: none;
}

input:-internal-autofill-selected {
    background-image: none !important;
    background-color: transparent !important;
}

.form-control.is-invalid:focus, .was-validated .form-control:invalid:focus {
    border-color: transparent !important;
    box-shadow: none !important;
}

.form-control.is-valid:focus, .was-validated .form-control:valid:focus {
    border-color: transparent !important;
    box-shadow: none !important;
}

.progressive-validation .form-control:not(.is-invalid):not(.is-valid):focus, .progressive-validation .form-select:not(.is-invalid):not(.is-valid):focus {
    border-color: transparent !important;
    box-shadow: none !important;
}

.progressive-validation .form-control:not(.is-invalid):not(.is-valid):focus,
.progressive-validation .form-select:not(.is-invalid):not(.is-valid):focus {
    border-color: transparent !important;
    box-shadow: none !important;
}

.form-control::-moz-placeholder {
    color: var( --bs-placeholder-font-color ) !important;
    font-size: var(--bs-body-font-size-small) !important;
    opacity: 1;
}

.form-control::placeholder {
    color: var(--bs-placeholder-font-color) !important;
    font-size: var(--bs-body-font-size-small) !important;
    opacity: 1;
}

.form-select::-moz-placeholder {
    color: var(--bs-placeholder-font-color) !important;
    font-size: var(--bs-body-font-size-small) !important;
}

.form-select::placeholder {
    color: var(--bs-placeholder-font-color) !important;
    font-size: var(--bs-body-font-size-small) !important;
}

.form-control {
    font-size: var(--bs-body-font-size) !important;
    color: var(--bs-body-color);
    background-color: transparent;
}

    .form-control:focus {
        color: var(--bs-body-color);
        background-color: transparent;
        border-color: transparent;
        outline: 0;
        box-shadow: none;
    }

.form-select {
    font-size: var(--bs-body-font-size) !important;
    color: var(--bs-body-color);
    background-color: transparent;
}

.form-select-sm {
    font-size: var(--bs-body-font-size);
}

.form-select:focus {
    border-color: transparent;
    box-shadow: none;
}

.form-select:disabled {
    background-color: transparent;
    cursor: no-drop;
}

.Search-Input {
    width: 200px;
}
/* Selectize Select */

.selectize-dropdown-emptyoptionlabel {
    text-align: left;
}

.selectize-input {
    padding-bottom: 0;
}
/*.selectize-input > * {
    width: 138px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

}*/ 

.selectize-control.single .selectize-input:not(.no-arrow):after {
    right: calc(0rem + 5px);
}

.selectize-input.full {
    background-color: transparent;
}

.selectize-input {
    border: 0px solid #ced4da;
}

.selectize-input {
    min-height: calc(1.5em + 0.75rem + 0px);
}

    .selectize-input.focus {
        border-color: transparent;
        box-shadow: none;
    }

.selectize-input, .selectize-control.single .selectize-input.input-active {
    background: transparent;
}

.selectize-dropdown .selected {
    background-color: var(--bs-primary);
    color: var(--bs-white);
}

.invalid-feedback {
    text-align: right;
    position: absolute;
    margin-top: -1px !important;
    border-top: 1px solid var(--bs-danger);
}

.selectize-dropdown, .selectize-dropdown.form-control {
    background: var(--bs-white) !important;
}

/* End Form Input & Select Style */

/* Icon Button Style */
/*.btn {
    height: fit-content;
}*/

.btn-action i {
    font-size: var(--bs-page-title-font-size);
}

.icon-btn {
    display: flex;
    align-items: center;
}

    .icon-btn i {
        padding-right: 6px;
    }

/* End Button Style */

.form-check-input:checked {
    background-color: #e63875;
    border-color: #e63875;
}

.modal-footer button {
    max-width: 150px !important;
    min-width: 90px;
}
