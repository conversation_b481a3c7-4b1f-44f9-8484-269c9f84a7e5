﻿@model IEnumerable<BCM.BusinessClasses.OrgGroup>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Organization Group </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <div class="dropdown">
            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                <i class="cv-filter align-middle" title="View Filter"></i>
            </button>
            <form class="dropdown-menu p-3 border-0" style="width:15rem;">
                <div class="mb-3">
                    <label>Organizations</label>
                    <div class="input-group">
                        <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                        <select class="form-select form-control selectized" autocomplete="on" id="ddlOrgGroup" aria-label="Default select example">
                            <option value="0">-- All Org Groups --</option>
                            @foreach (var organizations in ViewBag.OrgGroup)
                            {
                                <option value="@organizations.Value">@organizations.Text</option>
                            }
                        </select>
                    </div>
                </div>
            </form>
        </div>

        <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="Modal"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>

<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>@Html.DisplayName("Org Group Name")</th>
                <th>@Html.DisplayName("Communication Details")</th>
                <th>@Html.DisplayName("Legal Entity")</th>
                <th>@Html.DisplayName("SPOC Details")</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="tblBody">
            @await Html.PartialAsync("_FilterTableRecords")
        </tbody>
    </table>
</div>

<!-- Configuration Modal -->
<div class="modal fade" id="Modal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Organization Group Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-data">
            </div>
        </div>
    </div>
</div>
<!--End Configuration Modal -->
<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>
<!-- End Delete Modal -->
@section Scripts {
    <script>
        $(document).ready(function () {

            $('#btnCreate').click(function () {
                $.get('@Url.Action("AddOrgGroup", "OrgGroup")', function (data) {
                    $('.modal-data').html(data);
                    $('#Modal').modal('show');
                });
            });

            // $(document).on('click', '.btnEdit', function () {
            //     var iID = $(this).data('id');
            //     $.get('/BCMOrgStructure/OrgGroup/EditOrgGroup/' + iID, function (data) {
            //         $('.modal-body').html(data);
            //         $('#Modal').modal('show');
            //     });
            // });
            $(document).on('click', '.btnEdit', function () {
                var iID = $(this).data('id');
                $.get('@Url.Action("EditOrgGroup", "OrgGroup")', { iID: iID }, function (data) {
                    $('.modal-data').html(data);
                    $('#Modal').modal('show');
                });
            });

            $(document).on('click', '.btnDelete', function () {
                var iID = $(this).data('id');
                $.get('@Url.Action("DeleteOrgGroup", "OrgGroup")', { iID: iID }, function (data) {
                    $('#DeleteBody').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        $('#DeleteModal').modal('hide');

                        // Update toast message
                        $('#liveToast .toast-body .d-flex span:last-child').text(data.message);

                        // Determine toast color based on operation result and type
                        const toastElement = $('#liveToast');
                        const formAction = form.attr('action').toLowerCase();

                        // Remove existing background classes
                        toastElement.removeClass('bg-success bg-warning bg-danger');

                        if (data && data.success) {
                            // Success: Add appropriate background class based on action
                            if (formAction.includes('edit')) {
                                toastElement.addClass('bg-success');
                            } else {
                                toastElement.addClass('bg-success');
                            }
                        } else {
                            // Failure: Add danger background class
                            toastElement.addClass('bg-danger');
                        }

                        const toastLiveExample = document.getElementById('liveToast');
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                        toastBootstrap.show();

                        //Delay reload to allow toast to be visible
                        setTimeout(function () {
                            location.reload();
                        }, 3000);
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

            $('#ddlOrgGroup').change(function () {
                var selectedValue = $(this).val();
                $.get('@Url.Action("GetOrgGroupByID", "OrgGroup")', { iOrgGroupID: selectedValue }, function (data) {
                    var tblBody = $('#tblBody');
                    tblBody.empty();
                    $('#example tbody').html(data);
                });
            });
        })

    </script>
}



