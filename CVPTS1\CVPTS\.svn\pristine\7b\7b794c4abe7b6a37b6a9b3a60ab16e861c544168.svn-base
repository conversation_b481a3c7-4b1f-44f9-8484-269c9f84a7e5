﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.Title = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    #RiskHeatMap_Chart {
        width: 100%;
        height: calc(100vh - 365px);
    }

    /* Fixed column widths for both tables */
    #tblheatmapSeverity, #tblheatmapSeverity1 {
        width: 100%;
        table-layout: fixed;
    }

        /* First column width */
        #tblheatmapSeverity th:first-child,
        #tblheatmapSeverity td:first-child,
        #tblheatmapSeverity1 th:first-child,
        #tblheatmapSeverity1 td:first-child {
            width: 150px;
        }

        /* Other columns equal width */
        #tblheatmapSeverity th:not(:first-child),
        #tblheatmapSeverity td:not(:first-child),
        #tblheatmapSeverity1 th:not(:first-child),
        #tblheatmapSeverity1 td:not(:first-child) {
            width: calc((100% - 150px) / 5);
        }

        /* Equal height for heat map cells */
        #tblheatmapSeverity td {
            height: 50px;
            vertical-align: middle;
        }

        /* Larger height for risk severity description cells */
        #tblheatmapSeverity1 td {
            height: 100px;
            vertical-align: middle;
        }

        /* Ensure text wrapping in cells */
        #tblheatmapSeverity td, #tblheatmapSeverity th,
        #tblheatmapSeverity1 td, #tblheatmapSeverity1 th {
            word-wrap: break-word;
        }
</style>


<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Risk HeatMap Configuration</h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>

        <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"> <i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>

<!-- Risk HeatMap Configuration -->
@* <div class="d-flex align-items-center mb-3">
    <i class="cv-edit-pencil me-2 text-primary"></i>
    <span class="fw-semibold text-primary">Risk HeatMap Configuration</span>
</div> *@

<div class="d-flex align-items-center mb-3">
    <div class="input-group me-2" style="width: 300px;">
        <select class="form-select form-select-sm" aria-label="Profile selection" id="profileSelect">
           <option value="0">Select Profile</option>
           <option value="1" selected>RiskProfileConfiguration</option>
           <option value="2">RiskProfileHeatMap</option>
        </select>
        
        <button class="btn btn-sm btn-primary" type="button">
            <i class="cv-edit" title="Edit"></i>
        </button>
    </div>

    <div class="form-check me-3">
        <input class="form-check-input" type="checkbox" id="setAsDefaultCheck" checked>
        <label class="form-check-label" for="setAsDefaultCheck">Set As Default Risk Profile</label>
    </div>

    <button type="button" class="btn btn-primary btn-sm">Create New Profile using this</button>
</div>
<div>
    
</div>
<div class="Page-Condant card border-0">
    @* <div class="card-header header border-0">

        <form class="d-flex tab-design">
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="option1" checked>
                <label class="form-check-label" for="inlineRadio1">All Risks</label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="option2">
                <label class="form-check-label" for="inlineRadio2">Inherent Risks</label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="option3">
                <label class="form-check-label" for="inlineRadio3">Residual Risks</label>
            </div>
        </form>
        <nav aria-label="Page navigation example" style="display:none;">
            <ul class="pagination pagination-sm mb-0">

                <li class="page-item"><a class="page-link" href="#">1D</a></li>
                <li class="page-item"><a class="page-link" href="#">1W</a></li>
                <li class="page-item"><a class="page-link" href="#">1M</a></li>
                <li class="page-item"><a class="page-link" href="#">3M</a></li>
                <li class="page-item"><a class="page-link" href="#">6M</a></li>
                <li class="page-item"><a class="page-link" href="#">1Y</a></li>
                <li class="page-item"><a class="page-link active" href="#">3Y</a></li>

            </ul>
        </nav>
    </div> *@
    <div class="card-body pt-0">



        <div class="row">
            <div class="col-12">
                <div class="p-2 bg-secondary-subtle text-center my-2">
                    <span>Impact( As a % of Annual Revenues )</span>
                </div>

                <!-- Heat Map Table -->
                <div class="bg-white rounded p-3 mb-4">
                    <div class="table-responsive">
                        <table class="table table-bordered text-center mb-3 risk-table" id="tblheatmapSeverity">
                            <thead>
                                <tr class="bg-light">
                                    <th class="text-center">HEAT MAP</th>
                                    <th style="background-color: #48A422;">Insignificant</th>
                                    <th style="background-color: #FFCC00;">Low</th>
                                    <th style="background-color: #FFA500;">Medium</th>
                                    <th style="background-color: #FF3300;">High</th>
                                    <th style="background-color: #FF3300;">Critical</th>
                                </tr>
                                <tr>
                                    <th></th>
                                    <th class="text-center">1</th>
                                    <th class="text-center">2</th>
                                    <th class="text-center">3</th>
                                    <th class="text-center">4</th>
                                    <th class="text-center">5</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="text-center bg-light">Rare<br>1</th>
                                    <td style="background-color: #48A422;">1</td>
                                    <td style="background-color: #FFCC00;">2</td>
                                    <td style="background-color: #FFA500;">3</td>
                                    <td style="background-color: #FF3300;">4</td>
                                    <td style="background-color: #FF3300;">5</td>
                                </tr>
                                <tr>
                                    <th class="text-center bg-light">Unlikely<br>2</th>
                                    <td style="background-color: #FFCC00;">2</td>
                                    <td style="background-color: #FFCC00;">4</td>
                                    <td style="background-color: #FFA500;">6</td>
                                    <td style="background-color: #FF3300;">8</td>
                                    <td style="background-color: #FF3300;">10</td>
                                </tr>
                                <tr>
                                    <th class="text-center bg-light">Possible<br>3</th>
                                    <td style="background-color: #FFA500;">3</td>
                                    <td style="background-color: #FFA500;">6</td>
                                    <td style="background-color: #FFA500;">9</td>
                                    <td style="background-color: #FF3300;">12</td>
                                    <td style="background-color: #FF3300;">15</td>
                                </tr>
                                <tr>
                                    <th class="text-center bg-light">Likely<br>4</th>
                                    <td style="background-color: #FF3300;">4</td>
                                    <td style="background-color: #FF3300;">8</td>
                                    <td style="background-color: #FF3300;">12</td>
                                    <td style="background-color: #FF3300;">16</td>
                                    <td style="background-color: #FF3300;">20</td>
                                </tr>
                                <tr>
                                    <th class="text-center bg-light">Almost Certain<br>5</th>
                                    <td style="background-color: #FF3300;">5</td>
                                    <td style="background-color: #FF3300;">10</td>
                                    <td style="background-color: #FF3300;">15</td>
                                    <td style="background-color: #FF3300;">20</td>
                                    <td style="background-color: #FF3300;">25</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Risk Severity Section -->
                    <div class="table-responsive mt-4">
                        <table class="table table-bordered text-center risk-table risk-severity-table" id="tblheatmapSeverity1">
                            <thead>
                                <tr class="bg-light">
                                    <th class="text-center">RISK SEVERITY<br>( Probability x Impact )</th>
                                    <th class="text-center" style="background-color: #48A422;">Insignificant</th>
                                    <th class="text-center" style="background-color: #FFCC00;">Low</th>
                                    <th class="text-center" style="background-color: #FFA500;">Medium</th>
                                    <th class="text-center" style="background-color: #FF3300;">High</th>
                                    <th class="text-center" style="background-color: #FF3300;">Critical</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td></td>
                                    <td>Potential to cause<br>insignificant impact on<br>objectives</td>
                                    <td>Potential to cause minor<br>impact that, in most cases,<br>can be absorbed</td>
                                    <td>Potential to cause noticeable<br>impact on objectives</td>
                                    <td>Potential to cause major<br>impact on objectives</td>
                                    <td>Potential to cause severe<br>impact on objectives</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="RiskHeatMap_Chart"></div>
            </div>

        </div>

    </div>
    <!-- Configuration Modal -->
    <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">Risk Profile Configuration</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Profile Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-profile"></i></span>
                                    <input type="text" class="form-control" placeholder="Enter Profile Name" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Impact Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Impact Description" style="height:0px"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Risk Severity Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-risk-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Risk Severity Description" style="height:0px"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">

                            <div class="form-group">
                                <label class="form-label">Profile Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Profile Description" style="height:0px"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Probability Description</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <textarea class="form-control" placeholder="Enter Probability Description" style="height:0px"></textarea>
                                </div>
                            </div>
                            <div class="form-group d-flex align-items-center gap-2">
                                <div>
                                    <label class="form-label">Impact Scale</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-impact"></i></span>
                                        <input type="text" class="form-control" placeholder="Enter Impact Scale" />
                                    </div>
                                </div>
                                <div>
                                    <label class="form-label">Probability Scale</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cv-impact"></i></span>
                                        <input type="text" class="form-control" placeholder="Enter Probability Scale" />
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--End Configuration Modal -->
    <!-- Configuration Modal -->
    <div class="modal fade" id="AddModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">RAO-2023 104(1.0) Accidental Hazards</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-4">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-risk-status me-1 "></i>Risk Status
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Open
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-department-name me-1"></i>IT Service Name
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Continuity vault
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Inherent Probability

                                        </td>
                                        <td>:</td>
                                        <td>
                                            Very Low(1)
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Residual Probability
                                        </td>
                                        <td>:</td>
                                        <td>
                                            N/A
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-4">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-owner me-1"></i>Risk Entry Date
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Open
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-profile me-1"></i>Risk Owner
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Continuity vault
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Inherent Impact
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Minor (1)

                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Residual Impact
                                        </td>
                                        <td>:</td>
                                        <td>
                                            N/A
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-4">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Threat
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Man made disasters
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-department-name me-1"></i>Next Review Date
                                        </td>
                                        <td>:</td>
                                        <td>
                                            25/02/2023
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Inherent Risk Rating
                                        </td>
                                        <td>:</td>
                                        <td>
                                            Residual(1)
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">
                                            <i class="cv-process-name me-1"></i>Residual Risk Rating
                                        </td>
                                        <td>:</td>
                                        <td>
                                            N/A
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <!--End Configuration Modal -->

</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<!-- <script src="~/js/riskheatmap.js"></script> -->
