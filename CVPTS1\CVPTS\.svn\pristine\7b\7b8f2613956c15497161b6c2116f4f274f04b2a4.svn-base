﻿@model BCM.BusinessClasses.Equipments
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    string formAction = (Model != null && Model.ID > 0) ? "UpdateEquipment" : "AddEquipment";
}

<form asp-action="@formAction" asp-controller="WorkAreaRecovery" method="post" id="addEquipmentForm" class="needs-validation progressive-validation" novalidate>
    <div class="form-group">
        <input type="hidden" name="ID" id="ID" value="@(Model?.ID ?? 0)" />
        <label for="equipmentName" class="form-label">Equipment Name</label>
        <div class="input-group">
            <span class="input-group-text">
                <i class="cv-name"></i>
            </span>
            @if (Model != null)
            {
                <textarea class="form-control" name="EquipmentName" id="equipmentName" asp-for="EquipmentName" placeholder="Enter Equipment Name" required></textarea>
            }
            else
            {
                <textarea class="form-control" name="EquipmentName" id="equipmentName" placeholder="Enter Equipment Name" required></textarea>
            }

            @if (Model != null && Model.ID > 0)
            {
                <span type="button" class="btn-action btnDelete1" data-id="@Model.ID" data-bs-toggle="modal" data-bs-target="#DeleteModalForEquipmant">
                    <i class="cv-delete text-danger" title="Delete"></i>
                </span>
            }
        </div>
        <div class="invalid-feedback">Enter Equipment Name</div>
        <div class="modal-footer justify-content-center">           
            @if (Model != null && Model.ID > 0)
            {
                <button type="submit" class="btn btn-primary btn-sm">Update</button>
            }
            else
            {
                <button type="submit" class="btn btn-primary btn-sm">Create</button>
            }
            <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
        </div>
    </div>
</form>




<script>
    $(document).ready(function () {

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for addEquipmentForm");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('addEquipmentForm');
                if (!form) {
                    console.error("Form not found with ID: addEquipmentForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Add required field indicators (asterisks)
                window.BCMValidation.addRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    if (!isValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });

                // Add user interaction validation for required fields
                const requiredInputs = form.querySelectorAll('[required]');
                requiredInputs.forEach(function(input) {
                    // Add input event listener for real-time validation
                    input.addEventListener('input', function() {
                        if (window.BCMValidation) {
                            window.BCMValidation.validateInput(this);
                        }
                    });

                    // Add blur event listener for validation when field loses focus
                    input.addEventListener('blur', function() {
                        if (window.BCMValidation) {
                            window.BCMValidation.validateInput(this);
                        }
                    });
                });

            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        // Form submission success handler
        $('#addEquipmentForm').on('submit', function(e) {
            // Let the form submit naturally after validation passes
            // The validation is already handled by the BCM validation framework above
        });
    });
</script>
