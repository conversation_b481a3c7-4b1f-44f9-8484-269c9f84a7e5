﻿@model BCM.BusinessClasses.ReviewReportMomItem
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using BCM.Shared
@{
    var iReviewTypeID = ViewBag.ReviewTypeID;
    // var isEdit = Model != null && Model.MomID > 0;
    // var formTitle = isEdit ? "Edit MOM Item" : "Add MOM Item";
    // var actionName = isEdit ? "EditMomItem" : "AddMomItem";
    // var submitButtonText = isEdit ? "Update" : "Save";
}

@* <form id="momItemForm" asp-action="@actionName" asp-controller="ReviewReportMasterForm" method="post" class="needs-validation progressive-validation" novalidate> *@
<form id="momItemForm" asp-action="AddMomItem" asp-controller="ReviewReportMasterForm" method="post" class="needs-validation progressive-validation" novalidate>
    @* @if (isEdit)
    {
        <input type="hidden" asp-for="MomID" />
    } *@
    <div class="row">
        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group">
                <label class="form-label" for="ReviewTypeID">Review Type</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-review-type"></i></span>
                    <input type="hidden" value="@ViewBag.ReportID" asp-for="ReportID" />
                    @if (iReviewTypeID > 0 )
                    {
                        <select class="form-select form-select-sm" id="ReviewTypeID" asp-for="ReviewTypeID" asp-items="@(new SelectList(ViewBag.ReviewType, "ReviewTypeID", "ReviewType", Model?.ReviewTypeID ?? iReviewTypeID))" required disabled>
                        </select>
                        <input type="hidden" name="ReviewTypeID" value="@(Model?.ReviewTypeID ?? iReviewTypeID)" />
                    }
                    else
                    {
                        <select class="form-select form-select-sm" id="ReviewTypeID" asp-for="ReviewTypeID" asp-items="@(new SelectList(ViewBag.ReviewType, "ReviewTypeID", "ReviewType"))" required>
                            <option selected disabled value="">-- Select ReviewType --</option>
                        </select>
                    }
                </div>
                <div class="invalid-feedback">Select Review Type</div>
            </div>
            <div class="form-group">
                <label class="form-label" for="MomItem">MOM Item</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-name"></i></span>
                    <input type="text" class="form-control" id="MomItem" asp-for="MomItem" required />
                </div>
                <div class="invalid-feedback">Enter MOM Item</div>
            </div>
            <div class="form-group">
                <label class="form-label" for="ReviewDate">Review Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-calendar"></i></span>
                    <input type="date" class="form-control" id="ReviewDate" asp-for="ReviewDate" required />
                </div>
                <div class="invalid-feedback">Select Review Date</div>
            </div>
        </div>

        <div class="col-md-6 col-lg-6 col-xl-6">
            <div class="form-group">
                <label class="form-label" for="ddlResource">Owner</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-owner"></i></span>                    
                    <select class="form-select form-control" autocomplete="off" id="ddlResource" asp-for="OwnerID" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ResourceList, "ResourceId", "ResourceName"))" required>
                        <option selected disabled value="">-- Select Owner --</option>
                    </select>
                </div>
                <div class="invalid-feedback">Select Owner</div>
            </div>
            <div class="form-group">
                <label class="form-label">Description</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <textarea class="form-control" style="height:0px" asp-for="Description"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label" for="PlanStartDate">Closing Date</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-calendar"></i></span>
                    <input type="date" class="form-control" id="ClosedDate" asp-for="ClosedDate" required />
                </div>
                <div class="invalid-feedback">Select Close Date</div>
            </div>
        </div>

        <div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" id="isClosed" asp-for="IsClosed" name="IsClosed">
                <label class="form-check-label" for="isClosed">
                    Closed ?
                </label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input isActionItem" type="checkbox" id="isActionItem" asp-for="IsActionItem" name="IsActionItem">
                <label class="form-check-label" for="isActionItem">
                    Action Item ?
                </label>
            </div>
        </div>

        <div class="row hiddenSection" style="display:none">
            <div class="col-md-6 col-lg-6 col-xl-6">
                <div class="form-group">
                    <label class="form-label" for="BCMEntityType">BCM Entity Type</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-entity-type"></i></span>
                        <select class="form-select form-select-sm" id="ddlBCMEntityType" asp-for="BCMEntityType">
                            @* asp-items="@(new SelectList(ViewBag.BCMEntities, "BCMEntityID", "BCMEntityName"))" *@
                            <option selected disabled value="0">-- Select Entity Type --</option>
                            @{
                                foreach (var item in ViewBag.BCMEntities)
                                {
                                    <option value="@item.Value">@item.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="ActionItem">Action Item</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-action"></i></span>
                        <input type="text" class="form-control" id="ActionItem" asp-for="ActionItem" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="PlanStartDate">Plan Start Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                        <input type="date" class="form-control" id="PlanStartDate" asp-for="PlanStartDate" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="ActualStartDate">Actual Start Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                        <input type="date" class="form-control" id="ActualStartDate" asp-for="ActualStartDate" />
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-6 col-xl-6">
                <div class="form-group">
                    <label class="form-label" for="BCMEntityName">BCM Entity Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-entity-name"></i></span>
                        @* class="form-select form-select-sm" *@
                        <select class="form-control clsddlBCMEntity" id="ddlBCMEntity" asp-for="BCMEntityID" name="BCMEntityID">
                            <option selected disabled value="0">-- Select Entity Name --</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="ActionOwner">Action Owner</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-owner"></i></span>
                        <select class="form-select form-select-sm" id="ActionOwner" asp-for="ActionOwnerID" asp-items="@(new SelectList(ViewBag.ResourceList, "ResourceId", "ResourceName"))">
                            <option selected disabled value="">-- Select Action Owner --</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="PlanEndDate">Plan End Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                        <input type="date" class="form-control" id="PlanEndDate" asp-for="PlanEndDate" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="ActualEndDate">Actual End Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar"></i></span>
                        <input type="date" class="form-control" id="ActualEndDate" asp-for="ActualEndDate" />
                    </div>
                </div>

                @*@<div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" id="IsImprovement" asp-for="IsImprovement">
                    <label class="form-check-label" for="IsImprovement">
                        Is Improvement
                    </label>
                </div>*@
            </div>
        </div>
        
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            @* <button type="submit" class="btn btn-primary btn-sm" id="btnSave">@submitButtonText</button> *@
            <button type="submit" class="btn btn-primary btn-sm" id="btnSave">Save</button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function () {
        $('.isActionItem').on('change', function () {
            if ($(this).is(':checked')) {
                $('.hiddenSection').show();
            } else {
                $('.hiddenSection').hide();
            }
        });
    });
</script>
<script>
    $(document).ready(function(){
        $(document).on('change', '#ddlBCMEntityType', function () {
            var iEntityType = $(this).val();
            $.ajax({
                url: '@Url.Action("BindEntity", "ReviewReportMasterForm")',
                type: 'GET',
                data: { iEntityType: iEntityType },
                success:function(data){
                    console.log(data);
                    var ddlEntites = $('.clsddlBCMEntity');
                    ddlEntites.empty();
                    ddlEntites.append('<option selected disabled value="0">-- Select Entity Name --</option>');
                    $.each(data,function(index,item){
                        ddlEntites.append('<option value="'+ item.value +'">'+ item.text +'</option>');
                    });
                },
                error: function (xhr, status, error) {
                    console.log(error);
                    console.error(xhr.status);
                    console.error(xhr.responseText);
                }
            })
        })
    })
</script>

<script>
    $(document).ready(function () {
        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for AddMOM form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('momItemForm');
                if (!form) {
                    console.error("Form not found with ID: momItemForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                feedbackElement.style.display = 'block';
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Custom function to add required field indicators without duplicates
                function addCustomRequiredFieldIndicators(form) {
                    const requiredInputs = form.querySelectorAll('[required]');

                    requiredInputs.forEach(function(input) {
                        const label = form.querySelector('label[for="' + input.id + '"]');
                        if (label) {
                            // Check if asterisk already exists
                            if (!label.querySelector('.required-asterisk')) {
                                // Create asterisk element
                                const asterisk = document.createElement('span');
                                asterisk.className = 'required-asterisk text-danger';
                                asterisk.textContent = ' *';
                                asterisk.style.fontWeight = 'bold';

                                // Append asterisk to label
                                label.appendChild(asterisk);
                                console.log("Added asterisk to label for:", input.id);
                            }
                        }
                    });
                }

                // Use custom function instead of the default one
                addCustomRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    if (!isValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }
    });
</script>
