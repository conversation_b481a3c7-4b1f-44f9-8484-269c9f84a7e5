﻿let modifiedArray = [], originalTableData = [], originalCheckboxStates = [];
const tableAccessURL = {
    tableList: "BCMAdministration/TableAccess/GetTableAccessData",
    createOrUpdate: "BCMAdministration/TableAccess/UpdateTableAccess"
};

$(function () {
    loadTableAccessData();

    setTimeout(initializeOriginalData, 500);
    // --- Events ---

    $('#searchInput').on('input', function () {
            const query = $(this).val().toLowerCase().trim();
            renderTableCards(query === ''
                ? originalTableData
                : originalTableData.filter(item => item.tableName.toLowerCase().startsWith(query))
            );
        })
        .on('keypress', function (e) {
            if (e.key === '=' || e.key === 'Enter') return false;
        });

    $('#selectAllSwitch').on('change', function () {
        const checked = this.checked;
        $('.table-switch:visible').prop('checked', checked);
        modifiedArray = [];
        $('.table-switch').each(function () {
            modifiedArray.push({
                ID: +$(this).data('id'),
                IsChecked: this.checked
            });
        });
        enableSaveButtons();
        updateSelectAllState();
    });

    $(document).on('change', '.table-switch', function () {
        enableSaveButtons();
        const id = +$(this).data('id');
        const checked = this.checked;
        const idx = modifiedArray.findIndex(d => d.ID === id);
        if (idx !== -1) modifiedArray[idx].IsChecked = checked;
        else modifiedArray.push({ ID: id, IsChecked: checked });
        updateSelectAllState();
    });

    $('#tableAccessSaveBtn').on('click', saveTableAccess);

   // --- Functions ---
    function loadTableAccessData() {
        $.getJSON("/" + tableAccessURL.tableList, function (response) {
            if (response && response.success && Array.isArray(response.data)) {
                originalTableData = response.data;
                renderTableCards(originalTableData);
            } else {
                originalTableData = [];
                renderTableCards([]);
            }
            setTimeout(updateSelectAllState, 200);
        }).fail(function (xhr) {
            originalTableData = [];
            renderTableCards([]);
            setTimeout(updateSelectAllState, 200);
        });
    }
    function initializeOriginalData() {
        originalCheckboxStates = [];
        modifiedArray = [];
        $('.table-switch').each(function () {
            originalCheckboxStates.push({
                ID: +$(this).data('id'),
                IsChecked: this.checked
            });
        });
        updateSelectAllState();
    }
    function renderTableCards(data) {
        let html = '';
        if (!Array.isArray(data) || data.length === 0) {
            $('#table-access').empty();
            $('#noDataMessage').show();
            $('.card_ScrollBody').hide();
            return;
        }
        $('#noDataMessage').hide();
        $('.card_ScrollBody').show();
        (data.slice().sort((a, b) => a.tableName.localeCompare(b.tableName))).forEach(item => {
            const displayName = item.tableName.length > 31
                ? item.tableName.substring(0, 29) + '...'
                : item.tableName;
            html += `<div class="col d-grid">
                <div class="d-flex tabel-access-card">
                    <div class="custom-control-label w-100 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                        <div class="col-8 d-inline-flex">
                            <span class="fs-6 d-flex align-items-center" title="${item.tableName}">
                                <i class="cv-calculated me-1 fs-5"></i>${displayName}
                            </span>
                        </div>
                        <div class="col-4 d-flex justify-content-end align-items-center">
                            <div class="form-check form-switch" style="min-height:0px">
                                <input class="form-check-input table-switch" type="checkbox" role="switch"
                                    data-id="${item.id}" ${item.isChecked ? 'checked' : ''} />
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;
        });
        $('#table-access').html(html);
    }
    function updateSelectAllState() {
        const total = $('.table-switch:visible').length,
            checked = $('.table-switch:visible:checked').length;
        $('#selectAllSwitch')
            .prop('indeterminate', checked > 0 && checked < total)
            .prop('checked', checked && checked === total).css('background-color', '');
    }
    function enableSaveButtons() {
        $('#tableAccessSaveBtn').removeClass('disabled');
    }
    function saveTableAccess() {
        const currentStates = [];
        $('.table-switch').each(function () {
            currentStates.push({ ID: +$(this).data('id'), IsChecked: this.checked });
        });

        const changedItems = currentStates.filter(cur => {
            const orig = originalCheckboxStates.find(o => o.ID === cur.ID);
            return orig && orig.IsChecked !== cur.IsChecked;
        });

        if (!changedItems.length) {
            showNotification('No changes to save', 'info');
            return;
        }

        $('#tableAccessSaveBtn').prop('disabled', true).text('Saving...');

        $.ajax({
            type: "POST",
            url: "/" + tableAccessURL.createOrUpdate,
            data: JSON.stringify(changedItems),
            contentType: 'application/json',
            dataType: "json"
        }).done(function (response) {
            if (response?.success) {
                if (response.warnings?.length) {
                    response.warnings.forEach(w => showNotification(w, 'warning'));
                }

                showNotification('✅ Table access updated successfully', 'success');
                location.reload();
            } else {
                showNotification((response?.message || 'Error updating table access'), 'warning');
                location.reload();
            }
        }).fail(function () {
            showNotification('❌ Failed to update table access', 'warning');
        }).always(function () {
            $('#tableAccessSaveBtn').prop('disabled', false).text('Save');
        });
    }

    function showNotification(message, type) {
        if (typeof notificationAlert === 'function') {
            notificationAlert(type, message);
        } else {
            alert((type === 'success' ? '✅ ' : type === 'error' ? '❌ ' : '') + message);
        }
    }
});