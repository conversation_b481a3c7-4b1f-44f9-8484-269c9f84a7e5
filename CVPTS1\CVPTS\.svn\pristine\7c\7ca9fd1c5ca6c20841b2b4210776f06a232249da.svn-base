﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Security.Helper;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Controllers;

public class ChangePasswordController : BaseController
{
    private readonly ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;

    public ChangePasswordController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult ChangePassword()
    {
        return View();
    }

    [HttpPost]
    public IActionResult ResetPassword(string OldPassword, string NewPassword, string ConfirmPassword)
    {
        bool bSuccess;
        string UserNameVal;
        string userID;
        string OrgID;
        string OldPasswordDecrypt = string.Empty;

        try
        {
            // Check if user details are available
            if (_UserDetails == null || _UserDetails.UserID <= 0)
            {
                TempData["ErrorMessage"] = "User session has expired. Please login again.";
                return RedirectToAction("Login", "Login");
            }
            else
            {
                // Get user ID and organization ID
                userID = _UserDetails.UserID.ToString();
                OrgID = _UserDetails.OrgID.ToString();
            }


            // Validate inputs
            if (string.IsNullOrEmpty(OldPassword) || string.IsNullOrEmpty(NewPassword) || string.IsNullOrEmpty(ConfirmPassword))
            {
                TempData["ErrorMessage"] = "Please Enter Password !!!";
                return RedirectToAction("ChangePassword");
            }

            // Get user details using old password
            var oDetails = _ProcessSrv.GetOldPassword(OldPassword, Convert.ToInt32(userID));
            if (oDetails == null)
            {
                TempData["ErrorMessage"] = "Invalid user or password";
                return RedirectToAction("ChangePassword");
            }
            else
            {
                UserNameVal = oDetails.LoginName ?? string.Empty;
                OldPasswordDecrypt = CryptographyHelper.Decrypt(oDetails.Password);
            }



            // Check if old password is correct
            if (OldPassword != OldPasswordDecrypt)
            {
                TempData["ErrorMessage"] = "Incorrect Old Password !!!";

                // Handle invalid attempts logic
                try
                {
                    string countVal = "0";
                    string tempAttempts = "0";

                    if (!string.IsNullOrEmpty(UserNameVal))
                    {
                        ManageUsersDetails objDetailsNew = _ProcessSrv.GetManageUserByLoginNameColl(UserNameVal);

                        if (objDetailsNew != null)
                        {
                            if (!string.IsNullOrEmpty(objDetailsNew.InValidAttempts))
                            {
                                tempAttempts = objDetailsNew.InValidAttempts;
                            }

                            VaultSettings objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt32(objDetailsNew.OrgID));
                            if (objVaultSettings != null)
                            {
                                countVal = objVaultSettings.WrongLoginAttempts ?? "5";
                            }

                            var objManageUsers = new ManageUsersDetails
                            {
                                UserID = objDetailsNew.UserID,
                                InValidAttempts = tempAttempts
                            };

                            bool success1 = _ProcessSrv.UpdateWrongPswdAttemptsCount(objManageUsers);

                            objDetailsNew = _ProcessSrv.GetManageUserByLoginNameColl(UserNameVal);

                            if (Convert.ToInt32(tempAttempts) > Convert.ToInt32(countVal))
                            {
                                var objManageUserDetails = new ManageUsersDetails
                                {
                                    UserID = oDetails.UserID,
                                    InValidAttempts = "0"
                                };

                                bool success = _ProcessSrv.UpdateUsersDetailsForWrongPswdAttempts(objManageUserDetails);

                                if (success)
                                {
                                    TempData["ErrorMessage"] = "User Account is Disabled, Please Contact Application Admin";
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _CVLogger.LogErrorApp(ex);
                }

                return RedirectToAction("ChangePassword");
            }

            // Check if new password and confirm password match
            if (NewPassword != ConfirmPassword)
            {
                TempData["ErrorMessage"] = "New Password and Confirm Password Does Not Match !!!";
                return RedirectToAction("ChangePassword");
            }

            // Check if new password is same as old password
            if (OldPassword == NewPassword)
            {
                TempData["ErrorMessage"] = "New Password cannot be same as Old !!!";
                return RedirectToAction("ChangePassword");
            }

            // Check if user details are available
            if (_UserDetails == null || _UserDetails.UserID <= 0)
            {
                TempData["ErrorMessage"] = "User session has expired. Please login again.";
                return RedirectToAction("Login", "Login");
            }

            // Check password history
            try
            {
                List<ResourcesInfo> resourceList = _ProcessSrv.GetResourceInfoByUserIDAndOldPassword(OldPassword, Convert.ToInt32(userID));
                foreach (ResourcesInfo resource in resourceList)
                {
                    OrgID = resource.OrgID.ToString();
                }

                string countVal = "0";
                VaultSettings objVaultSettings = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt32(OrgID));
                if (objVaultSettings != null)
                {
                    countVal = objVaultSettings.PasswordHistory ?? "0";
                }

                var passwordHistoryList = _ProcessSrv.GetPasswordHistoryListByUserIDAndOrgID(Convert.ToInt32(OrgID), Convert.ToInt32(userID), Convert.ToInt32(countVal));

                foreach (PasswordHistory passwordHistory in passwordHistoryList)
                {
                    if (Convert.ToInt32(countVal) > 0)
                    {
                        if (passwordHistory.Password == NewPassword)
                        {
                            TempData["ErrorMessage"] = "New password can not be same as your last " + countVal + " password(s)";
                            return RedirectToAction("ChangePassword");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _CVLogger.LogErrorApp(ex);
            }

            // Check if password contains username
            if (NewPassword.Contains(UserNameVal))
            {
                TempData["ErrorMessage"] = "User Name and Password can not be similar";
                return RedirectToAction("ChangePassword");
            }

            // Call the service to reset the password
            bSuccess = _ProcessSrv.ManageUserResetPasswordByOLDPassword(OldPassword, NewPassword, userID, DateTime.Now);

            if (bSuccess)
            {
                // Save password history
                PasswordHistorySave(userID, OrgID, NewPassword);

                // Log the successful password change
                _CVLogger.LogInfo("Password changed successfully for user ID: " + userID);

                // Clear any existing sessions
                HttpContext.Session.Clear();

                // Set success message and stay on the ChangePassword page
                TempData["SuccessMessage"] = "Password Changed Successfully. Please login with your new password.";
                TempData["RedirectToLogin"] = "true"; // Flag to indicate redirect should happen
                return RedirectToAction("ChangePassword");
            }
            else
            {
                TempData["ErrorMessage"] = "Password must contain: Minimum 8 and Maximum 12 characters atleast 1 UpperCase Alphabet, 1 LowerCase Alphabet, 1 Number and 1 Special Character in $@!%*?&";
                return RedirectToAction("ChangePassword");
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            TempData["ErrorMessage"] = "An error occurred while changing the password. Please try again.";
            return RedirectToAction("ChangePassword");
        }
    }

    public void PasswordHistorySave(string userID, string orgID, string NewPassword)
    {
        try
        {
            var objPasswordHistory = new PasswordHistory
            {
                UserID = userID,
                Password = NewPassword,
                OrgID = orgID
            };

            var result = _ProcessSrv.PasswordHistorySave(objPasswordHistory);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
}
