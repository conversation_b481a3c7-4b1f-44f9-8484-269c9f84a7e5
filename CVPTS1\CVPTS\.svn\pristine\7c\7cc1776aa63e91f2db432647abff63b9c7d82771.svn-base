﻿using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc;
using BCM.BusinessClasses;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Text.Json.Nodes;
using BCM.UI.Controllers;



namespace BCM.UI.Areas.OrgStructure.Controllers;
[Area("BCMOrgStructure")]
public class OrgGroupController : BaseController
{
    private ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    public OrgGroupController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult GetOrgGroupByID(int iOrgGroupID)
    {
        try
        {
            List<OrgGroup> lstOrgGroup;
            lstOrgGroup = _ProcessSrv.GetOrgGroupList();
            if (iOrgGroupID > 0)
            {
                lstOrgGroup = lstOrgGroup.Where(x=>x.OrgGroupID == iOrgGroupID).ToList();
                if(lstOrgGroup == null || !lstOrgGroup.Any())
                {
                    return NotFound("No any record");
                }
            }
            return PartialView("_FilterTableRecords", lstOrgGroup);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageOrgGroup");
    }
    
    public IActionResult ManageOrgGroup()
    {
        List<OrgGroup> lstOrgGroup = new List<OrgGroup>();
        try
        {
            lstOrgGroup = _ProcessSrv.GetOrgGroupList();
            ViewBag.OrgGroup = new SelectList(lstOrgGroup, "OrgGroupID", "OrganizationGroupName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstOrgGroup);
    }

    [HttpGet]
    public IActionResult AddOrgGroup()
    { 
        try
        {
            return PartialView("_AddOrgGroup", new OrgGroup());
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageOrgGroup");
    }

    [HttpPost]
    public IActionResult AddOrgGroup(OrgGroup lstOrgGroup)
    {
        bool bSuccess = false;
        try
        {
            var jsonResult = CheckIsExist(lstOrgGroup.OrganizationGroupName, lstOrgGroup.OrganizationGroupCode);

            // Extract the actual data from the JsonResult
            var jsonData = jsonResult.Value as dynamic;

            // Check if either name or code exists
            if (jsonData.nameExists == true || jsonData.codeExists == true)
            {
                return RedirectToAction("ManageOrgGroup");
            }
            bSuccess = _ProcessSrv.OrgGroupSave(lstOrgGroup);
        }

        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? lstOrgGroup.OrganizationGroupName + " Added Successfully" : "Failed to update Organization Group." });
        }

        return RedirectToAction("ManageOrgGroup");
    }

    [HttpGet]
    public IActionResult EditOrgGroup(int iID)
    {
        var objOrgGroup = new OrgGroup();
        try
        {
            objOrgGroup = _ProcessSrv.GetOrgGroupByOrgGroupID(iID);
            return PartialView("_EditOrgGroup", objOrgGroup);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageOrgGroup");
    }

    [HttpPost]
    public IActionResult EditOrgGroup(OrgGroup OrgGroup)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.OrgGroupUpdate(OrgGroup);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? OrgGroup.OrganizationGroupName + " Updated Successfully" : "Failed to update Organization Group." });
        }
        return RedirectToAction("ManageOrgGroup");
    }

    [HttpGet]
    public IActionResult DeleteOrgGroup(int iID)
    {
        var objOrgGroup = new OrgGroup();
        try
        {
            objOrgGroup = _ProcessSrv.GetOrgGroupByOrgGroupID(iID);
            return PartialView("_DeleteOrgGroup", objOrgGroup);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageOrgGroup");                
    }

    [HttpPost]
    public IActionResult DeleteOrgGroup(OrgGroup OrgGroup)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.OrgGroupDelete(OrgGroup.OrgGroupID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? OrgGroup.OrganizationGroupName + " Deleted Successfully" : "Failed to Delete." });
        }
        return RedirectToAction("ManageOrgGroup");
    }

    [HttpGet]
    public IActionResult CheckOrgGroupExists(string orgName, string orgCode)
    {
        try
        {
            //int Id = 0;
            //int oName = 0; 
            //int oCode = 0;
            
            //// Check if organization name or code exists
            //var orgDetails = _ProcessSrv.IsExistByOrgNameAndGroupCode(
            //    orgName?.ToLower() ?? string.Empty, 
            //    orgCode?.ToLower() ?? string.Empty, 
            //    Id, ref oCode, ref oName);
            
            //return Json(new { 
            //    nameExists = oName > 0,
            //    codeExists = oCode > 0
            //});

            return CheckIsExist(orgName, orgCode);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { 
                nameExists = false, 
                codeExists = false, 
                error = ex.Message 
            });
        }
    }

    public JsonResult CheckIsExist(string orgName, string orgCode)
    {
        try
        {
            int Id = 0;
            int oName = 0;
            int oCode = 0;

            // Check if organization name or code exists
            var orgDetails = _ProcessSrv.IsExistByOrgNameAndGroupCode(
                orgName?.ToLower() ?? string.Empty,
                orgCode?.ToLower() ?? string.Empty,
                Id, ref oCode, ref oName);

            return Json(new
            {
                nameExists = oName > 0,
                codeExists = oCode > 0
            });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new
            {
                nameExists = false,
                codeExists = false,
                error = ex.Message
            });
        }
    }
}
