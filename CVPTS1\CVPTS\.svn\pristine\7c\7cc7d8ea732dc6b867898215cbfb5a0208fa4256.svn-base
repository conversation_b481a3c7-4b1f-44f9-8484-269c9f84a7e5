﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;

using BCM.Shared;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Data.SqlClient;
using System.Diagnostics;
using Ubiety.Dns.Core;
using static BCM.Shared.BCPEnum;
using static System.Collections.Specialized.BitVector32;

namespace BCM.UI.Areas.BCMProcessBIAForms.Controllers;
[Area("BCMProcessBIAForms")]
public class BIAVitalRecordsController : Controller
{

    private ManageUsersDetails _UserDetails = new ManageUsersDetails();
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    int iBIAID = 0;
    int iProcessID = 0;
    int iSectionID = 0;
    int iIsBCMEntity = 0;

    public BIAVitalRecordsController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _UserDetails = _Utilities.LoginUserDetails();
        _CVLogger = CVLogger;

        if (_UserDetails == null)
        {
            RedirectToAction("Login", "Login");
        }
    }

    public void Cancel()
    {

    }

    public JsonResult GetVitalRecordByID(int? id)
    {

        try
        {

            HttpContext.Session.SetString("VitalRecordId", id.Value.ToString());
            var vitalRecord = _ProcessSrv.BIAVitalRecordGetByID(id.Value);
            if (vitalRecord != null)
            {
                if (vitalRecord.Impact == "Electronic" || vitalRecord.TypeOfRecord == "Static")
                {
                    vitalRecord.Impact = "1";
                    vitalRecord.TypeOfRecord = "1";
                }
                else if (vitalRecord.Impact == "Paper Based" || vitalRecord.TypeOfRecord == "Dynamic")
                {
                    vitalRecord.Impact = "2";
                    vitalRecord.TypeOfRecord = "2";
                }
                else
                {
                    vitalRecord.Impact = "0";
                    vitalRecord.TypeOfRecord = "0";
                }

                return Json(vitalRecord);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }              
        return Json(null);
    }

    [HttpGet]
    public IActionResult DeleteBIAVitalRecord(int id)
    {
        
        BIAVitalRecord objBIAVitalRecord = new BIAVitalRecord();
        try
        {

            objBIAVitalRecord = _ProcessSrv.BIAVitalRecordGetByID(id);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteBIAVitalRecord", objBIAVitalRecord);
    }


    [HttpPost]
    public IActionResult DeleteBIAVitalRecord(BIAVitalRecord objBIAVitalRecord)
    {
        bool bSuccess = false;
        try
        {           
            bSuccess = _ProcessSrv.ProcessBIAVitalRecordInfoDeleteById(objBIAVitalRecord.VitalRecordId, _UserDetails.UserID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objBIAVitalRecord.VitalRecordName + " Deleted Successfully" : "Failed To Delete." });
        }
        return RedirectToAction("BIAVitalRecords", new { strSectionID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID").ToString()), strProcessID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID").ToString()), strIsBCMEntity = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity").ToString()), strBIAID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("BIAID"))});
    }


    public IActionResult BIAVitalRecords(string strSectionID, string strProcessID, string strIsBCMEntity, string strBIAID)
    {
        try
        {
            // _Utilities.PopulateFacilitiesByUnitIdOrgid(Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(_UserDetails.UnitID), _UserDetails.OrgGroupID.ToString());
            iBIAID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strBIAID));
            iProcessID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strProcessID));
            iSectionID = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strSectionID));
            iIsBCMEntity = Convert.ToInt32(BCM.Security.Helper.CryptographyHelper.Decrypt(strIsBCMEntity));

            HttpContext.Session.SetString("BIAID", iBIAID.ToString());
            HttpContext.Session.SetString("SectionID", iSectionID.ToString());
            HttpContext.Session.SetString("IsBCMEntity", iIsBCMEntity.ToString());
            HttpContext.Session.SetString("ProcessID", iProcessID.ToString());

            List<BIAVitalRecord> lstBIAVitalRecord = _ProcessSrv.BIAVitalRecordGetAllByBIAID(iBIAID);
           // ViewBag.GeridData = lstBIAVitalRecord;
            ViewBag.Questions = _ProcessSrv.GetBIASurveyQuestionListBySectionID(iSectionID);
            ViewBag.RecordOwnerNames = _ProcessSrv.GetBIAVitalRecordOwnerNameByOrgId(_UserDetails.OrgID);
            ViewBag.BIASectionVersion = _ProcessSrv.GetProcessBIASectionByBIAID(iBIAID);
            ViewBag.TypeOfRecord = GetTypeOfRecords();
            ViewBag.Impact = GetImpact();
            BIASection objBIASection = _ProcessSrv.GetBIASurveySectionById(iSectionID, _UserDetails.OrgID);
            ViewBag.Description = objBIASection.SectionDescription;


            if (ViewBag.BIASectionVersion.Version == null)
            {
                ViewBag.BIASectionVersion.Version = "1.0";
            }
            return View(lstBIAVitalRecord);

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
    }   

    [HttpPost]
    public IActionResult AddUpdateVitalRecords(BIAVitalRecord objBIAVitalRecord)
    {        
        bool bSuccess = false;
        string strError = string.Empty;
        try
        {
            TempData["Version"] = objBIAVitalRecord.Version;
            
            if (objBIAVitalRecord.RecordOwnerID <= 0 ||
                string.IsNullOrEmpty(objBIAVitalRecord.Description) ||
                string.IsNullOrEmpty(objBIAVitalRecord.TypeOfRecord) ||
                string.IsNullOrEmpty(objBIAVitalRecord.Facility) ||
                string.IsNullOrEmpty(objBIAVitalRecord.SecondaryStorageLocation) ||
                string.IsNullOrEmpty(objBIAVitalRecord.Impact))
            {
                objBIAVitalRecord.IsComplete = 0;
            }
            else
            {
                objBIAVitalRecord.IsComplete = 1;
                            
            }
                      
            objBIAVitalRecord.Impact = (objBIAVitalRecord.Impact == "1") ? "Electronic" : "Paper Based";
            objBIAVitalRecord.TypeOfRecord = (objBIAVitalRecord.TypeOfRecord == "1") ? "Static" : "Dynamic";
            objBIAVitalRecord.CreatedBy = Convert.ToInt32(_UserDetails.UserID);
            objBIAVitalRecord.ChangedBy = Convert.ToInt32(_UserDetails.UserID);
            objBIAVitalRecord.Site = string.Empty;
            objBIAVitalRecord.BIAID = GetCurrentBIAID();
            objBIAVitalRecord.IsBackUp = "No";



            bSuccess = _ProcessSrv.BIAVitalRecordSaveAndUpdate(objBIAVitalRecord);

            if (bSuccess == true)
            {
                UpdateBusinessProcessBIAByBIAID();                
            }
            else
            {                
                ModelState.AddModelError("", "Failed to save the record.");
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);          
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            if (objBIAVitalRecord.VitalRecordId > 0)
            {
                return Json(new { success = bSuccess, message = bSuccess ? objBIAVitalRecord.VitalRecordName + " Updated Successfully" : " Failed To Update." });
            }
            else
            {
                return Json(new { success = bSuccess, message = bSuccess ? objBIAVitalRecord.VitalRecordName + " Added Successfully" : " Failed To Add." });
            }
        }
        //return RedirectToAction("BIAVitalRecords");
        return RedirectToAction("BIAVitalRecords", new { strSectionID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("SectionID").ToString()), strProcessID = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("ProcessID").ToString()), strIsBCMEntity = @BCM.Security.Helper.CryptographyHelper.Encrypt(HttpContext.Session.GetString("IsBCMEntity").ToString()), strBIAID = @BCM.Security.Helper.CryptographyHelper.Encrypt(objBIAVitalRecord.BIAID.ToString()) });
    }
    
    private int GetCurrentBIAID()
    {
        BIASection objBIASection = new BIASection();

        int iBIAIDForActivity = Convert.ToInt32(HttpContext.Session.GetString("BIAID"));
        try
        {
            if (iBIAIDForActivity == 0)
            {
                var Version = TempData.Peek("Version") as string;
                //objBIASection.Version = ViewBag.BIASectionVersion.Version;
                objBIASection.Version = Version;
                objBIASection.VersionChangeDescription = "";
                objBIASection.ApprovalStatus = ((int)BCPEnum.ApprovalType.Initiated).ToString();
                objBIASection.ProcessID = Convert.ToInt32(HttpContext.Session.GetString("ProcessID"));
                objBIASection.SectionID = Convert.ToInt32(HttpContext.Session.GetString("SectionID"));
                objBIASection.IsEffective = 1;
                objBIASection.CreatedBy = Convert.ToInt32(_UserDetails.UserID);
                objBIASection.ChangedBy = Convert.ToInt32(_UserDetails.UserID);
                objBIASection.IsBCMEntity = Convert.ToInt32(HttpContext.Session.GetString("IsBCMEntity"));

                iBIAIDForActivity = _ProcessSrv.ProcessBIASectionSave(objBIASection);

               
                HttpContext.Session.SetString("BIAID", iBIAIDForActivity.ToString());
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return iBIAIDForActivity;
    }

    protected int UpdateBusinessProcessBIAByBIAID()
    {
        string strBIAid = HttpContext.Session.GetString("BIAID");
        return _ProcessSrv.ProcessBIAUpdateByBIAID(Convert.ToInt32(strBIAid), Convert.ToInt32(_UserDetails.UserID));
    }


    private List<SelectListItem> GetTypeOfRecords()
    {
        try {
            var items = new List<SelectListItem>
            {
                // new SelectListItem { Text = "--Select--", Value = "0" }
            };

            foreach (int value in Enum.GetValues(typeof(TypeOfRecord)))
            {
                items.Add(new SelectListItem
                {
                    Text = Enum.GetName(typeof(TypeOfRecord), value),
                    Value = value.ToString()
                });
            }

            return items;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return (null);
    }

    private List<SelectListItem> GetImpact()
    {
        try
        {
            var items = new List<SelectListItem>
            {
                // new SelectListItem { Text = "--Select--", Value = "0" }
            };

            foreach (int value in Enum.GetValues(typeof(VitalReordImpact)))
            {
                items.Add(new SelectListItem
                {
                    Text = Enum.GetName(typeof(VitalReordImpact), value),
                    Value = value.ToString()
                });
            }

            return items;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return (null);
    }
}

