﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    
    Layout = "~/Views/Shared/_Layout.cshtml";
}



<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Gridstack CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/gridstack@9.2.0/dist/gridstack.min.css">

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- Custom CSS -->
<link rel="stylesheet" href="~/css/CustomDasboard.css">

<style>



    .empty-card-body.highlight-drop {
        border: 2px dashed #007bff;
        background-color: #f1f9ff;
    }

    .prebuildreport-leftsidebar {
        height: calc(100vh - 180px);
        overflow: auto;
    }

        .prebuildreport-leftsidebar ul {
            margin: 0px;
            list-style: none;
            flex-direction: column;
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
        }

            .prebuildreport-leftsidebar ul li {
                width: 100%;
                padding: 10px;
                cursor: pointer;
            }

                .prebuildreport-leftsidebar ul li.active, .prebuildreport-leftsidebar ul li:hover {
                    border-radius: 10px;
                    border: 1px solid #cc3178;
                }

                .prebuildreport-leftsidebar ul li div {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                    gap: 8px;
                }

    .disables {
        pointer-events: none;
        opacity: 0.5;
    }
</style>



<div id="dashboardListModal">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">
            Dashboard List
        </h6>
        <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
            <div class="input-group Search-Input">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control" placeholder="Search">
            </div>
           @*  <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal"
                    data-bs-target="#CreateModal">
                <i class="cv-Plus" title="Create New"></i>Create
            </button> *@
            <button type="button" id="dashboardListCreate" class="btn icon-btn btn-primary btn-sm">
                <i class="cv-Plus" title="Create New"></i>Create
            </button>
        </div>
    </div>

    <div class="Page-Condant card border-0 ">
        <table id="dashboardList" class="table table-hover" style="width:100%">
            <thead>
                <tr>
                    <th class="SrNo_th">#</th>
                    <th>Dashboard</th>
                    <th>Description</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody id="log-table-body"></tbody>
        </table>
        @* <table id="dashboardList" class="table table-hover" style="width:100%">
            <thead>
                <tr>
                    <th class="SrNo_th">#</th>
                    <th>Dashboard</th>
                    <th>Description</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody id="log-table-body">
            </tbody>
        </table> *@
    </div>
</div>

<div id="createModalDashboardList" class="d-none">
    <div class="p-2">
        <div class="row">
            <div class="col-2">
                <div class="card card-custom gutter-b">
                    <div class="card-body">
                        <div>
                            <div class="input-group">
                                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                                <input id="widghtSearch" type="text" class="form-control" placeholder="Search">
                            </div>
                        </div>
                        <div class="prebuildreport-leftsidebar">
                            <ul>
                              
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col">
                <!-- Navigation Bar -->
                <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="#">
                            <i class="fas fa-chart-line me-2"></i>Dashboard Builder
                        </a>

                        <div class="navbar-nav ms-auto align-items-center">
                            <!-- Back Button -->
                            <button type="button" class="btn btn-outline-light me-2" id="backBtn" title="Back">
                                <i class="fas cv-circle-left-linearrow"></i>
                            </button>

                            <!-- Save Button -->
                            <div class="btn-group me-3" role="group">
                                <button type="button" class="btn btn-outline-light" id="saveBtn" title="Save Dashboard" data-bs-toggle="modal" data-bs-target="#AddModal">
                                    <i class="fas cv-save"></i>
                                </button>
                            </div>

                            <!-- Preview Mode -->
                            <div class="btn-group me-3" role="group">
                                <button type="button" class="btn btn-outline-light" id="previewMode" title="Preview Dashboard">
                                    <i class="fas cv-view-feed"></i>
                                </button>
                            </div>
                            <!-- Add card -->
                            <div class="btn-group me-3" role="group">
                                <button type="button" class="btn btn-outline-light" id="addEmptyCardBtn" title="Add Empty Card">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
             
                        </div>


                    </div>
                </nav>

                <!-- Main Dashboard Container -->
                <div class="container-fluid p-4">
                    <div class="grid-stack" id="dashboard-grid">
                        <!-- Widgets will be dynamically added here -->
                    </div>
                </div>
       

            </div>
        </div>
    </div>


</div>


<div class="modal fade" id="AddModal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">Dashboard Configuration</h6>
                <button type="button" class="btn-close Closebtn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-1">
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Dashboard Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-calculated"></i></span>
                                <input id="dashboardName" type="text" class="form-control" placeholder="Dashboard Name">
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">User Mapping</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-user"></i></span>
                                <select class="form-select" id="userDropdown" name="userMapping">
                                    <option value="">Select User</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col">
                        <div class="form-group field-touched">
                            <label class="form-label">Description</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Description" name="Description" id="description">
                            </div>
                        </div>
                    </div>
                    
                   
                </div>

            </div>
            <div class="modal-footer">
                <button data-bs-dismiss="modal"  class="btn btn-secondary">Cancel</button>
                <button class="btn btn-primary" id="dashboardBuilderSaveBtn">Save</button>
            </div>
        </div>

    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Gridstack JS -->
<script src="https://cdn.jsdelivr.net/npm/gridstack@9.2.0/dist/gridstack-all.js"></script>

<!-- amCharts 5 -->
<script src="https://cdn.amcharts.com/lib/5/index.js"></script>
<script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
<script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
<script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
<script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>


<!-- Custom JavaScript -->
<script src="~/js/customdashboard/dashboardbuilder/widgets.js"></script>
<script src="~/js/customdashboard/widgetbuilder/widgetchart.js"></script>
<script src="~/js/customdashboard/dashboardbuilder/storage.js"></script>
@* <script src="~/js/customdashboard/dashboardbuilder/dashboard.js"></script> *@
<script src="~/js/customdashboard/dashboardbuilder/dashboardlist.js"></script>



