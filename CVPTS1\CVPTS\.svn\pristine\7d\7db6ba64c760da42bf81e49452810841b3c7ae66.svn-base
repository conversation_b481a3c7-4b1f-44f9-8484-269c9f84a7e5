﻿@model IEnumerable<BCM.BusinessClasses.ProcessUpDownbiadetails>
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.AspNetCore.Http
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor

@{
    ViewBag.Title = "BIA Upstream Business Process";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var ProcessName = HttpContextAccessor.HttpContext.Session.GetString("ProcessNameWithCode");
    var ProcessVersion = HttpContextAccessor.HttpContext.Session.GetString("ProcessVersion");
}

<div class="Page-Condant card border-0">
    <div class="Page-Header d-flex align-items-center justify-content-between">
        <p style="padding-left:1%" class="fw-bold mb-2">Configure Application UpStream Dependencies for @ProcessName</p>
        <div class="align-items-right" style="padding-right:2%">
            @* <p class="fw-semibold">Version : @ViewBag.BIASectionVersion.Version</p> *@
            <p class="fw-semibold">Version : @ProcessVersion</p>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-12">
                <div>                    
                    <div class="">
                      
                        <div>
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>UpStream Dependent Application</th>
                                        <th>Data Source</th>
                                        <th>RealTimeProcess</th>
                                        <th>Activity critical for BCM</th>
                                        <th>Area of Focus</th>
                                        <th>BCM Scope</th>
                                        <th>Supported</th>
                                        <th>RTO</th>
                                        <th>IsCritical</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model != null)
                                    {
                                        int iIndex = 0;
                                        foreach (var objQusetions in ViewBag.Questions)
                                        {
                                            
                                            <tr data-bs-toggle="collapse" data-bs-target="#collapseQuestion" aria-expanded="false" aria-controls="collapseQuestion" role="button">
                                                <td class="bg-secondary-subtle"><i class="cv-down-arrow ms-2"></i></td>
                                                <td class="bg-secondary-subtle" colspan="10">Question: @objQusetions.QuestionDetails</td>
                                            </tr>
                                            foreach (var item in Model)
                                            {
                                                if (objQusetions.ID == item.QuestionID)
                                                {
                                                    iIndex++;
                                                    <tr class="collapse" id="collapseQuestion">
                                                        <td>@iIndex</td>
                                                        <td>@item.ProcessName</td>
                                                        <td>@item.DataSource</td>
                                                        @{
                                                            var IsRealTime = item.IsRealTime == 1 ? "Yes" : "No";
                                                        }
                                                        <td>@IsRealTime</td>
                                                        @{
                                                            var CriticalForBCM = item.IsRealTime == 1 ? "Yes" : "No";
                                                        }
                                                        <td>@CriticalForBCM</td>
                                                        <td>@item.AreaofFocus</td>
                                                        @{
                                                            var BCMScope = item.BCMScope == "1" ? "Yes" : "No";
                                                        }
                                                        <td>@BCMScope</td>
                                                        @{
                                                            var Supported = item.Supported == "1" ? "Yes" : "No";
                                                        }
                                                        <td>@Supported</td>
                                                        <td>@item.RTO</td>
                                                        @{
                                                            var IsCritical = item.IsCritical == 1 ? "Yes" : "No";
                                                        }
                                                        <td>@IsCritical</td>
                                                        @{
                                                            var IsComplete = item.IsComplete == 1 ? "Complete" : "Incomplete";
                                                        }
                                                        <td>@IsComplete</td>
                                                        <td>
                                                            @* <a asp-action="EditUpStream" asp-controller="BIAUpstreamBusinessProcess" asp-route-iID="@item.ID">
                                                                <span role="button"><i class="cv-edit" title="Edit"></i></span>
                                                            </a> *@
                                                            <span role="button"><i class="cv-edit me-1 btnEdit" data-id="@item.ID"></i></span>
                                                            <span role="button"><i class="cv-delete text-danger btnDelete" data-id="@item.ID"></i></span>
                                                        </td>
                                                    </tr>
                                                }                                                
                                            }
                                        }                                        
                                    }
                                    <tr class="d-none">
                                        <td colspan="11"> 
                                            <div class="text-center"><img src="~/img/isomatric/no_records_to_display.svg" class="img-fluid" width="150" /></div>
                                        </td>
                                    </tr>                                    
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">
                <form asp-action="AddOrEditUpStream" method="post">

                    <div class="row row-cols-2">
                       @*  <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Version</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-version"></i></span>
                                    <input class="form-control" type="text" readonly value="1.0" />
                                    <input type="hidden" value="0" name="ID"/>
                                </div>
                            </div>
                        </div> *@
                        <div class="col d-grid align-items-end">
                            <div class="form-group">
                                <label class="form-lable">Questions</label>
                                @foreach (var objQusetions in ViewBag.Questions)
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="QuestionID" id="@objQusetions.ID" value="@objQusetions.ID">
                                        <label class="form-check-label" for="inlineRadio1">@objQusetions.QuestionDetails</label>
                                    </div>
                                }
                            </div>
                        </div>
                        <div class="col d-grid align-items-end">
                            <div class="form-group">
                                <label class="form-lable">Upstream/Downstream  </label>
                                &nbsp;
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="InBound"
                                           id="inlineRadio1" value="1">
                                    <label class="form-check-label" for="inlineRadio1">UpStream</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="InBound"
                                           id="inlineRadio2" value="0">
                                    <label class="form-check-label" for="inlineRadio2">DownStream</label>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Process</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-business-process"></i></span>
                                    <select class="form-select-sm" name="ProcessID" id="ProcessID" required>
                                        <option selected disabled value="">-- Select Process --</option>
                                        @{
                                            foreach (var objBusinessProvess in ViewBag.BusinessProcess)
                                            {
                                              <option value="@objBusinessProvess.Value">@objBusinessProvess.Text</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Input Data Source</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-source"></i></span>
                                    <textarea class="form-control" placeholder="Input Data Source" style="height:0px" name="DataSource" required></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">Area of Focus</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-focus"></i></span>
                                    <textarea class="form-control" placeholder="Area of Focus" style="height:0px" name="AreaofFocus" required></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    Real Time Process
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-process"></i></span>
                                    <select class="form-select-sm" name="IsRealTime" required>
                                        <option disabled value="" selected>-- Select Real Time Process --</option>
                                        <option value="1">Yes</option>
                                        <option value="2">No</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    Is activity critical for BCM
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-critical-BCM-entities"></i></span>
                                    <select class="form-select-sm" name="CriticalForBCM" required>
                                        <option value="" disabled selected>-- Select Is activity critical for BCM --</option>
                                        <option value="1">Yes</option>
                                        <option value="2">No</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    BCM Scope
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-bcm-scope"></i></span>
                                    <select class="form-select-sm" name="BCMScope" required>
                                        <option disabled value="" selected>-- Select BCM Scope --</option>
                                        <option value="1">Yes</option>
                                        <option value="2">No</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    Supported?
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-support"></i></span>
                                    <select class="form-select-sm" name="Supported" required>
                                        <option value="" disabled selected>-- Select Supported --</option>
                                        <option value="1">Yes</option>
                                        <option value="2">No</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    RTO
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-RTO"></i></span>
                                    <select class="form-select-sm" name="RTO" required>
                                        <option disabled selected value="">-- Select RTO --</option>
                                        @{
                                            foreach (var objRTO in ViewBag.RTOEnums)
                                            {
                                                                                    <option value="@objRTO.Value">@objRTO.Text</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    IsCritical
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-critical-BCM-entities"></i></span>
                                    <select class="form-select-sm" name="IsCritical" required>
                                        <option value="" disabled selected>-- Select IsCritical-- </option>
                                        <option value="1">Yes</option>
                                        <option value="2">No</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    If yes whether need to cover under DR
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-drsite"></i></span>
                                    <select class="form-select-sm" name="IsDRRequired" required>
                                        <option value="" disabled selected>-- Select If yes whether need to cover under DR --</option>
                                        <option value="1">Yes</option>
                                        <option value="2">No</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    If Application has Partial impact then Child Application
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-impact"></i></span>
                                    <select class="form-select-sm" name="ImpactSev1" required>
                                        <option value="" disabled selected>-- Select  If Application has Partial impact then Child Application --</option>
                                        @{
                                            foreach (var objImpactSeverity in ViewBag.ImpactSeverity)
                                            {
                                                                                    <option value="@objImpactSeverity.Value">@objImpactSeverity.Text</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    If Application has Major impact then Child Application
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-impact"></i></span>
                                    <select class="form-select-sm" name="ImpactSev2" required>
                                        <option value="" disabled selected>-- Select If Application has Major  impact then Child Application --</option>
                                        @{
                                            foreach (var objImpactSeverity in ViewBag.ImpactSeverity)
                                            {
                                                                                    <option value="@objImpactSeverity.Value">@objImpactSeverity.Text</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-lable">
                                    If Application has Total impact then Child Application
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-impact"></i></span>
                                    <select class="form-select-sm" name="ImpactSev3">
                                        <option value="" disabled selected>-- Select If Application has Total impact then Child Application --</option>
                                        @{
                                            foreach (var objImpactSeverity in ViewBag.ImpactSeverity)
                                            {
                                                                                    <option value="@objImpactSeverity.Value">@objImpactSeverity.Text</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 text-end">
                        <a class="btn btn-sm btn-outline-primary" role="button" asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-area="BCMProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@HttpContextAccessor.HttpContext.Session.GetString("ProcessID").ToString())">Back</a>
                        <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit">Submit</button>
                        <button class="btn btn-sm btn-secondary" id="btnCancel">Cancel</button>
                        <a role="button" class="btn btn-sm btn-primary" asp-action="ManageBusinessProcess" asp-controller="ManageBusinessProcesses" asp-area="BCMProcessBIA">View All</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="DeleteBody">
            </div>
        </div>
    </div>
</div>
<!-- Delete Modal -->

@section Scripts {
    <script>
        $(document).ready(function () {

            UpdateButtonLabel();

            $('.btnEdit').click(function () {
                var iID = $(this).data('id');
                $.ajax({
                    url: '@Url.Action("EditUpStream", "BIAUpstreamBusinessProcess")',
                    type: 'GET',
                    data: { iID: iID },
                    success: function (data) {
                        $('textarea[name="DataSource"]').val(data.dataSource);
                        $('textarea[name="AreaofFocus"]').val(data.areaofFocus);
                        $('input[name="InBound"][value="' + data.inBound + '"]').prop('checked', true);
                        $('input[name="QuestionID"][value="' + data.questionID + '"]').prop('checked', true);
                        $('select[name="ProcessID"]').val(data.processID);
                        $('select[name="IsRealTime"]').val(data.isRealTime);
                        $('select[name="IsDRRequired"]').val(data.isDRRequired);
                        $('select[name="IsCritical"]').val(data.isCritical);
                        $('select[name="RTO"]').val(data.rto);
                        $('select[name="Supported"]').val(data.supported);
                        $('select[name="BCMScope"]').val(data.bcmScope);
                        $('select[name="CriticalForBCM"]').val(data.criticalForBCM);
                        $('select[name="ImpactSev1"]').val(data.impactSev1);
                        $('select[name="ImpactSev2"]').val(data.impactSev2);
                        $('select[name="ImpactSev3"]').val(data.impactSev3);
                        $('input[name="ID"]').val(data.id);
                        UpdateButtonLabel();
                    },
                    error: function () {
                        console.log('Error');
                    }
                });
            });        

            $(document).on('click', '.btnDelete', function () {
                var iID = $(this).data('id');
                //$.get('/BCMProcessBIAForms/BIAUpstreamBusinessProcess/DeleteUpStream/', { iID: iID }, function (data) {
                $.get('@Url.Action("DeleteUpStream", "BIAUpstreamBusinessProcess")', { iID: iID }, function (data) {
                    $('#DeleteBody').html(data);
                    $('#DeleteModal').modal('show');
                });
            });

            $(document).on('click', '#btnCancel', function (event) {
                event.preventDefault();
                location.reload();
            });
        });


        function UpdateButtonLabel(){
            var id = $('input[name="ID"]').val();      
            if (id > 0) {
                $('#btnSubmit').text('Update');
            } else {
                $('#btnSubmit').text('Submit');
            }
        }
    </script>
}