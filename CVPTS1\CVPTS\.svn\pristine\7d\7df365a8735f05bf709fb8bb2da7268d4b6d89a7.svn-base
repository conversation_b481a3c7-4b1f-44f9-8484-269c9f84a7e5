﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Reflection;

namespace BCM.UI.Areas.BCMMenuAccess.Controllers;
[Area("BCMMenuAccess")]
public class PageMasterController : BaseController
{
    private ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    ManageUsersDetails? _UserDetails = new ManageUsersDetails();
    private readonly CVLogger _CVLogger;

    public PageMasterController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    public IActionResult Index()
    {
        return View();
    }

    [HttpGet]
    public IActionResult PageMaster()
    {
        List<MenuRights> objMenuRightsColl = new List<MenuRights>();
        try
        {
            objMenuRightsColl = _ProcessSrv.GetPageMasterlist();
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
        return View(objMenuRightsColl);
    }

    [HttpGet]
    public IActionResult EditPageMaster(int id)
    {
        MenuRights objMenuRights = new MenuRights();
        try
        {
            PolulateControllerAndAction();
            objMenuRights = _ProcessSrv.GetPageNamelistByID(Convert.ToInt32(id));

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_EditPageMaster", objMenuRights);
    }
    public void PolulateControllerAndAction()
    {
        try
        {
            Assembly asm = Assembly.GetExecutingAssembly();
            var controlleractionlist = asm.GetTypes()
                .SelectMany(type => type.GetMethods(BindingFlags.Instance | BindingFlags.DeclaredOnly | BindingFlags.Public))
                .Where(m => !m.GetCustomAttributes(typeof(System.Runtime.CompilerServices.CompilerGeneratedAttribute), true).Any())
                .Select(x => new { Controller = x.DeclaringType.Name, Action = x.Name, ReturnType = x.ReturnType.Name, Attributes = String.Join(",", x.GetCustomAttributes().Select(a => a.GetType().Name.Replace("Attribute", ""))) })
                .OrderBy(x => x.Controller).ThenBy(x => x.Action).ToList();

            List<string> a1 = controlleractionlist.Where(r => r.Controller.Contains("Controller")).Select(a => a.Controller).ToList();
            var lstActins = controlleractionlist.Where(a => a.Controller.Contains("Controller")).Select(a => a.Action).ToList();

            var lstArea = controlleractionlist.Where(a => a.Controller.Contains("Areas")).Select(a => a.Controller).ToList();
            List<string> ListAreas = new List<string>();
            foreach (var a in lstArea)
            {
                string[] b = a.Split("_");
                ListAreas.Add(b[1]);
            }
            ViewBag.ListAreas = ListAreas.Order().Distinct();
            ViewBag.lstlstActins = lstActins.Order().Distinct();
            ViewBag.lstControllers = a1.Order().Distinct();
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
    }
    [HttpGet]
    public IActionResult DeletePageMaster(int id)
    {
        MenuRights objMenuRights = new MenuRights();
        try
        {
            objMenuRights = _ProcessSrv.GetPageNamelistByID(Convert.ToInt32(id));
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_DeletePageMaster", objMenuRights);

    }

    [HttpPost]
    public IActionResult DeletePageMaster(MenuRights objMenuRights)
    {
        List<MenuRights> objMenuRightsColl = new List<MenuRights>();
        try
        {
            bool bSuccess = _ProcessSrv.DeletePageUpdate(Convert.ToInt32(objMenuRights.PageID));

            objMenuRightsColl = _ProcessSrv.GetPageMasterlist();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View("PageMaster", objMenuRightsColl);
    }

    [HttpGet]
    public IActionResult AddPageMaster()
    {
        try
        {
            PolulateControllerAndAction();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddPageMaster");
    }

    [HttpPost]
    public IActionResult SavePageMaster(MenuRights objMenuRights)
    {
        List<MenuRights> objMenuRightsColl = new List<MenuRights>();
        try
        {
            PolulateControllerAndAction();
            objMenuRights.CreatedBy = _UserDetails.UserID;
            objMenuRights.UpdatedBy = _UserDetails.UserID;
            objMenuRights.ControllerName = objMenuRights.ControllerName;
            bool bSuccess = _ProcessSrv.MenuPageSaveUpdate(objMenuRights);
            GetMenuData();

            objMenuRightsColl = _ProcessSrv.GetPageMasterlist();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("PageMaster", objMenuRightsColl);
    }

    private void GetMenuData()
    {
        List<MenuRights> objMenuRights = new List<MenuRights>();
        try
        {
            _UserDetails = _Utilities.LoginUserDetails();
            if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
            {
                objMenuRights = _ProcessSrv.GetMenuDataByRoleID("0", "0");

            }
            else
            {
                objMenuRights = _ProcessSrv.GetMenuDataByRoleID(_UserDetails.UserRoleID.ToString(), _UserDetails.UserID.ToString());

            }

            if (objMenuRights != null)
            {
                HttpContext.Session.SetString("MenuData", JsonConvert.SerializeObject(objMenuRights));
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

    }

    [HttpGet]
    public JsonResult GetAllAction(string strControllerName)
    {
        try
        {
            Assembly asm = Assembly.GetExecutingAssembly();
            var controlleractionlist = asm.GetTypes()
                .SelectMany(type => type.GetMethods(BindingFlags.Instance | BindingFlags.DeclaredOnly | BindingFlags.Public))
                .Where(m => !m.GetCustomAttributes(typeof(System.Runtime.CompilerServices.CompilerGeneratedAttribute), true).Any())
                .Select(x => new { Controller = x.DeclaringType.Name, Action = x.Name, ReturnType = x.ReturnType.Name, Attributes = String.Join(",", x.GetCustomAttributes().Select(a => a.GetType().Name.Replace("Attribute", ""))) })
                .OrderBy(x => x.Controller).ThenBy(x => x.Action).ToList();

            List<string> lstFilterdController = controlleractionlist.Where(r => r.Controller.Contains(strControllerName)).Select(a => a.Action).ToList();


            return Json(lstFilterdController.Distinct().Order());
        }
        catch (Exception ex)
        {

        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllController(string strAreaName)
    {
        try
        {
            Assembly asm = Assembly.GetExecutingAssembly();
            var controlleractionlist = asm.GetTypes()
                .SelectMany(type => type.GetMethods(BindingFlags.Instance | BindingFlags.DeclaredOnly | BindingFlags.Public))
                .Where(m => !m.GetCustomAttributes(typeof(System.Runtime.CompilerServices.CompilerGeneratedAttribute), true).Any())
                .Select(x => new { Controller = x.DeclaringType.Name, Action = x.Name, ReturnType = x.ReturnType.Name, Attributes = String.Join(",", x.GetCustomAttributes().Select(a => a.GetType().Name.Replace("Attribute", ""))) })
                .OrderBy(x => x.Controller).ThenBy(x => x.Action).ToList();

            List<string> a1 = controlleractionlist.Where(r => r.Controller.Contains(strAreaName)).Select(a => a.Action).ToList();

            var lstArea = controlleractionlist.Where(a => a.Controller.Contains(strAreaName)).Select(a => a.Controller).ToList();



            return Json(lstArea.Distinct());
        }
        catch (Exception ex)
        {

        }
        return Json(null);
    }

    [HttpPost]
    public IActionResult AddPageMaster(MenuRights objMenuRights)
    {
        return RedirectToAction("PageMaster");
    }

    public IActionResult GetSearchPages(string textSearch = "")
    {
        List<MenuRights> objMenuRightsColl = new List<MenuRights>();
        try
        {
            objMenuRightsColl = _ProcessSrv.GetPageMasterlist();

            objMenuRightsColl = objMenuRightsColl.Where(x=> x.PageName.Contains(textSearch)).ToList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_FilterPageMaster", objMenuRightsColl);
    }
}

