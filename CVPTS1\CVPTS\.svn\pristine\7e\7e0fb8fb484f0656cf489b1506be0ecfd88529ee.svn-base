﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.BusinessProcessComponents;
using BCM.Shared;
using BCM.UI.Areas.BCMIncidentManagement.Services;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using NuGet.Protocol.Plugins;
using Serilog;
using System.Collections.Generic;
using System.Data;
using System.Runtime.Serialization;
using System.Text;
using System.Xml;

namespace BCM.UI.Areas.BCMIncidentManagement.Controllers;
[Area("BCMIncidentManagement")]
public class IncidentTimeLineLatestController : BaseController
{
    private ProcessSrv _ProcessSrv;
    private ProcessMgr _oMgrService;
    private readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    private readonly BCMMail _BCMMail;
    private readonly ApiService _ApiService;
    public IncidentTimeLineLatestController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, BCMMail bcmMail, ProcessMgr oMgrService, ApiService ApiService) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _BCMMail = bcmMail;
        _oMgrService = oMgrService;
        _ApiService = ApiService;
    }


    [HttpGet]
    public IActionResult IncidentTimelineDetails(int iIncidentID)
    {
        IncidentManagement objIncidentManagement = new IncidentManagement();
        try
        {
            if (iIncidentID <= 0)
            {
                return PartialView("_IncidentTimelineDetails", objIncidentManagement);
            }
            HttpContext.Session.SetString("sessionIncidentId", iIncidentID.ToString());
            objIncidentManagement = _ProcessSrv.GetIncidentManagementGetByIncidentID(iIncidentID);
            ResourcesInfo objResource = new ResourcesInfo();
            if (objIncidentManagement!=null)
            {
                objResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt16(objIncidentManagement.NotifiedBy));
            }
            objIncidentManagement.OwnerEmail = objResource.CompanyEmail;
            objIncidentManagement.OwnerMobile = Convert.ToInt64(objResource.MobilePhone);
            objIncidentManagement.NotifiedAs = _Utilities.GetNotificationType(objIncidentManagement.NotifiedAs);
            objIncidentManagement.Status = _Utilities.GetIncidentStatus(objIncidentManagement.Status);

            List<RecoveryTaskStepInfo> objIncidentColl = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(iIncidentID, 0);

            DataTable StepStatusCount = new DataTable();
            StepStatusCount.Columns.Add("PlanName");
            StepStatusCount.Columns.Add("TotalSteps");
            StepStatusCount.Columns.Add("NotInitiated");
            StepStatusCount.Columns.Add("Assigned");
            StepStatusCount.Columns.Add("Acknowledged");
            StepStatusCount.Columns.Add("InProgress");
            StepStatusCount.Columns.Add("Completed");
            StepStatusCount.Columns.Add("ReInitiated");

            StepStatusCount.Rows.Add(objIncidentManagement.EventName, objIncidentColl.Count(), objIncidentColl.Count(x => x.StepStatus=="0"), objIncidentColl.Count(x => x.StepStatus=="1"), objIncidentColl.Count(x => x.StepStatus=="2"), objIncidentColl.Count(x => x.StepStatus=="3"), objIncidentColl.Count(x => x.StepStatus=="4"), objIncidentColl.Count(x => x.StepStatus=="5"));

            ViewBag.StatusCounts = StepStatusCount;
            ViewBag.RecoverySteps = objIncidentColl.OrderBy(x => x.Sequence);
            //getStepDetailsForChatbot(iIncidentID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return StatusCode(500, "An error occurred while loading incident details.");
        }
        return PartialView("_IncidentTimelineDetails", objIncidentManagement);
    }


    [HttpGet]
    public List<RecoveryTaskStepInfo> getStepDetailsForChatbot(int iIncidentID)
    {
        IncidentManagement objIncidentManagement = new IncidentManagement();
        List<RecoveryTaskStepInfo> objIncidentColl = new List<RecoveryTaskStepInfo>();
        try
        {
            objIncidentManagement = _ProcessSrv.GetIncidentManagementGetByIncidentID(iIncidentID);
            ResourcesInfo objResource = new ResourcesInfo();
            if (objIncidentManagement!=null)
            {
                objResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt16(objIncidentManagement.NotifiedBy));
            }
            objIncidentManagement.OwnerEmail = objResource.CompanyEmail;
            objIncidentManagement.OwnerMobile = Convert.ToInt64(objResource.MobilePhone);
            objIncidentManagement.NotifiedAs = _Utilities.GetNotificationType(objIncidentManagement.NotifiedAs);
            objIncidentManagement.Status = _Utilities.GetIncidentStatus(objIncidentManagement.Status);

            objIncidentColl = _ProcessSrv.GetRecoveryTaskStepsByIncidentID(iIncidentID, 0);

            ViewBag.RecoverySteps = objIncidentColl.OrderBy(x => x.Sequence);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return objIncidentColl;
    }


    [HttpPost]
    public IActionResult updateStepStatus(int iIncidentStepId, int stepStatus)
    {
        int iErrorCount = 0;
        int iStepNotSelected = 0;
        try
        {
            RecoveryTaskStepInfo objTaskSteps = GetRecoveryStepsData(iIncidentStepId);
            objTaskSteps.IncidentID=iIncidentStepId.ToString();
            objTaskSteps.StepStatus=stepStatus.ToString();

            RecoveryTaskStepInfo objRecoveryTaskStep = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(Convert.ToInt32(objTaskSteps.IncidentStepID));
            if (!objRecoveryTaskStep.StepStatus.Equals(((int)BCPEnum.StepStatus.Completed).ToString()))
            {
                objRecoveryTaskStep.StepStatus=objTaskSteps.StepStatus;
                if (objRecoveryTaskStep.StepStatus.Equals(((int)BCPEnum.StepStatus.Completed).ToString()) ||
                                objRecoveryTaskStep.StepStatus.Equals(((int)BCPEnum.StepStatus.Failed).ToString()))
                {
                    objRecoveryTaskStep.NotificationSentTime = string.Empty;
                    objRecoveryTaskStep.CompletionTime = DateTime.Now.ToString();
                }
                else
                {
                    if (!string.IsNullOrEmpty(objRecoveryTaskStep.CompletionTime))
                    {
                        objRecoveryTaskStep.CompletionTime = objRecoveryTaskStep.CompletionTime;
                    }
                    else
                    {
                        objRecoveryTaskStep.CompletionTime = string.Empty;
                    }
                }

                objRecoveryTaskStep.UpdatedBy = ((int)BCPEnum.NotificationType.EMail).ToString();
                objRecoveryTaskStep.Remarks="";
                objRecoveryTaskStep.ChangedBy = _UserDetails.UserID.ToString();
                if (objRecoveryTaskStep.ExecutedBy == "0")
                {
                    objRecoveryTaskStep.ExecutedBy = AcknowledgeUser(objRecoveryTaskStep);
                }

                //if (!_ApiService.UpdateStepStatus(objRecoveryTaskStep, Convert.ToInt32(objRecoveryTaskStep.OrgID)))
                if (!_ApiService.UpdateStepStatusNew(objRecoveryTaskStep, Convert.ToInt32(objRecoveryTaskStep.OrgID)))
                {
                    iErrorCount+=1;
                    return Json(new { success = false, message = "Error occoured while updating status..." });
                }
                return Json(new { success = true, message = "Step Updated Successfully..." });
            }
            else
                iStepNotSelected +=1;
            return Json(new { success = false, message = "Step status is match,Please select other status..." });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "ex" });
        }
    }

    private RecoveryTaskStepInfo GetRecoveryStepsData(int iIncidentStepID)
    {
        try
        {
            return _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(iIncidentStepID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return new RecoveryTaskStepInfo();
        }
    }

    private void BindStatus()
    {
        try
        {
            ViewBag.StepStaus = new SelectList(_Utilities.PopulateStepStatus(), "StatusId", "StatusName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    private string AcknowledgeUser(RecoveryTaskStepInfo objRecoveryTaskStep)
    {
        string ValidMessageBody = string.Empty;
        string ExecutedBy = "0";

        try
        {
            if (objRecoveryTaskStep.StepOwnerID.Equals(HttpContext.Session.GetInt32("iUserID").ToString()))
            {
                if (_ProcessSrv.ResourceMaster_UpdateActiveUser(HttpContext.Session.GetInt32("iUserID").ToString(), HttpContext.Session.GetInt32("iIncidentStepID") ?? 0))
                {
                    ValidMessageBody =  objRecoveryTaskStep.StepOwnerName + " has taken the responsibilty of performing Task" + objRecoveryTaskStep.StepName;
                    ExecutedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                    //BCPSms.SMSSend(objRecoveryTaskStep.AltStepOwnerMobileNo, objRecoveryTaskStep.AltStepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);
                    ExecutedBy = objRecoveryTaskStep.StepOwnerID;
                }
            }
            else if (objRecoveryTaskStep.AltStepOwnerID.Equals(HttpContext.Session.GetInt32("iUserID").ToString()))
            {
                if (_ProcessSrv.ResourceMaster_UpdateActiveUser(HttpContext.Session.GetInt32("iUserID").ToString(), HttpContext.Session.GetInt32("iIncidentStepID") ?? 0))
                {
                    ValidMessageBody = objRecoveryTaskStep.AltStepOwnerName + " has taken the responsibilty of performing Task" + objRecoveryTaskStep.StepName; //"Dear " + objRecoveryTaskStep.StepOwnerName + ", This is to inform you that " +
                    ExecutedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                    //BCPSms.SMSSend(objRecoveryTaskStep.StepOwnerMobileNo, objRecoveryTaskStep.StepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);
                    ExecutedBy = objRecoveryTaskStep.AltStepOwnerID;
                }
            }
            else if (objRecoveryTaskStep.ReassignedTo.Equals(HttpContext.Session.GetInt32("iUserID").ToString()))
            {
                if (_ProcessSrv.ResourceMaster_UpdateActiveUser(objRecoveryTaskStep.ReassignedTo, HttpContext.Session.GetInt32("iIncidentStepID") ?? 0))
                {
                    ValidMessageBody = objRecoveryTaskStep.ReassignedOwnerName + " has taken the responsibilty of performing Task" + objRecoveryTaskStep.StepName;//"Dear " + objRecoveryTaskStep.StepOwnerName + ", This is to inform you that " +

                    //BCPSms.SMSSend(objRecoveryTaskStep.StepOwnerMobileNo, objRecoveryTaskStep.StepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);

                    ValidMessageBody = objRecoveryTaskStep.ReassignedOwnerName + " has taken the responsibilty of performing Task"
                        + objRecoveryTaskStep.StepName; //"Dear " + objRecoveryTaskStep.AltStepOwnerName + ", This is to inform you that " +
                                                        //BCPSms.SMSSend(objRecoveryTaskStep.AltStepOwnerMobileNo, objRecoveryTaskStep.AltStepOwnerName, ValidMessageBody, objRecoveryTaskStep.OrgID);

                    ExecutedBy = objRecoveryTaskStep.ReassignedTo;
                }
            }
            else
            {

                RecoveryTaskStepInfo obj = new RecoveryTaskStepInfo();
                obj.StepID = objRecoveryTaskStep.IncidentStepID;
                obj.PlanID = objRecoveryTaskStep.PlanID;
                obj.AltStepOwnerID = HttpContext.Session.GetInt32("iUserID").ToString();
                bool Success = _ProcessSrv.RecoveryTaskStepInfoUpdate_AltOwner(obj);
                if (Success)
                {
                    if (_ProcessSrv.ResourceMaster_UpdateActiveUser(HttpContext.Session.GetInt32("iUserID").ToString(), Convert.ToInt32(objRecoveryTaskStep.IncidentStepID)))
                    {
                        //bool notify = NotifyReassignedOwner(Convert.ToInt32(ResponseID), Convert.ToInt32(objTaskStepInfo.IncidentID));
                        //ValidMessageBody = "Dear " + objTaskStepInfo.StepOwnerName + ", " + Convert.ToString(_DataTable.Rows[0]["ResourceName"]) + " has taken the responsibilty of performing Task" + objTaskStepInfo.StepName;

                        //BCPSms.SMSSend(objTaskStepInfo.StepOwnerMobileNo, ValidMessageBody);

                        //ValidMessageBody = "Dear " + objTaskStepInfo.AltStepOwnerName + ", " + Convert.ToString(_DataTable.Rows[0]["ResourceName"]) + " has taken the responsibilty of performing Task" + objTaskStepInfo.StepName;
                        //BCPSms.SMSSend(objTaskStepInfo.ReassignedOwnerMobileNo, ValidMessageBody);
                        RecoveryTaskStepInfo objrecstep = _ProcessSrv.GetRecoveryTaskStepsByIncidentStepID(HttpContext.Session.GetInt32("iIncidentStepID") ?? 0);
                        ValidMessageBody =  objrecstep.ReassignedOwnerName + " has taken the responsibilty of performing Task" + objrecstep.StepName;

                        //BCPSms.SMSSend(objrecstep.StepOwnerMobileNo, objrecstep.StepOwnerName, ValidMessageBody, objrecstep.OrgID);

                        ValidMessageBody =  objrecstep.ReassignedOwnerName + " has taken the responsibilty of performing Task" + objrecstep.StepName;
                        //BCPSms.SMSSend(objrecstep.AltStepOwnerMobileNo, objrecstep.AltStepOwnerName, ValidMessageBody, objrecstep.OrgID);

                        ExecutedBy = HttpContext.Session.GetInt32("iUserID").ToString();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return ExecutedBy;
    }

    public void MarkAsCompleted()
    {
        try
        {
            int IncidentID =Convert.ToInt32(HttpContext.Session.GetString("sessionIncidentId"));
            bool update = _ProcessSrv.IncidentManagementUpdateStatus(Convert.ToInt32(IncidentID), Convert.ToInt32(_UserDetails.UserID), (int)BCPEnum.IncidentStatus.Completed, 0);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
}