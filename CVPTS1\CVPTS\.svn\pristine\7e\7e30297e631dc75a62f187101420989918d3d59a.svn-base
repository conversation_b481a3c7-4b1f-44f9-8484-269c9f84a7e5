@model BCM.BusinessClasses.BusinessProcessInfo
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{

    Layout = "~/Views/Shared/_ParticalLayout.cshtml";
}
<form id="addBCMEntitiesForm" asp-action="AddBCMEntities" asp-controller="ManageBCMEntities" method="post" class="needs-validation progressive-validation" novalidate>
    <div class="row row-cols-2">
        <div style="display:none">
            <input type="hidden" asp-for="ID" />
        </div>
        <div class="col-6">
            <h6 class="Sub-Title">Version</h6>
            <div class="form-group">
                <label class="form-label">Organization</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-organization"></i></span>
                    <select id="OrgID" name="OrgID" class="form-select form-control selectized ddlOrganization" asp-for="OrgID" autocomplete="off" aria-label="Default select example" required>
                        <option selected value="0">-- Select Organizations --</option>
                        @foreach (var objOrg in ViewBag.OrgInfo)
                        {
                            <option value="@objOrg.Value">@objOrg.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Organization</div>
            </div>
            <div class="form-group">
                <label class="form-label">Department</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-department"></i></span>
                    <select id="DepartmentID" name="DepartmentID" class="form-select form-control selectized ddlDepartment" asp-for="DepartmentID" autocomplete="off" aria-label="Default select example">
                        <option selected value="0">-- All Departments --</option>
                        @foreach (var objDepartment in ViewBag.DepartmentInfo)
                        {
                            <option value="@objDepartment.Value">@objDepartment.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Department</div>
            </div>
            <div class="form-group">
                <label class="form-label">BCM Entity Type</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-entity-type"></i></span>
                    @* <input class="form-control" type="text" placeholder="Enter BCM Entity" /> *@
                    <select id="EntityTypeID" name="EntityTypeID" class="form-select form-control selectized" asp-for="EntityTypeID" autocomplete="off" aria-label="Default select example" required>
                        <option selected value="0">-- Select BCM Entity --</option>
                        @foreach (var objBCMEntityType in ViewBag.BcmEntities)
                        {
                            <option value="@objBCMEntityType.Value">@objBCMEntityType.Text</option>
                        }
                    </select>
                    <input type="hidden" id="ProcessName" name="ProcessName" />
                </div>
                <div class="invalid-feedback">Select BCM Entity Type</div>
            </div>
            <div class="form-group">
                <label class="form-label">Select Approver</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-approver"></i></span>
                    <select id="ApproverID" name="ApproverID" class="form-select form-select-sm selectized" asp-for="ApproverID" autocomplete="off" aria-label="Default select example" required>
                        <option selected value="0">-- Select Approver --</option>
                        @foreach (var objResource in ViewBag.ResourcesInfo)
                        {
                            <option value="@objResource.Value">@objResource.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Approver</div>
            </div>
            <div class="form-group">
                <label class="form-label">BIA Profile</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-profile"></i></span>
                    <select id="ddlBIAProfile" class="form-control form-select selectized" asp-for="ProfileID" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.lstBIAProfile,"ID","ProfileName"))">
                        <option selected value="0">-- All Profiles --</option>
                    </select>
                    <span style="display:none" class="input-group-text bg-info px-2 rounded-0 text-white" role="button"><i class="cv-profile-details"></i></span>
                </div>
                <div class="invalid-feedback">Select BIA Profile</div>
            </div>
        </div>
        <div class="col-6">
            <h6 class="Sub-Title">Process Code</h6>
            <div class="form-group">
                <label class="form-label">Unit</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-unit"></i></span>
                    <select id="UnitID" name="UnitID" class="form-select form-control selectized ddlUnit" asp-for="UnitID" autocomplete="off" aria-label="Default select example">
                        <option selected value="0">-- All Units --</option>
                        @foreach (var objUnit in ViewBag.OrgUnit)
                        {
                            <option value="@objUnit.Value">@objUnit.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Unit</div>
            </div>
            <div class="form-group">
                <label class="form-label">SubDepartment</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                    <select id="SubfunctionID" name="SubfunctionID" class="form-select form-control selectized ddlSubDepartment" asp-for="SubfunctionID" autocomplete="off" aria-label="Default select example">
                        <option selected value="0">-- All Sub Departments --</option>
                        @foreach (var objSubDepartment in ViewBag.SubFunction)
                        {
                            <option value="@objSubDepartment.Value">@objSubDepartment.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select SubDepartment</div>
            </div>
            <div class="form-group">
                <label class="form-label">Business Process <i class="cv-note"></i></label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-business-process"></i></span>
                    <select class="form-select form-select-sm selectized" id="RecordID" name="RecordID" asp-for="RecordID" autocomplete="off" required>
                        <option selected value="0">-- Select Process --</option>
                        @if (ViewBag.lstEntities != null)
                        {
                            foreach (var objSubDepartment in ViewBag.lstEntities)
                            {
                                <option value="@objSubDepartment.Value">@objSubDepartment.Text</option>
                            }
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select Business Process</div>
            </div>
            <div class="form-group">
                <label class="form-label">RPO</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-rpo"></i></span>
                    <input class="form-control" id="RPO" placeholder="Enter RPO" asp-for="RPO" />
                    <select id="RPOUnit" name="RPOUnit" class="form-select form-control selectized" asp-for="RPOUnit" autocomplete="off" aria-label="Default select example">
                        <option selected value="0">-- Select --</option>
                        @foreach (var objTimeUnit in ViewBag.TimeUnit)
                        {
                            <option value="@objTimeUnit.Value">@objTimeUnit.Text</option>
                        }
                    </select>
                </div>
                <div class="invalid-feedback">Select RPO unit</div>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                <label class="form-label">Description <i class="cv-note"></i></label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-description"></i></span>
                    <input class="form-control" type="text" id="Comments" name="Comments" placeholder="Entity Description" asp-for="Comments" />
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">Total Transaction Volume</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-transaction-volume"></i></span>
                    <input class="form-control" type="text" id="PeakTranVolume" name="PeakTranVolume" placeholder="Entity Total Transaction Volume" asp-for="PeakTranVolume" />
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                <label class="form-label">Team Size</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-team-size"></i></span>
                    <input class="form-control" type="text" id="TeamSize" name="TeamSize" placeholder="Team size" asp-for="TeamSize" />
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="d-flex align-items-center gap-3 mb-2">
                <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                <h6 class="mb-0">Owner Details</h6>
            </div>
            <div class="row collapse p-3" id="collapseExample">
                <div class="col-md-6 col-lg-6 col-sm-12 ">
                    <div class="w-100">

                        <div class="form-group">
                            <label class="form-label" for="ProcessOwnerID">Select Owner</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-approver"></i></span>
                                <select id="ProcessOwnerID" name="ProcessOwnerID" class="form-select form-select-sm selectized" asp-for="ProcessOwnerID" autocomplete="off" aria-label="Default select example" required>
                                    <option selected value="0">-- Select Owner --</option>
                                    @foreach (var objResource in ViewBag.ResourcesInfo)
                                    {
                                        <option value="@objResource.Value">@objResource.Text</option>
                                    }
                                </select>
                            </div>
                            <div class="invalid-feedback">Select Owner</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input class="form-control" readonly type="text" placeholder="Enter Email" id="OwnerEmail" asp-for="OwnerEmail" name="OwnerEmail" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Mobile</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input class="form-control" readonly type="text" placeholder="Enter Mobile" asp-for="ProcessOwnerMobile" id="ProcessOwnerMobile" name="ProcessOwnerMobile" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-sm-12">
                    <div class="w-100">

                        <div class="form-group">
                            <label class="form-label" for="AltProcessOwnerID"></i>Select Alternate Owner</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-approver"></i></span>
                                <select id="AltProcessOwnerID" class="form-select form-select-sm selectized" asp-for="AltProcessOwnerID" name="AltProcessOwnerID" autocomplete="off" aria-label="Default select example" required>
                                    <option selected value="0">-- Select Alternate Owner --</option>
                                    @foreach (var objResource in ViewBag.ResourcesInfo)
                                    {
                                        <option value="@objResource.Value">@objResource.Text</option>
                                    }
                                </select>
                            </div>
                            <div class="invalid-feedback">Select Alternate Owner</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input class="form-control" readonly type="text" placeholder="Enter Email" asp-for="AltProcessOwnerEmail" id="AltProcessOwnerEmail" name="AltProcessOwnerEmail" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Mobile</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-description"></i></span>
                                <input class="form-control" readonly type="text" placeholder="Enter Mobile" asp-for="AltProcessOwnerMobile" id="AltProcessOwnerMobile" name="AltProcessOwnerMobile" />
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            @* <div class="d-flex align-items-center gap-3 my-2">
                <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#profile-details" aria-expanded="false" aria-controls="collapseExample">
                    <i class="cv-Plus align-middle"></i>
                </span>
                <h6 class="mb-0">Business Parameter Profile Details </h6>
            </div>
            <div class="row collapse p-3" id="profile-details">
                <div class="col-md-6 col-lg-6 col-sm-12">
                    <table class="w-100">
                        <tbody>
                            <tr>
                                <td style="width:42%">
                                    <i class="cv-profile me-1"></i>Business Parameter Profile

                                </td>
                                <td>:</td>
                                <td>
                                    <div class="form-group">
                                        <div class="input-group">
                                            <select class="form-select form-control" id="BPProfileID" name="BPProfileID" asp-for="BPProfileID" autocomplete="off" aria-label="Default select example for resource" asp-items="@(new SelectList(ViewBag.lstParameterProfile,"ID","ProfileName"))">
                                                <option selected value="0">-- All Parameter Profile --</option>
                                            </select>
                                        </div>
                                        <div class="invalid-feedback">Business Parameter Profile</div>
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                    </table>

                </div>
                <table class="table table-striped" style="visibility:hidden">
                    <thead>
                        <tr>
                            <td>Sr.No</td>
                            <td>Parameter Name</td>
                            <td>Parameter Type</td>
                            <td>Monitoring Enabled</td>
                            <td>Active</td>
                            <td>Schedule Time</td>
                            <td>Edit/View</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>01</td>
                            <td>
                                Move resource to other EGS location
                            </td>
                            <td>
                                Object
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <a href="#"><i class="cv-edit"></i></a>
                            </td>
                        </tr>
                        <tr>
                            <td>02</td>
                            <td>
                                Move resource to other EGS location
                            </td>
                            <td>
                                Sensitivity
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <a href="#"><i class="cv-edit"></i></a>
                            </td>
                        </tr>
                        <tr>
                            <td>03</td>
                            <td>
                                People WFH
                            </td>
                            <td>
                                Sensitivity
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <a href="#"><i class="cv-edit"></i></a>
                            </td>
                        </tr>
                        <tr>
                            <td>04</td>
                            <td>
                                Increasing shift time at DR Site
                            </td>
                            <td>
                                Object
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <a href="#"><i class="cv-edit"></i></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div> *@
            <div class="d-flex align-items-center gap-3 my-2">
                <span class="toggle-password" role="button" data-bs-toggle="collapse" data-bs-target="#Process-Review" aria-expanded="false" aria-controls="collapseExample"><i class="cv-Plus align-middle"></i></span>
                <h6 class="mb-0">Process  Review Section </h6>
            </div>
            <div class="row collapse p-3" id="Process-Review">


                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <span><i class="cv-calendar me-1"></i>Last Review Date :</span><span class="ms-1 text-primary">NA</span>
                    </div> 
                    <div class="d-flex align-items-center">

                        <div class="form-group">
                            <label class="form-label">Next Review Date</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cv-calendar"></i></span>
                                <input type="date" id="ReviewDate" name="ReviewDate" asp-for="ReviewDate" class="form-control" required />
                            </div>
                            <div class="invalid-feedback">Select Review Date</div>
                        </div>
                        
                    </div>
                </div>
                @*  <table class="table table-striped">
                    <thead>
                        <tr>

                            <td>Cycle</td>
                            <td>Start Date</td>
                            <td>End Date</td>
                            <td>Status</td>
                            <td>Next Review Date</td>
                            <td>Reviewer Name</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>

                            <td>01</td>
                            <td>
                                10/05/2024
                            </td>
                            <td>
                                -
                            </td>
                            <td>In Progress</td>
                            <td>18/08/2022</td>
                            <td>
                                <div class="d-flex">
                                    <div class="User-icon">
                                        <img class="rounded-circle table-profile" src="/img/profile-img/usericon.svg">
                                    </div>
                                    <div>
                                        <ul class="ps-0 mb-0">
                                            <li class="list-group-item fw-semibold">Arun Patil</li>
                                            <li class="list-group-item"><a class="text-primary" href="#"><EMAIL></a></li>
                                            <li class="list-group-item">9988337711</li>
                                        </ul>
                                    </div>
                                </div>
                            </td>

                        </tr>

                    </tbody>
                </table>
                <div class="d-flex align-items-center justify-content-end gap-2">
                    <button class="btn btn-sm btn-primary">End Review</button>
                    <button class="btn btn-sm btn-primary">Show History</button>
                </div> *@
            </div>
        </div>

    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1 Closebtn" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>


<script>
    $(document).ready(function () {
        //console.log("AddBCMEntities form ready");

        // Force a check to see if global validation is loaded
        if (typeof window.BCMValidation === 'undefined') {
            console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
            // Try to load it dynamically as a fallback
            $.getScript('/js/global-validation.js')
                .done(function() {
                    console.log("Successfully loaded global-validation.js dynamically");
                    initializeValidation();
                })
                .fail(function() {
                    console.error("Failed to load global-validation.js dynamically");
                });
        } else {
            console.log("BCMValidation is already defined");
            initializeValidation();
        }

        // Function to initialize validation
        function initializeValidation() {
            console.log("Initializing validation for AddBCMEntities form");

            if (window.BCMValidation) {
                console.log("BCMValidation found, initializing");

                // Get the form element
                const form = document.getElementById('addBCMEntitiesForm');
                if (!form) {
                    console.error("Form not found with ID: addBCMEntitiesForm");
                    return;
                }

                // Store the original content of all invalid-feedback divs
                const customMessages = {};
                form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                    // Find the associated input
                    const formGroup = element.closest('.form-group');
                    const input = formGroup?.querySelector('input, select, textarea');
                    if (input) {
                        // Store the custom message using the input's ID or name as the key
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key) {
                            customMessages[key] = element.textContent.trim();
                            console.log("Stored custom message for", key, ":", customMessages[key]);
                        }
                    }
                });

                // Override the validateInput function to preserve custom messages
                const originalValidateInput = window.BCMValidation.validateInput;
                window.BCMValidation.validateInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                // Don't use direct style manipulation - let CSS classes handle display
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateEmail function similarly
                const originalValidateEmail = window.BCMValidation.validateEmail;
                window.BCMValidation.validateEmail = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidateEmail(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                // Don't use direct style manipulation - let CSS classes handle display
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validatePatternInput function similarly
                const originalValidatePatternInput = window.BCMValidation.validatePatternInput;
                window.BCMValidation.validatePatternInput = function(input, forceValidation = false) {
                    // Get the result from the original function
                    const result = originalValidatePatternInput(input, forceValidation);

                    // If the input is invalid, restore the custom message
                    if (!result) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                // Don't use direct style manipulation - let CSS classes handle display
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    }

                    return result;
                };

                // Override the validateForm function to restore all custom messages after validation
                const originalValidateForm = window.BCMValidation.validateForm;
                window.BCMValidation.validateForm = function(form) {
                    // Get the result from the original function
                    const result = originalValidateForm(form);

                    // Restore all custom messages for invalid inputs
                    form.querySelectorAll('.' + window.BCMValidation.classes.invalidClass).forEach(function(input) {
                        const key = input.id || input.name || input.getAttribute('asp-for');
                        if (key && customMessages[key]) {
                            const formGroup = input.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            if (feedbackElement) {
                                // Restore the custom message
                                feedbackElement.textContent = customMessages[key];
                                // Don't use direct style manipulation - let CSS classes handle display
                                console.log("Restored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    return result;
                };

                // Initialize the validation framework
                window.BCMValidation.init();

                // Custom function to add required field indicators without duplicates
                function addCustomRequiredFieldIndicators(form) {
                    const requiredInputs = form.querySelectorAll('[required]');

                    requiredInputs.forEach(function(input) {
                        const label = form.querySelector('label[for="' + input.id + '"]');
                        if (label) {
                            // Check if asterisk already exists
                            if (!label.querySelector('.required-asterisk')) {
                                // Create asterisk element
                                const asterisk = document.createElement('span');
                                asterisk.className = 'required-asterisk text-danger';
                                asterisk.textContent = ' *';
                                asterisk.style.fontWeight = 'bold';

                                // Append asterisk to label
                                label.appendChild(asterisk);
                                console.log("Added asterisk to label for:", input.id);
                            }
                        }
                    });
                }

                // Use custom function instead of the default one
                addCustomRequiredFieldIndicators(form);

                // Add format indicators for pattern-based inputs
                window.BCMValidation.addFormatIndicators(form);

                // Add a manual validation trigger on form submission
                form.addEventListener('submit', function(event) {
                    console.log("Form submission triggered");

                    // Show all validation messages
                    window.BCMValidation.showAllValidationMessages(form);

                    // Validate the form
                    const isValid = window.BCMValidation.validateForm(form);
                    console.log("Form validation result:", isValid);

                    // Also validate owners are different
                    const ownersValid = validateOwnersDifferent();
                    console.log("Owners validation result:", ownersValid);

                    if (!isValid || !ownersValid) {
                        console.log("Preventing form submission due to validation errors");
                        event.preventDefault();
                        event.stopPropagation();

                        // Focus the first invalid field
                        const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                        if (firstInvalidField) {
                            firstInvalidField.focus();
                        }
                    }
                });
            } else {
                console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
            }
        }

        // Function to validate that owner and alternate owner are different
        function validateOwnersDifferent() {
            const ownerSelect = document.getElementById('ProcessOwnerID');
            const altOwnerSelect = document.getElementById('AltProcessOwnerID');

            if (!ownerSelect || !altOwnerSelect) {
                return true; // If elements don't exist, skip validation
            }

            const ownerValue = ownerSelect.value;
            const altOwnerValue = altOwnerSelect.value;

            let isValid = true;

            // Clear previous error states for role validation
            clearOwnerValidationErrors();

            // Only validate if both have values and are not the default "" or "0" value
            if (ownerValue && altOwnerValue && ownerValue !== "" && altOwnerValue !== "" && ownerValue !== "0" && altOwnerValue !== "0" && ownerValue === altOwnerValue) {
                // Show validation error for both fields
                showOwnerValidationError('#ProcessOwnerID', 'Owner and Alternate Owner cannot be the same person');
                showOwnerValidationError('#AltProcessOwnerID', 'Owner and Alternate Owner cannot be the same person');
                isValid = false;
            }

            return isValid;
        }

        // Function to show owner validation error
        function showOwnerValidationError(selector, message) {
            var element = $(selector);
            element.addClass('is-invalid').removeClass('is-valid');

            var formGroup = element.closest('.form-group');
            var feedbackElement = formGroup.find('.invalid-feedback');
            if (feedbackElement.length > 0) {
                feedbackElement.text(message);
                // Use CSS classes and show method for proper display
                feedbackElement.addClass('custom-validation show').show();
            }
        }

        // Function to clear owner validation errors
        function clearOwnerValidationErrors() {
            var ownerSelectors = ['#ProcessOwnerID', '#AltProcessOwnerID'];

            ownerSelectors.forEach(function(selector) {
                var element = $(selector);
                var formGroup = element.closest('.form-group');
                var feedbackElement = formGroup.find('.invalid-feedback');

                // Only clear if the current message is an owner validation error
                if (feedbackElement.length > 0 && feedbackElement.text().includes('cannot be the same person')) {
                    element.removeClass('is-invalid');
                    // Use CSS classes for proper hiding
                    feedbackElement.removeClass('custom-validation show').hide();

                    // Restore original message
                    var originalMessages = {
                        '#ProcessOwnerID': 'Select Owner',
                        '#AltProcessOwnerID': 'Select Alternate Owner'
                    };

                    if (originalMessages[selector]) {
                        feedbackElement.text(originalMessages[selector]);
                    }
                }
            });
        }

        // Add change event listeners to owner dropdowns for real-time validation
        $(document).on('change', '#ProcessOwnerID, #AltProcessOwnerID', function() {
            // First clear any existing validation state when user makes a selection
            var $this = $(this);
            if ($this.val() && $this.val() !== "" && $this.val() !== "0") {
                $this.removeClass('is-invalid');
                var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be the same person')) {
                    feedbackElement.hide();
                }
            }

            // Then validate that owners are different
            validateOwnersDifferent();
        });

        // Combined event handler for AJAX and validation
        $('#ProcessOwnerID,#AltProcessOwnerID').change(function () {
            var selectedDDL = $(this).attr('id');
            var iId = $(this).val();
            var $this = $(this);

            // Handle AJAX call for populating email/mobile fields
            if (selectedDDL == "ProcessOwnerID") {
                $.ajax({
                    url: '@Url.Action("OnSelectedChange", "ManageBCMEntities")',
                    type: 'GET',
                    data: { OwnerID: iId },
                    success: function (data) {
                        if (data != null) {
                            $("#OwnerEmail").val(data.companyEmail);
                            $("#ProcessOwnerMobile").val(data.mobilePhone);
                        }
                        else {
                            console.log("Error While Binding Data.");
                        }
                    },
                    error: function (data) {
                        console.log('Error Is Invoked');
                    }
                });
            } else if (selectedDDL == "AltProcessOwnerID") {
                $.ajax({
                    url: '@Url.Action("OnAltSelectedChange", "ManageBCMEntities")',
                    type: 'GET',
                    data: { AltOwnerID: iId },
                    success: function (data) {
                        if (data != null) {
                            $("#AltProcessOwnerEmail").val(data.companyEmail);
                            $("#AltProcessOwnerMobile").val(data.mobilePhone);
                        }
                        else {
                            console.log("Error While Binding Data.");
                        }
                    },
                    error: function (data) {
                        console.log('Error Is Invoked');
                    }
                });
            }

            // Clear validation state when user makes a selection
            if ($this.val() && $this.val() !== "" && $this.val() !== "0") {
                $this.removeClass('is-invalid');
                var feedbackElement = $this.closest('.form-group').find('.invalid-feedback');
                if (feedbackElement.length > 0 && !feedbackElement.text().includes('cannot be the same person')) {
                    feedbackElement.hide();
                }
            }

            // Validate that owners are different (with small delay to allow AJAX to complete)
            setTimeout(function() {
                validateOwnersDifferent();
            }, 100);
        });

        $('#EntityTypeID').change(function () {
            var EntityType = $(this).val();

            //debugger;
            if (EntityType) {
                $.ajax({
                    url: '@Url.Action("BCMEntityType_SelectedIndexChanged", "ManageBCMEntities")',
                    type: 'GET',
                    data: { EntityTypeID: EntityType },
                    success: function (response) {
                       debugger;
                       let selectizeInstance = $('#RecordID')[0].selectize;
                       selectizeInstance.clear();
                       selectizeInstance.clearOptions();
                       selectizeInstance.addOption({ value: "0", text: "-- All Entities --" });
                       selectizeInstance.addItem("0");

                       response && response.forEach(({ value, text }) => {
                               if (value && text) {
                                    selectizeInstance.addOption({ value: value, text: text });
                               }
                       });
                    },
                    // success: function (data) {
                    //     var OrgUnit = $('#RecordID');
                    //     //debugger;
                    //     OrgUnit.empty();
                    //     OrgUnit.append('<option value="0">-- All Entities --</option>');
                    //     $.each(data, function (index, item) {

                    //         OrgUnit.append('<option value="' + item.value + '">' + item.text + '</option>')
                    //     });
                    // },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                })
            }

        });


        $('#RecordID').change(function () {
            //debugger;
            var processName = $(this).find("option:selected").text();
            $('#ProcessName').val(processName);
        });

        // Cascading Dropdown Implementation
        // Organization change event - populate Units
        $(document).on("change", "#OrgID", function () {
            var iOrgID = $(this).val();
            console.log("Organization changed to:", iOrgID);

            if (iOrgID && iOrgID !== "" && iOrgID !== "0") {
                $.ajax({
                    url: '@Url.Action("GetAllUnits", "ManageBCMEntities")',
                    type: 'GET',
                    data: { iOrgID: iOrgID },
                    success: function (response) {
                       debugger;
                       let selectizeInstance = $('.ddlUnit')[0].selectize;
                       selectizeInstance.clear();
                       selectizeInstance.clearOptions();
                       selectizeInstance.addOption({ value: "0", text: "-- All Units --" });
                       selectizeInstance.addItem("0");

                       response && response.forEach(({ unitID, unitName }) => {
                               if (unitID && unitName) {
                                    selectizeInstance.addOption({ value: unitID, text: unitName });
                               }
                       });
                       // $('.ddlDepartment').empty().append('<option value="0">-- All Departments --</option>');
                       // $('.ddlSubDepartment').empty().append('<option value="0">-- All Sub Departments --</option>');
                    },
                    // success: function (data) {
                    //     console.log("Units data received:", data);
                    //     var ddlUnit = $('.ddlUnit');
                    //     ddlUnit.empty();
                    //     ddlUnit.append('<option value="0">-- All Units --</option>');
                    //     $.each(data, function (index, item) {
                    //         ddlUnit.append('<option value="' + item.unitID + '">' + item.unitName + '</option>');
                    //     });

                    //     // Clear dependent dropdowns
                    //     $('.ddlDepartment').empty().append('<option value="0">-- All Departments --</option>');
                    //     $('.ddlSubDepartment').empty().append('<option value="0">-- All Sub Departments --</option>');
                    // },
                    error: function (xhr, status, error) {
                        console.log('Error loading units:', error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            } else {
                // Clear all dependent dropdowns when no organization is selected
                $('.ddlUnit').empty().append('<option value="0">-- All Units --</option>');
                $('.ddlDepartment').empty().append('<option value="0">-- All Departments --</option>');
                $('.ddlSubDepartment').empty().append('<option value="0">-- All Sub Departments --</option>');
            }
        });

        // // Unit change event - populate Departments
        // $(document).on("change", ".ddlUnit", function () {
        //     var iUnitID = $(this).val();
        //     console.log("Unit changed to:", iUnitID);

        //     if (iUnitID && iUnitID !== "" && iUnitID !== "0") {
        //         $.ajax({
        //             url: '@Url.Action("GetAllDepartments", "ManageBCMEntities")',
        //             type: 'GET',
        //             data: { iUnitID: iUnitID },
        //             success: function (response) {
        //                debugger;
        //                let selectizeInstance = $('.ddlDepartment')[0].selectize;
        //                selectizeInstance.clear();
        //                selectizeInstance.clearOptions();
        //                selectizeInstance.addOption({ value: "0", text: "-- All Departments --" });
        //                selectizeInstance.addItem("0");

        //                response && response.forEach(({ departmentID, departmentName }) => {
        //                        if (departmentID && departmentName) {
        //                             selectizeInstance.addOption({ value: departmentID, text: departmentName });
        //                        }
        //                });
        //                //$('.ddlSubDepartment').empty().append('<option value="0">-- All Sub Departments --</option>');
        //             },
        //             // success: function (data) {
        //             //     console.log("Departments data received:", data);
        //             //     var ddlDepartment = $('.ddlDepartment');
        //             //     ddlDepartment.empty();
        //             //     ddlDepartment.append('<option value="0">-- All Departments --</option>');
        //             //     $.each(data, function (index, item) {
        //             //         ddlDepartment.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>');
        //             //     });

        //             //     // Clear dependent dropdown
        //             //     $('.ddlSubDepartment').empty().append('<option value="0">-- All Sub Departments --</option>');
        //             // },
        //             error: function (xhr, status, error) {
        //                 console.log('Error loading departments:', error);
        //                 console.error(xhr.status);
        //                 console.error(xhr.responseText);
        //             }
        //         });
        //     } else {
        //         // Clear dependent dropdowns when no unit is selected
        //         $('.ddlDepartment').empty().append('<option value="0">-- All Departments --</option>');
        //         $('.ddlSubDepartment').empty().append('<option value="0">-- All Sub Departments --</option>');
        //     }
        // });

        // Department change event - populate SubDepartments
        // $(document).on("change", ".ddlDepartment", function () {
        //     var iDepartmentID = $(this).val();
        //     console.log("Department changed to:", iDepartmentID);

        //     if (iDepartmentID && iDepartmentID !== "" && iDepartmentID !== "0") {
        //         $.ajax({
        //             url: '@Url.Action("GetAllSubDepartments", "ManageBCMEntities")',
        //             type: 'GET',
        //             data: { iDepartmentID: iDepartmentID },
        //             success: function (response) {
        //                debugger;
        //                let selectizeInstance = $('.ddlSubDepartment')[0].selectize;
        //                selectizeInstance.clear();
        //                selectizeInstance.clearOptions();
        //                selectizeInstance.addOption({ value: "0", text: "-- All SubDepartments --" });
        //                selectizeInstance.addItem("0");

        //                response && response.forEach(({ subFunctionID, subFunctionName }) => {
        //                        if (subFunctionID && subFunctionName) {
        //                             selectizeInstance.addOption({ value: subFunctionID, text: subFunctionName });
        //                        }
        //                });
        //             },
        //             // success: function (data) {
        //             //     console.log("SubDepartments data received:", data);
        //             //     var ddlSubDepartment = $('.ddlSubDepartment');
        //             //     ddlSubDepartment.empty();
        //             //     ddlSubDepartment.append('<option value="0">-- All Sub Departments --</option>');
        //             //     $.each(data, function (index, item) {
        //             //         ddlSubDepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>');
        //             //     });
        //             // },
        //             error: function (xhr, status, error) {
        //                 console.log('Error loading sub departments:', error);
        //                 console.error(xhr.status);
        //                 console.error(xhr.responseText);
        //             }
        //         });
        //     } else {
        //         // Clear dependent dropdown when no department is selected
        //         $('.ddlSubDepartment').empty().append('<option value="0">-- All Sub Departments --</option>');
        //     }
        // });

        $(document).ready(function () {
            $(document).on("change", ".ddlUnit", function () {
                var iUnitID = $(this).val();
                $.ajax({
                    url: '@Url.Action("GetAllDepartments", "ManageBCMEntities")',
                    type: 'GET',
                    data: { iUnitID: iUnitID },
                    success: function (response) {
                       debugger;
                       let selectizeInstance = $(".ddlDepartment")[0].selectize;
                       selectizeInstance.clear();
                       selectizeInstance.clearOptions();
                       selectizeInstance.addOption({ value: "0", text: "-- All Departments --" });
                       selectizeInstance.addItem("0");

                       response && response.forEach(({ departmentID, departmentName }) => {
                               if (departmentID && departmentName) {
                                    selectizeInstance.addOption({ value: departmentID, text: departmentName });
                               }
                       });
                    },
                    // success: function (data) {
                    //     console.log(data);
                    //     var department = $(".ddlDepartment");
                    //     department.empty();
                    //     department.append('<option value="0">-- All Departments --</option>');
                    //     $.each(data, function (index, item) {
                    //         department.append('<option value="' + item.departmentID + '">' + item.departmentName + '</option>')
                    //     });
                    // },
                    error: function (data) {
                        console.log('Error Is Invoked');
                    }
                });
            });


            $(document).on("change", ".ddlDepartment", function () {
                debugger;
                var iUnitID = $(this).val();
                if (iUnitID) {
                    $.ajax({
                        url: '@Url.Action("GetAllSubDepartments", "ManageBCMEntities")',
                        type: 'GET',
                        data: { iDepartmentID: iUnitID },
                        success: function (response) {
                            debugger;
                               let selectizeInstance = $('.ddlSubDepartment')[0].selectize;
                               selectizeInstance.clear();
                               selectizeInstance.clearOptions();
                               selectizeInstance.addOption({ value: "0", text: "-- All SubDepartments --" });
                               selectizeInstance.addItem("0");

                               response && response.forEach(({ subFunctionID, subFunctionName }) => {
                                       if (subFunctionID && subFunctionName) {
                                            selectizeInstance.addOption({ value: subFunctionID, text: subFunctionName });
                                       }
                               });
                        }
                        // success: function (data) {
                        //     var Subdepartment = $('.ddlSubDepartment');
                        //     Subdepartment.empty();
                        //     Subdepartment.append('<option value="0">-- All SubDepartments --</option>');
                        //     $.each(data, function (index, item) {
                        //         Subdepartment.append('<option value="' + item.subFunctionID + '">' + item.subFunctionName + '</option>')
                        //     });
                        // }
                    })
                }
            });
        });
    });
</script>


