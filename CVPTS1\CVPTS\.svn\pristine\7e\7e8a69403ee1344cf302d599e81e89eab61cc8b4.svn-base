﻿/*#DepartmentBIA_Chart,
#GRRS_Chart,
#ReviewProgress_Chart,
#ReviewMeetings_Chart,
#CriticalProcesses_Chart,
#OverallKPI_Chart,
#RiskHeatMap_Chart {
    width: 100%;
    height: 280px;
}*/
/*
#DepartmentBIA-Chart,
#BusinessProcessesBIA-Chart,
#OverAllKPIStatus-Chart
 {
    width: 100%;
    height: 250px;
}*/

.card {
    box-shadow: 0 0.125rem 2rem rgba(0, 0, 0, 0.075) !important;
}

#RiskHeatMap_Chart1 {
    width: 100%;
    height: 350px;
}

#Critical_NonCritical-Chart,
#ReviewinMeetings-Chart {
    width: 100%;
    height: 150px;
}

.chart-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 20px;
    border: none;
    height: 100%;
}

.chart-title {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    text-align: left;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.card-blue {
    background-image: url('../img/dashboard-img/unit.svg');
    background-repeat: no-repeat;
    background-size: cover;
}

.card-pink {
    background-image: url('../img/dashboard-img/department.svg');
    background-repeat: no-repeat;
    background-size: cover;
}

.card-orange {
    background-image: url('../img/dashboard-img/businessprocess.svg');
    background-repeat: no-repeat;
    background-size: cover;
}

.card-green {
    background-image: url('../img/dashboard-img/subdepartment.svg');
    background-repeat: no-repeat;
    background-size: cover;
}


.page_title {
    margin-bottom: 0px;
}
/*-- Calendar Style --*/
.avatar img {
    left: 0px;
    margin-left: -12px;
}

.fc .fc-toolbar.fc-header-toolbar {
    margin-bottom: 1em;
}

.fc .fc-button .fc-icon {
    font-size: 1em;
}

.fc .fc-button {
    font-size: 0.9em;
    padding: 3px 10px;
}

.fc .fc-toolbar-title {
    font-size: 1.5em;
}

/*-- End Calendar Style --*/

.Review-Tab {
    border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
    border-radius: var(--bs-border-radius-sm) !important;
}
    .Review-Tab .nav-link {
        padding: 4px 10px;
        font-size: 12px;
        border-radius: var(--bs-border-radius-sm) !important;
        color: var(--bs-gray-500);
    }
    .Review-Tab .nav-item .active {
        background: rgb(230, 56, 117);
        background: linear-gradient(114deg, rgba(230, 56, 117, 1) 4%, rgba(50, 2, 132, 1) 100%);
        border: none;
    }



.donut-chart {
    position: relative;
/*    width: 75px;
    height: 75px;*/
}

    .donut-chart svg {
        transform: rotate(-90deg);
    }

.circle-bg {
    fill: none;
    stroke: #d9d9d9;
    stroke-width: 1px;
}

.circle-progress {
    fill: none;
    stroke: #3dced3;
    stroke-width: 6px;
    stroke-linecap: round;
    stroke-dasharray: 314;
    stroke-dashoffset: calc(314 - (314 * var(--progress)) / 100);
    transition: stroke-dashoffset 0.6s ease;
}

.percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* font-size: 11px; */
    font-weight: bold;
    color: #333;
}
