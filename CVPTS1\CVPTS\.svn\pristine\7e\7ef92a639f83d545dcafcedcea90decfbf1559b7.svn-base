﻿@model BCM.BusinessClasses.Attachments
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    var selectedOrgID = ViewBag.selectedOrgID;
    var selectedOwnerID = ViewBag.selectedOwnerID;
}

<form asp-action="AddOtherBCMDocument" method="post" enctype="multipart/form-data">
    <div class="modal-body pt-0">
        <blockquote class="blockquote">Attach Other BCM Document</blockquote>
        <div class="row row-cols-2">
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Organization</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-organization"></i></span>
                        <select class="form-select form-control" asp-for="OrgID" aria-label="Default select example" required>
                            <option selected disabled value="">All Organizations</option>
                            @{
                                foreach (var objOrg in ViewBag.OrgName)
                                {
                                    <!option value="@objOrg.Value" @(objOrg.Value == selectedOrgID.ToString() ? "selected=\"selected=\"" : "")>@objOrg.Text</!option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Department</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-department"></i></span>
                        <select class="form-control" asp-for="DeptID" id="ddlDepartmentPartial" aria-label="Default select example">
                            <option value="0" selected>All Departments</option>
                            @{
                                foreach (var objDepartment in ViewBag.Department)
                                {
                                    <option value="@objDepartment.Value">@objDepartment.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Owner </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-owner"></i></span>
                        <select class="form-select form-select-sm" asp-for="UserId" required>
                            <option selected disabled value="">Select Owner</option>
                            @{
                                foreach (var objOwner in ViewBag.Resource)
                                {
                                    <!option value="@objOwner.Value" @(objOwner.Value == selectedOwnerID.ToString() ? "selected=\"selected=\"" : "")>@objOwner.Text</!option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Attach file</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-attach"></i></span>
                        <input type="file" class="form-control" asp-for="AttachementFile" required>
                    </div>
                    <div class="invalid-feedback">Upload Logo</div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Unit</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-unit"></i></span>
                        <select class="form-select form-control" id="ddlUnitPartial" aria-label="Default select example" asp-for="UnitID">
                            <option value="0" selected>All Units</option>
                            @{
                                foreach (var objUnits in ViewBag.Unit)
                                {
                                    <option value="@objUnits.Value">@objUnits.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Sub Department</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-subdepartment"></i></span>
                        <select class="form-select form-control" id="ddlSubDepartment" aria-label="Default select example" asp-for="DeptID">
                            <option selected>All Sub Departments</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Approver</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-approver"></i></span>
                        <select class="form-select form-select-sm" asp-for="ApproverID" required>
                            <option selected disabled value="">Select Approver</option>
                            @{
                                foreach (var objOwner in ViewBag.Resource)
                                {
                                    <option value="@objOwner.Value" )>@objOwner.Text</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Effective Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-calendar "></i></span>
                        <input class="form-control" type="date" asp-for="EffectiveDate" required />
                    </div>
                </div>

            </div>
            <div class="col-12">
                            <div class="form-group">
                    <label class="form-label">Description</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-description"></i></span>
                        <input class="form-control" type="text" asp-for="Description" required />
                    </div>
                </div>
            </div>
        </div>
        <div class="row row-cols-2">
            <div class="col">
                <h6 class="Sub-Title">Review Section</h6>
                <div class="form-group">
                    <label class="form-label">Last Review Date : NA</label>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label class="form-label">Next Review Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-review-date"></i></span>
                        <input class="form-control" type="date" asp-for="ReviewDate" required />
                    </div>
                </div>

            </div>
        </div>
    </div>
    
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div class="text-end">
            <div class="mb-2">
                <button type="button" id="btnEndReview" class="btn btn-primary btn-sm me-1" data-bs-dismiss="modal" hidden>End Review</button>
                <button type="submit" id="btnShowHistory" class="btn btn-primary btn-sm" hidden>Show History</button>
            </div>
            <div>
                <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</form>