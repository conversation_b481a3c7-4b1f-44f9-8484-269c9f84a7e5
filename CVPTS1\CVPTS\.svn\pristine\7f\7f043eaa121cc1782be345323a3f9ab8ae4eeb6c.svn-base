﻿@model IEnumerable<BCM.BusinessClasses.BIASection>
@{
    ViewData["Title"] = "BIASurveySection";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<style>
    .table > :not(caption) > * > * {
        padding: 7px 5px;
    }
</style>
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">
        BIA Survey Section
    </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <button class="btn icon-btn btn-primary btn-sm" id="btnCreate" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant  border-0 pe-2" style="height: calc(100vh - 115px);overflow: auto;">
    <div class="card-body">
        <div class="row g-3">
            @foreach (var Section in Model)
            {
                <div class="col-3 Incident-bg">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <ul class="ps-0 mb-0">
                                    <li class="list-group-item text-muted">Section Name</li>
                                    <li class="list-group-item fw-semibold text-primary">@Section.SectionName</li>
                                </ul>
                                <div class="d-flex align-items-center gap-2">
                                    <span class="btnEdit" role="button" data-bs-target="#CreateModal" data-bs-toggle="modal" data-id="@Section.SectionID"><i class="cv-edit align-middle"></i></span>
                                    <span role="button" data-bs-toggle="modal" class="btnDelete" data-id="@Section.SectionID" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>
                                </div>
                            </div>
                            <div class="mt-2">
                                <table class="table table-sm table-borderless mb-0 align-middle">
                                    <tr>
                                        <td class="text-muted">Section Weightage</td>
                                        <td>:</td>
                                        <td>@Section.SectionWeightage</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">Sequence</td>
                                        <td>:</td>
                                        <td>@Section.SequenceNo</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">Page</td>
                                        <td>:</td>
                                        <td>@Section.PageName</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Configuration Modal -->
    <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="Page-Title">BIA Survey Sections Configuration</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                </div>

            </div>
        </div>
    </div>
    <!--End Configuration Modal -->
    <!-- Delete Modal -->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    
                </div>
            </div>
        </div>
    </div>
    <!-- End Delete Modal -->
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#btnCreate').click(function () {
                //$.get('/BCMAdministration/BIASurveySection/AddBIASurveySection', function (data) {
                $.get('@Url.Action("AddBIASurveySection", "BIASurveySection")', function (data) {
                    $('.modal-body').html(data);
                    $('#CreateModal').modal('show');
                    $('#modelTitle').text('Page Configuration');
                });
            });

            $('.btnEdit').click(function () {
                var id = $(this).data('id');
                //$.get('/BCMAdministration/BIASurveySection/EditBIASurveySection/' + id, function (data) {
                $.get('@Url.Action("EditBIASurveySection", "BIASurveySection")' ,{ id:id }, function (data) {
                    $('.modal-body').html(data);
                    $('#NormalModal').modal('show');
                    $('#modelTitle').text('Update Page Configuration');
                });
            });

            $('.btnDelete').click(function () {
                var id = $(this).data('id');
                //$.get('/BCMAdministration/BIASurveySection/DeleteBIASurveySection/' + id, function (data) {
                $.get('@Url.Action("DeleteBIASurveySection", "BIASurveySection")' ,{ id:id }, function (data) {
                    $('.modal-body').html(data);
                    $('#DeleteModal').modal('show');
                    
                });
            });
        });
    </script>
}
