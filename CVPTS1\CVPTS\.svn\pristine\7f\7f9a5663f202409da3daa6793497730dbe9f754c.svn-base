﻿
.donut-chart {
    position: relative;
}

    .donut-chart svg {
        transform: rotate(-90deg);
    }

.circle-bg {
    fill: none;
    stroke: #d9d9d9;
    stroke-width: 1px;
}

.circle-progress {
    fill: none;
    stroke: #3dced3;
    stroke-width: 6px;
    stroke-linecap: round;
    stroke-dasharray: 314;
    stroke-dashoffset: calc(314 - (314 * var(--progress)) / 100);
    transition: stroke-dashoffset 0.6s ease;
}

.percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: #333;
}

#PieChart {
    width: 100%;
    height: 250px;
}

#DonutChart {
    width: 100%;
    height: 250px;
}
