// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

// Create chart instance
var chart = am4core.create("pci_score_chart", am4charts.XYChart);
if (chart.logo) {
    chart.logo.disabled = true;
}
//chart.scrollbarX = new am4core.Scrollbar();

// Add data
chart.data = [{
    "country": "Jan",
    "visits": 90
}, {
    "country": "Feb",
    "visits": 50
}, {
    "country": "Mar",
    "visits": 70
}, {
    "country": "Apr",
    "visits": 80
}, {
    "country": "May",
    "visits": 50
}, {
    "country": "Jun",
    "visits": 65
}, {
    "country": "Jul",
    "visits": 40
}, {
    "country": "Aug",
    "visits": 50
}, {
    "country": "Sep",
    "visits": 60
}, {
    "country": "Oct",
    "visits": 80
}, {
    "country": "Nov",
    "visits": 60
}, {
    "country": "Dec",
    "visits": 40
}];

// Create axes
var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
categoryAxis.dataFields.category = "country";
categoryAxis.renderer.grid.template.location = 0;
categoryAxis.renderer.minGridDistance = 30;
categoryAxis.renderer.labels.template.verticalCenter = "middle";
categoryAxis.tooltip.disabled = true;
categoryAxis.renderer.minHeight = 100;

var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
valueAxis.renderer.minWidth = 50;

// Create series
var series = chart.series.push(new am4charts.ColumnSeries());
series.sequencedInterpolation = true;
series.dataFields.valueY = "visits";
series.dataFields.categoryX = "country";
series.tooltipText = "[{categoryX}: bold]{valueY}[/]";
series.columns.template.strokeWidth = 0;

series.tooltip.pointerOrientation = "vertical";
series.columns.template.width = am4core.percent(30);
series.columns.template.column.cornerRadiusTopLeft = 50;
series.columns.template.column.cornerRadiusTopRight = 50;
series.columns.template.column.cornerRadiusBottomLeft = 50;
series.columns.template.column.cornerRadiusBottomRight = 50;
series.columns.template.column.fillOpacity = 1;

series.columns.template.adapter.add("fill", function (fill, target) {
    return chart.colors.getIndex(target.dataItem.index);
});

chart.padding(10, 0, -60, -20)
// Cursor
chart.cursor = new am4charts.XYCursor();