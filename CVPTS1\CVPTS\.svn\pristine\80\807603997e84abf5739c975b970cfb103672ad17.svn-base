﻿@model IEnumerable<BCM.BusinessClasses.BCMTrainingMaster>
@{
    ViewData["Title"] = "ManageBCMTrainingForm";
    Layout = "~/Views/Shared/_Layout.cshtml";
    int iIndex = 1;
}

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="Page-Header ">
    
    <div class="d-flex align-items-center justify-content-between">
        <h6 class="Page-Title">Manage BCM Training</h6>
        <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
            <div class="input-group w-30">
                <span class="input-group-text py-1"><i class="cv-organization"></i></span>
                <select id="orglist" class="form-select form-control" autocomplete="off" aria-label="Default select example">
                    <option selected value="0">-- All Organizations --</option>
                    @foreach (var objOrg in ViewBag.OrgInfo)
                    {
                        <option value="@objOrg.Value">@objOrg.Text</option>
                    }
                </select>
            </div>
            <div class="input-group w-30">
                <span class="input-group-text py-1"><i class="cv-unit"></i></span>
                <select id="unitlist" class="form-select form-control" autocomplete="off" aria-label="Default select example">
                    <option selected value="0">-- All Units --</option>
                    @foreach (var objUnit in ViewBag.OrgUnit)
                    {
                        <option value="@objUnit.Value">@objUnit.Text</option>
                    }
                </select>
            </div>
            <div class="input-group w-30">
                <span class="input-group-text py-1"><i class="cv-department"></i></span>
                <select id="departmentlist" class="form-select form-control" autocomplete="off" aria-label="Default select example">
                    <option selected value="0">-- All Departments --</option>
                    @foreach (var objDepartment in ViewBag.DepartmentInfo)
                    {
                        <option value="@objDepartment.Value">@objDepartment.Text</option>
                    }
                </select>
            </div>

            <div class="input-group Search-Input1">
                <span class="input-group-text py-1"><i class="cv-search"></i></span>
                <input id="search-inp" type="text" class="form-control" placeholder="Search">
            </div>
            <a class="btn btn-sm btn-primary" asp-action="BCMTrainingMasterConfiguration" asp-controller="BCMTrainingMasterConfiguration" asp-route-iID="@BCM.Security.Helper.CryptographyHelper.Encrypt("0")">
                Create
            </a>
           @*  <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="Modal"><i class="cv-Plus" title="Create New"></i>Create</button> *@
        </div>
    </div>

</div>

<div class="Page-Condant card border-0">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th class="SrNo_th">#</th>
                <th>Training Name</th>
                <th>Owner Details</th>
                <th>Approver Details</th>
                <th>Organization</th>
                <th>Unit</th>
                <th>Department</th>
                <th>Status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="tblBody">
            @if (Model != null)
            {
                @foreach (var objBCMTrainingMaster in Model)
                {
                    <tr>
                        <td>
                            @iIndex
                        </td>
                        <td>
                            <div class="d-grid">
                                @* <span class="fw-semibold text-warning">@objBCMTrainingMaster.TrainingCode</span> *@
                                <span class="fw-semibold">@objBCMTrainingMaster.TrainingName ( @objBCMTrainingMaster.Version )</span>
                                @* <span>Version  : <span class="text-info"> @objBCMTrainingMaster.Version</span></span> *@
                            </div>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    <tr>
                                        <td>@objBCMTrainingMaster.OwnerName</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    <tr>
                                        <td>@objBCMTrainingMaster.ApproverName</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>

                        <td>
                            @objBCMTrainingMaster.OrgName
                        </td>
                        <td>
                            @objBCMTrainingMaster.UnitName
                        </td>
                        <td>
                            @objBCMTrainingMaster.DepartmentName
                        </td>
                        <td>
                            @{
                                int statusId = Convert.ToInt32(@objBCMTrainingMaster.Status);
                            }
                            <span class="d-flex align-items-center @BCM.Shared.Utilities.ApprovalStatusWiseTextClass(statusId)">
                                <i class="@BCM.Shared.Utilities.ApprovalStatusWiseClass(statusId) me-2"></i>
                                @BCM.Shared.Utilities.ApprovalStatus(statusId)
                            </span>

                        </td>
                        <td>

                            <span class="btn-action publish" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@objBCMTrainingMaster.ID"><i class="cv-page-name align-middle ViewBIA" title="Publish"></i></span>

                            <a class="text-dark" asp-action="BCMTrainingMasterConfiguration" asp-controller="BCMTrainingMasterConfiguration" asp-route-iID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@objBCMTrainingMaster.ID.ToString())">
                                <i class="cv-edit" title="Edit"></i>
                            </a>
                            @* <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@objBCMTrainingMaster.ID"><i class="cv-edit" title="Edit"></i></span>
                             *@<span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@objBCMTrainingMaster.ID"><i class="cv-delete text-danger" title="Delete"></i></span>

                        </td>
                    </tr>
                    iIndex++;
                }
            }
        </tbody>
    </table>
</div>

<div class="modal fade" id="Modal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle"></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {

            $('#btnCreate').click(function () {
                $.get('@Url.Action("AddBCMTrainingMaster", "ManageBCMTrainingForm")', function (data) {
                    $('.modal-body').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('BCM Training Program Configuration');
                });
            });

            $(document).on('click', '.btnEdit', function () {
                var iId = $(this).data('id');
                $.get('@Url.Action("EditBCMTrainingMaster", "ManageBCMTrainingForm")', { iId: iId }, function (data) {
                    $('.modal-body').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Update BCM Training Program');
                });
            })

            $(document).on('click', '.btnDelete', function () {
                var iId = $(this).data('id');
                $.get('@Url.Action("DeleteBCMTrainingMaster", "ManageBCMTrainingForm")', { iId: iId }, function (data) {
                    $('#deleteBody').html(data);
                    $('#DeleteModal').modal('show');
                    $('#modaltitle').text('Delete BCM Training Program');
                });
            })

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        location.reload();
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

            $('#departmentlist').change(function () {
                var iDepartmentId = $(this).val();
                $.get('@Url.Action("GetDepartmentByID", "ManageBCMTrainingForm")', { iDepartmentId: iDepartmentId }, function (data) {
                    $('#example tbody').html(data);
                });
            });

            $('#unitlist').change(function () {
                var iUnitId = $(this).val();
                $.get('@Url.Action("GetUnitByID", "ManageBCMTrainingForm")', { iUnitId: iUnitId }, function (data) {
                    $('#example tbody').html(data);
                });
            });
        });
    </script>
}
