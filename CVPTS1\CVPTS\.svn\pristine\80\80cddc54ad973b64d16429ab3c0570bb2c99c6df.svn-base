﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Diagnostics;
using BCM.Security.Helper;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore.Metadata;
using System.Xml;
using Microsoft.AspNetCore.Components.Routing;
using Newtonsoft.Json;
using static BCM.Shared.BCPEnum;
using Microsoft.VisualStudio.Web.CodeGenerators.Mvc.Templates.Blazor;
using System.Data;
using System.Security.Cryptography;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMEntities.Controllers;
[Area("BCMEntities")]
public class ManageBCMEntitiesController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    SearchFields objsearch = new SearchFields();
    private readonly BCMMail _BCMMail;


    public ManageBCMEntitiesController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, BCMMail BCMMail) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _BCMMail = BCMMail;
    }

    [HttpGet]
    public IActionResult ManageBCMEntities()
    {
        List<BusinessProcessInfo> lstBusinessProcessInfo = new List<BusinessProcessInfo>();
        try
        {
            int iEntityId = 0;
            PopulateDropdown();

            lstBusinessProcessInfo = _ProcessSrv.GetBIASurveyListOrgUnitLevel(Convert.ToInt32(_UserDetails.OrgID),  iEntityId);


            ViewBag.UserOrgID = _UserDetails.OrgID.ToString();
            //lstBusinessProcess = _ProcessSrv.GetBIAProcess_OrgUnitLevel(_UserDetails.OrgID);
            //lstBusinessProcess = GetBusinessProcess(lstBusinessProcess);


            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    lstBusinessProcessInfo = _Utilities.FilterListByOrgGroupID(lstBusinessProcessInfo, _UserDetails.OrgGroupID);

                }
                else
                {

                    lstBusinessProcessInfo = _Utilities.FilterListByOrgID(lstBusinessProcessInfo, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstBusinessProcessInfo = _Utilities.FilterListByRoleID(lstBusinessProcessInfo, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                }
            }
            //if (IsDepartmentBIA != 0)
            //{
            //    lstBusinessProcessInfo = lstBusinessProcessInfo.Where(x => x.DepartmentID == iDepartmentID).ToList();
            //}


            ViewBag.TotalCount = lstBusinessProcessInfo.Count;
            ViewBag.UnderBCMCount = lstBusinessProcessInfo.Where(x => x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;
            ViewBag.CriticalCount = lstBusinessProcessInfo.Where(x => x.IsCritical == 1 && x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;
            ViewBag.NonCriticalCount = lstBusinessProcessInfo.Where(x => x.IsCritical == 0 && x.ProcessID > 0 && !string.IsNullOrEmpty(x.ProcessCode)).ToList().Count;

            // Remove the ProcessCode grouping as it's causing display issues
            // ViewBag.ProcessCode = lstBusinessProcess
            //                   .GroupBy(p => new { p.ProcessCode,p.ProcessName}).Select(g => g.First())
            //                   .ToList();
            ViewBag.lstBusinessProcess = lstBusinessProcessInfo;
            // ViewBag.BusinessProcessInfo = lstBusinessProcessInfo;

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }


        return View(lstBusinessProcessInfo);
    }

    [HttpGet]
    public IActionResult AddBCMEntities()
    {
        try
        {
            PopulateDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddBCMEntities", new BusinessProcessInfo());
    }

    [HttpPost]
    public IActionResult AddBCMEntities(BusinessProcessInfo objbusinessProcessInfo)
    {
        bool bSuccess = false;
        try
        {
            objbusinessProcessInfo = BuildData(objbusinessProcessInfo);
            int iProcessID = _ProcessSrv.BusinessProcessSave(objbusinessProcessInfo);
            bSuccess = iProcessID > 0 ? true : false;
            //if(iProcessID > 0)
            //{
            //    SendMailForAppproval(objbusinessProcessInfo);
            //}

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? "BCM Entity" + " Added Successfully" : "Failed To Add BCM Entity." });
        }
        return RedirectToAction("ManageBCMEntities");
    }

    [HttpGet]
    public IActionResult DeleteBcmEntity(int id, int iEntityTypeId )
    {
        BusinessProcessInfo objBusinessProcess = new BusinessProcessInfo();
        try
        {
            PopulateDropdown();
            objBusinessProcess = _ProcessSrv.GetBusinessProcessByProcessId(id, iEntityTypeId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteBcmEntity", objBusinessProcess);
    }


    [HttpPost]
    public IActionResult DeleteBcmEntity(BusinessProcessInfo objBusinessProcess)
    {
        bool bSuccess = false;
        try
        {
            PopulateDropdown();
            bSuccess = _ProcessSrv.BusinessProcessDeleteByID(objBusinessProcess.ProcessID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? "BCM Entity" + " Deleted Successfully" : "Failed To Delete BCM Entity." });
        }
        return RedirectToAction("ManageBCMEntities");
    }

    [HttpGet]
    public IActionResult EditBcmEntity(int iEntityTypeId, int iRecordId) //, int iProcessId, )
    {
        try
        {
            if (iEntityTypeId == (int)BCPEnum.EntityType.BusinessProcess)
            {
                BusinessProcessInfo objBusinessProcess = new BusinessProcessInfo();

                objBusinessProcess = _ProcessSrv.GetBusinessProcessMasterByProcessID(iRecordId);
                PopulateDropdownsForBusinessProces(objBusinessProcess.OrgGroupID, objBusinessProcess.OrgID, objBusinessProcess.UnitID,
                    objBusinessProcess.DepartmentID, objBusinessProcess.SubfunctionID);
                objBusinessProcess.IsButtonVisible = 1;
                objBusinessProcess.EntityTypeID = iEntityTypeId;

                return PartialView("~/Areas/BCMProcessBIA/Views/ManageBusinessProcesses/_EditBusinessProcess.cshtml", objBusinessProcess);
            }
            else if (iEntityTypeId == (int)BCPEnum.EntityType.Facilities)
            {
                FacilityAndHoliday objFacilityInfo = new FacilityAndHoliday();

                if (iRecordId > 0)
                {
                    objFacilityInfo.Facility = _ProcessSrv.GetFacilitiesById(iRecordId);
                    //Facility_HolidayColl objFacilityColl = new Facility_HolidayColl();
                    objFacilityInfo.Facility_Holiday = _ProcessSrv.GetAllFacility_Holidays_ByFacilityID(iRecordId);
                    List<TypeMasterInfo> TypeMasterInfo = _ProcessSrv.GetTypeInfoByEntityID(iEntityTypeId);
                    ViewBag.typeMasterInfo = TypeMasterInfo;
                    ViewBag.facilityList = _Utilities.GetFacilityListByUnitID(objFacilityInfo.Facility.FacilityUnitID);
                    PopulateDropDownsForFacility();
                    List<LocationMaster> lstLocation = _ProcessSrv.GetLocationListByUnit(_UserDetails.OrgID, Convert.ToInt32(objFacilityInfo.Facility.FacilityUnitID));
                    ViewBag.LocationList = lstLocation;
                    List<ResourcesInfo> objresource = _ProcessSrv.GetAllResourcesList();
                    ViewBag.ResourceList = objresource;
                    objFacilityInfo.Facility.IsButtonVisible = 1;
                    objFacilityInfo.Facility.EntityTypeID = iEntityTypeId;


                }
                //return RedirectToAction("EditBCMFacility", "ManageFacility", new { Area = "BCMFacility", id = objBusinessProcessInfo.RecordID.ToString(), action = "E" });
                return PartialView("~/Areas/BCMFacility/Views/ManageFacility/_EditBCMFacility.cshtml", objFacilityInfo);
            }
            else if (iEntityTypeId == (int)BCPEnum.EntityType.Location)
            {
                LocationMaster ObjLocationMaster = new LocationMaster();

                PopulateDropdownForLocation();

                ObjLocationMaster = _ProcessSrv.GetLocationById(iRecordId);
                ObjLocationMaster.IsButtonVisible = 1;
                ObjLocationMaster.EntityTypeID = iEntityTypeId;



                return PartialView("~/Areas/BCMAdministration/Views/ManageLocation/_EditLocation.cshtml", ObjLocationMaster);

            }
            else if (iEntityTypeId == (int)BCPEnum.EntityType.People)
            {

                ResourcesInfo objResourcesInfo = new ResourcesInfo();

                PopulateDropdownsForResource();
                objResourcesInfo = _ProcessSrv.GetBCMResourcesDetailsByResourceID(iRecordId);

                ViewBag.Roleslist = _ProcessSrv.GetAllRoleList().ToList();

                objResourcesInfo.IsButtonVisible = 1;
                objResourcesInfo.EntityTypeID = iEntityTypeId;

                //return RedirectToAction("EditBCMResource", "ManageBCMResource", new { Area = "BCMResourceManagement", id = objBusinessProcessInfo.RecordID.ToString(), action = "E" });
                return PartialView("~/Areas/BCMResourceManagement/Views/ManageBCMResource/_EditBCMResource.cshtml", objResourcesInfo);


            }
            else if (iEntityTypeId == (int)BCPEnum.EntityType.BCMEntity)
            {
                var objOtherBCMEntities = new OtherBCMEntities();

                PopulateDropdownForOtherBCMEntity();
                ViewBag.SelectedOrgID = _UserDetails.OrgID;
                objOtherBCMEntities = _ProcessSrv.OtherBCMEntitiesGetByBCMEntityID(iRecordId.ToString());
                objOtherBCMEntities.EntityTypeID = iEntityTypeId;
                objOtherBCMEntities.IsButtonVisible = 1;
                //return RedirectToAction("EditOtherBCMEntities", "ManageOtherBCMEntities", new { Area = "BCMEntities", iId = objBusinessProcessInfo.RecordID.ToString(), action = "E" });
                return PartialView("~/Areas/BCMEntities/Views/ManageOtherBCMEntities/_EditOtherBCMEntities.cshtml", objOtherBCMEntities);
            }
            else if (iEntityTypeId == (int)BCPEnum.EntityType.ThirdParty)
            {
                CompanyMasterInfo objVendorInfo = new CompanyMasterInfo();
                PopulateDropDownForThirdParty();
                objVendorInfo = _ProcessSrv.GetCompanyByCopmanyId(iRecordId);
                objVendorInfo.IsButtonVisible = 1;
                objVendorInfo.EntityTypeID = iEntityTypeId;
                //return RedirectToAction("EditBCMVendor", "ManageVendor", new { Area = "BCMThirdParty", id = objBusinessProcessInfo.RecordID.ToString(), action = "E" });
                return PartialView("~/Areas/BCMThirdParty/Views/ManageVendor/_EditVendor.cshtml", objVendorInfo);
            }
            else if (iEntityTypeId == (int)BCPEnum.EntityType.Application)
            {

                Applications objApplication = new Applications();

                PopulateDropDownForAppliction();
                objApplication = _ProcessSrv.GetApplicationByApplicationId(iRecordId);
                objApplication.IsButtonVisible = 1;
                objApplication.EntityTypeID = iEntityTypeId;

                //return RedirectToAction("EditBCMApplication", "ManageApplication", new { Area = "BCMApplicationBIA", id = objBusinessProcessInfo.RecordID.ToString(), action = "E" });
                return PartialView("~/Areas/BCMApplicationBIA/Views/ManageApplication/_EditApplication.cshtml", objApplication);


            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("Index"); // Fallback action if the URL wasn't set
    }

    [HttpPost]
    public IActionResult GetProcessId(int iId , int iEntityTypeId)
    {
        int iProcessID  = _ProcessSrv.GetBusinessProcessByTypenRecordID(iId, iEntityTypeId);

        //return RedirectToAction("BusinessProcessForm", "BusinessProcessForm", new { area = "BCMProcessBIA", strRecordID = BCM.Security.Helper.CryptographyHelper.Encrypt(iId.ToString()) , strEntityTypeID = BCM.Security.Helper.CryptographyHelper.Encrypt(((int)BCPEnum.EntityType.Location).ToString()), strProcessID = BCM.Security.Helper.CryptographyHelper.Encrypt(iProcessID.ToString()) });

        var redirectUrl = Url.Action("BusinessProcessForm", "BusinessProcessForm", new
        {
            area = "BCMProcessBIA",
            strRecordID = BCM.Security.Helper.CryptographyHelper.Encrypt(iId.ToString()),
            strEntityTypeID = BCM.Security.Helper.CryptographyHelper.Encrypt(iEntityTypeId.ToString()),
            strProcessID = BCM.Security.Helper.CryptographyHelper.Encrypt(iProcessID.ToString())
        });

        return Json(new { redirectUrl = redirectUrl });
    }

    public void PopulateDropdown(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
           // ViewBag.DepartmentInfo = new SelectList(_Utilities.GetDepartmentAllListForDropdown(), "DepartmentID", "DepartmentName");
            ViewBag.DepartmentInfo = new SelectList(_Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() :iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString() == "0" ? _UserDetails.UnitID.ToString() : iUnitID.ToString()), "DepartmentID", "DepartmentName");
            ViewBag.SubFunction = new SelectList(_Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(),_UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID.ToString() == "0" ? _UserDetails.DepartmentID.ToString() : iDepartmentID.ToString()), "SubFunctionID", "SubFunctionName");
            //ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString()), "Id", "OrganizationName");
            ViewBag.OrgInfo = new SelectList(_Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString()), "Id", "OrganizationName");
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetResources(_UserDetails.OrgID), "ResourceId", "ResourceName");
            //ViewBag.OrgUnit = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails.OrgID), "UnitID", "UnitName");
            ViewBag.OrgUnit = new SelectList(_Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString()), "UnitID", "UnitName");


            ViewBag.BcmEntities = new SelectList(_Utilities.PopulateBCMEntities(),"BCMEntityID", "BCMEntityName");
            ViewBag.TimeUnit = new SelectList(_Utilities.PopulateStepTimeUnit(), "ID","Name");
            ViewBag.BiaProfiles = new SelectList(FilteredProfiles(), "ID", "ProfileName");
            ViewBag.lstParameterProfile = _Utilities.GetBusinessParameterProfileList(Convert.ToInt32(_UserDetails.OrgID));
            ViewBag.lstBIAProfile = _ProcessSrv.BIAProfileMasterListAll();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }


    private BusinessProcessInfo BuildData(BusinessProcessInfo objBusinessProcessInfo)
    {
        BusinessProcessInfo objBusinesProcess = new BusinessProcessInfo();

        objBusinesProcess = _ProcessSrv.GetBusinessProcessByProcessId(Convert.ToInt32(Convert.ToString(objBusinessProcessInfo.ProcessID) == "" ? "0" : objBusinessProcessInfo.ProcessID), 1);
        objBusinesProcess.ProcessDescription = objBusinessProcessInfo.ProcessDescription;
        objBusinesProcess.EntityTypeID = objBusinessProcessInfo.EntityTypeID;
        objBusinesProcess.RecordID = objBusinessProcessInfo.RecordID;
        objBusinesProcess.ProcessID = objBusinessProcessInfo.ProcessID;
        objBusinesProcess.IsActive = 1;
        objBusinesProcess.Version = "1.0";
        objBusinesProcess.ProcessName = objBusinessProcessInfo.ProcessName;
        objBusinesProcess.ProcessOwnerID = objBusinessProcessInfo.ProcessOwnerID;
        objBusinesProcess.AltProcessOwnerID = objBusinessProcessInfo.AltProcessOwnerID;
        objBusinesProcess.DepartmentID = objBusinessProcessInfo.DepartmentID;
        objBusinesProcess.SubfunctionID = objBusinessProcessInfo.SubfunctionID;
        objBusinesProcess.UnitID = objBusinessProcessInfo.UnitID;
        objBusinesProcess.Comments = objBusinessProcessInfo.Comments;
        objBusinesProcess.ProcessCode = objBusinessProcessInfo.ProcessCode;
        objBusinesProcess.RTO = string.IsNullOrEmpty(objBusinessProcessInfo.RTO) ? "0" : objBusinessProcessInfo.RTO;
        objBusinesProcess.RPO = string.IsNullOrEmpty(objBusinessProcessInfo.RPO) ? "0" : GetRPOInMinutes(objBusinessProcessInfo.RPO, objBusinessProcessInfo.RPOUnit);
        objBusinesProcess.RPOUnit = objBusinessProcessInfo.RPOUnit;
        objBusinesProcess.IsEffective = 1;
        //objBusinesProcess.Version = !string.IsNullOrEmpty(objBusinessProcessInfo.Version.ToString()) ? objBusinessProcessInfo.Version : "1.0";

        if (objBusinessProcessInfo != null && objBusinessProcessInfo.Version != null)
        {
            objBusinesProcess.Version = !string.IsNullOrEmpty(objBusinessProcessInfo.Version) ? objBusinessProcessInfo.Version : "1.0";
        }
        else
        {
            objBusinesProcess.Version = "1.0";
        }
        //objBusinesProcess.ChangedBy = _UserDetails.UserID;
        objBusinesProcess.CreatedBy = _UserDetails.UserID;
        objBusinesProcess.IsCritical = 0;
        objBusinesProcess.OrgID = objBusinessProcessInfo.OrgID;
        objBusinesProcess.ApproverID = objBusinessProcessInfo.ApproverID;
        objBusinesProcess.ProfileID = objBusinessProcessInfo.ProfileID;
        objBusinesProcess.BPProfileID = objBusinessProcessInfo.BPProfileID;
        objBusinesProcess.TeamSize = !string.IsNullOrEmpty(objBusinessProcessInfo.TeamSize.ToString()) ? objBusinessProcessInfo.TeamSize : 0;
        objBusinesProcess.PeakTranVolume = !string.IsNullOrEmpty(objBusinessProcessInfo.PeakTranVolume) ? Convert.ToString(objBusinessProcessInfo.PeakTranVolume) : "0";
        objBusinesProcess.Comments = objBusinessProcessInfo.Comments;
        objBusinesProcess.ProcessCode = objBusinessProcessInfo.ProcessCode;
        if (objBusinesProcess.LastReviewDate.ToString() == "01-01-0001 00:00:00")
        {
            objBusinesProcess.LastReviewDate = null;
        }
        if (objBusinesProcess.ReviewDate.ToString() == "01-01-1754 00:00:00")
        {
            objBusinesProcess.ReviewDate = DateTime.MinValue;
        }
        else
        {
            objBusinesProcess.ReviewDate = objBusinessProcessInfo.ReviewDate;
        }

        ResourcesInfo objResourcesInfo = _ProcessSrv.GetResourceMaster_ByUserID(Convert.ToString(objBusinessProcessInfo.ApproverID));
        if (objResourcesInfo != null)
        {
            objBusinesProcess.ApproverEmail = objResourcesInfo.CompanyEmail;
            //ViewState["ApproverMail"] = dt.Rows[0]["CompanyEmail"];
        }
        return objBusinesProcess;
    }

    private string GetRPOInMinutes(string iRPO, string TimeUnitSelectedValue)
    {
        String RPO = string.Empty;
        int TimeUnit = Convert.ToInt32(TimeUnitSelectedValue);
        switch (TimeUnit)
        {
            case 1:
                RPO = iRPO;
                break;
            case 2:
                RPO = (Convert.ToInt32(iRPO) * 60).ToString();
                break;
            case 3:
                RPO = (Convert.ToInt32(iRPO) * 60 * 24).ToString();
                break;
            case 4:
                RPO = (Convert.ToInt32(iRPO) * 60 * 24 * 30).ToString();
                break;

        }
        return RPO;
    }


    #region SvaeMethodsForBCMEntity
    [HttpPost]
    public IActionResult EditBCMVendor(CompanyMasterInfo objCompanyMaster)
    {
        try
        {
            objCompanyMaster.EntityTypeID = Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.ThirdParty);
            objCompanyMaster.CreatedBy = _UserDetails.UserID;
            objCompanyMaster.UpdatedBy = _UserDetails.UserID;
            int iSucess = 0;
            iSucess = _ProcessSrv.CompanyMasterSave(objCompanyMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMEntities");
    }

    [HttpPost]
    public IActionResult EditOtherBCMEntities(OtherBCMEntities objOtherBCMEntities)
    {
        try
        {
            PopulateDropdownForOtherBCMEntity();
            objOtherBCMEntities = BuildDataForOtherBCMEntity("", objOtherBCMEntities);
            bool bSuccess = _ProcessSrv.OtherBCMEntitiesSave(objOtherBCMEntities);
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMEntities");
    }

    [HttpPost]
    public IActionResult EditBCMApplication(Applications objApplicationInfo)
    {
        try
        {
            int iSucess = 0;
            iSucess = _ProcessSrv.ApplicationUpdate(objApplicationInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMEntities");
    }

    [HttpPost]
    public IActionResult EditBCMFacility(FacilityAndHoliday objFacilityAndHolidayInfo)
    {
        LocationMaster objLocationInfo = new LocationMaster();
        try
        {
            objLocationInfo = _ProcessSrv.GetLocationById(Convert.ToInt32(objFacilityAndHolidayInfo.Facility.Location));
            //int iFacilityID = objFacilityAndHolidayInfo.Facility.FacilityID;
            objFacilityAndHolidayInfo.Facility_Holiday.FacilityID = objFacilityAndHolidayInfo.Facility.FacilityID;

            objFacilityAndHolidayInfo.Facility.FacilityIsActive = 1;
            objFacilityAndHolidayInfo.Facility.EntityTypeID = objFacilityAndHolidayInfo.Facility.FacilityID;

            bool facilityID = _ProcessSrv.FacilitiesUpdate(objFacilityAndHolidayInfo.Facility);
            if (objFacilityAndHolidayInfo.Facility_Holiday.ID == 0)
            {
                objFacilityAndHolidayInfo.Facility_Holiday.CreatedBy = _UserDetails.UserID;
                int Holiday = _ProcessSrv.Facility_Holiday_Save(objFacilityAndHolidayInfo.Facility_Holiday);
            }
            else
            {
                objFacilityAndHolidayInfo.Facility_Holiday.UpdatedBy = _UserDetails.UserID;
                objFacilityAndHolidayInfo.Facility_Holiday.IsActive = 1;
                int Result_Fac_Holiday = _ProcessSrv.Facility_Holiday_Update(objFacilityAndHolidayInfo.Facility_Holiday);
            }

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMEntities");
    }

    [HttpPost]
    public IActionResult EditBusinessProcess(BusinessProcessInfo objBusinessProcessInfo)
    {
        try
        {
            //PopulateDropdownsForBusinessProces();
            objBusinessProcessInfo = BuildData(objBusinessProcessInfo);
            objBusinessProcessInfo.TimeOperationF = "0 0/  * * ? ";
            objBusinessProcessInfo.TimeOperationT = "0 0/  * * ? ";
            objBusinessProcessInfo.PeakTranVolume = string.Empty;
            objBusinessProcessInfo.PeakPeriod = "-- Select --";
            objBusinessProcessInfo.SPOF = string.Empty;
            objBusinessProcessInfo.PossibleSol = string.Empty;
            objBusinessProcessInfo.Field1 = string.Empty;
            objBusinessProcessInfo.Field2 = string.Empty;
            objBusinessProcessInfo.Field3 = string.Empty;
            objBusinessProcessInfo.Field4 = string.Empty;

            objBusinessProcessInfo.WorkType = 0;
            objBusinessProcessInfo.HandOffs = 0;
            objBusinessProcessInfo.TeamSize = 0;

            objBusinessProcessInfo.EntityTypeID = (int)BCPEnum.EntityType.BusinessProcess;
            bool success = _ProcessSrv.BusinessProcessMasterUpdate(objBusinessProcessInfo);
        }
        catch (Exception ex)
        {

            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMEntities");
    }

    [HttpPost]
    public IActionResult EditLocation(LocationMaster ObjLocationMaster)
    {
        try
        {
            ObjLocationMaster.IsActive = 1;
            ObjLocationMaster.UpdatedAt = DateTime.Now;
            ObjLocationMaster.UpdatedBy = _UserDetails.UserID;
            ObjLocationMaster.EntityTypeID = (int)BCPEnum.EntityType.Location;
            var entities = _ProcessSrv.LocationmasterUpdate(ObjLocationMaster);

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMEntities");
    }

    [HttpPost]
    public IActionResult EditBCMResource([FromBody] ResourcesInfo objResourcesInfo)
    {
        int iSuccess = 0;
        bool success = false;
        try
        {
            // Add validation logic for edit as well
            if (!string.IsNullOrEmpty(objResourcesInfo.EffectiveStartDate.ToString()) && !string.IsNullOrEmpty(objResourcesInfo.EffectiveEndDate.ToString()))
            {
                DateTime DtStartDate = objResourcesInfo.EffectiveStartDate.Date;
                DateTime DtEndDate = objResourcesInfo.EffectiveEndDate.Date;

                int result = DateTime.Compare(DtStartDate, DtEndDate);
                if (result >= 0)
                {
                    return Json(new { success = false, message = "End date should be greater than start date" });
                }
            }

            if (IsExitsNameByCompanyEmail(objResourcesInfo.CompanyEmail, objResourcesInfo.ResourceId))
            {
                return Json(new { success = false, message = "Company email already exists" });
            }

            if (IsExitsResourceMobile(objResourcesInfo.MobilePhone, objResourcesInfo.ResourceId))
            {
                return Json(new { success = false, message = "Mobile phone already exists" });
            }

            var objResource = BuildDataBaseEntities(objResourcesInfo);
            if (objResource != null)
            {
                iSuccess = _ProcessSrv.UserRole_ResourceDelete(objResource);

                if (_ProcessSrv.ResourcesUpdate(objResource))
                {
                    //string roles = objResourcesInfo.UserRole == null ? string.Empty : objResourcesInfo.UserRole;
                    //string[] strRoleList = roles.Split(',');

                    List<int>? strRoleList = objResourcesInfo.role;
                    foreach (int obj in strRoleList)
                    {
                        var objResources = new ResourcesInfo();
                        objResources.ResourceId = Convert.ToInt32(objResourcesInfo.ResourceId);
                        objResources.RoleList = Convert.ToString(obj);
                        var rt = _ProcessSrv.UserRoleSave_Update(objResources);
                    }

                    List<OrgUnit?> lstOrgUnit = new List<OrgUnit?>();
                    OrgUnit objOrgUnititem = new OrgUnit();
                    List<DepartmentInfo> lstDepartmentInfo = new List<DepartmentInfo>();
                    List<SubFunction> lstSubfunction = new List<SubFunction>();

                    // Delete only the Org Hierarchiaccess not the record ID Row of explicit given rights
                    //Boolean resultDel = _IProcessSrv.DeleteOrganizationalAccessByUserID(Convert.ToInt32(objResourcesInfo.ResourceId), Convert.ToInt32(_UserDetails.OrgID.ToString()));

                    //if (resultDel)
                    //{
                    if (objResourcesInfo.OrgID > 0 && objResourcesInfo.UnitID == 0)//OrgLevelUser
                    {
                        #region "OrgLevelUser"
                        lstOrgUnit = _ProcessSrv.GetOrganizationUnitListByOrgID(Convert.ToInt32(objResourcesInfo.OrgID));
                        if (lstOrgUnit != null)
                        {
                            foreach (OrgUnit? objOrgUnit in lstOrgUnit)
                            {
                                if (lstDepartmentInfo != null && lstDepartmentInfo.Count > 0 && objOrgUnit != null)
                                {
                                    lstDepartmentInfo = _ProcessSrv.GetDepartmentByUnitId(Convert.ToInt32(objOrgUnit.UnitID));

                                    foreach (DepartmentInfo objDepartment in lstDepartmentInfo)
                                    {
                                        lstSubfunction = _ProcessSrv.GetSubFunctionListByFunctionID(Convert.ToString(objDepartment.DepartmentID));
                                        if (lstSubfunction != null && lstSubfunction.Count > 0)
                                        {
                                            foreach (SubFunction objSubfunction in lstSubfunction)
                                            {
                                                List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                                                OrgRoleRights objrights = new OrgRoleRights();

                                                objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                                                objrights.CreatedBy = _UserDetails.UserID;
                                                objrights.UpdatedBy = _UserDetails.UserID;
                                                objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                                                objrights.OrgID = objResourcesInfo.OrgID;
                                                objrights.UnitID = objResourcesInfo.UnitID;
                                                objrights.DeptID = objResourcesInfo.UnitID;
                                                objrights.SubDeptID = objResourcesInfo.SubFunctionID;

                                                objrightscoll.Add(objrights);
                                                iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                                            }
                                        }
                                        else
                                        {
                                            List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                                            OrgRoleRights objrights = new OrgRoleRights();

                                            objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                                            objrights.CreatedBy = _UserDetails.UserID;
                                            objrights.UpdatedBy = _UserDetails.UserID;
                                            objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                                            objrights.OrgID = objResourcesInfo.OrgID;
                                            objrights.UnitID = objResourcesInfo.UnitID;
                                            objrights.DeptID = objResourcesInfo.UnitID;
                                            objrightscoll.Add(objrights);
                                            iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                                        }
                                    }
                                }
                                else
                                {
                                    List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                                    OrgRoleRights objrights = new OrgRoleRights();

                                    objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                                    objrights.CreatedBy = _UserDetails.UserID;
                                    objrights.UpdatedBy = _UserDetails.UserID;
                                    objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                                    objrights.OrgID = objResourcesInfo.OrgID;
                                    objrights.UnitID = objResourcesInfo.UnitID;
                                    objrightscoll.Add(objrights);
                                    iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                                }
                            }
                        }
                        else
                        {
                            List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                            OrgRoleRights objrights = new OrgRoleRights();

                            objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                            objrights.CreatedBy = _UserDetails.UserID;
                            objrights.UpdatedBy = _UserDetails.UserID;
                            objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                            objrights.OrgID = objResourcesInfo.OrgID;

                            objrightscoll.Add(objrights);
                            iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                        }
                        #endregion
                    }
                    else if (objResourcesInfo.OrgID > 0 && objResourcesInfo.UnitID > 0 && objResourcesInfo.DepartmentID == 0)
                    {
                        #region "UnitlLevelUser"
                        lstDepartmentInfo = _ProcessSrv.GetDepartmentByUnitId(Convert.ToInt32(objResourcesInfo.UnitID));
                        if (lstDepartmentInfo != null)
                        {
                            foreach (DepartmentInfo objDepartment in lstDepartmentInfo)
                            {
                                lstSubfunction = _ProcessSrv.GetSubFunctionListByFunctionID(Convert.ToString(objDepartment.DepartmentID));
                                if (lstSubfunction != null)
                                {
                                    foreach (SubFunction objSubfunction in lstSubfunction)
                                    {
                                        List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                                        OrgRoleRights objrights = new OrgRoleRights();

                                        objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                                        objrights.CreatedBy = _UserDetails.UserID;
                                        objrights.UpdatedBy = _UserDetails.UserID;
                                        objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                                        objrights.OrgID = objResourcesInfo.OrgID;
                                        objrights.UnitID = objResourcesInfo.UnitID;
                                        objrights.DeptID = objResourcesInfo.DepartmentID;
                                        objrights.SubDeptID = objResourcesInfo.SubFunctionID;

                                        objrightscoll.Add(objrights);
                                        iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                                    }
                                }
                                else
                                {
                                    List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                                    OrgRoleRights objrights = new OrgRoleRights();

                                    objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                                    objrights.CreatedBy = _UserDetails.UserID;
                                    objrights.UpdatedBy = _UserDetails.UserID;
                                    objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                                    objrights.OrgID = objResourcesInfo.OrgID;
                                    objrights.UnitID = objResourcesInfo.UnitID;
                                    objrights.DeptID = objResourcesInfo.UnitID;

                                    objrightscoll.Add(objrights);
                                    iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                                }
                            }
                        }
                        else
                        {
                            List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                            OrgRoleRights objrights = new OrgRoleRights();

                            objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                            objrights.CreatedBy = _UserDetails.UserID;
                            objrights.UpdatedBy = _UserDetails.UserID;
                            objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                            objrights.OrgID = objResourcesInfo.OrgID;
                            objrights.UnitID = objResourcesInfo.UnitID;

                            objrightscoll.Add(objrights);
                            iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                        }
                        #endregion
                    }
                    else if (objResourcesInfo.OrgID > 0 && objResourcesInfo.UnitID > 0 && objResourcesInfo.DepartmentID > 0 && objResourcesInfo.SubFunctionID == 0)
                    {
                        #region "DepartmentLevelUser"

                        lstSubfunction = _ProcessSrv.GetSubFunctionListByFunctionID(Convert.ToString(objResourcesInfo.DepartmentID));
                        if (lstSubfunction != null && lstSubfunction.Count > 0)
                        {
                            foreach (SubFunction objSubfunction in lstSubfunction)
                            {
                                List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                                OrgRoleRights objrights = new OrgRoleRights();

                                objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                                objrights.CreatedBy = _UserDetails.UserID;
                                objrights.UpdatedBy = _UserDetails.UserID;
                                objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                                objrights.OrgID = objResourcesInfo.OrgID;
                                objrights.UnitID = objResourcesInfo.UnitID;
                                objrights.DeptID = objResourcesInfo.UnitID;
                                objrights.SubDeptID = objResourcesInfo.SubFunctionID;

                                objrightscoll.Add(objrights);
                                iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                            }
                        }
                        else
                        {
                            List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                            OrgRoleRights objrights = new OrgRoleRights();

                            objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                            objrights.CreatedBy = _UserDetails.UserID;
                            objrights.UpdatedBy = _UserDetails.UserID;
                            objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                            objrights.OrgID = objResourcesInfo.OrgID;
                            objrights.UnitID = objResourcesInfo.UnitID;
                            objrights.DeptID = objResourcesInfo.UnitID;

                            objrightscoll.Add(objrights);
                            iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                        }

                        #endregion
                    }
                    else if (objResourcesInfo.OrgID > 0 && objResourcesInfo.UnitID > 0 && objResourcesInfo.DepartmentID > 0)//SubDeptLevelUser
                    {
                        #region "SubDeptLevelUser"
                        List<OrgRoleRights> objrightscoll = new List<OrgRoleRights>();
                        OrgRoleRights objrights = new OrgRoleRights();

                        objrights.UserID = Convert.ToInt32(objResource.ResourceId);
                        objrights.CreatedBy = _UserDetails.UserID;
                        objrights.UpdatedBy = _UserDetails.UserID;
                        objrights.OrgGroupID = objResourcesInfo.OrgGroupID;
                        objrights.OrgID = objResourcesInfo.OrgID;
                        objrights.UnitID = objResourcesInfo.UnitID;
                        objrights.DeptID = objResourcesInfo.DepartmentID;
                        objrights.SubDeptID = objResourcesInfo.SubFunctionID;

                        objrightscoll.Add(objrights);
                        iSuccess = _ProcessSrv.OrganizationalAccessCollSave(objrightscoll);
                        #endregion
                    }
                    //}
                    success = true;
                }
            }
            else
            {
                success = false;
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            success = false;
        }

        // Return JSON response for AJAX calls
        return Json(new { success = success, message = success ? "Resource updated successfully" : "Error updating resource" });
    }

    private bool IsExitsNameByCompanyEmail(string email, int resourceId)
    {
        try
        {
            if (string.IsNullOrEmpty(email))
                return false;

            var existingResource = _ProcessSrv.GetResourcesList(_UserDetails.OrgID)
                .FirstOrDefault(r => r.CompanyEmail.Equals(email, StringComparison.OrdinalIgnoreCase)
                                && r.ResourceId != resourceId);

            return existingResource != null;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return false;
        }
    }

    private bool IsExitsResourceMobile(string mobile, int resourceId)
    {
        try
        {
            if (string.IsNullOrEmpty(mobile))
                return false;

            var existingResource = _ProcessSrv.GetResourcesList(_UserDetails.OrgID)
                .FirstOrDefault(r => r.MobilePhone.Equals(mobile, StringComparison.OrdinalIgnoreCase)
                                && r.ResourceId != resourceId);

            return existingResource != null;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return false;
        }
    }

    protected ResourcesInfo BuildDataBaseEntities(ResourcesInfo objResourcesInfo)
    {
        byte[] GuidSize = new byte[16];
        Random rd = new Random();
        rd.NextBytes(GuidSize);
        System.Guid guid = new Guid(GuidSize);
        string guidtxt = guid.ToString();

        ResourcesInfo _objResourcesInfo = new ResourcesInfo();

        _objResourcesInfo.ResourceId = objResourcesInfo.ResourceId;
        _objResourcesInfo.EntityId = objResourcesInfo.EntityId;
        _objResourcesInfo.FName = objResourcesInfo.FName;
        _objResourcesInfo.ResourceName = objResourcesInfo.ResourceName;
        _objResourcesInfo.MName = objResourcesInfo.MName;
        _objResourcesInfo.LName = objResourcesInfo.LName;
        _objResourcesInfo.ResourceTitle = objResourcesInfo.ResourceTitle;
        _objResourcesInfo.StatusID = objResourcesInfo.StatusID;
        _objResourcesInfo.ResourceTypeID = objResourcesInfo.ResourceTypeID;
        _objResourcesInfo.BusinessPhone = objResourcesInfo.BusinessPhone;
        _objResourcesInfo.HomePhone = objResourcesInfo.HomePhone;
        _objResourcesInfo.MobilePhone = objResourcesInfo.MobilePhone;
        _objResourcesInfo.Fax = objResourcesInfo.Fax;
        _objResourcesInfo.OtherPhone = objResourcesInfo.OtherPhone;
        _objResourcesInfo.CompanyEmail = objResourcesInfo.CompanyEmail;
        _objResourcesInfo.OtherEmail = objResourcesInfo.OtherEmail;
        _objResourcesInfo.Address = objResourcesInfo.Address;
        _objResourcesInfo.City = objResourcesInfo.City;
        _objResourcesInfo.State = objResourcesInfo.State;
        _objResourcesInfo.PinCode = objResourcesInfo.PinCode;
        _objResourcesInfo.EffectiveStartDate = objResourcesInfo.EffectiveStartDate;
        _objResourcesInfo.EffectiveEndDate = objResourcesInfo.EffectiveEndDate;
        _objResourcesInfo.DepartmentID = objResourcesInfo.DepartmentID;
        _objResourcesInfo.role = objResourcesInfo.role; //objResourcesInfo.UserRole;
        _objResourcesInfo.DefaultRole = objResourcesInfo.DefaultRole;
        _objResourcesInfo.RoleDetails = objResourcesInfo.RoleDetails;
        _objResourcesInfo.Comments = objResourcesInfo.Comments;
        DateTime EffectiveStartDate = objResourcesInfo.EffectiveStartDate;
        DateTime EffectiveEndDate = objResourcesInfo.EffectiveEndDate;       
        _objResourcesInfo.IsActive = CheckUserValidOrNot(EffectiveStartDate, EffectiveEndDate);
        _objResourcesInfo.ChangedBy = objResourcesInfo.ChangedBy;
        _objResourcesInfo.OrgID = objResourcesInfo.OrgID;
        _objResourcesInfo.VendorID = objResourcesInfo.VendorID;
        _objResourcesInfo.guid = guidtxt;
        _objResourcesInfo.mime = objResourcesInfo.mime;
        _objResourcesInfo.EntityTypeID = objResourcesInfo.EntityTypeID;
        _objResourcesInfo.OrgGroupID = objResourcesInfo.OrgGroupID;
        _objResourcesInfo.Field1 = objResourcesInfo.Field1;
        _objResourcesInfo.Field2 = objResourcesInfo.Field2;
        _objResourcesInfo.Field3 = objResourcesInfo.Field3;
        _objResourcesInfo.Field4 = objResourcesInfo.Field4;
        _objResourcesInfo.SubFunctionID = objResourcesInfo.SubFunctionID;
        _objResourcesInfo.DesignationId = objResourcesInfo.DesignationId;
        _objResourcesInfo.UserRole = objResourcesInfo.role.FirstOrDefault().ToString();

        return _objResourcesInfo;

    }

    public static int CheckUserValidOrNot(DateTime dtStart, DateTime dtEnd)
    {
        int isValidUser = 2;
        DateTime dtCurrDate = DateTime.Now;

        try
        {
            if (dtStart != DateTime.MinValue && dtStart <= dtCurrDate)
            {
                isValidUser = 1;
            }
            else
            {
                isValidUser = 2;
            }

            if (dtEnd != DateTime.MinValue && dtEnd >= dtCurrDate && isValidUser == 1)
            {
                isValidUser = 1;
            }
            else
            {
                isValidUser = 2;
            }
        }
        catch (Exception)
        {
            isValidUser = 2;
        }

        return isValidUser;
    }
   
    #endregion

    private OtherBCMEntities BuildDataForOtherBCMEntity(string strProcessID, OtherBCMEntities objOtherBCMEntities)
    {
        try
        {
            objOtherBCMEntities.IsActive = "1";
            objOtherBCMEntities.Field1 = string.Empty;
            objOtherBCMEntities.Field2 = string.Empty;
            objOtherBCMEntities.Field3 = string.Empty;
            objOtherBCMEntities.Field4 = string.Empty;
            objOtherBCMEntities.UpdatorID = _UserDetails.UserID.ToString();
            objOtherBCMEntities.CreatorID = _UserDetails.UserID.ToString();
            objOtherBCMEntities.EntityTypeID = (int)BCPEnum.EntityType.BCMEntity;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return objOtherBCMEntities;
    }

    #region DropdwonMethodForEdit

    public void PopulateDropDownForAppliction(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
           // PopulateDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID : iOrgGroupID, iOrgID == 0 ? _UserDetails.OrgID : iOrgID, iUnitID == 0 ? _UserDetails.UnitID : iUnitID);
            ViewBag.OrgInfo = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());

            ViewBag.Unit = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());

            ViewBag.Department =  _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString() == "0" ? _UserDetails.UnitID.ToString() : iUnitID.ToString());

            ViewBag.ResourceList = _Utilities.GetResources(_UserDetails.OrgID);

            ViewBag.Subdepartment = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(),_UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID.ToString() == "0" ? _UserDetails.DepartmentID.ToString() : iDepartmentID.ToString());

            ViewBag.lstResource = _ProcessSrv.GetAllResourcesList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public void PopulateDropdownsForResource(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            var orgGroupInfoList = _Utilities.GetAlOrgGroupList();
            ViewBag.orgGroupInfoList = orgGroupInfoList;

            //var orgInfoList = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown("10");
            var orgInfoList = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());
            ViewBag.orgInfo = orgInfoList;

            //var UnitInfoList = _ProcessSrv.GetOrganizationUnitListByOrgID(1);
            var UnitInfoList = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());
            ViewBag.unit = UnitInfoList;

            //var departmentlist = _Utilities.GetDepartmentMasterAll();
            var departmentlist = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString() == "0" ? _UserDetails.UnitID.ToString() : iUnitID.ToString());
            ViewBag.department = departmentlist;

            //var SubDepartmentList = _Utilities.GetAllSubDepartmentListDropdown();
            var SubDepartmentList = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID.ToString() == "0" ? _UserDetails.DepartmentID.ToString() : iDepartmentID.ToString());
            ViewBag.SubDepartment = SubDepartmentList;

            List<UserRoleMasterInfo> RoleMasterList = _Utilities.GetUserRoleMasterByOrgID(1);
            ViewBag.RoleMasterList = RoleMasterList;

            var TypeList = _Utilities.GetTypeInfoByEntityID(Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.ResourcePlan));
            ViewBag.Type = TypeList;

            var CompanyList = _Utilities.GetCompanyMasterInfoList(1);
            ViewBag.CompanyList = CompanyList;

            ViewBag.ResourceDesignation = _Utilities.GetAllResourceDesignationList();

        }
        catch (Exception ex)
        {
            BadRequest(ex.Message);
        }
    }


    public void PopulateDropDownsForFacility(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            PopulateDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID : iOrgGroupID, iOrgID == 0 ? _UserDetails.OrgID : iOrgID);
            ViewBag.OrgInfo = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());

            ViewBag.Unit = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString() == "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());

            //ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);
            ViewBag.Department = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString() == "0" ? _UserDetails.UnitID.ToString() : iUnitID.ToString());
            ViewBag.Subdepartment = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID.ToString() == "0" ? _UserDetails.DepartmentID.ToString() : iDepartmentID.ToString());

            ViewBag.FacilityList = _Utilities.GetFacilityListByUnitID(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

            ViewBag.ResourceList = _Utilities.GetResources(_UserDetails.OrgID);

           // ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();

            ViewBag.TypeMasterInfo = _ProcessSrv.GetTypeInfoByEntityID((int)BCPEnum.EntityType.Facilities);

            ViewBag.LocationList = _ProcessSrv.GetLocationListByUnit(_UserDetails.OrgID, _UserDetails.UnitID);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public void PopulateDropdownsForBusinessProces(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            iOrgGroupID = iOrgGroupID == 0 ? _UserDetails.OrgGroupID : iOrgGroupID;
            iOrgID = iOrgID == 0 ? _UserDetails.OrgID : iOrgID;
            iUnitID = iUnitID == 0 ? _UserDetails.UnitID : iUnitID;
            iDepartmentID = iDepartmentID == 0 ? _UserDetails.DepartmentID : iDepartmentID;

            ViewBag.lstOrg = _Utilities.PupulateOrganisation(iOrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());
            ViewBag.lstUnit = _Utilities.PupulateUnit(iOrgGroupID.ToString(), iOrgID.ToString(), _UserDetails.UserRoleID.ToString());
            ViewBag.lstDepartment = _Utilities.PupulateDepartment(iOrgGroupID.ToString(), iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString());
            ViewBag.lstSubDepartment = _Utilities.PupulateSubDepartment(iOrgGroupID.ToString(), iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID.ToString());
            
            var lstOrgGroupInfo = _Utilities.GetOrgGroupList();
            ViewBag.lstorgGroupInfoList = lstOrgGroupInfo;

            //var lstOrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString());
           // var lstOrgInfo = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());
            //ViewBag.lstOrg = lstOrgInfo;

            //var UnitInfoList = _ProcessSrv.GetOrganizationUnitListByOrgID(_UserDetails.OrgID);
            //var UnitInfoList = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());
           // ViewBag.lstUnit = UnitInfoList;

            //ViewBag.lstDepartment = _Utilities.GetDepartmentAllListForDropdown();
            //ViewBag.lstDepartment = _Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iUnitID.ToString() == "0" ? _UserDetails.UnitID.ToString() : iUnitID.ToString());

            //ViewBag.lstSubDepartment = _Utilities.GetAllSubDepartmentListDropdown();
            //ViewBag.lstSubDepartment = _Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString(), iDepartmentID.ToString() == "0" ? _UserDetails.DepartmentID.ToString() : iDepartmentID.ToString());

            List<UserRoleMasterInfo> RoleMasterList = _Utilities.GetUserRoleMasterByOrgID(_UserDetails.OrgID);
            ViewBag.lstRoleMasterList = RoleMasterList;

            List<TypeMasterInfo> TypeList = _Utilities.GetTypeInfoByEntityID(Convert.ToInt32(BCM.Shared.BCPEnum.EntityType.ResourcePlan));
            ViewBag.lstType = TypeList;

            ViewBag.lstCompany = _Utilities.GetCompanyMasterInfoList(_UserDetails.OrgID);
            ViewBag.lstResourceDesignation = _Utilities.GetAllResourceDesignationList();
            ViewBag.lstResource = _ProcessSrv.GetAllResourcesList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public void PopulateDropdownForOtherBCMEntity()
    {
        try
        {
            PopulateDropdown(_UserDetails.OrgGroupID, _UserDetails.OrgID);
            //ViewBag.DepartmentInfo = new SelectList(_Utilities.GetDepartmentAllListForDropdown(), "DepartmentID", "DepartmentName");
            //ViewBag.SubFunction = new SelectList(_Utilities.GetAllSubDepartmentListDropdown(), "SubFunctionID", "SubFunctionName");
            //ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString()), "Id", "OrganizationName");
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetResources(_UserDetails.OrgID), "ResourceId", "ResourceName");
            //ViewBag.OrgUnit = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails.OrgID), "UnitID", "UnitName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
    public void PopulateDropDownForThirdParty(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            //PopulateDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID : iOrgGroupID, iOrgID == 0 ? _UserDetails.OrgID : iOrgID, iUnitID == 0 ? _UserDetails.UnitID : iUnitID);
            //ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());

            //ViewBag.Unit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);

            //ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);
            ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());

            ViewBag.Unit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);

            ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

            //ViewBag.ResourceList = _Utilities.GetAllResourceList();

            ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();

            ViewBag.ResourceList = _Utilities.GetResources(_UserDetails.OrgID);

            //ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public void PopulateDropdownForLocation(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            PopulateDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID : iOrgGroupID, iOrgID == 0 ? _UserDetails.OrgID : iOrgID, iUnitID == 0 ? _UserDetails.UnitID : iUnitID);
            // ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());
            ViewBag.OrgName = new SelectList(_Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString()), "Id", "OrganizationName");
            //ViewBag.OrgUnit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);
            ViewBag.Unit = new SelectList(_Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString()), "UnitID", "UnitName");

            // ViewBag.DepartmentInfo = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

            //ViewBag.SubFunction = _Utilities.GetAllSubDepartmentListDropdown();

            ViewBag.OrgGroup = new SelectList(_Utilities.GetOrgGroupList(), "OrgGroupID", "OrganizationGroupName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    #endregion

    #region EventActionMethods


    public JsonResult BCMEntityType_SelectedIndexChanged(int EntityTypeID)
    {
        try
        {
            if (EntityTypeID == (int)BCPEnum.EntityType.BusinessProcess)
                ViewBag.lstEntities = new SelectList(_ProcessSrv.GetBusinessProcessMasterList(Convert.ToInt32(_UserDetails.OrgID)), "ProcessID", "ProcessName");

            else if (EntityTypeID == (int)BCPEnum.EntityType.Facilities)


                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateFacilitiesByUnitIdOrgid(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(ddlUnit.SelectedValue));
                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //Utilities.Utilities.PopulateFacilitiesByOrgGroup(ddlEntity, Convert.ToInt32(_UserDetails.OrgGroupID));
                }
                else
                {
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetAllFacilitieslistByUnitIDOrgId(Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(_UserDetails.UnitID), _UserDetails.OrgGroupID.ToString()), "FacilityID", "FacilityName");

                    return Json(ViewBag.lstEntities);
                    //_Utilities.PopulateFacilitiesByUnitIdOrgidNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(_UserDetails.UnitID));
                }
            else if (EntityTypeID == (int)BCPEnum.EntityType.Location)

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateLocation(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetLocationMasterList(_UserDetails.OrgID), "Id", "LocationName");
                    return Json(ViewBag.lstEntities);
                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //Utilities.Utilities.PopulateLocationByOrgGroupID(ddlEntity, Convert.ToInt32(_UserDetails.OrgGroupID));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.PopulateLocationByOrgGroupID(_UserDetails.OrgGroupID), "Id", "LocationName");
                    return Json(ViewBag.lstEntities);
                }
                else
                {
                    // Utilities.Utilities.PopulateLocationNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetLocationMasterList(_UserDetails.OrgID), "Id", "LocationName");
                    return Json(ViewBag.lstEntities);
                }




            //Utilities.Utilities.PopulateBCMGroupsByUnitId(ddlBusinessProcess, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(ddlUnit.SelectedValue));
            else if (EntityTypeID == (int)BCPEnum.EntityType.People)
                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetResourcesList(_UserDetails.OrgID), "ResourceId", "ResourceName");
                    return Json(ViewBag.lstEntities);
                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {                    
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.GetResourcesListByOrgGrpID(ddlEntity, _UserDetails.OrgGroupID);
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    //}
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetResourcesListByOrgGrpID(_UserDetails.OrgGroupID), "ResourceId", "ResourceName");
                    return Json(ViewBag.lstEntities);

                }
                else
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //   Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateResources(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    //}
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetResourcesList(_UserDetails.OrgID), "ResourceId", "ResourceName");
                    return Json(ViewBag.lstEntities);
                }


            else if (EntityTypeID == (int)BCPEnum.EntityType.BCMEntity)
            {

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {

                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetOtherBCMEntities(_UserDetails.OrgID,_UserDetails.UnitID,0,0), "ID", "EntityName");
                    return Json(ViewBag.lstEntities);

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.PopulateOtherBCMEntitiesByOrgGroupID(ddlEntity, Convert.ToInt32(_UserDetails.OrgGroupID), Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_UserDetails.UnitID), 0, 0);
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateOtherBCMEntitiesNew(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_UserDetails.UnitID), 0, 0);
                    //}
                    //ViewBag.lstEntities = new SelectList(_ProcessSrv.GetOtherBCMEntities(_UserDetails.OrgGroupID, _UserDetails.UnitID), "ID", "EntityName");
                    //return Json(ViewBag.lstEntities);
                }
                else
                {
                    // Utilities.Utilities.PopulateOtherBCMEntitiesNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID), Convert.ToInt32(_UserDetails.UnitID), 0, 0);
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetOtherBCMEntities(_UserDetails.OrgID,_UserDetails.UnitID,0,0), "ID", "EntityName");
                    return Json(ViewBag.lstEntities);
                }

            }
            else if (EntityTypeID == (int)BCPEnum.EntityType.ThirdParty)
            {

                //if ((_UserDetails.UserRole.Equals(CVGlobal.ProductAdminRole)) || (_UserDetails.UserRole.Equals(CVGlobal.SuperAdminRole)))
                //{
                //    Utilities.Utilities.PopulateCompany(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                //}
                //else
                //{
                //    Utilities.Utilities.PopulateCompanyNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                //}


                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {
                    // Utilities.Utilities.PopulateCompany(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));

                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //    Utilities.Utilities.PopulateCompanyByOrgGroupID(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue), Convert.ToInt32(_UserDetails.OrgGroupID));
                    //}
                    //else
                    //{
                    //    Utilities.Utilities.PopulateCompany(ddlEntity, Convert.ToInt32(ddlOrg.SelectedValue));
                    //}
                }
                else
                {
                    // Utilities.Utilities.PopulateCompanyNew(ddlEntity, Convert.ToInt32(_UserDetails.OrgID));
                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetCompanyList(_UserDetails.OrgID), "CompanyID", "CompanyName");
                    return Json(ViewBag.lstEntities);

                }

            }
            else if (EntityTypeID == (int)BCPEnum.EntityType.Application)

                if (_Utilities.IsProductAdmin(_UserDetails.UserRole))
                {


                }
                else if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    //if (ddlOrg.SelectedValue == "0")
                    //{
                    //   // Utilities.Utilities.PopulateApplicationsByOrgGroupID(ddlEntity, _UserDetails.OrgGroupID);
                    //}
                    //else
                    //{
                    //   // Utilities.Utilities.PopulateApplicationsByOrgID(ddlEntity, ddlOrg.SelectedValue);
                    //}
                }
                else
                {

                    ViewBag.lstEntities = new SelectList(_ProcessSrv.GetApplicationList(Convert.ToInt32(_UserDetails.OrgID)), "ApplicationId", "ApplicationName");

                }            
            return Json(ViewBag.lstEntities);
        }
        catch (Exception)
        {

            throw;
        }
    }

    [HttpGet]
    public JsonResult GetAllUnits(int iOrgID)
    {
        try
        {
            var objUnitList = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString());
            return Json(objUnitList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID)
    {
        try
        {
            ViewBag.SelectedUnit = iUnitID.ToString();
            var objDepartmentList = _Utilities.BindFunction(iUnitID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllSubDepartments(int iDepartmentID)
    {
        try
        {
            ViewBag.SelectedDepartment = iDepartmentID.ToString();
            var objSubDepartmentList = _Utilities.BindSubFunction(iDepartmentID);
            return Json(objSubDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    private List<BIAProfileMaster> FilteredProfiles()
    {
        List <BIAProfileMaster> lstBIAProfileMaster = _ProcessSrv.BIAProfileMasterListAll();

        var filteredProfiles = lstBIAProfileMaster.Where(profile => profile.Status == "2").ToList();

        return filteredProfiles;
    }


    public JsonResult OnSelectedChange(int OwnerID)
    {
        ResourcesInfo objResource = new ResourcesInfo();

        objResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(OwnerID));


        return Json(objResource);
    }

    public JsonResult OnAltSelectedChange(int AltOwnerID)
    {
        ResourcesInfo objResource = new ResourcesInfo();

        objResource = _ProcessSrv.GetResourcesByResourceID(Convert.ToInt32(AltOwnerID));


        return Json(objResource);
    }

    public IActionResult GetFileredProcess(int OrgID = 0, int UnitID = 0, int DepartmentID = 0, int SubDepartmentID = 0, int IsUnderBCM = -1)
    {
        List<BusinessProcessInfo> lstBusinesProcess = new List<BusinessProcessInfo>();

        try
        {
            lstBusinesProcess = _ProcessSrv.GetBIASurveyListOrgUnitLevel(_UserDetails.OrgID);
            if (!(_Utilities.IsProductAdmin(_UserDetails.UserRole)))
            {
                if (_Utilities.IsSuperAdmin(_UserDetails.UserRole))
                {
                    lstBusinesProcess = _Utilities.FilterListByOrgGroupID(lstBusinesProcess, _UserDetails.OrgGroupID);

                }
                else
                {

                    lstBusinesProcess = _Utilities.FilterListByOrgID(lstBusinesProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                    lstBusinesProcess = _Utilities.FilterListByRoleID(lstBusinesProcess, _UserDetails.OrgGroupID, _UserDetails.OrgID, _UserDetails.UserRoleID);
                }
            }

            // Apply organizational hierarchy filters cumulatively
            if (OrgID > 0)
            {
                lstBusinesProcess = lstBusinesProcess.Where(x => x.OrgID == OrgID).ToList();
            }

            if (UnitID > 0)
            {
                lstBusinesProcess = lstBusinesProcess.Where(x => x.UnitID == UnitID).ToList();
            }

            if (DepartmentID > 0)
            {
                lstBusinesProcess = lstBusinesProcess.Where(x => x.DepartmentID == DepartmentID).ToList();
            }

            if (SubDepartmentID > 0)
            {
                lstBusinesProcess = lstBusinesProcess.Where(x => x.SubfunctionID == SubDepartmentID).ToList();
            }

            // Apply BCM scope filter
            if (IsUnderBCM == 1)
            {
                lstBusinesProcess = lstBusinesProcess.Where(x => !string.IsNullOrEmpty(x.ProcessCode)).ToList();
            }
            else if (IsUnderBCM == 0)
            {
                lstBusinesProcess = lstBusinesProcess.Where(x => string.IsNullOrEmpty(x.ProcessCode)).ToList();
            }
            // If IsUnderBCM == -1, show all records regardless of ProcessCode
            ViewBag.lstBusinessProcess = lstBusinesProcess;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_FilterBcmEntities", lstBusinesProcess);

    }

    //public IActionResult SendMailForAppproval(BusinessProcessInfo objBusinessProcessInfo)
    //{
    //    try
    //    {
    //        if (!string.IsNullOrEmpty(objBusinessProcessInfo.ApproverEmail))
    //        {
    //            bool bValidEmail = _BCMMail.IsValidEmail(Convert.ToString(objBusinessProcessInfo.ApproverEmail));

    //            if (bValidEmail)
    //            {
    //                ManageUsersDetails objManageUsersDetails = _ProcessSrv.GetPWDBYEmailID(objBusinessProcessInfo.ApproverEmail.Trim());
    //                int iOrgId = objManageUsersDetails.OrgID;

    //                if (!string.IsNullOrEmpty(objManageUsersDetails.Password))
    //                {
    //                    VaultSettings objVaultSetting = _ProcessSrv.GetVaultSettingsByOrgID(Convert.ToInt16(iOrgId));

    //                    if (!string.IsNullOrEmpty(objVaultSetting.DevMode))
    //                    {


    //                        string body = "<HTML><HEAD></HEAD><BODY>Hello " + objManageUsersDetails.UserName + ",";
    //                        body += "<br /><br /> Please Find The Attachement.";
    //                        body += "<br />Email Address :<u><font color='blue'>" + objBusinessProcessInfo.ApproverEmail + "</font></u>";
    //                        string? strUName = objManageUsersDetails.UserName;

    //                        string strLink = " " + objBusinessProcessInfo.ApproverEmail.Trim();

    //                        body += "<br /> Please <a href =";
    //                        body += strLink + "> Click Here </a>to proceed...";

    //                        body += "<br />This is notification mail, no need to reply.";
    //                        body += "<br /><br />Thanks. <br /><br /><br /><br /><b>Admin</b><br />Continuity Vault</body></HTML>";

    //                        bool bSuccess = _BCMMail.SendMail("Continuity Vault Appointment", body, (objBusinessProcessInfo.ApproverEmail.Trim()), string.Empty, string.Empty, string.Empty, Convert.ToString(iOrgId));

    //                        if (bSuccess)
    //                        {
    //                            return Ok("mail has been send successfully");
    //                        }
    //                    }
    //                }
    //            }
    //            else
    //            {
    //                return Ok("Please enter valid Email ID");
    //            }
    //        }
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //        return BadRequest(ex.Message);
    //    }
    //    return Ok();
    //}

    #endregion

    #region ReviewScetion
    [HttpGet]
    public IActionResult ReviewSection()
    {
        string? strRecordID = HttpContext.Session.GetString("strRecordID");
        string? strEntityTypeID = HttpContext.Session.GetString("strEntityTypeID");

        ViewBag.RecordID = strRecordID;
        ViewBag.EntityID = strEntityTypeID;

        List<EntityReview> lstEntityReview = _ProcessSrv.EntityReviewHistoryGetByEntityIDAndRecordID(strEntityTypeID, strRecordID);

        //bind next review date
        var strNextReviewDate = lstEntityReview
            .OrderByDescending(e => e.NextReviewDate)
            .Skip(1)
            .Select(e => e.NextReviewDate)
            .FirstOrDefault();
        ViewBag.NextReviewDate = strNextReviewDate;

        return PartialView("~/Areas/BCMProcessBIA/Views/BusinessProcessForm/ReviewSection.cshtml", lstEntityReview);
    }
    #endregion
}

