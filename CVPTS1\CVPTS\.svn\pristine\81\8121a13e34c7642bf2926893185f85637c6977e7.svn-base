﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;

namespace BCM.UI.Areas.BCMTraining.Controllers;
[Area("BCMTraining")]
public class ManageBCMTrainingFormController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;

    public ManageBCMTrainingFormController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    public IActionResult ManageBCMTrainingForm()
    {
        var lstBCMTrainingMaster = new List<BCMTrainingMaster>();

        try
        {
            PopulateDropdown();
            lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View(lstBCMTrainingMaster);
    }

    [HttpGet]
    public IActionResult AddBCMTrainingMaster()
    {
        try
        {
            PopulateDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_AddBCMTraining", new BCMTrainingMaster());
    }

    [HttpPost]
    public IActionResult AddBCMTrainingMaster(BCMTrainingMaster objBCMTrainingMaster)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.SaveTrainingMaster(objBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }


    [HttpGet]
    public IActionResult EditBCMTrainingMaster(int iId)
    {        
        BCMTrainingMaster objBCMTrainingMaster = new BCMTrainingMaster();

        try
        {
            PopulateDropdown();

            objBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getByID(iId);            
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_EditBCMTraining", objBCMTrainingMaster);
    }

    [HttpPost]
    public IActionResult EditBCMTrainingMaster(BCMTrainingMaster objBCMTrainingMaster)
    {
        bool bSuccess = false;

        try
        {
            bSuccess = _ProcessSrv.SaveTrainingMaster(objBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }


    [HttpGet]
    public IActionResult DeleteBCMTrainingMaster(int iId)
    {  
        BCMTrainingMaster objBCMTrainingMaster = new BCMTrainingMaster();

        try
        {
            objBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getByID(iId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return PartialView("_DeleteBCMTraining", objBCMTrainingMaster);
    }

    [HttpPost]
    public IActionResult DeleteBCMTrainingMaster(BCMTrainingMaster objBCMTrainingMaster)
    {        
        bool bTrainingMasterDelete = false;
        bool bQuestionsDelete = false;
        bool bDeleteOptions = false;

        try
        {
            bTrainingMasterDelete = _ProcessSrv.BCMTrainingMaster_DeleteByID(Convert.ToInt32(objBCMTrainingMaster.ID));
            List<BCMTrainingMaster> objQuestiondetails = _ProcessSrv.QuestionsDetailsGetByTrainingMasterID(Convert.ToInt32(objBCMTrainingMaster.ID));
            bQuestionsDelete = _ProcessSrv.BCMTrainingQuestion_DeleteByTrainingID(Convert.ToInt32(objBCMTrainingMaster.ID));

            foreach (BCMTrainingMaster item in objQuestiondetails)
            {
                bDeleteOptions = _ProcessSrv.BCMTrainingOption_DeleteByQuestionID(Convert.ToInt32(item.ID));
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }

    public IActionResult GetDepartmentByID(int iDepartmentId)
    {
        try
        {
            List<BCMTrainingMaster> lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();
            if (iDepartmentId > 0)
            {
                lstBCMTrainingMaster = lstBCMTrainingMaster.Where(x => x.DepartmentID == iDepartmentId).ToList();
                if (lstBCMTrainingMaster == null || !lstBCMTrainingMaster.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterBCMTraining", lstBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return RedirectToAction("ManageBCMTrainingForm");
    }


    public IActionResult GetUnitByID(int iUnitId)
    {
        try
        {
            List<BCMTrainingMaster> lstBCMTrainingMaster = _ProcessSrv.BCMTrainingMaster_getAll();
            if (iUnitId > 0)
            {
                lstBCMTrainingMaster = lstBCMTrainingMaster.Where(x => x.UnitID == iUnitId).ToList();
                if (lstBCMTrainingMaster == null || !lstBCMTrainingMaster.Any())
                {
                    return NotFound("No Records Found.");
                }
            }
            return PartialView("_FilterBCMTraining", lstBCMTrainingMaster);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageBCMTrainingForm");
    }


    public void PopulateDropdown()
    {
        try
        {
            ViewBag.DepartmentInfo = new SelectList(_Utilities.GetDepartmentAllListForDropdown(), "DepartmentID", "DepartmentName");
            ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(_UserDetails.OrgGroupID.ToString()), "Id", "OrganizationName");
            ViewBag.OrgUnit = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails.OrgID), "UnitID", "UnitName");
            ViewBag.ResourcesInfo = new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
}

