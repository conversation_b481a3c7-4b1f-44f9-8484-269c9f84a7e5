﻿@model BCM.BusinessClasses.WorkflowActionInfo
@{
    ViewBag.Title = "WorkflowConfiguration";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var PlanId = ViewData["PlanId"];
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<link href="~/css/workflow.css" rel="stylesheet" />

<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">Workflow Configuration</h6>
    <input type="text" id="txtPlanId" value="@PlanId" hidden />

    <div class="d-flex align-items-center gap-2">
        @* <span role="button" id="btnInsertNewStep"><i class="cv-new me-1"></i>Insert New</span>
        <span class="vr"></span>
        <span role="button"><i class="cv-wf-edit me-1"></i>Load</span>
        <span class="vr"></span>
        <span role="button"><i class="cv-delete me-1"></i>Delete</span>
        <span class="vr"></span> *@
        <span class="d-flex gap-1 align-items-center" role="button" id="btnSaveWorkflow"><i class="cv-save  fs-6"></i><span>Save</span></span>
        <span class="vr"></span>
        <span class="d-flex gap-1 align-items-center" role="button"><i class="cv-Report fs-6"></i><span>Report</span></span>
        <span class="vr"></span>
        <span class="d-flex gap-1 align-items-center" role="button" id="btnNext"><i></i><span><b>Next</b></span></span>
        @* <span role="button"><i class="cv-saveas me-1"></i>Save As</span>
        <span class="vr"></span>
        <span role="button"><i class="cv-condition me-1"></i>Set Condition</span>
        <span class="vr"></span>
        <span role="button"><i class="cv-export me-1"></i>Export</span>
        <span class="vr"></span>
        <span role="button"><i class="cv-import me-1"></i>Import</span>
        <span class="vr"></span> *@
    </div>
</div>
<div class="Page-Condant border-0">
    <div class="">
        <div class="app-container" style="background-image: radial-gradient(#d8d8d8 1px, transparent 0);  background-size: 20px 20px;">
            <!-- Main Content -->
            <div class="main-content">
                <div class="row h-100">
                    <div class="col-12 col-lg-2">
                        <div class="card palette-section card-design-none shadow-sm h-100">
                            <div class="card-header header p-2">
                                <div class="d-flex align-items-center gap-2"><i class="cv-actions1 fs-5"></i><div class="fs-6">Actions</div></div>
                                <i class="cv-new fs-5" id="btnAddStep" role="button"></i>
                            </div>
                            <div class="card-body pt-0">
                                <ul class="list-group list-group-flush" id="stepNodeContainer">
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-10">
                        <!-- Canvas Area -->
                        <main class="canvas-container" id="canvas">
                            <div class="d-flex h-100" id="workflowContainer">

                                <div class="d-flex h-100 align-items-center gap-5" id="workflowSteps">
                                    <div class="Start-btn link-dot" id="start">
                                        <img title="Start" src="/img/Start.svg" width="30" height="30" draggable="false" loading="lazy" alt="Start image" /><span>Start</span><i class="cv-stroke-circle text-warning"></i>
                                    </div>
                                </div>
                                <div class="row mx-0" id="btnEndWorkflow" style="display:none;">
                                    <i class="cv-workflow-line fs-7"></i>
                                    <img title="End" src="/img/End.svg" width="30" height="30" draggable="false" loading="lazy" alt="End image" />
                                </div>
                            </div>
                            <div class="contextMenu dropdown" data-bs-toggle="dropdown" id="wfContextMenu" aria-expanded="false">
                                <ul class="UlContextBtn dropdown-menu dropdown-menu-lg-end fs-8" style="display:block;">
                                    <li id="btnSelectAll">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-uncheck me-1 fs-7"></i>Select All</span>

                                        </a>
                                    </li>
                                    <li id="btnUnselect">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-checks me-1  fs-7"></i>Unselect All</span>

                                        </a>
                                    </li>
                                    <li id="btnCut">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-cut me-1  fs-7"></i>Cut</span>

                                        </a>
                                    </li>
                                    <li id="btnCopy">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-copy me-1  fs-7"></i>Copy</span>

                                        </a>
                                    </li>
                                    <li id="btnPaste">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-paste me-1  fs-7"></i>Paste</span>

                                        </a>
                                    </li>
                                    <li id="btndelete">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-Delete me-1 text-dark  fs-7"></i>Delete</span>

                                        </a>
                                    </li>
                                    <li id="btnConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-conditional me-1 text-dark fs-7"></i>Add Condition</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="btnGoToConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-left-right me-1 text-dark fs-7"></i>Go To</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="removeConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-Delete me-1 text-dark fs-7"></i>Delete Condition</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="removeIfConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-link-off text-dark me-1 fs-7"></i>Remove Success</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="removeElseConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-link-off text-dark me-1 fs-7"></i>Remove Failure</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </main>
                    </div>
                </div>
            </div>
        </div>

        <!-- Context Menu -->
        <div id="context-menu" class="context-menu" style="display: none;">
            <ul>
                <li id="delete-node">Delete Node</li>
                <li id="duplicate-node">Duplicate Node</li>
                <li id="edit-node">Edit Node</li>
            </ul>
        </div>

        <!-- Connection Context Menu -->
        <div id="connection-context-menu" class="context-menu" style="display: none;">
            <ul>
                <li id="delete-connection">Delete Connection</li>
            </ul>
        </div>
    </div>
    <!-- Node Editor Offcanvas -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="nodeEditorOffcanvas" aria-labelledby="nodeEditorOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="nodeEditorOffcanvasLabel">
                <i class="bi bi-pencil-square"></i> Edit Node
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <form id="nodeEditorForm">
                <input type="hidden" id="editNodeId">

                <div class="mb-3">
                    <label for="editNodeName" class="form-label">Node Name *</label>
                    <input type="text" class="form-control" id="editNodeName" placeholder="Enter node name" required>
                    <div class="form-text">This will be displayed on the node</div>
                </div>

                <div class="mb-3">
                    <label for="editNodeDescription" class="form-label">Description</label>
                    <textarea class="form-control" id="editNodeDescription" rows="3" placeholder="Optional description"></textarea>
                </div>

                <div class="mb-3">
                    <label for="editNodeIcon" class="form-label">Icon</label>
                    <select class="form-select" id="editNodeIcon">
                        <option value="⚙️">⚙️ Settings</option>
                        <option value="🔧">🔧 Tool</option>
                        <option value="📊">📊 Analytics</option>
                        <option value="🔍">🔍 Search</option>
                        <option value="💾">💾 Storage</option>
                        <option value="🌟">🌟 Feature</option>
                        <option value="🚀">🚀 Launch</option>
                        <option value="📝">📝 Note</option>
                        <option value="🎯">🎯 Target</option>
                        <option value="🔔">🔔 Notification</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Properties</label>
                    <div id="editNodeProperties">
                        <!-- Properties will be populated dynamically -->
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="addEditProperty">
                        <i class="bi bi-plus"></i> Add Property
                    </button>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update Node
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="offcanvas">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Node Creator Offcanvas -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="nodeCreatorOffcanvas" aria-labelledby="nodeCreatorOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="nodeCreatorOffcanvasLabel">
                <i class="bi bi-plus-circle"></i> Create New Node
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <form id="nodeCreatorForm">
                @*  <div class="mb-3">
                <label for="nodeName" class="form-label">Node Name *</label>
                <input type="text" class="form-control" id="nodeName" placeholder="Enter node name" required>
                <div class="form-text">This will be displayed on the node</div>
                </div> *@

                <div class="mb-3">
                    <label for="nodeDescription" class="form-label">Description</label>
                    <textarea class="form-control" id="nodeDescription" rows="3" placeholder="Optional description"></textarea>
                </div>

                <div class="mb-3">
                    <label for="nodeIcon" class="form-label">Icon</label>
                    <select class="form-select" id="nodeIcon">
                        <option value="⚙️">⚙️ Settings</option>
                        <option value="🔧">🔧 Tool</option>
                        <option value="📊">📊 Analytics</option>
                        <option value="🔍">🔍 Search</option>
                        <option value="💾">💾 Storage</option>
                        <option value="🌟">🌟 Feature</option>
                        <option value="🚀">🚀 Launch</option>
                        <option value="📝">📝 Note</option>
                        <option value="🎯">🎯 Target</option>
                        <option value="🔔">🔔 Notification</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Properties</label>
                    <div id="nodeProperties">
                        <div class="property-row mb-2">
                            <div class="row">
                                <div class="col-5">
                                    <input type="text" class="form-control property-key" placeholder="Key">
                                </div>
                                <div class="col-6">
                                    <input type="text" class="form-control property-value" placeholder="Value">
                                </div>
                                <div class="col-1">
                                    <button type="button" class="btn btn-sm btn-outline-danger remove-property">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="addProperty">
                        <i class="bi bi-plus"></i> Add Property
                    </button>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Save Node
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="offcanvas">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>


    <div class="card diagram-area shadow-sm d-none">
        <div class="card-body" align="center">
            <div class="workflow_body_scroll h-100" id="workflowContainer">
                <div>
                    <img title="Start" src="/img/Start.svg" width="30" height="30" draggable="false" loading="lazy" alt="Start image" />
                </div>
                <div class="" id="workflowSteps">
                </div>
                <div class="row mx-0" id="btnEndWorkflow" style="display:none;">
                    <i class="cv-workflow-line fs-7"></i>
                    <img title="End" src="/img/End.svg" width="30" height="30" draggable="false" loading="lazy" alt="End image" />
                </div>
            </div>

        </div>
        @*    <div class="contextMenu dropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <ul class="UlContextBtn dropdown-menu dropdown-menu-lg-end fs-8" style="display:block;">
        <li id="btnStepEdit">
        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
        <span><i class="cp-uncheck me-1 fs-7"></i>Edit</span>
        <small class="font-monospace d-none">ctrl + a</small>
        </a>
        </li>
        <li id="btnStepDelete">
        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
        <span><i class="cp-checks me-1  fs-7"></i>Delete</span>
        <small class="font-monospace"></small>
        </a>
        </li>
        <li id="btnAddCondition">
        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
        <span><i class="cp-checks me-1  fs-7"></i>Add Condition</span>
        <small class="font-monospace"></small>
        </a>
        </li>
        <li id="btnGotoCondition">
        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
        <span><i class="cp-checks me-1  fs-7"></i>Goto</span>
        <small class="font-monospace"></small>
        </a>
        </li>
        <li id="btnRemoveCondition">
        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
        <span><i class="cp-checks me-1  fs-7"></i>Remove</span>
        <small class="font-monospace"></small>
        </a>
        </li>
        </ul>
        </div> *@
    </div>



    <div class="offcanvas offcanvas-end w-50" tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="offcanvasRightLabel">Configure Action</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <div class="row row-cols-2">
                <div class="col">
                    <div class="form-group">
                        <label>Step Name</label>
                        <div class="input-group">
                            <div class="input-group-text"><i class="cv-name"></i></div>
                            <input class="form-control" type="text" name="txtName" id="stepName" placeholder="Enter Step Name" />
                            <input class="form-control" type="text" value="" id="stepId" hidden />

                        </div>
                        <span class="text-danger" id="stepName_error"></span>
                    </div>

                </div>
                <div class="col">
                    <div class="form-group">
                        <label>Description</label>
                        <div class="input-group">
                            <div class="input-group-text"><i class="cv-description"></i></div>
                            <input class="form-control" type="text" id="stepDesription" placeholder="Enter Step Description" />
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>Execution Type</label>
                        <div class="input-group">
                            <div class="input-group-text"><i class="cv-options"></i></div>
                            <div class="form-control d-flex align-items-center gap-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="optionGroup" id="option2" value="Option2" checked />
                                    <label class="form-check-label" for="option2">Manual</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="optionGroup" id="option1" value="Option1"/>
                                    <label class="form-check-label" for="option1">Auto</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>Step Owner</label>
                        <div class="input-group">
                            <div class="input-group-text"><i class="cv-description"></i></div>
                            <select class="form-control" autocomplete="off" aria-label="Default select example" id="stepOwner" data-placeholder="Select Owner">
                                @*  <option value="0">-- Select Owner --</option> *@
                                @if (ViewBag.ResourceList != null)
                                {
                                    @foreach (var item in ViewBag.ResourceList)
                                    {
                                        <option value="@item.ResourceId">@item.ResourceName</option>
                                    }
                                }
                            </select>
                        </div>
                        <span class="text-danger" id="stepOwner_error"></span>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>Alternate Step Owner</label>
                        <div class="input-group">
                            <div class="input-group-text"><i class="cv-owner"></i></div>
                            <select class="form-control" autocomplete="off" aria-label="Default select example" id="stepAlternateOwner" data-placeholder="Select Alternate Owner">
                                @* <option value="0">-- Select Alternate Step Owner --</option> *@
                                @if (ViewBag.ResourceList != null)
                                {
                                    @foreach (var item in ViewBag.ResourceList)
                                    {
                                        <option value="@item.ResourceId">@item.ResourceName</option>
                                    }
                                }
                            </select>

                        </div>
                        <span class="text-danger" id="stepAlternateOwner_error"></span>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>CP Profile<small class="text-secondary">(optional)</small></label>
                        <div class="input-group">
                            <div class="input-group-text"><i class="cv-profile"></i></div>
                            <select class="form-control" autocomplete="off" aria-label="Default select example" id="CPProfile" data-placeholder="Select CP Profile">
                                <option value="0">-- Loading... --</option>

                            </select>
                            <button class="btn btn-outline-secondary refresh-cp-profiles" type="button" title="Refresh CP Profiles">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>

                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>API's'<small class="text-secondary">(optional)</small></label>
                        <div class="input-group">
                            <div class="input-group-text"><i class="cv-profile"></i></div>
                            @*   <select class="form-control" autocomplete="off" aria-label="Default select example" id="idApis" data-placeholder="Select API's'">
                            <option value="0">-- Select --</option>
                            </select> *@
                            <select class="form-control" autocomplete="off" aria-label="Default select example" id="idApis" data-placeholder="Select Alternate Owner">
                                <option selected value="0">-- Select API's --</option>
                                @if (ViewBag.ApiList != null)
                                {
                                    @foreach (var item in ViewBag.ApiList)
                                    {
                                        <option value="@item.ID">@item.APIName</option>
                                    }
                                }
                            </select>
                        </div>

                    </div>
                </div>
                <div class="col">
                    <div class="row row-cols-3 align-items-end">
                        <label class="col-12">Est Time</label>
                        <div class="col">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Enter Est Time" id="estimtedTime">
                            </div>
                            @* <span class="text-danger" id="cpEstTime_error"></span> *@
                        </div>
                        <div class="col">
                            <div class="input-group">
                                <select class="form-control" autocomplete="off" id="estimtedTimeUnit">
                                    <option value="0">Minute (s)</option>
                                    <option value="1">Hour(s)</option>
                                    <option value="2">Day(s)</option>
                                    <option value="3">Month(s)</option>
                                </select>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="col d-none">
                    <div class="form-group">
                        <label>Go to Step</label>
                        <div class="input-group">
                            <div class="input-group-text"><i class="cv-step"></i></div>
                            <select class="form-control" autocomplete="off" aria-label="Default select example" id="gotoStep">
                                <option selected value="0">-- Select Step --</option>
                                <option value="1">Step1</option>
                                <option value="2">Step2</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="col d-flex align-items-center">
                        <input class="form-check-inputab-content m-0" type="checkbox" name="interDependency" value="" id="interDependency">
                        <label class="small ms-1">InterDependency</label>
                    </div>
                </div>
                <div class="col" id="stepDependentCol">
                    <div class="form-group">
                        <label><i class="cv-workflow-dependent me-1"></i>Dependent On</label>
                        <div class="d-flex align-items-center gap-3 flex-wrap mt-2">

                            @if (ViewBag.DependentOnList != null)
                            {
                                @foreach (var item in ViewBag.DependentOnList)
                                {
                                    <div class="d-flex align-items-center gap-1">
                                        <input class="form-check-input m-0 checkDependentList" type="checkbox" name="@item.Name" id="@item.ID">
                                        <label class="small ms-1" for="@item.ID">
                                            @item.Name
                                        </label>
                                    </div>
                                }
                            }


                        </div>
                    </div>
                </div>
                <input class="form-control" type="hidden" name="stepId" id="stepId" />
            </div>
        </div>
        <div class="offcanvas-footer d-flex justify-content-end align-items-center gap-2 p-2">
            <button class="btn btn-secondary btn-sm" id="btnCancelOffcanvas">Cancel</button>
            <button class="btn btn-primary btn-sm me-2" id="addWorkflowStep">Add</button>
        </div>
    </div>

</div>




<div class="card hoverable-card mt-2 shadow d-none" style="width:23rem">
    <div class="card-header">Development Team Configuration Details</div>
    <div class="card-body pt-0">
        <div>
            <table class="table mb-0">
                <tbody>
                    <tr>
                        <td><i class="cv-name me-1"></i>Step Name</td>
                        <td>:</td>
                        <td>Development Team</td>
                    </tr>
                    <tr>
                        <td><i class="cv-description me-1"></i>Step Description</td>
                        <td>:</td>
                        <td>Text_text</td>
                    </tr>
                    <tr>
                        <td><i class="cv-owner me-1"></i>Step Owner</td>
                        <td>:</td>
                        <td>Text_text</td>
                    </tr>
                    <tr>
                        <td><i class="cv-owner me-1"></i>Alternate Step Owner</td>
                        <td>:</td>
                        <td>Elias Boustros</td>
                    </tr>
                    <tr>
                        <td><i class="cv-profile me-1"></i>CP Profile</td>
                        <td>:</td>
                        <td>IO_DataSync_Linux_App</td>
                    </tr>
                    <tr>
                        <td><i class="cv-schedule-time me-1"></i>Est Time</td>
                        <td>:</td>
                        <td>00:20:04</td>
                    </tr>
                    <tr>
                        <td><i class="cv-workflow-dependent me-1"></i>Dependent On</td>
                        <td>:</td>
                        <td>Design</td>
                    </tr>
                    <tr>
                        <td><i class="cv-step me-1"></i>Go to Step</td>
                        <td>:</td>
                        <td>Step 4</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>








<div class="d-none">
    <div class="Page-Header">
        <h6 class="Page-Title">Workflow Configuration</h6>
    </div>
    <div class="Page-Condant border-0">
        <div class="row row-cols-2">
            <div class="col">
                <div class="card">
                    <div class="card-header border-0">
                        <div class="row align-items-end">
                            <div class="col">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-actions"></i></span>
                                    <select class="form-control" id="dllsteps" asp-for="ID" autocomplete="off" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ddlLoadProperty, "ID", "Name"))">
                                        <option selected value="0">--Select --</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-schedule-time"></i></span>
                                    <select class="form-select" id="ddlPlace">
                                        <option value="0" selected>Insert After</option>
                                        <option value="1">Insert Before</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-primary btn-sm rounded-1" id="AddStepToWorkflow" type="button">Add Step to Workflow</button>
                            </div>
                        </div>
                    </div>
                    <div class="card mt-3" id="configDiv">
                        <div class="card-header border-0 fw-semibold">Configure Action</div>
                        <div class="card-body pt-0" style="height: calc(100vh - 242px); overflow-y: auto;">
                            <div class="form-group">
                                <label>Step Name</label>
                                <div class="input-group">
                                    <div class="input-group-text"><i class="cv-name"></i></div>
                                    <input class="form-control" type="text" id="txtName" asp-for="Name" name="txtName" placeholder="Enter Step Name" />
                                    <span id="spanFortxtName" style="color: red"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Step Description</label>
                                <div class="input-group">
                                    <div class="input-group-text"><i class="cv-description"></i></div>
                                    <input class="form-control" type="text" id="txtDescription" asp-for="Description" name="txtDescription" placeholder="Enter Step Description" />
                                    <span id="spanFortxtDescription" style="color: red"></span>
                                </div>
                            </div>
                            <div class="row row-cols-2">
                                <div class="col">
                                    <div class="form-group">
                                        <label>Step Owner</label>
                                        <div class="input-group">
                                            <div class="input-group-text"><i class="cv-description"></i></div>
                                            <select class="form-control" autocomplete="off" asp-for="Stepownerid" id="ddlOwners" name="ddlOwners" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ResourceList, "ResourceId", "ResourceName"))">
                                                <option selected value="0">-- Select Owner --</option>
                                            </select>
                                            <span id="spanForddlStepOwner" style="color: red"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>CP Profile</label>
                                        <div class="input-group">
                                            <div class="input-group-text"><i class="cv-profile"></i></div>
                                            <select class="form-select">
                                                <option selected value="0">-- Select --</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label>Alternate Step Owner</label>
                                        <div class="input-group">
                                            <div class="input-group-text"><i class="cv-owner"></i></div>
                                            <select class="form-control" autocomplete="off" id="ddlAltOwners" asp-for="Aleternatespetownerid" aria-label="Default select example">
                                                <option selected value="0">-- Select AltOwner --</option>
                                                @foreach (var name in ViewBag.ResourceList)
                                                {
                                                    <option value="@name.ResourceId">@name.ResourceName</option>
                                                }
                                            </select>
                                            <span id="spanddlAlternateStepOwner" style="color: red"></span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>Go to Step</label>
                                        <div class="input-group">
                                            <div class="input-group-text"><i class="cv-escalation"></i></div>
                                            <select class="form-control" autocomplete="off" asp-for="GoToStep" id="dllGoToStep" aria-label="Default select example" asp-items="@(new SelectList(ViewBag.ddlLoadProperty, "ID", "Name"))">
                                                <option selected value="0">-- Select Step --</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row row-cols-3 align-items-end">
                                <label class="col-12">Est Time</label>
                                <div class="col">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="txtTime" asp-for="Estimationtime" placeholder="Enter Est Time">
                                        <span id="spanFortxtTime" style="color: red"></span>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="input-group">
                                        <select class="form-control" asp-for="TimeUnit" aria-label="Default select example" autocomplete="off" id="dllUnit">
                                            <option value="0">Minute (s)</option>
                                            <option value="1">Hour(s)</option>
                                            <option value="2">Day(s)</option>
                                            <option value="3">Month(s)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col">
                                    <input class="form-check-inputab-content m-0" type="checkbox" value="" id="flexCheckDefault">
                                    <label class="small ms-1">InterDependency</label>
                                </div>
                            </div>
                            <div class="form-group">
                            </div>
                            <div class="form-group" id="dependent" hidden>
                                <label>Dependent On</label>
                                <div class="input-group">
                                    <div class="input-group-text"><i class="cv-escalation"></i></div>
                                    @if (ViewBag.DependentOnList != null)
                                    {
                                        @foreach (var item in ViewBag.DependentOnList)
                                        {
                                            <div class="form-check">
                                                <input class="form-check-inputab-content m-0" type="checkbox" name="DependentOn" value="@item.ID" id="DependentOn">
                                                <label class="small ms-1" for="dependentOn">
                                                    @item.Name
                                                </label>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <p>No options available.</p>
                                    }

                                    <span id="spanForlstDependentStepsNEW" style="color: red"></span>
                                </div>
                            </div>


                        </div>

                        <div class="card-footer text-end border-0">
                            <div class="btn-group gap-2">
                                <button class="btn btn-primary btn-sm rounded-1" id="btnActionEdit" type="button">Edit</button>
                                <button class="btn btn-primary btn-sm rounded-1" id="btnActionSave" type="button">Save</button>
                                <button class="btn btn-primary btn-sm rounded-1" id="btnActionUpdate" type="button" style="display: none;">Update</button>
                                <button class="btn btn-primary btn-sm rounded-1" type="button">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Condition Div -->
                <div id="conditionDiv" class="card mt-3" style="display: none;">

                    <div class="card-header border-0 fw-semibold">Connect Action</div>
                    <!-- Success Action -->
                    <div class="form-group">
                        <label for="txtSuccessAction">Success Action</label>
                        <div class="input-group">
                            <div class="input-group-text"><i class="cv-description"></i></div>
                            <input class="form-control" type="text" id="txtSuccessAction" placeholder="Enter Success Action" disabled />
                            <div>
                                <input type="hidden" id="stepid" />
                            </div>
                        </div>
                    </div>
                    <!-- Failure Action -->
                    <div class="form-group mt-3">
                        <label for="ddlExist1">Select Failure Action</label>
                        <div class="input-group">
                            <select id="ddlExist1">
                                <option value="0" selected>-- Select Failure Action --</option>
                            </select>
                        </div>
                    </div>
                    <!-- Connect Button -->
                    <div class="form-group mt-3 text-end">
                        <button id="btnConnectAction" class="btn btn-primary btn-sm" type="button">Connect</button>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card">
                    <div class="card-header border-0 fw-semibold header">
                        <span>Workflow Edit</span>
                        <div class="btn-group gap-2">
                            <div class="dropdown">
                                <button class="btn btn-primary dropdown-toggle btn-sm rounded-1" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Workflow Menu
                                </button>
                                <ul class="dropdown-menu border-0">
                                    <li><a class="dropdown-item" id="btnSaveWorkflow" href="#">Save</a></li>
                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                    <li><a class="dropdown-item" href="#">Delete</a></li>
                                </ul>
                            </div>
                            <button class="btn btn-primary btn-sm rounded-1" id="btnInsertNew" type="button">Insert New</button>
                            <button class="btn btn-primary btn-sm rounded-1" id="btnAddCondition" type="button" style="display: none;">Add Conition</button>
                        </div>
                    </div>
                    <div class="card-body text-center py-2 d-grid" style="height: calc(100vh - 204px); overflow-y: auto;">
                        <div class="timeLine">
                            <div class="start small">Start</div>
                            <div class="day">
                                <div class="items">
                                    @* <div class="item">

                                    </div> *@
                                </div>
                            </div>
                            <div class="end small">End</div>
                        </div>
                    </div>
                    <div class="card-footer text-end border-0">
                        <div class="btn-group gap-2">
                            <button class="btn btn-primary btn-sm rounded-1" type="button">Back</button>
                            <button class="btn btn-primary btn-sm rounded-1" type="button">Next</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="createConditionModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="createConditionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="createConditionModalLabel">Modal title</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0">

                <div class="form-group">
                    <label>Step</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-step"></i></span>
                        <select class="form-select" id="gotoConditionStep" placeholder="Select Step" autocomplete="off" aria-label="Default select example">
                        </select>
                    </div>
                </div>
                <div class="form-group mb-0">
                    <label>Condition</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-condition"></i></span>
                        <select class="form-select" id="stepCondition">
                            <option value="">-- Select Condition --</option>
                            <option value="ifcondition">If Condition</option>
                            <option value="elsecondition">Else Condition</option>
                        </select>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="btnSaveCondition">Save</button>
            </div>
        </div>
    </div>
</div>


@* <script src="~/lib/jqueryui/jquery-ui.min.js"></script> *@

@* <script src="~/lib/jqueryui/jquery-ui.js"></script> *@
@* <script src="~/lib/jqueryui/jquery-ui.min.js"></script> *@
@* <script src="~/js/workflowconfiguration/flowcreation.js"></script> *@

<!-- jQuery (must be first) -->
@* <script src="@Url.Content("~/lib/jquery/jquery.min.js")"></script> *@

<!-- jQuery UI (adds .sortable) -->
@* <script src="@Url.Content("~/lib/jqueryui/jquery-ui.min.js")"></script> *@

<!-- Your script that uses .sortable -->
@* <script src="@Url.Content("~/js/workflowconfiguration/flowcreation.js")"></script> *@


<script src="~/lib/jqueryui/jquery-ui.min.js" defer></script>

<script src="~/js/workflowconfiguration/flowcreation.js" defer></script>

@* <script src="~/js/workflowconfiguration/workflowgenerate.js" defer></script> *@

@section scripts{
    <script>
        $(document).ready(function() {
            // Function to show/hide dropdowns based on execution type
            function toggleDropdowns() {
                var isAutoSelected = $('#option1').is(':checked'); // Auto radio button
                var isManualSelected = $('#option2').is(':checked'); // Manual radio button

                if (isAutoSelected) {
                    // Show CP Profile and API's dropdowns, hide Step Owner dropdowns
                    $('#CPProfile').closest('.form-group').show();
                    $('#idApis').closest('.form-group').show();
                    $('#stepOwner').closest('.form-group').hide();
                    $('#stepAlternateOwner').closest('.form-group').hide();
                } else if (isManualSelected) {
                    // Show Step Owner dropdowns, hide CP Profile and API's dropdowns
                    $('#stepOwner').closest('.form-group').show();
                    $('#stepAlternateOwner').closest('.form-group').show();
                    $('#CPProfile').closest('.form-group').hide();
                    $('#idApis').closest('.form-group').hide();
                }
            }

            // Hide CP Profile and API's dropdowns on page load
            $('#CPProfile').closest('.form-group').hide();
            $('#idApis').closest('.form-group').hide();

            // Bind change event to radio buttons
            $('input[name="optionGroup"]').change(function() {
                toggleDropdowns();
            });

            // Initial call to set correct state based on default selection
            toggleDropdowns();
        });
    </script>
}