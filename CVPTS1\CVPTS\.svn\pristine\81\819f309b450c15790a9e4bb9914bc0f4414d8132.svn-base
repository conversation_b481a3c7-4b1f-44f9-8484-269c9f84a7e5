﻿// Sample Data for Dashboard Widgets

const SampleData = {
    // KPI Data
    kpis: [
        {
            title: "Total Revenue",
            value: "$125,430",
            icon: "fas fa-dollar-sign",
            change: "+12.5%",
            changeType: "positive"
        },
        {
            title: "Active Users",
            value: "8,542",
            icon: "fas fa-users",
            change: "+8.2%",
            changeType: "positive"
        },
        {
            title: "Conversion Rate",
            value: "3.24%",
            icon: "fas fa-chart-line",
            change: "-2.1%",
            changeType: "negative"
        },
        {
            title: "Page Views",
            value: "45,123",
            icon: "fas fa-eye",
            change: "+15.7%",
            changeType: "positive"
        }
    ],



    // Table Data
    tableData: [
        { id: 1, name: "<PERSON>", email: "<EMAIL>", status: "Active", revenue: "$1,234" },
        { id: 2, name: "<PERSON>", email: "<EMAIL>", status: "Active", revenue: "$2,456" },
        { id: 3, name: "<PERSON>", email: "<EMAIL>", status: "Inactive", revenue: "$789" },
        { id: 4, name: "<PERSON>", email: "<EMAIL>", status: "Active", revenue: "$3,456" },
        { id: 5, name: "Charlie Wilson", email: "<EMAIL>", status: "Pending", revenue: "$567" }
    ],

    // Progress Data
    progressData: [
        { label: "Project Alpha", value: 75, color: "success" },
        { label: "Project Beta", value: 45, color: "warning" },
        { label: "Project Gamma", value: 90, color: "info" },
        { label: "Project Delta", value: 30, color: "danger" }
    ],

    // Weather Data
    weatherData: {
        location: "New York, NY",
        temperature: 72,
        condition: "Partly Cloudy",
        icon: "fas fa-cloud-sun",
        humidity: 65,
        windSpeed: 8,
        forecast: [
            { day: "Today", high: 75, low: 62, icon: "fas fa-cloud-sun" },
            { day: "Tomorrow", high: 78, low: 65, icon: "fas fa-sun" },
            { day: "Wednesday", high: 73, low: 59, icon: "fas fa-cloud-rain" }
        ]
    },

    // Calendar Data
    calendarData: {
        currentDate: new Date(),
        events: [
            { date: "2024-01-15", title: "Team Meeting", time: "10:00 AM" },
            { date: "2024-01-16", title: "Project Review", time: "2:00 PM" },
            { date: "2024-01-18", title: "Client Call", time: "11:00 AM" }
        ]
    },

    // News Data
    newsData: [
        {
            title: "Dashboard Analytics Show Strong Growth",
            summary: "Latest metrics indicate 25% increase in user engagement...",
            time: "2 hours ago",
            category: "Business"
        },
        {
            title: "New Features Released",
            summary: "Enhanced widget customization and improved performance...",
            time: "5 hours ago",
            category: "Technology"
        },
        {
            title: "Market Update",
            summary: "Stock markets show positive trends with tech sector leading...",
            time: "1 day ago",
            category: "Finance"
        }
    ],

    // Social Media Data
    socialData: {
        followers: 12500,
        likes: 8750,
        shares: 2340,
        comments: 1890,
        engagement: 85.2,
        recentPosts: [
            { platform: "Twitter", content: "Just launched our new dashboard!", likes: 45, time: "2h" },
            { platform: "LinkedIn", content: "Check out our latest analytics...", likes: 78, time: "4h" },
            { platform: "Facebook", content: "Thank you for 10k followers!", likes: 156, time: "1d" }
        ]
    },

    // Gauge Data
    gaugeData: [
        { label: "CPU Usage", value: 68, max: 100, color: "#28a745" },
        { label: "Memory", value: 82, max: 100, color: "#ffc107" },
        { label: "Disk Space", value: 45, max: 100, color: "#17a2b8" },
        { label: "Network", value: 91, max: 100, color: "#dc3545" }
    ],

    // Map Data
    mapData: {
        center: { lat: 40.7128, lng: -74.0060 }, // New York
        markers: [
            { lat: 40.7128, lng: -74.0060, title: "New York Office", info: "Main Headquarters" },
            { lat: 34.0522, lng: -118.2437, title: "Los Angeles Office", info: "West Coast Branch" },
            { lat: 41.8781, lng: -87.6298, title: "Chicago Office", info: "Midwest Operations" }
        ]
    },

    // Text Content
    textContent: {
        welcome: {
            title: "Welcome to Dashboard Builder",
            content: `
                <p>This is a fully customizable dashboard where you can:</p>
                <ul>
                    <li>Drag and drop widgets to rearrange them</li>
                    <li>Resize widgets to fit your needs</li>
                    <li>Add, duplicate, or delete widgets</li>
                    <li>Switch to preview mode for a clean view</li>
                    <li>Save and load your dashboard configurations</li>
                </ul>
                <p><strong>Get started by adding widgets from the menu above!</strong></p>
            `
        },
        analytics: {
            title: "Analytics Overview",
            content: `
                <h5>Key Insights</h5>
                <p>Our analytics show strong performance across all metrics:</p>
                <ul>
                    <li>Revenue growth of 12.5% this quarter</li>
                    <li>User engagement up 8.2%</li>
                    <li>Page views increased by 15.7%</li>
                </ul>
                <p class="text-muted">Last updated: ${new Date().toLocaleDateString()}</p>
            `
        }
    },

    // Default Dashboard Layout (Gridstack format)
    defaultLayout: [
        //{
        //    id: 'widget-1',
        //    type: 'kpi',
        //    title: 'Total Revenue',
        //    x: 0, y: 0, w: 3, h: 5,
        //    data: 0 // Index in kpis array
        //},
        //{
        //    id: 'widget-2',
        //    type: 'kpi',
        //    title: 'Active Users',
        //    x: 3, y: 0, w: 3, h: 5,
        //    data: 1
        //},
        //{
        //    id: 'widget-3',
        //    type: 'weather',
        //    title: 'Weather',
        //    x: 6, y: 0, w: 3, h: 5,
        //    data: 'weatherData'
        //},
        //{
        //    id: 'widget-4',
        //    type: 'clock',
        //    title: 'Current Time',
        //    x: 9, y: 0, w: 3, h: 5,
        //    data: null
        //},
        //{
        //    id: 'widget-5',
        //    type: 'amchart-line',
        //    title: 'Sales Trend',
        //    x: 0, y: 3, w: 6, h: 4,
        //    data: null
        //},
        //{
        //    id: 'widget-6',
        //    type: 'table',
        //    title: 'Customer Data',
        //    x: 6, y: 5, w: 6, h: 4,
        //    data: 'tableData'
        //},
        //{
        //    id: 'widget-7',
        //    type: 'gauge',
        //    title: 'System Metrics',
        //    x: 9, y: 3, w: 3, h: 4,
        //    data: 'gaugeData'
        //},
        //{
        //    id: 'widget-8',
        //    type: 'news',
        //    title: 'Latest News',
        //    x: 0, y: 7, w: 5, h: 6,
        //    data: 'newsData'
        //},
        //{
        //    id: 'widget-9',
        //    type: 'social',
        //    title: 'Social Media',
        //    x: 5, y: 9, w: 4, h: 5,
        //    data: 'socialData'
        //},
        //{
        //    id: 'widget-10',
        //    type: 'amchart-pie',
        //    title: 'Device Usage',
        //    x: 9, y: 9, w: 3, h: 4,
        //    data: null
        //},
        //{
        //    id: 'widget-11',
        //    type: 'amchart-area',
        //    title: 'Area Chart',
        //    x: 0, y: 13, w: 6, h: 4,
        //    data: null
        //},
        //{
        //    id: 'widget-12',
        //    type: 'amchart-scatter',
        //    title: 'Scatter Plot',
        //    x: 6, y: 13, w: 6, h: 4,
        //    data: null
        //}
    ]
};


