﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

using BCM.Shared;
using System.Linq;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;
using Microsoft.AspNetCore.Components.Routing;
using System.ComponentModel.Design;
using System.Xml;
using System;
using BCM.UI.Controllers;

namespace BCM.UI.Areas.BCMFunctionRecoveryPlan.Controllers;
[Area("BCMFunctionRecoveryPlan")]
public class ManageRecoveryPlansController : BaseController
{
    private ProcessSrv _ProcessSrv;
    readonly Utilities _Utilities;
    readonly CVLogger _CVLogger;
    private readonly ILoggerFactory? _LoggerFactory;
    int iEntityTypeID = 0;
    string strLoggerName = "ManageRecoveryPlans";

    public ManageRecoveryPlansController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        iEntityTypeID = Convert.ToInt32(BCPEnum.EntityType.RecoveryPlan);
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult ManageRecoveryPlans(int? upcoming, int? past)
    {
        List<RecoveryPlan> objRecoveryPlan = new List<RecoveryPlan>();
        try
        {
            PopulateDropDown();
            objRecoveryPlan = _ProcessSrv.GetRecoveryPlansList(Convert.ToInt32(_UserDetails.UserID), Convert.ToInt32(_UserDetails.OrgID));
            //List<RecoveryPlan> lstRecoveryPlan = _ProcessSrv.GetRecoveryPlansList(2, 0);
            objRecoveryPlan = objRecoveryPlan.Where(x => x.IsActive == 1).ToList();
            ViewBag.RecoveryPlan = objRecoveryPlan;

            if (upcoming == 1)
            {
                var today = DateTime.Today;
                var next7 = today.AddDays(7);

                objRecoveryPlan = objRecoveryPlan
                .Where(x =>
                    x.LastRevisionDate >= today && x.LastRevisionDate <= next7
                )
                .ToList();
                ViewBag.RecoveryPlan = objRecoveryPlan;
            }

            if (past == 1)
            {
                var today = DateTime.Today;

                objRecoveryPlan = objRecoveryPlan
                 .Where(r =>  r.LastRevisionDate < today)
                .ToList();
                ViewBag.RecoveryPlan = objRecoveryPlan;
            }

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }

        return View();
    }


    [HttpGet]
    public IActionResult AddRecoveryPlan()

    {
        RecoveryPlan objRecoveryPlan = new RecoveryPlan();

        try
        {
            PopulateDropDown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_AddRecoveryPlan", new RecoveryPlan());
    }


    [HttpPost]
    public int AddRecoveryPlan([FromBody] RecoveryPlan objRecoveryPlanInfo)
    {
        int iPlanID = 0;
        try
        {
            //objRecoveryPlanInfo.ID = 0;
            //objRecoveryPlanInfo.PlanStageID = 0;
            //objRecoveryPlanInfo.IncidentType = "0";
            ////objRecoveryPlanInfo.PlanCreateDate = DateTime.Now;
            ////objRecoveryPlanInfo.PlanReviewDate = DateTime.Now;
            //objRecoveryPlanInfo.PlanReviewDate = objRecoveryPlanInfo.PlanReviewDate == null ? DateTime.Now : objRecoveryPlanInfo.PlanReviewDate;
            //objRecoveryPlanInfo.IsActive = 1;
            //objRecoveryPlanInfo.IsEffective = objRecoveryPlanInfo.ID == 0 ? 0 : 1;
            //objRecoveryPlanInfo.ChangedBy = _UserDetails.UserID;
            //objRecoveryPlanInfo.CreatedBy = _UserDetails.UserID;
            //objRecoveryPlanInfo.EntityType = (iEntityTypeID).ToString();

            //TimeSpan timeSpan = TimeSpan.Parse(objRecoveryPlanInfo.EstimatedRecoveryTime);
            //objRecoveryPlanInfo.EstimatedRecoveryTime = (Convert.ToInt32((timeSpan.Hours * 60)) + Convert.ToInt32(timeSpan.Minutes)).ToString();

            //iPlanID = _ProcessSrv.RecoveryPlanSave(objRecoveryPlanInfo);

            objRecoveryPlanInfo.PlanStageID = 0;
            objRecoveryPlanInfo.IncidentType = "0";
            //objRecoveryPlanInfo.PlanCreateDate = DateTime.Now;
            //objRecoveryPlanInfo.PlanReviewDate = DateTime.Now;
            objRecoveryPlanInfo.PlanReviewDate = objRecoveryPlanInfo.PlanReviewDate == null ? DateTime.Now : objRecoveryPlanInfo.PlanReviewDate;
            objRecoveryPlanInfo.LastReviewDate = objRecoveryPlanInfo.LastReviewDate == null ? DateTime.Now : objRecoveryPlanInfo.LastReviewDate;
            objRecoveryPlanInfo.IsActive = 1;
            objRecoveryPlanInfo.IsEffective = objRecoveryPlanInfo.ID == 0 ? 0 : 1;
            objRecoveryPlanInfo.ChangedBy = _UserDetails.UserID;
            objRecoveryPlanInfo.CreatedBy = _UserDetails.UserID;
            objRecoveryPlanInfo.EntityType = (iEntityTypeID).ToString();
            TimeSpan timeSpan = TimeSpan.Parse(objRecoveryPlanInfo.EstimatedRecoveryTime);
            objRecoveryPlanInfo.EstimatedRecoveryTime = (Convert.ToInt32((timeSpan.Hours * 60)) + Convert.ToInt32(timeSpan.Minutes)).ToString();

            iPlanID = _ProcessSrv.RecoveryPlanSave(objRecoveryPlanInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        //return RedirectToAction("ManageRecoveryPlans");
        return iPlanID;
    }


    [HttpGet]
    public IActionResult EditRecoveryPlan(string id)
    {
        RecoveryPlan objRecoveryPlan = new RecoveryPlan();
        try
        {
            if (Convert.ToInt32(id) > 0)
            {
                objRecoveryPlan = _ProcessSrv.GetRecoveryPlanByID(Convert.ToInt32(id));

                objRecoveryPlan.EstimatedRecoveryTime = TimeSpan.FromMinutes(Convert.ToInt32(objRecoveryPlan.EstimatedRecoveryTime)).ToString(@"hh\:mm");
                PopulateDropDown();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_EditRecoveryPlan", objRecoveryPlan);
    }


    [HttpPost]
    public int EditRecoveryPlan([FromBody] RecoveryPlan objRecoveryPlanInfo)
    {
        int iPlanID = 0;
        try
        {
            objRecoveryPlanInfo.PlanStageID = 0;
            objRecoveryPlanInfo.IncidentType = "0";
            //objRecoveryPlanInfo.PlanCreateDate = DateTime.Now;
            //objRecoveryPlanInfo.PlanReviewDate = DateTime.Now;
            objRecoveryPlanInfo.PlanReviewDate = objRecoveryPlanInfo.PlanReviewDate == null ? DateTime.Now : objRecoveryPlanInfo.PlanReviewDate;
            objRecoveryPlanInfo.LastReviewDate = objRecoveryPlanInfo.LastReviewDate == null ? DateTime.Now : objRecoveryPlanInfo.LastReviewDate;
            objRecoveryPlanInfo.IsActive = 1;
            objRecoveryPlanInfo.IsEffective = objRecoveryPlanInfo.ID == 0 ? 0 : 1;
            objRecoveryPlanInfo.ChangedBy = _UserDetails.UserID;
            objRecoveryPlanInfo.CreatedBy = _UserDetails.UserID;
            objRecoveryPlanInfo.EntityType = (iEntityTypeID).ToString();
            TimeSpan timeSpan = TimeSpan.Parse(objRecoveryPlanInfo.EstimatedRecoveryTime);
            objRecoveryPlanInfo.EstimatedRecoveryTime = (Convert.ToInt32((timeSpan.Hours * 60)) + Convert.ToInt32(timeSpan.Minutes)).ToString();

            iPlanID = _ProcessSrv.RecoveryPlanSave(objRecoveryPlanInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        //return RedirectToAction("ManageRecoveryPlans");
        return iPlanID;
    }

    [HttpGet]
    public JsonResult GetRecoveryPlansForChatbot()
    {
        List<RecoveryPlan> objRecoveryPlan = new List<RecoveryPlan>();
        try
        {
            objRecoveryPlan = _ProcessSrv.GetRecoveryPlansList(Convert.ToInt32(_UserDetails.UserID), Convert.ToInt32(_UserDetails.OrgID));
            objRecoveryPlan = objRecoveryPlan.Where(x => x.IsActive == 1 && x.PlanStageID == 2).ToList(); // Only approved plans

            var recoveryPlansData = objRecoveryPlan.Select(x => new
            {
                ID = x.ID,
                PlanName = x.PlanName,
                PlanCode = x.PlanCode,
                PlanOwnerName = x.PlanOwnerName,
                OrgName = x.OrgName,
                UnitName = x.UnitName,
                DepartmentName = x.DepartmentName,
                EstimatedRecoveryTime = x.EstimatedRecoveryTime,
                PlanDescription = x.PlanDescription
            }).ToList();

            return Json(recoveryPlansData);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { error = "Failed to retrieve recovery plans" });
        }
    }

    [HttpPost]
    public JsonResult ExecuteRecoveryPlan(int planId)
    {
        try
        {
            var recoveryPlan = _ProcessSrv.GetRecoveryPlanByID(planId);
            if (recoveryPlan != null)
            {
                // Here you would implement the actual execution logic
                // For now, we'll return a success message with plan details
                return Json(new
                {
                    success = true,
                    message = $"Recovery Plan '{recoveryPlan.PlanName}' has been successfully executed.",
                    planDetails = new
                    {
                        PlanName = recoveryPlan.PlanName,
                        PlanCode = recoveryPlan.PlanCode,
                        PlanOwnerName = recoveryPlan.PlanOwnerName,
                        EstimatedRecoveryTime = recoveryPlan.EstimatedRecoveryTime,
                        PlanDescription = recoveryPlan.PlanDescription
                    }
                });
            }
            else
            {
                return Json(new { success = false, message = "Recovery plan not found." });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "Failed to execute recovery plan." });
        }
    }

    [HttpGet]
    public IActionResult DeleteRecoveryPlan(string id)
    {
        RecoveryPlan objRecoveryPlan = new RecoveryPlan();
        try
        {
            if (Convert.ToInt32(id) > 0)
            {
                objRecoveryPlan = _ProcessSrv.GetRecoveryPlanByID(Convert.ToInt32(id));
                PopulateDropDown();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_DeleteRecoveryPlan", objRecoveryPlan);
    }


    [HttpPost]
    public IActionResult DeleteRecoveryPlan(RecoveryPlan objRecoveryPlan)
    {
        bool success = false;
        try
        {
            success = _ProcessSrv.RecoveryPlanDelete(Convert.ToInt32(objRecoveryPlan.ID));

            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new { success = success, message = success ? "Recovery plan deleted successfully." : "Failed to delete recovery plan." });
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new { success = false, message = "An error occurred while deleting the recovery plan." });
            }
        }
        return RedirectToAction("ManageRecoveryPlans");
    }


    public void PopulateDropDown(int iOrgGroupID = 0, int iOrgID = 0, int iUnitID = 0, int iDepartmentID = 0, int iSubDepartmentID = 0)
    {
        try
        {
            //ViewBag.OrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown(iOrgGroupID == 0 ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString());
            ViewBag.OrgInfo = _Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(), _UserDetails.UserRoleID.ToString());

            //ViewBag.Unit = _ProcessSrv.GetOrganizationUnitListByOrgID(iOrgID == 0 ? _UserDetails.OrgID : iOrgID);
            ViewBag.Unit = _Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString() != "0" ? _UserDetails.OrgGroupID.ToString() : iOrgGroupID.ToString(), iOrgID.ToString() == "0" ? _UserDetails.OrgID.ToString() : iOrgID.ToString(), _UserDetails.UserRoleID.ToString()); ;

            ViewBag.Department = _ProcessSrv.GetDepartmentByUnitId(iUnitID == 0 ? _UserDetails.UnitID : iUnitID);

            ViewBag.ResourceList = _Utilities.GetAllResourceList();

            ViewBag.Subdepartment = _Utilities.GetAllSubDepartmentListDropdown();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }


    public IActionResult GetFileredRecoveryPlans(int OrgID = 0, int UnitID = 0, int DepartmentID = 0, int SubDepartmentID = 0)
    {
        try
        {
            PopulateDropDown(_UserDetails.OrgGroupID, OrgID, UnitID, DepartmentID, SubDepartmentID);
            //List<RecoveryPlan> lstRecoveryPlan = _ProcessSrv.GetRecoveryPlansList(2, 0);
            List<RecoveryPlan> lstRecoveryPlan = _ProcessSrv.GetRecoveryPlansList(Convert.ToInt32(_UserDetails.UserID), Convert.ToInt32(_UserDetails.OrgID));
            //lstRecoveryPlan = lstRecoveryPlan.Where(x => x.IsActive == 1).ToList();

            lstRecoveryPlan = lstRecoveryPlan.Where(x => x.IsActive == 1).ToList();
            string strFilter = "x => 1==1";

            if (OrgID > 0)
            {
                strFilter = " && x => x.OrgID == " + OrgID;
                lstRecoveryPlan = lstRecoveryPlan.Where(x => x.OrgID == OrgID).ToList();
            }
            if (UnitID > 0)
            {
                strFilter = " && x => x.UnitID == " + UnitID;
                lstRecoveryPlan = lstRecoveryPlan.Where(x => x.UnitID == UnitID).ToList();
            }
            if (DepartmentID > 0)
            {
                strFilter = " && x => x.DepartmentID == " + DepartmentID;
                lstRecoveryPlan = lstRecoveryPlan.Where(x => x.DepartmentID == DepartmentID).ToList();
            }
            if (SubDepartmentID > 0)
            {
                strFilter = " && x => x.SubfunctionID == " + SubDepartmentID;
                lstRecoveryPlan = lstRecoveryPlan.Where(x => x.SubfunctionID == SubDepartmentID).ToList();
            }

            ViewBag.RecoveryPlan = lstRecoveryPlan;
            return PartialView("_FilteredRecoveryPlan", lstRecoveryPlan);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("ManageRecoveryPlans");
    }
}

