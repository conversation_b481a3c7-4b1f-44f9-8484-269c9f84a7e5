﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.BusinessProcessComponentsContext;
using BCM.Security.Helper;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using static BCM.Shared.BCPEnum;


namespace BCM.UI.Areas.BCMTeams.Controllers;
[Area("BCMTeams")]
public class BCMGroupsNotificationController : BaseController
{
    private ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    private readonly BCPIncidentNotification _Incident;
    private readonly CVLogger _CVLogger;
    private readonly BCMMail _BCMMail;
    private readonly BCPSms _BCPSms;
    string strLoggerName = "BCMGroupsNotification";
    const string FYA = "A";
    const string FYI = "I";

    int iNotifyUsersAsFYACheckedCount = 0;
    string strNotifyUsersAsFYA = string.Empty;
    int iNotifyUsersAsFYICheckedCount = 0;
    string strNotifyUsersAsFYI = string.Empty;

    int iNotifyTeamsAsFYACheckedCount = 0;
    string strNotifyTeamsAsFYA = string.Empty;
    int iNotifyTeamsAsFYICheckedCount = 0;
    string strNotifyTeamsAsFYI = string.Empty;

    public BCMGroupsNotificationController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger, BCPIncidentNotification Incident,
        BCMMail BCMMail, BCPSms BCPSms) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
        _Incident = Incident;
        _BCMMail = BCMMail;
        _BCPSms = BCPSms;
    }

    [HttpGet]
    public IActionResult BCMGroupsNotification([FromQuery] int iGrpId)
    {
        BCMTeamsAndResources objBCMTeamsAndResources = new BCMTeamsAndResources();
        try
        {
            if (!_Utilities.EnsureUserLoggedIn(_UserDetails))
            {
                return RedirectToAction("Login", "Login", new { area = "" });
            }
            HttpContext.Session.SetInt32("objGrpMapID", iGrpId);

            ViewBag.selectedOrgID = _UserDetails.OrgID;
            ViewBag.OrgInfo = new SelectList(BindOrg(), "Id", "OrganizationName");
            ViewBag.UnitList = new SelectList(BindOrgUnit(_UserDetails.OrgID), "UnitID", "UnitName");
            GetIncidentData();
            StepTimeUnit();
            objBCMTeamsAndResources.ResourceList = GetResource();
            objBCMTeamsAndResources.BCMGroupList = GetBcmTeams(iGrpId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView("_NotifyTeams", objBCMTeamsAndResources);
    }

    public List<OrgInfo> BindOrg()
    {
        List<OrgInfo> lstOrgInfo = new List<OrgInfo>();
        try
        {
            if (_UserDetails.UserRole == "ProductAdminRole")
            {

            }
            else if (_UserDetails.UserRole == "SuperAdminRole")
            {

            }
            else
            {
                lstOrgInfo = _Utilities.GetOrganizationListByOrgGroupID_ForDropdown();
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return lstOrgInfo;
    }

    public void StepTimeUnit()
    {
        try
        {
            ViewBag.TimeUnit = new SelectList(_Utilities.PopulateStepTimeUnit(), "ID", "Name");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public List<OrgUnit> BindOrgUnit(int iOrgID)
    {
        List<OrgUnit> lstOrgUnit = new List<OrgUnit>();
        try
        {
            if (_UserDetails.UserRole == "ProductAdminRole")
            {

            }
            else if (_UserDetails.UserRole == "SuperAdminRole")
            {

            }
            else
            {
                lstOrgUnit = _Utilities.GetUnitListByOrgID(iOrgID);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return lstOrgUnit;
    }

    //[HttpGet]
    //public List<IncidentManagement> BindIncidentManagement(int iUnitID = 0)
    //{
    //    List<IncidentManagement> lstIncidentManagement = new List<IncidentManagement>();
    //    try
    //    {
    //        //ViewBag.IncidentManagement = new SelectList(GetIncidentData(iUnitID), "Id", "EventName");
    //        ViewBag.IncidentManagement = new SelectList(_ProcessSrv.GetIncidentManagementList(_UserDetails.OrgID), "Id", "EventName");
    //        //return GetIncidentData(iUnitID);
    //    }
    //    catch (Exception)
    //    {
    //    }
    //    return lstIncidentManagement;
    //}

    [HttpGet]
    public JsonResult GetIncidentData(int iUnitID = 0)
    {
        List<IncidentManagement> lstIncidentManagement = new List<IncidentManagement>();
        List<IncidentManagement> filteredIncidentManagementList = new List<IncidentManagement>();
        try
        {
            if (_UserDetails.UserRole == "ProductAdminRole")
            {

            }
            else if (_UserDetails.UserRole == "SuperAdminRole")
            {

            }
            else
            {
                lstIncidentManagement = _ProcessSrv.GetIncidentManagementList(_UserDetails.OrgID);
            }

            if (lstIncidentManagement != null)
            {
                filteredIncidentManagementList = lstIncidentManagement.Where(x => x.UnitID == iUnitID).ToList();
            }

            string strEventName;
            foreach (var item in filteredIncidentManagementList)
            {
                strEventName=string.Empty;
                if ((item.Status.Equals(((int)BCPEnum.IncidentStatus.TaskAssigned).ToString()) || 
                    item.Status.Equals(((int)BCPEnum.IncidentStatus.InProgress).ToString())))
                {
                    if (item.NotifiedAs.ToString() == Convert.ToInt32(BCPEnum.IncidentNotfnType.Live).ToString())
                    {
                        strEventName = BCPEnum.IncidentNotfnType.Live.ToString();
                    }
                    else
                    {
                        strEventName = BCPEnum.IncidentNotfnType.Drill.ToString();
                    }
                    item.EventName = strEventName + " - " + item.EventName;
                }
            }

            

            ViewBag.IncidentManagement = new SelectList(filteredIncidentManagementList, "Id", "EventName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(filteredIncidentManagementList);
    }

    //public BCMTeamsAndResources GetBCMTeamsAndResource()
    //{        
    //    var objBCMTeamsAndResources = new BCMTeamsAndResources();
    //    try
    //    {
    //        var objResourceList = _Utilities.GetAllResourceList();
    //        ViewData["ResourceList"] = objResourceList;
    //        var objBCMTeamsList = _ProcessSrv.GetBCMGroupAllList();
    //        ViewBag.TimeOutUnit = new SelectList(_Utilities.PopulateStepTimeUnit().ToString(), "TimeUnit.ID", "TimeUnit.Name");

    //        objBCMTeamsAndResources = new BCMTeamsAndResources{ ResourceList= objResourceList, BCMGroupList= objBCMTeamsList };
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }
    //    return objBCMTeamsAndResources;
    //}

    //public BCMTeamsAndResources GetBCMTeamsAndResource()
    //{
    //    var objBCMTeamsAndResources = new BCMTeamsAndResources();
    //    try
    //    {
    //        var objResourceList = _Utilities.GetAllResourceList();
    //        ViewData["ResourceList"] = objResourceList;
    //        var objBCMTeamsList = _ProcessSrv.GetBCMGroupAllList();
    //        ViewBag.TimeOutUnit = new SelectList(_Utilities.PopulateStepTimeUnit().ToString(), "TimeUnit.ID", "TimeUnit.Name");

    //        objBCMTeamsAndResources = new BCMTeamsAndResources { ResourceList = objResourceList, BCMGroupList = objBCMTeamsList };
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //    }
    //    return objBCMTeamsAndResources;
    //}

    public List<ResourcesInfo> GetResource()
    {
        List<ResourcesInfo> lstResourcesInfo = new List<ResourcesInfo>();
        try
        {
            lstResourcesInfo = _Utilities.GetAllResourceList();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return lstResourcesInfo;
    }

    public List<BCMGroupInfo> GetBcmTeams(int iGrpId)
    {
        List<BCMGroupInfo> lstBCMGroupInfo = new List<BCMGroupInfo>();
        try
        {
            lstBCMGroupInfo = _ProcessSrv.GetBCMGroupAllList();
            lstBCMGroupInfo.RemoveAll(x => x.GroupMapID == iGrpId);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return lstBCMGroupInfo;
    }

    [HttpGet]
    public PartialViewResult SearchUsers(string strUserName, string strSearchId)
    {
        string strPartialView = string.Empty;
        List<ResourcesInfo> lstResourcesInfo = new List<ResourcesInfo>();
        try
        {
            lstResourcesInfo = _Utilities.GetAllResourceList();
            if (strUserName != null)
            {
                //lstResourcesInfo = lstResourcesInfo.Where(x => x.ResourceName.ToLower().Contains(strUserName.ToLower())).ToList();
                lstResourcesInfo = lstResourcesInfo.Where(u => string.IsNullOrEmpty(strUserName) || u.ResourceName.Contains(strUserName, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            if (strSearchId == "usersFYADiv")
            {
                strPartialView = "_FilteredFYAUsers";
            }
            else if (strSearchId == "usersFYIDiv")
            {
                strPartialView = "_FilteredFYIUsers";
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return PartialView(strPartialView, lstResourcesInfo);
    }

    #region SendNotifyMail    

    //[HttpPost]
    //public IActionResult NotifyClick([FromBody] NotificationRequest objRequest, IFormFile file)
    //{
    //    int iGroupMapID = HttpContext.Session.GetInt32("objGrpMapID") ?? 0;
    //    try
    //    {
    //        if (!_Utilities.ValidateSelectedResourceOrTeams(objRequest.UsersFYA.Count, objRequest.UsersFYI.Count, objRequest.TeamsFYA.Count, objRequest.TeamsFYI.Count))
    //        {
    //            return Json(new { success = false, message = "Please select at least one USER or TEAM..." });
    //        }

    //        if (_UserDetails.Password != objRequest.TxtPassword)
    //        {
    //            return Json(new { success = false, message = "Please enter a valid password..." });
    //        }

    //        if (string.IsNullOrWhiteSpace(objRequest.TxtMsg) || string.IsNullOrWhiteSpace(objRequest.TxtSubject))
    //        {
    //            return Json(new { success = false, message = "Subject and Message cannot be empty..." });
    //        }

    //        ViewBag.usersFYA = objRequest.UsersFYA;
    //        ViewBag.usersFYI = objRequest.UsersFYI;
    //        ViewBag.teamsFYA = objRequest.TeamsFYA;
    //        ViewBag.teamsFYI = objRequest.TeamsFYI;

    //        bool isSuccess = BtnNotify_Click(objRequest.TxtMsg.Trim(), objRequest.TxtSubject.Trim(), objRequest.IChkResponse, objRequest.DdlNotification,
    //            objRequest.TxtTimeTaken, objRequest.DdlTimeOut);

    //        if (isSuccess)
    //        {
    //            return Json(new { success = true, message = "Notification sent successfully..." });
    //        }

    //        return Json(new { success = false, message = "Error occurred while sending mail..." });
    //    }
    //    catch (Exception ex)
    //    {
    //        _CVLogger.LogErrorApp(ex);
    //        return Json(new { success = false, message = "An error occurred. Please try again later..." });
    //    }
    //}
    [HttpPost]
    public IActionResult NotifyClick([FromForm] NotificationRequest objRequest, IFormFile file)
    {
        int iGroupMapID = HttpContext.Session.GetInt32("objGrpMapID") ?? 0;
        try
        {
            if (!_Utilities.ValidateSelectedResourceOrTeams(objRequest.UsersFYA.Count, objRequest.UsersFYI.Count,
                objRequest.TeamsFYA.Count, objRequest.TeamsFYI.Count))
            {
                return Json(new { success = false, message = "Please select at least one USER or TEAM..." });
            }

            string passWord = CryptographyHelper.Decrypt(_UserDetails.Password);

            if (passWord != objRequest.TxtPassword)
            {
                return Json(new { success = false, message = "Please enter a valid password..." });
            }

            if (string.IsNullOrWhiteSpace(objRequest.TxtMsg) || string.IsNullOrWhiteSpace(objRequest.TxtSubject))
            {
                return Json(new { success = false, message = "Subject and Message cannot be empty..." });
            }

            ViewBag.usersFYA = objRequest.UsersFYA;
            ViewBag.usersFYI = objRequest.UsersFYI;
            ViewBag.teamsFYA = objRequest.TeamsFYA;
            ViewBag.teamsFYI = objRequest.TeamsFYI;

            bool isSuccess = BtnNotify_Click(objRequest.TxtMsg.Trim(), objRequest.TxtSubject.Trim(), objRequest.IChkResponse,
                objRequest.DdlNotification,objRequest.TxtTimeTaken, objRequest.DdlTimeOut, file);

            if (isSuccess)
            {
                return Json(new { success = true, message = "Notification sent successfully..." });
            }

            return Json(new { success = false, message = "Error occurred while sending mail..." });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, message = "An error occurred. Please try again later..." });
        }
    }


    protected bool BtnNotify_Click(string strMailBody, string strSubject, int iChkResponse, string strNotifyType, 
        string strTimeTaken, string strTimeOut, IFormFile objFile)
    {
        bool bSuccess = false;
        List<BCMGroupResources> lstBCMGroupResources = new List<BCMGroupResources>();
        int iGroupMapID;
        try
        {
            iGroupMapID = HttpContext.Session.GetInt32("objGrpMapID") ?? 0;
            if (iGroupMapID > 0)
            {
                lstBCMGroupResources = _ProcessSrv.GetBCMGroupMemberResourcesList(0, 0, iGroupMapID, 0, 0, 0, "");
            }
            BCMGroupNotification objBcmGrpNotfn = new BCMGroupNotification();
            objBcmGrpNotfn.groupMapId = iGroupMapID;
            objBcmGrpNotfn.NotifiedBy = _UserDetails.UserID;
            objBcmGrpNotfn.NeedResponse = iChkResponse.ToString();//bChkResponse == false ? "0" : "1";
            objBcmGrpNotfn.NotifiedFYIA = FYA;
            objBcmGrpNotfn.IncidentID = 0;
            objBcmGrpNotfn.Subject = strSubject;

            const string strStartmsg = "Dear User, This is to inform you that ";
            const string strEndMsg = " Please reply RESP Yxxx for Yes and RESP Nxxx for No to @@@@@.";

            if (objBcmGrpNotfn.NeedResponse == "1")
                objBcmGrpNotfn.MailBody = strStartmsg + strMailBody + strEndMsg;
            else
                objBcmGrpNotfn.MailBody = strStartmsg + strMailBody;

            objBcmGrpNotfn.NotiType = strNotifyType;
            objBcmGrpNotfn.MgtGroupMapId = 0;
            objBcmGrpNotfn.GUIDAttachment = "";

            if (!string.IsNullOrEmpty(strTimeTaken))
                objBcmGrpNotfn.EndTime = _Utilities.GetTimeUnit(strTimeTaken, strTimeOut);
            //else
            //    //objBcmGrpNotfn.EndTime = _Utilities.GetTimeUnit("0", strTimeOut);
            //    objBcmGrpNotfn.EndTime = null;

            if (string.IsNullOrEmpty(strTimeTaken))
            {
                strTimeTaken = "1";
            }

            string strTimeUnitText = string.Empty;
            switch (strTimeOut)
            {
                case "1":
                    strTimeUnitText = "Minutes";
                    break;
                case "2":
                    strTimeUnitText = "Hours";
                    break;
                case "3":
                    strTimeUnitText = "Days";
                    break;
                case "4":
                    strTimeUnitText = "Months";
                    break;
                default:
                    strTimeUnitText = string.Empty;
                    break;
            }
            objBcmGrpNotfn.TimeUnit = strTimeUnitText;
            objBcmGrpNotfn.NotificationID = Convert.ToInt32(_ProcessSrv.BCMGroupNotifnSave(objBcmGrpNotfn, "0"));

            #region File Upload

            if (objBcmGrpNotfn.NotificationID > 0 && objFile != null && objFile.Length > 0)
            {
                int iAttachment = AttachmentSave(objFile, iGroupMapID, objBcmGrpNotfn.NotificationID);
                if (iAttachment > 1)
                {
                    return false;
                }
            }

            #endregion

            string strAppUrl = $"{this.Request.Scheme}://{this.Request.Host}{this.Request.PathBase}";
            SendNotification_ForUsers(objBcmGrpNotfn, lstBCMGroupResources, FYA, strSubject, strMailBody, false, objBcmGrpNotfn.NeedResponse, strAppUrl);

            foreach (var objTeamsFYA in ViewBag.teamsFYA)
            {
                objBcmGrpNotfn.groupMapId = objTeamsFYA;
                lstBCMGroupResources = _ProcessSrv.GetBCMGroupMemberResourcesList(0, 0, objTeamsFYA, 0, 0, 0, "");
                if (lstBCMGroupResources.Count > 0)
                {
                    SendNotification(objBcmGrpNotfn, lstBCMGroupResources, Convert.ToInt32(BCPEnum.NotificationAs.FYA), strSubject + " - FYA", strMailBody, 
                        false, objBcmGrpNotfn.NeedResponse, strAppUrl);
                    iNotifyTeamsAsFYACheckedCount = 1;
                    BCMGroupInfo objGroupInfo = _ProcessSrv.GetBCMGroupByID(objTeamsFYA);
                    strNotifyTeamsAsFYA += objGroupInfo.GroupName + ", ";
                }
            }

            foreach (var objTeamsFYI in ViewBag.teamsFYI)
            {
                objBcmGrpNotfn.groupMapId = objTeamsFYI;
                lstBCMGroupResources = _ProcessSrv.GetBCMGroupMemberResourcesList(0, 0, objTeamsFYI, 0, 0, 0, "");
                if (lstBCMGroupResources.Count > 0)
                {
                    SendNotification(objBcmGrpNotfn, lstBCMGroupResources, Convert.ToInt32(BCPEnum.NotificationAs.FYI), strSubject + " - FYI", strMailBody, 
                        false, objBcmGrpNotfn.NeedResponse, strAppUrl);
                    iNotifyTeamsAsFYICheckedCount = 1;
                    BCMGroupInfo objGroupInfo = _ProcessSrv.GetBCMGroupByID(objTeamsFYI);
                    strNotifyTeamsAsFYI += objGroupInfo.GroupName + ", ";
                }
            }

            if (iNotifyUsersAsFYACheckedCount > 0)
            {
                AuditLogs_DetailsForSave(strNotifyUsersAsFYA, "Notify Users (as FYA)", strSubject, objBcmGrpNotfn.NotificationID);
            }
            if (iNotifyUsersAsFYICheckedCount > 0)
            {
                AuditLogs_DetailsForSave(strNotifyUsersAsFYI, "Notify Users (as FYI)", strSubject, objBcmGrpNotfn.NotificationID);
            }
            if (iNotifyTeamsAsFYACheckedCount > 0)
            {
                AuditLogs_DetailsForSave(strNotifyTeamsAsFYA, "Notify Teams (as FYA)", strSubject, objBcmGrpNotfn.NotificationID);
            }
            if (iNotifyTeamsAsFYICheckedCount > 0)
            {
                AuditLogs_DetailsForSave(strNotifyTeamsAsFYI, "Notify Teams (as FYI)", strSubject, objBcmGrpNotfn.NotificationID);
            }

            return bSuccess = true;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return bSuccess;
        }
    }

    protected bool SendNotification_ForUsers(BCMGroupNotification objBcmGrpNotfn, List<BCMGroupResources> lstBcmGrpRes, string strFYIA, string strSubject,
        string strMailBody, bool bMgrtGrp, string strChkResponse,string strAppUrl)
    {
        bool bSuccess = false;
        string strAttchment = string.Empty;
        
        string strEmailID = string.Empty;
        string strResId = string.Empty;
        string strMobileNo = string.Empty;
        int i = 0;
        try
        {

            foreach (var objUserFYA in ViewBag.usersFYA)
            {
                SendMailForBCM(objBcmGrpNotfn, Convert.ToInt32(BCPEnum.NotificationAs.FYA), strSubject + " - FYA", bMgrtGrp, strEmailID, strMobileNo, i, objUserFYA,
                    strAttchment, strAppUrl, strChkResponse, strMailBody);
                iNotifyUsersAsFYACheckedCount = 1;
                ResourcesInfo objRes = _ProcessSrv.GetResourceNameByID(objUserFYA);
                strNotifyUsersAsFYA += objRes.ResourceName + ", ";
            }
            foreach (var objUsersFYI in ViewBag.usersFYI)
            {
                SendMailForBCM(objBcmGrpNotfn, Convert.ToInt32(BCPEnum.NotificationAs.FYI), strSubject + " - FYI", bMgrtGrp, strEmailID, strMobileNo, i, objUsersFYI,
                    strAttchment, strAppUrl, strChkResponse, strMailBody);
                iNotifyUsersAsFYICheckedCount = 1;
                ResourcesInfo objRes = _ProcessSrv.GetResourceNameByID(objUsersFYI);
                strNotifyUsersAsFYI += objRes.ResourceName + ", ";
            }
            if (lstBcmGrpRes.Count > 0)
            {
                foreach (BCMGroupResources objBCMGroupResources in lstBcmGrpRes)
                {
                    if (objBCMGroupResources.ResourceId != 0)
                    {
                        SendMailForBCM(objBcmGrpNotfn, Convert.ToInt32(BCPEnum.NotificationAs.FYA), strSubject + " - FYA", bMgrtGrp, strEmailID, strMobileNo, i,
                            objBCMGroupResources.ResourceId, strAttchment, strAppUrl, strChkResponse, strMailBody);
                    }
                }
            }
            return bSuccess = true;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return bSuccess;
        }
    }

    private bool SendMailForBCM(BCMGroupNotification objBcmGrpNotfn, int iFYIA, string strSubject, bool bMgtGrp, string strEmailID, string strMobileNo, int i, int iRes,
        string strAttchment, string strAppUrl, string strChkResponse, string strMailBody)
    {
        bool bSuccess = false;
        string strUserName = string.Empty;
        string strFileName = strAttchment;
        int iApproverID = 0;
        try
        {
            if (iRes > 0)
            {
                ResourcesInfo objResourcesInfo = _ProcessSrv.GetResourcesByResourceID(iRes);
                if (objResourcesInfo != null)
                {
                    strUserName = objResourcesInfo.ResourceName;
                    strEmailID = objResourcesInfo.CompanyEmail;
                    objBcmGrpNotfn.ID = 0;
                    objBcmGrpNotfn.SentSuccess = "-1";
                    objBcmGrpNotfn.resourceID = objResourcesInfo.ResourceId;
                    objBcmGrpNotfn.CommunicationMode = Convert.ToInt32(BCPEnum.NotificationType.EMail);
                    iApproverID = objResourcesInfo.ResourceId;
                    objBcmGrpNotfn.NotificationAs = iFYIA;
                    string strDetails = "";
                    int iRecordID = _ProcessSrv.BCMGroupNotifnResourcesSave(objBcmGrpNotfn, bMgtGrp);

                    string strLinkPage = $"{strAppUrl}/BCMTeams/BCMTeamResponse/BCMTeamResponse";
                    string strLink1 = $"{strLinkPage}?iNotificationID={iRecordID}&iUserID={iRes}&strRESP=Y";
                    string strLink2 = $"{strLinkPage}?iNotificationID={iRecordID}&iUserID={iRes}&strRESP=N";

                    if (strChkResponse == "1")
                    {
                        if (iFYIA == Convert.ToInt32(BCPEnum.NotificationAs.FYI))
                        {
                            strDetails = _Incident.GetEmailBodyforNotification(strUserName, BCPEnum.NotificationAs.FYI + ":" + strMailBody, null, null, null, null);
                        }
                        else
                        {
                            strDetails = _Incident.GetEmailBodyforNotification(strUserName, BCPEnum.NotificationAs.FYA + ":" + strMailBody, strLink1, " for YES", strLink2, " for NO");
                        }
                    }
                    else
                    {
                        if (iFYIA == Convert.ToInt32(BCPEnum.NotificationAs.FYI))
                        {
                            strDetails = _Incident.GetEmailBodyforNotification(strUserName, BCPEnum.NotificationAs.FYI + ":" + strMailBody, null, null, null, null);
                        }
                        else
                        {
                            strDetails = _Incident.GetEmailBodyforNotification(strUserName, BCPEnum.NotificationAs.FYA + ":" + strMailBody, null, null, null, null);
                        }
                    }

                    objBcmGrpNotfn.ID = iRecordID;
                    int iUserID = _UserDetails.UserID;
                    
                    objBcmGrpNotfn.SentSuccess = (_BCMMail.SendMail(strSubject, strDetails, strEmailID, string.Empty, string.Empty, strFileName,
                        _UserDetails.OrgID.ToString(), "0", "", iUserID.ToString(), iApproverID.ToString())) ? "1" : "0";

                    _ProcessSrv.BCMGroupNotifnResourcesSave(objBcmGrpNotfn, bMgtGrp);

                    strMobileNo = objResourcesInfo.MobilePhone;
                    objBcmGrpNotfn.CommunicationMode = Convert.ToInt16(BCPEnum.NotificationType.SMS);
                    objBcmGrpNotfn.ID = 0;
                    iRecordID = _ProcessSrv.BCMGroupNotifnResourcesSave(objBcmGrpNotfn, bMgtGrp);

                    if (strChkResponse == "1")
                    {
                        if (iFYIA == Convert.ToInt16(BCPEnum.NotificationAs.FYI))
                        {
                            strDetails = BCPEnum.NotificationAs.FYI + ":" + strMailBody + " .";
                        }
                        else
                        {
                            strDetails = BCPEnum.NotificationAs.FYA + ":" + strMailBody + " . Please reply <RESP Y" + iRecordID + "> for YES and <RESP N" + iRecordID + "> for NO to @@@@@";
                        }
                    }
                    else
                    {
                        strDetails = BCPEnum.NotificationAs.FYI + ":" + strMailBody + " .";
                    }
                    objBcmGrpNotfn.ID = iRecordID;
                    objBcmGrpNotfn.SentSuccess = (_BCPSms.SMSSend(strMobileNo, strUserName, strDetails, _UserDetails.OrgID)) ? "1" : "0";

                    _ProcessSrv.BCMGroupNotifnResourcesSave(objBcmGrpNotfn, bMgtGrp);
                }
            }
            return bSuccess = true;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return bSuccess;
        }
    }

    protected bool SendNotification(BCMGroupNotification objBcmGrpNotfn, List<BCMGroupResources> lstBCMGroupResources, int iFYIA, 
        string strSubject, string strMailBody, bool bMgrtGrp, string strChkResponse, string strAppUrl)
    {
        bool bSuccess = false;
        try
        {
            string strEmailID = string.Empty;
            string resId = string.Empty;
            string strMobileNo = string.Empty;
            int i = 0;
            string strAttchment = string.Empty;
            //string strAppUrl = string.Empty;

            foreach (BCMGroupResources objBCMGroupResources in lstBCMGroupResources)
            {
                if (objBCMGroupResources.ResourceId > 0)
                {
                    SendMailForBCM(objBcmGrpNotfn, iFYIA, strSubject, bMgrtGrp, strEmailID, strMobileNo, i, objBCMGroupResources.ResourceId, strAttchment, strAppUrl,
                        strChkResponse, strMailBody);
                }
            }
            return bSuccess = true;
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return bSuccess;
        }
    }

    public void AuditLogs_DetailsForSave(string strNewValue, string strDescription, string strSubject, int iNotificationID)
    {
        try
        {
            strNewValue = strNewValue.TrimEnd(' ', ',');

            List<UserActivitiesInfo> lstUserActs = new List<UserActivitiesInfo>();
            lstUserActs = _ProcessSrv.UserActivities_GetBySessionActive(Convert.ToInt32(_UserDetails.UserID), 1);

            int iIdValue = 0;
            string strIPAddress = string.Empty;

            if (lstUserActs.Count > 0)
            {
                foreach (UserActivitiesInfo objUserAct in lstUserActs)
                {
                    iIdValue = objUserAct.ID;
                    strIPAddress = objUserAct.IPAddress;
                }
            }

            Auditlog_Process_Details objAuditlog = new Auditlog_Process_Details();

            objAuditlog.ModuleID = 67;
            objAuditlog.RecordID = iNotificationID;
            objAuditlog.ActionID = "I";
            objAuditlog.CreatedBy = _UserDetails.UserID;
            objAuditlog.RecordName = strSubject;
            objAuditlog.IPAddress = strIPAddress;
            objAuditlog.IDValue = iIdValue;

            int iAuditlog = _ProcessSrv.SaveAuditlog_Process(objAuditlog);

            Auditlog_Process_Details objAuditlogDetails = new Auditlog_Process_Details();

            objAuditlogDetails.DetailsID = iAuditlog;
            objAuditlogDetails.ChangeDescription = strDescription;
            objAuditlogDetails.PreviousValue = "";
            objAuditlogDetails.NewValue = strNewValue;

            int iAuditlogDetails = _ProcessSrv.SaveAuditlog_Process_Details(objAuditlogDetails);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }

    public int AttachmentSave(IFormFile objFile, int iGroupMapID,int iNotificationID)
    {
        string strAttachmentStatus = string.Empty;
        int iResult = 0;
        bool iSuccess = false;
        Attachments objAttachments = new Attachments();
        try
        {
            string fileExtension = Path.GetExtension(objFile.FileName);
            string strExtention = fileExtension.TrimStart('.');
            var iFileLengthInKB = objFile.Length;

            if (_Utilities.CheckFileTypeandSize(strExtention, Convert.ToInt32(iFileLengthInKB), _UserDetails.OrgID))
            {
                byte[] GuidSize = new byte[16];
                Random rd = new Random();
                rd.NextBytes(GuidSize);
                System.Guid guid = new Guid(GuidSize);

                if (iGroupMapID > 0)
                {
                    objAttachments.EntityId= iGroupMapID;
                }
                if (iNotificationID> 0)
                {
                    objAttachments.RecordId= iNotificationID;
                }

                objAttachments.AttchmentName = objFile.FileName;
                objAttachments.Description = "";
                objAttachments.FileSize = Convert.ToInt32(iFileLengthInKB);
                objAttachments.MimeType = strExtention;
                objAttachments.GUID = guid.ToString();
                objAttachments.CreatedBy = _UserDetails.UserID;

                byte[] blob = _Utilities.ConvertToBlob(objFile);

                if (blob != null)
                {
                    objAttachments.AttachmentObj = (object)blob;

                    iSuccess = _ProcessSrv.CVaultAttachmentSave(objAttachments);

                    if (iSuccess)
                    {
                        var uploadsFolder = Path.Combine("Attachments", "BCMGroupsNotificationAttachments");
                        if (!Directory.Exists(uploadsFolder))
                        {
                            Directory.CreateDirectory(uploadsFolder);
                        }

                        var filePath = Path.Combine(uploadsFolder, objFile.FileName);

                        using (var stream = new FileStream(filePath, FileMode.Create))
                        {
                            objFile.CopyToAsync(stream).Wait();
                        }
                        iResult = 1; // File conversion save successfully in db and solution
                    }
                }
                else
                {
                    iResult = 4; // Error occurred during file conversion
                }
            }
            else
            {
                iResult = 2; // Invalid file type or size                
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            iResult = 3; // Error occurred during file upload
        }
        if (iResult == 2)
        {
            strAttachmentStatus = "Invalid file type or size";
        }
        else if (iResult == 3)
        {
            strAttachmentStatus = "Error occurred during file upload";
        }
        else if (iResult == 4)
        {
            strAttachmentStatus = "Error occurred during file conversion";
        }
        ViewBag.AttachmentStatus = strAttachmentStatus;
        return iResult;
    }

    #endregion
}