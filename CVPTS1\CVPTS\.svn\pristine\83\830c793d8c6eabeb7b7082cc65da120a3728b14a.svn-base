﻿// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

// Create chart instance - ensure the element exists first
var grrsChart;

// Make sure the chart is created only if the element exists
if (document.getElementById("GRRS_Chart")) {
    grrsChart = am4core.create("GRRS_Chart", am4charts.PieChart);
    grrsChart.className = "pie-chart-improved";
    if (grrsChart.logo) {
        grrsChart.logo.disabled = true;
    }
} else {
    console.error("Element GRRS_Chart not found in the DOM");
}

// Make chart responsive - only if chart was created
if (grrsChart) {
    grrsChart.responsive.enabled = true;

    // Single responsive rule with breakpoints
    grrsChart.responsive.rules.push({
        relevant: function () {
            // Apply to all screen sizes
            return true;
        },
        state: function (target, stateId) {
            if (target instanceof am4charts.PieSeries) {
                var state = target.states.create(stateId);
                // Adjust inner radius based on screen size
                if (target.chart && target.chart.pixelWidth && target.chart.pixelWidth <= 400) {
                    state.properties.innerRadius = am4core.percent(40);
                }
                return state;
            }
            if (target instanceof am4charts.Legend) {
                var state = target.states.create(stateId);
                // Position legend based on screen size
                if (target.chart && target.chart.pixelWidth) {
                    if (target.chart.pixelWidth <= 600) {
                        state.properties.position = "bottom";
                        state.properties.width = undefined;
                        if (target.chart.pixelWidth <= 400) {
                            //state.properties.maxHeight = 80;
                        }
                    } else {
                        state.properties.position = "bottom";
                        //state.properties.width = 120;
                    }
                } else {
                    // Default position if pixelWidth is not available
                    state.properties.position = "bottom";
                    //state.properties.width = 120;
                }
                return state;
            }
            if (target instanceof am4charts.Chart) {
                var state = target.states.create(stateId);
                // Adjust padding based on screen size
                if (target.pixelWidth && target.pixelWidth <= 400) {
                    state.properties.paddingTop = 0;
                    state.properties.paddingRight = 0;
                    state.properties.paddingBottom = 0;
                    state.properties.paddingLeft = 0;
                } else {
                    state.properties.paddingTop = 10;
                    state.properties.paddingRight = 15;
                    state.properties.paddingBottom = 10;
                    state.properties.paddingLeft = 15;
                }
                return state;
            }
            return null;
        }
    });
}

// Continue only if chart was created
if (grrsChart) {
    // Add data
    grrsChart.data = [{
        "category": "Initiated",
        "value": 20,
        "percentage": 20,
        "color": am4core.color("#6BBBF7") // Light blue
    }, {
        "category": "Waiting",
        "value": 30,
        "percentage": 30,
        "color": am4core.color("#7F7FFF") // Purple
    }, {
        "category": "Approved",
        "value": 50,
        "percentage": 50,
        "color": am4core.color("#4A7CF8") // Blue
    }];

    // Add and configure Series
    var pieSeries = grrsChart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "value";
    pieSeries.dataFields.category = "category";
    pieSeries.slices.template.propertyFields.fill = "color";
    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 1; // Default stroke width
    pieSeries.slices.template.strokeOpacity = 1;

    // Default hover effect
    pieSeries.slices.template.states.getKey("hover").properties.scale = 1.05;

    // Make slices bigger
    pieSeries.slices.template.tooltipText = "{category}: {value}";
    pieSeries.slices.template.cornerRadius = 5;

    // Let's cut a hole in our Pie chart the size of 30% the radius
    grrsChart.innerRadius = am4core.percent(70);

    // Set chart padding to ensure labels fit - matched with OverallKPI_Chart
    grrsChart.paddingTop = 10;
    grrsChart.paddingBottom = 10;
    grrsChart.paddingLeft = 15;
    grrsChart.paddingRight = 15;
    grrsChart.radius = am4core.percent(90); // Increased radius for better visibility

    // Reduce background area
    grrsChart.background.fill = am4core.color("#ffffff");
    grrsChart.background.fillOpacity = 0;

    // Add a legend
    grrsChart.legend = new am4charts.Legend();
    // Position will be set by responsive rules
    grrsChart.legend.fontSize = 12;
    grrsChart.legend.marginTop = 0;
    grrsChart.legend.marginLeft = 15; // Added left margin
    grrsChart.legend.markers.template.width = 12;
    grrsChart.legend.markers.template.height = 12;
    grrsChart.legend.contentAlign = "left"; // Align content to the left

    // Disable labels on pie slices
    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;
    pieSeries.alignLabels = false;

    // Add actual count to legend
    pieSeries.legendSettings.itemValueText = "{value}";
    pieSeries.legendSettings.valueText = "{value}";

    // Make legend more visible
    grrsChart.legend.valueLabels.template.fontSize = 12;
    grrsChart.legend.valueLabels.template.fontWeight = "bold";
    grrsChart.legend.labels.template.fontSize = 12;
    grrsChart.legend.useDefaultMarker = true;

    // Legend is now handled by the responsive rules above
}
