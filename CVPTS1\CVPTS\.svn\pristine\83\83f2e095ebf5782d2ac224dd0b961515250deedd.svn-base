﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<BCM.BusinessClasses.RTOMTRConfigurations>
@{
    ViewData["Title"] = "RTO-MTR Configuration";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .accidental_icon_bg {
        background: #fff;
    }

    .circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        max-width: 100%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
<div class="Page-Header d-flex align-items-center justify-content-between">
    <h6 class="Page-Title">RTO MAO </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        <a asp-controller="Unit" asp-action="AddUnit" class="btn icon-btn btn-primary btn-sm Create" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cv-Plus" title="Create New"></i>Create</a>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#AddModal"> <i class="cv-Plus" title="Create New"></i>Create</button> *@
    </div>
</div>
<div class="Page-Condant  border-0 pe-2" style="height: calc(100vh - 115px);overflow: auto;">
    <div class="card-body">
        <div class="row g-3">
            @if(Model != null)
            {
                foreach (var item in Model)
                {
                    <div class="col-3 Incident-bg">
                        <div class="card shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <div class="d-flex gap-2 align-items-center">
                                            <div class="circle accidental_icon_bg shadow-sm">
                                          <img src="~/img/rss_feed_master/company_logo/intel.png" class="img-fluid" width="30" />
                                            </div>
                                        </div>
                                        <div>
                                            <ul class="ps-0 mb-0">
                                                <li class="list-group-item text-muted"><small>Organization</small></li>
                                                <li class="list-group-item fw-bold text-primary">
                                                    @item.OrganizationName
                                                    
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-1">
                                        <span class="me-1 btnEdit" role="button" data-bs-toggle="modal" data-bs-target="#CreateModal" data-id="@item.ID" data-org-id="@item.OrgID" data-configured-rto="@item.ConfiguredRTO" data-mtr-rating="@item.MTRRating"><i class="cv-edit align-middle"></i></span>
                                        @* <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span> *@

                                    </div>
                                </div>
                                <div class="mt-2">
                                    <table class="table table-sm table-borderless mb-0 align-middle">
                                        <tbody>
                                            <tr>
                                                <td class="text-muted">RTO(Hrs)</td>
                                                <td>:</td>
                                                <td>@item.ConfiguredRTO</td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted">MAO Score</td>
                                                <td>:</td>
                                                <td>
                                                    @item.MTRRating
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
           @*  <div class="col-3">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div>
                                    <ul class="ps-0 mb-0">
                                        <li class="list-group-item text-muted"><small>Organization</small></li>
                                        <li class="list-group-item fw-bold text-primary">
                                            Dell
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tbody>
                                    <tr>
                                        <td class="text-muted">RTO(Hrs)</td>
                                        <td>:</td>
                                        <td>10</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">MAO Score</td>
                                        <td>:</td>
                                        <td>
                                            Minor
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div>
                                    <ul class="ps-0 mb-0">
                                        <li class="list-group-item text-muted"><small>Organization</small></li>
                                        <li class="list-group-item fw-bold text-primary">
                                            TCS
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tbody>
                                    <tr>
                                        <td class="text-muted">RTO(Hrs)</td>
                                        <td>:</td>
                                        <td>10</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">MAO Score</td>
                                        <td>:</td>
                                        <td>
                                            Minor
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex gap-2 align-items-center">
                                <div>
                                    <ul class="ps-0 mb-0">
                                        <li class="list-group-item text-muted"><small>Organization</small></li>
                                        <li class="list-group-item fw-bold text-primary">
                                            Perpetuuiti
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-1">
                                <span class="me-1" role="button"><i class="cv-edit align-middle"></i></span>
                                <span role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete align-middle text-danger"></i></span>

                            </div>
                        </div>
                        <div class="mt-2">
                            <table class="table table-sm table-borderless mb-0 align-middle">
                                <tbody>
                                    <tr>
                                        <td class="text-muted">RTO(Hrs)</td>
                                        <td>:</td>
                                        <td>10</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">MAO Score</td>
                                        <td>:</td>
                                        <td>
                                            Minor
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div> *@
        </div>

    </div>
   
</div>
    <!-- Configuration Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title">RTO MAO Configuration</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>          
            <form id="rtoMtrConfigForm" class="needs-validation progressive-validation" novalidate>
                <div class="modal-body">
                    <div class="row row-cols-1">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">Organization</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-organization"></i></span>
                                    <select class="form-select selectize" id="ddlOrg" required>
                                        <option selected value="0">--Select--</option>
                                        @foreach (var objOrgGrp in ViewBag.Organization)
                                        {
                                            <option value="@objOrgGrp.Value">@objOrgGrp.Text</option>
                                        }
                                    </select>
                                    <input class="form-control" name="ID" id="id" type="hidden" />
                                </div>
                                <div class="invalid-feedback">Please select an Organization</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">MAO Score</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-organization"></i></span>
                                    <select class="form-select selectize" id="ddlMao" required>
                                        <option selected value="0">--Select--</option>
                                        @foreach (var objOrgGrp in ViewBag.ImpactRatings)
                                        {
                                            <option value="@objOrgGrp.Value">@objOrgGrp.Text</option>
                                        }
                                    </select>
                                </div>
                                <div class="invalid-feedback">Please select a MAO Score</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">RTO(Hrs)</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-description"></i></span>
                                    <input class="form-control" name="MTRRating" id="RTOhrs" type="number" min="0.1" step="0.1" placeholder="RTO(Hrs)" required />
                                </div>
                                <div class="invalid-feedback">Please enter a valid RTO (Hrs)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
                    <div>
                        <button type="button" class="btn btn-secondary btn-sm me-1 btnEdit1" data-bs-dismiss="modal">Close</button>
                        <button type="submit" id="btnSaveRTOMTR" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
    <!--End Configuration Modal -->
    <!-- Delete Modal -->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header d-grid text-center">
                    <span class="fw-semibold">Do you really want to delete</span>
                    <span>"<span class="text-primary fw-semibold">CARP-2022-14</span>" ?</span>
                </div>
                <div class="modal-body text-center">
                    <img src="~/img/isomatric/delete.svg" width="260" />
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel this time</button>
                    <button type="button" class="btn btn-primary btn-sm">Yes delete the file</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End Delete Modal -->
@section Scripts {
    <script>

        $(document).ready(function(){

            // Global variable to store custom messages
            let customMessages = {};
            let validateDropdownWithCustomMessage;

            // Force a check to see if global validation is loaded
            if (typeof window.BCMValidation === 'undefined') {
                console.error("BCMValidation is not defined! Check if global-validation.js is loaded properly.");
                // Try to load it dynamically as a fallback
                $.getScript('/js/global-validation.js')
                    .done(function() {
                        console.log("Successfully loaded global-validation.js dynamically");
                        initializeValidation();
                    })
                    .fail(function() {
                        console.error("Failed to load global-validation.js dynamically");
                    });
            } else {
                console.log("BCMValidation is already defined");
                initializeValidation();
            }

            // Function to initialize validation
            function initializeValidation() {
                console.log("Initializing validation for rtoMtrConfigForm form");

                if (window.BCMValidation) {
                    console.log("BCMValidation found, initializing");

                    // Get the form element
                    const form = document.getElementById('rtoMtrConfigForm');
                    if (!form) {
                        console.error("Form not found with ID: rtoMtrConfigForm");
                        return;
                    }

                    // Store the original content of all invalid-feedback divs
                    form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                        // Find the associated input
                        const formGroup = element.closest('.form-group');
                        const input = formGroup?.querySelector('input, select, textarea');
                        if (input) {
                            // Store the custom message using the input's ID or name as the key
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key) {
                                customMessages[key] = element.textContent.trim();
                                console.log("Stored custom message for", key, ":", customMessages[key]);
                            }
                        }
                    });

                    // Override the validateInput function to preserve custom messages
                    const originalValidateInput = window.BCMValidation.validateInput;
                    window.BCMValidation.validateInput = function(input, forceValidation = false) {
                        // Get the result from the original function
                        const result = originalValidateInput(input, forceValidation);

                        // If the input is invalid, restore the custom message
                        if (!result) {
                            const key = input.id || input.name || input.getAttribute('asp-for');
                            if (key && customMessages[key]) {
                                const formGroup = input.closest('.form-group');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                                if (feedbackElement) {
                                    // Restore the custom message
                                    feedbackElement.textContent = customMessages[key];
                                    feedbackElement.style.display = 'block';
                                    console.log("Restored custom message for", key, ":", customMessages[key]);
                                }
                            }
                        }

                        return result;
                    };

                    // Custom validation function for dropdowns that preserves messages
                    validateDropdownWithCustomMessage = function(dropdown) {
                        const key = dropdown.id || dropdown.name || dropdown.getAttribute('asp-for');
                        const formGroup = dropdown.closest('.form-group');
                        const feedbackElement = formGroup?.querySelector('.invalid-feedback');

                        const isValid = dropdown.value !== "" && dropdown.value !== "0";

                        if (!isValid) {
                            dropdown.classList.add('is-invalid');
                            dropdown.classList.remove('is-valid');
                            if (feedbackElement) {
                                if (key && customMessages[key]) {
                                    feedbackElement.textContent = customMessages[key];
                                } else {
                                    feedbackElement.textContent = "Please select an option";
                                }
                                feedbackElement.style.display = 'block';
                            }
                        } else {
                            dropdown.classList.remove('is-invalid');
                            dropdown.classList.add('is-valid');
                            if (feedbackElement) {
                                feedbackElement.style.display = 'none';
                            }
                        }

                        return isValid;
                    }

                    // Override the validateForm function with custom validation logic
                    const originalValidateForm = window.BCMValidation.validateForm;
                    window.BCMValidation.validateForm = function(form) {
                        let formIsValid = true;

                        // Validate dropdowns with custom messages
                        const orgDropdown = form.querySelector('#ddlOrg');
                        const maoDropdown = form.querySelector('#ddlMao');

                        if (orgDropdown) {
                            if (!validateDropdownWithCustomMessage(orgDropdown)) {
                                formIsValid = false;
                            }
                        }

                        if (maoDropdown) {
                            if (!validateDropdownWithCustomMessage(maoDropdown)) {
                                formIsValid = false;
                            }
                        }

                        // Validate other required inputs
                        const rtoInput = form.querySelector('#RTOhrs');
                        if (rtoInput) {
                            const key = rtoInput.id || rtoInput.name || rtoInput.getAttribute('asp-for');
                            const formGroup = rtoInput.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');

                            const value = rtoInput.value.trim();
                            const isValid = value !== "" && !isNaN(value) && parseFloat(value) > 0;

                            if (!isValid) {
                                rtoInput.classList.add('is-invalid');
                                rtoInput.classList.remove('is-valid');
                                if (feedbackElement) {
                                    if (key && customMessages[key]) {
                                        feedbackElement.textContent = customMessages[key];
                                    } else {
                                        feedbackElement.textContent = "Please enter a valid RTO (Hrs)";
                                    }
                                    feedbackElement.style.display = 'block';
                                }
                                formIsValid = false;
                            } else {
                                rtoInput.classList.remove('is-invalid');
                                rtoInput.classList.add('is-valid');
                                if (feedbackElement) {
                                    feedbackElement.style.display = 'none';
                                }
                            }
                        }

                        return formIsValid;
                    };

                    // Initialize the validation framework
                    window.BCMValidation.init();

                    // Add required field indicators (asterisks)
                    window.BCMValidation.addRequiredFieldIndicators(form);

                    // Add format indicators for pattern-based inputs
                    window.BCMValidation.addFormatIndicators(form);

                    // Add user interaction validation for all required fields
                    form.querySelectorAll('input[required], select[required], textarea[required]').forEach(function(input) {
                        input.addEventListener('input', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            // Use custom validation for dropdowns
                            if (this.tagName === 'SELECT') {
                                validateDropdownWithCustomMessage(this);
                            } else if (this.id === 'RTOhrs') {
                                // Custom validation for RTO input
                                const key = this.id || this.name || this.getAttribute('asp-for');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');

                                const value = this.value.trim();
                                const isValid = value !== "" && !isNaN(value) && parseFloat(value) > 0;

                                if (!isValid) {
                                    this.classList.add('is-invalid');
                                    this.classList.remove('is-valid');
                                    if (feedbackElement) {
                                        if (key && customMessages[key]) {
                                            feedbackElement.textContent = customMessages[key];
                                        } else {
                                            feedbackElement.textContent = "Please enter a valid RTO (Hrs)";
                                        }
                                        feedbackElement.style.display = 'block';
                                    }
                                } else {
                                    this.classList.remove('is-invalid');
                                    this.classList.add('is-valid');
                                    if (feedbackElement) {
                                        feedbackElement.style.display = 'none';
                                    }
                                }
                            } else if (this.type === 'email') {
                                window.BCMValidation.validateEmail(this);
                            } else if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }
                        });

                        input.addEventListener('blur', function() {
                            const formGroup = this.closest('.form-group');
                            if (formGroup) {
                                formGroup.classList.remove(window.BCMValidation.classes.validationPendingClass);
                            }

                            // Use custom validation for dropdowns
                            if (this.tagName === 'SELECT') {
                                validateDropdownWithCustomMessage(this);
                            } else if (this.id === 'RTOhrs') {
                                // Custom validation for RTO input
                                const key = this.id || this.name || this.getAttribute('asp-for');
                                const feedbackElement = formGroup?.querySelector('.invalid-feedback');

                                const value = this.value.trim();
                                const isValid = value !== "" && !isNaN(value) && parseFloat(value) > 0;

                                if (!isValid) {
                                    this.classList.add('is-invalid');
                                    this.classList.remove('is-valid');
                                    if (feedbackElement) {
                                        if (key && customMessages[key]) {
                                            feedbackElement.textContent = customMessages[key];
                                        } else {
                                            feedbackElement.textContent = "Please enter a valid RTO (Hrs)";
                                        }
                                        feedbackElement.style.display = 'block';
                                    }
                                } else {
                                    this.classList.remove('is-invalid');
                                    this.classList.add('is-valid');
                                    if (feedbackElement) {
                                        feedbackElement.style.display = 'none';
                                    }
                                }
                            } else if (this.type === 'email') {
                                window.BCMValidation.validateEmail(this);
                            } else if (this.hasAttribute('pattern')) {
                                window.BCMValidation.validatePatternInput(this);
                            } else {
                                window.BCMValidation.validateInput(this);
                            }
                        });

                        // Add change event specifically for dropdowns
                        if (input.tagName === 'SELECT') {
                            input.addEventListener('change', function() {
                                validateDropdownWithCustomMessage(this);
                            });
                        }
                    });

                    // Add a manual validation trigger on form submission
                    form.addEventListener('submit', function(event) {
                        console.log("Form submission triggered");

                        // Force validation of all fields to show messages
                        validateDropdownWithCustomMessage(form.querySelector('#ddlOrg'));
                        validateDropdownWithCustomMessage(form.querySelector('#ddlMao'));

                        // Validate RTO input
                        const rtoInput = form.querySelector('#RTOhrs');
                        if (rtoInput) {
                            const formGroup = rtoInput.closest('.form-group');
                            const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                            const key = rtoInput.id || rtoInput.name || rtoInput.getAttribute('asp-for');

                            const value = rtoInput.value.trim();
                            const isValid = value !== "" && !isNaN(value) && parseFloat(value) > 0;

                            if (!isValid) {
                                rtoInput.classList.add('is-invalid');
                                rtoInput.classList.remove('is-valid');
                                if (feedbackElement) {
                                    if (key && customMessages[key]) {
                                        feedbackElement.textContent = customMessages[key];
                                    } else {
                                        feedbackElement.textContent = "Please enter a valid RTO (Hrs)";
                                    }
                                    feedbackElement.style.display = 'block';
                                }
                            }
                        }

                        // Validate the form
                        const isValid = window.BCMValidation.validateForm(form);
                        console.log("Form validation result:", isValid);

                        if (!isValid) {
                            console.log("Preventing form submission due to validation errors");
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus the first invalid field
                            const firstInvalidField = form.querySelector('.' + window.BCMValidation.classes.invalidClass);
                            if (firstInvalidField) {
                                firstInvalidField.focus();
                            }
                            return false;
                        }

                        // If validation passes, handle the AJAX submission
                        event.preventDefault();
                        handleFormSubmission();
                    });
                } else {
                    console.error("BCMValidation not found! Make sure global-validation.js is loaded.");
                }
            }

            $(document).on('click', '.btnEdit', function () {
                var ddlVal = $(this).data('mtr-rating');
                var ddlOrgVal = $(this).data('org-id');
                $('#id').val($(this).data('id'));
                $('#RTOhrs').val($(this).data('configured-rto'));
                
                // For selectize dropdowns, we need to use the selectize API
                if ($('#ddlMao')[0].selectize) {
                    $('#ddlMao')[0].selectize.setValue(ddlVal);
                } else {
                    $('#ddlMao').val(ddlVal).change();
                }
                
                if ($('#ddlOrg')[0].selectize) {
                    $('#ddlOrg')[0].selectize.setValue(ddlOrgVal);
                } else {
                    $('#ddlOrg').val(ddlOrgVal).change();
                }

                // Clear any validation states when editing
                const form = document.getElementById('rtoMtrConfigForm');
                if (form && window.BCMValidation) {
                    window.BCMValidation.clearAllValidationMessages(form);
                }
            });

            $(document).on('click', '.Create', function () {
                $('#ddlMao').val('');
                $("#ddlOrg").prop("disabled", false);
                $('#ddlOrg').val('');
                $('#RTOhrs').val('');
                $('#id').val('');

                // Clear any validation states when creating new
                const form = document.getElementById('rtoMtrConfigForm');
                if (form && window.BCMValidation) {
                    window.BCMValidation.clearAllValidationMessages(form);
                }
            });

            // Handle form submission with validation
            function handleFormSubmission() {
                var isIndexID = $("#id").val();

                if (isIndexID == "") {
                    isIndexID = 0;
                } else {
                    isIndexID = $("#id").val();
                }

                var data = {
                    ConfiguredRTO: $("#RTOhrs").val(),
                    MTRRating: $("#ddlMao").val(),
                    OrgID: $("#ddlOrg").val(),
                    ID: isIndexID
                }

                $.ajax({
                    type: 'POST',
                    url: '@Url.Action("RTOMTRSaveSettings", "RTOMTRConfiguration")',
                    data: JSON.stringify(data),
                    contentType: 'application/json',
                    success: function (response) {
                        //console.log("RTO MTR Configuration saved successfully");
                        // Close modal and refresh page or update UI as needed
                        
                        // Optionally reload the page to show updated data
                        // location.reload();

                        if (response.success)
                        {                                                        
                            $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                            const toastElement = $('#liveToast');
                            toastElement.removeClass('bg-success bg-warning bg-danger');
                            toastElement.addClass('bg-success');
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();
                            // $('#CreateModal').modal('hide');
                            setTimeout(function () {
                                location.reload();
                            }, 3000);
                        }
                        else
                        {
                            $('#liveToast .toast-body .d-flex span:last-child').text(response.message);
                            const toastElement = $('#liveToast');
                            toastElement.removeClass('bg-success bg-warning bg-danger');
                            toastElement.addClass('bg-danger');
                            const toastLiveExample = document.getElementById('liveToast');
                            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample);
                            toastBootstrap.show();
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log("Error saving RTO MTR Configuration:", error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                        alert("An error occurred while saving the configuration. Please try again.");
                    }
                });
            }

            // Handle save button click - trigger form submission
            $('#btnSaveRTOMTR').on('click', function (e) {
                e.preventDefault();
                const form = document.getElementById('rtoMtrConfigForm');
                if (form) {
                    console.log("Save button clicked, triggering validation");

                    // Force show validation messages for all fields
                    const orgDropdown = form.querySelector('#ddlOrg');
                    const maoDropdown = form.querySelector('#ddlMao');
                    const rtoInput = form.querySelector('#RTOhrs');

                    if (orgDropdown) validateDropdownWithCustomMessage(orgDropdown);
                    if (maoDropdown) validateDropdownWithCustomMessage(maoDropdown);

                    if (rtoInput) {
                        const formGroup = rtoInput.closest('.form-group');
                        const feedbackElement = formGroup?.querySelector('.invalid-feedback');
                        const key = rtoInput.id || rtoInput.name || rtoInput.getAttribute('asp-for');

                        const value = rtoInput.value.trim();
                        const isValid = value !== "" && !isNaN(value) && parseFloat(value) > 0;

                        if (!isValid) {
                            rtoInput.classList.add('is-invalid');
                            rtoInput.classList.remove('is-valid');
                            if (feedbackElement) {
                                feedbackElement.textContent = customMessages[key] || "Please enter a valid RTO (Hrs)";
                                feedbackElement.style.display = 'block';
                            }
                        }
                    }

                    // Trigger form submission which will handle validation
                    const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                    form.dispatchEvent(submitEvent);
                }
            });
        })
    </script>
}
