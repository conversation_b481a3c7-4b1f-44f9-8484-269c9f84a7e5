﻿@model BCM.BusinessClasses.ImpactType

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<form asp-action="AddImpactTypeMaster" method="post">
    <div class="row row-cols-1">
        <div class="col">
            <div class="form-group w-100">
                <label class="form-label">Impact Category</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-impact_name"></i></span>
                    <input type="text" class="form-control" placeholder="Enter The Impact Category Name" asp-for="ImpactTypeName" required/>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="form-group w-100">
                <label class="form-label">Impact Type Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cv-impact_name"></i></span>
                    <input type="text" class="form-control" placeholder="Enter The Impact Type Name" asp-for="ImpactTypeDetails" required />
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer d-flex justify-content-between">
        <span class="fst-italic d-flex align-items-center text-secondary"><i class="cv-note me-1"></i><small>Note : All fields are mandatory except optional</small></span>
        <div>
            <button type="button" class="btn btn-secondary btn-sm me-1" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary btn-sm">Save</button>
        </div>
    </div>
</form>
