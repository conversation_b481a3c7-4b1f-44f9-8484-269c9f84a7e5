﻿let queryData = "";
let usedIds = new Set();
let widgettitle = "";
let Xaxis = "";
let Yaxis = "";
let tableArray = "";

async function GetUserList() {
    try {

        const response = await $.ajax({
            url: '/BCMAdministration/DashboardList/GetUserData',
            type: "GET",
            dataType: "json"
        });

        if (response?.success) {
            const $dropdown = $('#userDropdown');

            // Destroy previous instance if already initialized
            if ($dropdown[0].selectize) {
                $dropdown[0].selectize.destroy();
            }

            // Initialize selectize
            $dropdown.selectize({
                valueField: 'userID',
                labelField: 'loginName',
                searchField: 'loginName',
                options: response.data.filter(u => u.loginName && u.userID),
                placeholder: 'Select a user...',
                create: false
            }); 
        } else {
            console.error("Server error:", response.message);

        }

    } catch (error) {
        alert("Something went wrong while fetching dashboard list.");
    }

}

async function loadDashboardList() {
    try {
    
        const response = await $.ajax({
            url: '/BCMAdministration/DashboardList/DashboardBuilderList',
            type: "GET",
            dataType: "json"
        });

        if (response?.success) {
            const tbody = document.getElementById("log-table-body");
     
            if (response.data.length > 0) {
                $("#log-table-body").empty();
                response.data.forEach((item, index) => {
                    let props = {};
                    try {
                        props = JSON.parse(item.properties || "{}");
                    } catch (e) {
                        console.warn("Invalid properties JSON for item:", item, e);
                    }

                    const tr = document.createElement("tr");

                    tr.innerHTML = `
            <td>${index + 1}</td>
            <td>${item.name}</td>         
            <td>${props.WidgetDescription || '-'}</td>
            <td>
              <div class="d-flex align-items-center gap-2">
                <span 
                    class="btn-action btnEdit" type="button"   data-edit='${encodeURIComponent(item.properties)}'>
                  <i class="cv-edit" title="Edit"></i>
                </span>
                <span 
                    class="btn-action btnDelete text-danger" 
                    type="button" 
                    data-bs-toggle="modal" 
                    data-bs-target="#Modal" 
                    data-id="${item.referenceId}">
                  <i class="cv-delete" title="Delete"></i>
                </span>
              </div>
            </td>
        `;
                    tbody.appendChild(tr);
                });
            }      

        } else {
            console.error("Server error:", response.message);
          
        }
 
    } catch (error) {
        alert("Something went wrong while fetching dashboard list.");
    }
    
}
function generateUniqueNumericId() {
    let id;
    do {
        const length = Math.floor(Math.random() * 4) + 4; // Length: 4–7
        const min = Math.pow(10, length - 1);
        const max = Math.pow(10, length) - 1;
        id = Math.floor(Math.random() * (max - min + 1)) + min;
    } while (usedIds.has(id));
    usedIds.add(id);
    return id;
}

async function loadWidgetList() {
    try {

        const response = await $.ajax({
            url: '/BCMAdministration/DashboardList/GetWidgetList',
            type: "GET",
            dataType: "json"
        });

        if (response?.success && Array.isArray(response.data)) {
            const widgetList = response.data;
            const $ul = $(".prebuildreport-leftsidebar ul");
            $ul.empty();           
            widgetList.forEach(widget => {
                const numericId = generateUniqueNumericId();
                const props = JSON.parse(widget.properties || '{}');
                const encodedProps = encodeURIComponent(JSON.stringify(props));

                const listItem = `
                    <li class="report-item dragItem" draggable="true" data-name="${widget.name}" data-props="${encodedProps}"  data-id="${numericId}">
                        <div>
                            <img src="${props.hrefImage || '/img/default-thumbnail.jpg'}" style="max-width: 80%;height: 54px;" alt="${widget.name}">
                         <p class="mb-0 text-truncate" style="max-width: 121px;" title="${widget.name}">${widget.name}</p>
                        </div>
                    </li>
                `;

                $ul.append(listItem);
            })
        }
            else {
            console.error("Server error:", response.message);

        }

    } catch (error) {
        alert("Something went wrong while fetching dashboard list.");
    }

}

async function loadDashboard(properties) {
    const widgetManager = window.WidgetManager;
    widgetManager.initializeGrid();

    properties.forEach(widget => {
        widgetManager.createWidget(
            widget.type,
            widget.title,
            widget.id,
            widget.query,
            {
                x: widget.x,
                y: widget.y,
                w: widget.w,
                h: widget.h
            }
        );
    });
}

$(document).ready(async function () {

    await loadDashboardList();
    $(".btnEdit").on("click", async function () {
            // Get encoded data-edit attribute
            const encodedProps = $(this).attr('data-edit');

        const dragItem = document.querySelectorAll('.dragItem');
        const dropZone = document.getElementById('dashboard-grid');


            // Decode and parse the properties JSON
            const decodedProps = decodeURIComponent(encodedProps);
            const properties = JSON.parse(decodedProps);


            $("#dashboardListModal").addClass('d-none');
            $("#createModalDashboardList").removeClass('d-none');
            await loadWidgetList();

            dragItem.forEach(item => {
                item.addEventListener('dragstart', function (e) {
                    e.dataTransfer.clearData();
                    e.dataTransfer.setData('props', item.getAttribute('data-props'));
                    e.dataTransfer.setData('Id', item.getAttribute('data-id'));
                    e.dataTransfer.setData('title', item.getAttribute('data-name'));
                });
            });

            dropZone.addEventListener('dragover', function (e) {
                e.preventDefault();
            });

            dropZone.addEventListener('drop', async function (e) {
                e.preventDefault();
                const itemProps = JSON.parse(decodeURIComponent(e.dataTransfer.getData('props')));
                Xaxis = itemProps.xaxis;
                Yaxis = itemProps.yaxis;
                widgettitle = e.dataTransfer.getData('title');
                if (itemProps.datasetstoredQuery) {
                    try {
                        const response = await $.ajax({
                            url: '/BCMAdministration/DashboardList/getQueryData',
                            type: "GET",
                            data: { query: itemProps.datasetstoredQuery },
                            dataType: "json"
                        });

                        if (response?.success && typeof response.data === "string") {

                            queryData = JSON.parse(response.data);
                        }
                        else {
                            console.error("Server error:", response.message);
                        }

                    } catch (error) {
                        alert("Something went wrong while fetching dashboard list.");
                    }
                }



                const widgetManager = new WidgetManager();
                widgetManager.initializeGrid();
                const widgetId = `widget-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
                gridOptions = null;
                if (itemProps.ChartType === 'line') {

                    widgetManager.createWidget("amchart-line", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
                } else if (itemProps.ChartType === 'bar') {
                    widgetManager.createWidget("amchart-bar", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
                } else if (itemProps.ChartType === 'pie') {
                    widgetManager.createWidget("amchart-pie", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
                } else {
                    console.warn("Unknown chart type:", itemProps.ChartType);
                }


            });

            loadDashboard(properties);
        
    });

    $("#dashboardListCreate").on("click", async function () {
        $("#dashboardListModal").addClass('d-none');
        $("#createModalDashboardList").removeClass('d-none');
        await loadWidgetList();

        const dragItem = document.querySelectorAll('.dragItem');
        const dropZone = document.getElementById('dashboard-grid');
        dragItem.forEach(item => {
            item.addEventListener('dragstart', function (e) {
                e.dataTransfer.clearData();
                e.dataTransfer.setData('props', item.getAttribute('data-props'));    
                e.dataTransfer.setData('Id', item.getAttribute('data-id'));
                e.dataTransfer.setData('title', item.getAttribute('data-name'));
            });
        });
       dropZone.addEventListener('dragover', function (e) {
                e.preventDefault();
       });

        dropZone.addEventListener('drop',async function (e) {
            e.preventDefault(); 
            const itemProps = JSON.parse(decodeURIComponent(e.dataTransfer.getData('props')));  
            if (itemProps.hasOwnProperty('tableArray')) {
                tableArray = itemProps.tableArray;
            } else {
                Xaxis = itemProps.xaxis;
                Yaxis = itemProps.yaxis;
            }

            widgettitle = e.dataTransfer.getData('title');
            if (itemProps.datasetstoredQuery) {                
                    try {
                        const response = await $.ajax({
                            url: '/BCMAdministration/DashboardList/getQueryData',
                            type: "GET",
                            data: { query: itemProps.datasetstoredQuery }, 
                            dataType: "json"
                        });

                        if (response?.success && typeof response.data === "string") {
                                               
                            queryData = JSON.parse(response.data);
                        }
                        else {
                            console.error("Server error:", response.message);
                        }

                    } catch (error) {
                        alert("Something went wrong while fetching dashboard list.");
                    }              
            }



            const widgetManager = new WidgetManager();
            widgetManager.initializeGrid();
            const widgetId =`widget-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
            gridOptions = null;

            if (itemProps.WidgetType == 'table') {
                widgetManager.createWidget("table", widgettitle, widgetId, itemProps.datasetstoredQuery,"","", tableArray )
            }

            if (itemProps.ChartType === 'line') {

                widgetManager.createWidget("amchart-line", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            } else if (itemProps.ChartType === 'bar') {
                widgetManager.createWidget("amchart-bar", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            } else if (itemProps.ChartType === 'pie') {
                widgetManager.createWidget("amchart-pie", widgettitle, widgetId, itemProps.datasetstoredQuery, Xaxis, Yaxis);
            } else {
                console.warn("Unknown chart type:", itemProps.ChartType);
            }

        
        });    
    });

    $("#backBtn").on("click", async function () {
        $("#createModalDashboardList").addClass('d-none');
        $("#dashboardListModal").removeClass('d-none');      
    });

    $("#saveBtn").on("click", async function () {
        await GetUserList();
        $("#AddModal").modal('show');
    });

    $("#dashboardBuilderSaveBtn").on("click", async function () {

        const properties = [];

        $('#dashboard-grid .grid-stack-item').each(function () {
            const $widget = $(this);

            const widgetType = $widget.data('widget-type');
            const widgetQuery = $widget.data('widgetquery');
            const widgetId = $widget.attr('gs-id');
            const x = parseInt($widget.attr('gs-x'));
            const y = parseInt($widget.attr('gs-y'));
            const width = parseInt($widget.attr('gs-w'));
            const hight = parseInt($widget.attr('gs-h'));
            const title = $widget.find('#headerName').text().trim();

            const baseProps = {
                id: widgetId,
                type: widgetType,
                query: widgetQuery,
                x,
                y,
                width,
                hight,
                title
            };

            if (widgetType === 'table') {
                const encodedArray = $widget.attr('data-widgettablearray');
                const tableArray = encodedArray ? JSON.parse(decodeURIComponent(encodedArray)) : [];

                baseProps.tableArray = tableArray; // attach only for table
            } else {
                baseProps.widgetXaxis = $widget.data('widgetxaxis');
                baseProps.widgetYaxis = $widget.data('widgetyaxis');
            }

            properties.push(baseProps);
        });




        //$('#dashboard-grid .grid-stack-item').each(function () {
        //    const $widget = $(this);

        //    // Extract key properties
        //    const widgetType = $widget.data('widget-type');
        //    const widgetQuery = $widget.data('widgetquery');
        //    const widgetXaxis = $widget.data('widgetxaxis');
        //    const widgetYaxis = $widget.data('widgetyaxis');
        //    const widgetId = $widget.attr('gs-id');
        //    const x = $widget.attr('gs-x');
        //    const y = $widget.attr('gs-y');
        //    const width = $widget.attr('gs-w');
        //    const hight = $widget.attr('gs-h');
        //    const title = $widget.find('#headerName').text().trim();

        //    // Store as an object
        //    properties.push({
        //        id: widgetId,
        //        type: widgetType,
        //        query: widgetQuery,
        //        widgetXaxis: widgetXaxis,
        //        widgetYaxis: widgetYaxis,
        //        x: parseInt(x),
        //        y: parseInt(y),
        //        width: parseInt(width),
        //        hight: parseInt(hight),
        //        title: title
        //    });


        //    if (widgetType === 'table') {
        //        const encodedArray = $widget.attr('data-widgettablearray');
        //        const tableArray = encodedArray ? JSON.parse(decodeURIComponent(encodedArray)) : [];

        //        properties.tableArray = tableArray; // attach only for table
        //    } else {
        //        properties.widgetXaxis = $widget.data('widgetxaxis');
        //        properties.widgetYaxis = $widget.data('widgetyaxis');
        //    }

        //    properties.push(baseProps);



        //});

        



        const dashboardData = {
            Id: null, 
            ReferenceId:"45",
            Name: $("#dashboardName").val(),           
            CreatedBy: "admin", 
            CreatedDate: new Date().toISOString(),
            LastModifiedBy: "admin",
            LastModifiedDate: new Date().toISOString(),
            IsActive: true,
            IsPublish:true,
            IsLock: false,
            Properties: JSON.stringify(properties),
            UserName: $('#userDropdown option:selected').text(),
            UserId: $('#userDropdown').val(),
            Description: $('#description').val()
         };

            try {
                const response = await $.ajax({
                    url: '/BCMAdministration/DashboardList/SaveDashboardBuilder',
                    type: 'POST',
                    contentType: 'application/json', 
                    data: JSON.stringify(dashboardData), 
                });


                if (response.success) {
                $("#AddModal").modal('hide');
                alert(response.Message); // success
            } else {
                alert("Failed: " + response.Message); // error
            }
        } catch (err) {
            console.error("Error saving dashboard:", err);
            alert("Unexpected error while saving dashboard.");
        }
    });


    $('#widghtSearch').on('keyup', function () {
        const searchTerm = $(this).val().toLowerCase();
        $('.prebuildreport-leftsidebar ul li').each(function () {
            const itemText = $(this).text().toLowerCase();
            $(this).toggle(itemText.indexOf(searchTerm) > -1);
        });
    });

    setTimeout(() => {
        window.WidgetManager.initializeAllAmCharts();
    }, 2000);


});
