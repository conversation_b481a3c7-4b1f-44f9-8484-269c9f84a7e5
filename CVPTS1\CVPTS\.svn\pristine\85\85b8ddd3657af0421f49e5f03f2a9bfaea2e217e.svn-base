﻿@model IEnumerable<BCM.BusinessClasses.ImpactType>
@{
    ViewData["Title"] = "ImpactTypeMaster";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<style>
    .circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        max-width: 100%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .table > :not(caption) > * > * {
        background: transparent
    }
</style>
<div class="Page-Header d-flex align-items-center justify-content-between"> 
    <h6 class="Page-Title">Configure Impact Type Master </h6>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">

        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-inp" type="text" class="form-control" placeholder="Search">
        </div>
        @* <button type="button" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal"
        data-bs-target="#staticBackdrop">
        <i class="cv-Plus" title="Create New"></i>Create
        </button> *@
        <button type="button" id="btnCreate" class="btn icon-btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="Modal"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant  border-0 pe-2" style="height: calc(100vh - 115px);overflow: auto;">
    <div class="card-body">
        <div class="row g-3">
            @if (Model != null)
            {
                @foreach (var objImpact in Model)
                {
                    <div class="col-3 Incident-bg">
                        <div class="card shadow-sm h-100">
                            <div class="card-body bg-transparent">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <div class="circle delibrate_icon_bg">
                                            <i class="cv-delibrate-hazards fs-5 text-white"></i>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-1">
                                        <span class="btn-action btnEdit" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@objImpact.ImpactTypeID"><i class="cv-edit" title="Edit"></i></span>
                                        <span class="btn-action btnDelete" type="button" data-bs-toggle="modal" data-bs-target="Modal" data-id="@objImpact.ImpactTypeID"><i class="cv-delete text-danger" title="Delete"></i></span>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <table class="table table-sm table-borderless mb-0 align-middle">
                                        <tr>
                                            <td class="text-muted">Impact Type Name</td>
                                            <td>:</td>
                                            <td class="fw-semibold">@objImpact.ImpactTypeDetails</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">Impact Category</td>
                                            <td>:</td>
                                            <td class="fw-semibold">@objImpact.ImpactTypeName</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</div>


<div class="modal fade" id="Modal" tabindex="-1" aria-labelledby="ModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="Page-Title" id="modaltitle"></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center" id="deleteBody">
            </div>
        </div>
    </div>
</div>
<!-- Delete Modal -->
@section Scripts {
    <script>
        $(document).ready(function () {

            $('#btnCreate').click(function () {
                //$.get('/BCMAdministration/ImpactTypeMaster/AddImpactTypeMaster', function (data) {
                $.get('@Url.Action("AddImpactTypeMaster", "ImpactTypeMaster")', function (data) {
                    $('.modal-body').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Impact Type Master Configuration');
                });
            });

            $(document).on('click', '.btnEdit', function () {
                var iId = $(this).data('id');
                //$.get('/BCMAdministration/ImpactTypeMaster/EditImpactTypeMaster', { iId: iId }, function (data) {
                $.get('@Url.Action("EditImpactTypeMaster", "ImpactTypeMaster")', { iId: iId }, function (data) {
                    $('.modal-body').html(data);
                    $('#Modal').modal('show');
                    $('#modaltitle').text('Update Impact Type Master');
                });
            })

            $(document).on('click', '.btnDelete', function () {
                var iId = $(this).data('id');
                //$.get('/BCMAdministration/ImpactTypeMaster/DeleteImpactTypeMaster', { iId: iId }, function (data) {
                $.get('@Url.Action("DeleteImpactTypeMaster", "ImpactTypeMaster")', { iId: iId }, function (data) {
                    $('#deleteBody').html(data);
                    $('#DeleteModal').modal('show');
                    $('#modaltitle').text('Delete Impact Type Master');
                });
            })

            $('body').on('submit', 'form', function (e) {
                e.preventDefault();
                var form = $(this);
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    success: function (data) {
                        $('#Modal').modal('hide');
                        location.reload();
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                        console.error(xhr.status);
                        console.error(xhr.responseText);
                    }
                });
            });

         document.querySelectorAll('.Incident-bg').forEach((el, idx) => {
          const circle = el.querySelector('.circle');
          if (!circle) return;

          const colors = ['#f96443', '#f4b14f', '#727eff', '#7c38f1','#e63875','#d9dd17'];
          circle.style.backgroundColor = colors[idx % 6];
        });
        });
    </script>
}