﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BCM.UI.Areas.BCMDocuments.Controllers;
[Area("BCMDocuments")]
public class BCMManualController : BaseController
{
    private ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;

    public BCMManualController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public IActionResult BCMManual()
    {
        try
        {
            PopulateDropdowns();
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return View();
    }

    public void PopulateDropdowns()
    {
        try
        {
            ViewBag.selectedOrgID = _UserDetails.OrgID;
            //Organization
            ViewBag.OrgName = new SelectList(_Utilities.PupulateOrganisation(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.UserRoleID.ToString()), "Id", "OrganizationName");
            //Unit
            ViewBag.Unit = new SelectList(_Utilities.PupulateUnit(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString()), "UnitID", "UnitName");
            //Department
            ViewBag.Department = new SelectList(_Utilities.PupulateDepartment(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString()), "DepartmentID", "DepartmentName");
            //SubDepartment
            ViewBag.lstSubDepartment = new SelectList(_Utilities.PupulateSubDepartment(_UserDetails.OrgGroupID.ToString(),
                _UserDetails.OrgID.ToString(), _UserDetails.UserRoleID.ToString(), _UserDetails.UnitID.ToString()), "SubFunctionID", "SubFunctionName");
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
    }
}

