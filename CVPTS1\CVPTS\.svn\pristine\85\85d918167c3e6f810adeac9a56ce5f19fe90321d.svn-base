﻿am4core.useTheme(am4themes_animated);
var chart = am4core.create("DepartmentBIA-PreBuilt", am4charts.PieChart);
if (chart.logo) {
    chart.logo.disabled = true;
}
// Add data
chart.data = [{
    "country": "Completed",
    "litres": 1
}, {
    "country": "In Progress",
    "litres": 3
}, {
    "country": "Not Started",
    "litres": 1
}];

// Set inner radius
chart.innerRadius = am4core.percent(68);
chart.startAngle = 120;
chart.endAngle = 420;
chart.padding(0, 0, 0, 0);

// Add and configure Series
var pieSeries = chart.series.push(new am4charts.PieSeries());
pieSeries.dataFields.value = "litres";
pieSeries.dataFields.category = "country";
pieSeries.slices.template.tooltipText = "{category}: {value}";

pieSeries.slices.template.stroke = am4core.color("#fff");
pieSeries.slices.template.strokeWidth = 5;
pieSeries.slices.template.strokeOpacity = 5;
pieSeries.slices.template.cornerRadius = 20;
pieSeries.slices.template.innerCornerRadius = 20;
// This creates initial animation
pieSeries.hiddenState.properties.opacity = 1;
pieSeries.hiddenState.properties.endAngle = -90;
pieSeries.hiddenState.properties.startAngle = -90;
pieSeries.ticks.template.disabled = true;
pieSeries.labels.template.disabled = true;
pieSeries.colors.list = [
    am4core.color("#4218b3"),
    am4core.color("#ecb60f"),
    am4core.color("#f22721")
];

pieSeries.hiddenState.properties.opacity = 1;
pieSeries.hiddenState.properties.endAngle = -90;
pieSeries.hiddenState.properties.startAngle = -90;

chart.legend = new am4charts.Legend();
chart.legend.position = "right";
chart.legend.valueLabels.template.text = "{value}";
chart.legend.markers.template.width = 12;
chart.legend.markers.template.height = 12;  


