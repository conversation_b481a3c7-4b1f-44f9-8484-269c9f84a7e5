@using BCM.UI.Areas.BCMReports
@using BCM.UI.Areas.BCMReports.ReportTemplate
@using DevExpress.AspNetCore
@model BIAReport

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<link href="~/css/thirdparty.bundle.css" rel="stylesheet" />
<link href="~/css/designer.part.bundle.css" rel="stylesheet" />
<script src="~/js/thirdparty.bundle.js"></script>
<script src="~/js/designer.part.bundle.js" asp-append-version="true"></script>

<div class="card card-custom gutter-b">
    <div class="card-body">
        <div id="reportContainer">
            @Html.DevExpress().WebDocumentViewer("WebDocumentViewer").Height("1000px").Bind(Model);
        </div>
    </div>
</div>
