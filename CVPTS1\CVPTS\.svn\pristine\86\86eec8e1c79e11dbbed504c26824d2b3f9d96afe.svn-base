@{
    ViewData["Title"] = "BCM Training Test";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@if (ViewBag.ShowError == true)
{
    <div class="container mt-4">
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Error Loading Training Test</h4>
            <p>@ViewBag.ErrorMessage</p>
            <hr>
            <p class="mb-0">
                <a href="@Url.Action("TrainingandExamination", "TrainingandExamination", new { Area = "BCMTraining" })"
                    class="btn btn-primary">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Training List
                </a>
            </p>
        </div>
    </div>
}
else
{

    @section Scripts
    {
        <script>
            var timerInterval;
            var timeRemaining = @(ViewBag.TimeRemaining ?? 3600); // Default 1 hour in seconds
            var PublishID = @(Context.Session.GetInt32("PublishID") ?? 0);
            console.log('Global PublishID from session:', PublishID);
            var currentQuestionIndex = @(ViewBag.CurrentQuestionIndex ?? 1);
            var totalQuestions = @(ViewBag.TotalQuestions ?? 1);

            $(document).ready(function () {
                startTimer();
                restoreSelectedAnswers(); // Restore previously selected answers

                // Handle Previous button click
                $('#btnPrevious').on('click', function (e) {
                    e.preventDefault();
                    saveAndNavigate('previous');
                });

                // Handle Next button click
                $('#btnNext').on('click', function (e) {
                    e.preventDefault();
                    saveAndNavigate('next');
                });

                // Handle Finish button click
                $('#btnFinish').on('click', function (e) {
                    e.preventDefault();
                    console.log('Finish button clicked');
                    if (confirm('Are you sure you want to finish the exam?')) {
                        console.log('User confirmed finish exam');
                        finishExam();
                    } else {
                        console.log('User cancelled finish exam');
                    }
                });

                // Auto-save when radio button selection changes
                $(document).on('change', 'input[type="radio"]', function () {
                    // Debounce auto-save to avoid too many requests
                    clearTimeout(window.autoSaveTimeout);
                    window.autoSaveTimeout = setTimeout(function () {
                        autoSaveAnswer();
                    }, 1000); // Wait 1 second after last change
                });



                // Auto-save answers when radio button selection changes
                $('input[type="radio"]').on('change', function () {
                    autoSaveAnswer();
                });

                // Test if finishExam function is defined
                console.log('finishExam function defined:', typeof finishExam);

                // Test if finish button exists
                console.log('Finish button exists:', $('#btnFinish').length > 0);
                console.log('Finish button element:', $('#btnFinish'));
            });

            function startTimer() {
                timerInterval = setInterval(function () {
                    timeRemaining--;
                    updateTimerDisplay();

                    if (timeRemaining <= 0) {
                        clearInterval(timerInterval);
                        showTimeUpModal();
                        finishExam();
                    }
                }, 1000);
            }

            function updateTimerDisplay() {
                var hours = Math.floor(timeRemaining / 3600);
                var minutes = Math.floor((timeRemaining % 3600) / 60);
                var seconds = timeRemaining % 60;

                var timeString = String(hours).padStart(2, '0') + ':' +
                    String(minutes).padStart(2, '0') + ':' +
                    String(seconds).padStart(2, '0');

                $('#timerDisplay').text(timeString);
            }

            function saveAndNavigate(direction) {
                // Get the selected radio button value (single option ID)
                var selectedOptionId = 0;
                var checkedRadio = $('.options-container input[type="radio"]:checked');
                if (checkedRadio.length > 0) {
                    selectedOptionId = parseInt(checkedRadio.val()) || 0;
                }

                console.log('Selected Option ID:', selectedOptionId);
                // Get questionId with fallback logic
                var questionId = @(ViewBag.CurrentQuestionId ?? 0);
                var currentQuestionIndex = @(ViewBag.CurrentQuestionIndex ?? 1);
                console.log('Using global PublishID:', PublishID);
                // If questionId is 0, use currentQuestionIndex as fallback
                if (questionId <= 0) {
                    questionId = currentQuestionIndex;
                }

                // Show saving indicator
                showNotification('Saving answer...', 'info');
                console.log('Sending AJAX with PublishID:', PublishID);

                $.ajax({
                    url: '@Url.Action("SaveAnswer", "TrainingandExamination")',
                    type: 'POST',
                    data: {
                        questionId: questionId,
                        selectedOptionId: selectedOptionId,
                        direction: direction,
                        trainingId: @(ViewBag.TrainingId ?? 0),
                        PublishID: PublishID,
                        currentQuestionIndex: currentQuestionIndex

                    },
                    success: function (response) {
                        if (response.success) {
                            showNotification('Answer saved successfully!', 'success');
                            setTimeout(function () {
                                window.location.href = response.redirectUrl;
                            }, 500);
                        } else {
                            showNotification(response.message, 'error');
                        }
                    },
                    error: function () {
                        showNotification('Error saving answer. Please try again.', 'error');
                    }
                });
            }

            // Auto-save function when answers are selected
            function autoSaveAnswer() {
                // Get the selected radio button value (single option ID)
                var selectedOptionId = 0;
                var checkedRadio = $('input[type="radio"]:checked');
                if (checkedRadio.length > 0) {
                    selectedOptionId = parseInt(checkedRadio.val()) || 0;
                }

                // Get questionId with fallback logic
                var questionId = @(ViewBag.CurrentQuestionId ?? 0);
                var currentQuestionIndex = @(ViewBag.CurrentQuestionIndex ?? 1);

                // If questionId is 0, use currentQuestionIndex as fallback
                if (questionId <= 0) {
                    questionId = currentQuestionIndex;
                }

                $.ajax({
                    url: '@Url.Action("AutoSaveAnswer", "TrainingandExamination")',
                    type: 'POST',
                    data: {
                        questionId: questionId,
                        selectedOptionId: selectedOptionId,
                        trainingId: @(ViewBag.TrainingId ?? 0)
                                                                },
                    success: function (response) {
                        if (response.success) {
                            // Show subtle save indicator
                            $('#autoSaveIndicator').text('✓ Saved').removeClass('text-danger').addClass('text-success').show();
                            setTimeout(function () {
                                $('#autoSaveIndicator').fadeOut();
                            }, 2000);
                        } else {
                            $('#autoSaveIndicator').text('⚠ Save failed').removeClass('text-success').addClass('text-danger').show();
                        }
                    },
                    error: function () {
                        $('#autoSaveIndicator').text('⚠ Save failed').removeClass('text-success').addClass('text-danger').show();
                    }
                });
            }

            function autoSaveAnswer() {
                var selectedAnswers = [];
                $('input[type="radio"]:checked').each(function () {
                    selectedAnswers.push($(this).val());
                });

                $.ajax({
                    url: '@Url.Action("AutoSaveAnswer", "TrainingandExamination")',
                    type: 'POST',
                    data: {
                        questionId: @(ViewBag.CurrentQuestionId ?? 0),
                        selectedAnswers: selectedAnswers,
                        trainingId: @(ViewBag.TrainingId ?? 0)
                                                                },
                    success: function (response) {
                        if (response.success) {
                            showNotification('Answer saved automatically', 'success');
                        }
                    }
                });
            }

            // Restore previously selected answers
            function restoreSelectedAnswers() {
                // First, ensure all radio buttons are unchecked
                $('input[type="radio"]').prop('checked', false);

                @if (ViewBag.SelectedAnswers != null)
                    {
                        <text>
                            var selectedAnswers = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(ViewBag.SelectedAnswers));

                                                                                        if (selectedAnswers && selectedAnswers.length > 0) {
                                                                                            var restoredCount = 0;
                            selectedAnswers.forEach(function(answerId) {
                                                                                                var radioButton = $('input[type="radio"][value="' + answerId + '"]');
                                                                                                if (radioButton.length > 0) {
                                radioButton.prop('checked', true);
                            restoredCount++;
                                                                                                }
                                                                                            });

                                                                                            if (restoredCount > 0) {
                                // Show indicator that answers were restored
                                $('#autoSaveIndicator').text('✓ ' + restoredCount + ' previous answer(s) restored').removeClass('text-danger').addClass('text-success').show();
                            setTimeout(function() {
                                $('#autoSaveIndicator').fadeOut();
                                                                                                }, 3000);
                                                                                            }
                                                                                        }
                        </text>
                }
                                                        }





            function finishExam() {
                console.log('finishExam function called');

                try {
                    // Ensure PublishID is available
                    if (typeof PublishID === 'undefined') {
                        PublishID = @(Context.Session.GetInt32("PublishID") ?? 0);
                        console.log('PublishID was undefined, retrieved from session:', PublishID);
                    }

                    // Collect current question's selected answers before finishing
                    // Get the selected radio button value (single option ID)
                    var selectedOptionId = 0;
                    var checkedRadio = $('.options-container input[type="radio"]:checked');
                    if (checkedRadio.length > 0) {
                        selectedOptionId = parseInt(checkedRadio.val()) || 0;
                    }

                    var questionId = @(ViewBag.CurrentQuestionId ?? 0);
                    var currentQuestionIndex = @(ViewBag.CurrentQuestionIndex ?? 0);

                    console.log('Data collected - QuestionID:', questionId, 'SelectedOptionId:', selectedOptionId, 'PublishID:', PublishID);
                    console.log('TrainingId:', @(ViewBag.TrainingId ?? 0));

                    console.log('About to send AJAX request to FinishExam');

                    $.ajax({
                        url: '@Url.Action("FinishExam", "TrainingandExamination")',
                        type: 'POST',
                        data: {
                            trainingId: @(ViewBag.TrainingId ?? 0),
                            questionId: questionId,
                            selectedOptionId: selectedOptionId,
                            PublishID: PublishID
                        },
                        beforeSend: function () {
                            console.log('AJAX request started');
                        },
                        success: function (response) {
                            console.log('AJAX success response:', response);
                            if (response.success) {
                                showExamCompletedModal();
                            } else {
                                showNotification(response.message, 'error');
                            }
                        },
                        error: function (xhr, status, error) {
                            console.log('AJAX error:', status, error);
                            console.log('Response text:', xhr.responseText);
                            showNotification('Error finishing exam. Please try again.', 'error');
                        }
                    });
                } catch (ex) {
                    console.log('Exception in finishExam function:', ex);
                }
            }

            function showTimeUpModal() {
                $('#timeUpModal').modal('show');
            }

            function showExamCompletedModal() {
                $('#examCompletedModal').modal('show');
            }

            function viewResults() {
                window.location.href = '@Url.Action("ViewResults", "TrainingandExamination", new { trainingId = ViewBag.TrainingId })';
            }

            function showNotification(message, type) {
                var alertClass, iconClass;

                switch (type) {
                    case 'success':
                        alertClass = 'alert-success';
                        iconClass = 'fa-check-circle';
                        break;
                    case 'warning':
                        alertClass = 'alert-warning';
                        iconClass = 'fa-exclamation-triangle';
                        break;
                    case 'error':
                    default:
                        alertClass = 'alert-danger';
                        iconClass = 'fa-exclamation-triangle';
                        break;
                }

                var notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
                    '<i class="fas ' + iconClass + ' me-2"></i>' + message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>');

                $('body').append(notification);

                setTimeout(function () {
                    notification.alert('close');
                }, 5000);
            }
        </script>
    }

    <div class="container-fluid">
        <!-- Header Section -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clipboard-list me-2"></i>
                        <div>
                            <h5 class="mb-0">BCM Training Test</h5>
                            <small>@ViewBag.TrainingName</small>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock me-2"></i>
                        <h5 class="mb-0" id="timerDisplay">01:00:00</h5>
                    </div>
                </div>
            </div>
        </div>

        <!-- Question Section -->
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h6 class="text-muted mb-0">
                                Question @ViewBag.CurrentQuestionIndex of @ViewBag.TotalQuestions
                                <span id="autoSaveIndicator" class="ms-2 small" style="display: none;"></span>
                            </h6>
                            <div class="progress" style="width: 200px; height: 8px;">
                                <div class="progress-bar bg-primary" role="progressbar"
                                    style="width: @(((double)(ViewBag.CurrentQuestionIndex ?? 1) / (ViewBag.TotalQuestions ?? 1)) * 100)%">
                                </div>
                            </div>
                        </div>

                        <div class="question-container mb-4">
                            <h5 class="fw-bold mb-3">@ViewBag.QuestionText</h5>

                            @if (ViewBag.QuestionOptions != null)
                            {
                                <div class="options-container">
                                    @foreach (var option in (List<dynamic>)ViewBag.QuestionOptions)
                                    {
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="radio" name="<EMAIL>"
                                                value="@option.OptionID" id="option_@<EMAIL>">
                                            <label class="form-check-label"
                                                for="option_@<EMAIL>">
                                                @option.OptionText
                                            </label>
                                        </div>
                                    }
                                </div>
                            }
                        </div>

                        <!-- Navigation Buttons -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                @if (ViewBag.CurrentQuestionIndex > 1)
                                {
                                    <button type="button" class="btn btn-outline-primary" id="btnPrevious">
                                        <i class="fas fa-chevron-left me-1"></i>Previous
                                    </button>
                                }
                            </div>

                            <div class="text-center">
                                <span class="text-muted small" id="autoSaveStatus"></span>
                            </div>

                            <div>
                                @if (ViewBag.CurrentQuestionIndex < ViewBag.TotalQuestions)
                                {
                                    <button type="button" class="btn btn-primary" id="btnNext">
                                        Next<i class="fas fa-chevron-right ms-1"></i>
                                    </button>
                                }
                                else
                                {
                                    <button type="button" class="btn btn-success" id="btnFinish">
                                        <i class="fas fa-check me-1"></i>Finish Exam
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Time Up Modal -->
    <div class="modal fade" id="timeUpModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static"
        data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="fas fa-clock me-2"></i>Time Up!
                    </h5>
                </div>
                <div class="modal-body text-center py-4">
                    <i class="fas fa-hourglass-end fs-1 text-warning mb-3"></i>
                    <h5>Your exam time has expired</h5>
                    <p class="text-muted mb-0">The exam has been automatically submitted.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" onclick="viewResults()">
                        View Results
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Exam Completed Modal -->
    <div class="modal fade" id="examCompletedModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static"
        data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-check-circle me-2"></i>Exam Completed
                    </h5>
                </div>
                <div class="modal-body text-center py-4">
                    <i class="fas fa-trophy fs-1 text-success mb-3"></i>
                    <h5>Congratulations!</h5>
                    <p class="text-muted mb-0">You have successfully completed the training exam.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-success" onclick="viewResults()">
                        View Results
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden Fields -->
    <input type="hidden" id="trainingId" value="@ViewBag.TrainingId" />
    <input type="hidden" id="currentQuestionId" value="@ViewBag.CurrentQuestionId" />
    <input type="hidden" id="userTrainingId" value="@ViewBag.UserTrainingId" />

    <style>
        .question-container {
            min-height: 300px;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }

        .form-check {
            padding: 12px;
            border-radius: 6px;
            transition: background-color 0.2s ease;
        }

        .form-check:hover {
            background-color: #e9ecef;
        }

        .form-check-input:checked+.form-check-label {
            font-weight: 500;
            color: #0d6efd;
        }

        .progress {
            border-radius: 10px;
        }

        .progress-bar {
            border-radius: 10px;
        }

        .card {
            transition: all 0.3s ease;
        }

        .btn {
            border-radius: 6px;
            padding: 8px 16px;
        }

        .modal-content {
            border-radius: 12px;
            border: none;
        }

        .modal-header {
            border-radius: 12px 12px 0 0;
        }

        #timerDisplay {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>

}
<!-- End of else block -->
