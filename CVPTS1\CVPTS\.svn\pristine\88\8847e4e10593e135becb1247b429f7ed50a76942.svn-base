﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using BCM.Shared
@{
    int iIndex = 0;
}

<div class="card">
    <table id="example" class="table table-hover" style="width:100%;vertical-align:middle">
        <thead>
            <tr>
                <th>#</th>
                <th>Process Name</th>
                @*<th>Version</th>*@
                <th>Owner Name</th>
                @* <th>Org Level</th> *@
                <th>Unit</th>
                <th>Department</th>
                <th>SubDepartment</th>
                <th>RTO</th>
                <th>IsCritical</th>
                <th>Status</th>
                <th>Action</th>
                @* <th>Action</th> *@
            </tr>
        </thead>
        <tbody>
            @if (ViewBag.SurveyList != null)
            {
                foreach (var item in ViewBag.SurveyList)
                {
                    item.UnitName = string.IsNullOrEmpty(item.UnitName) ? "NA" : item.UnitName;
                    item.DepartmentName = string.IsNullOrEmpty(item.DepartmentName) ? "NA" : item.DepartmentName;
                    item.SubFunctionName = string.IsNullOrEmpty(item.SubFunctionName) ? "NA" : item.SubFunctionName;
                    item.ProcessOwner = string.IsNullOrEmpty(item.ProcessOwner) ? "NA" : item.ProcessOwner;

                    iIndex++;
                    <tr>
                        <td>@iIndex</td>
                        @* <td><span class="fw-semibold text-warning">@item.ProcessCode</span></td> *@
                        <td>
                            <input type="hidden" value="@item.ProcessID" />
                            @item.ProcessName
                            <span class="text-info">(@item.ProcessVersion)</span>
                        </td>
                        @* <td>@item.ProcessVersion</td> *@
                        <td>
                            <table>
                                <tbody>
                                    <tr>
                                        @* <td class="fw-semibold"><i class="cv-user"></i></td>
                                         <td> : </td>  *@
                                        <td>@item.ProcessOwner</td>
                                    </tr>
                                    @*  <tr>
                                        <td class="fw-semibold"><i class="cv-mail"></i></td>
                                        <td>:</td>
                                        <td>@item.OwnerEmail</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold"><i class="cv-phone"></i></td>
                                        <td>:</td>
                                        <td>@item.ProcessOwnerMobile</td>
                                    </tr> *@
                                </tbody>
                            </table>
                        </td>
                        @*<td>
                            <table>
                                <tbody>
                                 <tr title="Organization">
                                        <td class="fw-semibold"><i class="cv-company"></i></td>
                                        <td> : </td>
                                        <td>
                                            @item.OrgName
                                        </td>
                                    </tr> *
                                    <tr title="Unit">
                                        <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                        <td>:</td>
                                        <td>@item.UnitName</td>
                                    </tr>
                                    <tr title="Department">
                                        <td class="fw-semibold"><i class="cv-department"></i> </td>
                                        <td>:</td>
                                        <td>@item.DepartmentName</td>
                                    </tr>
                                    <tr title="Sub Department">
                                        <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                        <td>:</td>
                                        <td>@item.SubFunctionName</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td> *@
                        <td>
                            <table>
                                <tbody>
                                    @*                  <tr title="Organization">
                                        <td class="fw-semibold"><i class="cv-company"></i></td>
                                        <td> : </td>
                                        <td>
                                            @item.OrgName
                                        </td>
                                    </tr> *@
                                    <tr title="Unit">
                                        @* <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                         <td>:</td>  *@
                                        <td>@item.UnitName</td>
                                    </tr>
                                    @*  <tr title="Department">
                                        <td class="fw-semibold"><i class="cv-department"></i> </td>
                                        <td>:</td>
                                        <td>@item.DepartmentName</td>
                                    </tr>
                                    <tr title="Sub Department">
                                        <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                        <td>:</td>
                                        <td>@item.SubFunctionName</td>
                                    </tr> *@
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    @*                  <tr title="Organization">
                                        <td class="fw-semibold"><i class="cv-company"></i></td>
                                        <td> : </td>
                                        <td>
                                            @item.OrgName
                                        </td>
                                    </tr> *@
                                    @* <tr title="Unit">
                                        <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                        <td>:</td>
                                        <td>@item.UnitName</td>
                                    </tr> *@
                                    <tr title="Department">
                                        @* <td class="fw-semibold"><i class="cv-department"></i> </td>
                                         <td>:</td>  *@
                                        <td>@item.DepartmentName</td>
                                    </tr>
                                    @*  <tr title="Sub Department">
                                        <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                        <td>:</td>
                                        <td>@item.SubFunctionName</td>
                                    </tr> *@
                                </tbody>
                            </table>
                        </td>
                        <td>
                            <table>
                                <tbody>
                                    @*                  <tr title="Organization">
                                        <td class="fw-semibold"><i class="cv-company"></i></td>
                                        <td> : </td>
                                        <td>
                                            @item.OrgName
                                        </td>
                                    </tr> *@
                                    @*                                     <tr title="Unit">
                                        <td class="fw-semibold"><i class="cv-unit"></i> </td>
                                        <td>:</td>
                                        <td>@item.UnitName</td>
                                    </tr>
                                    <tr title="Department">
                                        <td class="fw-semibold"><i class="cv-department"></i> </td>
                                        <td>:</td>
                                        <td>@item.DepartmentName</td> 
                                    </tr>*@
                                    <tr title="Sub Department">
                                        @* <td class="fw-semibold"><i class="cv-subdepartment"></i> </td>
                                         <td>:</td>  *@
                                        <td>@item.SubFunctionName</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        @*<td>@Utilities.GetFormattedRTO(Convert.ToInt32(item.OwnerRTO))</td>*@
                        <td>
                            <table>
                                <tbody>
                                    <tr>
                                        @* <td class="fw-semibold"><i class="cv-RTO me-1"></i> </td>
                                        <td>:</td> *@
                                        <td>
                                            @{
                                                if (item.OwnerRTO == "0" || item.OwnerRTO == null)
                                                {
                                                    <span class="text-danger"><i class="cv-na" title="Not Available"></i></span>
                                                }
                                                else
                                                {
                                                    @Utilities.GetFormattedRTO(Convert.ToInt32(item.OwnerRTO))
                                                }
                                            }
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>@Utilities.GetIsCriticalStatusNew((item.IsCritical).ToString())</td>
                        <td>
                            @{
                                int statusId = Convert.ToInt32(@item.Status);
                            }
                            <span class="d-flex align-items-center @BCM.Shared.Utilities.ApprovalStatusWiseTextClass(statusId)">
                                <i class="@BCM.Shared.Utilities.ApprovalStatusWiseClass(statusId) me-2"></i>
                                @BCM.Shared.Utilities.ApprovalStatus(statusId)
                            </span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-2">
                                <a class="text-dark" asp-action="PerformProcessBIA" asp-controller="PerformProcessBIA" asp-route-strProcessID="@BCM.Security.Helper.CryptographyHelper.Encrypt(@item.ProcessID.ToString())">
                                    <i class="cv-page-name align-middle ViewBIA" type="button" title="View BIA"></i>
                                </a>
                                <div class="dropdown dropstart">
                                    <span class="btn-action" role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false"><i class="cv-activity-details" title="View Details"></i></span>
                                    <div class="dropdown-menu border-0">
                                        @* <h6 class="dropdown-header fw-semibold text-dark pb-0">Ptech Pune LTD</h6> *@
                                        <table class="table mb-0 table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <table class="table table-sm mb-0 table-borderless">
                                                            <tbody>
                                                                <tr>
                                                                    <th class="fw-semibold text-primary" colspan="3">Process Details</th>
                                                                </tr>
                                                                <tr>
                                                                    <td>Process Code </td>
                                                                    <td> : </td>
                                                                    <td>
                                                                        @if (string.IsNullOrEmpty(item.ProcessCode))
                                                                        {
                                                                            <span><i class="cv-na" title="Not Available"></i></span>
                                                                        }
                                                                        else
                                                                        {
                                                                            <span class="fw-semibold text-warning">  (@item.ProcessCode )</span>
                                                                        }
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Version </td>
                                                                    <td>:</td>
                                                                    <td>
                                                                        @if (string.IsNullOrEmpty(item.ProcessVersion))
                                                                        {
                                                                            <span><i class="cv-na" title="Not Available"></i></span>
                                                                        }
                                                                        else
                                                                        {
                                                                            <span class="fw-semibold text-warning">  (@item.ProcessVersion)</span>
                                                                        }
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Is Under BCM Scope</td>
                                                                    <td>:</td>
                                                                    <td>
                                                                        @if (string.IsNullOrEmpty(item.ProcessCode))
                                                                        {
                                                                            <span class="text-success fw-semibold">
                                                                                Yes
                                                                            </span>
                                                                        }
                                                                        else
                                                                        {
                                                                            <span class="text-danger fw-semibold">
                                                                                No
                                                                            </span>
                                                                        }
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <table class="table table-sm mb-0 table-borderless">
                                                            <tbody>
                                                                <tr>
                                                                    <th class="fw-semibold text-primary" colspan="3">Owner Details</th>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-semibold"><i class="cv-user"></i></td>
                                                                    <td> : </td>
                                                                    <td>
                                                                        @{
                                                                            if (string.IsNullOrEmpty(item.ProcessOwner))
                                                                            {
                                                                                <span><i class="cv-na" title="Not Available"></i></span>
                                                                            }
                                                                            else
                                                                            {
                                                                                @item.ProcessOwner
                                                                            }
                                                                        }
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-semibold"><i class="cv-mail"></i></td>
                                                                    <td>:</td>
                                                                    <td>@item.OwnerEmail</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-semibold"><i class="cv-phone"></i></td>
                                                                    <td>:</td>
                                                                    <td>@item.ProcessOwnerMobile</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </td>
                        @* <td>
                            <span class="btn-action" type="button"><i class="cv-edit" title="Edit"></i></span>
                            <span class="btn-action" type="button" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cv-delete text-danger" title="Delete"></i></span>
                        </td> *@
                    </tr>
                }
            }
        </tbody>
    </table>
</div>