﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BCM.UI.Areas.BCMTeams.Controllers;
[Area("BCMTeams")]
public class BCMGroupsController : BaseController
{
    private ProcessSrv _ProcessSrv;
    private readonly Utilities _Utilities;
    private readonly CVLogger _CVLogger;
    public BCMGroupsController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }

    [HttpGet]
    public JsonResult GetAllDepartments(int iUnitID)
    {
        try
        {
            var objDepartmentList = _ProcessSrv.GetDepartmentByUnitId(iUnitID);
            return Json(objDepartmentList);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllFacilities(int iUnitID)
    {
        try
        {
            var objFacilities = _ProcessSrv.GetFacilitieslistByUnitID(iUnitID, _UserDetails.OrgID, 0);
            return Json(objFacilities);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public JsonResult GetAllProcessNames(int iUnitID)
    {
        try
        {
            var objProcessNames = _ProcessSrv.GetAllProcessNames(_UserDetails.OrgID, iUnitID, 0, 0);
            return Json(objProcessNames);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return Json(null);
    }

    [HttpGet]
    public IActionResult BCMGroups()
    {
        List<BCMGroupInfo> lstBCMGroupInfo=new List<BCMGroupInfo>();
        try
        {
            lstBCMGroupInfo = _ProcessSrv.GetBCMGroupAllList();
            var objOrgID = lstBCMGroupInfo.FirstOrDefault()?.OrgID.ToString();
            if (objOrgID != null)
            {
                HttpContext.Session.SetString("objOrgID", objOrgID);
            }
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }        
        return View(lstBCMGroupInfo);
    }

    [HttpGet]
    public IActionResult AddBCMGroup()
    {
        try
        {
            ViewBag.selectedOrgID = _UserDetails.OrgID;
            ViewBag.ResourceInfo = new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
            ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(), "Id", "OrganizationName");
            ViewBag.UnitList = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails.OrgID), "UnitID", "UnitName");
            ViewBag.DepartmentList = new SelectList(_Utilities.GetDepartmentByUnitId(0), "DepartmentID", "DepartmentName");
            ViewBag.Facilities = new SelectList(_Utilities.GetFacilitieslistByUnitID(0, _UserDetails.OrgID, 0), "FacilityID", "FacilityName");
            ViewBag.ProcessNames = new SelectList(_Utilities.GetAllProcessNames(_UserDetails.OrgID, 0, 0, 0), "ProcessID", "ProcessName");
            return PartialView("_AddBCMGroup", new BCMGroupInfo());
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BCMGroups");
    }

    [HttpPost]
    public IActionResult AddBCMGroup(BCMGroupInfo objGroupInfo)
    {
        bool bSuccess = false;
        try
        {
            objGroupInfo.CreatedBy = _UserDetails.UserID;
            bSuccess = _ProcessSrv.BCMGroupSave(objGroupInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objGroupInfo.GroupName + " Added Successfully" : "Failed to Add." });
        }
        return RedirectToAction("BCMGroups");
    }

    [HttpGet]
    public IActionResult EditBCMGroup(int iID)
    {
        try
        {
            var objBcmGroup = _ProcessSrv.GetBCMGroupByID(iID);
            ViewBag.selectedOrgID = _UserDetails.OrgID;
            ViewBag.ResourceInfo = new SelectList(_Utilities.GetAllResourceList(), "ResourceId", "ResourceName");
            ViewBag.OrgInfo = new SelectList(_Utilities.GetOrganizationListByOrgGroupID_ForDropdown(), "Id", "OrganizationName");
            ViewBag.UnitList = new SelectList(_Utilities.GetUnitListByOrgID(_UserDetails.OrgID), "UnitID", "UnitName");
            ViewBag.DepartmentList = new SelectList(_Utilities.GetDepartmentByUnitId(objBcmGroup.UnitId), "DepartmentID", "DepartmentName");
            ViewBag.Facilities = new SelectList(_Utilities.GetFacilitieslistByUnitID(objBcmGroup.UnitId, _UserDetails.OrgID, 0), "FacilityID", "FacilityName");
            ViewBag.ProcessNames = new SelectList(_Utilities.GetAllProcessNames(_UserDetails.OrgID, objBcmGroup.UnitId, 0, 0), "ProcessID", "ProcessName");
            return PartialView("_EditBCMGroup", objBcmGroup);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BCMGroups");
    }

    [HttpPost]
    public IActionResult EditBCMGroup(BCMGroupInfo objGroupInfo)
    {
        bool bSuccess = false;
        try
        {
            objGroupInfo.UpdatedBy = _UserDetails.UserID;
            bSuccess = _ProcessSrv.BCMGroupUpdate(objGroupInfo);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objGroupInfo.GroupName + " Updated Successfully" : "Failed to Update." });
        }
        return RedirectToAction("BCMGroups");
    }

    [HttpGet]
    public IActionResult DeleteBCMGroup(int iID)
    {
        try
        {
            var objBcmGroup = _ProcessSrv.GetBCMGroupByID(iID);
            return PartialView("_DeleteBCMGroup", objBcmGroup);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        return RedirectToAction("BCMGroups");        
    }

    [HttpPost]
    public IActionResult DeleteBCMGroup(BCMGroupInfo objGroupInfo)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.BCMGroupDelete(objGroupInfo.OrgID, objGroupInfo.GroupMapID, 0);
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
        }
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return Json(new { success = bSuccess, message = bSuccess ? objGroupInfo.GroupName + " Deleted Successfully" : "Failed to Delete." });
        }
        return RedirectToAction("BCMGroups");
    }
}