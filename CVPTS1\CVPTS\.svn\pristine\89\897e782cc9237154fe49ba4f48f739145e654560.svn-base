﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class WidgetListController : BaseController
{
    private readonly Utilities _Utilities;
    private ProcessSrv _ProcessSrv;
    readonly CVLogger _CVLogger;
    private readonly ILoggerFactory? _LoggerFactory;
    public WidgetListController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
    {
        _ProcessSrv = ProcessSrv;
        _Utilities = Utilities;
        _CVLogger = CVLogger;
    }
    public IActionResult WidgetList()
    {
        return View();
    }

    [HttpGet]
    public IActionResult WidgetBuilderList()
    {
        List<DashboardInfo> lstWidgetBuilder = new List<DashboardInfo>();
        try
        {
            //PopulateDropDown();

            lstWidgetBuilder = _ProcessSrv.GetWidgetBuilderList();

            return Json(new { success = true, data = lstWidgetBuilder });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, Message = ex });
        }
        
    }


 [HttpGet]
 public JsonResult GetAllDatasets()
 {
     try
     {
         var data = _ProcessSrv.GetAllDatasets();
         return Json(new { success = true, data });
     }
     catch (Exception ex)
     {
         _CVLogger.LogErrorApp(ex);
         return Json(new { success = false, message = "Failed to load datasets." });
     }
 }


    [HttpPost]
    public IActionResult SaveWidgetBuilder([FromBody] WidgetInfo objInfo)
    {

        try
        {
            int iSuccess = _ProcessSrv.WidgetBuilder_Save_Upadte(objInfo);

            // Correcting the syntax for JsonResult usage
            return Json(new { success = true, Message = "Widget save successfuly" });

        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);

            return Json(new { success = false, Message = ex });
        }

    }


    [HttpPost]
    public IActionResult DeleteWidgetBuilder([FromBody] WidgetDeleteRequest request)
    {
        bool bSuccess = false;
        try
        {
            bSuccess = _ProcessSrv.Dashboard_Widget_Builder_Delete(request.Id, "widget");

            return Json(new { success = bSuccess, message = bSuccess ? "Widget" + " Deleted Successfully" : "Failed To Delete Widget." });
        }
        catch (Exception ex)
        {
            _CVLogger.LogErrorApp(ex);
            return Json(new { success = false, Message = ex });
        }
       
    }



    public class WidgetDeleteRequest
    {
        public string Id { get; set; }
    }


}