﻿@* 
@using BCM.Shared
@using BCM.BusinessClasses *@
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
<div class="card">
    <div class="card-body">
       
            <table class="table border">
                <thead>
                    <tr>
                        <th class="bg-secondary-subtle rounded-0">Plan&nbsp;Code</th>
                        <th class="bg-secondary-subtle">Org&nbsp;Level</th>
                        <th class="bg-secondary-subtle">Owner&nbsp;Name</th>
                        <th class="bg-secondary-subtle">Estimated&nbsp;Execution&nbsp;Time</th>
                        <th class="bg-secondary-subtle">Plan&nbsp;Revision&nbsp;Date</th>
                       @*  <th class="bg-secondary-subtle">View&nbsp;WorkFlow</th>
                        <th class="bg-secondary-subtle  rounded-0">View&nbsp;/&nbsp;Attach&nbsp;Matrix</th> *@
                    </tr>

                </thead>
                <tbody>
                    @* @if (ViewBag.RecoveryPlan != 0)
       { *@
                    @foreach (BCM.BusinessClasses.RecoveryPlan item in ViewBag.RecoveryPlan)
                    {
                        <tr>
                            <td>
                                <table>
                                    <tbody>
                                        <tr>
                                            <td class="text-muted"><i class="cv-process-code"></i></td>
                                            <td>:</td>
                                            <td>@item.PlanCode</td>
                                            @*                                              <td>CARP-2024-22</td> *@
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="cv-name"></i></td>
                                            <td>:</td>
                                            <td>@item.PlanName</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="cv-version"></i></td>
                                            <td>:</td>
                                            <td>@item.Version</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tbody>
                                        <tr>
                                            <td class="text-muted"><i class="cv-organization"></i></td>
                                            <td>:</td>
                                            <td>@item.OrgName</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="cv-unit"></i></td>
                                            <td>:</td>
                                            <td>@item.UnitName</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="cv-department"></i></td>
                                            <td>:</td>
                                            <td>@item.DepartmentName</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="cv-subdepartment"></i></td>
                                            <td>:</td>
                                            <td>@item.SubfunctionName</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td>
                                <table>
                                    <tbody>
                                        <tr>
                                            <td class="fw-semibold"><i class="cv-user"></i></td>
                                            <td> : </td>
                                            <td>@item.PlanOwnerName</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold"><i class="cv-mail"></i></td>
                                            <td>:</td>
                                            <td><a class="text-primary" href="#">@item.CompanyEmail</a></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold"><i class="cv-phone"></i></td>
                                            <td>:</td>
                                            <td>@item.MobilePhone</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td>@item.EstimatedRecoveryTime</td>
                            <td>@item.PlanReviewDate</td>
                        @*     <td>
                                <i class="cv-view_report fs-6"></i>
                            </td>
                            <td>
                                <i class="cv-attachment-folder fs-6"></i>
                            </td> *@
                        </tr>
                    }
                    @* } *@
                    @*  else
       {
           <tr>
               <td>
                   <table>
                       <tbody>
                           <tr><td>no records found...</td></tr>
                       </tbody>
                   </table>
               </td>
           </tr>
       } *@

                </tbody>
            </table>
@*         <div class="">
            <div style="float:inline-end;">
                <button type="submit" class="btn btn-primary btn-sm me-1 AddRecovery" id="btnAddRecovery">Update</button>
            </div>
        </div> *@
    </div>
</div>

<script>
                    //Save Recovery Plan
    $(document).on('click', '.AddRecovery', function () {
        debugger;

         var RiskID = $("#RiskID").val();
           var PlanID = $('#PlanID').val();
           var IncidentName = $('#IncidentDisplayName').val();

        $.get('@Url.Action("AttachRecoveryPlan", "ManageRisk")', { RiskID: RiskID,PlanID:PlanID,IncidentName:IncidentName }, function (data) {
             $('#RiskList').html(data);
            // $('.modal-body').html(data);
            // $('#NewVersionModal').modal('show');
        });
    });
</script>