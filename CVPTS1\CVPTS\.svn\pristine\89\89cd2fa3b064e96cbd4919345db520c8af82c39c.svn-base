﻿using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Areas.BCMAdministration.Controllers;
[Area("BCMAdministration")]
public class TableAccessController : BaseController
{
    private readonly Utilities _Utilities;
    public TableAccessController(Utilities Utilities) : base(Utilities)
    {
        _Utilities = Utilities;
    }
    public IActionResult TableAccess()
    {
        return View();
    }
}