﻿@{
    ViewData["Title"] = "Widget List";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<link href="~/css/widgetlibrary.css" rel="stylesheet" />




<style>
    .card-design-none{
        min-height:224px;
    }


    .toolbar {
        padding: 0.5rem 1rem;
        display: flex;
        gap: 2rem;
        align-items: center;
    }

        .toolbar i {
            font-size: 1.2rem;
            cursor: pointer;
            color: #333;
        }

    .grid-container {
        background-image: linear-gradient(to right, #e0e0e0 1px, transparent 1px), linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
        background-size: 40px 40px;
        height: calc(100vh - 160px);
        position: relative;
        overflow: auto;
    }

    .bcm-box {
        width: 280px;
      
        border: 1px solid #ccc;
        border-radius: 12px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
        background-color: #fff;
        padding: 0.5rem 1rem;
        position: absolute;
        top: 40%;
        left: 40%;
        transform: translate(-40%, -40%);
    }

    .bcm-title {
        font-weight: 600;
        font-size: 0.95rem;
        color: #333;
        margin-bottom: 0.3rem;
    }

    .widget.nav-tabs .nav-link.active {
        border-color: transparent;
        border-bottom: 2px solid #af1975;
        color: #af1975;
        background: transparent;
    }

    .widget.nav-tabs .nav-link {
        color: #838282;
    }

    .icon-preview {
        font-size: 1.2rem;
        margin-right: 0.5rem;
    }

     table {
      border-collapse: collapse;
      margin-top: 10px;
      width: 300px;
    }
    th, td {
      border: 1px solid #aaa;
      padding: 8px;
      text-align: center;
    }

    .color-box {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin: 0.3rem;
        border: 1px solid #ddd;
        cursor: pointer;
        display: inline-block;
    }

   .card-icon-active {
        /* background: rgb(230, 56, 117); */
        background: linear-gradient(114deg, rgba(230, 56, 117, 1) 4%, rgba(50, 2, 132, 1) 100%);
        border: none;
    }

    .color-grid {
        display: flex;
        flex-wrap: wrap;
        max-width: 180px;
    }
</style>
<div class="Page-Header header headerList">
    <div class="d-flex align-items-center gap-3">
        <h6 class="Page-Title">Widget Library</h6>
        <div class="tab-design">
            <ul class="nav nav-tabs gap-3" id="pills-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active fw-normal" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Pre-Built</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link fw-normal" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Custom</button>
                </li>
            </ul>
        </div>
    </div>
    <div class="d-flex gap-3 w-75 justify-content-end align-items-end">
        <div class="input-group Search-Input">
            <span class="input-group-text py-1"><i class="cv-search"></i></span>
            <input id="search-widget" type="text" class="form-control" placeholder="Search">
        </div>
        <button type="button" class="btn icon-btn btn-primary btn-sm" id="createCanvas"><i class="cv-Plus" title="Create New"></i>Create</button>
    </div>
</div>
<div class="Page-Condant headerList">
    <div class="tab-content" id="pills-tabContent">
        <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0">
            <div class="row g-3 mb-3 " id="prebuildList">
               
            </div>
            <div class="row g-3">
                <div class="col-md-8 col-lg-8 col-sm-12 d-grid" style="grid-template-rows: max-content; height:fit-content;">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="card h-100">
                                <div class="card-header header border-0">
                                    <h6 class="page_title d-flex align-items-center">
                                        <span class="p-2 bg-white shadow-sm rounded-circle">
                                            <i class="cv-business-continuity-summary align-middle text-primary fs-5"></i>
                                        </span>
                                        <span>Business Continuity Summary</span>
                                    </h6>
                                    <form class="d-flex tab-design">
                                        <ul class="nav nav-tabs gap-3" id="myTab" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane" type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">
                                                </button>
                                            </li>
                                        </ul>
                                    </form>
                                </div>
                                <div class="card-body pt-0">
                                    <div class="row h-100">
                                        <div class="tab-content" id="myTabContent">
                                            <div class="tab-pane fade  show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">
                                                <div class="row row-cols-3 g-3 dashboard-row">
                                                    <div class="col-12 col-xl-5 col-xxl-4">
                                                        <div class="card shadow-sm h-100">
                                                            <div class="card-header border-0">
                                                                <h6>Business Processes BIA</h6>
                                                            </div>
                                                            <div class="card-body pt-0">
                                                                <div id="DepartmentBIA-Chart" style="height:184px;"><div style="width: 100%; height: 100%; position: relative; top: -0.203125px;"><svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" role="group" style="width: 100%; height: 100%; overflow: visible;"><defs><clipPath id="id-46"><rect width="258" height="184"></rect></clipPath><linearGradient id="gradient-id-69" x1="1%" x2="99%" y1="59%" y2="41%"><stop stop-color="#474758" offset="0"></stop><stop stop-color="#474758" stop-opacity="1" offset="0.75"></stop><stop stop-color="#3cabff" stop-opacity="1" offset="0.755"></stop></linearGradient><clipPath id="id-78"></clipPath><filter id="filter-id-51" filterUnits="objectBoundingBox" width="200%" height="200%" x="-50%" y="-50%"><feGaussianBlur result="blurOut" in="SourceGraphic" stdDeviation="1.5"></feGaussianBlur><feOffset result="offsetBlur" dx="1" dy="1"></feOffset><feFlood flood-color="#000000" flood-opacity="0.5"></feFlood><feComposite in2="offsetBlur" operator="in"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter><filter id="filter-id-66" filterUnits="objectBoundingBox" width="120%" height="120%" x="-10%" y="-10%"><feColorMatrix type="saturate" values="0"></feColorMatrix></filter><filter id="filter-id-83" filterUnits="objectBoundingBox" width="200%" height="200%" x="-50%" y="-50%"><feGaussianBlur result="blurOut" in="SourceGraphic" stdDeviation="1.5"></feGaussianBlur><feOffset result="offsetBlur" dx="1" dy="1"></feOffset><feFlood flood-color="#000000" flood-opacity="0.5"></feFlood><feComposite in2="offsetBlur" operator="in"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><g><g fill="#ffffff" fill-opacity="0"><rect width="258" height="184"></rect></g><g><g role="region" clip-path="url(&quot;#id-46&quot;)" opacity="1" aria-label="Chart"><g><g><g><g><g><g transform="translate(62.5,95.3494)"><g><g role="group" aria-label="Series"><g><g clip-path="url(&quot;#id-78&quot;)"><g></g></g><g></g><g><g><g stroke-opacity="1" stroke="#ffffff" stroke-width="5" role="menuitem" focusable="true" tabindex="0" id="id-120" fill="#4218b3"><g><g><path d=" M-23.125,40.0537  L-23.125,40.0537  A3.75,3.75,0,0,1,-28.175,41.3058 a50,50,0,0,1,-21.6844,-37.5584 A3.75,3.75,0,0,1,-46.25,0 L-46.25,0  A3.915441176625,3.915441176625,0,0,1,-42.3346,3.7463 a42.5,42.5,0,0,0,17.9228,31.0433 A3.915441176625,3.915441176625,0,0,1,-23.125,40.0537 L-23.125,40.0537 "></path></g><g></g></g></g><g stroke-opacity="1" stroke="#ffffff" stroke-width="5" role="menuitem" focusable="true" tabindex="0" id="id-154" fill="#ecb60f"><g><g><path d=" M-46.25,0  L-46.25,0  A3.75,3.75,0,0,1,-49.8594,-3.7474 a50,50,0,0,1,99.7188,0 A3.75,3.75,0,0,1,46.25,0 L46.25,0  A3.915441176625,3.915441176625,0,0,1,42.3346,-3.7463 a42.5,42.5,0,0,0,-84.6691,0 A3.915441176625,3.915441176625,0,0,1,-46.25,0 L-46.25,0 "></path></g><g></g></g></g><g stroke-opacity="1" stroke="#ffffff" stroke-width="5" role="menuitem" focusable="true" tabindex="0" id="id-187" fill="#f22721" transform="translate(0,0)"><g><g><path d=" M46.25,0  L46.25,0  A3.75,3.75,0,0,1,49.8594,3.7474 a50,50,0,0,1,-21.6844,37.5584 A3.75,3.75,0,0,1,23.125,40.0537 L23.125,40.0537  A3.915441176625,3.915441176625,0,0,1,24.4117,34.7896 a42.5,42.5,0,0,0,17.9228,-31.0433 A3.915441176625,3.915441176625,0,0,1,46.25,0 L46.25,0 "></path></g><g></g></g></g></g></g><g><g></g></g><g><g></g></g></g></g></g></g><g transform="translate(62.5,95.3494)"><g><g><g></g></g></g></g></g></g><g role="group" aria-label="Legend" transform="translate(135,45.5)"><g><g focusable="true" tabindex="0" role="switch" aria-controls="id-120" aria-labelledby="id-120" aria-checked="true" transform="translate(10,0)" style="cursor: pointer;"><g fill="#ffffff" fill-opacity="0"><rect width="98" height="31"></rect></g><g transform="translate(0,8)"><g transform="translate(0,1.5)" style="pointer-events: none;"><g fill="#ffffff" fill-opacity="0" stroke-opacity="0"><rect width="12" height="12"></rect></g><g><g stroke-opacity="1" stroke="#ffffff" fill="#4218b3"><path d="M3,0 L9,0 a3,3 0 0 1 3,3 L12,9 a3,3 0 0 1 -3,3 L3,12 a3,3 0 0 1 -3,-3 L0,3 a3,3 0 0 1 3,-3 Z"></path></g></g></g><g fill="#000000" aria-label="Completed" style="pointer-events: none;" transform="translate(17,0)"><g style="user-select: none;"><text x="0" y="15" overflow="hidden" dy="-4.05"><tspan>Completed</tspan></text></g></g><g fill="#000000" aria-label="1" style="pointer-events: none;" transform="translate(85,0)"><g style="user-select: none;"><text x="8" y="15" dy="-4.05" text-anchor="end"><tspan>1</tspan></text></g></g></g></g><g focusable="true" tabindex="0" role="switch" aria-controls="id-154" aria-labelledby="id-154" aria-checked="true" transform="translate(10,31)" style="cursor: pointer;"><g fill="#ffffff" fill-opacity="0"><rect width="98" height="31"></rect></g><g transform="translate(0,8)"><g transform="translate(0,1.5)" style="pointer-events: none;"><g fill="#ffffff" fill-opacity="0" stroke-opacity="0"><rect width="12" height="12"></rect></g><g><g stroke-opacity="1" stroke="#ffffff" fill="#ecb60f"><path d="M3,0 L9,0 a3,3 0 0 1 3,3 L12,9 a3,3 0 0 1 -3,3 L3,12 a3,3 0 0 1 -3,-3 L0,3 a3,3 0 0 1 3,-3 Z"></path></g></g></g><g fill="#000000" aria-label="In Progress" style="pointer-events: none;" transform="translate(17,0)"><g style="user-select: none;"><text x="0" y="15" overflow="hidden" dy="-4.05"><tspan>In Progress</tspan></text></g></g><g fill="#000000" aria-label="3" style="pointer-events: none;" transform="translate(85,0)"><g style="user-select: none;"><text x="8" y="15" dy="-4.05" text-anchor="end"><tspan>3</tspan></text></g></g></g></g><g focusable="true" tabindex="0" role="switch" aria-controls="id-187" aria-labelledby="id-187" aria-checked="true" transform="translate(10,62)" style="cursor: pointer;"><g fill="#ffffff" fill-opacity="0"><rect width="98" height="31"></rect></g><g transform="translate(0,8)"><g transform="translate(0,1.5)" style="pointer-events: none;"><g fill="#ffffff" fill-opacity="0" stroke-opacity="0"><rect width="12" height="12"></rect></g><g><g stroke-opacity="1" stroke="#ffffff" fill="#f22721"><path d="M3,0 L9,0 a3,3 0 0 1 3,3 L12,9 a3,3 0 0 1 -3,3 L3,12 a3,3 0 0 1 -3,-3 L0,3 a3,3 0 0 1 3,-3 Z"></path></g></g></g><g fill="#000000" aria-label="Not Started" style="pointer-events: none;" transform="translate(17,0)"><g style="user-select: none;"><text x="0" y="15" overflow="hidden" dy="-4.05"><tspan>Not Started</tspan></text></g></g><g fill="#000000" aria-label="1" style="pointer-events: none;" transform="translate(85,0)"><g style="user-select: none;"><text x="8" y="15" dy="-4.05" text-anchor="end"><tspan>1</tspan></text></g></g></g></g></g></g></g></g></g></g><g><g><g role="tooltip" visibility="hidden" opacity="0"><g fill="#ffffff" fill-opacity="0.9" stroke-width="1" stroke-opacity="1" stroke="#ffffff" filter="url(&quot;#filter-id-51&quot;)" style="pointer-events: none;" transform="translate(0,6)"><path d="M3,0 L3,0 L0,-6 L13,0 L21,0 a3,3 0 0 1 3,3 L24,8 a3,3 0 0 1 -3,3 L3,11 a3,3 0 0 1 -3,-3 L0,3 a3,3 0 0 1 3,-3"></path></g><g><g fill="#ffffff" style="pointer-events: none;" transform="translate(12,6)"><g transform="translate(0,7)" display="none"></g></g></g></g><g visibility="hidden" display="none" style="pointer-events: none;"><g fill="#ffffff" opacity="1"><rect width="258" height="184"></rect></g><g><g transform="translate(129,92)"><g><g stroke-opacity="1" fill="#f3f3f3" fill-opacity="0.8"><g><g><path d=" M53,0  a53,53,0,0,1,-106,0 a53,53,0,0,1,106,0 M42,0  a42,42,0,0,0,-84,0 a42,42,0,0,0,84,0 L42,0 "></path></g></g></g><g stroke-opacity="1" fill="#000000" fill-opacity="0.2"><g><g><path d=" M50,0  a50,50,0,0,1,-100,0 a50,50,0,0,1,100,0 M45,0  a45,45,0,0,0,-90,0 a45,45,0,0,0,90,0 L45,0 "></path></g></g></g><g fill="#000000" fill-opacity="0.4"><g transform="translate(-16,-7.5)" style="user-select: none;"><text x="16px" y="15" dy="-4.05" text-anchor="middle"><tspan>100%</tspan></text></g></g></g></g></g></g><g opacity="0.3" aria-labelledby="id-66-title" filter="url(&quot;#filter-id-66&quot;)" display="none" style="cursor: pointer;"><g fill="#ffffff" opacity="0"><rect></rect></g><g><g shape-rendering="auto" fill="none" stroke-opacity="1" stroke-width="1.7999999999999998" stroke="#3cabff"><path d=" M15,15  C17.4001,15 22.7998,15.0001 27,15 C31.2002,14.9999 33.2999,6 36,6 C38.7001,6 38.6999,10.5 40.5,10.5 C42.3001,10.5 42.2999,6 45,6 C47.7001,6 50.9999,14.9999 54,15 C57.0002,15.0001 58.7999,15 60,15"></path></g><g shape-rendering="auto" fill="none" stroke-opacity="1" stroke-width="1.7999999999999998" stroke="url(&quot;#gradient-id-69&quot;)"><path d=" M6,15  C8.2501,15 9.7498,15.0001 15,15 C20.2502,14.9999 20.7748,3.6 27,3.6 C33.2252,3.6 33.8998,14.9999 39.9,15 C45.9002,15.0001 45.9748,15 51,15 C56.0252,15 57.7499,15 60,15"></path></g></g><title id="id-66-title">Chart created using amCharts library</title></g><g role="tooltip" opacity="0" aria-describedby="id-154" transform="translate(62.5,49.0994)" aria-hidden="true" visibility="hidden"><g fill="#ecb60f" fill-opacity="0.9" stroke-width="1" stroke-opacity="1" stroke="#ffffff" filter="url(&quot;#filter-id-83&quot;)" style="pointer-events: none;" transform="translate(-49.5,-32)"><path d="M3,0 L96,0 a3,3 0 0 1 3,3 L99,23 a3,3 0 0 1 -3,3 L96,26 L54.5,26 L49.5,32 L44.5,26 L3,26 a3,3 0 0 1 -3,-3 L0,3 a3,3 0 0 1 3,-3"></path></g><g><g fill="#000000" style="pointer-events: none;" transform="translate(0,-32)"><g transform="translate(-37.5,7)" style="user-select: none;"><text x="0" y="15" dy="-4.05"><tspan>In Progress: 3</tspan></text></g></g></g></g></g></g></g></g></svg></div></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-xl-3 col-xxl-2">
                                                        <div class="card shadow-sm h-100">
                                                            <div class="card-header border-0">
                                                                <h6>OverAll KPI Status</h6>
                                                            </div>
                                                            <div class="card-body pt-0 pb-2">
                                                                <div class="d-grid align-items-center justify-content-center mb-2">
                                                                    <div class="donut-chart" style="--progress: 55;">
                                                                        <svg width="125" height="125" viewBox="0 0 120 120">
                                                                            <circle class="circle-bg" cx="60" cy="60" r="50"></circle>
                                                                            <circle class="circle-progress " cx="60" cy="60" r="50" style="stroke:#d51ba1;"></circle>
                                                                        </svg>
                                                                        <div class="percentage">
                                                                            80
                                                                        </div>
                                                                    </div>
                                                                    <div class="d-grid text-center">
                                                                        <span class="fw-normal">
                                                                            <span class="fs-4">
                                                                                20
                                                                                %
                                                                            </span>
                                                                        </span>
                                                                        <span class="text-secondary">InProgress</span>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex align-items-center justify-content-between d-none">
                                                                    <div class="donut-chart" style="--progress: 60;">
                                                                        <svg width="92" height="92" viewBox="0 0 120 120">
                                                                            <circle class="circle-bg" cx="60" cy="60" r="50"></circle>
                                                                            <circle class="circle-progress" cx="60" cy="60" r="50" style="stroke:#2315a3;"></circle>
                                                                        </svg>
                                                                        <div class="percentage">60%</div>
                                                                    </div>
                                                                    <div class="d-grid">
                                                                        <span class="fw-normal">
                                                                            <span class="fs-4">06</span>
                                                                        </span>
                                                                        <span class="text-secondary">Marketing</span>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-xl-4 col-xxl-6">
                                                        <div class="card shadow-sm h-100">
                                                            <div class="card-header header border-0">
                                                                <h6>Review Progress BIA</h6>
                                                            </div>
                                                            <div class="card-body pt-0 pb-2">
                                                                <ul class="list-group list-group-flush d-grid h-100">
                                                                    <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                                                        <div class="d-flex gap-2">
                                                                            <i class="cv-dot fs-5" style="color:#2315a3;"></i>
                                                                            <div class="text-secondary">Business Progress</div>
                                                                        </div>
                                                                        <div class="d-flex flex-fill align-items-center gap-3">
                                                                            <div class="fw-semibold">4 / 32</div>
                                                                            <div class="flex-fill">
                                                                                <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="12" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                                                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 12%; background-color:#2315a3;"></div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="fw-normal">12%</div>
                                                                        </div>
                                                                    </li>
                                                                    <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                                                        <div class="d-flex gap-2">
                                                                            <i class="cv-dot fs-5" style="color:#cd086a;"></i>
                                                                            <div class="text-secondary">Recovery Plan</div>
                                                                        </div>
                                                                        <div class="d-flex flex-fill align-items-center gap-3">
                                                                            <div class="fw-semibold">16 / 23</div>
                                                                            <div class="flex-fill">
                                                                                <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                                                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 70%; background-color:#cd086a;"></div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="fw-normal">70%</div>
                                                                        </div>
                                                                    </li>
                                                                    <li class="list-group-item border-0 bg-transparent px-0 gap-3">
                                                                        <div class="d-flex gap-2">
                                                                            <i class="cv-dot fs-5" style="color:#880ba7;"></i>
                                                                            <div class="text-secondary">Risk Assessment</div>
                                                                        </div>
                                                                        <div class="d-flex flex-fill align-items-center gap-3">
                                                                            <div class="fw-semibold">6 / 78</div>
                                                                            <div class="flex-fill">
                                                                                <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuenow="8" aria-valuemin="0" aria-valuemax="100" style="height: 8px">
                                                                                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 8%; background-color:#880ba7;"></div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="fw-normal">8%</div>
                                                                        </div>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row row-cols-3 g-3 dashboard-row mt-0">
                                                    <div class="col-lg-8">
                                                        <div class="card shadow-sm h-100">
                                                            <div class="card-header border-0 header">
                                                                <h6>Business Entities Summary</h6>
                                                            </div>
                                                            <div class="card-body p-0 align-content-center">
                                                                <ul class="list-group list-group-horizontal">
                                                                    <li class="list-group-item bg-white flex-fill border-0 border-end">
                                                                        <div class="d-grid">
                                                                            <div class="d-flex align-items-center justify-content-between">
                                                                                <span class="fs-6">Risk Assessment</span>
                                                                                <span class="shadow-sm rounded-2 icon-circle" style="background-color:#2315a3;">
                                                                                    <i class="cv-initiated-2 text-white fs-4" style="line-height: 1.2;"></i>
                                                                                </span>
                                                                            </div>
                                                                            <span class="fw-normal my-1">
                                                                                <span class="fs-4">
                                                                                    16
                                                                                </span>
                                                                            </span>
                                                                            <span class="text-secondary">

                                                                                <a href="/BCMRiskAssessment/ManageRisk/ManageRisk?upcoming=1" style="text-decoration: underline; color: inherit;">

                                                                                    <span class="fw-semibold text-primary">0</span>
                                                                                    BCM Risk(s) has been
                                                                                    <span class="text-secondary">coming for review within next 7 days</span>
                                                                                </a>

                                                                            </span>

                                                                            <span class="text-secondary">

                                                                                <a href="/BCMRiskAssessment/ManageRisk/ManageRisk?past=1" style="text-decoration: underline; color: inherit;">

                                                                                    <span class="fw-semibold text-primary">14</span>
                                                                                    BCM Risk
                                                                                    <span class="text-secondary">item(s) past review date</span>
                                                                                </a>

                                                                            </span>
                                                                        </div>
                                                                    </li>
                                                                    <li class="list-group-item bg-white flex-fill border-0">
                                                                        <div class="d-grid">
                                                                            <div class="d-flex align-items-center justify-content-between">
                                                                                <span class="fs-6">Recovery Plan</span>
                                                                                <span class="shadow-sm rounded-2 icon-circle" style="background-color:#880ba7;">
                                                                                    <i class="cv-waiting-2 text-white fs-4" style="line-height: 1.2;"></i>
                                                                                </span>
                                                                            </div>
                                                                            <span class="fw-normal my-1">
                                                                                <span class="fs-4">
                                                                                    23
                                                                                </span>
                                                                            </span>
                                                                            <a href="/BCMFunctionRecoveryPlan/ManageRecoveryPlans/ManageRecoveryPlans?upcoming=1" style="text-decoration: underline; color: inherit;">
                                                                                <span class="text-secondary"><span class="fw-semibold text-primary">8</span> Recovery Plan(s) has been</span>
                                                                                <span class="text-secondary">coming for review within next 7 days</span>
                                                                            </a>

                                                                            <a href="/BCMFunctionRecoveryPlan/ManageRecoveryPlans/ManageRecoveryPlans?past=1" style="text-decoration: underline; color: inherit;">
                                                                                <span class="text-secondary"><span class="fw-semibold text-primary">13</span> Recovery Plan(s)</span>
                                                                                <span class="text-secondary">past review date</span>
                                                                            </a>
                                                                        </div>
                                                                    </li>
                                                                    <li class="list-group-item bg-white flex-fill border-0 border-start">
                                                                        <div class="d-grid">
                                                                            <div class="d-flex align-items-center justify-content-between">
                                                                                <span class="fs-6">Vendor</span>
                                                                                <span class="shadow-sm rounded-2 icon-circle" style="background-color:#cd086a;">
                                                                                    <i class="cv-approved text-white fs-4" style="line-height: 1.2;"></i>
                                                                                </span>
                                                                            </div>
                                                                            <span class="fw-normal my-1">
                                                                                <span class="fs-4">
                                                                                    20
                                                                                </span>
                                                                            </span>
                                                                            <span class="text-secondary">
                                                                                <a href="/BCMThirdParty/ManageVendor/ManageVendor?upcoming=1" style="text-decoration: underline; color: inherit;">
                                                                                    <span class="fw-semibold text-primary">0</span>
                                                                                    Vendor(s) contract

                                                                                    <span class="text-secondary">has been expiring in next 7 days</span>
                                                                                </a>
                                                                            </span>
                                                                            <span class="text-secondary">
                                                                                <a href="/BCMThirdParty/ManageVendor/ManageVendor?past=1" style="text-decoration: underline; color: inherit;">
                                                                                    <span class="fw-semibold text-primary">6</span>
                                                                                    Vendor(s) contract

                                                                                    <span class="text-secondary">is expired in past days</span>
                                                                                </a>
                                                                            </span>
                                                                        </div>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <div class="card shadow-sm h-100">
                                                            <div class="card-header border-0">
                                                                <h6>Critical &amp; Non-Critical Business</h6>
                                                            </div>
                                                            <div class="card-body pt-0">
                                                                <div class="card-group gap-3">
                                                                    <div class="card w-50 text-center rounded-2 bg-light border-light-subtle" style="box-shadow:none !important;">
                                                                        <div class="card-header bg-transparent border-0 pt-3">
                                                                            <span class="shadow-sm rounded-2 icon-circle mx-auto" style="background-color:#2315a3;">
                                                                                <i class="cv-success1 text-white fs-4" style="line-height: 1.2;"></i>
                                                                            </span>
                                                                        </div>
                                                                        <div class="card-body pt-1">
                                                                            <div class="fs-4">26</div>
                                                                            <span class="Sub-Title">Non Critical</span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="card w-50 text-center rounded-2 bg-light border-light-subtle" style="box-shadow:none !important;">
                                                                        <div class="card-header bg-transparent border-0 pt-3">
                                                                            <span class="shadow-sm rounded-2 icon-circle mx-auto" style="background-color:#cd086a;">
                                                                                <i class="cv-critical-entities text-white fs-4" style="line-height: 1.2;"></i>
                                                                            </span>
                                                                        </div>
                                                                        <div class="card-body pt-1">
                                                                            <div class="fs-4">4</div>
                                                                            <span class="Sub-Title">Critical</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="tab-pane fade h-100" id="disabled-tab-pane" role="tabpanel" aria-labelledby="disabled-tab" tabindex="0">
                                                <div class="card h-100">

                                                    <div class="card-header header border-0 pb-0" id="RiskFilterList">
                                                        <form class="d-flex tab-design mb-0">
                                                            <div class="form-check form-check-inline mb-0">
                                                                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="0" checked="">
                                                                <label class="form-check-label" for="inlineRadio1">All Risks</label>
                                                            </div>
                                                            <div class="form-check form-check-inline mb-0">
                                                                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="1">
                                                                <label class="form-check-label" for="inlineRadio2">Inherent Risks</label>
                                                            </div>
                                                            <div class="form-check form-check-inline mb-0">
                                                                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="2">
                                                                <label class="form-check-label" for="inlineRadio3">Residual Risks</label>
                                                            </div>
                                                        </form>

                                                    </div>
                                                    <div class="card-body pt-0">
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <div class="p-2 bg-secondary-subtle text-center my-2">
                                                                    <span>Impact( As a % of Annual Revenues )</span>
                                                                </div>

                                                                <!-- Heat Map Table -->
                                                                <div class="bg-white rounded p-3 mb-4">
                                                                    <div class="table-responsive">
                                                                        <table class="table table-bordered text-center mb-3 risk-table" id="tblheatmapSeverity">
                                                                            <thead>
                                                                                <tr class="bg-light">
                                                                                    <th class="text-center">HEAT MAP</th>
                                                                                    <th style="background-color: #48A422;">Insignificant</th>
                                                                                    <th style="background-color: #FFCC00;">Low</th>
                                                                                    <th style="background-color: #FFA500;">Medium</th>
                                                                                    <th style="background-color: #FF3300;">High</th>
                                                                                    <th style="background-color: #FF3300;">Critical</th>
                                                                                </tr>
                                                                                <tr>
                                                                                    <th></th>
                                                                                    <th class="text-center">1</th>
                                                                                    <th class="text-center">2</th>
                                                                                    <th class="text-center">3</th>
                                                                                    <th class="text-center">4</th>
                                                                                    <th class="text-center">5</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="text-center bg-light">Rare<br>1</td>
                                                                                    <td style="background-color: #48A422;"></td>
                                                                                    <td style="background-color: #FFCC00;"></td>
                                                                                    <td style="background-color: #FFA500;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="text-center bg-light">Unlikely<br>2</td>
                                                                                    <td style="background-color: #FFCC00;"></td>
                                                                                    <td style="background-color: #FFCC00;"></td>
                                                                                    <td style="background-color: #FFA500;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="text-center bg-light">Possible<br>3</td>
                                                                                    <td style="background-color: #FFA500;"></td>
                                                                                    <td style="background-color: #FFA500;"></td>
                                                                                    <td style="background-color: #FFA500;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="text-center bg-light">Likely<br>4</td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="text-center bg-light">Almost Certain<br>5</td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                    <td style="background-color: #FF3300;"></td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </div>

                                                                    <!-- Risk Severity Section -->
                                                                    <div class="table-responsive mt-4">
                                                                        <table class="table table-bordered text-center risk-table risk-severity-table" id="tblheatmapSeverity1">
                                                                            <thead>
                                                                                <tr class="bg-light">
                                                                                    <td class="text-center">RISK SEVERITY<br>( Probability x Impact )</td>
                                                                                    <th class="text-center" style="background-color: #48A422;">Insignificant</th>
                                                                                    <th class="text-center" style="background-color: #FFCC00;">Low</th>
                                                                                    <th class="text-center" style="background-color: #FFA500;">Medium</th>
                                                                                    <th class="text-center" style="background-color: #FF3300;">High</th>
                                                                                    <th class="text-center" style="background-color: #FF3300;">Critical</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td></td>
                                                                                    <td>Potential to cause<br>insignificant impact on<br>objectives</td>
                                                                                    <td>Potential to cause minor<br>impact that, in most cases,<br>can be absorbed</td>
                                                                                    <td>Potential to cause noticeable<br>impact on objectives</td>
                                                                                    <td>Potential to cause major<br>impact on objectives</td>
                                                                                    <td>Potential to cause severe<br>impact on objectives</td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </div>

                                                                <div id="RiskHeatMap_Chart"><div style="width: 100%; height: 100%; position: relative;"><svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" role="group" style="width: 100%; height: 100%; overflow: visible;"><defs><clipPath id="id-1385"><rect width="0" height="0"></rect></clipPath><linearGradient id="gradient-id-1408" x1="1%" x2="99%" y1="59%" y2="41%"><stop stop-color="#474758" offset="0"></stop><stop stop-color="#474758" stop-opacity="1" offset="0.75"></stop><stop stop-color="#3cabff" stop-opacity="1" offset="0.755"></stop></linearGradient><filter id="filter-id-1417" filterUnits="objectBoundingBox" width="200%" height="200%" x="-50%" y="-50%"></filter><filter id="filter-id-1437" filterUnits="objectBoundingBox" width="200%" height="200%" x="-50%" y="-50%"></filter><clipPath id="id-1472"><path d="M0,0 L0.0001,0 L0.0001,0.0001 L0,0.0001 L0,0"></path></clipPath><filter id="filter-id-1390" filterUnits="objectBoundingBox" width="200%" height="200%" x="-50%" y="-50%"><feGaussianBlur result="blurOut" in="SourceGraphic" stdDeviation="1.5"></feGaussianBlur><feOffset result="offsetBlur" dx="1" dy="1"></feOffset><feFlood flood-color="#000000" flood-opacity="0.5"></feFlood><feComposite in2="offsetBlur" operator="in"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter><filter id="filter-id-1405" filterUnits="objectBoundingBox" width="120%" height="120%" x="-10%" y="-10%"><feColorMatrix type="saturate" values="0"></feColorMatrix></filter><filter id="filter-id-1477" filterUnits="objectBoundingBox" width="200%" height="200%" x="-50%" y="-50%"><feGaussianBlur result="blurOut" in="SourceGraphic" stdDeviation="1.5"></feGaussianBlur><feOffset result="offsetBlur" dx="1" dy="1"></feOffset><feFlood flood-color="#000000" flood-opacity="0.5"></feFlood><feComposite in2="offsetBlur" operator="in"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><g><g fill="#ffffff" fill-opacity="0"><rect width="0" height="0"></rect></g><g><g role="region" clip-path="url(&quot;#id-1385&quot;)" opacity="1" aria-label="Chart"><g><g><g><g><g><g><g><g transform="translate(0,0)"><g fill="#ffffff" fill-opacity="0" transform="translate(0,0)"><rect width="0" height="0"></rect></g><g><g><g></g></g><g><g></g></g><g><g><g role="group" stroke-opacity="0" fill-opacity="1" stroke="#67b7dc" aria-label="Series"><g><g clip-path="url(&quot;#id-1472&quot;)"><g><g><g></g></g></g></g><g></g></g></g></g></g><g><g><g fill="#67b7dc" stroke="#67b7dc"><g></g></g></g></g><g><g><g><g></g></g><g><g></g></g></g></g><g><g></g></g><g><g></g></g><g role="button" focusable="true" tabindex="0" opacity="0" visibility="hidden" aria-hidden="true" transform="translate(0,-3)" aria-labelledby="id-1376-title"><g fill="#6794dc" stroke="#ffffff" fill-opacity="1" stroke-opacity="0" transform="translate(0,8)"><path d="M9,0 L9,0 a9,9 0 0 1 9,9 L18,9 a9,9 0 0 1 -9,9 L9,18 a9,9 0 0 1 -9,-9 L0,9 a9,9 0 0 1 9,-9 Z"></path></g><g transform="translate(9,9)"><g stroke="#ffffff" transform="translate(0,8)" style="pointer-events: none;"><path d=" M0,0  L11,0 " transform="translate(2.5,7.5)"></path></g><g fill="#000000" transform="translate(6,8)" style="pointer-events: none;"><g></g></g></g><title id="id-1376-title">Zoom Out</title></g></g></g><g><g><g aria-hidden="true"><g><g fill="#000000" transform="translate(0,0) rotate(-90)"><g></g></g><g stroke="#000000" stroke-opacity="0.15" fill="none" display="none" transform="translate(0,0)"><path transform="translate(-0.5,-0.5)" d=" M0,0  L0,0 "></path></g><g transform="translate(20,0)"><g><g fill="#000000" aria-label="L" fill-opacity="0" opacity="0" stroke-opacity="0" style="pointer-events: none;"><g transform="translate(-10,0)" style="user-select: none;"><text x="0" y="0" dy="0"><tspan>L</tspan></text></g></g><g fill="#000000"><g transform="translate(-10,0)"></g></g></g></g><g stroke="#000000" stroke-opacity="0" fill="none" style="pointer-events: none;"><path d=" M0,0  L0,0.0001 " transform="translate(-0.5,-0.5)"></path></g></g></g></g></g><g><g></g></g></g></g><g><g></g></g><g><g><g aria-hidden="true"><g><g stroke="#000000" stroke-opacity="0" fill="none" style="pointer-events: none;"><path d=" M0,0  L0.0001,0 " transform="translate(-0.5,-0.5)"></path></g><g><g><g fill="#000000" aria-label="L" fill-opacity="0" opacity="0" stroke-opacity="0" style="pointer-events: none;"><g transform="translate(0,10)" style="user-select: none;"><text x="0" y="0" dy="0"><tspan>L</tspan></text></g></g><g fill="#000000"><g transform="translate(0,10)"></g></g></g></g><g stroke="#000000" stroke-opacity="0.15" fill="none" display="none"><path transform="translate(-0.5,-0.5)" d=" M0,0  L0,0 "></path></g><g fill="#000000"><g></g></g></g></g></g></g></g></g></g></g></g></g><g><g><g role="tooltip" visibility="hidden" opacity="0"><g fill="#ffffff" fill-opacity="0.9" stroke-width="1" stroke-opacity="1" stroke="#ffffff" filter="url(&quot;#filter-id-1390&quot;)" style="pointer-events: none;"><path d="M3,0 L21,0 a3,3 0 0 1 3,3 L24,8 a3,3 0 0 1 -3,3 L3,11 a3,3 0 0 1 -3,-3 L0,3 a3,3 0 0 1 3,-3"></path></g><g><g fill="#ffffff" transform="translate(12,0)" style="pointer-events: none;"><g transform="translate(0,7)"></g></g></g></g><g visibility="hidden" style="pointer-events: none;" display="none"><g fill="#ffffff" opacity="1"><rect width="0" height="0"></rect></g><g><g><g><g stroke-opacity="1" fill="#f3f3f3" fill-opacity="0.8"><g><g><path d=" M53,0  a53,53,0,0,1,-106,0 a53,53,0,0,1,106,0 M42,0  a42,42,0,0,0,-84,0 a42,42,0,0,0,84,0 L42,0 "></path></g></g></g><g stroke-opacity="1" fill="#000000" fill-opacity="0.2"><g><g><path d=" M50,0  a50,50,0,0,1,-100,0 a50,50,0,0,1,100,0 M45,0  a45,45,0,0,0,-90,0 a45,45,0,0,0,90,0 L45,0 "></path></g></g></g><g fill="#000000" fill-opacity="0.4"><g></g></g></g></g></g></g><g opacity="0" aria-labelledby="id-1405-title" filter="url(&quot;#filter-id-1405&quot;)" display="none" aria-hidden="true" visibility="hidden" style="cursor: pointer;"><g fill="#ffffff" opacity="0"><rect></rect></g><g><g shape-rendering="auto" fill="none" stroke-opacity="1" stroke-width="1.7999999999999998" stroke="#3cabff"><path d=" M15,15  C17.4001,15 22.7998,15.0001 27,15 C31.2002,14.9999 33.2999,6 36,6 C38.7001,6 38.6999,10.5 40.5,10.5 C42.3001,10.5 42.2999,6 45,6 C47.7001,6 50.9999,14.9999 54,15 C57.0002,15.0001 58.7999,15 60,15"></path></g><g shape-rendering="auto" fill="none" stroke-opacity="1" stroke-width="1.7999999999999998" stroke="url(&quot;#gradient-id-1408&quot;)"><path d=" M6,15  C8.2501,15 9.7498,15.0001 15,15 C20.2502,14.9999 20.7748,3.6 27,3.6 C33.2252,3.6 33.8998,14.9999 39.9,15 C45.9002,15.0001 45.9748,15 51,15 C56.0252,15 57.7499,15 60,15"></path></g></g><title id="id-1405-title">Chart created using amCharts library</title></g><g role="tooltip" visibility="hidden" opacity="0"><g fill="#000000" fill-opacity="1" stroke-width="1" stroke-opacity="1" stroke="#000000" transform="translate(-10,5)" style="pointer-events: none;"><path d="M0,0 L5,0 L10,-5 L15,0 L20,0 a0,0 0 0 1 0,0 L20,10 a0,0 0 0 1 -0,0 L0,10 a0,0 0 0 1 -0,-0 L0,0 a0,0 0 0 1 0,-0"></path></g><g><g fill="#ffffff" transform="translate(0,5)" style="pointer-events: none;"><g transform="translate(0,5)"></g></g></g></g><g role="tooltip" visibility="hidden" opacity="0"><g fill="#000000" fill-opacity="1" stroke-width="1" stroke-opacity="1" stroke="#000000" transform="translate(-25,-5)" style="pointer-events: none;"><path d="M0,0 L20,0 a0,0 0 0 1 0,0 L20,0 L20,0 L25,5 L20,10 L20,10 a0,0 0 0 1 -0,0 L0,10 a0,0 0 0 1 -0,-0 L0,0 a0,0 0 0 1 0,-0"></path></g><g><g fill="#ffffff" transform="translate(-15,-5)" style="pointer-events: none;"><g transform="translate(0,5)"></g></g></g></g><g role="tooltip" visibility="hidden" opacity="0"><g fill="#ffffff" fill-opacity="0.9" stroke-width="1" stroke-opacity="1" stroke="#ffffff" filter="url(&quot;#filter-id-1477&quot;)" transform="translate(6,-5.5)" style="pointer-events: none;"><path d="M3,0 L21,0 a3,3 0 0 1 3,3 L24,8 a3,3 0 0 1 -3,3 L3,11 a3,3 0 0 1 -3,-3 L0,8 L0,10.5 L-6,5.5 L0,0.5 L0,3 a3,3 0 0 1 3,-3"></path></g><g><g fill="#ffffff" transform="translate(18,-5.5)" style="pointer-events: none;"><g transform="translate(0,7)"></g></g></g></g></g></g></g></g></svg></div></div>
                                                            </div>

                                                        </div>

                                                    </div>


                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-8 col-sm-12 ">
                            <div class="card">
                                <div class="card-header header border-0">
                                    <h6 class="page_title d-flex align-items-center mb-0">
                                        <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                            <i class="cv-BCM-calendar align-middle text-primary fs-5"></i>
                                        </span><span>BCM Calendar</span>
                                    </h6>
                                    <form class="d-flex">
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" style="padding: 2px 4px; font-size: 13px;">
                                                    Meetings
                                                </button>
                                                <div class="dropdown-menu  dropdown-menu-end border-0 shadow">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex align-items-center border-0 bg-transparent gap-2">
                                                            <span class="shadow-sm rounded-2 icon-circle" style="background-color:#2315a3;">
                                                                <i class="cv-task-in-progress text-white fs-5" style="line-height: 1.2;"></i>
                                                            </span>
                                                            <div class="text-truncate">
                                                                <div class="fw-semibold">Initiated</div>
                                                                <div class="text-secondary text-truncate" title="Meeting which are started">
                                                                    Meeting which are
                                                                    started
                                                                </div>
                                                            </div>
                                                            <span class="fw-normal my-1 ms-auto">
                                                                <span class="fs-5">2</span>
                                                            </span>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center border-0 bg-transparent gap-2">
                                                            <span class="shadow-sm rounded-2 icon-circle" style="background-color:#880ba7;">
                                                                <i class="cv-holiday-date text-white fs-5" style="line-height: 1.2;"></i>
                                                            </span>
                                                            <div class="text-truncate">
                                                                <div class="fw-semibold">Scheduled</div>
                                                                <div class="text-secondary text-truncate" title="Scheduled for future review">
                                                                    Scheduled for future
                                                                    review
                                                                </div>
                                                            </div>
                                                            <span class="fw-normal my-1 ms-auto">
                                                                <span class="fs-5">5</span>
                                                            </span>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center border-0 bg-transparent gap-2">
                                                            <span class="shadow-sm rounded-2 icon-circle" style="background-color:#cd086a;">
                                                                <i class="cv-success text-white fs-5" style="line-height: 1.2;"></i>
                                                            </span>
                                                            <div class="text-truncate">
                                                                <div class="fw-semibold">Completed</div>
                                                                <div class="text-secondary text-truncate" title="session which are completed">
                                                                    session which are
                                                                    completed
                                                                </div>
                                                            </div>
                                                            <span class="fw-normal my-1 ms-auto">
                                                                <span class="fs-5">1</span>
                                                            </span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <span tooltip="Create Event" class="btn-action" type="button" onclick="window.location.href = '../BCMCalendar/ManageBCMCalender/ManageBCMCalender'">
                                                <i class="cv-Plus me-1" title="Create"></i>
                                            </span>
                                        </div>
                                    </form>
                                </div>
                                <div class="card-body pt-0">
                                    <div id="calendar" class="fc fc-media-screen fc-direction-ltr fc-theme-standard"><div class="fc-header-toolbar fc-toolbar fc-toolbar-ltr"><div class="fc-toolbar-chunk"><div class="fc-button-group"><button type="button" title="Previous month" aria-pressed="false" class="fc-prev-button fc-button fc-button-primary"><span class="fc-icon fc-icon-chevron-left" role="img"></span></button><button type="button" title="Next month" aria-pressed="false" class="fc-next-button fc-button fc-button-primary"><span class="fc-icon fc-icon-chevron-right" role="img"></span></button></div><button type="button" title="This month" disabled="" aria-pressed="false" class="fc-today-button fc-button fc-button-primary">today</button></div><div class="fc-toolbar-chunk"><h2 class="fc-toolbar-title" id="fc-dom-1">July 2025</h2></div><div class="fc-toolbar-chunk"><div class="fc-button-group"><button type="button" title="month view" aria-pressed="true" class="fc-dayGridMonth-button fc-button fc-button-primary fc-button-active">month</button><button type="button" title="week view" aria-pressed="false" class="fc-timeGridWeek-button fc-button fc-button-primary">week</button><button type="button" title="day view" aria-pressed="false" class="fc-timeGridDay-button fc-button fc-button-primary">day</button></div></div></div><div aria-labelledby="fc-dom-1" class="fc-view-harness fc-view-harness-active" style="height: 434.074px;"><div class="fc-dayGridMonth-view fc-view fc-daygrid"><table role="grid" class="fc-scrollgrid  fc-scrollgrid-liquid"><thead role="rowgroup"><tr role="presentation" class="fc-scrollgrid-section fc-scrollgrid-section-header "><th role="presentation"><div class="fc-scroller-harness"><div class="fc-scroller" style="overflow: hidden;"><table role="presentation" class="fc-col-header " style="width: 583px;"><colgroup></colgroup><thead role="presentation"><tr role="row"><th role="columnheader" class="fc-col-header-cell fc-day fc-day-sun"><div class="fc-scrollgrid-sync-inner"><a aria-label="Sunday" class="fc-col-header-cell-cushion"><span class="d-grid"><span class="text-secondary fw-normal">Sun</span><span class="text-dark fw-semibold Day-colum">04</span></span></a></div></th><th role="columnheader" class="fc-col-header-cell fc-day fc-day-mon"><div class="fc-scrollgrid-sync-inner"><a aria-label="Monday" class="fc-col-header-cell-cushion"><span class="d-grid"><span class="text-secondary fw-normal">Mon</span><span class="text-dark fw-semibold Day-colum">05</span></span></a></div></th><th role="columnheader" class="fc-col-header-cell fc-day fc-day-tue"><div class="fc-scrollgrid-sync-inner"><a aria-label="Tuesday" class="fc-col-header-cell-cushion"><span class="d-grid"><span class="text-secondary fw-normal">Tue</span><span class="text-dark fw-semibold Day-colum">06</span></span></a></div></th><th role="columnheader" class="fc-col-header-cell fc-day fc-day-wed"><div class="fc-scrollgrid-sync-inner"><a aria-label="Wednesday" class="fc-col-header-cell-cushion"><span class="d-grid"><span class="text-secondary fw-normal">Wed</span><span class="text-dark fw-semibold Day-colum">07</span></span></a></div></th><th role="columnheader" class="fc-col-header-cell fc-day fc-day-thu"><div class="fc-scrollgrid-sync-inner"><a aria-label="Thursday" class="fc-col-header-cell-cushion"><span class="d-grid"><span class="text-secondary fw-normal">Thu</span><span class="text-dark fw-semibold Day-colum">08</span></span></a></div></th><th role="columnheader" class="fc-col-header-cell fc-day fc-day-fri"><div class="fc-scrollgrid-sync-inner"><a aria-label="Friday" class="fc-col-header-cell-cushion"><span class="d-grid"><span class="text-secondary fw-normal">Fri</span><span class="text-dark fw-semibold Day-colum">09</span></span></a></div></th><th role="columnheader" class="fc-col-header-cell fc-day fc-day-sat"><div class="fc-scrollgrid-sync-inner"><a aria-label="Saturday" class="fc-col-header-cell-cushion"><span class="d-grid"><span class="text-secondary fw-normal">Sat</span><span class="text-dark fw-semibold Day-colum">10</span></span></a></div></th></tr></thead></table></div></div></th></tr></thead><tbody role="rowgroup"><tr role="presentation" class="fc-scrollgrid-section fc-scrollgrid-section-body  fc-scrollgrid-section-liquid"><td role="presentation"><div class="fc-scroller-harness fc-scroller-harness-liquid"><div class="fc-scroller fc-scroller-liquid-absolute" style="overflow: hidden auto;"><div class="fc-daygrid-body fc-daygrid-body-balanced " style="width: 583px;"><table role="presentation" class="fc-scrollgrid-sync-table" style="width: 583px; height: 440px;"><colgroup></colgroup><tbody role="presentation"><tr role="row"><td aria-labelledby="fc-dom-2" role="gridcell" data-date="2025-06-29" class="fc-day fc-day-sun fc-day-past fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to June 29, 2025" data-navlink="" tabindex="0" id="fc-dom-2" class="fc-daygrid-day-number">29</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-event-harness" style="margin-top: 0px;"><a class="fc-event fc-event-draggable fc-event-resizable fc-event-end fc-event-past fc-daygrid-event fc-daygrid-block-event fc-h-event" style="border-color: rgb(214, 204, 230); background-color: rgb(214, 204, 230);"><div class="fc-event-main" style="color: rgb(51, 51, 51);"><div class="fc-event-title">DR Drill</div><div class="fc-event-img"><img src="/img/Profile-img/Usericon.svg" class="img-fluid rounded-circle border border-white border-2" style="width:24px"></div></div><div class="fc-event-resizer fc-event-resizer-end"></div></a></div><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-4" role="gridcell" data-date="2025-06-30" class="fc-day fc-day-mon fc-day-past fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to June 30, 2025" data-navlink="" tabindex="0" id="fc-dom-4" class="fc-daygrid-day-number">30</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-6" role="gridcell" data-date="2025-07-01" class="fc-day fc-day-tue fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 1, 2025" data-navlink="" tabindex="0" id="fc-dom-6" class="fc-daygrid-day-number">1</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-8" role="gridcell" data-date="2025-07-02" class="fc-day fc-day-wed fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 2, 2025" data-navlink="" tabindex="0" id="fc-dom-8" class="fc-daygrid-day-number">2</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-10" role="gridcell" data-date="2025-07-03" class="fc-day fc-day-thu fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 3, 2025" data-navlink="" tabindex="0" id="fc-dom-10" class="fc-daygrid-day-number">3</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-12" role="gridcell" data-date="2025-07-04" class="fc-day fc-day-fri fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 4, 2025" data-navlink="" tabindex="0" id="fc-dom-12" class="fc-daygrid-day-number">4</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-14" role="gridcell" data-date="2025-07-05" class="fc-day fc-day-sat fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 5, 2025" data-navlink="" tabindex="0" id="fc-dom-14" class="fc-daygrid-day-number">5</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td></tr><tr role="row"><td aria-labelledby="fc-dom-16" role="gridcell" data-date="2025-07-06" class="fc-day fc-day-sun fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 6, 2025" data-navlink="" tabindex="0" id="fc-dom-16" class="fc-daygrid-day-number">6</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-18" role="gridcell" data-date="2025-07-07" class="fc-day fc-day-mon fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 7, 2025" data-navlink="" tabindex="0" id="fc-dom-18" class="fc-daygrid-day-number">7</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-20" role="gridcell" data-date="2025-07-08" class="fc-day fc-day-tue fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 8, 2025" data-navlink="" tabindex="0" id="fc-dom-20" class="fc-daygrid-day-number">8</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-22" role="gridcell" data-date="2025-07-09" class="fc-day fc-day-wed fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 9, 2025" data-navlink="" tabindex="0" id="fc-dom-22" class="fc-daygrid-day-number">9</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-24" role="gridcell" data-date="2025-07-10" class="fc-day fc-day-thu fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 10, 2025" data-navlink="" tabindex="0" id="fc-dom-24" class="fc-daygrid-day-number">10</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-26" role="gridcell" data-date="2025-07-11" class="fc-day fc-day-fri fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 11, 2025" data-navlink="" tabindex="0" id="fc-dom-26" class="fc-daygrid-day-number">11</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-28" role="gridcell" data-date="2025-07-12" class="fc-day fc-day-sat fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 12, 2025" data-navlink="" tabindex="0" id="fc-dom-28" class="fc-daygrid-day-number">12</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td></tr><tr role="row"><td aria-labelledby="fc-dom-30" role="gridcell" data-date="2025-07-13" class="fc-day fc-day-sun fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 13, 2025" data-navlink="" tabindex="0" id="fc-dom-30" class="fc-daygrid-day-number">13</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-32" role="gridcell" data-date="2025-07-14" class="fc-day fc-day-mon fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 14, 2025" data-navlink="" tabindex="0" id="fc-dom-32" class="fc-daygrid-day-number">14</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-34" role="gridcell" data-date="2025-07-15" class="fc-day fc-day-tue fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 15, 2025" data-navlink="" tabindex="0" id="fc-dom-34" class="fc-daygrid-day-number">15</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-36" role="gridcell" data-date="2025-07-16" class="fc-day fc-day-wed fc-day-past fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 16, 2025" data-navlink="" tabindex="0" id="fc-dom-36" class="fc-daygrid-day-number">16</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-38" role="gridcell" data-date="2025-07-17" class="fc-day fc-day-thu fc-day-today fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 17, 2025" data-navlink="" tabindex="0" id="fc-dom-38" class="fc-daygrid-day-number">17</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-40" role="gridcell" data-date="2025-07-18" class="fc-day fc-day-fri fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 18, 2025" data-navlink="" tabindex="0" id="fc-dom-40" class="fc-daygrid-day-number">18</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-42" role="gridcell" data-date="2025-07-19" class="fc-day fc-day-sat fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 19, 2025" data-navlink="" tabindex="0" id="fc-dom-42" class="fc-daygrid-day-number">19</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td></tr><tr role="row"><td aria-labelledby="fc-dom-44" role="gridcell" data-date="2025-07-20" class="fc-day fc-day-sun fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 20, 2025" data-navlink="" tabindex="0" id="fc-dom-44" class="fc-daygrid-day-number">20</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-46" role="gridcell" data-date="2025-07-21" class="fc-day fc-day-mon fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 21, 2025" data-navlink="" tabindex="0" id="fc-dom-46" class="fc-daygrid-day-number">21</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-48" role="gridcell" data-date="2025-07-22" class="fc-day fc-day-tue fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 22, 2025" data-navlink="" tabindex="0" id="fc-dom-48" class="fc-daygrid-day-number">22</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-50" role="gridcell" data-date="2025-07-23" class="fc-day fc-day-wed fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 23, 2025" data-navlink="" tabindex="0" id="fc-dom-50" class="fc-daygrid-day-number">23</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-52" role="gridcell" data-date="2025-07-24" class="fc-day fc-day-thu fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 24, 2025" data-navlink="" tabindex="0" id="fc-dom-52" class="fc-daygrid-day-number">24</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-54" role="gridcell" data-date="2025-07-25" class="fc-day fc-day-fri fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 25, 2025" data-navlink="" tabindex="0" id="fc-dom-54" class="fc-daygrid-day-number">25</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-56" role="gridcell" data-date="2025-07-26" class="fc-day fc-day-sat fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 26, 2025" data-navlink="" tabindex="0" id="fc-dom-56" class="fc-daygrid-day-number">26</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td></tr><tr role="row"><td aria-labelledby="fc-dom-58" role="gridcell" data-date="2025-07-27" class="fc-day fc-day-sun fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 27, 2025" data-navlink="" tabindex="0" id="fc-dom-58" class="fc-daygrid-day-number">27</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-60" role="gridcell" data-date="2025-07-28" class="fc-day fc-day-mon fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 28, 2025" data-navlink="" tabindex="0" id="fc-dom-60" class="fc-daygrid-day-number">28</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-62" role="gridcell" data-date="2025-07-29" class="fc-day fc-day-tue fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 29, 2025" data-navlink="" tabindex="0" id="fc-dom-62" class="fc-daygrid-day-number">29</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-64" role="gridcell" data-date="2025-07-30" class="fc-day fc-day-wed fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 30, 2025" data-navlink="" tabindex="0" id="fc-dom-64" class="fc-daygrid-day-number">30</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-66" role="gridcell" data-date="2025-07-31" class="fc-day fc-day-thu fc-day-future fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to July 31, 2025" data-navlink="" tabindex="0" id="fc-dom-66" class="fc-daygrid-day-number">31</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-68" role="gridcell" data-date="2025-08-01" class="fc-day fc-day-fri fc-day-future fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to August 1, 2025" data-navlink="" tabindex="0" id="fc-dom-68" class="fc-daygrid-day-number">1</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-70" role="gridcell" data-date="2025-08-02" class="fc-day fc-day-sat fc-day-future fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to August 2, 2025" data-navlink="" tabindex="0" id="fc-dom-70" class="fc-daygrid-day-number">2</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td></tr><tr role="row"><td aria-labelledby="fc-dom-72" role="gridcell" data-date="2025-08-03" class="fc-day fc-day-sun fc-day-future fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to August 3, 2025" data-navlink="" tabindex="0" id="fc-dom-72" class="fc-daygrid-day-number">3</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-74" role="gridcell" data-date="2025-08-04" class="fc-day fc-day-mon fc-day-future fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to August 4, 2025" data-navlink="" tabindex="0" id="fc-dom-74" class="fc-daygrid-day-number">4</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-76" role="gridcell" data-date="2025-08-05" class="fc-day fc-day-tue fc-day-future fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to August 5, 2025" data-navlink="" tabindex="0" id="fc-dom-76" class="fc-daygrid-day-number">5</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-78" role="gridcell" data-date="2025-08-06" class="fc-day fc-day-wed fc-day-future fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to August 6, 2025" data-navlink="" tabindex="0" id="fc-dom-78" class="fc-daygrid-day-number">6</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-80" role="gridcell" data-date="2025-08-07" class="fc-day fc-day-thu fc-day-future fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to August 7, 2025" data-navlink="" tabindex="0" id="fc-dom-80" class="fc-daygrid-day-number">7</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-82" role="gridcell" data-date="2025-08-08" class="fc-day fc-day-fri fc-day-future fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to August 8, 2025" data-navlink="" tabindex="0" id="fc-dom-82" class="fc-daygrid-day-number">8</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td><td aria-labelledby="fc-dom-84" role="gridcell" data-date="2025-08-09" class="fc-day fc-day-sat fc-day-future fc-day-other fc-daygrid-day"><div class="fc-daygrid-day-frame fc-scrollgrid-sync-inner"><div class="fc-daygrid-day-top"><a title="Go to August 9, 2025" data-navlink="" tabindex="0" id="fc-dom-84" class="fc-daygrid-day-number">9</a></div><div class="fc-daygrid-day-events"><div class="fc-daygrid-day-bottom" style="margin-top: 0px;"></div></div><div class="fc-daygrid-day-bg"></div></div></td></tr></tbody></table></div></div></div></td></tr></tbody></table></div></div></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-12 d-grid" style="grid-template-rows: max-content;">
                            <div class="card mb-3">
                                <div class="card-header header border-0">
                                    <h6 class="page_title d-flex align-items-center mb-0">
                                        <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                            <i class="cv-to-do-list align-middle text-primary fs-5"></i>
                                        </span>
                                        <span>To Do List</span>
                                    </h6>
                                    <div class="d-none gap-2">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="actionWithSelected" data-bs-toggle="dropdown" aria-expanded="false">
                                                Action with selected
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="actionWithSelected">
                                                <li>
                                                    <a class="dropdown-item todo-action" data-action="done" href="#">Done</a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item todo-action" data-action="archive" href="#">
                                                        Add
                                                        to archive
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item todo-action" data-action="remove" href="#">Remove</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary active" data-filter="all">
                                                All
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" data-filter="done">
                                                Done
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" data-filter="archived">
                                                Archived
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0" style="max-height: 160px; overflow-y:auto;">
                                    <div class="todo-list-container" style="height: 160px !important; min-height: 160px !important;">
                                        <ul class="list-group list-group-flush todo-list-container" id="todoListItems">
                                            <li class="list-group-item d-flex align-items-center border-0 bg-transparent">
                                                <!-- Explicitly set checked to false -->
                                                <img class="me-2" src="/img/Profile-img/Usericon.svg" height="30">
                                                <div>
                                                    <div class="fw-semibold">this is another TODO list for archive</div>
                                                    <div class="text-primary">Created on  <small class="text-black-50">11-06-2025 16:21:34</small></div>
                                                </div>
                                                <span class="ms-auto edit-todo-btn d-none" role="button" data-id="10" data-description="this is another TODO list for archive">
                                                    <i class="cv-edit fs-4 text-primary"></i>
                                                </span>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center border-0 bg-transparent">
                                                <!-- Explicitly set checked to false -->
                                                <img class="me-2" src="/img/Profile-img/Usericon.svg" height="30">
                                                <div>
                                                    <div class="fw-semibold">this is another to list for done 101</div>
                                                    <div class="text-primary">Created on  <small class="text-black-50">11-06-2025 16:21:06</small></div>
                                                </div>
                                                <span class="ms-auto edit-todo-btn d-none" role="button" data-id="9" data-description="this is another to list for done 101">
                                                    <i class="cv-edit fs-4 text-primary"></i>
                                                </span>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center border-0 bg-transparent">
                                                <!-- Explicitly set checked to false -->
                                                <img class="me-2" src="/img/Profile-img/Usericon.svg" height="30">
                                                <div>
                                                    <div class="fw-semibold">this to list for archive</div>
                                                    <div class="text-primary">Created on  <small class="text-black-50">11-06-2025 15:51:08</small></div>
                                                </div>
                                                <span class="ms-auto edit-todo-btn d-none" role="button" data-id="7" data-description="this to list for archive">
                                                    <i class="cv-edit fs-4 text-primary"></i>
                                                </span>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center border-0 bg-transparent">
                                                <!-- Explicitly set checked to false -->
                                                <img class="me-2" src="/img/Profile-img/Usericon.svg" height="30">
                                                <div>
                                                    <div class="fw-semibold">this to list is for done</div>
                                                    <div class="text-primary">Created on  <small class="text-black-50">11-06-2025 15:50:55</small></div>
                                                </div>
                                                <span class="ms-auto edit-todo-btn d-none" role="button" data-id="6" data-description="this to list is for done">
                                                    <i class="cv-edit fs-4 text-primary"></i>
                                                </span>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center border-0 bg-transparent">
                                                <!-- Explicitly set checked to false -->
                                                <img class="me-2" src="/img/Profile-img/Usericon.svg" height="30">
                                                <div>
                                                    <div class="fw-semibold">This is 2nd to do list for testing</div>
                                                    <div class="text-primary">Created on  <small class="text-black-50">11-06-2025 15:50:33</small></div>
                                                </div>
                                                <span class="ms-auto edit-todo-btn d-none" role="button" data-id="5" data-description="This is 2nd to do list for testing">
                                                    <i class="cv-edit fs-4 text-primary"></i>
                                                </span>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center border-0 bg-transparent">
                                                <!-- Explicitly set checked to false -->
                                                <img class="me-2" src="/img/Profile-img/Usericon.svg" height="30">
                                                <div>
                                                    <div class="fw-semibold">This is 1st to do list new</div>
                                                    <div class="text-primary">Created on  <small class="text-black-50">11-06-2025 15:50:12</small></div>
                                                </div>
                                                <span class="ms-auto edit-todo-btn d-none" role="button" data-id="4" data-description="This is 1st to do list new">
                                                    <i class="cv-edit fs-4 text-primary"></i>
                                                </span>
                                            </li>
                                        </ul>
                                        <script src="/lib/jquery/jquery.min.js"></script>
                                        <script>
                                            // Execute immediately when partial view loads
                                            (function() {
                                                // Uncheck all checkboxes
                                                $('#todoListItems .todo-checkbox').prop('checked', false);
                                            })();
                                        </script>

                                    </div>
                                </div>
                                <div class="card-footer bg-transparent p-2 d-none">
                                    <div class="d-flex justify-content-end">
                                        <form id="todoForm" class="d-flex gap-2 mb-3">
                                            <input type="text" class="form-control" id="todoInput" placeholder="Add new task...">
                                            <button type="submit" class="btn btn-primary" id="addTodoBtn">Add</button>
                                            <button type="button" class="btn btn-secondary" id="cancelEditBtn" style="display: none;">
                                                Cancel
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-header border-0 pb-0">
                                    <h6 class="page_title d-flex align-items-center ">
                                        <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                            <i class="cv-approvals-pending align-middle text-primary fs-5"></i>
                                        </span>
                                        <span>Approvals Pending</span>
                                    </h6>
                                </div>
                                <div class="card-body py-0">
                                    <div class="row g-2">
                                        <div class="col">
                                            <ul class="list-group list-group-flush ">
                                                <li role="button" data-bs-target="#TakeActionModal" data-id="1" data-status="1" class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                                    <div class="d-flex fw-semibold align-items-center">
                                                        <i class="cv-business-process fs-6 icon-circle bg-warning-subtle text-warning"></i><span class="ms-2 fw-normal">Business Process</span>
                                                    </div>
                                                    <span class="fs-5">2</span>
                                                </li>
                                                <li role="button" data-bs-target="#TakeActionModal" data-id="57" data-status="1" class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                                    <div class="d-flex fw-semibold align-items-center">
                                                        <i class="cv-recovery-plan fs-6 icon-circle bg-info-subtle text-info"></i><span class="ms-2 fw-normal">Recovery Plan</span>
                                                    </div>
                                                    <span class="fs-5">1</span>
                                                </li>
                                                <li role="button" data-bs-target="#TakeActionModal" data-id="77" data-status="1" class="list-group-item bg-white d-flex justify-content-between align-items-center px-0 py-1 TakeActionModal">
                                                    <span class="d-flex fw-semibold  align-items-center">
                                                        <i class="cv-other-bcm-entities  icon-circle bg-danger-subtle text-danger-emphasis fs-6"></i>
                                                        <span class="ms-2 fw-normal">BCM Risk</span>
                                                    </span>
                                                    <span class="fs-5">1</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-sm-12 d-grid" style="grid-template-rows: max-content;">


                    <div class="card mb-3">
                        <div class="card-header header border-0">
                            <h6 class="page_title d-flex align-items-center mb-0">
                                <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                    <i class="cv-organisation-summary align-middle text-primary fs-5"></i>
                                </span>
                                <span>Organization Summary</span>
                            </h6>
                        </div>
                        <div class="card-body pt-0">
                            <div class="row g-3">
                                <div class="col-auto col-xxl-3 border-end">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#f7e9fa">
                                        <i class="cv-unit align-middle fs-4" style="color:#ae1fcd;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Business&nbsp;Unit</div>
                                    <h4 class="fw-normal mb-0">12</h4><span class="text-success"></span>
                                </div>
                                <div class="col col-xxl-3 border-end">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#f5f4fd">
                                        <i class="cv-department align-middle fs-4" style="color:#6645e9;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Department</div>
                                    <h4 class="fw-normal mb-0">15</h4><span class="text-success"></span>
                                </div>
                                <div class="col col-xxl-3 border-end">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#efe7f6">
                                        <i class="cv-subdepartment align-middle fs-4" style="color:#640aa8;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Sub&nbsp;Department</div>
                                    <h4 class="fw-normal mb-0">9</h4><span class="text-success"></span>
                                </div>
                                <div class="col col-xxl-3">
                                    <div class="p-2 shadow-sm rounded-2 w-fit" style="background-color:#f9e9f4">
                                        <i class="cv-business-process fs-4 align-middle" style="color:#c8238e;"></i>
                                    </div>
                                    <div class="my-2 text-secondary">Business&nbsp;Process</div>
                                    <h4 class="fw-normal mb-0">30</h4><span class="text-success"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header header border-0">
                            <h6 class="page_title d-flex align-items-center ">
                                <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                    <i class="cv-my-task-list align-middle text-primary fs-5"></i>
                                </span><span>My Task List</span>
                            </h6>
                        </div>
                        <div class="card-body pt-0" style="max-height: 188px; overflow-y:auto;">
                            <div class="mb-3">
                                <div class="d-flex align-items-start justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#2315a3;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold" style="font-size:large;">0 </span>
                                                <span style="padding-left: 8px !important;">
                                                    Processes Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="TakeActionModal" data-id="1" data-status="1" data-bs-toggle="modal" data-bs-target="#TakeActionModal" role="button">
                                        <i class="cv-action fs-4 text-primary"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#880ba7;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold" style="font-size:large;">1</span>
                                                <span style="padding-left: 8px !important;">
                                                    Recovery Plan Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span role="button" class="TakeActionModal" data-id="57" data-status="1" data-bs-toggle="modal" data-bs-target="#TakeActionModal">
                                        <i class="cv-action fs-4 text-primary"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex gap-2 align-items-center">
                                        <i class="cv-dot fs-5" style="color:#cd086a;"></i>
                                        <div>
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span class="fw-semibold" style="font-size:large;">1</span> <span style="padding-left: 8px !important;">
                                                    Risk Assessments Waiting for
                                                    Approval
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <span role="button" class="TakeActionModal" data-bs-toggle="modal" data-id="77" data-status="1" data-bs-target="#TakeActionModal"><i class="cv-action fs-4 text-primary"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="d-grid" style="grid-template-rows: max-content;">
                        <div class="card-group gap-3 mb-3">
                            <div class="card rounded-2">
                                <div class="card-body p-2 d-flex align-items-center gap-2" role="button" id="NotifyIncident" onclick="NotifyIncidentClick()">
                                    <span class="shadow-sm rounded-2 icon-circle" style="background-color:#fd8213;">
                                        <i class="cv-notify-incident text-white fs-4" style="line-height: 1.2;"></i>
                                    </span>
                                    <span>Notify Incident</span>
                                </div>
                            </div>
                            <div class="card rounded-2">
                                <div class="card-body p-2 d-flex align-items-center gap-2" role="button" id="NotifyIncident" onclick="NotifyTeamsClick()">
                                    <span class="shadow-sm icon-circle rounded-2" style="background-color:#265ef9;">
                                        <i class="cv-notify-team align-middle text-white fs-5" style="line-height: 1.2;"></i>
                                    </span>
                                    <span>Notify Team</span>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header header border-0">
                                <form class="d-flex tab-design w-100">
                                    <ul class="nav nav-tabs nav-justified w-100" id="myTab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="incidentNotificationTab" data-bs-toggle="tab" type="button" role="tab" aria-selected="true">
                                                <h6 class="page_title d-flex align-items-center mb-0">
                                                    <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                        <i class="cv-feed-notify align-middle text-primary fs-5"></i>
                                                    </span>
                                                    <span>Incident Summary</span>
                                                </h6>
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="teamNotificationTab" data-bs-toggle="tab" type="button" role="tab" aria-selected="false" tabindex="-1">
                                                <h6 class="page_title d-flex align-items-center mb-0">
                                                    <span class="p-2 bg-white shadow-sm rounded-circle me-1">
                                                        <i class="cv-notify-team align-middle text-primary fs-5"></i>
                                                    </span><span>Team Notification</span>
                                                </h6>
                                            </button>
                                        </li>
                                    </ul>
                                </form>
                            </div>
                            <div class="card-body pt-0 " id="notificationBlock" style="height: calc(50vh - 45px); overflow-y: auto;">
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Dummy 4</span>
                                            <small class="text-secondary">Notified by David Brown on 31-07-2025 16:58:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="65">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="65" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Dummy 1</span>
                                            <small class="text-secondary">Notified by David Brown on 31-07-2025 15:00:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="62">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="62" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Dummy 2</span>
                                            <small class="text-secondary">Notified by David Brown on 30-07-2025 15:09:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="63">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="63" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Dummy 3</span>
                                            <small class="text-secondary">Notified by David Brown on 29-07-2025 15:48:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="64">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="64" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Plan flood for testing</span>
                                            <small class="text-secondary">Notified by David Brown on 25-07-2025 14:57:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="48">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="48" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Plan 103</span>
                                            <small class="text-secondary">Notified by David Brown on 01-07-2025 14:26:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="45">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="45" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Plan Storm</span>
                                            <small class="text-secondary">Notified by David Brown on 29-06-2025 17:33:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="27">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="27" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Plan flood for testing</span>
                                            <small class="text-secondary">Notified by David Brown on 28-06-2025 16:20:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="24">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="24" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Plan Validation</span>
                                            <small class="text-secondary">Notified by David Brown on 27-06-2025 15:38:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="22">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="22" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="icon-circle" style="background-color:var(--bs-warning-bg-subtle)">
                                            <i class="cv-test-recovery-plan text-warning  fs-6"></i>
                                        </div>
                                        <div class="d-grid">
                                            <span class="fw-medium">Cyber security breach</span>
                                            <small class="text-secondary">Notified by David Brown on 26-06-2025 16:27:00</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="btnIncident" role="button" data-bs-toggle="modal" data-bs-target="#incidentDetails" data-incident-id="25">
                                            <i class="cv-timeline1 fs-4 text-primary"></i>
                                        </span>
                                        <span class="btnReport" data-incident-id="25" role="button" style="color:#880ba7;"><i class="cv-report fs-4"></i></span>
                                    </div>
                                </div>

                                <div class="modal fade" id="incidentDetails" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
                                    <div class="modal-dialog modal-dialog-centered modal-xl">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h6 class="Page-Title">Incident TimeLine</h6>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body" id="incidentModalBody">
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="modal fade" id="notificationDetails" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
                                    <div class="modal-dialog modal-dialog-centered modal-xl">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h6 class="Page-Title">Notification Details</h6>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body" id="notificationModalBody">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="modal fade incident-summary-modal" id="incidentSummaryReportModal" tabindex="-1" aria-labelledby="incidentSummaryReportModalLabel" aria-hidden="true" data-bs-backdrop="static">
                                    <div class="modal-dialog modal-dialog-centered modal-fullscreen">
                                        <div class="modal-content">
                                            <div class="modal-header border-bottom">
                                                <h6 class="Page-Title mb-0"><i class="cv-report me-2"></i>Incident Summary Report</h6>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body p-3" id="incidentSummaryReportModalBody">
                                                <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 row-cols-xxl-4 g-3" id="customList">
             
            </div>
        </div>
    </div>
</div>

@* Toast message start *@
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="notificationToast" class="toast  text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex" data-bs-theme="dark">
            <div class="toast-body">
                <div class="d-flex align-items-center gap-1">
                    <span><i class="cv-success align-middle fs-5 "></i></span>
                    <span id="notificationMessage">
                       
                    </span>
                </div>
            </div>
            <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
@* Toast message end *@


<div class="modal fade" id="previewZoom" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-business-service"></i><span>Preview</span></h6>
                <button type="button" class="btn-close ms-2 ms-auto" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body py-2">
                <div class="card-body pt-0 pb-1" style="height:300px;" id="widgetCreationCardCanvas">
                   
                    <div class="preview-chart h-75" id="widgetCreationCard" ></div>
          
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn_save">Save</button>
            </div>
        </div>
    </div>
</div>



<div class="p-2 widgetbuilderblock d-none">
    <div class="row">
        <div class="col-8">
            <div class="card">
                <div class="card-header">
                    <!-- Toolbar -->
                    <div class="toolbar d-none" id="toolbarList">
                        <i class="cv-cut"></i>
                        <i class="cv-paste"></i>
                        <i class="bi bi-trash"></i>
                        <i class="cv-delete1"></i>
                        <i class="cv-undo"></i>
                        <i class="cv-redo"></i>
                        <i class="cv-align-left"></i>
                        <i class="cv-align-vertical-center"></i>
                        <i class="cv-align-right"></i>
                        <i class="cv-align-top"></i>
                        <i class="cv-align-horizontal-center"></i>
                        <i class="cv-align-bottom"></i>
                        <i class="cv-distribute-vertical"></i>
                        <i class="cv-image"></i>
                        <i class="cv-line"></i>
                        <i class="cv-box"></i>
                    </div>
                </div>
                <div class="card-body grid-container">
                    <!-- Grid and Box -->

                    <div class="bcm-box card"  id="cardContainer">
                        <div class="card-header" style="border-bottom: none !important;">

                            <div class="bcm-title">
                                <i class="" id="iconadd"></i>
                               <span class="title-text" id="headerName"></span>
                           </div>
                        </div>
                        <!-- Additional content if needed -->
                     <div class="card-body bodyColor pt-0">
                         <div id="BodyContainer">
                         </div>
                     </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-header d-flex">
                    <h6 class="Page-Title">
                        Configure Widget
                    </h6>
                    <span class="btn btn-primary btm-sm ms-auto backButton"><i class="cv-circle-left-linearrow align-middle fs-6"></i></span>
                </div>

                <div class="card-body" style=" height: calc(100vh - 135px);overflow:auto">
                    <!-- Tabs -->
                    <ul class="widget nav nav-tabs mb-3" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="header-tab" data-bs-toggle="tab" data-bs-target="#header" type="button" role="tab">Header</button>
                        </li>
                         <li class="nav-item" role="presentation">
                            <button class="nav-link" id="chart-tab" data-bs-toggle="tab" data-bs-target="#chart" type="button" role="tab">Card/Chart</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="style-tab" data-bs-toggle="tab" data-bs-target="#dataset" type="button" role="tab">Dataset</button>
                        </li>
                       

                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">Advanced</button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="header" role="tabpanel">

                            <!-- Header Name -->
                            <div class="mb-3">
                                <label class="form-label">Header / Widget Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-BCP-coordinator"></i></span>
                                    <input type="text" class="form-control" placeholder="Widget Name" id='WidgetName'>
                                </div>
                            </div>
                            <div class="form-group field-touched">
            <label>Widget Description</label>
            <div class="input-group">
                <span class="input-group-text"><i class="cv-type"></i></span>
                <input type="text" class="form-control" placeholder="Enter Widget Type" id="WidgetDescription">
            </div>
        </div>
                            <!-- Header Icon -->
                            <div class="mb-3">
                                <label class="form-label">Header Icon</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-BCP-coordinator"></i></span>
                                    <input type="text" class="form-control headericonSet" placeholder="Enter icons class name or select icons">
                                    <span class="input-group-text" role="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample"><i class="cv-down-arrow"></i></span>

                                </div>
                                <div class="collapse" id="collapseExample">
                                    <div class="card card-body">
                                        <table class="table table-sm table-bordered" style="table-layout: fixed;text-align: center;">
                                            <tbody>
                                                <tr>
                                                    <td class="card-icon-active iconbundile"><i class="cv-call_back fs-6 text-white "></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                </tr>
                                                <tr>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                </tr>
                                                <tr>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                </tr>
                                                <tr>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                    <td class="iconbundile"><i class="cv-call_back fs-6"></i></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Header and Body Color -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Header Color</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Header Color" id="Headercolor">
                                        <span class="input-group-text"><i class="cv-wf-edit"></i></span>
                                    </div>
                                   @*  <div class="color-grid mt-2">
                                        <span class="color-box" style="background-color:#ccc"></span>
                                        <span class="color-box" style="background-color:#000"></span>
                                        <span class="color-box" style="background-color:#f0f"></span>
                                        <span class="color-box" style="background-color:#0ff"></span>
                                        <span class="color-box" style="background-color:#f00"></span>
                                        <span class="color-box" style="background-color:#0f0"></span>
                                        <span class="color-box" style="background-color:#00f"></span>
                                        <span class="color-box" style="background-color:#ff0"></span>
                                        <span class="color-box" style="background-color:#800080"></span>
                                        <span class="color-box" style="background-color:#008000"></span>
                                    </div> *@
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Body Color</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Body Color" id="bodycolor">
                                        <span class="input-group-text"><i class="cv-wf-edit"></i></span>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="tab-pane fade  " id="chart" role="tabpanel">

                               <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Height</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Height" id="height">
                                    </div>

                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Width</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Width" id="Width">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group field-touched">
                                <label>Widget Type</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-calculated"></i></span>
                                    <select class=" form-control selectized" id="WidgetType">
                                        <option value="" selected>Select Widget Type</option>
                                        <option value="chart">Chart</option>
                                        <option value="table">Table</option>
                                        <option value="card">Card</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group field-touched ChartTypeForm">
                                <label>Chart Type</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-calculated"></i></span>
                                    <select class=" form-control selectized" id="ChartType">
                                        <option value="" selected>Select Chart</option>
                                        <option value="donut">Donet Chart</option>
                                        <option value="pie">Pie Chart</option>
                                        <option value="line">Line Chart</option>
                                        <option value="bar">Bar Chart</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row chartHeightWidth d-none">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Chart Height</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Chart Heigh" id="Chartheight">
                                    </div>

                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Chart Width</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Chart Width" id="ChartWidth">
                                    </div>
                                </div>
                            </div>
                      

                        </div>
                        <div class="tab-pane fade  " id="dataset" role="tabpanel">
                            <div class="form-group field-touched">
                                <label>Dataset</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-calculated"></i></span>
                                    <select class="form-control selectized" placeholder="Select Dataset" id="SPDataset">
                                    </select>
                                </div>
                            </div>
                            <div class="row fieldForm d-none">
     
            <div class="col-6">
                <div class="form-group field-touched">
                            <label>X axis</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cv-name"></i></span>
                        <select class=" form-control  datasetValue"  id="xaxis">

                </select>
                    </div>
                </div>
            </div>
                                <div class="col-6">
                                    <div class="form-group field-touched">
                                        <label>Y axis</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-name"></i></span>
                                            <select class=" form-control  datasetValue" id="yaxis">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row CardPropertices d-none">
                                @*    <div class="col-md-6 mb-3">
                                    <label class="form-label">Row</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Chart Heigh" id="CardRow">
                                    </div>

                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Column</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Chart Width" id="CardColumn">
                                    </div>
                                </div> *@

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Card Sub Title</label>
                                    <div class="input-group">
                                        <select class=" form-control  datasetValue" placeholder="Card Sub Title" id="cardsubtitle">
                                        </select>

                                    </div>

                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Card Text</label>
                                    <div class="input-group">
                                        <select class=" form-control  datasetValue" placeholder="Card Text" id="CardText">
                                        </select>
                                    </div>
                                </div>
                              




                            </div>


                            <div class="row tableForm d-none" id="tableForm">
                                @* <div class="col-5">
                                    <div class="form-group field-touched">
                                        <label>Column 1</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-name"></i></span>
                                            <input type="text" class="form-control" placeholder="Field Name" id="field1">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-5">
                                    <div class="form-group field-touched">
                                        <label>Value</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cv-name"></i></span>
                                            <select class=" form-control  datasetValue" id="value1">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-2 text-end">
                                    <button class="btn btn-danger" onclick="removeColumn(1)"><i class="cv-save" title="Show"></i></button>
                                </div> *@
                            </div>
                         <div >
                                <button type="button" class="btn  btn-primary btn-sm  ms-auto addbutton d-none" onclick="addColumn()"><i class="cv-circle-plus" title="Show"></i>Add</button>
                                <button type="button" class="btn  btn-primary btn-sm btn_Show ms-auto"><i class="cv-save" title="Show"></i>Show</button>
                                </div>
                            </div>
                        <div class="tab-pane fade  " id="advanced" role="tabpanel">
                            <div class="form-group field-touched">
                                <label>Advance</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cv-calculated"></i></span>
                                    <select class="form-control selectized" placeholder="Select Dataset" id="advance">
                                    </select>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
     @*  <button type="button" class="btn  btn-primary btn-sm btn_preview"><i class='cv-activity-type' title="Preview"></i>Preview</button> *@
    <div class="d-flex">
      <button type="button" class="btn  btn-primary btn-sm btn_save ms-auto"><i class="cv-save" title="Save"></i>Save</button>
        </div >
</div>



<script src="~/lib/jquery/jquery.min.js"></script>

@* Chart Library *@
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="https://cdn.amcharts.com/lib/5/index.js"></script>
<script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
<script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
<script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
<script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
@* All PreBuild Charts & Js *@
<script src="~/js/widgetlibrarycharts/pre-built/departmentbia-prebuilt.js"></script>
<script src="~/js/widgetlibrarycharts/pre-built/PreBuiltCalendar.js"></script>

@* All Custom Charts *@
<script src="~/lib/canvas/canvas.js"></script>

<script src="~/js/widgetlibrarycharts/custom-built/piechart.js"></script>
<script src="~/js/widgetlibrarycharts/custom-built/donutchart.js"></script>
<script src="~/js/customdashboard/widgetbuilder/widgetlist.js"></script>
<script src="~/js/customdashboard/widgetbuilder/widgetchart.js"></script>