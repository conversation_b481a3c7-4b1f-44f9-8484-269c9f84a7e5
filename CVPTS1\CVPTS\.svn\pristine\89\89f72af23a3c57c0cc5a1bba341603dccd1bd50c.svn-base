﻿
let selectedActions = [];
let globalWorkflowContainer = []
let ConditionDetails = { 'If': false, 'Else': false }
let lastNode = null;
let lastGoToConditional = '';
let PlanId = $("#txtPlanId").val();

let sampleData = [
    {
        "name": "test1",
        "description": "",
        "stepOwner": "admin",
        "stepOwnerId": "135",
        "stepAlternateOwner": "admin",
        "stepAlternateOwnerId": "135",
        "CPProfile": "1",
        "estimtedTime": "13:42",
        "estimtedTimeUnit": null,
        "interDependency": false,
        "interDependencyArray": [],
        "id": "step_75502006",
        "uniqueId": "node_227019481",
        "conditionDetails": [
            {
                "condition": "ifcondition",
                "gotoAction": "node_73149540"
            }
        ]
    },
    {
        "name": "test2",
        "description": "",
        "stepOwner": "admin",
        "stepOwnerId": "135",
        "stepAlternateOwner": "admin",
        "stepAlternateOwnerId": "135",
        "CPProfile": "1",
        "estimtedTime": "13:42",
        "estimtedTimeUnit": null,
        "interDependency": false,
        "interDependencyArray": [],
        "id": "step_75502006",
        "uniqueId": "node_764763158"
    },
    {
        "name": "test3",
        "description": "",
        "stepOwner": "admin",
        "stepOwnerId": "135",
        "stepAlternateOwner": "admin",
        "stepAlternateOwnerId": "135",
        "CPProfile": "1",
        "estimtedTime": "13:42",
        "estimtedTimeUnit": null,
        "interDependency": false,
        "interDependencyArray": [],
        "id": "step_75502006",
        "uniqueId": "node_578010386"
    },
    {
        "name": "test4",
        "description": "",
        "stepOwner": "admin",
        "stepOwnerId": "135",
        "stepAlternateOwner": "admin",
        "stepAlternateOwnerId": "135",
        "CPProfile": "1",
        "estimtedTime": "13:42",
        "estimtedTimeUnit": null,
        "interDependency": false,
        "interDependencyArray": [],
        "id": "step_75502006",
        "uniqueId": "node_251082831"
    },
    {
        "name": "step18",
        "description": "step18",
        "stepOwner": "admin",
        "stepOwnerId": "1",
        "stepAlternateOwner": "Ahoud",
        "stepAlternateOwnerId": "27",
        "CPProfile": null,
        "estimtedTime": "15:06",
        "estimtedTimeUnit": null,
        "interDependency": true,
        "interDependencyArray": [
            {
                "id": "154",
                "value": "Step 127"
            }
        ],
        "id": "step_77223968",
        "uniqueId": "node_73149540"
    }
]
$(() => {
    const contextClose = () => $("#wfContextMenu").fadeOut(100);
    $('#btnPaste').hide()

    $('.paletteNode').draggable({
        helper: "clone",
        containment: "#canvas",
    });

    // Make canvas droppable
    $('#canvas').droppable({
        accept: ".paletteNode",
        drop: function (event, ui) {
            let textCont = ui.helper.text().trim();
            let stepDetails = ui.helper.attr('details')
            createWorkflowSteps(textCont, stepDetails);
        }
    });

    // Optional: Allow steps to be sortable
    $('#workflowSteps').sortable({
        items: '.nodeParentContainer',
        stop: function (event, ui) {
            updateNodeMargins();
            updateAllConnections();
        }
    });

    const clearFields = () => {
        bindInterdependencysteps();
        $('#stepName, #stepDesription, #estimtedTime, #gotoStep,#stepId').val('').text('')
        $('#interDependency').prop('checked', false)
        $('#stepDependentCol').addClass('d-none')

        //$('#stepOwner')[0].selectize.clear()
        //$('#estimtedTimeUnit')[0].selectize.clear()
        //$('#stepAlternateOwner')[0].selectize.clear()
        //$('#CPProfile')[0].selectize.clear()
        $('#stepOwner').val("1");
        $('#estimtedTimeUnit').val("1");
        $('#stepAlternateOwner').val("1");
        $('#CPProfile').val("0");
        $('.checkDependentList').prop('checked', false)
        $('#stepName_error, #stepOwner_error, #stepAlternateOwner_error, #cpEstTime_error').text('')
        $('#addWorkflowStep').text('Add')
    }

    $('#workflowSteps').on("sortstart", function (event, ui) {
        let connectId = ''
        let $conditionBlock = null

        if (ui?.item[0]) {
            connectId = ui?.item[0]?.id
            $conditionBlock = $(ui.item[0]).nextAll('.parentConditionCont').first();
        }

        if (connectId && !connectId.includes('condition') && $conditionBlock) {

            const connectedConditionId = $conditionBlock?.find('.conditionalParent')?.attr('id');
            const tempConditionDetails = $conditionBlock?.find('.conditionalParent')?.attr('conditionDetails');

            if (connectedConditionId) {
                const $target = $('#' + connectId);
                $target.attr('addedconditionid', connectedConditionId);

                if (tempConditionDetails) $target.attr('conditionDetails', tempConditionDetails);
            }

            $conditionBlock?.remove();
        }
    });

    $('#workflowSteps').on("sortstop", function (event, ui) {
        let connectId = '';
        let connectedConditionId = ''
        let tempConditionDetails = '';

        if (ui?.item[0]) {
            connectId = ui?.item[0]?.id;
            connectedConditionId = ui?.item[0]?.getAttribute('addedconditionid')
            tempConditionDetails = $('#' + connectId).attr('conditionDetails');
        }

        const workflowNodes = $('#workflowSteps').children().not('svg');
        const isNowLast = ui.item[0] === workflowNodes.last()[0];

        if (isNowLast) $('#workflowSteps').sortable('cancel');

        if (connectId && connectedConditionId && !connectId.includes('condition')) {

            const conditionHtml = `
           <div class="parentConditionCont">
                 <div class="justify-content-center align-items-center d-flex flex-direction-row conditionalParent" id="${connectedConditionId}" conditionDetails=${tempConditionDetails}>             
                 <div><img class="conditionDiagram" src="/../img/WF_Condition.svg" height="90" width="90" /></div>
           </div></div>`;

            $('#' + connectId).after(conditionHtml);
        }
    });

    function updateAllConnections() {
        $('#workflowSteps svg').remove();

        const steps = $('#workflowSteps').children();
        let previousNode = null;
        let isFirstNodeConnected = false;
        let conditionalArray = []

        steps.each(function () {
            const current = $(this);
            const isNode = current.find('.nodeClass').length > 0;
            const isCondition = current.hasClass('parentConditionCont');

            if (isNode) {
                if (previousNode) connectSvg(previousNode, current);

                if (!isFirstNodeConnected) {
                    const startNode = $('#start');
                    if (startNode.length) {
                        connectSvg(startNode, current);
                        isFirstNodeConnected = true;
                    }
                }

                previousNode = current;
            } else if (isCondition) {
                const conditionNode = current;
                const $conditionalParent = current.find('.conditionalParent');
                const conditionNodeId = $conditionalParent?.attr('id')
                let conditionDetails = [];

                try {
                    const detailsAttr = $conditionalParent?.attr('conditionDetails');
                    if (detailsAttr) conditionDetails = JSON.parse(detailsAttr);
                } catch (e) { }

                $conditionalParent?.removeAttr('conditionDetails');
                if (previousNode) connectSvg(previousNode, conditionNode);
                previousNode = current;

                conditionalArray.push({ conditionNodeId, conditionDetails })
            }
        });

        setTimeout(() => {
            conditionalArray.forEach(({ conditionNodeId, conditionDetails }) => {
                const conditionNode = $(`#${conditionNodeId}`)?.parent();

                conditionDetails.forEach(({ gotoAction, condition }) => {
                    const targetNode = $(`#${gotoAction}`);
                    const isNext = conditionNode?.next()?.[0] === targetNode?.[0];

                    if (!targetNode.length) return;
                    let conditionArray = [];

                    try {
                        conditionArray = JSON.parse(targetNode.attr('conditionArray') || '[]');
                    } catch (e) { }

                    const alreadyExists = conditionArray.some(item => item.conditionalConnectionId === lastGoToConditional);

                    if (isNext && !alreadyExists) {
                        lastGoToConditional = conditionNodeId
                        createConditionForNearWF(targetNode, condition)
                    } else {
                        connectSvg(conditionNode, targetNode, true, condition);
                    }
                })
            });
        }, 50)
    }

    const getRandomId = (value) => {
        return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
    }
    function updateNodeMargins() {
        $('#workflowSteps .nodeParentContainer').each(function (index) {
            if ((index + 1) % 2 !== 0) {
                $(this).css({ 'top': '-6rem', 'height': 'fit-content', 'position': 'relative' });
            } else {
                $(this).css({ 'top': '', 'height': '', 'position': '' });
            }
        });
    }

    const createWorkflowSteps = (text, details = {}, mode = '') => {
        details = details && mode !== 'load' ? JSON.parse(atob(details)) : details
        const id = mode == 'load' ? details?.uniqueId : getRandomId("node");
        if (!details?.uniqueId) details['uniqueId'] = id

        details = details && btoa(JSON.stringify(details))

        let html = `<div class="nodeParentContainer" id="${id}" details="${details}">
            <div class="nodeClass d-grid">
                <div class="d-flex flex-row">
                    <i class="Workflow-Title-Icon cv-contingencies"></i>
                     <span class="actionSpan ms-2 fs-6">${text}</span>
                </div>
                <div class="d-flex justify-content-between link-dot">
                   <div><i class="cv-stroke-circle text-success"></i></div><div><i class="cv-stroke-circle text-warning"></i></div>
                </div>
            </div>
        </div>`;
        $('#workflowSteps').append(html);
        updateNodeMargins();
        const newNode = $(`#${id}`);

        if ($('#workflowSteps .nodeParentContainer').not('#start').length === 1) {
            const startNode = $('#start');
            if (startNode.length) {
                connectSvg(startNode, newNode);
            }
        }

        if (lastNode) connectSvg(lastNode, newNode);

        const circle = newNode.find('.Workflow-Title-Icon')[0];
        if (circle) {
            const colors = [
                ['#f96443', '#d62a00'],
                ['#fcb20d', '#e06607'],
                ['#8b49fc', '#5a0ddc'],
                ['#3de94b', '#0e51ab'],
                ['#e63875', '#d2003c'],
                ['#d9dd17', '#969c00']
            ];
            const idx = $('#workflowSteps .nodeClass').length - 1;
            const [startColor, endColor] = colors[idx % colors.length];
            circle.style.backgroundImage = `linear-gradient(135deg, ${startColor}, ${endColor})`;
        }

        lastNode = newNode;
    }

    const connectSvg = (lastNode, newNode, isGoToCondition = false, stepCondition = '') => {
        let flowLineId = getRandomId('flowline');
        let endArrowId = getRandomId('arrow');
        const sourceId = lastNode;
        const targetId = newNode;
        const getConditionId = lastNode?.find('.conditionalParent')?.attr('id');

        if (isGoToCondition) {
            let $condition = $(`#${getConditionId}`);

            const conditionDetails = JSON.parse($condition.attr('conditionDetails') || '[]');
            const workflowDetails = JSON.parse(newNode?.attr('conditionArray') || '[]');

            conditionDetails.push({ condition: stepCondition, gotoAction: newNode?.attr('id') })
            const existing = workflowDetails.find(item => item.conditionalConnectionId === getConditionId);

            if (existing) {
                existing.condition = stepCondition;
                existing.flowLineId = flowLineId;
            } else {
                workflowDetails.push({
                    condition: stepCondition,
                    flowLineId: flowLineId,
                    conditionalConnectionId: getConditionId
                });
            }

            $(`#${getConditionId}`).attr('conditionDetails', JSON.stringify(conditionDetails));
            newNode?.attr('conditionArray', JSON.stringify(workflowDetails));
        }

        let svgLine = `<svg id="${flowLineId}" connectionstring="${isGoToCondition ? lastGoToConditional : ''}" width="${$('#workflowSteps').width()}" height="${$('#workflowSteps').height()}" style="position:absolute;">
        <defs>
            <marker id="${endArrowId}" markerWidth="10" markerHeight="10" refX="3" refY="3" orient="auto">
                <path d="M0,0 L6,3 L0,6 Z" fill="#8c8c8c"/>
            </marker>
        </defs>
    </svg>`;

        if (!isGoToCondition) targetId?.attr('flowLineId', flowLineId)
        $('#workflowSteps').append(svgLine);
        connectSVGLine(sourceId, targetId, flowLineId, endArrowId, isGoToCondition, stepCondition);
    }

    $('#workflowSteps').on('click', '.nodeClass', function (e) {
        e.preventDefault();
        const isSelected = $(this).hasClass('selected');
        $('.nodeClass').removeClass('selected');

        if (!isSelected) $(this).addClass('selected');

    });

    const connectSVGLine = (sourceId, targetId, svgId, pathId, isGoToCondition, stepCondition) => {

        const sourcePorts = getPortPositions(sourceId);
        const targetPorts = getPortPositions(targetId);

        const portId = isGoToCondition
            ? (stepCondition?.toLowerCase() === 'elsecondition' ? 'topMiddle' : 'bottomMiddle')
            : 'rightMiddle';

        const targetPortId = isGoToCondition ? portId : 'leftMiddle';

        const nearestPortPair = {
            source: sourcePorts.find(p => p.id === portId),
            target: targetPorts.find(p => p.id === targetPortId)
        };

        if (!nearestPortPair.source || !nearestPortPair.target) return;

        const startX = nearestPortPair.source.x;
        const startY = nearestPortPair.source.y;
        const endX = nearestPortPair.target.x;
        const endY = nearestPortPair.target.y;
        let pathData = ''

        if (isGoToCondition) {
            const gap = stepCondition?.toLowerCase() === 'elsecondition' ? -150 : 100;
            const midY = startY + gap;
            pathData = `M ${startX} ${startY} C ${startX} ${midY}, ${endX} ${midY}, ${endX} ${endY}`;

        } else {
            const dx = endX - startX;
            const controlOffset = Math.abs(dx) * 0.5;
            const cx1 = startX + controlOffset;
            const cx2 = endX - controlOffset;

            pathData = `M ${startX} ${startY} C ${cx1} ${startY}, ${cx2} ${endY}, ${endX} ${endY}`;
        }

        createPathWithArrow(pathData, svgId, pathId, stepCondition);
    };
    function getPortPositions($div) {
        let offset = $div.position();
        let width = $div.outerWidth();
        let height = $div.outerHeight();

        const isNodeParent = $div.hasClass('nodeParentContainer');
        const verticalOffset = isNodeParent ? (height / 2) + 20 : (height / 2);

        return [
            {
                id: 'topMiddle',
                x: offset.left + width / 2,
                y: offset.top
            },
            {
                id: 'leftMiddle',
                x: offset.left + 6,
                y: offset.top + verticalOffset
            },
            {
                id: 'rightMiddle',
                x: (offset.left - 10) + width,
                y: offset.top + verticalOffset
            },
            {
                id: 'bottomMiddle',
                x: offset.left + width / 2,
                y: offset.top + height
            }
        ];
    }
    function createPathWithArrow(pathData, lineId, endArrowId, stepCondition) {
        setTimeout(() => {
            // $(`#${lineId} path`).remove();
            const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
            let color = stepCondition?.toLowerCase() == 'ifcondition' ? "#4F7942" : stepCondition?.toLowerCase() == 'elsecondition' ? '#D2042D' :
                '#8c8c8c';

            $(path).attr({
                d: pathData,
                fill: "none",
                stroke: color,
                "stroke-width": 1,
                "vector-effect": "non-scaling-stroke",
                "marker-end": `url(#${endArrowId})`
            });

            $(`#${endArrowId} path`).attr("fill", color);
            $(`#${lineId}`).append(path);
            if (stepCondition) createPathWithText(path, lineId, stepCondition);
        });
    }

    const createPathWithText = (path, lineId, stepCondition) => {
        $(`#${lineId} path`)?.find('text.flowLineLabel')?.remove();

        try {
            path = path?.length ? path[0] : path
            const length = path.getTotalLength();
            const midpoint = path.getPointAtLength(length / 2);
            let conditionText = '';
            let labelColor = 'black';

            const label = document.createElementNS("http://www.w3.org/2000/svg", "text");
            label.setAttribute("x", midpoint.x + 10);
            label.setAttribute("y", midpoint.y + 20);
            label.setAttribute("font-size", "14");
            label.setAttribute("text-anchor", "middle");
            label.setAttribute("vector-effect", "non-scaling-stroke");
            label.classList.add("flowLineLabel");

            if (stepCondition?.toLowerCase() === 'ifcondition') {
                conditionText = 'Yes';
                labelColor = '#4F7942';
            } else if (stepCondition?.toLowerCase() === 'elsecondition') {
                conditionText = 'No';
                labelColor = '#D2042D';
            }

            label.textContent = conditionText;
            label.setAttribute("fill", labelColor);
            $(`#${lineId}`).append(label);
        } catch (e) { }
    }

    $(document).on("contextmenu", "#canvas", function (e) {
        e.preventDefault();

        let menu = $("#wfContextMenu");
        let workflowContainer = $("#workflowSteps");
        let SelectContainer = $(".selected.nodeClass")
        menu.hide();

        $('#btnCopy, #btnCut, #btnUnselect, #btndelete, #btnSelectAll, #btnConditional, #btnGoToConditional, #removeIfConditional, #removeElseConditional, #removeConditional').hide()

        if (!SelectContainer.length) {
            if ($('.selected.nodeClass').length && workflowContainer.children().length === $('.selected.nodeClass').length) {
                $("#btnUnselect").show()
            } else {
                $("#btnSelectAll").show()
            }
        }

        if (SelectContainer.is(e.target) && SelectContainer.has(e.target).length === 0 || SelectContainer.length) {
            if (SelectContainer.length === 1) {
                if (!SelectContainer?.parent()?.next()?.hasClass('parentConditionCont')) {
                    $("#btnCopy, #btndelete, #btnCut, #btnConditional").show();
                } else {
                    $("#btnCopy, #btndelete, #btnCut").show();
                }
            } else {
                $("#btnCopy, #btndelete, #btnCut").show();
            }
        }

        if (e.target.classList.contains('conditionDiagram')) {
            $('#btnCopy, #btndelete, #btnCut, #btnConditional').hide()
            lastGoToConditional = e.target.parentElement.parentElement.id;

            if ($(`#${lastGoToConditional}`).attr('conditionDetails') !== undefined) {
                let condDetails = JSON.parse($(`#${lastGoToConditional}`).attr('conditionDetails'))
                if (condDetails.length < 2) {
                    $('#btnGoToConditional').show();
                    $('#removeConditional').show()
                    if (condDetails[0].condition === 'ifcondition') {
                        $('#removeIfConditional').show()
                        ConditionDetails['If'] = true
                    } else {
                        $('#removeElseConditional').show();
                        ConditionDetails['Else'] = true
                    }
                } else {
                    $('#removeIfConditional, #removeElseConditional, #removeConditional').show();
                }
            } else {
                ConditionDetails = { 'If': false, 'Else': false }
                $('#btnGoToConditional, #removeConditional').show()
            }
        }
        menu.fadeIn(100);

        let pageX = e.pageX;
        let pageY = e.pageY;
        let mwidth = menu.outerWidth();
        let mheight = menu.outerHeight();
        let screenWidth = $(window).width();
        let screenHeight = $(window).height();
        let scrTop = $(window).scrollTop();

        if (pageX + mwidth > screenWidth) {
            pageX = pageX - mwidth;
        }
        if (pageY + mheight > screenHeight + scrTop) {
            pageY = pageY - mheight;
        }

        menu.css({
            top: pageY,
            left: pageX
        });

    });

    $(document).on("click", function () {
        contextClose();
    });

    const removeSelection = () => {
        $('.nodeClass').removeClass("selected")
    }

    $("#btnSelectAll").on('click', function () {
        $('.nodeClass').addClass('selected')
    });

    $("#btnUnselect").on('click', function () {
        removeSelection()
    });

    $("#btnCut").on("click", function () {
        cutWorkflowActions();
        $('#btnPaste').show()
    });

    $('#btndelete').on('click', function () {
        $('.nodeClass.selected').parent().remove()
        contextClose();
        updateNodeMargins();
        updateAllConnections();
    })

    const cutWorkflowActions = () => {
        selectedActions = Array.from(document.querySelectorAll('.selected.nodeClass')).map(el => el.parentElement);
        $('.nodeClass.selected').parent().remove();
        removeSelection()
        contextClose();
        updateNodeMargins();
        updateAllConnections();
    }

    $("#btnCopy").on("click", function () {
        if ($('.selected.nodeClass').length > 0) {
            copyWorkflowActions();
        }
    })

    const copyWorkflowActions = () => {
        selectedActions = Array.from(document.querySelectorAll('.selected.nodeClass')).map(el => el.parentElement);
        contextClose();
        removeSelection();
        $('#btnPaste').show()
    }

    $("#btnPaste").on("click", function () {
        BtnPasteWorkFlow();
    });

    const BtnPasteWorkFlow = async () => {
        let selectedActionsLength = selectedActions.length
        for (let i = 0; i < selectedActionsLength; i++) {
            setTimeout(async () => {
                await pasteWorkflowActions(selectedActions[i])
            }, 30)
        }
    }

    const pasteWorkflowActions = async (selectedActionsData) => {
        let nodeId = getRandomId('node');
        let clone = selectedActionsData.cloneNode(true);

        let detailsAttr = clone?.getAttribute('details');
        let detailsObj = {};

        if (detailsAttr) {
            try {
                detailsObj = JSON.parse(atob(detailsAttr));
            } catch (e) { }
        }

        detailsObj["uniqueId"] = nodeId;
        detailsObj["stepId"] = '';

        const encodedDetails = btoa(JSON.stringify(detailsObj));
        $(clone).attr("id", nodeId).attr("details", encodedDetails);

        $('#workflowSteps').append(clone);

        removeSelection();
        updateNodeMargins();
        updateAllConnections();
    };


    $('#btnAddStep').on('click', function () {
        clearFields();
        $('#offcanvasRight').offcanvas('show')
    })

    $('#btnCancelOffcanvas').on('click', function () {
        $('#offcanvasRight').offcanvas('hide')
    })

    const validateInputField = (value, errorElement, message) => {
        if (value) $(`#${errorElement}`).text('')
        else $(`#${errorElement}`).text(message)
    }

    $('#stepName').on('change', function () {
        let stepName = $('#stepName').val()
        validateInputField(stepName, 'stepName_error', 'Enter Step Name')
    })
    $('#stepOwner').on('change', function () {
        //var stepOwn = $('#stepOwner')[0].selectize;
        //let stepOwnerId = stepOwn.getValue();

        var stepOwn = $('#stepOwner option:selected').text();
        let stepOwnerId = $('#stepOwner').val();

        validateInputField(stepOwnerId, 'stepOwner_error', 'Select Step Owner')
    })
    $('#stepAlternateOwner').on('change', function () {
        //var stepAlternateOwn = $('#stepAlternateOwner')[0].selectize;
        //let stepAlternateOwnerId = stepAlternateOwn.getValue();

        var stepAlternateOwn = $('#stepAlternateOwner option:selected').text();
        let stepAlternateOwnerId = $('#stepAlternateOwner').val();

        validateInputField(stepAlternateOwnerId, 'stepAlternateOwner_error', 'Select Step Alternate Owner')
    })

    $('#estimtedTime').on('change', function () {
        let estTime = $('#estimtedTime').val()
        validateInputField(estTime, 'stepName_error', 'Enter Step Name')
    })

    $('#interDependency').on('change', function (e) {
        if (e?.target?.checked) $('#stepDependentCol').removeClass('d-none')
        else $('#stepDependentCol').addClass('d-none')
    })

    $('#addWorkflowStep').on('click', function () {
        let dependentList = []
        $('.checkDependentList').each(function () {
            let isChecked = this.checked
            if (isChecked) {
                dependentList.push($(this).attr('id'))
            }
        })

        var stepOwn = $('#stepOwner')[0].selectize;
        var stepAlternateOwn = $('#stepAlternateOwner')[0].selectize;
        let stepName = $('#stepName').val()
        let description = $('#stepDesription').val()
        //let stepOwner = stepOwn.getItem(stepOwn.getValue()).text();
        //let stepOwnerId = stepOwn.getValue();
        //let stepAlternateOwner = stepAlternateOwn.getItem(stepAlternateOwn.getValue()).text();
        //let stepAlternateOwnerId = stepAlternateOwn.getValue();
        let stepOwner = $('#stepOwner option:selected').text();
        let stepOwnerId = $('#stepOwner').val();
        let stepAlternateOwner = $('#stepAlternateOwner option:selected').text();
        let stepAlternateOwnerId = $('#stepAlternateOwner').val();
        let CPProfile = $('#CPProfile').val();
        let estimtedTime = $('#estimtedTime').val();
        let estimtedTimeUnit = $('#estimtedTimeUnit').val();
        let interDependency = $('#interDependency').prop('checked');
        let stepId = $('#stepId').val();

        validateInputField(stepName, 'stepName_error', 'Enter Step Name')
        validateInputField(stepOwner, 'stepOwner_error', 'Select Step Owner')
        validateInputField(stepAlternateOwner, 'stepAlternateOwner_error', 'Select Step Alternate Owner')
        //validateInputField(estimtedTime, 'cpEstTime_error', 'Select Enstimated Time')
        //console.log(dependentList);
        // Recovery Step Save Dynamic Code Start
        var objWorkflowActionInfo =
        {
            ID: String(stepId),
            Name: String(stepName),
            Description: String(description),
            Stepownerid: String(stepOwnerId),
            Aleternatespetownerid: String(stepAlternateOwnerId),
            Estimationtime: String(estimtedTime),
            GoToStep: String("0"),
            InterDependancy: String(interDependency),
            CPProfileID: String(CPProfile),
            TimeUnit: String(estimtedTimeUnit),
            objDependedOnCheckListNew: dependentList,
        }
        var newSavedStep = 0;

        $.ajax({
            url: 'http://localhost:5164/BCMFunctionRecoveryPlan/WorkflowConfiguration/SaveWorkflowAction',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(objWorkflowActionInfo),
            success: function (response) {
                newSavedStep = response;

                let step = {
                    stepId: response,
                    name: stepName,
                    description: description,
                    stepOwner: stepOwner,
                    stepOwnerId: stepOwnerId,
                    stepAlternateOwner: stepAlternateOwner,
                    stepAlternateOwnerId: stepAlternateOwnerId,
                    CPProfile: CPProfile,
                    estimtedTime: estimtedTime,
                    estimtedTimeUnit: estimtedTimeUnit,
                    interDependency: interDependency,
                }

                if (stepName && stepOwnerId && stepAlternateOwnerId && estimtedTime) {
                    if ($(addWorkflowStep).text() === 'Update') {
                        let getCurrentStepId = $(addWorkflowStep).attr('currentStepId')
                        step['id'] = getCurrentStepId
                        step['stepId'] = response

                        let details = btoa(JSON.stringify(step))
                        $(`#${step.id}`).attr('details', details).find('.actionSpan').text(step.name)
                        $('#offcanvasRight').offcanvas('hide')
                    } else {
                        step['id'] = getRandomId('step')
                        addStepToDiagram(step)
                    }
                }

            },
            error: function (xhr, status, error) {
                console.error('Error:', error);
            }
        });
        // Recovery Step Save Dynamic Code End

        //let step = {
        //    stepId: stepId,
        //    name: stepName,
        //    description: description,
        //    stepOwner: stepOwner,
        //    stepOwnerId: stepOwnerId,
        //    stepAlternateOwner: stepAlternateOwner,
        //    stepAlternateOwnerId: stepAlternateOwnerId,
        //    CPProfile: CPProfile,
        //    estimtedTime: estimtedTime,
        //    estimtedTimeUnit: estimtedTimeUnit,
        //    interDependency: interDependency,
        //    interDependencyArray: dependentList,
        //}

        //if (stepName && stepOwnerId && stepAlternateOwnerId && estimtedTime) {
        //    if ($(this).text() === 'Update') {
        //        let getCurrentStepId = $(this).attr('currentStepId')
        //        step['id'] = getCurrentStepId
        //        let details = btoa(JSON.stringify(step))
        //        $(`#${step.id}`).attr('details', details).find('.actionSpan').text(step.name)
        //        $('#offcanvasRight').offcanvas('hide')
        //    } else {
        //        step['id'] = getRandomId('step')
        //        addStepToDiagram(step)
        //    }
        //}
    })

    //const addStepToDiagram = (step) => {
    //    let details = btoa(JSON.stringify(step))

    //    const colors = [
    //        ['#f96443', '#d62a00'],
    //        ['#fcb20d', '#e06607'],
    //        ['#8b49fc', '#5a0ddc'],
    //        ['#3de94b', '#0e51ab'],
    //        ['#e63875', '#d2003c'],
    //        ['#d9dd17', '#969c00']
    //    ];

    //    const idx = $('#stepNodeContainer .paletteNode').length;
    //    const [startColor, endColor] = colors[idx % colors.length];
    //    const gradientStyle = `background-image: linear-gradient(135deg, ${startColor}, ${endColor});`;


    //    let stepHtml = ` <div class="paletteNode list-group-item bg-white d-flex justify-content-between align-items-center px-0" draggable="true" id='${step.id}' details='${details}'><div class="me-auto"><i class="cv-action Workflow-Title-Icon"></i><span>${step.name}</span></div><i class="cv-right-arrow"></i></div>`

    //    $('#stepNodeContainer').append(stepHtml)

    //    setTimeout(() => {
    //        $('.paletteNode').draggable({
    //            helper: "clone",
    //            containment: "#canvas",
    //        });
    //    },200)

    //    $('#offcanvasRight').offcanvas('hide')
    //}

    const addStepToDiagram = (step) => {
        let details = btoa(JSON.stringify(step));

        const colors = [
            ['#f96443', '#d62a00'],
            ['#fcb20d', '#e06607'],
            ['#8b49fc', '#5a0ddc'],
            ['#3de94b', '#0e51ab'],
            ['#e63875', '#d2003c'],
            ['#d9dd17', '#969c00']
        ];

        const idx = $('#stepNodeContainer .paletteNode').length;
        const [startColor, endColor] = colors[idx % colors.length];
        const gradientStyle = `linear-gradient(135deg, ${startColor}, ${endColor})`;

        let stepHtml = `
        <div class="paletteNode list-group-item bg-white d-flex justify-content-between align-items-center px-0"
             draggable="true"
             id='${step.id}'
             details='${details}'>
            <div class="d-flex align-items-center gap2">
                <i class="cv-action Action-Title-Icon"></i>
                <span class="Sub-Title">${step.name}</span>
            </div>
            <i class="cv-right-arrow"></i>
        </div>`;

        $('#stepNodeContainer').append(stepHtml);

        // Apply gradient background to the newly added icon
        const newNode = $(`#${step.id}`);
        const circle = newNode.find('.Action-Title-Icon')[0];
        if (circle) {
            circle.style.backgroundImage = gradientStyle;
        }

        setTimeout(() => {
            $('.paletteNode').draggable({
                helper: "clone",
                containment: "#canvas",
            });
        }, 200);

        $('#offcanvasRight').offcanvas('hide');
    };

    $(document).on('dblclick', '.nodeParentContainer', function (e) {
        //let getStepId = this.id
        //if (getStepId) updateStepConfiguration(getStepId)

        let getStepId = this.id
        let detailsAttr = $(this).attr('details')
        let stepIdDatabase = null

        if (detailsAttr) {
            try {
                const details = JSON.parse(atob(detailsAttr))
                stepIdDatabase = details.stepId
            } catch (error) {
                console.error('Error parsing details:', error)
            }
        }

        if (getStepId) updateStepConfiguration(getStepId, stepIdDatabase)
    })

    const updateStepConfiguration = (id, stepIdDatabase) => {

        // Dynamic Pop Up Binding Start
        if (stepIdDatabase > 0) {
            //alert("fromDatabase");
            var getDetailsDynamic;

            $.ajax({
                url: 'http://localhost:5164/BCMFunctionRecoveryPlan/WorkflowConfiguration/GetWorkflowActionById?iID=' + stepIdDatabase + '',
                type: 'GET',
                contentType: 'application/json',
                //data: JSON.stringify(objWorkflowActionInfo),
                success: function (response) {
                    getDetailsDynamic = response;
                    $('.checkDependentList').prop('checked', false)
                    $('#stepName').val(getDetailsDynamic?.name)
                    $('#stepId').val(stepIdDatabase)
                    $('#stepDesription').val(getDetailsDynamic?.description)
                    $('#stepOwner').val(getDetailsDynamic?.stepOwnerId)
                    $('#stepAlternateOwner').val(getDetailsDynamic?.alterStepOwnerID)
                    $('#CPProfile').val("0")
                    $('#estimtedTime').val(getDetailsDynamic?.estimationTime)
                    $('#estimtedTimeUnit').val(getDetailsDynamic?.timeUnit)
                    $('#interDependency').prop('checked', getDetailsDynamic?.interDependancy)
                    if (Array.isArray(getDetailsDynamic?.objDependedOnCheckListNew)) {
                        getDetailsDynamic?.objDependedOnCheckListNew.forEach((id) => {
                            $(`#${id}`).prop('checked', true);
                        });
                    }
                    if (getDetailsDynamic?.interDependancy) $('#stepDependentCol').removeClass('d-none')
                    else $('#stepDependentCol').addClass('d-none')
                    $('#addWorkflowStep').text('Update').attr('currentStepId', id)
                    $('.workflowSteps').removeClass('highlighted')
                    $('#offcanvasRight').offcanvas('show')
                },
                error: function (xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        }

        else {
            //alert("fromUI");
            console.log(response);
            let getDetails = $(`#${id}`).attr('details')
            getDetails = JSON.parse(atob(getDetails))

            getDetails?.interDependencyArray.length && getDetails?.interDependencyArray?.forEach((d) => {
                $(`#${d.id}`).prop('checked', true)
            })

            if (getDetails?.interDependency) $('#stepDependentCol').removeClass('d-none')
            else $('#stepDependentCol').addClass('d-none')

            $('#stepName').val(getDetails?.name)
            $('#stepId').val(getDetails?.stepId || '')
            $('#stepDesription').val(getDetails?.description)
            $('#stepOwner').val(getDetails?.stepOwner)
            $('#stepAlternateOwner').val(getDetails?.stepAlternateOwner)
            $('#CPProfile').val(getDetails?.CPProfile)
            $('#estimtedTime').val(getDetails?.estimtedTime)
            $('#estimtedTimeUnit').val(getDetails?.estimtedTimeUnit)
            $('#interDependency').prop('checked', getDetails?.interDependency)
            $('#addWorkflowStep').text('Update').attr('currentStepId', id)
            $('.workflowSteps').removeClass('highlighted')
            $('#offcanvasRight').offcanvas('show')
        }

        // Dynamic Pop Up Binding End 
    }

    //$('#btnSaveWorkflow').on('click', function () {
    //    globalWorkflowContainer = [];

    //    $('#workflowSteps').children().not('svg').each(function () {
    //        if (!$(this)?.hasClass('parentConditionCont')) {
    //            const getStepId = $(this)?.attr('id');
    //            const detailBase64 = $(`#${getStepId}`)?.attr('details');

    //            if (!detailBase64) return;

    //            let getDetail = JSON.parse(atob(detailBase64));

    //            const conditionalParent = $(`#${getStepId}`)?.next()?.find('.conditionalParent');

    //            if (conditionalParent.length) {
    //                const conditionDetails = JSON.parse(conditionalParent.attr('conditionDetails') || '[]');
    //                getDetail['conditionDetails'] = conditionDetails;
    //            }

    //            globalWorkflowContainer.push(getDetail);
    //        }
    //    });

    //    $('#workflowSteps').children().not('#start').remove();
    //    lastNode = null;
    //    // loadWorkflowDetails()

    //    var objDiagramData = {
    //        WorkflowXoml: JSON.stringify(globalWorkflowContainer),
    //        Id: PlanId
    //    }

    //    $.ajax({
    //        url: 'http://localhost:5164/BCMFunctionRecoveryPlan/WorkflowConfiguration/SaveWorkflowConfigurationDiagram',
    //        type: 'POST',
    //        data: JSON.stringify(objDiagramData),
    //        dataType: 'text',
    //        contentType: 'application/json',
    //        success: function (result) {
    //            bindDiagram();
    //        },
    //        error: function (result) { }
    //    })
    //});

    $('#btnSaveWorkflow').on('click', function () {
        globalWorkflowContainer = [];
        let invalidStepsFound = false;

        $('#workflowSteps').children().not('svg').each(function () {
            if (!$(this)?.hasClass('parentConditionCont')) {
                const getStepId = $(this)?.attr('id');
                const detailBase64 = $(`#${getStepId}`)?.attr('details');

                if (!detailBase64) return;

                let getDetail = JSON.parse(atob(detailBase64));

                // Check if stepId is missing or not greater than 0
                if (!getDetail.stepId || parseInt(getDetail.stepId) <= 0) {
                    invalidStepsFound = true;
                    // Highlight the step with missing stepId
                    $(`#${getStepId}`).addClass('border border-danger');
                }

                const conditionalParent = $(`#${getStepId}`)?.next()?.find('.conditionalParent');

                if (conditionalParent.length) {
                    const conditionDetails = JSON.parse(conditionalParent.attr('conditionDetails') || '[]');
                    getDetail['conditionDetails'] = conditionDetails;
                }

                globalWorkflowContainer.push(getDetail);
            }
        });

        // If any steps have invalid stepIds, show alert and stop
        if (invalidStepsFound) {
            alert("Some steps don't have valid IDs. Please save each step individually before saving the workflow.");
            return;
        }

        $('#workflowSteps').children().not('#start').remove();
        lastNode = null;
        // loadWorkflowDetails()

        var objDiagramData = {
            WorkflowXoml: JSON.stringify(globalWorkflowContainer),
            Id: PlanId
        }

        $.ajax({
            url: 'http://localhost:5164/BCMFunctionRecoveryPlan/WorkflowConfiguration/SaveWorkflowConfigurationDiagram',
            type: 'POST',
            data: JSON.stringify(objDiagramData),
            dataType: 'text',
            contentType: 'application/json',
            success: function (result) {
                bindDiagram();
            },
            error: function (result) { }
        })
    });

    // condition part

    $('#btnConditional').on('click', function () {
        const source = $('.nodeParentContainer')?.find('.selected')?.parent();
        if (!source?.length) return;

        const workflowBlocks = $('#workflowSteps').children().not('svg');
        if (source[0] === workflowBlocks.last()[0]) return;

        const conditionId = getRandomId('condition');
        const conditionHtml = `
        <div class="parentConditionCont">
            <div class="justify-content-center align-items-center d-flex flex-direction-row conditionalParent" id="${conditionId}">             
                <div class="position-relative">
                    <img class="conditionDiagram" src="/../img/WF_Condition.svg" height="90" width="90" />
                    <div class="d-flex justify-content-between link-dot position-absolute w-100 px-2" style="align-self:anchor-center; margin-top: -5px;">
                       <div><i class="cv-stroke-circle text-success"></i></div><div><i class="cv-stroke-circle text-warning"></i></div>
                    </div>
                </div>
            </div>
        </div>`;

        source.after(conditionHtml);
        $('.nodeClass').removeClass("selected");
        updateAllConnections();
    });

    $('#btnGoToConditional').on('click', function () {
        $('#createConditionModal').modal('show');

        const $select = $('#gotoConditionStep');
        const $stepSelect = $('#stepCondition');
        const stepSelectize = $stepSelect[0]?.selectize;
        const selectize = $select[0]?.selectize;

        selectize.clearOptions();
        stepSelectize.clearOptions();

        stepSelectize.addOption([
            {
                value: 'ifcondition', text: 'If Condition'
            },
            {
                value: 'elsecondition', text: 'Else Condition'
            }
        ]);

        stepSelectize.updateOption('ifcondition', {
            value: 'ifcondition',
            text: 'If Condition',
            disabled: !!ConditionDetails?.If
        });

        stepSelectize.updateOption('elsecondition', {
            value: 'elsecondition',
            text: 'Else Condition',
            disabled: !!ConditionDetails?.Else
        });

        stepSelectize.refreshOptions(false);

        $('#workflowSteps .nodeParentContainer').each(function () {
            const node = $(this).find('.nodeClass');
            const nodeId = $(this).attr('id');
            const nodeText = node.text().trim();
            const selectedConditionFound = $(this)?.next()?.find(`#${lastGoToConditional}`)

            if (nodeId && nodeText && !selectedConditionFound?.length) {
                selectize.addOption({ value: nodeId, text: nodeText });
            }
        });
    });

    $('#btnSaveCondition').on('click', function (e) {
        e?.preventDefault();

        let stepWfId = $('#gotoConditionStep')?.val();
        let stepCondition = $('#stepCondition')?.val();

        if (stepWfId) {
            let connectNode = $(`#${stepWfId}`);
            let conditionNode = $(`#${lastGoToConditional}`)?.parent()
            const isNext = conditionNode?.next()?.[0] === connectNode?.[0];
            let conditionArray = [];

            try {
                const connectDetails = connectNode.attr('conditionArray');
                conditionArray = connectDetails ? JSON.parse(connectDetails) : [];
            } catch (e) { conditionArray = []; }

            const alreadyExists = conditionArray.some(item => item.conditionalConnectionId === lastGoToConditional);

            if (isNext && !alreadyExists) {
                createConditionForNearWF(connectNode, stepCondition)
            } else {
                if (stepCondition) connectSvg(conditionNode, connectNode, true, stepCondition);
            }
        }

        $('#createConditionModal').modal('hide');
        lastGoToConditional = ''
    })

    $('#removeConditional').on('click', function () {

        if ($(`#${lastGoToConditional}`).attr('conditionDetails')?.length) {
            let getConditionDetails = JSON.parse($(`#${lastGoToConditional}`).attr('conditionDetails'))
            getConditionDetails.forEach((d) => {
                let conditionArray = [];
                let targetActionId = d.gotoAction
                let getFlowLine = $(`#${targetActionId}`).attr('conditionArray')
                if (getFlowLine?.length) {
                    let flowLineDetails = JSON.parse(getFlowLine)
                    flowLineDetails.forEach((x) => {
                        let flowLineId = x.flowLineId
                        if (lastGoToConditional === x.conditionalConnectionId) {
                            $(`#${flowLineId}`)?.remove();
                        } else {
                            conditionArray.push(x)
                        }
                    })
                }
                if (conditionArray.length) {
                    $(`#${targetActionId}`).attr('conditionArray', JSON.stringify(conditionArray))
                } else {
                    $(`#${targetActionId}`).removeAttr('conditionArray')
                }

            })
        }
        $(`#${lastGoToConditional}`).parent().remove()
        setTimeout(() => {
            updateAllConnections()
        })
    })

    $('#removeIfConditional').on('click', function () {

        if ($(`#${lastGoToConditional}`).attr('conditionDetails') !== undefined) {
            let conditionData = JSON.parse($(`#${lastGoToConditional}`).attr('conditionDetails'))
            let filterCondition = conditionData.filter(x => x.condition === 'ifcondition')
            let goToId = filterCondition[0].gotoAction
            let getSvgDetails = JSON.parse($('#' + goToId).attr('conditionArray'))
            let filterdGoToCondition = getSvgDetails.filter((d) => d.condition === 'ifcondition')
            let getSvg = filterdGoToCondition[0].flowLineId
            let $flowLine = $('#' + getSvg);

            if ($('#' + goToId)?.attr('flowLineId') === getSvg) {
                $flowLine?.removeAttr('connectionstring')?.find('path')?.css('stroke', '#8c8c8c');
                $flowLine?.find('.flowLineLabel')?.remove();
            } else {
                $flowLine?.remove();
            }

            let filterSvg = conditionData.filter(x => x.condition !== 'ifcondition')
            if (filterSvg.length > 0) {
                $(`#${lastGoToConditional}`).attr('conditionDetails', JSON.stringify(filterSvg))

            } else {
                $(`#${lastGoToConditional}`).removeAttr('conditionDetails')
            }

            let filterSvgTarget = getSvgDetails.filter(x => x.condition !== 'ifcondition')
            if (filterSvgTarget.length) {
                $(`#${goToId}`).attr('conditionArray', JSON.stringify(filterSvgTarget))
            } else {
                $(`#${goToId}`).removeAttr('conditionArray')
            }
        }

    })

    $('#removeElseConditional').on('click', function () {

        if ($(`#${lastGoToConditional}`).attr('conditionDetails') !== undefined) {
            let conditionData = JSON.parse($(`#${lastGoToConditional}`).attr('conditionDetails'))
            let filterCondition = conditionData.filter(x => x.condition === 'elsecondition')
            let goToId = filterCondition[0].gotoAction

            let getSvgDetails = JSON.parse($('#' + goToId).attr('conditionArray'))
            let filterdGoToCondition = getSvgDetails.filter((d) => d.condition === 'elsecondition')
            let getSvg = filterdGoToCondition[0].flowLineId
            let $flowLine = $('#' + getSvg);

            if ($('#' + goToId)?.attr('flowLineId') === getSvg) {
                $flowLine?.removeAttr('connectionstring')?.find('path')?.css('stroke', '#8c8c8c');
                $flowLine?.find('.flowLineLabel')?.remove();
            } else {
                $flowLine?.remove();
            }

            let filterSvg = conditionData.filter(x => x.condition !== 'elsecondition')
            if (filterSvg.length > 0) {
                $(`#${lastGoToConditional}`).attr('conditionDetails', JSON.stringify(filterSvg))

            } else {
                $(`#${lastGoToConditional}`).removeAttr('conditionDetails')
            }

            let filterSvgTarget = getSvgDetails.filter(x => x.condition !== 'elsecondition')
            if (filterSvgTarget.length) {
                $(`#${goToId}`).attr('conditionArray', JSON.stringify(filterSvgTarget))
            } else {
                $(`#${goToId}`).removeAttr('conditionArray')
            }
        }

    })

    const createConditionForNearWF = (connectNode, stepCondition) => {

        const flowLineId = connectNode?.attr('flowlineid');
        const conditionDetails = JSON.parse($(`#${lastGoToConditional}`)?.attr('conditionDetails') || '[]');
        const workflowDetails = JSON.parse(connectNode?.attr('conditionArray') || '[]');
        let color = stepCondition?.toLowerCase() == 'ifcondition' ? "#4F7942" : '#D2042D'

        if (flowLineId && stepCondition) {
            const mainPath = $(`#${flowLineId}`)?.children('path')[0];
            conditionDetails.push({ condition: stepCondition, gotoAction: connectNode?.attr('id') });
            workflowDetails.push({ condition: stepCondition, flowLineId: flowLineId, conditionalConnectionId: lastGoToConditional });

            $(`#${lastGoToConditional}`)?.attr('conditionDetails', JSON.stringify(conditionDetails));
            connectNode?.attr('conditionArray', JSON.stringify(workflowDetails));
            $(`#${flowLineId}`)?.find('path')?.css('stroke', color)?.attr('connectionstring', lastGoToConditional);
            if (mainPath) createPathWithText(mainPath, flowLineId, stepCondition)
        }
    }

    const loadWorkflowDetails = (strDiagram) => {
        let conditionalArray = [];
        let workflowDataLength = strDiagram?.length;

        for (let i = 0; i < workflowDataLength; i++) {

            setTimeout(async () => {
                let getParticularObj = strDiagram[i];

                if (getParticularObj) {
                    createWorkflowSteps(getParticularObj?.name, getParticularObj, 'load')
                    if (getParticularObj?.conditionDetails?.length) {
                        conditionalArray.push({ 'actionId': getParticularObj?.uniqueId, condionDetail: getParticularObj?.conditionDetails })
                    }
                }

                if (i === workflowDataLength - 1) {
                    setTimeout(() => {
                        if (conditionalArray?.length) {
                            getLoadCondition(conditionalArray)
                        }
                    }, 100)
                }
            }, 40)
        }
    }

    const getLoadCondition = (conditionalArray) => {
        conditionalArray && conditionalArray?.length && conditionalArray.forEach((d) => {
            let goToAction = d.actionId;
            let connectionString = getRandomId('condition')
            let conditionDetail = JSON.stringify(d?.condionDetail);

            const conditionHtml = `<div class="parentConditionCont">
        <div class="justify-content-center align-items-center d-flex flex-direction-row conditionalParent" id="${connectionString}" conditionDetails=${conditionDetail}>             
            <div><img class="conditionDiagram" src="/../img/WF_Condition.svg" height="90" width="90" /></div>
        </div></div>`;

            $(`#${goToAction}`).after(conditionHtml);
            updateAllConnections()
        })
    }

    $(document).ready(function () {
        bindDiagram();
    });

    function bindDiagram() {
        const getDiagramData = async () => {
            let DiagramData = await $.get('/BCMFunctionRecoveryPlan/WorkflowConfiguration/GetWorkflowConfigurationDiagram', { strPlanID: PlanId });
            return DiagramData;
        }
        getDiagramData().then((result) => {
            loadWorkflowDetails(JSON.parse(result));
        })
    }

    $("#btnNext").click(function () {
        window.location.href = '/BCMFunctionRecoveryPlan/FunctionalRecoveryPlanSummary/FunctionalRecoveryPlanSummary/';
    });

    function bindInterdependencysteps() {
        $.ajax({
            url: '/BCMFunctionRecoveryPlan/WorkflowConfiguration/GetAllStepsForInterdependencyBinding', // Replace with your controller name
            type: 'GET',
            success: function (response) {
                if (response) {
                    // Clear existing checkboxes
                    $('#stepDependentCol .d-flex.align-items-center.gap-3.flex-wrap').empty(); // Target the correct container

                    // Bind new checkboxes
                    response.forEach(function (item) {
                        const checkboxHtml = `
                 <div class="d-flex align-items-center gap-1">
                     <input class="form-check-input m-0 checkDependentList" type="checkbox" name="${item.name}" id="${item.id}">
                     <label class="small ms-1" for="${item.id}">${item.name}</label>
                 </div>`;
                        $('#stepDependentCol .d-flex.align-items-center.gap-3.flex-wrap').append(checkboxHtml); // Append to the correct container
                    });
                }
            },
            error: function (xhr, status, error) {
                console.error('Error fetching data:', error);
            }
        });
    }
})