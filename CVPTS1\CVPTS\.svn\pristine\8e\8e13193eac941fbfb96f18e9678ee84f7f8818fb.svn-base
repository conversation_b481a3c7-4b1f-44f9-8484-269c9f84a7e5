﻿using BCM.BusinessClasses;
using BCM.BusinessFacadeSrv;
using BCM.Shared;
using BCM.UI.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace BCM.UI.Areas.BCMOrgStructure.Controllers
{
    [Area("BCMOrgStructure")]
    public class ManageOrgStructureController : BaseController
    {
        private ProcessSrv _ProcessSrv;
        private readonly Utilities _Utilities;
        private readonly CVLogger _CVLogger;
        ManageUsersDetails? _UserDetails = new ManageUsersDetails();
        public ManageOrgStructureController(ProcessSrv ProcessSrv, Utilities Utilities, CVLogger CVLogger) : base(Utilities)
        {
            _ProcessSrv = ProcessSrv;
            _Utilities = Utilities;
            _CVLogger = CVLogger;
        }

        public IActionResult ManageOrgStructure()
        {
            return View();
        }
    }
}
