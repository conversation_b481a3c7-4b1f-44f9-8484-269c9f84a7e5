﻿@using Newtonsoft.Json
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    var prop = ViewBag.WidgetData as List<object>;
}

<!-- Load Custom Dashboard CSS -->
<link href="~/css/CustomDasboard.css" rel="stylesheet" />
<link href="~/css/Dashboard.css" rel="stylesheet" />

<style>
    /* Preview mode specific styling to match dashboard widgets */
    .preview-mode .grid-stack {
        background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                    linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                    linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
        background-size: 20px 20px;
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        min-height: calc(100vh - 120px);
        pointer-events: none; /* Disable interactions in preview */
    }

    .preview-mode .grid-stack-item {
        pointer-events: auto;
    }

    .preview-mode .widget-toolbar {
        display: none; /* Hide toolbar buttons in preview */
    }

    .bcm-title {
        font-weight: 600;
        font-size: 0.95rem;
        color: #333;
        margin-bottom: 0.3rem;
    }
</style>

<!-- ✅ Load amCharts -->
<script src="https://cdn.amcharts.com/lib/5/index.js"></script>
<script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
<script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
<script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
<script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

<!-- ✅ Load GridStack (before your code runs) -->
<link href="https://cdn.jsdelivr.net/npm/gridstack@9.2.1/dist/gridstack.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/gridstack@9.2.1/dist/gridstack-extra.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/gridstack@9.2.1/dist/gridstack-all.js"></script>

<!-- ✅ Load jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<div class="container-fluid mt-4 preview-mode">
    <h3 class="mb-4">Dashboard Preview</h3>

    <!-- ✅ Grid container for widget layout matching actual dashboard -->
    <div class="grid-stack" id="dashboardContainer"></div>
</div>

@section Scripts {
    <!-- ✅ Your Dashboard scripts -->
    <script src="~/js/customdashboard/widgetbuilder/widgetchart.js"></script>
    <script src="~/js/customdashboard/dashboardbuilder/DashboardListView.js"></script>

    <!-- ✅ Send data & directly call dashboard -->
    <script>
        const prop = @Html.Raw(JsonConvert.SerializeObject(prop ?? new List<object>()));

        try {
            if (typeof customDashboard === "function" && Array.isArray(prop)) {
                customDashboard(prop); // 🔥 Dashboard loads immediately
            } else {
                console.error("❌ customDashboard function missing or 'prop' invalid");
            }
        } catch (e) {
            console.error("🔥 Error while loading dashboard:", e);
        }
    </script>
}