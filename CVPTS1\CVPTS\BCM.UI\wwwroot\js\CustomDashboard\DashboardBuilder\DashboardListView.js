async function customDashboard(properties) {
    const dashboardContainer = document.getElementById("dashboardContainer");

    // Initialize GridStack with same configuration as actual dashboard
    const grid = GridStack.init({
        cellHeight: 70,
        margin: 8,
        minRow: 1,
        animate: true,
        float: false,
        removable: false,
        resizable: false, // Disable resizing in preview
        draggable: false, // Disable dragging in preview
        marginUnit: 'px',
        column: 12,
        maxRow: 0,
        disableOneColumnMode: false,
        oneColumnModeDomSort: true,
        alwaysShowResizeHandle: false
    }, dashboardContainer);

    // Clear existing widgets
    grid.removeAll();

    // Get default grid sizes for widget types (same as actual dashboard)
    const getDefaultGridSize = (type) => {
        const sizes = {
            'kpi': { x: 0, y: 0, w: 3, h: 3 },
            'table': { x: 0, y: 0, w: 8, h: 4 },
            'text': { x: 0, y: 0, w: 6, h: 3 },
            'progress': { x: 0, y: 0, w: 4, h: 4 },
            'amchart-line': { x: 0, y: 0, w: 6, h: 4 },
            'amchart-column': { x: 0, y: 0, w: 6, h: 4 },
            'amchart-pie': { x: 0, y: 0, w: 4, h: 4 },
            'amchart-radar': { x: 0, y: 0, w: 4, h: 4 },
            'amchart-area': { x: 0, y: 0, w: 6, h: 4 },
            'amchart-scatter': { x: 0, y: 0, w: 5, h: 4 },
            'weather': { x: 0, y: 0, w: 4, h: 5 },
            'calendar': { x: 0, y: 0, w: 5, h: 6 },
            'clock': { x: 0, y: 0, w: 3, h: 3 },
            'gauge': { x: 0, y: 0, w: 4, h: 4 },
            'map': { x: 0, y: 0, w: 6, h: 5 },
            'news': { x: 0, y: 0, w: 5, h: 6 },
            'social': { x: 0, y: 0, w: 4, h: 5 }
        };
        return sizes[type] || { x: 0, y: 0, w: 4, h: 3 };
    };

    for (const prop of properties) {
        let data = [];
        let chartId = `preview-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
        let chartType = prop.type?.replace("amchart-", "");

        // Fetch data if query exists
        if (prop.query) {
            try {
                const response = await $.ajax({
                    url: '/BCMAdministration/DashboardList/getQueryData',
                    type: "GET",
                    data: { query: prop.query },
                    dataType: "json"
                });

                if (response?.success && typeof response.data === "string") {
                    data = JSON.parse(response.data);
                }
            } catch (error) {
                console.warn("Error loading data for query:", prop.query);
            }
        }

        // Get widget size based on type
        const size = getDefaultGridSize(prop.type);

        // Create widget with exact same structure as actual dashboard
        const widgetHtml = `
            <div class="grid-stack-item-content mini-widget p-2 border rounded shadow-sm"
                 data-widget-type="${prop.type}"
                 data-widgetquery="${prop.query || ''}"
                 data-widgetxaxis="${prop.widgetXaxis || ''}"
                 data-widgetyaxis="${prop.widgetYaxis || ''}"
                 ${prop.type === 'table' && prop.tableArray ? `data-widgettablearray='${encodeURIComponent(JSON.stringify(prop.tableArray))}'` : ''}
                 style="resize: both; overflow: auto; min-height: 150px;">
                <div class="dashboard-widget bcm-box card">
                    <div class="card-header" style="border-bottom: none !important;">
                        <div class="bcm-title" id="headerName">${prop.title || 'Untitled'}</div>
                    </div>
                    <div class="card-body">
                        <div id="BodyContainer">
                            <div id="${chartId}" style="width: 100%; height: 100%; min-height: 200px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add widget to grid with proper sizing
        grid.addWidget(widgetHtml, {
            w: size.w,
            h: size.h
        });

        // Render chart after widget is added to DOM
        setTimeout(() => {
            if (prop.type === "table" && prop.tableArray?.length) {
                // Transform table based on tableArray mapping
                const newTableResult = data[0]?.map(item => {
                    const newObj = {};
                    prop.tableArray.forEach(map => {
                        newObj[map.name] = item[map.value];
                    });
                    return newObj;
                }) ?? [];

                renderChart("table", chartId, newTableResult);
            } else {
                renderChart(chartType, chartId, data, prop.title, prop.widgetXaxis, prop.widgetYaxis);
            }
        }, 100);
    }
}
